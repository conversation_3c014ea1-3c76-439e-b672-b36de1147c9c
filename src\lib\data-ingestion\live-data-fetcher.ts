import { createClient } from '@/lib/supabase/server';

export interface LiveDataContext {
  query: string;
  userType: 'customer' | 'admin';
}

export interface DatabaseContext {
  cars: any[];
  bookings: any[];
  profiles: any[];
  vehicleCategories: any[];
  payments: any[];
  documents: any[];
  gpsLocations: any[];
  issues: any[];
  recentActivity: string;
}

export class LiveDataFetcher {
  private async getSupabase() {
    return await createClient();
  }

  // Fetch real-time car availability and information
  async fetchCarData(query?: string): Promise<string> {
    try {
      const supabase = await this.getSupabase();
      let carsQuery = supabase
        .from('cars')
        .select('*')
        .order('model');

      // Filter based on query if provided
      if (query) {
        const searchTerms = query.toLowerCase();
        if (searchTerms.includes('available')) {
          carsQuery = carsQuery.eq('status', 'Available');
        } else if (searchTerms.includes('maintenance')) {
          carsQuery = carsQuery.eq('status', 'In Maintenance');
        } else if (searchTerms.includes('rented')) {
          carsQuery = carsQuery.eq('status', 'Rented');
        }
      }

      const { data: cars, error } = await carsQuery;
      
      if (error) throw error;

      if (!cars || cars.length === 0) {
        return 'No cars found matching your criteria.';
      }

      let carInfo = `CURRENT CAR FLEET STATUS (${cars.length} vehicles):\n\n`;
      
      const groupedByType = cars.reduce((acc: any, car) => {
        if (!acc[car.type]) acc[car.type] = [];
        acc[car.type].push(car);
        return acc;
      }, {});

      Object.entries(groupedByType).forEach(([type, vehicles]: [string, any]) => {
        carInfo += `${type.toUpperCase()} CATEGORY:\n`;
        vehicles.forEach((car: any) => {
          carInfo += `- ${car.model} (${car.type}) - $${car.price_per_day}/day - Status: ${car.status}\n`;
          carInfo += `  Transmission: ${car.transmission}, Seats: ${car.seats}, Color: ${car.color}\n`;
          carInfo += `  Plate: ${car.plate_number}, Condition: ${car.condition}\n`;
        });
        carInfo += '\n';
      });

      return carInfo.trim();
    } catch (error) {
      console.error('Error fetching car data:', error);
      return 'Unable to retrieve current car information.';
    }
  }

  // Fetch recent booking activity and statistics
  async fetchBookingData(userType: 'customer' | 'admin', userId?: string): Promise<string> {
    try {
      const supabase = await this.getSupabase();
      let bookingsQuery = supabase
        .from('bookings')
        .select(`
          *,
          cars(model, type),
          profiles(full_name, email)
        `)
        .order('created_at', { ascending: false });

      // Filter by user for customers
      if (userType === 'customer' && userId) {
        bookingsQuery = bookingsQuery.eq('customer_id', userId);
      }

      // Limit results based on user type
      bookingsQuery = bookingsQuery.limit(userType === 'admin' ? 20 : 10);

      const { data: bookings, error } = await bookingsQuery;
      
      if (error) throw error;

      if (!bookings || bookings.length === 0) {
        return userType === 'customer' 
          ? 'You have no booking history.' 
          : 'No recent bookings found.';
      }

      let bookingInfo = userType === 'customer' 
        ? `YOUR BOOKING HISTORY (${bookings.length} bookings):\n\n`
        : `RECENT BOOKING ACTIVITY (${bookings.length} latest bookings):\n\n`;

      bookings.forEach((booking: any, index: number) => {
        const car = booking.cars || {};
        const customer = booking.profiles || {};
        
        bookingInfo += `${index + 1}. Booking #${booking.id}\n`;
        bookingInfo += `   Vehicle: ${car.model || 'N/A'} (${car.type || 'N/A'})\n`;
        bookingInfo += `   Status: ${booking.status}\n`;
        bookingInfo += `   Dates: ${booking.start_date} to ${booking.end_date}\n`;
        bookingInfo += `   Total: $${booking.total_amount || 'N/A'}\n`;
        
        if (userType === 'admin') {
          bookingInfo += `   Customer: ${customer.full_name || customer.email || 'N/A'}\n`;
        }
        
        if (booking.notes) {
          bookingInfo += `   Notes: ${booking.notes}\n`;
        }
        bookingInfo += '\n';
      });

      // Add summary statistics for admin
      if (userType === 'admin') {
        const statusCounts = bookings.reduce((acc: any, booking: any) => {
          acc[booking.status] = (acc[booking.status] || 0) + 1;
          return acc;
        }, {});

        bookingInfo += 'BOOKING STATUS SUMMARY:\n';
        Object.entries(statusCounts).forEach(([status, count]) => {
          bookingInfo += `- ${status}: ${count} bookings\n`;
        });
      }

      return bookingInfo.trim();
    } catch (error) {
      console.error('Error fetching booking data:', error);
      return 'Unable to retrieve booking information.';
    }
  }

  // Fetch system statistics for admin users
  async fetchSystemStats(): Promise<string> {
    try {
      const supabase = await this.getSupabase();
      const [carsResult, bookingsResult, profilesResult] = await Promise.all([
        supabase.from('cars').select('status').order('id'),
        supabase.from('bookings').select('status, created_at').gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()),
        supabase.from('profiles').select('role').order('created_at', { ascending: false })
      ]);

      let statsInfo = 'PATHLINK SYSTEM STATISTICS:\n\n';

      // Car fleet statistics
      if (carsResult.data) {
        const carStats = carsResult.data.reduce((acc: any, car: any) => {
          acc[car.status] = (acc[car.status] || 0) + 1;
          return acc;
        }, {});

        statsInfo += 'FLEET STATUS:\n';
        Object.entries(carStats).forEach(([status, count]) => {
          statsInfo += `- ${status}: ${count} vehicles\n`;
        });
        statsInfo += `Total Fleet: ${carsResult.data.length} vehicles\n\n`;
      }

      // Recent booking statistics
      if (bookingsResult.data) {
        const bookingStats = bookingsResult.data.reduce((acc: any, booking: any) => {
          acc[booking.status] = (acc[booking.status] || 0) + 1;
          return acc;
        }, {});

        statsInfo += 'LAST 30 DAYS BOOKINGS:\n';
        Object.entries(bookingStats).forEach(([status, count]) => {
          statsInfo += `- ${status}: ${count} bookings\n`;
        });
        statsInfo += `Total: ${bookingsResult.data.length} bookings\n\n`;
      }

      // User statistics
      if (profilesResult.data) {
        const userStats = profilesResult.data.reduce((acc: any, profile: any) => {
          acc[profile.role] = (acc[profile.role] || 0) + 1;
          return acc;
        }, {});

        statsInfo += 'USER ACCOUNTS:\n';
        Object.entries(userStats).forEach(([role, count]) => {
          statsInfo += `- ${role}: ${count} users\n`;
        });
        statsInfo += `Total Users: ${profilesResult.data.length}\n`;
      }

      return statsInfo.trim();
    } catch (error) {
      console.error('Error fetching system stats:', error);
      return 'Unable to retrieve system statistics.';
    }
  }

  // Fetch customer-specific data
  async fetchCustomerContext(userId: string, query: string): Promise<string> {
    try {
      const supabase = await this.getSupabase();
      const [bookingsResult, profileResult] = await Promise.all([
        supabase
          .from('bookings')
          .select('*, cars(model, type)')
          .eq('customer_id', userId)
          .order('created_at', { ascending: false })
          .limit(5),
        supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single()
      ]);

      let context = 'YOUR ACCOUNT INFORMATION:\n\n';

      if (profileResult.data) {
        const profile = profileResult.data;
        context += `Name: ${profile.full_name || 'Not provided'}\n`;
        context += `Email: ${profile.email}\n`;
        context += `Phone: ${profile.phone || 'Not provided'}\n`;
        context += `Member since: ${new Date(profile.created_at).toLocaleDateString()}\n\n`;
      }

      if (bookingsResult.data && bookingsResult.data.length > 0) {
        context += await this.fetchBookingData('customer', userId);
      } else {
        context += 'You have no previous bookings. Browse our available cars to make your first booking!';
      }

      return context;
    } catch (error) {
      console.error('Error fetching customer context:', error);
      return 'Unable to retrieve your account information.';
    }
  }

  // Fetch vehicle categories with real-time statistics
  async fetchVehicleCategories(): Promise<string> {
    try {
      const supabase = await this.getSupabase();
      const { data: categories, error } = await supabase
        .from('vehicle_categories')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      if (error) throw error;

      if (!categories || categories.length === 0) {
        return 'No vehicle categories available.';
      }

      let categoryInfo = 'VEHICLE CATEGORIES AND PRICING:\n\n';
      
      for (const category of categories) {
        // Get car count for each category
        const { data: cars } = await supabase
          .from('cars')
          .select('id, status')
          .eq('category_id', category.id)
          .eq('is_archived', false);

        const totalVehicles = cars?.length || 0;
        const availableVehicles = cars?.filter(car => car.status === 'Available').length || 0;

        categoryInfo += `${category.name}:\n`;
        categoryInfo += `  Type: ${category.type}\n`;
        categoryInfo += `  Price Range: $${category.min_price} - $${category.max_price} per day\n`;
        categoryInfo += `  Vehicles: ${availableVehicles}/${totalVehicles} available\n`;
        categoryInfo += `  Description: ${category.description}\n\n`;
      }

      return categoryInfo.trim();
    } catch (error) {
      console.error('Error fetching vehicle categories:', error);
      return 'Unable to retrieve vehicle category information.';
    }
  }

  // Fetch payment information for admin users
  async fetchPaymentData(userType: 'customer' | 'admin', userId?: string): Promise<string> {
    try {
      const supabase = await this.getSupabase();
      let paymentsQuery = supabase
        .from('payments')
        .select(`
          *,
          bookings!inner(
            id,
            customer_id,
            cars(model, type),
            profiles(full_name, email)
          )
        `)
        .order('created_at', { ascending: false });

      // Filter by user for customers
      if (userType === 'customer' && userId) {
        paymentsQuery = paymentsQuery.eq('bookings.customer_id', userId);
      }

      paymentsQuery = paymentsQuery.limit(userType === 'admin' ? 15 : 10);

      const { data: payments, error } = await paymentsQuery;
      
      if (error) throw error;

      if (!payments || payments.length === 0) {
        return userType === 'customer' 
          ? 'You have no payment history.' 
          : 'No recent payments found.';
      }

      let paymentInfo = userType === 'customer' 
        ? `YOUR PAYMENT HISTORY (${payments.length} transactions):\n\n`
        : `RECENT PAYMENT ACTIVITY (${payments.length} latest transactions):\n\n`;

      payments.forEach((payment: any, index: number) => {
        const booking = payment.bookings || {};
        const car = booking.cars || {};
        const customer = booking.profiles || {};
        
        paymentInfo += `${index + 1}. Payment #${payment.transaction_id || payment.id}\n`;
        paymentInfo += `   Amount: $${payment.amount}\n`;
        paymentInfo += `   Status: ${payment.status}\n`;
        paymentInfo += `   Method: ${payment.method}\n`;
        paymentInfo += `   Vehicle: ${car.model || 'N/A'} (${car.type || 'N/A'})\n`;
        
        if (userType === 'admin') {
          paymentInfo += `   Customer: ${customer.full_name || customer.email || 'N/A'}\n`;
        }
        
        paymentInfo += `   Date: ${new Date(payment.transaction_date || payment.created_at).toLocaleDateString()}\n\n`;
      });

      return paymentInfo.trim();
    } catch (error) {
      console.error('Error fetching payment data:', error);
      return 'Unable to retrieve payment information.';
    }
  }

  // Fetch document verification status
  async fetchDocumentStatus(userType: 'customer' | 'admin', userId?: string): Promise<string> {
    try {
      const supabase = await this.getSupabase();
      
      if (userType === 'customer' && userId) {
        // Customer view: their documents
        const { data: documents, error } = await supabase
          .from('booking_documents')
          .select(`
            *,
            bookings!inner(id, status, pickup_datetime, cars(model))
          `)
          .eq('bookings.customer_id', userId)
          .order('created_at', { ascending: false })
          .limit(10);

        if (error) throw error;

        if (!documents || documents.length === 0) {
          return 'You have no document submissions.';
        }

        let docInfo = `YOUR DOCUMENT STATUS (${documents.length} submissions):\n\n`;
        documents.forEach((doc: any, index: number) => {
          const booking = doc.bookings || {};
          const car = booking.cars || {};
          
          docInfo += `${index + 1}. ${doc.document_type.replace('_', ' ').toUpperCase()}\n`;
          docInfo += `   Status: ${doc.verification_status}\n`;
          docInfo += `   Booking: ${car.model || 'N/A'}\n`;
          docInfo += `   Uploaded: ${new Date(doc.created_at).toLocaleDateString()}\n`;
          if (doc.verification_notes) {
            docInfo += `   Notes: ${doc.verification_notes}\n`;
          }
          docInfo += '\n';
        });

        return docInfo.trim();
      } else if (userType === 'admin') {
        // Admin view: pending documents
        const { data: documents, error } = await supabase
          .from('booking_documents')
          .select(`
            *,
            bookings!inner(
              id,
              status,
              profiles(full_name, email),
              cars(model)
            )
          `)
          .eq('verification_status', 'pending')
          .order('created_at', { ascending: false })
          .limit(15);

        if (error) throw error;

        if (!documents || documents.length === 0) {
          return 'No pending documents for verification.';
        }

        let docInfo = `PENDING DOCUMENT VERIFICATIONS (${documents.length} items):\n\n`;
        documents.forEach((doc: any, index: number) => {
          const booking = doc.bookings || {};
          const customer = booking.profiles || {};
          const car = booking.cars || {};
          
          docInfo += `${index + 1}. ${doc.document_type.replace('_', ' ').toUpperCase()}\n`;
          docInfo += `   Customer: ${customer.full_name || customer.email || 'N/A'}\n`;
          docInfo += `   Vehicle: ${car.model || 'N/A'}\n`;
          docInfo += `   Submitted: ${new Date(doc.created_at).toLocaleDateString()}\n`;
          docInfo += `   Booking ID: ${booking.id}\n\n`;
        });

        return docInfo.trim();
      }

      return 'Unable to retrieve document information.';
    } catch (error) {
      console.error('Error fetching document status:', error);
      return 'Unable to retrieve document status.';
    }
  }

  // Fetch GPS tracking data for admin users
  async fetchGPSData(): Promise<string> {
    try {
      const supabase = await this.getSupabase();
      const { data: gpsData, error } = await supabase
        .from('gps_locations')
        .select(`
          *,
          cars!inner(model, type, plate_number, status)
        `)
        .order('timestamp', { ascending: false })
        .limit(10);

      if (error) throw error;

      if (!gpsData || gpsData.length === 0) {
        return 'No recent GPS tracking data available.';
      }

      let gpsInfo = `RECENT GPS TRACKING DATA (${gpsData.length} latest updates):\n\n`;
      
      gpsData.forEach((gps: any, index: number) => {
        const car = gps.cars || {};
        
        gpsInfo += `${index + 1}. ${car.model || 'Unknown'} (${car.plate_number || 'N/A'})\n`;
        gpsInfo += `   Location: ${gps.latitude}, ${gps.longitude}\n`;
        gpsInfo += `   Status: ${gps.status}\n`;
        gpsInfo += `   Speed: ${gps.speed || 0} km/h\n`;
        gpsInfo += `   Last Update: ${new Date(gps.timestamp).toLocaleString()}\n`;
        gpsInfo += `   Vehicle Status: ${car.status}\n\n`;
      });

      return gpsInfo.trim();
    } catch (error) {
      console.error('Error fetching GPS data:', error);
      return 'Unable to retrieve GPS tracking information.';
    }
  }

  // Fetch customer issue tracking data for admin users
  async fetchIssueData(): Promise<string> {
    try {
      const supabase = await this.getSupabase();
      const { data: issues, error } = await supabase
        .from('renter_issues')
        .select(`
          *,
          issue_categories(name, color),
          profiles!renter_issues_customer_id_fkey(full_name, email),
          bookings(id, cars(model))
        `)
        .eq('resolved', false)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      if (!issues || issues.length === 0) {
        return 'No unresolved customer issues.';
      }

      let issueInfo = `UNRESOLVED CUSTOMER ISSUES (${issues.length} active):\n\n`;
      
      issues.forEach((issue: any, index: number) => {
        const category = issue.issue_categories || {};
        const customer = issue.profiles || {};
        const booking = issue.bookings || {};
        const car = booking?.cars || {};
        
        issueInfo += `${index + 1}. ${category.name || 'General Issue'}\n`;
        issueInfo += `   Customer: ${customer.full_name || customer.email || 'N/A'}\n`;
        issueInfo += `   Severity: ${issue.severity}\n`;
        issueInfo += `   Description: ${issue.description}\n`;
        if (car.model) {
          issueInfo += `   Related Vehicle: ${car.model}\n`;
        }
        issueInfo += `   Created: ${new Date(issue.created_at).toLocaleDateString()}\n\n`;
      });

      return issueInfo.trim();
    } catch (error) {
      console.error('Error fetching issue data:', error);
      return 'Unable to retrieve customer issue information.';
    }
  }

  // Main method to get comprehensive context based on query
  async getLiveContext(query: string, userType: 'customer' | 'admin', userId?: string): Promise<string> {
    try {
      const queryLower = query.toLowerCase();
      let context = '';

      // Determine what data to fetch based on query content
      const needsCarData = queryLower.includes('car') || queryLower.includes('vehicle') || 
                          queryLower.includes('available') || queryLower.includes('fleet') ||
                          queryLower.includes('rent') || queryLower.includes('booking');

      const needsCategoryData = queryLower.includes('category') || queryLower.includes('categories') ||
                               queryLower.includes('type') || queryLower.includes('pricing') ||
                               queryLower.includes('price');

      const needsBookingData = queryLower.includes('booking') || queryLower.includes('reservation') ||
                              queryLower.includes('history') || queryLower.includes('status');

      const needsPaymentData = queryLower.includes('payment') || queryLower.includes('transaction') ||
                              queryLower.includes('money') || queryLower.includes('cost') ||
                              queryLower.includes('refund') || queryLower.includes('billing');

      const needsDocumentData = queryLower.includes('document') || queryLower.includes('verification') ||
                               queryLower.includes('upload') || queryLower.includes('requirement') ||
                               queryLower.includes('approval');

      const needsGPSData = userType === 'admin' && (queryLower.includes('gps') || queryLower.includes('location') ||
                          queryLower.includes('tracking') || queryLower.includes('map'));

      const needsIssueData = userType === 'admin' && (queryLower.includes('issue') || queryLower.includes('problem') ||
                            queryLower.includes('complaint') || queryLower.includes('behavior'));

      const needsStatsData = userType === 'admin' && (queryLower.includes('stats') || 
                            queryLower.includes('statistics') || queryLower.includes('summary') ||
                            queryLower.includes('overview') || queryLower.includes('analytics'));

      const needsPersonalData = userType === 'customer' && (queryLower.includes('my') || 
                               queryLower.includes('account') || queryLower.includes('profile'));

      // Fetch relevant data based on query analysis
      if (needsCarData) {
        const carData = await this.fetchCarData(query);
        context += carData + '\n\n';
      }

      if (needsCategoryData) {
        const categoryData = await this.fetchVehicleCategories();
        context += categoryData + '\n\n';
      }

      if (needsBookingData) {
        const bookingData = await this.fetchBookingData(userType, userId);
        context += bookingData + '\n\n';
      }

      if (needsPaymentData) {
        const paymentData = await this.fetchPaymentData(userType, userId);
        context += paymentData + '\n\n';
      }

      if (needsDocumentData) {
        const documentData = await this.fetchDocumentStatus(userType, userId);
        context += documentData + '\n\n';
      }

      if (needsGPSData) {
        const gpsData = await this.fetchGPSData();
        context += gpsData + '\n\n';
      }

      if (needsIssueData) {
        const issueData = await this.fetchIssueData();
        context += issueData + '\n\n';
      }

      if (needsStatsData) {
        const statsData = await this.fetchSystemStats();
        context += statsData + '\n\n';
      }

      if (needsPersonalData && userId) {
        const personalData = await this.fetchCustomerContext(userId, query);
        context += personalData + '\n\n';
      }

      // If no specific data was requested, provide general context based on user type
      if (!context.trim()) {
        if (userType === 'admin') {
          // Provide comprehensive admin overview
          const [statsData, carData] = await Promise.all([
            this.fetchSystemStats(),
            this.fetchCarData()
          ]);
          context = statsData + '\n\n' + carData;
        } else {
          // Provide general customer information
          const [carData, categoryData] = await Promise.all([
            this.fetchCarData(),
            this.fetchVehicleCategories()
          ]);
          context = carData + '\n\n' + categoryData;
        }
      }

      return context.trim() || 'No relevant live data found for your query.';
    } catch (error) {
      console.error('Error getting live context:', error);
      return 'Unable to retrieve current system information.';
    }
  }
}

export async function getLiveWebsiteContext(
  query: string, 
  userType: 'customer' | 'admin', 
  userId?: string
): Promise<string> {
  const fetcher = new LiveDataFetcher();
  return await fetcher.getLiveContext(query, userType, userId);
}
