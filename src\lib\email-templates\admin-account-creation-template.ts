// Admin account creation email template
export function buildAdminAccountCreationEmail(params: {
  adminName?: string | null;
  adminEmail: string;
  temporaryPassword: string;
  createdBy?: string;
  loginUrl?: string;
}): { subject: string; html: string } {
  const { 
    adminName, 
    adminEmail, 
    temporaryPassword,
    createdBy,
    loginUrl = "https://olliesrentalcar.pathlinkio.app/admin-auth" 
  } = params;
  
  const subject = `Welcome to <PERSON><PERSON>'s Rent A Car - Your Admin Account Has Been Created`;
  const greeting = adminName ? `Dear ${adminName},` : `Hello,`;
  
  const html = `
    <div style="font-family: Arial, sans-serif; line-height:1.6; max-width: 600px; margin: 0 auto; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
      <!-- Header with Logo -->
      <div style="text-align: center; padding: 20px; background-color: #f8fafc; border-bottom: 2px solid #e5e7eb;">
        <img src="https://pathlinkio.app/ollie_logo.jpg" alt="O<PERSON>'s Rent A Car" style="height: 60px; width: auto; margin-bottom: 10px;">
        <h1 style="color: #1f2937; margin: 0; font-size: 24px;">Ollie's Rent A Car</h1>
      </div>
      
      <!-- Welcome Header -->
      <div style="background-color: #dbeafe; padding: 16px; border-bottom: 1px solid #e5e7eb;">
        <h2 style="margin: 0; color: #1e3a8a; font-size: 18px;">
          🎉 Welcome to the Admin Team!
        </h2>
        <div style="display: inline-block; background-color: #10b981; color: #ffffff; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin-top: 8px;">
          Account Created
        </div>
      </div>
      
      <!-- Main Message -->
      <div style="padding: 20px; border-bottom: 1px solid #e5e7eb;">
        <p>${greeting}</p>
        <p>Your admin account has been successfully created by ${createdBy || 'the super administrator'}. You now have access to the Ollie's Rent A Car administrative dashboard.</p>
        
        <div style="background-color: #f0f9ff; border: 1px solid #bae6fd; border-radius: 6px; padding: 16px; margin: 16px 0;">
          <h3 style="margin: 0 0 12px 0; color: #1e40af; font-size: 16px;">🔐 Your Login Credentials:</h3>
          <div style="background-color: #ffffff; border: 1px solid #e5e7eb; border-radius: 4px; padding: 12px; margin: 8px 0;">
            <p style="margin: 0 0 8px 0; color: #374151;"><strong>Email:</strong> ${escapeHtml(adminEmail)}</p>
            <p style="margin: 0; color: #374151;"><strong>Temporary Password:</strong> <code style="background-color: #f3f4f6; padding: 2px 6px; border-radius: 3px; font-family: monospace; font-size: 14px;">${escapeHtml(temporaryPassword)}</code></p>
          </div>
        </div>
        
        <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 16px; margin: 16px 0;">
          <p style="margin: 0; font-weight: bold; color: #92400e;">🛡️ Important Security Notice:</p>
          <p style="margin: 8px 0 0 0; color: #92400e;">Please change your password immediately after your first login for security purposes.</p>
        </div>
        
        <p><strong>What's Next:</strong></p>
        <ul style="color: #4b5563; padding-left: 20px;">
          <li>Click the "Access Admin Dashboard" button below</li>
          <li>Sign in using your credentials</li>
          <li>Change your temporary password</li>
          <li>Explore your admin dashboard features</li>
        </ul>
      </div>
      
      <!-- Access Button -->
      <div style="padding: 20px; background-color: #f0f9ff; border-bottom: 1px solid #e5e7eb; text-align: center;">
        <h3 style="margin: 0 0 16px 0; color: #1e3a8a; font-size: 16px;">Ready to Get Started?</h3>
        <a href="${loginUrl}" style="background:#1d4ed8;color:#fff;padding:12px 24px;border-radius:6px;text-decoration:none;display:inline-block;font-weight:bold;font-size:16px;">
          🚀 Access Admin Dashboard
        </a>
        <p style="margin: 12px 0 0 0; font-size: 12px; color: #6b7280;">
          If the button doesn't work, copy and paste this link: ${loginUrl}
        </p>
      </div>
      
      <!-- Admin Features -->
      <div style="padding: 20px; background-color: #fffbeb; border-bottom: 1px solid #e5e7eb;">
        <h3 style="margin: 0 0 12px 0; color: #92400e; font-size: 16px;">📊 Your Admin Access Includes:</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; color: #92400e; font-size: 14px;">
          <div>✅ Dashboard & Analytics</div>
          <div>✅ Fleet Management</div>
          <div>✅ Booking Management</div>
          <div>✅ Payment Processing</div>
          <div>✅ Customer Management</div>
          <div>✅ Sales Tracking</div>
        </div>
      </div>
      
      <!-- Support Information -->
      <div style="padding: 20px; background-color: #f9fafb; border-bottom: 1px solid #e5e7eb;">
        <h3 style="margin: 0 0 12px 0; color: #374151; font-size: 16px;">💬 Need Help?</h3>
        <p style="margin: 0; color: #6b7280; font-size: 14px;">
          If you have any questions or need assistance with your admin account, please contact the super administrator or our technical support team.
        </p>
      </div>
      
      <!-- Footer -->
      <div style="padding: 20px; background-color: #f3f4f6; text-align: center; font-size: 12px; color: #6b7280;">
        <p>Welcome to the Ollie's Rent A Car admin team!</p>
        <p style="margin-top: 12px;">We're excited to have you on board.</p>
        
        <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #e5e7eb;">
          <p style="margin: 0; font-size: 11px; color: #9ca3af;">
            This is an automated message. Please do not reply directly to this email.
          </p>
        </div>
      </div>
    </div>
  `;
  
  return { subject, html };
}

function escapeHtml(input: string) {
  return input
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/\"/g, "&quot;")
    .replace(/'/g, "&#039;");
}
