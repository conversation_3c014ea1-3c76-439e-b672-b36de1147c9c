"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  CheckCircle,
  Calendar,
  MapPin,
  User,
  Mail,
  Phone,
  Car,
  Loader2,
  FileText,
  Shield,
  CreditCard,
  Cog,
  Fuel,
} from "lucide-react";
import type { BookingData } from "./booking-flow";
import type { DocumentFile } from "@/components/ui/document-upload";
import { useRouter } from "next/navigation";
import {
  CustomerBookingLoading,
  CustomerLoadingOverlay,
} from "@/components/customer-side/loading/customer-loading";
import Image from "next/image";
import { createBooking } from "@/app/customer/booking/actions/booking-actions";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { syncServerSession } from "@/app/customer/booking/actions/auth-actions";
import Link from "next/link";
import { calculateBooking<PERSON>ota<PERSON>, getBookingCostBreakdown } from "@/utils/booking-total-calculation";
import { formatBookingIdForDisplay } from "@/lib/reference-ids";

interface ConfirmationStepProps {
  bookingData: BookingData;
}

export function ConfirmationStep({ bookingData }: ConfirmationStepProps) {
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [isCompleted, setIsCompleted] = React.useState(false);
  const [bookingId, setBookingId] = React.useState<string | null>(null);
  const [bookingRef, setBookingRef] = React.useState<string | null>(null);
  const [acceptedTerms, setAcceptedTerms] = React.useState(false);
  const [acceptedPrivacy, setAcceptedPrivacy] = React.useState(false);
  const router = useRouter();
  const { session } = useCustomerAuth();

  const { selectedCar } = bookingData;

  if (!selectedCar) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <Car className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Booking incomplete
          </h3>
          <p className="text-gray-600">
            Please complete all previous steps to proceed.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Use centralized calculation utility for accurate totals
  const bookingTotals = calculateBookingTotal(bookingData, selectedCar);
  const costBreakdown = getBookingCostBreakdown(bookingData, selectedCar);

  const formatDateTime = (dateTimeStr: string) => {
    if (!dateTimeStr) return "Not selected";
    const date = new Date(dateTimeStr);
    return date.toLocaleString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Sanitize booking data to remove File objects before sending to server
  const sanitizeBookingData = (data: any) => {
    const sanitized = { ...data };

    // Helper to sanitize document file arrays by removing File objects
    const sanitizeDocumentFiles = (files: any) => {
      if (!Array.isArray(files)) return files;
      return files.map((file: any) => ({
        id: file.id,
        url: file.url,
        status: file.status,
        error: file.error,
        progress: file.progress,
        fileName: file.fileName,
        fileSize: file.fileSize,
        fileType: file.fileType,
        // Remove the File object to prevent serialization issues
      }));
    };

    // Sanitize all document file arrays
    if (sanitized.driversLicense) {
      sanitized.driversLicense = sanitizeDocumentFiles(
        sanitized.driversLicense
      );
    }
    if (sanitized.governmentId) {
      sanitized.governmentId = sanitizeDocumentFiles(sanitized.governmentId);
    }
    if (sanitized.proofOfBilling) {
      sanitized.proofOfBilling = sanitizeDocumentFiles(sanitized.proofOfBilling);
    }
    if (sanitized.proofOfPayment) {
      sanitized.proofOfPayment = sanitizeDocumentFiles(
        sanitized.proofOfPayment
      );
    }

    return sanitized;
  };

  const handleConfirmBooking = async () => {
    setIsSubmitting(true);

    try {
      // Ensure server-side sees the same session as the client (cookies)
      if (session?.access_token && session?.refresh_token) {
        await syncServerSession({
          access_token: session.access_token,
          refresh_token: session.refresh_token,
        });
      }

      // Sanitize booking data to remove File objects
      const sanitizedBookingData = sanitizeBookingData(bookingData);
      const result = await createBooking(sanitizedBookingData);

      if (result.error) {
        throw new Error(result.error.message);
      }

      if (result.data && result.data.length > 0) {
        const newBooking = result.data[0];
        setBookingId(newBooking.id); // Use the real booking ID from Supabase
        setBookingRef(newBooking.booking_ref || null); // Store booking reference if available
        setIsCompleted(true);
        localStorage.removeItem("ollietrack-booking-data");
      } else {
        throw new Error("Booking creation failed: No data returned.");
      }
    } catch (error) {
      console.error("Failed to process booking request:", error);
      const errorMessage =
        error instanceof Error ? error.message : "An unknown error occurred.";
      alert(`Failed to process booking request: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };


  if (isCompleted) {
    return (
      <Card className="overflow-hidden">
        <CardContent className="text-center py-8 sm:py-12 px-4 sm:px-6">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 break-words">
            Booking Request Submitted!
          </h3>
          <p className="text-sm sm:text-base text-gray-600 mb-6 max-w-md mx-auto break-words">
            Your booking request has been successfully submitted. Our team will
            review your request and contact you within 24 hours to confirm
            availability and finalize the details.
          </p>
          <div className="bg-blue-50 rounded-lg p-3 sm:p-4 mb-6">
            <p className="text-sm font-medium text-blue-900">
              Booking Reference: {bookingId ? formatBookingIdForDisplay({ id: bookingId, booking_ref: bookingRef }) : "Generating..."}
            </p>
            <p className="text-sm text-blue-800">
              Please save this reference for your records. You can track your
              booking status in your dashboard.
            </p>
          </div>
          <div className="flex justify-center">
            <Button
              onClick={() => router.push("/customer/dashboard")}
              className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
            >
              Go to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-3 xs:space-y-4 sm:space-y-6 max-w-full">
      {/* Booking Summary */}
      <Card className="overflow-hidden">
        <CardHeader className="p-3 xs:p-4 sm:p-6">
          <CardTitle className="text-base xs:text-lg sm:text-xl break-words leading-tight">
            Final Booking Summary
          </CardTitle>
          <p className="text-xs xs:text-sm text-gray-600 break-words leading-relaxed mt-1 xs:mt-2">
            Please review all details before confirming your booking.
          </p>
        </CardHeader>
        <CardContent className="p-3 xs:p-4 sm:p-6 pt-0 space-y-3 xs:space-y-4 sm:space-y-6">
          {/* Selected Car */}
          <div className="bg-gray-50 rounded-lg p-2 xs:p-3 sm:p-4 space-y-3 xs:space-y-4">
            <h3 className="text-sm xs:text-base sm:text-lg font-semibold text-gray-900 leading-tight">
              Selected Vehicle
            </h3>
            <div className="flex flex-col sm:flex-row gap-3 xs:gap-4">
              <div className="w-full sm:w-40 md:w-48 flex-shrink-0">
                <div className="relative w-full h-24 xs:h-28 sm:h-32 bg-white rounded-lg overflow-hidden">
                  <Image
                    src={selectedCar.image_url || "/placeholder.svg"}
                    alt={selectedCar.model}
                    fill
                    sizes="(max-width: 640px) 100vw, (max-width: 768px) 160px, 192px"
                    style={{ objectFit: "contain" }}
                  />
                </div>
              </div>
              <div className="flex-1 min-w-0 space-y-3 xs:space-y-4">
                <div className="space-y-1">
                  <h4 className="font-bold text-gray-900 text-sm xs:text-base sm:text-lg break-words leading-tight">
                    {selectedCar.model}
                  </h4>
                  <p className="text-gray-500 text-xs xs:text-sm font-medium leading-tight uppercase tracking-wide">{selectedCar.type}</p>
                </div>
                
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 xs:gap-3">
                  <div className="flex items-center gap-2 bg-white rounded-lg p-2 xs:p-3 border border-gray-100">
                    <div className="w-6 h-6 xs:w-7 xs:h-7 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <User className="w-3 h-3 xs:w-4 xs:h-4 text-blue-600" />
                    </div>
                    <div className="min-w-0">
                      <p className="text-xs xs:text-sm font-semibold text-gray-900 truncate">
                        {selectedCar.seats}
                      </p>
                      <p className="text-xs text-gray-500">Seats</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 bg-white rounded-lg p-2 xs:p-3 border border-gray-100">
                    <div className="w-6 h-6 xs:w-7 xs:h-7 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <Cog className="w-3 h-3 xs:w-4 xs:h-4 text-green-600" />
                    </div>
                    <div className="min-w-0">
                      <p className="text-xs xs:text-sm font-semibold text-gray-900 truncate">
                        {selectedCar.transmission}
                      </p>
                      <p className="text-xs text-gray-500">Transmission</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 bg-white rounded-lg p-2 xs:p-3 border border-gray-100">
                    <div className="w-6 h-6 xs:w-7 xs:h-7 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <Fuel className="w-3 h-3 xs:w-4 xs:h-4 text-orange-600" />
                    </div>
                    <div className="min-w-0">
                      <p className="text-xs xs:text-sm font-semibold text-gray-900 truncate">
                        {selectedCar.fuel_type.split("/")[0]}
                      </p>
                      <p className="text-xs text-gray-500">Fuel Type</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Rental Details */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 xs:gap-4">
            <div className="bg-green-50 rounded-lg p-2 xs:p-3 sm:p-4 space-y-2 xs:space-y-3">
              <h4 className="font-semibold text-gray-900 flex items-center gap-2 text-xs xs:text-sm sm:text-base leading-tight">
                <MapPin className="h-3 w-3 xs:h-4 xs:w-4 text-green-600 flex-shrink-0" />
                <span className="truncate">Pick-up Details</span>
              </h4>
              <p className="text-xs xs:text-sm text-gray-600 break-words leading-tight">
                {bookingData.pickUpLocation}
              </p>
              <div className="text-xs sm:text-sm text-gray-500">
                <span className="text-gray-600 break-words leading-tight">
                  {formatDateTime(
                    bookingData.pickUpDate + "T" + bookingData.pickUpTime
                  )}
                </span>
              </div>
            </div>

            <div className="bg-orange-50 rounded-lg p-2 xs:p-3 sm:p-4 space-y-2 xs:space-y-3">
              <h4 className="font-semibold text-gray-900 flex items-center gap-2 text-xs xs:text-sm sm:text-base leading-tight">
                <MapPin className="h-3 w-3 xs:h-4 xs:w-4 text-orange-600 flex-shrink-0" />
                <span className="truncate">Drop-off Details</span>
              </h4>
              <p className="text-xs xs:text-sm text-gray-600 break-words leading-tight">
                {bookingData.dropOffLocation}
              </p>
              <div className="text-xs sm:text-sm text-gray-500">
                <span className="text-gray-600 break-words leading-tight">
                  {formatDateTime(
                    bookingData.dropOffDate + "T" + bookingData.dropOffTime
                  )}
                </span>
              </div>
            </div>
          </div>

          {/* Customer Information */}
          <div className="bg-blue-50 rounded-lg p-2 xs:p-3 sm:p-4 space-y-2 xs:space-y-3">
            <h4 className="font-semibold text-gray-900 flex items-center gap-2 text-xs xs:text-sm sm:text-base leading-tight">
              <User className="h-3 w-3 xs:h-4 xs:w-4 text-blue-600 flex-shrink-0" />
              <span className="truncate">Customer Information</span>
            </h4>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
              <div className="space-y-1 xs:space-y-2 min-w-0">
                <div className="flex items-center gap-2 min-w-0">
                  <User className="h-3 w-3 text-gray-400 flex-shrink-0" />
                  <span className="text-xs xs:text-sm text-gray-600 break-words leading-tight min-w-0">
                    {bookingData.customerName}
                  </span>
                </div>
                <div className="flex items-center gap-2 min-w-0">
                  <Mail className="h-3 w-3 text-gray-400 flex-shrink-0" />
                  <span className="text-xs xs:text-sm text-gray-600 break-all leading-tight min-w-0">
                    {bookingData.customerEmail}
                  </span>
                </div>
                <div className="flex items-center gap-2 min-w-0">
                  <Phone className="h-3 w-3 text-gray-400 flex-shrink-0" />
                  <span className="text-xs xs:text-sm text-gray-600 break-words leading-tight min-w-0">
                    {bookingData.customerPhone}
                  </span>
                </div>
              </div>
            </div>
            {bookingData.notes && (
              <div className="mt-2 xs:mt-3 pt-2 xs:pt-3 border-t border-blue-200">
                <p className="text-xs xs:text-sm text-gray-600 break-words leading-tight">
                  <strong className="font-medium">Special Requests:</strong> {bookingData.notes}
                </p>
              </div>
            )}
          </div>

          {/* Uploaded Requirements */}
          <div className="bg-green-50 rounded-lg p-2 xs:p-3 sm:p-4 space-y-2 xs:space-y-3">
            <h4 className="font-semibold text-gray-900 flex items-center gap-2 text-xs xs:text-sm sm:text-base leading-tight">
              <Shield className="h-3 w-3 xs:h-4 xs:w-4 text-green-600 flex-shrink-0" />
              <span className="truncate">Required Documents</span>
            </h4>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-1 xs:gap-2 sm:gap-3">
              {bookingData.driversLicense &&
                bookingData.driversLicense.length > 0 && (
                  <div className="flex items-center gap-2 min-w-0">
                    <CheckCircle className="h-3 w-3 xs:h-4 xs:w-4 text-green-600 flex-shrink-0" />
                    <span className="text-xs xs:text-sm text-gray-700 break-words leading-tight min-w-0">
                      Driver's License ({bookingData.driversLicense.length} file
                      {bookingData.driversLicense.length > 1 ? "s" : ""})
                    </span>
                  </div>
                )}
              {bookingData.governmentId &&
                bookingData.governmentId.length > 0 && (
                  <div className="flex items-center gap-2 min-w-0">
                    <CheckCircle className="h-3 w-3 xs:h-4 xs:w-4 text-green-600 flex-shrink-0" />
                    <span className="text-xs xs:text-sm text-gray-700 break-words leading-tight min-w-0">
                      Government ID ({bookingData.governmentId.length} file
                      {bookingData.governmentId.length > 1 ? "s" : ""})
                    </span>
                  </div>
                )}
              {bookingData.proofOfBilling &&
                bookingData.proofOfBilling.length > 0 && (
                  <div className="flex items-center gap-2 min-w-0">
                    <CheckCircle className="h-3 w-3 xs:h-4 xs:w-4 text-green-600 flex-shrink-0" />
                    <span className="text-xs xs:text-sm text-gray-700 break-words leading-tight min-w-0">
                      Proof of Billing ({bookingData.proofOfBilling.length} file
                      {bookingData.proofOfBilling.length > 1 ? "s" : ""})
                    </span>
                  </div>
                )}
            </div>
            <div className="mt-2 xs:mt-3 pt-2 xs:pt-3 border-t border-green-200">
              <p className="text-xs text-green-700 leading-tight">
                All required documents have been uploaded and verified.
              </p>
            </div>
          </div>

          {/* Payment Information */}
          <div className="bg-blue-50 rounded-lg p-2 xs:p-3 sm:p-4 space-y-2 xs:space-y-3">
            <h4 className="font-semibold text-gray-900 flex items-center gap-2 text-xs xs:text-sm sm:text-base leading-tight">
              <CreditCard className="h-3 w-3 xs:h-4 xs:w-4 text-blue-600 flex-shrink-0" />
              <span className="truncate">Payment Information</span>
            </h4>
            <div className="space-y-2 xs:space-y-3">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 xs:gap-2 min-w-0">
                <span className="text-xs xs:text-sm text-gray-600 leading-tight">Payment Method:</span>
                <span className="font-medium text-xs xs:text-sm sm:text-base break-words min-w-0">
                  {bookingData.paymentMethod || "Not selected"}
                </span>
              </div>

              {bookingData.proofOfPayment &&
              bookingData.proofOfPayment.length > 0 ? (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 min-w-0">
                    <CheckCircle className="h-3 w-3 xs:h-4 xs:w-4 text-green-600 flex-shrink-0" />
                    <span className="text-xs xs:text-sm text-gray-700 break-words leading-tight min-w-0">
                      Proof of payment uploaded (
                      {bookingData.proofOfPayment.length} file
                      {bookingData.proofOfPayment.length > 1 ? "s" : ""})
                    </span>
                  </div>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-2 xs:p-3">
                    <p className="text-xs xs:text-sm text-green-800 break-words leading-tight">
                      <strong className="font-medium">Payment Status:</strong> Pending verification. Our
                      team will review your payment proof within 24 hours.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="bg-red-50 border border-red-200 rounded-lg p-2 xs:p-3">
                  <p className="text-xs xs:text-sm text-red-800 break-words leading-tight">
                    <strong className="font-medium">Notice:</strong> No payment method or proof of
                    payment provided.
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Pricing Breakdown */}
          <div className="bg-gray-50 rounded-lg p-2 xs:p-3 sm:p-4 space-y-2 xs:space-y-3">
            <h4 className="font-semibold text-gray-900 text-xs xs:text-sm sm:text-base leading-tight">
              Pricing Summary
            </h4>
            <div className="space-y-1 xs:space-y-2">
              <div className="flex justify-between items-center min-w-0">
                <span className="text-xs xs:text-sm text-gray-600 leading-tight truncate">Daily Rate</span>
                <span className="text-xs xs:text-sm font-medium leading-tight ml-2 flex-shrink-0">{costBreakdown.dailyRate}</span>
              </div>
              <div className="flex justify-between items-center min-w-0">
                <span className="text-xs xs:text-sm text-gray-600 leading-tight truncate">Rental Duration</span>
                <span className="text-xs xs:text-sm font-medium leading-tight ml-2 flex-shrink-0">{costBreakdown.daysLabel}</span>
              </div>
              <div className="flex justify-between items-center min-w-0">
                <span className="text-xs xs:text-sm text-gray-600 leading-tight truncate">Rental Subtotal</span>
                <span className="text-xs xs:text-sm font-medium leading-tight ml-2 flex-shrink-0">{costBreakdown.subtotal}</span>
              </div>
              {costBreakdown.hasDeliveryFees && (
                <>
                  <div className="flex justify-between items-center min-w-0">
                    <span className="text-xs xs:text-sm text-gray-600 leading-tight truncate">Pickup Delivery Fee</span>
                    <span className="text-xs xs:text-sm font-medium leading-tight ml-2 flex-shrink-0">{costBreakdown.pickupFee}</span>
                  </div>
                  <div className="flex justify-between items-center min-w-0">
                    <span className="text-xs xs:text-sm text-gray-600 leading-tight truncate">Return Delivery Fee</span>
                    <span className="text-xs xs:text-sm font-medium leading-tight ml-2 flex-shrink-0">{costBreakdown.returnFee}</span>
                  </div>
                  <div className="flex justify-between items-center min-w-0">
                    <span className="text-xs xs:text-sm text-gray-600 leading-tight truncate">Total Delivery Fees</span>
                    <span className="text-xs xs:text-sm text-orange-600 font-medium leading-tight ml-2 flex-shrink-0">{costBreakdown.totalDeliveryFees}</span>
                  </div>
                </>
              )}
              <div className="border-t border-gray-300 pt-1 xs:pt-2 mt-1 xs:mt-2">
                <div className="flex justify-between items-center font-semibold min-w-0">
                  <span className="text-sm xs:text-base sm:text-lg leading-tight truncate">Total Amount</span>
                  <span className="text-sm xs:text-base sm:text-lg text-blue-600 leading-tight ml-2 flex-shrink-0">
                    {costBreakdown.grandTotal}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Terms and Confirmation */}
      <Card className="overflow-hidden">
        <CardContent className="p-3 xs:p-4 sm:p-6 space-y-3 xs:space-y-4 sm:space-y-6">
          <div className="bg-yellow-50 rounded-lg p-2 xs:p-3 sm:p-4">
            <h4 className="font-medium text-yellow-900 mb-2 xs:mb-3 text-xs xs:text-sm sm:text-base leading-tight">
              Important Notes
            </h4>
            <ul className="text-xs xs:text-sm text-yellow-800 space-y-1 leading-tight">
              <li className="break-words">
                • Payment will be arranged through our customer service team via messenger
              </li>
              <li className="break-words">
                • Please bring a valid driver's license and government ID for pickup
              </li>
              <li className="break-words">• Car inspection will be done before and after rental</li>
              <li className="break-words">• Cancellation policy applies as per our terms of service</li>
            </ul>
          </div>

          {/* Required Agreement Checkboxes */}
          <div className="bg-white border border-gray-200 rounded-lg p-2 xs:p-3 sm:p-4 lg:p-6">
            <h4 className="font-semibold text-gray-900 mb-2 xs:mb-3 sm:mb-4 text-xs xs:text-sm sm:text-base leading-tight">
              Required Agreement
            </h4>
            <div className="space-y-2 xs:space-y-3 sm:space-y-4">
              <div className="flex items-start gap-2 xs:gap-3 min-w-0">
                <Checkbox
                  id="terms-checkbox"
                  checked={acceptedTerms}
                  onCheckedChange={(checked) =>
                    setAcceptedTerms(checked === true)
                  }
                  className="mt-0.5 flex-shrink-0"
                />
                <label
                  htmlFor="terms-checkbox"
                  className="text-xs xs:text-sm text-gray-700 cursor-pointer break-words leading-tight min-w-0"
                >
                  I have read and agree to the{" "}
                  <Link
                    href="/customer/terms"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 underline hover:text-blue-800 font-medium"
                  >
                    Terms & Conditions
                  </Link>
                  , including the GPS tracking policy and all rental requirements.
                </label>
              </div>

              <div className="flex items-start gap-2 xs:gap-3 min-w-0">
                <Checkbox
                  id="privacy-checkbox"
                  checked={acceptedPrivacy}
                  onCheckedChange={(checked) =>
                    setAcceptedPrivacy(checked === true)
                  }
                  className="mt-0.5 flex-shrink-0"
                />
                <label
                  htmlFor="privacy-checkbox"
                  className="text-xs xs:text-sm text-gray-700 cursor-pointer break-words leading-tight min-w-0"
                >
                  I acknowledge and consent to the{" "}
                  <Link
                    href="/customer/terms#privacy-policy"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 underline hover:text-blue-800 font-medium"
                  >
                    Privacy Policy
                  </Link>
                  , including data collection and processing practices.
                </label>
              </div>

              {(!acceptedTerms || !acceptedPrivacy) && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-2 xs:p-3">
                  <p className="text-xs xs:text-sm text-red-800 break-words leading-tight">
                    <strong className="font-medium">Required:</strong> You must accept both the Terms &
                    Conditions and Privacy Policy to complete your booking.
                  </p>
                </div>
              )}
            </div>
          </div>

          <div className="space-y-3 xs:space-y-4">
            <Button
              onClick={handleConfirmBooking}
              disabled={isSubmitting || !acceptedTerms || !acceptedPrivacy}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-sm xs:text-base sm:text-lg py-3 xs:py-4 sm:py-6 min-h-[44px] flex items-center justify-center"
              aria-busy={isSubmitting}
              data-testid="customer-loading-button"
            >
              {isSubmitting ? (
                <>
                  <Loader2
                    className="w-4 h-4 xs:w-5 xs:h-5 mr-2 animate-spin flex-shrink-0"
                    aria-hidden="true"
                  />
                  <span className="truncate">Preparing request...</span>
                  <span className="sr-only">
                    Please wait while we prepare your booking request.
                  </span>
                </>
              ) : (
                <span className="truncate">Send Booking Request</span>
              )}
            </Button>
          </div>

          <p className="text-xs text-gray-500 text-center break-words leading-tight">
            By clicking "Send Booking Request", you confirm that all information
            provided is accurate and complete.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
