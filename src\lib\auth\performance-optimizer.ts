/**
 * Performance optimized authentication initialization
 * Reduces loading times for both admin and customer authentication
 * Similar to optimizations from Appwrite project but adapted for Supabase
 */

// Performance monitoring
const performanceMetrics = {
  authInitTime: 0,
  profileFetchTime: 0,
  sessionValidationTime: 0
}

export function optimizeAuthPerformance() {
  if (typeof window === 'undefined') return;

  const startTime = performance.now()

  // Preload critical auth data for both contexts
  const criticalKeys = [
    'sb-customer-auth-token.access_token',
    'sb-customer-auth-token.refresh_token', 
    'sb-customer-auth-token.user',
    'sb-admin-auth-token.access_token',
    'sb-admin-auth-token.refresh_token',
    'sb-admin-auth-token.user'
  ];

  // Enhanced cache with TTL
  const authCache = new Map<string, { value: string | null, timestamp: number }>();
  const CACHE_TTL = 2 * 60 * 1000; // 2 minutes

  // Preload cache
  criticalKeys.forEach(key => {
    const value = localStorage.getItem(key);
    authCache.set(key, { value, timestamp: Date.now() });
  });

  // Optimized localStorage with cache and TTL
  const originalGetItem = localStorage.getItem.bind(localStorage);
  localStorage.getItem = function(key: string): string | null {
    const cached = authCache.get(key);
    if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
      return cached.value;
    }
    
    // Refresh cache if expired
    const value = originalGetItem(key);
    if (key.includes('sb-') && key.includes('auth')) {
      authCache.set(key, { value, timestamp: Date.now() });
    }
    return value;
  };

  // Update cache on modifications
  const originalSetItem = localStorage.setItem.bind(localStorage);
  localStorage.setItem = function(key: string, value: string) {
    if (key.includes('sb-') && key.includes('auth')) {
      authCache.set(key, { value, timestamp: Date.now() });
    }
    return originalSetItem(key, value);
  };

  const originalRemoveItem = localStorage.removeItem.bind(localStorage);
  localStorage.removeItem = function(key: string) {
    if (authCache.has(key)) {
      authCache.set(key, { value: null, timestamp: Date.now() });
    }
    return originalRemoveItem(key);
  };

  // Periodic cache cleanup
  setInterval(() => {
    const now = Date.now();
    for (const [key, cached] of authCache.entries()) {
      if (now - cached.timestamp > CACHE_TTL) {
        authCache.delete(key);
      }
    }
  }, CACHE_TTL);

  performanceMetrics.authInitTime = performance.now() - startTime;
  
  if (process.env.NODE_ENV === 'development') {
    console.log('🚀 Auth performance optimized:', performanceMetrics.authInitTime.toFixed(2) + 'ms');
  }
}

// Performance monitoring utilities
export function trackAuthPerformance(operation: string, startTime: number) {
  const duration = performance.now() - startTime;
  
  switch (operation) {
    case 'profileFetch':
      performanceMetrics.profileFetchTime = duration;
      break;
    case 'sessionValidation':
      performanceMetrics.sessionValidationTime = duration;
      break;
  }
  
  if (process.env.NODE_ENV === 'development' && duration > 100) {
    console.warn(`⚠️ Slow auth operation: ${operation} took ${duration.toFixed(2)}ms`);
  }
}

export function getPerformanceMetrics() {
  return { ...performanceMetrics };
}

// Debounce utility for reducing excessive re-renders
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Auto-run on import in browser environment
if (typeof window !== 'undefined') {
  optimizeAuthPerformance();
}
