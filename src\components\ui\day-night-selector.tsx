"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Moon, Clock } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  TIME_CATEGORIES,
  getTimePeriod,
  type TimePeriod,
} from "@/lib/delivery-fee-constants";

interface DayNightSelectorProps {
  /** Current selected time period */
  value: TimePeriod;
  /** Callback when selection changes */
  onChange: (timePeriod: TimePeriod) => void;
  /** Optional callback to auto-fill time when Day/Night is selected */
  onTimeAutoFill?: (time: string) => void;
  /** Current time value for manual override detection */
  currentTime?: string;
  /** Optional label for the selector */
  label?: string;
  /** Show time ranges */
  showTimeRanges?: boolean;
  /** Disabled state */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Size variant */
  size?: "sm" | "md" | "lg";
  /** Enable auto-fill functionality */
  enableAutoFill?: boolean;
}

export function DayNightSelector({
  value,
  onChange,
  onTimeAutoFill,
  currentTime,
  label = "Delivery Time",
  showTimeRanges = true,
  disabled = false,
  className,
  size = "md",
  enableAutoFill = false,
}: DayNightSelectorProps) {
  // Default times for auto-fill
  const DEFAULT_DAY_TIME = "10:00";
  const DEFAULT_NIGHT_TIME = "18:00";
  
  // Check if current time matches auto-fill defaults (indicates auto-selection)
  const isAutoSelected = React.useMemo(() => {
    if (!enableAutoFill || !currentTime) return false;
    return currentTime === DEFAULT_DAY_TIME || currentTime === DEFAULT_NIGHT_TIME;
  }, [currentTime, enableAutoFill]);
  
  const handleDayNightClick = (timePeriod: TimePeriod) => {
    if (disabled) return;
    
    // Update the time period
    onChange(timePeriod);
    
    // Auto-fill time if enabled
    if (enableAutoFill && onTimeAutoFill) {
      const defaultTime = timePeriod === "day" ? DEFAULT_DAY_TIME : DEFAULT_NIGHT_TIME;
      onTimeAutoFill(defaultTime);
    }
  };
  const sizeClasses = {
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg",
  };

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-5 w-5", 
    lg: "h-6 w-6",
  };

  const paddingClasses = {
    sm: "p-2",
    md: "p-3",
    lg: "p-4",
  };

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label className={cn("font-medium text-gray-700", sizeClasses[size])}>
          <Clock className={cn("inline mr-2", iconSizes[size])} />
          {label}
        </Label>
      )}

      <div className="grid grid-cols-2 gap-3">
        {/* Day Time Option */}
        <Button
          type="button"
          variant={value === "day" ? "primary" : "secondary"}
          onClick={() => handleDayNightClick("day")}
          disabled={disabled}
          className={cn(
            "flex flex-col items-center justify-center h-auto",
            paddingClasses[size],
            value === "day" 
              ? "bg-orange-500 hover:bg-orange-600 text-white" 
              : "bg-white hover:bg-orange-50 text-gray-700 border-2 border-orange-200"
          )}
        >
          <Sun className={cn("mb-2", iconSizes[size])} />
          <span className={cn("font-semibold", sizeClasses[size])}>
            Day Time
          </span>
          {showTimeRanges && (
            <span className="text-xs opacity-90 mt-1 text-center">
              {TIME_CATEGORIES.DAY.range}
            </span>
          )}
        </Button>

        {/* Night Time Option */}
        <Button
          type="button"
          variant={value === "night" ? "primary" : "secondary"}
          onClick={() => handleDayNightClick("night")}
          disabled={disabled}
          className={cn(
            "flex flex-col items-center justify-center h-auto",
            paddingClasses[size],
            value === "night" 
              ? "bg-indigo-600 hover:bg-indigo-700 text-white" 
              : "bg-white hover:bg-indigo-50 text-gray-700 border-2 border-indigo-200"
          )}
        >
          <Moon className={cn("mb-2", iconSizes[size])} />
          <span className={cn("font-semibold", sizeClasses[size])}>
            Night Time
          </span>
          {showTimeRanges && (
            <span className="text-xs opacity-90 mt-1 text-center">
              {TIME_CATEGORIES.NIGHT.range}
            </span>
          )}
        </Button>
      </div>

      {/* Current Selection Badge */}
      <div className="flex justify-center">
        <Badge 
          variant="secondary" 
          className={cn(
            "flex items-center gap-1",
            value === "day" ? "bg-orange-100 text-orange-700" : "bg-indigo-100 text-indigo-700"
          )}
        >
          {value === "day" ? (
            <Sun className="h-3 w-3" />
          ) : (
            <Moon className="h-3 w-3" />
          )}
          Selected: {value === "day" ? "Day Time" : "Night Time"}
          {enableAutoFill && isAutoSelected && (
            <span className="text-xs ml-1">(Auto-filled)</span>
          )}
        </Badge>
      </div>
      
      {/* Auto-fill Instructions */}
      {enableAutoFill && (
        <div className="text-xs text-gray-500 text-center space-y-1">
          <p>💡 <strong>Auto-fill:</strong> Day = 10:00 AM, Night = 6:00 PM</p>
          <p>You can manually adjust the time after selection</p>
        </div>
      )}
    </div>
  );
}

/**
 * Compact inline Day/Night selector for smaller spaces
 */
interface CompactDayNightSelectorProps {
  /** Current selected time period */
  value: TimePeriod;
  /** Callback when selection changes */
  onChange: (timePeriod: TimePeriod) => void;
  /** Optional callback to auto-fill time when Day/Night is selected */
  onTimeAutoFill?: (time: string) => void;
  /** Current time value for manual override detection */
  currentTime?: string;
  /** Disabled state */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Enable auto-fill functionality */
  enableAutoFill?: boolean;
}

export function CompactDayNightSelector({
  value,
  onChange,
  onTimeAutoFill,
  currentTime,
  disabled = false,
  className,
  enableAutoFill = false,
}: CompactDayNightSelectorProps) {
  // Default times for auto-fill
  const DEFAULT_DAY_TIME = "10:00";
  const DEFAULT_NIGHT_TIME = "18:00";
  
  // Check if current time matches auto-fill defaults (indicates auto-selection)
  const isAutoSelected = React.useMemo(() => {
    if (!enableAutoFill || !currentTime) return false;
    return currentTime === DEFAULT_DAY_TIME || currentTime === DEFAULT_NIGHT_TIME;
  }, [currentTime, enableAutoFill]);
  
  const handleDayNightClick = (timePeriod: TimePeriod) => {
    if (disabled) return;
    
    // Update the time period
    onChange(timePeriod);
    
    // Auto-fill time if enabled
    if (enableAutoFill && onTimeAutoFill) {
      const defaultTime = timePeriod === "day" ? DEFAULT_DAY_TIME : DEFAULT_NIGHT_TIME;
      onTimeAutoFill(defaultTime);
    }
  };
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Label className="text-sm font-medium text-gray-700 flex items-center gap-1">
        <Clock className="h-3 w-3" />
        Time:
      </Label>
      
      <div className="flex rounded-lg border border-gray-200 overflow-hidden">
        <Button
          type="button"
          size="sm"
          variant={value === "day" ? "primary" : "secondary"}
          onClick={() => handleDayNightClick("day")}
          disabled={disabled}
          className={cn(
            "rounded-none px-3 py-1 text-xs",
            value === "day"
              ? "bg-orange-500 hover:bg-orange-600 text-white"
              : "bg-white hover:bg-orange-50 text-gray-600"
          )}
        >
          <Sun className="h-3 w-3 mr-1" />
          Day
        </Button>
        
        <Button
          type="button"
          size="sm"
          variant={value === "night" ? "primary" : "secondary"}
          onClick={() => handleDayNightClick("night")}
          disabled={disabled}
          className={cn(
            "rounded-none px-3 py-1 text-xs",
            value === "night"
              ? "bg-indigo-600 hover:bg-indigo-700 text-white"
              : "bg-white hover:bg-indigo-50 text-gray-600"
          )}
        >
          <Moon className="h-3 w-3 mr-1" />
          Night
        </Button>
      </div>
      
      {/* Auto-fill indicator for compact version */}
      {enableAutoFill && isAutoSelected && (
        <Badge variant="outline" className="text-xs px-2 py-0.5">
          Auto-filled
        </Badge>
      )}
    </div>
  );
}

/**
 * Helper function to auto-detect time period from time string and suggest selection
 */
export function getTimePeriodFromTime(timeString: string): TimePeriod {
  return getTimePeriod(timeString);
}

/**
 * Card wrapper for Day/Night selector with additional information
 */
interface DayNightSelectorCardProps extends DayNightSelectorProps {
  /** Show fee information */
  showFeeInfo?: boolean;
  /** Sample location for fee display */
  sampleLocation?: string;
  /** Sample fees for day/night */
  sampleFees?: {
    day: number;
    night: number;
  };
}

export function DayNightSelectorCard({
  showFeeInfo = false,
  sampleLocation,
  sampleFees,
  ...props
}: DayNightSelectorCardProps) {
  return (
    <Card>
      <CardContent className="p-4 space-y-4">
        <DayNightSelector {...props} />
        
        {showFeeInfo && sampleLocation && sampleFees && (
          <div className="border-t pt-4">
            <div className="text-sm text-gray-600 mb-2">
              <strong>Fee Example ({sampleLocation}):</strong>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex justify-between">
                <span>Day Time:</span>
                <span className="font-medium">₱{sampleFees.day.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Night Time:</span>
                <span className="font-medium">₱{sampleFees.night.toFixed(2)}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
