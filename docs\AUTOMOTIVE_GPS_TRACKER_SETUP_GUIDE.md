# ESP32-S3-A7670E Automotive GPS Tracker Setup Guide

**Real-Time Car Tracking System with 4G Connectivity**

---

## 📋 Table of Contents

1. [Hardware Requirements](#hardware-requirements)
2. [Initial Hardware Setup](#initial-hardware-setup)
3. [Software Installation](#software-installation)
4. [Configuration](#configuration)
5. [Testing Phases](#testing-phases)
6. [JSON Data Structure](#json-data-structure)
7. [Deployment to Vehicle](#deployment-to-vehicle)
8. [Remote Monitoring](#remote-monitoring)
9. [Troubleshooting](#troubleshooting)
10. [Advanced Features](#advanced-features)

---

## 🛠️ Hardware Requirements

### **Core Components**
- ESP32-S3-A7670E-4G Development Board
- GNSS antenna (included)
- 4G/LTE antenna (included)
- SIM card (nano-SIM with 4G data plan)
- USB-C cable for programming
- 18650 battery (for testing) or 12V car power adapter

### **Power Requirements**
- **Development**: USB power (5V/1A)
- **Testing**: 18650 battery (3.7V)
- **Production**: Car 12V adapter with 5V/3A regulator

### **Optional Components**
- External high-gain GNSS antenna
- Magnetic roof mount for antenna
- Waterproof enclosure for vehicle installation

---

## 🔧 Initial Hardware Setup

### **Step 1: DIP Switch Configuration**

**Critical**: Configure DIP switches on the **back** of the board:

```
Switch Position:  [1] [2] [3] [4]
Required Setting: ON  OFF ??  ??
Function:         4G  USB CAM POW
```

**Settings Explained:**
- **Switch 1 (4G)**: **ON** - Enables A7670E module power
- **Switch 2 (USB)**: **OFF** - UART communication mode
- **Switch 3 (CAM)**: OFF for GPS tracking
- **Switch 4 (POW)**: ON for power management

### **Step 2: Antenna Connections**

**GNSS Antenna**: Connect to **IPEX1** connector (smaller connector)
**4G Antenna**: Connect to **IPEX2** connector (larger connector)

**Important**: Ensure antennas are properly seated and not loose.

### **Step 3: SIM Card Installation**

1. Insert nano-SIM card with active 4G data plan
2. Ensure SIM is properly seated in holder
3. Verify SIM PIN is disabled (no PIN required)

### **Step 4: Power Connection**

**For Initial Testing**:
- Connect USB-C cable to laptop
- Install 18650 battery for standalone testing

---

## 💻 Software Installation

### **Step 1: Arduino IDE Setup**

1. Install Arduino IDE 2.x
2. Add ESP32 board support:
   - Go to **File → Preferences**
   - Add URL: `https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json`
   - Go to **Tools → Board → Boards Manager**
   - Search "ESP32" and install latest version

### **Step 2: Required Libraries**

Install these libraries via **Library Manager**:

```
- TinyGPS++ (by Mikal Hart)
- ArduinoJson (by Benoit Blanchon)
- PubSubClient (by Nick O'Leary)
- WiFi (ESP32 built-in)
- Wire (ESP32 built-in)
- EEPROM (ESP32 built-in)
```

### **Step 3: Board Configuration**

**Board Settings**:
- **Board**: "ESP32S3 Dev Module"
- **USB CDC On Boot**: "Enabled"
- **USB DFU On Boot**: "Disabled"
- **Flash Size**: "16MB (128Mb)"
- **Partition Scheme**: "Default 4MB with spiffs"
- **PSRAM**: "QSPI PSRAM"

---

## ⚙️ Configuration

### **Step 1: Update Credentials**

Edit `automotive_gps_tracker.ino`:

```cpp
// Update these values for your setup
const char *clientID = "your_client_id";        // Your unique tracker ID
char sub[] = "Sub/1149/37/your_client_id";      // Subscription topic
char pub[] = "Pub/1149/37/your_client_id";      // Publishing topic
```

### **Step 2: Optional Configuration**

```cpp
// Customize tracking behavior
#define GPS_UPDATE_INTERVAL 10000      // 10 seconds when moving
#define STATIONARY_INTERVAL 60000      // 1 minute when parked
#define MAX_SPEED_THRESHOLD 120.0      // Speed alert limit (km/h)
#define GEOFENCE_RADIUS 100.0          // Geofence radius (meters)
```

### **Step 3: Compile and Upload**

1. Connect ESP32 via USB-C
2. Select correct COM port
3. Click **Upload**
4. Wait for "Done uploading" message

---

## 🧪 Testing Phases

### **Phase 1: Desktop Testing (Laptop Connected)**

**Objective**: Verify hardware communication and software functionality

**Steps**:
1. Connect USB cable to laptop
2. Open Serial Monitor (115200 baud)
3. Power on device
4. Wait for initialization messages

**Expected Output**:
```
=== AUTOMOTIVE GPS TRACKER v2.0 ===
🚗 Optimized for Real-Time Car Tracking
🔋 Initializing A7670E Modem...
📡 Testing A7670E Communication...
✅ A7670E Communication Established!
🛰️ Enabling GNSS...
✅ GNSS Enabled for Automotive Tracking
```

**Success Indicators**:
- ✅ Modem communication established
- ✅ GNSS enabled successfully
- ✅ MQTT connection attempts (may fail indoors)

### **Phase 2: Indoor GPS Testing**

**Objective**: Test GPS acquisition near window

**Steps**:
1. Place device near south-facing window
2. Ensure GNSS antenna has clear view
3. Wait 5-10 minutes for GPS fix
4. Monitor Serial output for coordinates

**Expected Behavior**:
- GPS fix may take 5-15 minutes indoors
- Coordinates should appear when fix acquired
- HDOP should be reasonable (<10)

### **Phase 3: Outdoor Testing (Standalone)**

**Objective**: Verify full functionality without laptop

**Steps**:
1. **Disconnect USB cable**
2. **Power on battery only**
3. **Move to outdoor location**
4. **Wait 2-3 minutes for GPS fix**
5. **Check Waveshare Cloud for tracker icon**

**Success Criteria**:
- Device operates independently
- GPS fix acquired within 3 minutes
- Data appears on cloud map
- Red LED flashes (network connected)

### **Phase 4: Vehicle Testing**

**Objective**: Test real-world automotive tracking

**Steps**:
1. **Install in vehicle** (temporary mount)
2. **Connect to car power** (12V adapter)
3. **Mount GNSS antenna** on dashboard/roof
4. **Start engine** (ignition detection)
5. **Drive around block**
6. **Monitor cloud map** for tracking

**Expected Results**:
- Immediate GPS fix (clear sky view)
- Real-time tracking on map
- Trip data logging
- Speed monitoring

---

## 📊 JSON Data Structure

### **Real-Time Tracking Data**

The device sends this JSON structure every 10 seconds (moving) or 1 minute (parked):

```json
{
  "data": {
    "Latitude": 14.123456,
    "Longitude": 121.654321,
    "speed": 65.4,
    "course": 245.7,
    "altitude": 125.8,
    "satellites": 8,
    "hdop": 1.2,
    "ignition": true,
    "moving": true,
    "batteryLevel": 12.8,
    "timestamp": 1234567890,
    "maxSpeed": 87.3,
    "tripDistance": 15.8,
    "vehicleId": "b4922afa",
    "trackingMode": "ACTIVE"
  }
}
```

### **Speed Alert Data**

When vehicle exceeds speed limit:

```json
{
  "alert": {
    "type": "SPEED",
    "speed": 135.4,
    "limit": 120.0,
    "location": {
      "lat": 14.123456,
      "lng": 121.654321
    }
  }
}
```

### **Data Flow**

```
ESP32 → MQTT → Waveshare Cloud → Map Visualization
      ↓
   Serial Monitor (Development)
      ↓
   Custom Fleet App (Optional)
```

---

## 🚗 Deployment to Vehicle

### **Step 1: Permanent Installation**

**Power Connection**:
- Use 12V to 5V/3A converter
- Connect to ignition-switched power
- Add fuse protection (2A)

**Mounting**:
- Secure enclosure under dashboard
- Ensure ventilation for heat dissipation
- Protect from moisture and vibration

**Antenna Placement**:
- **GNSS antenna**: Roof mount for best reception
- **4G antenna**: Side window or roof mount
- **Cable routing**: Avoid pinch points

### **Step 2: Ignition Detection**

Connect GPIO35 to ignition power for automatic trip detection:

```cpp
// Hardware connection
// GPIO35 ← Ignition/Accessory Power (12V via voltage divider)
```

### **Step 3: Final Testing**

**Checklist**:
- [ ] Device powers on with ignition
- [ ] GPS fix acquired within 2 minutes
- [ ] 4G connection established (red LED flashing)
- [ ] Data appears on Waveshare Cloud
- [ ] Trip tracking starts/stops with ignition
- [ ] Speed alerts functional

---

## 🌐 Remote Monitoring

### **Waveshare Cloud Access**

**URL**: `https://mqtt.waveshare.cloud`

**Login Credentials**:
- **Client ID**: Your configured client ID
- **Username**: (if required)
- **Password**: (if required)

### **Real-Time Features**

**Live Tracking**:
- Vehicle location updates every 10 seconds
- Historical route playback
- Current speed and direction
- Trip statistics

**Remote Commands**:
Send MQTT message to request immediate location:
```json
{"cmd": "locate"}
```

**Fleet Management**:
- Multiple vehicle support
- Geofencing alerts
- Speed limit monitoring
- Trip reports

---

## 🔧 Troubleshooting

### **Common Issues**

#### **No AT Response**
**Symptoms**: "AT Timeout" messages in Serial Monitor
**Solutions**:
1. Check DIP Switch 1 (4G) is ON
2. Verify SIM card installation
3. Try power cycle (disconnect/reconnect power)
4. Check antenna connections

#### **No GPS Fix**
**Symptoms**: No coordinates in Serial output
**Solutions**:
1. Move to outdoor location with clear sky view
2. Check GNSS antenna connection (IPEX1)
3. Wait longer (up to 15 minutes for cold start)
4. Verify antenna is not damaged

#### **No Cloud Data**
**Symptoms**: GPS works but no data on Waveshare Cloud
**Solutions**:
1. Check 4G antenna connection (IPEX2)
2. Verify SIM card has active data plan
3. Confirm MQTT credentials are correct
4. Check red LED status (should flash when connected)

#### **Poor Battery Life**
**Symptoms**: Device shuts down quickly on battery
**Solutions**:
1. Use higher capacity 18650 battery
2. Enable power-saving mode for parked vehicles
3. Connect to car's electrical system
4. Check for excessive current draw

### **LED Status Indicators**

| LED Color | Status | Meaning |
|-----------|--------|---------|
| Blue (Solid) | Power | Device powered on |
| Red (Solid) | Network | Searching for cellular network |
| Red (Flashing 200ms) | Connected | Connected to cellular network |
| Yellow | Warning | Battery reversed (if applicable) |
| Green | Charging | Solar panel charging active |

### **Serial Monitor Debug Commands**

Add these to monitor device status:

```cpp
// Check network status
SentMessage("AT+CREG?", 3000);    // Registration status
SentMessage("AT+CSQ", 3000);      // Signal quality

// Check GPS status  
SentMessage("AT+CGNSSINFO", 3000); // Satellite info
```

---

## 🚀 Advanced Features

### **Geofencing**

Implement zone monitoring:

```cpp
bool isInGeofence(float lat, float lng, float centerLat, float centerLng, float radius) {
  float distance = TinyGPSPlus::distanceBetween(lat, lng, centerLat, centerLng);
  return distance <= radius;
}
```

### **Fleet Management**

Support multiple vehicles:

```cpp
// Different client IDs for each vehicle
const char *vehicles[] = {"vehicle_001", "vehicle_002", "vehicle_003"};
```

### **Over-the-Air Updates**

Enable remote firmware updates:

```cpp
#include <HTTPUpdate.h>
// Implement OTA update capability
```

### **Emergency Features**

Add panic button and emergency alerts:

```cpp
// Immediate emergency alert
void sendEmergencyAlert() {
  sendJson["emergency"]["type"] = "PANIC";
  sendJson["emergency"]["location"]["lat"] = gps.location.lat();
  sendJson["emergency"]["location"]["lng"] = gps.location.lng();
  client.publish("emergency/1149/37/b4922afa", alertJson.c_str());
}
```

---

## 📈 Performance Expectations

### **GPS Accuracy**
- **Outdoor**: 2-5 meter accuracy
- **Moving vehicle**: 3-8 meter accuracy  
- **Urban areas**: 5-15 meter accuracy
- **Indoor**: Limited or no fix

### **Update Intervals**
- **Real-time tracking**: 10-second updates
- **Parked monitoring**: 1-minute updates
- **Emergency mode**: 5-second updates

### **Battery Life** (18650 Li-ion)
- **Continuous tracking**: 8-12 hours
- **Parked monitoring**: 24-48 hours
- **Deep sleep mode**: 7-14 days

### **Data Usage** (Monthly)
- **Active tracking**: ~50-100 MB
- **Parked monitoring**: ~10-20 MB
- **Combined usage**: ~100-150 MB

---

## 🎯 Success Criteria

**Development Complete When**:
- [ ] Device communicates with A7670E module
- [ ] GPS coordinates acquired outdoors
- [ ] MQTT data published to cloud
- [ ] Real-time tracking visible on map
- [ ] Standalone operation (no laptop needed)
- [ ] Vehicle installation functional
- [ ] Trip tracking and alerts working

**Production Ready When**:
- [ ] Reliable GPS fix within 2 minutes
- [ ] Continuous 4G connectivity
- [ ] Battery life meets requirements
- [ ] Automotive installation complete
- [ ] Remote monitoring operational
- [ ] Emergency features tested

---

## 📞 Support Resources

**Technical Documentation**:
- [Waveshare ESP32-S3-A7670E Wiki](https://www.waveshare.com/wiki/ESP32-S3-A7670E-4G)
- [A7670E AT Command Manual](https://www.waveshare.com/w/upload/2/29/A7670X_series_AT_Command_Manual_V1.02.pdf)
- [TinyGPS++ Library](https://github.com/mikalhart/TinyGPSPlus)

**Community Support**:
- ESP32 Arduino Forums
- Waveshare Technical Support
- Arduino Community Forums

**Emergency Contacts**:
- Hardware Issues: Waveshare Support
- Cellular Issues: SIM Card Provider
- Software Issues: Arduino/ESP32 Community

---

*Last Updated: 2025-08-23*
*Version: 2.0*
*Author: ESP32-S3-A7670E Automotive GPS Tracker Project*
