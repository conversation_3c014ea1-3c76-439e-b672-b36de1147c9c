// Document request email template
export function buildDocumentRequestEmail(params: {
  customerName?: string | null;
  documentTypes: string[];
  requestType: 'missing' | 'revision';
  adminNotes?: string;
  adminName?: string;
  dashboardUrl?: string;
}): { subject: string; html: string } {
  const { 
    customerName, 
    documentTypes, 
    requestType, 
    adminNotes, 
    adminName,
    dashboardUrl = "https://olliesrentalcar.pathlinkio.app/customer/settings" 
  } = params;
  
  const subject = `Action Required: ${requestType === 'missing' ? 'Upload' : 'Update'} Your Legal Documents - Ollie's Rent A Car`;
  const greeting = customerName ? `Dear ${customerName},` : "Hello,";
  
  // Format document types for display
  const documentDisplayNames: { [key: string]: string } = {
    'drivers_license': "Driver's License",
    'government_id': "Government ID",
    'proof_of_billing': "Proof of Billing"
  };
  
  const actionText = requestType === 'missing' 
    ? 'upload the following required documents'
    : 'revise and re-upload the following documents';
    
  const reasonText = requestType === 'missing'
    ? 'We need these documents to process your booking'
    : 'The documents you previously uploaded require revision';
    
  const adminNotesBlock = adminNotes
    ? `
        <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 16px; margin: 16px 0;">
          <p style="margin: 0; font-weight: bold; color: #92400e;">Notes from ${adminName || 'our team'}:</p>
          <p style="margin: 8px 0 0 0; color: #92400e;">${escapeHtml(adminNotes)}</p>
        </div>
      `
    : '';
  
  const html = `
    <div style="font-family: Arial, sans-serif; line-height:1.6; max-width: 600px; margin: 0 auto; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
      <!-- Header with Logo -->
      <div style="text-align: center; padding: 20px; background-color: #f8fafc; border-bottom: 2px solid #e5e7eb;">
        <img src="https://pathlinkio.app/ollie_logo.jpg" alt="Ollie's Rent A Car" style="height: 60px; width: auto; margin-bottom: 10px;">
        <h1 style="color: #1f2937; margin: 0; font-size: 24px;">Ollie's Rent A Car</h1>
      </div>
      
      <!-- Document Request Header -->
      <div style="background-color: ${requestType === 'missing' ? '#dbeafe' : '#fef3c7'}; padding: 16px; border-bottom: 1px solid #e5e7eb;">
        <h2 style="margin: 0; color: ${requestType === 'missing' ? '#1e3a8a' : '#92400e'}; font-size: 18px;">
          ${requestType === 'missing' ? 'Documents Required' : 'Document Revision Needed'}
        </h2>
        <div style="display: inline-block; background-color: ${requestType === 'missing' ? '#fbbf24' : '#f59e0b'}; color: #ffffff; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin-top: 8px;">
          Action Required
        </div>
      </div>
      
      <!-- Main Message -->
      <div style="padding: 20px; border-bottom: 1px solid #e5e7eb;">
        <p>${greeting}</p>
        <p>${reasonText}. Please ${actionText}:</p>
        
        <div style="background-color: #f9fafb; border: 1px solid #e5e7eb; border-radius: 6px; padding: 16px; margin: 16px 0;">
          <h3 style="margin: 0 0 12px 0; color: #374151; font-size: 16px;">Required Documents:</h3>
          <ul style="margin: 0; padding-left: 20px; color: #4b5563;">
            ${documentTypes.map(type => `<li>${documentDisplayNames[type] || type}</li>`).join('')}
          </ul>
        </div>
        
        ${adminNotesBlock}
        
        <p><strong>Important:</strong> Your booking cannot be processed until all required documents are uploaded and verified.</p>
      </div>
      
      <!-- Instructions -->
      <div style="padding: 20px; background-color: #f0f9ff; border-bottom: 1px solid #e5e7eb;">
        <h3 style="margin: 0 0 12px 0; color: #1e3a8a; font-size: 16px;">How to Upload Documents:</h3>
        <ol style="margin: 0; padding-left: 20px; color: #4b5563;">
          <li>Click the "Update Documents" button below</li>
          <li>Sign in to your account if prompted</li>
          <li>Navigate to the Legal Documents section</li>
          <li>Upload the required documents</li>
          <li>Click "Save All Documents" to confirm</li>
        </ol>
        
        <div style="margin-top: 20px; text-align: center;">
          <a href="${dashboardUrl}" style="background:#1d4ed8;color:#fff;padding:12px 24px;border-radius:6px;text-decoration:none;display:inline-block;font-weight:bold;">
            Update Documents
          </a>
        </div>
      </div>
      
      <!-- Timeline -->
      <div style="padding: 20px; background-color: #fffbeb; border-bottom: 1px solid #e5e7eb;">
        <h3 style="margin: 0 0 12px 0; color: #92400e; font-size: 16px;">⏰ Timeline</h3>
        <p style="margin: 0; color: #92400e; font-size: 14px;">
          Please upload your documents within <strong>7 days</strong> to avoid booking cancellation.
          Our team will review and verify your documents within 24 hours of submission.
        </p>
      </div>
      
      <!-- Footer -->
      <div style="padding: 20px; background-color: #f3f4f6; text-align: center; font-size: 12px; color: #6b7280;">
        <p>If you have any questions or need assistance, please contact our support team.</p>
        <p style="margin-top: 12px;">Thank you for choosing Ollie's Rent A Car!</p>
        
        <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #e5e7eb;">
          <p style="margin: 0; font-size: 11px; color: #9ca3af;">
            This is an automated message. Please do not reply directly to this email.
          </p>
        </div>
      </div>
    </div>
  `;
  
  return { subject, html };
}

function escapeHtml(input: string) {
  return input
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/\"/g, "&quot;")
    .replace(/'/g, "&#039;");
}
