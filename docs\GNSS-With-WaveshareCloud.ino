#include <TinyGPS++.h>
#include <HardwareSerial.h>

#include <ArduinoJson.h>
#include <Arduino.h>
#include <PubSubClient.h>
#include <WiFi.h>
#include <Wire.h>

#define MSG_BUFFER_SIZE (50)
// ⚠️ UPDATE THESE CREDENTIALS BEFORE UPLOAD ⚠️
#define STASSID "YourWiFiNetwork"     // Replace with your WiFi SSID
#define STAPSK "YourWiFiPassword"     // Replace with your WiFi password

#define MAX17048_I2C_ADDRESS 0x36
const char *clientID = "ed5f06f8"; // Client ID
char sub[] = "Sub/1/37/ed5f06f8"; // Sub Topic
char pub[] = "Pub/1/37/ed5f06f8"; // Pub Topic
const char *mqtt_server = "mqtt.waveshare.cloud";

StaticJsonDocument<400> sendJson;
StaticJsonDocument<400> readJson;
unsigned long lastUpdateTime = 0;
const char *ssid = STASSID;
const char *password = STAPSK;
char msg[MSG_BUFFER_SIZE];
WiFiClient espClient;
PubSubClient client(espClient);

const unsigned long updateInterval = 5000;


// ✅ CORRECTED: ESP32-S3-A7670E-4G UART Pin Configuration
// Based on Waveshare wiki: ESP32-S3 UART2 standard pins
static const int RXPin = 18;  // ESP32-S3 RX <- A7670E TX
static const int TXPin = 17;  // ESP32-S3 TX -> A7670E RX
static const uint32_t GPSBaud = 9600;

// A7670E Control Pins
#define MODEM_PWRKEY_PIN 4    // Power control pin
#define MODEM_POWER_ON_PIN 33 // Module power enable

TinyGPSPlus gps;
HardwareSerial SerialAT(2); // Use Hardware Serial for ESP32-S3
String rev;
bool modemReady = false;
bool gnssEnabled = false;

void SentSerial(const char *p_char) {
  Serial.printf("Sending AT: %s\n", p_char);
  for (int i = 0; i < strlen(p_char); i++) {
    SerialAT.write(p_char[i]);
    delay(5);
  }

  SerialAT.write('\r');
  delay(5);
  SerialAT.write('\n');
  delay(100); // Increased delay for A7670E response time
}

bool SentMessage(const char *p_char, unsigned long timeout = 5000) {
  SentSerial(p_char);
  unsigned long start = millis();
  String response = "";
  
  while (millis() - start < timeout) {
    if (SerialAT.available()) {
      char c = SerialAT.read();
      response += c;
      
      // Check for complete response
      if (response.indexOf("OK") >= 0) {
        Serial.printf("✓ AT Response: %s\n", response.c_str());
        return true;
      }
      if (response.indexOf("ERROR") >= 0) {
        Serial.printf("✗ AT Error: %s\n", response.c_str());
        return false;
      }
      if (response.indexOf("+CGNSSPWR: READY!") >= 0) {
        Serial.println("✓ GNSS Power Ready!");
        return true;
      }
    }
    yield(); // Feed watchdog
  }
  Serial.printf("✗ AT Timeout for: %s\n", p_char);
  return false;
}

void setup() {
  Serial.begin(115200); // Increased baud for debugging
  
  Serial.println("\n==================================================");
  Serial.println("🛰️ Waveshare ESP32-S3-A7670E-4G GPS Tracker");
  Serial.println("==================================================");
  Serial.println("🔧 CRITICAL HARDWARE CHECKS:");
  Serial.println("1. DIP Switches: 4G=ON, USB=OFF");
  Serial.println("2. GNSS Antenna connected to IPEX1");
  Serial.println("3. SIM card inserted and activated");
  Serial.println("4. Outdoor location for GPS signal");
  Serial.println("==================================================\n");
  
  Wire.begin(3, 2);
  
  // Enhanced A7670E power control sequence
  pinMode(MODEM_POWER_ON_PIN, OUTPUT);
  pinMode(MODEM_PWRKEY_PIN, OUTPUT);
  
  Serial.println("=== Enhanced A7670E Power Sequence ===");
  
  // Ensure module is off first
  digitalWrite(MODEM_POWER_ON_PIN, LOW);
  digitalWrite(MODEM_PWRKEY_PIN, HIGH);
  delay(2000);
  
  // Power on sequence
  Serial.println("Step 1: Enabling module power...");
  digitalWrite(MODEM_POWER_ON_PIN, HIGH);
  delay(2000);
  
  // Enhanced PWRKEY sequence (longer pulse)
  Serial.println("Step 2: PWRKEY pulse (3s)...");
  digitalWrite(MODEM_PWRKEY_PIN, LOW);
  delay(3000);  // Longer pulse for reliable startup
  digitalWrite(MODEM_PWRKEY_PIN, HIGH);
  
  Serial.println("Step 3: Waiting for module boot (10s)...");
  delay(10000); // Extended boot time
  
  // Initialize UART for A7670E communication
  Serial.println("=== Testing Multiple Communication Modes ===");
  
  // Try multiple baud rates and pin configurations
  bool commEstablished = false;
  
  // Configuration 1: Standard pins with multiple baud rates (9600 first)
  int baudRates[] = {9600, 115200, 57600, 38400};
  int pinConfigs[][2] = {{18, 17}, {17, 18}, {16, 17}, {20, 21}};
  
  for (int p = 0; p < 4 && !commEstablished; p++) {
    for (int b = 0; b < 4 && !commEstablished; b++) {
      Serial.printf("Trying RX=%d, TX=%d at %d baud...\n", 
                   pinConfigs[p][0], pinConfigs[p][1], baudRates[b]);
      
      SerialAT.end();
      delay(100);
      SerialAT.begin(baudRates[b], SERIAL_8N1, pinConfigs[p][0], pinConfigs[p][1]);
      delay(1000);
      
      // Quick AT test
      SerialAT.println("AT");
      delay(500);
      if (SerialAT.available()) {
        String response = SerialAT.readString();
        if (response.indexOf("OK") >= 0) {
          Serial.printf("✓ Communication established!\n");
          commEstablished = true;
          break;
        }
      }
    }
  }
  
  if (!commEstablished) {
    Serial.println("⚠️ Standard UART failed, trying USB mode detection...");
    // Set back to default for continued attempts
    SerialAT.begin(9600, SERIAL_8N1, 18, 17);
  }
  
  delay(1000);

  // Enhanced AT communication testing
  Serial.println("=== Enhanced AT Communication Test ===");
  bool res = false;
  int attempts = 0;
  
  while (!res && attempts < 15) {
    attempts++;
    Serial.printf("AT attempt %d/15...\n", attempts);
    
    // Try different AT command variations
    const char* atCommands[] = {"AT", "AT\r\n", "AT\r", "AT\n", "+++"};
    
    for (int i = 0; i < 5 && !res; i++) {
      // Clear buffer first
      while (SerialAT.available()) SerialAT.read();
      
      Serial.printf("  Testing: %s\n", atCommands[i]);
      SerialAT.print(atCommands[i]);
      
      // Wait for response with shorter intervals
      unsigned long start = millis();
      String response = "";
      while (millis() - start < 2000) {
        if (SerialAT.available()) {
          char c = SerialAT.read();
          response += c;
          Serial.write(c); // Echo response
        }
        if (response.indexOf("OK") >= 0 || response.indexOf("AT") >= 0) {
          Serial.println("\n✓ Module responding!");
          res = true;
          break;
        }
        yield();
      }
      
      if (res) break;
      delay(500);
    }
    
    if (!res) {
      Serial.printf("Attempt %d failed, waiting 3s...\n", attempts);
      delay(3000);
      
      // Try power cycle every 5 attempts
      if (attempts % 5 == 0) {
        Serial.println("Power cycling module...");
        digitalWrite(MODEM_POWER_ON_PIN, LOW);
        delay(2000);
        digitalWrite(MODEM_POWER_ON_PIN, HIGH);
        delay(5000);
      }
    }
  }
  
  if (!res) {
    Serial.println("\n" + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + " HARDWARE DIAGNOSTICS " + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0));
    Serial.println("✗ CRITICAL: A7670E not responding to ANY AT commands!");
    Serial.println();
    Serial.println("TROUBLESHOOTING STEPS:");
    Serial.println("1. DIP SWITCHES (CRITICAL):");
    Serial.println("   - Locate DIP switches on BACK of board");
    Serial.println("   - 4G switch: Must be ON (towards 4G label)");
    Serial.println("   - USB switch: Must be OFF (towards USB label)");
    Serial.println();
    Serial.println("2. POWER SUPPLY:");
    Serial.println("   - Use 5V/3A external adapter (USB may be insufficient)");
    Serial.println("   - Check blue power LED is ON");
    Serial.println("   - Check red network LED turns ON after power-up");
    Serial.println();
    Serial.println("3. HARDWARE CONNECTIONS:");
    Serial.println("   - Ensure SIM card is fully inserted");
    Serial.println("   - Check GNSS antenna connection to IPEX1");
    Serial.println("   - Verify no loose connections");
    Serial.println();
    Serial.println("4. POSSIBLE USB COMMUNICATION MODE:");
    Serial.println("   - This board may use USB instead of UART");
    Serial.println("   - Check Waveshare wiki for USB examples");
    Serial.println("\nContinuing in WiFi-only mode...");
    Serial.println(String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0) + String("=").charAt(0));
    modemReady = false;
  } else {
    Serial.println("✓ A7670E responding to AT commands!");
    modemReady = true;
    
    // Get module information
    Serial.println("\n=== Module Information ===");
    SentMessage("AT+CGMM", 3000);  // Model
    SentMessage("AT+CGMR", 3000);  // Firmware
    SentMessage("AT+CIMI", 3000);  // SIM card
    
    // Enable GNSS with enhanced sequence
    Serial.println("\n=== Enabling GNSS ===");
    Serial.println("Enabling GNSS power (AT+CGNSSPWR=1)...");
    if (SentMessage("AT+CGNSSPWR=1", 15000)) {
      delay(3000);
      Serial.println("Enabling GNSS data output (AT+CGNSSTST=1)...");
      if (SentMessage("AT+CGNSSTST=1", 5000)) {
        Serial.println("Setting GNSS port switch...");
        SentMessage("AT+CGNSSPORTSWITCH=0,1", 3000);
        delay(2000);
        
        // Verify GNSS is working
        Serial.println("Testing GNSS status...");
        SentMessage("AT+CGNSINF", 3000);
        
        Serial.println("✓ GNSS enabled successfully");
        gnssEnabled = true;
      } else {
        Serial.println("✗ GNSS data output failed");
      }
    } else {
      Serial.println("✗ GNSS power enable failed");
    }
  }

  
  // Initialize WiFi and MQTT
  setup_wifi();
  client.setServer(mqtt_server, 1883);
  client.setCallback(callback);

  Serial.println();
  Serial.println(F("🚀 GPS Tracker initialized!"));
  Serial.println(F("📊 GPS Data Output:"));
  Serial.println(F("Sats HDOP  Latitude   Longitude   Fix  Date       Time     Alt    Course Speed  Battery  Status"));
  Serial.println(F("           (deg)      (deg)       Age                      (m)    (deg)  (km/h)  (V)      "));
  Serial.println(F("-------------------------------------------------------------------------------------------"));
}

void loop() {
  // Read battery level (if MAX17048 is available)
  float batteryLevel = readBatteryLevel();
  sendJson["data"]["batteryLevel"] = batteryLevel;

  // MQTT connection management
  if (!client.connected()) {
    reconnect();
  }
  client.loop();

  static const double LONDON_LAT = 51.508131, LONDON_LON = -0.128002;

  printInt(gps.satellites.value(), gps.satellites.isValid(), 5);
  printFloat(gps.hdop.hdop(), gps.hdop.isValid(), 6, 1);
  printFloat(gps.location.lat(), gps.location.isValid(), 11, 6);
  printFloat(gps.location.lng(), gps.location.isValid(), 12, 6);
  printInt(gps.location.age(), gps.location.isValid(), 5);
  printDateTime(gps.date, gps.time);
  printFloat(gps.altitude.meters(), gps.altitude.isValid(), 7, 2);
  printFloat(gps.course.deg(), gps.course.isValid(), 7, 2);
  printFloat(gps.speed.kmph(), gps.speed.isValid(), 6, 2);
  printStr(gps.course.isValid() ? TinyGPSPlus::cardinal(gps.course.deg()) : "*** ", 6);

  unsigned long distanceKmToLondon =
    (unsigned long)TinyGPSPlus::distanceBetween(
      gps.location.lat(),
      gps.location.lng(),
      LONDON_LAT,
      LONDON_LON)
    / 1000;
  printInt(distanceKmToLondon, gps.location.isValid(), 9);

  double courseToLondon =
    TinyGPSPlus::courseTo(
      gps.location.lat(),
      gps.location.lng(),
      LONDON_LAT,
      LONDON_LON);
  // Send GPS data to Waveshare Cloud if valid
  if (gps.location.isValid()) {
    sendJson["data"]["Latitude"] = gps.location.lat();
    sendJson["data"]["Longitude"] = gps.location.lng();
    sendJson["data"]["altitude"] = gps.altitude.meters();
    sendJson["data"]["speed"] = gps.speed.kmph();
    sendJson["data"]["course"] = gps.course.deg();
    sendJson["data"]["satellites"] = gps.satellites.value();
    sendJson["data"]["hdop"] = gps.hdop.hdop();
    
    // Send data to cloud
    sendJsonData();
    Serial.print(" 📡SENT ");
  } else {
    Serial.print(" ⏳WAIT ");
  }
  printFloat(courseToLondon, gps.location.isValid(), 7, 2);

  const char *cardinalToLondon = TinyGPSPlus::cardinal(courseToLondon);

  printStr(gps.location.isValid() ? cardinalToLondon : "*** ", 6);

  printInt(gps.charsProcessed(), true, 6);
  printInt(gps.sentencesWithFix(), true, 10);
  printInt(gps.failedChecksum(), true, 9);
  Serial.printf(" %6.2fV", batteryLevel);
  Serial.println();

  smartDelay(5000); // Update every 5 seconds

  // GPS health check and recovery
  if (millis() > 30000 && gps.charsProcessed() < 10) {
    Serial.println(F("⚠️ No GPS data received - check GNSS antenna and outdoor location"));
    
    // Try to re-enable GNSS if no data
    if (modemReady && !gnssEnabled) {
      Serial.println("Attempting to re-enable GNSS...");
      SentMessage("AT+CGNSSPWR=1", 10000);
      delay(2000);
      SentMessage("AT+CGNSSTST=1", 3000);
      gnssEnabled = true;
    }
  }
}

static void smartDelay(unsigned long ms) {
  unsigned long start = millis();
  do {
    while (SerialAT.available()) {
      char c = SerialAT.read();
      gps.encode(c);
      // Uncomment to see raw NMEA data:
      // Serial.write(c);
    }
    yield();
  } while (millis() - start < ms);
}

// Read battery level from MAX17048 (if available)
float readBatteryLevel() {
  Wire.beginTransmission(MAX17048_I2C_ADDRESS);
  Wire.write(0x02);
  if (Wire.endTransmission() != 0) {
    return 0.0; // Battery monitor not available
  }

  Wire.requestFrom(MAX17048_I2C_ADDRESS, 2);
  if (Wire.available() >= 2) {
    uint16_t soc = (Wire.read() << 8) | Wire.read();
    if (soc > 65535) soc = 65535;
    return (float)soc / 65535.0 * 5.0; // Convert to voltage estimate
  }
  return 0.0;
}

static void printFloat(float val, bool valid, int len, int prec) {
  if (!valid) {
    while (len-- > 1)
      Serial.print('*');
    Serial.print(' ');
  } else {
    Serial.print(val, prec);
    int vi = abs((int)val);
    int flen = prec + (val < 0.0 ? 2 : 1);  // . and -
    flen += vi >= 1000 ? 4 : vi >= 100 ? 3
                           : vi >= 10  ? 2
                                       : 1;
    for (int i = flen; i < len; ++i)
      Serial.print(' ');
  }
  smartDelay(0);
}

static void printInt(unsigned long val, bool valid, int len) {
  char sz[32] = "*****************";
  if (valid)
    sprintf(sz, "%ld", val);
  sz[len] = 0;
  for (int i = strlen(sz); i < len; ++i)
    sz[i] = ' ';
  if (len > 0)
    sz[len - 1] = ' ';
  Serial.print(sz);
  smartDelay(0);
}

static void printDateTime(TinyGPSDate &d, TinyGPSTime &t) {
  if (!d.isValid()) {
    Serial.print(F("********** "));
  } else {
    char sz[32];
    sprintf(sz, "%02d/%02d/%02d ", d.month(), d.day(), d.year());
    Serial.print(sz);
  }

  if (!t.isValid()) {
    Serial.print(F("******** "));
  } else {
    char sz[32];
    sprintf(sz, "%02d:%02d:%02d ", t.hour(), t.minute(), t.second());
    Serial.print(sz);
  }

  printInt(d.age(), d.isValid(), 5);
  smartDelay(0);
}

static void printStr(const char *str, int len) {
  int slen = strlen(str);
  for (int i = 0; i < len; ++i)
    Serial.print(i < slen ? str[i] : ' ');
  smartDelay(0);
}

void callback(char *topic, byte *payload, unsigned int length) {
  String inputString;
  for (int i = 0; i < length; i++) {
    inputString += (char)payload[i];
  }
  Serial.println(inputString);
  int jsonBeginAt = inputString.indexOf("{");
  int jsonEndAt = inputString.lastIndexOf("}");
  if (jsonBeginAt != -1 && jsonEndAt != -1) {
    inputString = inputString.substring(jsonBeginAt, jsonEndAt + 1);
    deserializeJson(readJson, inputString);
  }
}
void setup_wifi() {
  // Check if credentials are updated
  if (String(ssid) == "YourWiFiNetwork" || String(password) == "YourWiFiPassword") {
    Serial.println("⚠️ WiFi credentials not updated!");
    Serial.println("Please update STASSID and STAPSK in code");
    return;
  }
  
  Serial.println();
  Serial.print("Connecting to WiFi: ");
  Serial.println(ssid);
  
  WiFi.mode(WIFI_STA);
  WiFi.begin(ssid, password);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\n✓ WiFi connected!");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
  } else {
    Serial.println("\n✗ WiFi connection failed");
  }
}

void reconnect() {
  while (!client.connected()) {
    Serial.print("Connecting to MQTT...");
    if (client.connect(clientID)) {
      Serial.println(" ✓ Connected");
      client.subscribe(sub);
    } else {
      Serial.printf(" ✗ Failed, rc=%d, retrying in 5s\n", client.state());
      delay(5000);
    }
  }
}

void sendJsonData() {
  if (client.connected()) {
    String pubres;
    serializeJson(sendJson, pubres);
    client.publish(pub, pubres.c_str());
    Serial.printf("📤 Sent to cloud: %s\n", pubres.c_str());
  }
}
