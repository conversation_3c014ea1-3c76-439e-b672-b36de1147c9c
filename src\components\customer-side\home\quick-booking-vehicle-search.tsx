"use client";

import React, { useState, useCallback } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Car as CarIcon,
  Users,
  Fuel,
  Cog,
  Star,
  Search,
  Filter,
  ArrowRight,
  Loader2,
  X,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import type { Car } from "@/lib/types";
import { createClient } from "@/lib/supabase/client";
import { buildBookingFlowUrl } from "@/lib/customer-paths";
import { cn } from "@/lib/utils";

interface QuickBookingVehicleSearchProps {
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
  pickupDate: string;
  dropoffDate: string;
  pickupLocation: string;
  dropoffLocation: string;
  pickupDateTime: string;
  dropoffDateTime: string;
  onVehicleSelect: (car: Car) => void;
  isVisible: boolean;
  onClose: () => void;
}

export function QuickBookingVehicleSearch({
  searchQuery,
  onSearchQueryChange,
  pickupDate,
  dropoffDate,
  pickupLocation,
  dropoffLocation,
  pickupDateTime,
  dropoffDateTime,
  onVehicleSelect,
  isVisible,
  onClose,
}: QuickBookingVehicleSearchProps) {
  const router = useRouter();
  const [vehicles, setVehicles] = useState<Car[]>([]);
  const [filteredVehicles, setFilteredVehicles] = useState<Car[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [priceFilter, setPriceFilter] = useState<string>("all");

  // Fetch available vehicles based on date/location criteria
  const fetchAvailableVehicles = useCallback(async () => {
    if (!pickupDate || !dropoffDate || !pickupLocation || !dropoffLocation) {
      setVehicles([]);
      setFilteredVehicles([]);
      return;
    }

    setIsLoading(true);
    try {
      const supabase = createClient();

      // Get vehicles that are not booked during the specified period
      const pickupDateTime = new Date(`${pickupDate}T00:00:00`);
      const dropoffDateTime = new Date(`${dropoffDate}T23:59:59`);

      // First, get booked car IDs for the date range
      const { data: bookings, error: bookingError } = await supabase
        .from("bookings")
        .select("car_id")
        .in("status", ["Pending", "Active", "Confirmed"])
        .or(
          `and(pickup_datetime.lte.${dropoffDateTime.toISOString()},dropoff_datetime.gte.${pickupDateTime.toISOString()})`
        );

      if (bookingError) {
        console.error("Error fetching bookings:", bookingError);
      }

      const bookedCarIds = bookings?.map((b) => b.car_id) || [];

      // Then get available cars excluding booked ones
      let query = supabase
        .from("cars")
        .select("*")
        .eq("is_archived", false)
        .eq("status", "Available")
        .order("price_per_day", { ascending: true });

      // Exclude booked cars if any exist
      if (bookedCarIds.length > 0) {
        query = query.not("id", "in", `(${bookedCarIds.join(",")})`);
      }

      const { data: availableCars, error: carsError } = await query;

      if (carsError) {
        console.error("Error fetching available cars:", carsError);
        setVehicles([]);
        setFilteredVehicles([]);
        return;
      }

      const cars = availableCars || [];
      setVehicles(cars);
      setFilteredVehicles(cars);
    } catch (error) {
      console.error("Error in fetchAvailableVehicles:", error);
      setVehicles([]);
      setFilteredVehicles([]);
    } finally {
      setIsLoading(false);
    }
  }, [pickupDate, dropoffDate, pickupLocation, dropoffLocation]);

  // Filter vehicles based on search query, category, and price
  React.useEffect(() => {
    let filtered = [...vehicles];

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (car) =>
          car.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
          car.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
          car.fuel_type.toLowerCase().includes(searchQuery.toLowerCase()) ||
          car.transmission.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply category filter
    if (selectedCategory !== "all") {
      filtered = filtered.filter((car) => car.type === selectedCategory);
    }

    // Apply price filter
    if (priceFilter !== "all") {
      switch (priceFilter) {
        case "budget":
          filtered = filtered.filter((car) => car.price_per_day <= 2000);
          break;
        case "mid":
          filtered = filtered.filter(
            (car) => car.price_per_day > 2000 && car.price_per_day <= 4000
          );
          break;
        case "premium":
          filtered = filtered.filter((car) => car.price_per_day > 4000);
          break;
      }
    }

    setFilteredVehicles(filtered);
  }, [vehicles, searchQuery, selectedCategory, priceFilter]);

  // Fetch vehicles when component becomes visible or criteria change
  React.useEffect(() => {
    if (isVisible) {
      fetchAvailableVehicles();
    }
  }, [isVisible, fetchAvailableVehicles]);

  // Get unique categories from vehicles
  const categories = React.useMemo(() => {
    const uniqueCategories = [...new Set(vehicles.map((car) => car.type))];
    return uniqueCategories.sort();
  }, [vehicles]);

  const handleVehicleSelect = (car: Car) => {
    // Navigate directly to booking flow with selected vehicle and dates
    const bookingUrl = buildBookingFlowUrl({
      carId: car.id.toString(),
      pickUpLocation: pickupLocation,
      dropOffLocation: dropoffLocation,
      pickUpDateTime: pickupDateTime,
      dropOffDateTime: dropoffDateTime,
    });
    router.push(bookingUrl);
  };

  if (!isVisible) return null;

  return (
    <div className="mt-6 sm:mt-8 transition-all duration-300 ease-in-out transform overflow-hidden">
      <div className="text-center mb-4 sm:mb-6">
        <div className="flex items-center justify-center gap-2 sm:gap-3">
          <div className="inline-flex items-center gap-2 sm:gap-3 bg-white px-4 sm:px-6 py-2 sm:py-3 rounded-full shadow-sm border border-blue-100">
            <CarIcon className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
            <h3 className="text-base sm:text-lg font-semibold text-gray-800">
              Available Vehicles
            </h3>
          </div>
          <Button
            variant="secondary"
            size="sm"
            onClick={onClose}
            className="rounded-full p-2 hover:bg-gray-100"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-xs sm:text-sm text-gray-500 mt-2">
          {pickupDate && dropoffDate
            ? `Available from ${new Date(
                pickupDate
              ).toLocaleDateString()} to ${new Date(
                dropoffDate
              ).toLocaleDateString()}`
            : "Select your dates to see available vehicles"}
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search vehicles..."
              value={searchQuery}
              onChange={(e) => onSearchQueryChange(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Category Filter */}
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger>
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Price Filter */}
          <Select value={priceFilter} onValueChange={setPriceFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Price Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Prices</SelectItem>
              <SelectItem value="budget">Budget (≤₱2,000)</SelectItem>
              <SelectItem value="mid">Mid-range (₱2,001-₱4,000)</SelectItem>
              <SelectItem value="premium">Premium ({">"}₱4,000)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Results Summary */}
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>
            {isLoading
              ? "Searching..."
              : `${filteredVehicles.length} vehicle${
                  filteredVehicles.length !== 1 ? "s" : ""
                } available`}
          </span>
          {(searchQuery ||
            selectedCategory !== "all" ||
            priceFilter !== "all") && (
            <Button
              variant="secondary"
              size="sm"
              onClick={() => {
                onSearchQueryChange("");
                setSelectedCategory("all");
                setPriceFilter("all");
              }}
              className="text-blue-600 hover:text-blue-700"
            >
              Clear filters
            </Button>
          )}
        </div>
      </div>

      {/* Vehicle Results */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="text-center py-12">
            <div className="inline-flex items-center gap-2">
              <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
              <span className="text-sm sm:text-base text-gray-600">
                Loading available vehicles...
              </span>
            </div>
          </div>
        ) : filteredVehicles.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <div className="mb-4">
              <CarIcon className="h-12 w-12 mx-auto text-gray-400" />
            </div>
            <h4 className="text-base sm:text-lg font-medium mb-2">
              No vehicles available
            </h4>
            <p className="text-sm mb-4">
              Try different dates, locations, or adjust your filters
            </p>
            <Button
              variant="secondary"
              onClick={() => {
                onSearchQueryChange("");
                setSelectedCategory("all");
                setPriceFilter("all");
              }}
            >
              Clear all filters
            </Button>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-2 xl:grid-cols-3">
              {filteredVehicles.slice(0, 6).map((car) => (
                <Card
                  key={car.id}
                  className="overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer group"
                  onClick={() => handleVehicleSelect(car)}
                >
                  <div className="relative h-48 bg-gray-100">
                    <Image
                      src={car.image_url || "/placeholder.svg"}
                      alt={car.model}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-200"
                    />
                    <Badge className="absolute top-2 right-2 bg-green-100 text-green-800 border-green-200">
                      Available
                    </Badge>
                  </div>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="min-w-0 flex-1">
                        <h4 className="font-bold text-gray-900 text-sm leading-tight">
                          {car.model.toUpperCase()}
                        </h4>
                        <p className="text-xs text-gray-500 mt-1">
                          {car.type} • {car.transmission} Transmission
                        </p>
                      </div>
                      <Badge
                        variant="secondary"
                        className="bg-blue-50 border-blue-200 text-blue-700 flex-shrink-0 ml-2"
                      >
                        {car.type}
                      </Badge>
                    </div>

                    <div className="mt-4 space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2 text-gray-600">
                          <Users className="h-4 w-4 flex-shrink-0" />
                          <span>{car.seats} Seater</span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-600">
                          <Fuel className="h-4 w-4 flex-shrink-0" />
                          <span className="truncate">{car.fuel_type}</span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2 text-gray-600">
                          <Cog className="h-4 w-4 flex-shrink-0" />
                          <span className="truncate">{car.transmission}</span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-600">
                          <Star className="h-4 w-4 text-yellow-500 flex-shrink-0" />
                          <span className="font-medium">4.8</span>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-gray-100">
                      <div className="flex justify-between items-center">
                        <div>
                          <span className="text-sm text-gray-500">From</span>
                          <div className="font-bold text-lg text-gray-900">
                            ₱{car.price_per_day.toLocaleString()}/day
                          </div>
                        </div>
                        <Button
                          size="sm"
                          className="bg-blue-600 hover:bg-blue-700"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleVehicleSelect(car);
                          }}
                        >
                          Select
                          <ArrowRight className="w-4 h-4 ml-1" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Show count if more vehicles available */}
            {filteredVehicles.length > 6 && (
              <div className="text-center mt-6 sm:mt-8 px-2 sm:px-0">
                <p className="text-sm text-gray-600">
                  Showing 6 of {filteredVehicles.length} available vehicles
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Select any vehicle above to proceed with booking
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
