-- Add missing RLS policies for chatbot_knowledge table (NON-DESTRUCTIVE)
-- Allows admins and super_admins to manage knowledge base entries
-- Only creates policies that don't already exist

-- Add INSERT policy for admins and super_admins (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'chatbot_knowledge' 
        AND policyname = 'chatbot_knowledge_insert'
    ) THEN
        CREATE POLICY "chatbot_knowledge_insert" ON chatbot_knowledge
            FOR INSERT TO authenticated
            WITH CHECK (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() 
                    AND role IN ('admin', 'super_admin')
                )
            );
    END IF;
END $$;

-- Add UPDATE policy for admins and super_admins (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'chatbot_knowledge' 
        AND policyname = 'chatbot_knowledge_update'
    ) THEN
        CREATE POLICY "chatbot_knowledge_update" ON chatbot_knowledge
            FOR UPDATE TO authenticated
            USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() 
                    AND role IN ('admin', 'super_admin')
                )
            );
    END IF;
END $$;

-- Add DELETE policy for admins and super_admins (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'chatbot_knowledge' 
        AND policyname = 'chatbot_knowledge_delete'
    ) THEN
        CREATE POLICY "chatbot_knowledge_delete" ON chatbot_knowledge
            FOR DELETE TO authenticated
            USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() 
                    AND role IN ('admin', 'super_admin')
                )
            );
    END IF;
END $$;
