"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  checkUserLegalDocuments,
  saveUserLegalDocument,
  getStandardDocumentType,
  type DocumentCheckResult,
} from "@/lib/services/document-service";
import { createClient } from "@/lib/supabase/client";

export default function TestNewAccountDocumentsPage() {
  const [testResults, setTestResults] = React.useState<string[]>([]);
  const [documentCheck, setDocumentCheck] = React.useState<DocumentCheckResult | null>(null);
  const [isRunning, setIsRunning] = React.useState(false);
  const [userId, setUserId] = React.useState<string>("");

  const addResult = (message: string, isError = false) => {
    console.log(message);
    setTestResults(prev => [...prev, `${isError ? "❌" : "✅"} ${message}`]);
  };

  const runComprehensiveTest = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      // Get current user
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        addResult("No authenticated user found", true);
        return;
      }
      
      setUserId(user.id);
      addResult(`Testing with user: ${user.email}`);

      // Test 1: Check initial document state (should be empty for new accounts)
      addResult("🔍 Test 1: Checking initial document state...");
      const initialCheck = await checkUserLegalDocuments();
      setDocumentCheck(initialCheck);
      addResult(`Found ${initialCheck.existingDocuments.length} existing documents`);
      addResult(`Document summary: ${JSON.stringify(initialCheck.documentSummary)}`);

      // Test 2: Test document key mapping consistency
      addResult("🔍 Test 2: Testing document key mapping...");
      const testKeys = ['driversLicense', 'governmentId', 'proofOfBilling'];
      for (const key of testKeys) {
        const standardType = getStandardDocumentType(key);
        addResult(`${key} → ${standardType}`);
      }

      // Test 3: Test document upload (simulated)
      addResult("🔍 Test 3: Testing document upload simulation...");
      
      // Create a test file
      const testFile = new File(['test content'], 'test-document.pdf', { type: 'application/pdf' });
      
      // Test upload for each document type
      for (const key of testKeys) {
        try {
          const standardType = getStandardDocumentType(key);
          addResult(`Attempting to upload ${key} (${standardType})...`);
          
          const result = await saveUserLegalDocument(standardType, testFile);
          if (result.success) {
            addResult(`Successfully uploaded ${key}`);
          } else {
            addResult(`Failed to upload ${key}: ${result.error}`, true);
          }
        } catch (error) {
          addResult(`Error uploading ${key}: ${error}`, true);
        }
      }

      // Test 4: Verify documents after upload
      addResult("🔍 Test 4: Verifying documents after upload...");
      const postUploadCheck = await checkUserLegalDocuments();
      setDocumentCheck(postUploadCheck);
      addResult(`Found ${postUploadCheck.existingDocuments.length} documents after upload`);
      addResult(`Updated summary: ${JSON.stringify(postUploadCheck.documentSummary)}`);

      // Test 5: Check if documents are accessible across different pages
      addResult("🔍 Test 5: Cross-page document accessibility test...");
      for (const doc of postUploadCheck.existingDocuments) {
        // Test mapping from database format to UI format
        let uiKey = "";
        switch (doc.document_type) {
          case "drivers_license":
            uiKey = "driversLicense";
            break;
          case "government_id":
            uiKey = "governmentId";
            break;
          case "proof_of_billing":
            uiKey = "proofOfBilling";
            break;
        }
        addResult(`Document ${doc.document_type} maps to UI key: ${uiKey}`);
      }

      // Test 6: Check booking creation for new users
      addResult("🔍 Test 6: Checking booking creation for document storage...");
      const { data: bookings } = await supabase
        .from("bookings")
        .select("id, status, customer_id")
        .eq("customer_id", user.id);
      
      addResult(`User has ${bookings?.length || 0} booking(s)`);
      if (bookings && bookings.length > 0) {
        bookings.forEach((booking, index) => {
          addResult(`Booking ${index + 1}: ${booking.id} (status: ${booking.status})`);
        });
      }

      addResult("🎉 All tests completed!");

    } catch (error) {
      addResult(`Test error: ${error}`, true);
    } finally {
      setIsRunning(false);
    }
  };

  const clearTestData = async () => {
    try {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        addResult("No authenticated user found", true);
        return;
      }

      // Delete all documents for this user
      const { data: bookings } = await supabase
        .from("bookings")
        .select("id")
        .eq("customer_id", user.id);

      if (bookings && bookings.length > 0) {
        const bookingIds = bookings.map(b => b.id);
        
        // Delete documents
        await supabase
          .from("booking_documents")
          .delete()
          .in("booking_id", bookingIds);
        
        // Delete document_storage bookings
        await supabase
          .from("bookings")
          .delete()
          .eq("customer_id", user.id)
          .eq("status", "document_storage");
        
        addResult("Test data cleared successfully");
        setTestResults([]);
        setDocumentCheck(null);
      }
    } catch (error) {
      addResult(`Error clearing test data: ${error}`, true);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold">
              New Account Document Flow Test
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-gray-600">
                This page tests the complete document upload and retrieval flow for new accounts
                across all three pages: Setup Account, Booking Process Step 2, and Account Settings.
              </p>
              
              {userId && (
                <div className="bg-blue-50 p-3 rounded-lg">
                  <strong>Current User:</strong> {userId}
                </div>
              )}

              <div className="flex gap-3">
                <Button 
                  onClick={runComprehensiveTest}
                  disabled={isRunning}
                  className="flex items-center gap-2"
                >
                  {isRunning ? "Running Tests..." : "Run Comprehensive Test"}
                </Button>
                
                <Button 
                  variant="secondary"
                  onClick={clearTestData}
                  disabled={isRunning}
                >
                  Clear Test Data
                </Button>
              </div>

              {/* Document Status Summary */}
              {documentCheck && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Current Document Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {documentCheck.documentSummary.approved}
                        </div>
                        <div className="text-sm text-green-700">Approved</div>
                      </div>
                      <div className="text-center p-3 bg-yellow-50 rounded-lg">
                        <div className="text-2xl font-bold text-yellow-600">
                          {documentCheck.documentSummary.pending}
                        </div>
                        <div className="text-sm text-yellow-700">Pending</div>
                      </div>
                      <div className="text-center p-3 bg-red-50 rounded-lg">
                        <div className="text-2xl font-bold text-red-600">
                          {documentCheck.documentSummary.rejected}
                        </div>
                        <div className="text-sm text-red-700">Rejected</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-gray-600">
                          {documentCheck.documentSummary.missing}
                        </div>
                        <div className="text-sm text-gray-700">Missing</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div 
                    key={index} 
                    className={`p-2 rounded text-sm font-mono ${
                      result.includes('❌') 
                        ? 'bg-red-50 text-red-800' 
                        : 'bg-green-50 text-green-800'
                    }`}
                  >
                    {result}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Testing Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div>
                <h4 className="font-semibold mb-2">What this test validates:</h4>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Document service creates placeholder bookings for new users</li>
                  <li>Document key mapping consistency across all pages</li>
                  <li>Document upload functionality works for new accounts</li>
                  <li>Cross-page document accessibility and autofill</li>
                  <li>Database operations work correctly</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Expected behavior:</h4>
                <ul className="list-disc pl-5 space-y-1">
                  <li>New users should start with 0 documents</li>
                  <li>Document uploads should create placeholder bookings automatically</li>
                  <li>Documents should be accessible across all three pages</li>
                  <li>No database errors should occur</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Manual testing steps:</h4>
                <ol className="list-decimal pl-5 space-y-1">
                  <li>Create a new account and go through the setup process</li>
                  <li>Upload documents in the Setup Account page</li>
                  <li>Check if documents appear in Booking Process Step 2</li>
                  <li>Verify documents are visible in Account Settings</li>
                  <li>Test document deletion and re-upload</li>
                </ol>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
