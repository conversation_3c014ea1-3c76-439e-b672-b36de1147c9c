#include <TinyGPS++.h>
#include <ArduinoJson.h>
#include <Arduino.h>
#include <PubSubClient.h>
#include <WiFi.h>
#include <Wire.h>

#define MSG_BUFFER_SIZE (50)
#define STASSID "PLDTHOMEFIBR339c0"
#define STAPSK "PLDTWIFIzxj9r"

#define MAX17048_I2C_ADDRESS 0x36
const char *clientID = "b4922afa"; // Client ID
char sub[] = "Sub/1149/37/b4922afa"; // Sub Topic
char pub[] = "Pub/1149/37/b4922afa"; // Pub Topic
const char *mqtt_server = "mqtt.waveshare.cloud";

StaticJsonDocument<400> sendJson;
StaticJsonDocument<400> readJson;
unsigned long lastUpdateTime = 0;
const char *ssid = STASSID;
const char *password = STAPSK;
char msg[MSG_BUFFER_SIZE];
WiFiClient espClient;
PubSubClient client(espClient);

const unsigned long updateInterval = 5000;

// ✅ CORRECTED: Use correct baud rate and pins for ESP32-S3-A7670E-4G
static const int RXPin = 18, TXPin = 17;  // Corrected pin assignment
static const uint32_t GPSBaud = 9600;     // Corrected baud rate

TinyGPSPlus gps;
String rev;

// A7670E Control Pins
#define MODEM_PWRKEY_PIN 4
#define MODEM_POWER_ON_PIN 33

// Function to send data to the A7670E module
void SentSerial(const char *p_char) {
  Serial.printf("Sending AT: %s\n", p_char);
  for (int i = 0; i < strlen(p_char); i++) {
    Serial1.write(p_char[i]);
    delay(5);
  }
  Serial1.write('\r');
  delay(5);
  Serial1.write('\n');
  delay(100);
}

// Enhanced function to send a message and wait for a response
bool SentMessage(const char *p_char, unsigned long timeout = 5000) {
  SentSerial(p_char);
  unsigned long start = millis();
  String response = "";
  
  while (millis() - start < timeout) {
    if (Serial1.available()) {
      char c = Serial1.read();
      response += c;
      
      if (response.indexOf("OK") >= 0) {
        Serial.printf("✓ AT Response: %s\n", response.c_str());
        return true;
      }
      if (response.indexOf("ERROR") >= 0) {
        Serial.printf("✗ AT Error: %s\n", response.c_str());
        return false;
      }
      if (response.indexOf("+CGNSSPWR: READY!") >= 0) {
        Serial.println("✓ GNSS Power Ready!");
        return true;
      }
    }
    yield();
  }
  Serial.printf("✗ AT Timeout for: %s\n", p_char);
  return false;
}

void setup() {
  Wire.begin(3, 2);
  
  // ✅ Enhanced A7670E Power Control Sequence
  pinMode(MODEM_POWER_ON_PIN, OUTPUT);
  pinMode(MODEM_PWRKEY_PIN, OUTPUT);
  
  Serial.println("=== A7670E Power Sequence ===");
  digitalWrite(MODEM_POWER_ON_PIN, LOW);
  digitalWrite(MODEM_PWRKEY_PIN, HIGH);
  delay(100);
  
  Serial.println("Step 1: Powering on A7670E...");
  digitalWrite(MODEM_POWER_ON_PIN, HIGH);
  delay(1000);
  
  Serial.println("Step 2: PWRKEY pulse (3s)...");
  digitalWrite(MODEM_PWRKEY_PIN, LOW);
  delay(3000);  // Extended PWRKEY pulse
  digitalWrite(MODEM_PWRKEY_PIN, HIGH);
  
  Serial.println("Step 3: Waiting for module boot (10s)...");
  delay(10000);  // Extended boot wait
  
  Serial.begin(115200); // For USB communication
  Serial.println("=== ESP32-S3-A7670E GPS Tracker ===");

  // ✅ Initialize Hardware Serial with corrected pins and baud rate
  Serial1.begin(GPSBaud, SERIAL_8N1, RXPin, TXPin);
  Serial.printf("UART initialized: RX=%d, TX=%d, Baud=%d\n", RXPin, TXPin, GPSBaud);

  // ✅ Enhanced AT Communication Test
  Serial.println("=== Testing A7670E Communication ===");
  bool res = false;
  int attempts = 0;
  
  while (!res && attempts < 15) {
    attempts++;
    Serial.printf("AT Test attempt %d/15...\n", attempts);
    
    // Try different AT command formats
    res = SentMessage("AT") || 
          SentMessage("AT\r\n") || 
          SentMessage("AT\r") ||
          SentMessage("+++");
    
    if (!res) {
      delay(2000);
      // Power cycle every 5 attempts
      if (attempts % 5 == 0) {
        Serial.println("Power cycling A7670E...");
        digitalWrite(MODEM_POWER_ON_PIN, LOW);
        delay(2000);
        digitalWrite(MODEM_POWER_ON_PIN, HIGH);
        digitalWrite(MODEM_PWRKEY_PIN, LOW);
        delay(3000);
        digitalWrite(MODEM_PWRKEY_PIN, HIGH);
        delay(5000);
      }
    }
  }
  
  if (res) {
    Serial.println("✅ A7670E Communication Established!");
    
    // ✅ Enable GNSS with official Waveshare commands
    Serial.println("=== Enabling GNSS ===");
    Serial.println("Powering GNSS...");
    SentMessage("AT+CGNSSPWR=1", 10000);
    delay(10000);  // Wait for GNSS to power up
    
    Serial.println("Starting GNSS data output...");
    SentMessage("AT+CGNSSTST=1", 3000);
    
    Serial.println("Setting GNSS port switch...");
    SentMessage("AT+CGNSSPORTSWITCH=0,1", 3000);
    
    Serial.println("✅ GNSS Enabled - GPS data should start flowing");
  } else {
    Serial.println("❌ A7670E Communication Failed!");
    Serial.println("\n=== TROUBLESHOOTING GUIDE ===");
    Serial.println("1. Check DIP Switch: 4G=ON, USB=OFF");
    Serial.println("2. Verify 5V/3A external power supply");
    Serial.println("3. Check blue power LED and red network LED");
    Serial.println("4. Ensure proper antenna connections");
    Serial.println("5. Try USB communication mode if UART fails");
    Serial.println("Continuing in WiFi-only mode...\n");
  }

  setup_wifi();
  client.setServer(mqtt_server, 1883);
  client.setCallback(callback);

  Serial.println(F("✅ GPS Tracker Ready"));
  Serial.println(F("Sats HDOP  Latitude   Longitude   Fix  Date        Time      Alt    Course Speed"));
  Serial.println(F("------------------------------------------------------------------------"));
}

void loop() {
  // Read battery level
  float batteryLevel = readBatteryLevel();
  
  // ✅ CRITICAL: Complete JSON structure for Waveshare Cloud
  sendJson.clear();  // Clear previous data
  sendJson["data"]["batteryLevel"] = batteryLevel;

  if (!client.connected()) {
    reconnect();
  }
  client.loop();

  // Process GPS data and send only if valid
  if (gps.location.isValid()) {
    // ✅ FIXED: Proper GPS data structure for Waveshare Cloud map display
    sendJson["data"]["Latitude"] = gps.location.lat();
    sendJson["data"]["Longitude"] = gps.location.lng();
    sendJson["data"]["altitude"] = gps.altitude.meters();
    sendJson["data"]["speed"] = gps.speed.kmph(); 
    sendJson["data"]["course"] = gps.course.deg();
    sendJson["data"]["satellites"] = gps.satellites.value();
    sendJson["data"]["hdop"] = gps.hdop.hdop();
    
    // Add timestamp for tracking
    sendJson["data"]["timestamp"] = millis();
    
    // Send to Waveshare Cloud
    sendJsonData();
    
    // Print GPS status for monitoring
    printGPSData();
  } else {
    Serial.print(".");  // Show we're trying to get GPS fix
  }

  smartDelay(5000);

  // GPS health check - restart GNSS if no data received
  if (millis() > 30000 && gps.charsProcessed() < 10) {
    Serial.println(F("\n⚠️ No GPS data received - attempting GNSS restart"));
    SentMessage("AT+CGNSSPWR=1", 10000);
    delay(2000);
    SentMessage("AT+CGNSSTST=1", 3000);
  }
}

void printGPSData() {
  static unsigned long lastPrint = 0;
  if (millis() - lastPrint < 10000) return;  // Print every 10 seconds
  lastPrint = millis();
  
  Serial.printf("GPS: Lat=%.6f, Lng=%.6f, Alt=%.1fm, Speed=%.1fkm/h, Sats=%d, HDOP=%.1f\n",
                gps.location.lat(), gps.location.lng(), gps.altitude.meters(),
                gps.speed.kmph(), gps.satellites.value(), gps.hdop.hdop());
}

// Read battery level from MAX17048
float readBatteryLevel() {
  Wire.beginTransmission(MAX17048_I2C_ADDRESS);
  Wire.write(0x02);
  if (Wire.endTransmission() != 0) {
    return 3.7; // Default battery voltage if sensor unavailable
  }

  Wire.requestFrom(MAX17048_I2C_ADDRESS, 2);
  if (Wire.available() >= 2) {
    uint16_t soc = (Wire.read() << 8) | Wire.read();
    if (soc > 65535) soc = 65535;
    return (float)soc / 65535.0 * 5.0;
  }
  return 3.7;
}

static void smartDelay(unsigned long ms) {
  unsigned long start = millis();
  do {
    while (Serial1.available()) {
      char c = Serial1.read();
      gps.encode(c);
      // Uncomment to debug NMEA data:
      // Serial.write(c);
    }
    yield();
  } while (millis() - start < ms);
}

void callback(char *topic, byte *payload, unsigned int length) {
  String inputString;
  for (int i = 0; i < length; i++) {
    inputString += (char)payload[i];
  }
  Serial.println("MQTT received: " + inputString);
  
  int jsonBeginAt = inputString.indexOf("{");
  int jsonEndAt = inputString.lastIndexOf("}");
  if (jsonBeginAt != -1 && jsonEndAt != -1) {
    inputString = inputString.substring(jsonBeginAt, jsonEndAt + 1);
    deserializeJson(readJson, inputString);
  }
}

void setup_wifi() {
  // ✅ Enhanced WiFi connection with validation
  if (strlen(ssid) < 3 || strlen(password) < 3) {
    Serial.println("❌ WiFi credentials not set! Please update STASSID and STAPSK");
    return;
  }
  
  Serial.println();
  Serial.print("Connecting to ");
  Serial.println(ssid);
  WiFi.mode(WIFI_STA);
  WiFi.begin(ssid, password);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\n✅ WiFi connected!");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
  } else {
    Serial.println("\n❌ WiFi connection failed!");
  }
}

void reconnect() {
  while (!client.connected()) {
    Serial.print("Attempting MQTT connection...");
    if (client.connect(clientID)) {
      Serial.println(" connected!");
      client.subscribe(sub);
    } else {
      Serial.print(" failed, rc=");
      Serial.print(client.state());
      Serial.println(" retrying in 5 seconds");
      delay(5000);
    }
  }
}

void sendJsonData() {
  String pubres;
  serializeJson(sendJson, pubres);
  Serial.println("Sending to cloud: " + pubres);
  
  int str_len = pubres.length() + 1;
  char char_array[str_len];
  pubres.toCharArray(char_array, str_len);
  
  bool success = client.publish(pub, char_array);
  if (!success) {
    Serial.println("❌ MQTT publish failed!");
  } else {
    Serial.println("✅ GPS data sent to Waveshare Cloud");
  }
}
