"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RenterIssueTracking } from "@/components/admin/bookings/renter-issue-tracking";
import { useRenterIssueData } from "@/hooks/use-renter-issues";
import { useAdminAuth } from "@/components/auth/admin-auth-context";

export default function TestRenterIssueTrackingPage() {
  const { user, profile, loading: authLoading } = useAdminAuth();
  const [testCustomerId, setTestCustomerId] = React.useState<string>("");

  if (authLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[200px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>Loading authentication...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!profile || (profile.role !== "admin" && profile.role !== "super_admin")) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="text-center text-red-700">
              <h2 className="text-xl font-bold mb-2">Access Denied</h2>
              <p>This page is only accessible to admin users.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="space-y-6">
        {/* Page Header */}
        <Card className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
          <CardHeader>
            <CardTitle className="text-2xl">
              🧪 Renter Issue Tracking Test Page
            </CardTitle>
            <p className="text-blue-100">
              Test the renter issue tracking functionality with sample data
            </p>
          </CardHeader>
        </Card>

        {/* Admin Info */}
        <Card>
          <CardHeader>
            <CardTitle>Admin Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Name:</strong> {profile.full_name || "Not set"}
              </div>
              <div>
                <strong>Email:</strong> {profile.email}
              </div>
              <div>
                <strong>Role:</strong> {profile.role}
              </div>
              <div>
                <strong>Admin ID:</strong> {profile.id}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customer ID Input */}
        <Card>
          <CardHeader>
            <CardTitle>Test Customer ID</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Enter a customer ID to test renter issue tracking:
              </label>
              <input
                type="text"
                placeholder="e.g., customer-uuid-here"
                value={testCustomerId}
                onChange={(e) => setTestCustomerId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="text-sm text-gray-600">
              <p>
                <strong>Note:</strong> You can find customer IDs from the
                bookings page or profiles table.
              </p>
              <p>
                The system will create a test interface even if the customer
                doesn't exist yet.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Issue Tracking Interface */}
        {testCustomerId && (
          <TestRenterIssueInterface
            customerId={testCustomerId}
            currentAdminId={profile.id}
          />
        )}

        {/* Instructions */}
        <Card className="bg-yellow-50 border-yellow-200">
          <CardHeader>
            <CardTitle className="text-yellow-800">
              Testing Instructions
            </CardTitle>
          </CardHeader>
          <CardContent className="text-yellow-700">
            <ol className="list-decimal list-inside space-y-2">
              <li>Enter a valid customer ID above (from your database)</li>
              <li>Test setting a renter status/tagline</li>
              <li>Try adding and removing issue category tags</li>
              <li>Create a new issue with different severity levels</li>
              <li>Mark issues as resolved with notes</li>
              <li>Verify the behavior summary updates correctly</li>
            </ol>
          </CardContent>
        </Card>

        {/* Database Schema Info */}
        <Card className="bg-gray-50">
          <CardHeader>
            <CardTitle>Database Schema Required</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-3">
              Make sure you've run the database migration first:
            </p>
            <code className="block bg-gray-800 text-green-400 p-3 rounded text-xs">
              -- Execute: database/renter-issue-tracking-schema.sql
            </code>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Test interface component
function TestRenterIssueInterface({
  customerId,
  currentAdminId,
}: {
  customerId: string;
  currentAdminId: string;
}) {
  const {
    status,
    categoryTags,
    issues,
    behaviorSummary,
    availableCategories,
    loading,
    error,
    updateStatus,
    addCategoryTag,
    removeCategoryTag,
    addIssue,
    updateIssue,
    removeIssue,
  } = useRenterIssueData(customerId, currentAdminId);

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-6">
          <div className="text-red-700">
            <h3 className="font-bold mb-2">Error Loading Data</h3>
            <p className="text-sm">{error}</p>
            <p className="text-xs mt-2">
              This might be expected if the database schema hasn't been applied
              yet.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Renter Issue Tracking Interface</CardTitle>
        <p className="text-sm text-gray-600">Customer ID: {customerId}</p>
      </CardHeader>
      <CardContent>
        <RenterIssueTracking
          customerId={customerId}
          customerName={`Test Customer (${customerId.slice(0, 8)}...)`}
          status={status}
          categoryTags={categoryTags}
          issues={issues}
          behaviorSummary={behaviorSummary || undefined}
          availableCategories={availableCategories}
          currentAdminId={currentAdminId}
          onStatusUpdate={updateStatus}
          onAddCategoryTag={addCategoryTag}
          onRemoveCategoryTag={removeCategoryTag}
          onAddIssue={addIssue}
          onUpdateIssue={updateIssue}
          onDeleteIssue={removeIssue}
        />
      </CardContent>
    </Card>
  );
}
