import { createOpenAI } from '@ai-sdk/openai';

// OpenAI configuration for PathLink chatbot - Direct API usage for cost efficiency
export const openai = createOpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Cost-effective OpenAI model configuration
export const CHAT_MODEL = 'gpt-4o-mini'; // Cheapest GPT-4 class model: $0.15/1M input, $0.6/1M output

// Alternative cheaper option if you want even lower costs
// export const CHAT_MODEL = 'gpt-3.5-turbo'; // Even cheaper: $0.5/1M input, $1.5/1M output

// Model settings optimized for cost and performance
export const MODEL_SETTINGS = {
  model: CHAT_MODEL,
  temperature: 0.7,
  maxTokens: 300, // Reduced to save costs while maintaining quality
  frequencyPenalty: 0.3,
  presencePenalty: 0.3,
};

// Rate limiting configuration
export const RATE_LIMIT = {
  requests: 20, // requests per minute
  tokens: 10000, // tokens per minute
};
