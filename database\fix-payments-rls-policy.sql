-- Fix RLS policy for payments table to allow customer inserts
-- This allows customers to create payment records when submitting bookings

BEGIN;

-- Drop existing restrictive policy if it exists
DROP POLICY IF EXISTS "Users can insert payments for their own bookings" ON public.payments;

-- Create new policy that allows customers to insert payment records
CREATE POLICY "Users can insert payments for their own bookings" 
  ON public.payments FOR INSERT 
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.bookings 
      WHERE bookings.id = booking_id 
      AND bookings.customer_id = (SELECT auth.uid())
    )
  );

-- Ensure customers can view their own payment records
DROP POLICY IF EXISTS "Users can view payments for their own bookings" ON public.payments;

CREATE POLICY "Users can view payments for their own bookings" 
  ON public.payments FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.bookings 
      WHERE bookings.id = booking_id 
      AND bookings.customer_id = (SELECT auth.uid())
    ) OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- Allow admins to update payment records (for verification)
DROP POLICY IF EXISTS "<PERSON><PERSON> can update payment records" ON public.payments;

CREATE POLICY "Admins can update payment records" 
  ON public.payments FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

COMMIT;
