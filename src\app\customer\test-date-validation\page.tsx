"use client";

import React, { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DatePicker } from "@/components/customer-side/date";
import { TimePicker } from "@/components/customer-side/time";
import { 
  calculateDayOnlyRental, 
  formatRentalDuration,
  validateBookingDateTime,
  getMinimumDate,
  getMinimumDropoffDate,
  calculateDayBasedCost,
  generateRentalDisplayText
} from "@/utils/booking-date-validation";
import { Calendar, Clock, CheckCircle, AlertCircle, Car } from "lucide-react";

export default function TestDateValidationPage() {
  const [pickupDate, setPickupDate] = useState("");
  const [pickupTime, setPickupTime] = useState("08:00");
  const [dropoffDate, setDropoffDate] = useState("");
  const [dropoffTime, setDropoffTime] = useState("19:00");
  const [dailyRate] = useState(2500); // Example daily rate

  // Calculate rental details
  const rental = calculateDayOnlyRental(pickupDate, dropoffDate);
  const validation = validateBookingDateTime(pickupDate, pickupTime, dropoffDate, dropoffTime);
  const cost = calculateDayBasedCost(pickupDate, dropoffDate, dailyRate);
  const displayText = generateRentalDisplayText(pickupDate, dropoffDate);

  // Test scenarios for automated validation
  const testScenarios = [
    {
      name: "Same Day Rental",
      pickup: "2024-12-15",
      dropoff: "2024-12-15",
      expected: "1 day"
    },
    {
      name: "3 Day Rental",
      pickup: "2024-12-15",
      dropoff: "2024-12-17",
      expected: "3 days"
    },
    {
      name: "Invalid: Past Date",
      pickup: "2024-11-01",
      dropoff: "2024-11-02",
      expected: "Error"
    },
    {
      name: "Invalid: Dropoff Before Pickup",
      pickup: "2024-12-20",
      dropoff: "2024-12-18",
      expected: "Error"
    }
  ];

  const runTestScenario = (scenario: typeof testScenarios[0]) => {
    const testRental = calculateDayOnlyRental(scenario.pickup, scenario.dropoff);
    const testValidation = validateBookingDateTime(scenario.pickup, "08:00", scenario.dropoff, "19:00");
    
    return {
      ...scenario,
      result: testRental.isValid ? formatRentalDuration(testRental.days) : "Error",
      passed: testRental.isValid ? 
        formatRentalDuration(testRental.days) === scenario.expected :
        scenario.expected === "Error"
    };
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Day-Only Rental Validation Test
          </h1>
          <p className="text-gray-600">
            Test the new day-only rental enforcement system
          </p>
        </div>

        {/* Interactive Test Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Car className="h-5 w-5" />
              Interactive Rental Calculator
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Date and Time Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-green-600" />
                  Pickup Details
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Pickup Date
                    </label>
                    <DatePicker
                      value={pickupDate}
                      onChange={setPickupDate}
                      placeholder="Select pickup date"
                      minDate={getMinimumDate()}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Pickup Time
                      <span className="block text-xs text-gray-500 font-normal">
                        (Scheduling only - doesn't affect rental fee)
                      </span>
                    </label>
                    <TimePicker
                      value={pickupTime}
                      onChange={setPickupTime}
                      placeholder="Select pickup time"
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-orange-600" />
                  Dropoff Details
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Dropoff Date
                    </label>
                    <DatePicker
                      value={dropoffDate}
                      onChange={setDropoffDate}
                      placeholder="Select dropoff date"
                      minDate={getMinimumDropoffDate(pickupDate)}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Dropoff Time
                      <span className="block text-xs text-gray-500 font-normal">
                        (Scheduling only - doesn't affect rental fee)
                      </span>
                    </label>
                    <TimePicker
                      value={dropoffTime}
                      onChange={setDropoffTime}
                      placeholder="Select dropoff time"
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Results Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-6 border-t">
              {/* Validation Results */}
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900">Validation Results</h3>
                <div className="space-y-3">
                  <div className={`flex items-center gap-2 p-3 rounded-lg ${
                    validation.isValid ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                  }`}>
                    {validation.isValid ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <span className="text-sm font-medium">
                      {validation.isValid ? 'Valid booking dates' : validation.error}
                    </span>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Rental Duration:</span>
                      <span className="font-medium">
                        {rental.isValid ? formatRentalDuration(rental.days) : 'Invalid'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Display Text:</span>
                      <span className="font-medium">{displayText}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Cost Calculation */}
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900">Cost Calculation</h3>
                <div className="bg-blue-50 rounded-lg p-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Daily Rate:</span>
                    <span>₱{dailyRate.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Rental Days:</span>
                    <span>{cost.isValid ? cost.days : 0}</span>
                  </div>
                  <div className="border-t border-blue-200 pt-2">
                    <div className="flex justify-between font-semibold">
                      <span>Total Cost:</span>
                      <span className="text-blue-600">
                        ₱{cost.isValid ? cost.totalCost.toFixed(2) : '0.00'}
                      </span>
                    </div>
                  </div>
                  {!cost.isValid && cost.error && (
                    <p className="text-xs text-red-600 mt-1">{cost.error}</p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Automated Test Results */}
        <Card>
          <CardHeader>
            <CardTitle>Automated Test Scenarios</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {testScenarios.map((scenario, index) => {
                const result = runTestScenario(scenario);
                return (
                  <div
                    key={index}
                    className={`flex items-center justify-between p-3 rounded-lg border ${
                      result.passed
                        ? 'bg-green-50 border-green-200'
                        : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div>
                      <span className="font-medium">{result.name}</span>
                      <div className="text-sm text-gray-600">
                        {result.pickup} → {result.dropoff}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-2">
                        {result.passed ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-red-600" />
                        )}
                        <span className="text-sm">
                          Expected: {result.expected} | Got: {result.result}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Key Features Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Day-Only Rental Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">✅ Implemented Features</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Day-only rental calculation (ignores time for duration)</li>
                  <li>• Minimum 1-day rental period</li>
                  <li>• Proper date validation (dropoff ≥ pickup)</li>
                  <li>• Time picker for scheduling only</li>
                  <li>• Clear user messaging about day-only billing</li>
                  <li>• Responsive design maintained</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">🎯 Business Rules</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Rentals charged per day only</li>
                  <li>• Same day = 1 day rental</li>
                  <li>• Time used for pickup/delivery scheduling</li>
                  <li>• No partial day or hourly billing</li>
                  <li>• Clear separation of time vs. billing duration</li>
                  <li>• Admin side remains unchanged</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={() => window.open('/', '_blank')}
            className="flex items-center gap-2"
          >
            <Calendar className="h-4 w-4" />
            Test Homepage Quick Booking
          </Button>
          <Button
            onClick={() => window.open('/customer/catalog', '_blank')}
            variant="secondary"
            className="flex items-center gap-2"
          >
            <Car className="h-4 w-4" />
            Test Booking Flow
          </Button>
        </div>
      </div>
    </div>
  );
}
