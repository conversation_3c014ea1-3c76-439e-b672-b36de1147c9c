"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useAdminAuth } from "@/components/auth/admin-auth-context";
import { useRouter, useSearchParams } from "next/navigation";
import { Eye, EyeOff, ArrowLeft, Check, X } from "lucide-react";
import Image from "next/image";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";
import { CustomerLoadingButton } from "@/components/customer-side/loading/enhanced-loading-components";

export function DesktopAuthPage() {
  const { signIn, signOut, loading, user, isAdmin, profile } = useAdminAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  
  // Form states
  const [email, setEmail] = React.useState("");
  const [password, setPassword] = React.useState("");
  const [showPassword, setShowPassword] = React.useState(false);
  const [rememberMe, setRememberMe] = React.useState(false);
  const [error, setError] = React.useState("");
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Redirect already authenticated users
  React.useEffect(() => {
    if (!loading && user && profile) {
      if (isAdmin) {
        router.replace("/admin");
      } else {
        // Non-admin users should be signed out and can stay on admin login
        signOut();
      }
    }
  }, [user, loading, router, signOut, isAdmin, profile]);

  // Check for error message in URL params
  React.useEffect(() => {
    const errorParam = searchParams.get("error");
    const messageParam = searchParams.get("message");

    if (errorParam) {
      setError(decodeURIComponent(errorParam));
    }

    if (messageParam) {
      toast({
        title: "Account Status",
        description: decodeURIComponent(messageParam),
      });
    }
  }, [searchParams, toast]);

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (isSubmitting) return;

    setError("");
    setIsSubmitting(true);

    // Add small delay to prevent rapid successive requests
    await new Promise(resolve => setTimeout(resolve, 500));

    try {
      const { error } = await signIn(email, password);

      if (error) {
        setError(error.message || "Login failed");
        toast({
          variant: "destructive",
          title: "Admin Login Failed",
          description: error.message || "Login failed",
          className: "bg-white border-red-200",
          action: <X className="h-5 w-5 text-red-500" />,
        });
        return;
      }

      // Success toast for admin login
      toast({
        title: "Login Successful!",
        description: "Welcome to the admin portal",
        className: "bg-white border-green-200",
        action: <Check className="h-5 w-5 text-green-500" />,
      });

      // Check for redirect parameter
      const redirectTo = searchParams.get("redirect");
      if (redirectTo && redirectTo.startsWith("/admin")) {
        // Only allow admin routes for admin login
        router.replace(redirectTo);
      } else {
        // Default redirect to admin dashboard
        router.replace("/admin");
      }
    } catch (err) {
      const errorMessage = "An unexpected error occurred. Please try again.";
      setError(errorMessage);
      toast({
        variant: "destructive",
        title: "Admin Login Failed",
        description: errorMessage,
        className: "bg-white border-red-200",
        action: <X className="h-5 w-5 text-red-500" />,
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen grid place-items-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-sm text-muted-foreground mt-2">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen grid grid-cols-1 md:grid-cols-2">
      {/* Left Panel - Background Image with Text Overlay */}
      <div className="relative hidden md:flex overflow-hidden bg-muted items-center justify-center">
        <div className="relative w-full h-full max-w-2xl max-h-screen">
          <Image
            src="/ollie_cover_photo.jpg"
            alt="Ollie's Rent A Car"
            fill
            className="object-contain"
            priority
          />
        </div>
        {/* Text Overlay - Bottom Left */}
        <div className="absolute bottom-0 left-0 p-12 text-white bg-gradient-to-t from-black/60 to-transparent w-full">
          <h1 className="text-2xl font-bold mb-2">
            Admin Portal
          </h1>
          <p className="text-base font-normal opacity-90">
            Manage your fleet, bookings, and customer data
          </p>
        </div>
      </div>

      {/* Right Panel - Form */}
      <div className="flex flex-col justify-center items-center p-6 md:p-12 bg-background">
        {/* Back to Homepage Button - Top Right */}
        <div className="absolute top-4 right-4">
          <Link href="/">
            <Button
              variant="secondary"
              size="sm"
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4" />
              Go to Customer Side
            </Button>
          </Link>
        </div>

        {/* Logo - Centered */}
        <div className="w-full max-w-md mb-8">
          <div className="flex justify-center mb-6">
            <Link href="/" className="cursor-pointer">
              <div className="relative w-40 h-16">
                <Image
                  src="/ollie_logo1.jpg"
                  alt="Ollie's Logo"
                  fill
                  className="object-contain hover:opacity-80 transition-opacity"
                  priority
                />
              </div>
            </Link>
          </div>

          {/* Welcome Text - Centered */}
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold mb-2">
              Admin Portal
            </h2>
            <p className="text-base text-muted-foreground">
              Sign in to access the admin dashboard
            </p>
          </div>

          {/* Form */}
          <form onSubmit={onSubmit} className="space-y-4">
            {/* Error Message */}
            {error && (
              <div className="p-3 text-sm text-red-500 bg-red-50 border border-red-200 rounded-md">
                {error}
              </div>
            )}

            {/* Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                Admin Email
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your admin email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="h-12 px-4 rounded-full border border-border"
                disabled={isSubmitting}
              />
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium">
                Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="h-12 px-4 pr-12 rounded-full border border-border"
                  disabled={isSubmitting}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                  aria-label={showPassword ? "Hide password" : "Show password"}
                  disabled={isSubmitting}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
            </div>

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={rememberMe}
                  onCheckedChange={(checked) => setRememberMe(!!checked)}
                  className="h-4 w-4"
                />
                <Label htmlFor="remember" className="text-sm">
                  Remember Me
                </Label>
              </div>
              <Link
                href="/admin-forgot-password"
                className="text-sm text-[#4285f4] hover:text-[#3367d6] hover:underline"
              >
                Forgot Password?
              </Link>
            </div>

            {/* Submit Button */}
            <CustomerLoadingButton
              type="submit"
              isLoading={isSubmitting}
              loadingText="Signing In..."
              className="w-full h-12 rounded-full bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md transition-all duration-200 font-medium"
            >
              Sign In
            </CustomerLoadingButton>
          </form>

          {/* Footer Link */}
          <div className="text-center mt-6">
            <p className="text-sm text-muted-foreground">
              Need assistance?{" "}
              <Link
                href="/contact"
                className="text-blue-600 hover:text-blue-700 hover:underline font-medium transition-colors duration-200"
              >
                Contact IT Support
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
