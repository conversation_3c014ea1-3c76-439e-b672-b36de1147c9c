"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { createBrowserClient } from '@supabase/ssr'
import type { User, Session, AuthError } from '@supabase/supabase-js'
import { IsolatedStorageAdapter } from '../../lib/supabase/storage-adapter'
import { AuthCookieManager } from '../../lib/auth/cookie-manager'
import '../../lib/auth/performance-optimizer' // Import performance optimizations
import { logWithContext } from '../../lib/utils/logger'

type UserProfile = {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: 'customer' | 'admin' | 'super_admin'
  avatar_url: string | null
  created_at: string
  updated_at: string
}

type CustomerAuthContextType = {
  user: User | null
  session: Session | null
  profile: UserProfile | null
  loading: boolean
  isCustomer: boolean
  fetchUserProfile: () => Promise<void>
  signUp: (email: string, password: string, options?: { role?: 'customer' | 'admin'; full_name?: string; phone?: string; first_name?: string; middle_initial?: string; last_name?: string }) => Promise<{ error: AuthError | null }>
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<{ error: AuthError | null }>
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>
  verifyOTP: (email: string, token: string, type: 'signup' | 'recovery') => Promise<{ error: AuthError | null }>
  resendOTP: (email: string, type: 'signup' | 'recovery') => Promise<{ error: AuthError | null }>
}

const CustomerAuthContext = createContext<CustomerAuthContextType | undefined>(undefined)

export { CustomerAuthContext }

// Create a separate Supabase client instance for customers with a different storage key
let customerSupabaseInstance: ReturnType<typeof createBrowserClient> | null = null

function createCustomerClient() {
  if (customerSupabaseInstance) {
    return customerSupabaseInstance
  }
  
  // Create isolated storage adapter for customer auth
  const storageAdapter = new IsolatedStorageAdapter('sb-customer-auth-token')
  
  // Ensure we have a clean client with proper storage key
  customerSupabaseInstance = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false, // Disable for better performance
        flowType: 'pkce',
        storageKey: 'sb-customer-auth-token', // Separate storage key for customers
        storage: storageAdapter,
        debug: false // Disable debug logs for better performance
      }
    }
  )
  
  return customerSupabaseInstance
}

export function CustomerAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createCustomerClient()

  // Initialize cookie manager for customer context
  const cookieManager = React.useMemo(() => {
    if (typeof window !== 'undefined') {
      return AuthCookieManager.getInstance('customer')
    }
    return null
  }, [])

  // Debug: Log storage key being used
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development' && cookieManager) {
      // Run diagnostics less frequently for better performance
      const shouldRunDiagnostics = Math.random() < 0.2; // 20% chance
      
      if (shouldRunDiagnostics) {
        logWithContext('CustomerAuth', '🔑 Using storage key:', 'sb-customer-auth-token')
        logWithContext('CustomerAuth', '🔍 Available localStorage keys:', Object.keys(localStorage).filter(k => k.includes('sb-')))
        
        // Run diagnostics
        const diagnostics = cookieManager.getDiagnostics()
        logWithContext('CustomerAuth', '🔍 Diagnostics:', diagnostics)
        
        // Prevent cross-contamination
        cookieManager.preventCrossContamination()
      }
    }
  }, [cookieManager])

  const isCustomer = profile?.role === 'customer'

  // Add a timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      console.warn('Customer auth loading timeout - forcing loading to false')
      setLoading(false)
    }, 5000) // 5 second timeout

    return () => clearTimeout(timeout)
  }, [])

  // Profile cache to avoid redundant API calls
  const profileCache = React.useRef<Map<string, UserProfile | null>>(new Map())
  
  // Optimized profile fetching with caching and fallback
  const fetchProfile = React.useCallback(async (userId: string, userEmail: string): Promise<UserProfile | null> => {
    // Check cache first
    const cacheKey = `${userId}-${userEmail}`
    if (profileCache.current.has(cacheKey)) {
      return profileCache.current.get(cacheKey) || null
    }

    // Create fallback profile immediately to avoid loading delays
    const fallbackProfile = {
      id: userId,
      email: userEmail,
      full_name: '',
      phone: null,
      role: 'customer' as const,
      avatar_url: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        // Cache and return fallback profile
        profileCache.current.set(cacheKey, fallbackProfile)
        return fallbackProfile
      }

      const profile = data as UserProfile
      profileCache.current.set(cacheKey, profile)
      return profile
    } catch (error) {
      // Cache and return fallback profile on error
      profileCache.current.set(cacheKey, fallbackProfile)
      return fallbackProfile
    }
  }, [])

  useEffect(() => {
    let isMounted = true;
    
    // Get initial session
    const getSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (!isMounted) return;
        
        if (error) {
          if (process.env.NODE_ENV === 'development') {
            console.warn('Customer session error:', error.message)
          }
          setSession(null)
          setUser(null)
          setProfile(null)
        } else if (session) {
          // Quick validation - defer heavy operations
          if (cookieManager && !cookieManager.validateSession(session)) {
            if (process.env.NODE_ENV === 'development') {
              logWithContext('CustomerAuth', 'Invalid session detected in customer context, clearing session')
            }
            if (cookieManager) {
              cookieManager.clearAuthData()
            }
            await supabase.auth.signOut()
            if (isMounted) {
              setSession(null)
              setUser(null)
              setProfile(null)
            }
            return
          }

          // Set session and user immediately for faster UI response
          if (isMounted) {
            setSession(session)
            setUser(session.user)
            
            // Set immediate fallback profile to prevent loading delays
            const immediateProfile = {
              id: session.user.id,
              email: session.user.email || '',
              full_name: session.user.user_metadata?.full_name || '',
              phone: null,
              role: 'customer' as const,
              avatar_url: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
            setProfile(immediateProfile)
            setLoading(false)
          }

          // Sync initial session to server-side cookies for server actions with customer context
          try {
            await fetch('/api/auth/callback', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ session, context: 'customer' }),
            });
          } catch (error) {
            if (process.env.NODE_ENV === 'development') {
              console.warn('Failed to sync initial customer session to server:', error);
            }
          }

          // Fetch actual profile asynchronously in background
          fetchProfile(session.user.id, session.user.email || '').then(userProfile => {
            if (!isMounted) return;
            
            // Update with actual profile data if different from fallback
            if (userProfile && JSON.stringify(userProfile) !== JSON.stringify(profile)) {
              setProfile(userProfile)
            }
          }).catch(error => {
            if (process.env.NODE_ENV === 'development') {
              console.error('Failed to fetch customer profile:', error)
            }
            // Keep the fallback profile - no need to update
          })
        } else {
          if (isMounted) {
            setSession(null)
            setUser(null)
            setProfile(null)
            setLoading(false)
          }
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Customer session fetch error:', error)
        }
        if (isMounted) {
          setSession(null)
          setUser(null)
          setProfile(null)
          setLoading(false)
        }
      }
    }

    getSession()

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event: any, session: any) => {
      if (!isMounted) return;
      
      // Reduced logging for better performance
      if (process.env.NODE_ENV === 'development') {
        logWithContext('CustomerAuth', 'Auth state change:', { event, email: session?.user?.email })
      }

      // Sync session to server-side cookies for server actions with customer context
      if (session && (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED')) {
        try {
          await fetch('/api/auth/callback', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ session, context: 'customer' }),
          });
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.warn('Failed to sync customer session to server:', error);
          }
        }
      }

      try {
        if (event === 'SIGNED_OUT' || (!session && event !== 'INITIAL_SESSION')) {
          if (isMounted) {
            setSession(null)
            setUser(null)
            setProfile(null)
            setLoading(false)
          }
          return
        }

        // Handle any session with a user
        if (session?.user && (event === 'INITIAL_SESSION' || event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED')) {
          // Set session/user immediately for faster response
          if (isMounted) {
            setSession(session)
            setUser(session.user)
          }

          // Only fetch profile if we don't have one or if it's a new user
          if (!profile || profile.id !== session.user.id) {
            // Set immediate fallback profile
            const immediateProfile = {
              id: session.user.id,
              email: session.user.email || '',
              full_name: session.user.user_metadata?.full_name || '',
              phone: null,
              role: 'customer' as const,
              avatar_url: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
            
            if (isMounted) {
              setProfile(immediateProfile)
            }
            
            // Fetch actual profile in background
            fetchProfile(session.user.id, session.user.email || '').then(userProfile => {
              if (!isMounted) return;
              
              // Reject admin users in customer auth context
              if (userProfile && (userProfile.role === 'admin' || userProfile.role === 'super_admin')) {
                if (process.env.NODE_ENV === 'development') {
                  logWithContext('CustomerAuth', 'Admin user detected in customer auth context, signing out')
                }
                supabase.auth.signOut()
                setSession(null)
                setUser(null)
                setProfile(null)
                return
              }
              
              // Update with actual profile if different
              if (userProfile && JSON.stringify(userProfile) !== JSON.stringify(immediateProfile)) {
                setProfile(userProfile)
              }
            }).catch(error => {
              if (process.env.NODE_ENV === 'development') {
                console.error('Error fetching customer profile:', error)
              }
              // Keep fallback profile
            })
          }
        } else {
          // Handle case where there's no session but it's not a sign out
          if (isMounted) {
            setSession(null)
            setUser(null)
            setProfile(null)
            setLoading(false)
          }
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error in customer auth state change handler:', error)
        }
      }
    })

    return () => {
      isMounted = false;
      subscription.unsubscribe()
      // Clean up cookie manager to prevent memory leaks
      if (cookieManager) {
        cookieManager.clearCacheTimeouts()
      }
    }
  }, []) // Remove cookieManager dependency to prevent re-runs

  const signUp = async (email: string, password: string, options?: { role?: 'customer' | 'admin'; full_name?: string; phone?: string; first_name?: string; middle_initial?: string; last_name?: string }) => {
    try {
      const { data, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            role: options?.role || 'customer',
            full_name: options?.full_name || '',
            phone: options?.phone || '',
            first_name: options?.first_name || '',
            middle_initial: options?.middle_initial || '',
            last_name: options?.last_name || ''
          }
        }
      })

      if (signUpError) {
        // Handle specific error cases with user-friendly messages
        if (signUpError.status === 429 || signUpError.message.includes('Too Many Requests') || signUpError.message.includes('rate limit')) {
          return { error: { ...signUpError, message: 'Too many registration attempts. Please wait a few minutes before trying again.' } }
        }
        
        if (signUpError.message.includes('email') && signUpError.message.includes('already')) {
          return { error: { ...signUpError, message: 'An account with this email already exists. Please try logging in instead.' } }
        }
        
        if (signUpError.message.includes('confirmation') || signUpError.message.includes('email')) {
          return { error: { ...signUpError, message: 'There was an issue sending the confirmation email. Please check your email address and try again. If the problem persists, contact support.' } }
        }

        return { error: signUpError }
      }

      // Check if user was created but needs email confirmation
      if (data.user && !data.user.email_confirmed_at && data.user.confirmation_sent_at) {
        logWithContext('CustomerAuth', 'Registration successful, confirmation email sent')
      }

      return { error: null }
    } catch (error) {
      console.error('Signup error:', error)
      return { error: { message: 'An unexpected error occurred during registration. Please try again.' } as any }
    }
  }

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { error }
  }

  const signOut = async () => {
    // Clear cookie manager data first
    if (cookieManager) {
      cookieManager.clearAuthData()
    }
    
    const { error } = await supabase.auth.signOut()
    
    // Clear local state
    setUser(null)
    setSession(null)
    setProfile(null)
    
    return { error }
  }

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    })
    return { error }
  }

  const verifyOTP = async (email: string, token: string, type: 'signup' | 'recovery') => {
    const { error } = await supabase.auth.verifyOtp({
      email,
      token,
      type,
    })
    return { error }
  }

  const resendOTP = async (email: string, type: 'signup' | 'recovery') => {
    if (type === 'signup') {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email,
      })
      return { error }
    } else {
      const { error } = await supabase.auth.resetPasswordForEmail(email)
      return { error }
    }
  }

  const fetchUserProfile = React.useCallback(async () => {
    if (user) {
      const userProfile = await fetchProfile(user.id, user.email || '')
      if (userProfile) {
        setProfile(userProfile)
      }
    }
  }, [user, fetchProfile])

  const contextValue: CustomerAuthContextType = {
    user,
    session,
    profile,
    loading,
    isCustomer,
    fetchUserProfile,
    signUp,
    signIn,
    signOut,
    resetPassword,
    verifyOTP,
    resendOTP,
  }

  return <CustomerAuthContext.Provider value={contextValue}>{children}</CustomerAuthContext.Provider>
}

export function useCustomerAuth() {
  const context = useContext(CustomerAuthContext)
  if (context === undefined) {
    throw new Error('useCustomerAuth must be used within a CustomerAuthProvider')
  }
  return context
}
