-- Non-destructive security fixes for Supabase warnings and errors
-- This script only updates security settings without dropping/recreating functions

-- ========================================
-- 1. FIX RLS ERROR - Enable RLS on vehicle_categories (SAFE)
-- ========================================

-- Enable RLS on vehicle_categories table (if not already enabled)
DO $$ 
BEGIN
  IF NOT (SELECT rowsecurity FROM pg_tables WHERE tablename = 'vehicle_categories' AND schemaname = 'public') THEN
    ALTER TABLE public.vehicle_categories ENABLE ROW LEVEL SECURITY;
  END IF;
END $$;

-- Create policies only if they don't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'vehicle_categories' 
    AND policyname = 'Allow public read access to vehicle categories'
  ) THEN
    CREATE POLICY "Allow public read access to vehicle categories" ON public.vehicle_categories
    FOR SELECT 
    USING (is_active = true);
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'vehicle_categories' 
    AND policyname = 'Allow admin full access to vehicle categories'
  ) THEN
    CREATE POLICY "Allow admin full access to vehicle categories" ON public.vehicle_categories
    FOR ALL 
    USING (
      EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
      )
    );
  END IF;
END $$;

-- Grant necessary permissions (safe to re-run)
GRANT SELECT ON public.vehicle_categories TO anon, authenticated;
GRANT ALL ON public.vehicle_categories TO authenticated;

-- ========================================
-- 2. FIX FUNCTION SEARCH_PATH WARNINGS (NON-DESTRUCTIVE)
-- ========================================

-- Update existing functions with security settings using ALTER FUNCTION
-- This only adds the security settings without changing function logic

-- 1. get_vehicle_categories_with_stats
ALTER FUNCTION public.get_vehicle_categories_with_stats() SET search_path = public;
ALTER FUNCTION public.get_vehicle_categories_with_stats() SECURITY DEFINER;

-- 2. get_cars_by_category  
ALTER FUNCTION public.get_cars_by_category(text) SET search_path = public;
ALTER FUNCTION public.get_cars_by_category(text) SECURITY DEFINER;

-- 3. update_category_pricing
ALTER FUNCTION public.update_category_pricing(uuid, numeric, numeric) SET search_path = public;
ALTER FUNCTION public.update_category_pricing(uuid, numeric, numeric) SECURITY DEFINER;

-- 4. check_booking_documents_complete
ALTER FUNCTION public.check_booking_documents_complete(uuid) SET search_path = public;
ALTER FUNCTION public.check_booking_documents_complete(uuid) SECURITY DEFINER;

-- 5. get_booking_document_status
ALTER FUNCTION public.get_booking_document_status(uuid) SET search_path = public;
ALTER FUNCTION public.get_booking_document_status(uuid) SECURITY DEFINER;

-- 6. get_archived_cars
ALTER FUNCTION public.get_archived_cars() SET search_path = public;
ALTER FUNCTION public.get_archived_cars() SECURITY DEFINER;

-- 7. can_archive_car
ALTER FUNCTION public.can_archive_car(uuid) SET search_path = public;
ALTER FUNCTION public.can_archive_car(uuid) SECURITY DEFINER;

-- 8. car_has_archive_history
ALTER FUNCTION public.car_has_archive_history(uuid) SET search_path = public;
ALTER FUNCTION public.car_has_archive_history(uuid) SECURITY DEFINER;

-- 9. get_car_archive_history
ALTER FUNCTION public.get_car_archive_history(uuid) SET search_path = public;
ALTER FUNCTION public.get_car_archive_history(uuid) SECURITY DEFINER;

-- 10. sync_car_archive_status
ALTER FUNCTION public.sync_car_archive_status(uuid, boolean) SET search_path = public;
ALTER FUNCTION public.sync_car_archive_status(uuid, boolean) SECURITY DEFINER;

-- 11. handle_new_user
ALTER FUNCTION public.handle_new_user() SET search_path = public;
ALTER FUNCTION public.handle_new_user() SECURITY DEFINER;

-- 12. handle_updated_at
ALTER FUNCTION public.handle_updated_at() SET search_path = public;
ALTER FUNCTION public.handle_updated_at() SECURITY DEFINER;

-- ========================================
-- VERIFICATION QUERIES (SAFE TO RUN)
-- ========================================

-- Verify RLS is enabled
SELECT 
  schemaname, 
  tablename, 
  rowsecurity,
  CASE WHEN rowsecurity THEN '✅ ENABLED' ELSE '❌ DISABLED' END as status
FROM pg_tables 
WHERE tablename = 'vehicle_categories';

-- Verify function security settings
SELECT 
  p.proname as function_name,
  p.prosecdef as security_definer,
  p.proconfig as search_path_config,
  CASE WHEN p.prosecdef THEN '✅ SECURE' ELSE '❌ INSECURE' END as security_status,
  CASE WHEN 'search_path=public' = ANY(p.proconfig) THEN '✅ FIXED' ELSE '❌ MUTABLE' END as search_path_status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
  AND p.proname IN (
    'get_vehicle_categories_with_stats',
    'get_cars_by_category', 
    'update_category_pricing',
    'check_booking_documents_complete',
    'get_booking_document_status',
    'get_archived_cars',
    'can_archive_car',
    'car_has_archive_history',
    'get_car_archive_history',
    'sync_car_archive_status',
    'handle_new_user',
    'handle_updated_at'
  )
ORDER BY p.proname;
