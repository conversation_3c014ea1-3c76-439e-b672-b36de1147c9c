'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Send, X, Minimize2, Maximize2, Shield } from 'lucide-react';
import { cn } from '@/lib/utils';
import { MessageFormatter } from '@/components/ui/message-formatter';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  createdAt?: Date;
}

export function AdminChatbot() {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const sessionId = useRef(`admin-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);

  const sendMessage = async (content: string) => {
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content,
      createdAt: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      if (process.env.NODE_ENV === 'development' || typeof window !== 'undefined' && window.location.hostname === 'localhost') {
        console.log('Admin sending message:', content);
      }
      const currentMessages = [...messages, userMessage];
      
      const response = await fetch('/api/chat/admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: currentMessages,
          sessionId: sessionId.current,
        }),
      });

      if (process.env.NODE_ENV === 'development' || typeof window !== 'undefined' && window.location.hostname === 'localhost') {
        console.log('Admin response status:', response.status);
        console.log('Admin response headers:', response.headers);
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Try reading as text first to debug
      const responseText = await response.text();
      if (process.env.NODE_ENV === 'development' || typeof window !== 'undefined' && window.location.hostname === 'localhost') {
        console.log('Admin raw response:', responseText);
      }

      if (responseText) {
        const assistantMessage: Message = {
          id: `assistant-${Date.now()}`,
          role: 'assistant',
          content: responseText,
          createdAt: new Date(),
        };
        setMessages(prev => [...prev, assistantMessage]);
      }
    } catch (error) {
      console.error('Admin chat error:', error);
      setMessages(prev => [...prev, {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        createdAt: new Date(),
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 md:right-20 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          className="h-12 w-12 md:h-14 md:w-14 rounded-full bg-orange-600 hover:bg-orange-700 shadow-lg relative"
          size="icon"
        >
          <MessageCircle className="h-5 w-5 md:h-6 md:w-6 text-white" />
          <Shield className="h-2.5 w-2.5 md:h-3 md:w-3 text-white absolute -top-1 -right-1" />
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 md:right-20 z-50 w-[calc(100vw-2rem)] max-w-[28rem] md:w-[28rem]">
      <Card className={cn(
        "w-full h-[calc(100vh-6rem)] max-h-[36rem] md:h-[36rem] shadow-xl border bg-white",
        isMinimized && "h-12 md:h-14"
      )}>
        <CardHeader className="p-2 md:p-3 bg-orange-600 text-white rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1 md:gap-2 min-w-0">
              <Shield className="h-3 w-3 md:h-4 md:w-4 flex-shrink-0" />
              <CardTitle className="text-xs md:text-sm font-medium truncate">
                Admin Assistant
              </CardTitle>
              <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800 hidden sm:inline-flex">
                Admin
              </Badge>
            </div>
            <div className="flex items-center gap-1 flex-shrink-0">
              <Button
                variant="tertiary"
                size="icon"
                className="h-5 w-5 md:h-6 md:w-6 text-white hover:bg-orange-700"
                onClick={() => setIsMinimized(!isMinimized)}
              >
                {isMinimized ? (
                  <Maximize2 className="h-3 w-3 md:h-4 md:w-4" />
                ) : (
                  <Minimize2 className="h-3 w-3 md:h-4 md:w-4" />
                )}
              </Button>
              <Button
                variant="tertiary"
                size="icon"
                className="h-5 w-5 md:h-6 md:w-6 text-white hover:bg-orange-700"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-3 w-3 md:h-4 md:w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="p-0 flex flex-col h-[calc(100vh-10rem)] max-h-[32rem] md:h-[32rem]">
            <ScrollArea className="flex-1 p-2 md:p-3 h-full overflow-y-auto">
              <div className="space-y-2 md:space-y-3">
                {messages.length === 0 && (
                  <div className="text-xs md:text-sm text-gray-600 bg-orange-50 border border-orange-200 rounded-lg p-2 md:p-3">
                    <div className="flex items-start gap-2">
                      <Shield className="h-3 w-3 md:h-4 md:w-4 text-orange-600 mt-0.5 flex-shrink-0" />
                      <div className="text-xs text-orange-700 leading-relaxed">
                        Ask me about booking management, customer issues, system operations, 
                        or administrative procedures.
                      </div>
                    </div>
                  </div>
                )}
                
                {messages.map((message: any, index: number) => (
                  <div
                    key={message.id || index}
                    className={cn(
                      "flex",
                      message.role === 'user' ? 'justify-end' : 'justify-start'
                    )}
                  >
                    <div
                      className={cn(
                        "max-w-[90%] sm:max-w-[85%] min-w-0 rounded-lg px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm break-words overflow-wrap-anywhere",
                        message.role === 'user'
                          ? 'bg-orange-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      )}
                      style={{
                        wordWrap: 'break-word',
                        overflowWrap: 'anywhere',
                        hyphens: 'auto'
                      }}
                    >
                      {message.role === 'assistant' ? (
                        <MessageFormatter content={message.content} />
                      ) : (
                        <div className="whitespace-pre-wrap leading-relaxed">
                          {message.content}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                
                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-gray-50 border rounded-lg px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm">
                      <div className="flex items-center gap-2 mb-1">
                        <Shield className="h-2.5 w-2.5 md:h-3 md:w-3 text-orange-600" />
                        <span className="text-xs text-orange-600 font-medium hidden sm:inline">Admin Assistant</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-orange-400 rounded-full animate-bounce" />
                        <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-orange-400 rounded-full animate-bounce delay-100" />
                        <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-orange-400 rounded-full animate-bounce delay-200" />
                      </div>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            <form onSubmit={async (e) => {
              e.preventDefault();
              if (input.trim() && !isLoading) {
                await sendMessage(input);
                setInput('');
              }
            }} className="p-2 md:p-3 border-t bg-gray-50">
              <div className="flex gap-1.5 md:gap-2">
                <Input
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Ask about admin operations..."
                  className="flex-1 text-xs md:text-sm h-8 md:h-9"
                  disabled={isLoading}
                />
                <Button
                  type="submit"
                  size="icon"
                  className="h-8 w-8 md:h-9 md:w-9 bg-orange-600 hover:bg-orange-700 flex-shrink-0"
                  disabled={isLoading || !input.trim()}
                >
                  <Send className="h-3 w-3 md:h-4 md:w-4" />
                </Button>
              </div>
              <div className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                <Shield className="h-2.5 w-2.5 md:h-3 md:w-3" />
                <span className="hidden sm:inline">Secure admin assistant</span>
                <span className="sm:hidden">Secure</span>
              </div>
            </form>
          </CardContent>
        )}
      </Card>
    </div>
  );
}
