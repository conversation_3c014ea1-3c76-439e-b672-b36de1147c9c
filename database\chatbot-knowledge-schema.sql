-- PathLink Chatbot Knowledge Base Schema
-- Creates tables for storing embeddings and knowledge for RAG system
-- Aligned with existing PathLink database structure

-- Enable vector extension for embeddings
CREATE EXTENSION IF NOT EXISTS vector;

-- Knowledge base table for storing embedded content
CREATE TABLE IF NOT EXISTS chatbot_knowledge (
    id UUID NOT NULL DEFAULT extensions.uuid_generate_v4(),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    embedding vector(1536), -- OpenAI embedding dimensions
    metadata JSONB DEFAULT '{}',
    source TEXT, -- 'customer_faq', 'admin_docs', 'booking_info', etc.
    category TEXT, -- 'general', 'booking', 'payment', 'support', etc.
    created_at TIMESTAMP WITH TIME ZONE NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NULL DEFAULT NOW(),
    CONSTRAINT chatbot_knowledge_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

-- Chat conversations table
CREATE TABLE IF NOT EXISTS chatbot_conversations (
    id UUID NOT NULL DEFAULT extensions.uuid_generate_v4(),
    user_id UUID NOT NULL,
    user_type TEXT NOT NULL,
    session_id TEXT NOT NULL,
    title TEXT NULL,
    created_at TIMESTAMP WITH TIME ZONE NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NULL DEFAULT NOW(),
    CONSTRAINT chatbot_conversations_pkey PRIMARY KEY (id),
    CONSTRAINT chatbot_conversations_session_id_key UNIQUE (session_id),
    CONSTRAINT chatbot_conversations_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
    CONSTRAINT chatbot_conversations_user_type_check CHECK (user_type = ANY (ARRAY['customer'::text, 'admin'::text]))
) TABLESPACE pg_default;

-- Chat messages table
CREATE TABLE IF NOT EXISTS chatbot_messages (
    id UUID NOT NULL DEFAULT extensions.uuid_generate_v4(),
    conversation_id UUID NOT NULL,
    role TEXT NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE NULL DEFAULT NOW(),
    CONSTRAINT chatbot_messages_pkey PRIMARY KEY (id),
    CONSTRAINT chatbot_messages_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES chatbot_conversations(id) ON DELETE CASCADE,
    CONSTRAINT chatbot_messages_role_check CHECK (role = ANY (ARRAY['user'::text, 'assistant'::text, 'system'::text]))
) TABLESPACE pg_default;

-- Triggers for updated_at
CREATE TRIGGER handle_updated_at_chatbot_knowledge BEFORE UPDATE ON chatbot_knowledge FOR EACH ROW EXECUTE FUNCTION handle_updated_at();
CREATE TRIGGER handle_updated_at_chatbot_conversations BEFORE UPDATE ON chatbot_conversations FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_chatbot_knowledge_embedding ON chatbot_knowledge USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_chatbot_knowledge_source ON chatbot_knowledge USING btree (source) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_chatbot_knowledge_category ON chatbot_knowledge USING btree (category) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_chatbot_conversations_user ON chatbot_conversations USING btree (user_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_chatbot_conversations_session ON chatbot_conversations USING btree (session_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_chatbot_messages_conversation ON chatbot_messages USING btree (conversation_id) TABLESPACE pg_default;

-- RLS policies
ALTER TABLE chatbot_knowledge ENABLE ROW LEVEL SECURITY;
ALTER TABLE chatbot_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE chatbot_messages ENABLE ROW LEVEL SECURITY;

-- Knowledge base is readable by authenticated users
CREATE POLICY "chatbot_knowledge_read" ON chatbot_knowledge
    FOR SELECT TO authenticated
    USING (true);

-- Conversations are only accessible by the owner
CREATE POLICY "chatbot_conversations_read" ON chatbot_conversations
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "chatbot_conversations_insert" ON chatbot_conversations
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "chatbot_conversations_update" ON chatbot_conversations
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id);

-- Messages are only accessible through their conversation's owner
CREATE POLICY "chatbot_messages_read" ON chatbot_messages
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM chatbot_conversations 
            WHERE id = conversation_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "chatbot_messages_insert" ON chatbot_messages
    FOR INSERT TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM chatbot_conversations 
            WHERE id = conversation_id AND user_id = auth.uid()
        )
    );

-- Function to search knowledge base using vector similarity
CREATE OR REPLACE FUNCTION search_knowledge(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 5,
    filter_source text DEFAULT NULL,
    filter_category text DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    similarity float,
    source TEXT,
    category TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ck.id,
        ck.title,
        ck.content,
        1 - (ck.embedding <=> query_embedding) AS similarity,
        ck.source,
        ck.category
    FROM chatbot_knowledge ck
    WHERE (filter_source IS NULL OR ck.source = filter_source)
    AND (filter_category IS NULL OR ck.category = filter_category)
    AND 1 - (ck.embedding <=> query_embedding) > match_threshold
    ORDER BY ck.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;
