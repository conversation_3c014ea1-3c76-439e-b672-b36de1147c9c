-- Verify and ensure payments table exists with proper structure for PathLink
-- This addresses payment status tracking issues in sales-tracking and payments admin pages

-- Check if payments table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'payments') THEN
        -- Create payments table if it doesn't exist
        CREATE TABLE public.payments (
            id uuid NOT NULL DEFAULT extensions.uuid_generate_v4(),
            booking_id uuid NOT NULL,
            amount numeric(10,2) NOT NULL,
            method text NOT NULL DEFAULT 'GCash'::text,
            status text NOT NULL DEFAULT 'Pending Verification'::text,
            transaction_date timestamp with time zone NULL DEFAULT now(),
            proof_of_payment_url text NULL,
            reference_number text NULL,
            verification_notes text NULL,
            verified_by uuid NULL,
            verified_at timestamp with time zone NULL,
            created_at timestamp with time zone NULL DEFAULT now(),
            updated_at timestamp with time zone NULL DEFAULT now(),
            CONSTRAINT payments_pkey PRIMARY KEY (id),
            CONSTRAINT payments_booking_id_fkey FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
            CONSTRAINT payments_verified_by_fkey FOREIGN KEY (verified_by) REFERENCES profiles(id),
            CONSTRAINT payments_method_check CHECK (method = ANY (ARRAY[
                'GCash'::text,
                'Bank Transfer'::text,
                'Remittance Center'::text,
                'Cash'::text,
                'Credit Card'::text
            ])),
            CONSTRAINT payments_status_check CHECK (status = ANY (ARRAY[
                'Pending Verification'::text,
                'Paid'::text,
                'Rejected'::text,
                'Refunded'::text,
                'Failed'::text
            ]))
        ) TABLESPACE pg_default;
        
        -- Create indexes
        CREATE INDEX IF NOT EXISTS idx_payments_booking_id ON public.payments USING btree (booking_id) TABLESPACE pg_default;
        CREATE INDEX IF NOT EXISTS idx_payments_status ON public.payments USING btree (status) TABLESPACE pg_default;
        CREATE INDEX IF NOT EXISTS idx_payments_created ON public.payments USING btree (created_at) TABLESPACE pg_default;
        CREATE INDEX IF NOT EXISTS idx_payments_transaction_date ON public.payments USING btree (transaction_date) TABLESPACE pg_default;
        
        -- Create trigger for updated_at
        CREATE TRIGGER handle_updated_at_payments 
            BEFORE UPDATE ON payments 
            FOR EACH ROW 
            EXECUTE FUNCTION handle_updated_at();
        
        RAISE NOTICE 'Created payments table with proper structure';
    ELSE
        RAISE NOTICE 'Payments table already exists';
    END IF;
END
$$;

-- Ensure all active bookings have a payment record
INSERT INTO payments (booking_id, amount, method, status, transaction_date, created_at, updated_at)
SELECT 
    b.id,
    b.total_amount,
    'GCash'::text,
    CASE 
        WHEN b.status = 'Active' THEN 'Paid'::text
        WHEN b.status = 'Cancelled' THEN 'Refunded'::text
        ELSE 'Pending Verification'::text
    END,
    b.created_at,
    b.created_at,
    b.updated_at
FROM bookings b
WHERE NOT EXISTS (
    SELECT 1 FROM payments p WHERE p.booking_id = b.id
)
AND b.total_amount > 0;

-- Update payment status based on booking status for consistency
UPDATE payments 
SET 
    status = CASE 
        WHEN b.status = 'Active' THEN 'Paid'::text
        WHEN b.status = 'Cancelled' THEN 'Refunded'::text
        WHEN b.status = 'Completed' THEN 'Paid'::text
        ELSE payments.status
    END,
    updated_at = now()
FROM bookings b 
WHERE payments.booking_id = b.id
AND payments.status != CASE 
    WHEN b.status = 'Active' THEN 'Paid'::text
    WHEN b.status = 'Cancelled' THEN 'Refunded'::text
    WHEN b.status = 'Completed' THEN 'Paid'::text
    ELSE payments.status
END;
