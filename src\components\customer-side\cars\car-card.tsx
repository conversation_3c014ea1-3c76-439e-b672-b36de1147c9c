"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardAction,
} from "@/components/ui/card";
import { CarIcon, Users, Fuel, Cog } from "lucide-react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import type { Car } from "@/lib/types";
import { CUSTOMER_PATHS, buildBookingFlowUrl } from "@/lib/customer-paths";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { AuthRequiredModal } from "@/components/customer-side/auth/auth-required-modal";
import * as React from "react";
import { checkCarAvailability } from "@/app/customer/booking/actions/booking-actions";

interface CarCardProps {
  car: Car;
  showSimplified?: boolean;
  onSelect?: () => void;
  isSelected?: boolean;
  showAsGroup?: boolean; // New prop to indicate if this is for category grouping
  checkAvailability?: boolean; // New prop to enable availability checking
}

export function CarCard({
  car,
  showSimplified = false,
  onSelect,
  isSelected = false,
  showAsGroup = false,
  checkAvailability = true,
}: CarCardProps) {
  const router = useRouter();
  const { user } = useCustomerAuth();
  const [showAuthModal, setShowAuthModal] = React.useState(false);
  const [isAvailable, setIsAvailable] = React.useState(true);
  const [availabilityReason, setAvailabilityReason] = React.useState("");

  // Check car availability on mount if enabled
  React.useEffect(() => {
    if (checkAvailability && car.id) {
      // First check car status directly
      if (car.status !== "Available" || car.condition === "Needs Repair") {
        setIsAvailable(false);
        if (car.status === "Rented") {
          setAvailabilityReason("Currently rented");
        } else if (car.status === "In Maintenance" || car.condition === "Needs Repair") {
          setAvailabilityReason("Under maintenance");
        } else {
          setAvailabilityReason(`Status: ${car.status}`);
        }
        return;
      }

      // Check availability for next 7 days by default for booking conflicts
      const now = new Date();
      const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      
      checkCarAvailability(car.id.toString(), now, nextWeek).then((result) => {
        setIsAvailable(result.available);
        if (!result.available && result.reason) {
          setAvailabilityReason(result.reason);
        }
      }).catch(() => {
        // If availability check fails, assume available to not block bookings
        setIsAvailable(true);
      });
    }
  }, [car.id, car.status, car.condition, checkAvailability]);

  const handleRentNow = () => {
    if (onSelect) {
      onSelect();
    } else {
      // Check if user is authenticated
      if (!user) {
        setShowAuthModal(true);
        return;
      }

      // Check if car is available
      if (!isAvailable) {
        return; // Don't navigate if car is not available
      }
      
      // Navigate to correct customer booking flow with carId
      const bookingUrl = buildBookingFlowUrl({ carId: car.id.toString() });
      router.push(bookingUrl);
    }
  };

  if (showSimplified) {
    return (
      <Card
        variant={isSelected ? "outlined" : "default"}
        className={`cursor-pointer overflow-hidden min-w-0 ${
          isSelected ? "ring-2 ring-purple-500" : ""
        }`}
        onClick={onSelect}
      >
        <CardContent className="min-w-0">
          <div className="flex items-center justify-between min-w-0">
            <div className="min-w-0 flex-1">
              <CardTitle className="text-sm truncate">{car.model}</CardTitle>
              <CardDescription className="mt-1">
                <span className="border border-gray-300 px-2 py-1 rounded text-xs">
                  {car.transmission}
                </span>
              </CardDescription>
            </div>
            <div className="text-right flex-shrink-0 ml-2">
              <div className="text-lg font-bold text-purple-600">
                ₱{car.price_per_day.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500">per day</div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      variant="elevated"
      className="overflow-hidden group flex flex-col h-full w-full max-w-sm mx-auto car-card"
    >
      <div className="relative h-48 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden flex items-center justify-center">
        <Image
          src={car.image_url || "/placeholder.svg"}
          alt={car.model}
          fill
          className="object-contain transition-transform duration-300 group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>

      <CardContent className="text-center flex-1 flex flex-col justify-between card-body p-6">
        {/* Category Badge */}
        <div className="flex items-center justify-center mb-4">
          <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
            <CarIcon className="h-6 w-6 text-white" />
          </div>
        </div>

        {/* Car Model or Type */}
        <div className="mb-4">
          <CardTitle className="text-xl font-bold mb-2 tracking-wide text-gray-900 line-clamp-2 break-words text-center">
            {showAsGroup ? car.type.toUpperCase() : car.model.toUpperCase()}
          </CardTitle>
        </div>

        {/* Price */}
        <div className="mb-4 flex-1 flex flex-col justify-center card-content-area">
          <div className="text-gray-600 text-sm mb-1">From</div>
          <div className="text-2xl font-bold text-gray-900 mb-1 break-words">
            ₱ {car.price_per_day.toLocaleString()}
          </div>
          <div className="text-gray-500 text-xs">per day</div>
        </div>

        {/* Transmission Info */}
        <div className="mb-6 flex flex-col items-center justify-center">
          <CardDescription className="text-sm text-gray-500 line-clamp-2 break-words text-center">
            {showAsGroup
              ? `${car.transmission} Transmission`
              : `${car.type} - ${car.transmission} Transmission`}
          </CardDescription>
        </div>

        {/* Book Now Button or Status */}
        <div className="card-footer flex items-center justify-center">
          {!isAvailable ? (
            <div className="w-full text-center">
              <div className="bg-red-50 border-2 border-red-200 text-red-700 font-semibold py-3 rounded-full text-sm tracking-wide min-h-[44px] flex items-center justify-center">
                <span className="truncate">
                  {car.status === "Rented" ? "RENTED" : 
                   car.status === "In Maintenance" ? "MAINTENANCE" : 
                   "NOT AVAILABLE"}
                </span>
              </div>
              {availabilityReason && (
                <p className="text-xs text-red-600 mt-1 truncate">{availabilityReason}</p>
              )}
            </div>
          ) : (
            <Button
              onClick={handleRentNow}
              className="w-full bg-white border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-semibold py-3 rounded-full transition-all duration-200 text-sm tracking-wide min-h-[44px]"
              variant="secondary"
            >
              <span className="truncate">BOOK NOW</span>
            </Button>
          )}
        </div>
      </CardContent>

      {/* Authentication Modal */}
      <AuthRequiredModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        action="book this car"
        carModel={car.model}
      />
    </Card>
  );
}
