import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type')
  const next = searchParams.get('next') ?? '/dashboard'

  // Create redirect link without the secret token
  const redirectTo = new URL(request.url)
  redirectTo.pathname = next
  redirectTo.searchParams.delete('token_hash')
  redirectTo.searchParams.delete('type')

  if (token_hash && type) {
    const supabase = await createClient()

    const { error } = await supabase.auth.verifyOtp({
      type: type as any,
      token_hash,
    })

    if (!error) {
      redirectTo.searchParams.delete('next')
      return NextResponse.redirect(redirectTo)
    }
  }

  // return the user to an error page with instructions
  redirectTo.pathname = '/auth/error'
  return NextResponse.redirect(redirectTo)
}

// Sync client-side auth state to server cookies for SSR/server actions
export async function POST(request: Request) {
  const supabase = await createClient()
  const { event, session } = await request.json()

  try {
    if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED') {
      // Persist the session into HTTP-only cookies
      const access_token = session?.access_token
      const refresh_token = session?.refresh_token
      if (access_token && refresh_token) {
        await supabase.auth.setSession({ access_token, refresh_token })
      }
    }
    if (event === 'SIGNED_OUT') {
      await supabase.auth.signOut()
    }
  } catch (e) {
    // Best-effort; do not fail the client flow
    console.warn('Auth callback POST error:', e)
  }

  return NextResponse.json({ ok: true })
}
