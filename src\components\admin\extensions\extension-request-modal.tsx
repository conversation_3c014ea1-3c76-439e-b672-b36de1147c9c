"use client";

import React from "react";
import { format, differenceInHours } from "date-fns";
import { 
  <PERSON><PERSON>, 
  Dialog<PERSON>ontent, 
  Di<PERSON>Header, 
  Di<PERSON>T<PERSON>le, 
  DialogFooter 
} from "@/components/ui/dialog";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import {
  Clock,
  Calendar,
  User,
  Car,
  MapPin,
  Phone,
  Mail,
  DollarSign,
  FileText,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Timer,
  Info
} from "lucide-react";
import { cn } from "@/lib/utils";
import { ExtensionRequestWithDetails } from "@/lib/services/admin-extension-service";

interface ExtensionRequestModalProps {
  request: ExtensionRequestWithDetails | null;
  isOpen: boolean;
  onClose: () => void;
  onApprove: (requestId: string, adminNotes?: string) => Promise<void>;
  onReject: (requestId: string, rejectionReason: string, adminNotes?: string) => Promise<void>;
}

export function ExtensionRequestModal({
  request,
  isOpen,
  onClose,
  onApprove,
  onReject
}: ExtensionRequestModalProps) {
  const { toast } = useToast();
  const [currentAction, setCurrentAction] = React.useState<'approve' | 'reject' | null>(null);
  const [adminNotes, setAdminNotes] = React.useState("");
  const [rejectionReason, setRejectionReason] = React.useState("");
  const [loading, setLoading] = React.useState(false);

  // Reset form when modal opens/closes
  React.useEffect(() => {
    if (isOpen) {
      setCurrentAction(null);
      setAdminNotes("");
      setRejectionReason("");
    }
  }, [isOpen]);

  if (!request) return null;

  const formatDateTime = (dateString: string) => {
    try {
      return format(new Date(dateString), "EEEE, MMMM d, yyyy 'at' h:mm a");
    } catch {
      return dateString;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', { 
      style: 'currency', 
      currency: 'PHP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { 
        variant: "secondary" as const, 
        icon: <Timer className="h-4 w-4" />,
        className: "bg-yellow-100 text-yellow-800 border-yellow-200"
      },
      approved: { 
        variant: "default" as const, 
        icon: <CheckCircle className="h-4 w-4" />,
        className: "bg-green-100 text-green-800 border-green-200"
      },
      rejected: { 
        variant: "destructive" as const, 
        icon: <XCircle className="h-4 w-4" />,
        className: "bg-red-100 text-red-800 border-red-200"
      },
      expired: { 
        variant: "secondary" as const, 
        icon: <AlertTriangle className="h-4 w-4" />,
        className: "bg-gray-100 text-gray-800 border-gray-200"
      }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <Badge variant={config.variant} className={cn("flex items-center gap-1 capitalize", config.className)}>
        {config.icon}
        {status}
      </Badge>
    );
  };

  const handleApprove = async () => {
    if (!request) return;
    
    setLoading(true);
    try {
      await onApprove(request.id, adminNotes || undefined);
      toast({
        title: "Extension Approved",
        description: "The extension request has been approved successfully.",
      });
      onClose();
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Approval Failed",
        description: "Failed to approve the extension request. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    if (!request || !rejectionReason.trim()) {
      toast({
        variant: "destructive",
        title: "Rejection Reason Required",
        description: "Please provide a reason for rejecting this request.",
      });
      return;
    }
    
    setLoading(true);
    try {
      await onReject(request.id, rejectionReason, adminNotes || undefined);
      toast({
        title: "Extension Rejected",
        description: "The extension request has been rejected.",
      });
      onClose();
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Rejection Failed",
        description: "Failed to reject the extension request. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  };

  const getUrgencyLevel = () => {
    const now = new Date();
    const expiresAt = new Date(request.expires_at);
    const hoursUntilExpiry = (expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (request.status === 'pending' && hoursUntilExpiry <= 2) {
      return { level: "urgent", color: "text-red-600 bg-red-50 border-red-200", text: "Expires in less than 2 hours" };
    } else if (request.status === 'pending' && hoursUntilExpiry <= 6) {
      return { level: "medium", color: "text-yellow-600 bg-yellow-50 border-yellow-200", text: "Expires in less than 6 hours" };
    } else if (request.status === 'pending') {
      return { level: "normal", color: "text-blue-600 bg-blue-50 border-blue-200", text: "Normal priority" };
    }
    return null;
  };

  const urgency = getUrgencyLevel();

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="w-[95vw] xs:w-[90vw] sm:w-[85vw] lg:max-w-4xl max-h-[95vh] xs:max-h-[90vh] overflow-y-auto [&>button]:hidden p-3 xs:p-4 sm:p-6">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Extension Request Details</span>
            {getStatusBadge(request.status)}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 xs:space-y-6">
          {/* Urgency Alert */}
          {urgency && (
            <Card className={cn("border-2", urgency.color)}>
              <CardContent className="py-2 xs:py-3">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 xs:h-5 xs:w-5" />
                  <span className="font-medium text-sm xs:text-base">{urgency.text}</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Customer & Booking Info */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 xs:gap-4 lg:gap-6">
            <Card>
              <CardHeader className="pb-2 xs:pb-3">
                <CardTitle className="flex items-center gap-2 text-base xs:text-lg">
                  <User className="h-4 w-4 xs:h-5 xs:w-5" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 xs:space-y-3">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500 flex-shrink-0" />
                  <span className="font-medium text-sm xs:text-base truncate">{request.customers?.full_name || "Unknown"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500 flex-shrink-0" />
                  <span className="text-xs xs:text-sm text-gray-600 truncate">{request.customers?.email || "No email"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-gray-500 flex-shrink-0" />
                  <span className="text-xs xs:text-sm font-mono">#{request.bookings?.booking_ref || "N/A"}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2 xs:pb-3">
                <CardTitle className="flex items-center gap-2 text-base xs:text-lg">
                  <Car className="h-4 w-4 xs:h-5 xs:w-5" />
                  Vehicle Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 xs:space-y-3">
                <div className="flex items-center gap-2">
                  <Car className="h-4 w-4 text-gray-500 flex-shrink-0" />
                  <span className="font-medium text-sm xs:text-base truncate">{request.bookings?.cars?.model || "Unknown Vehicle"}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-xs bg-gray-100 px-2 py-1 rounded font-mono">
                    {request.bookings?.cars?.plate_number || "No Plate"}
                  </span>
                </div>
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-gray-500 flex-shrink-0 mt-0.5" />
                  <div className="text-xs xs:text-sm text-gray-600">
                    <div className="truncate">Pickup: {request.bookings?.pickup_location}</div>
                    <div className="truncate">Dropoff: {request.bookings?.dropoff_location}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Extension Details */}
          <Card>
            <CardHeader className="pb-2 xs:pb-3">
              <CardTitle className="flex items-center gap-2 text-base xs:text-lg">
                <Clock className="h-4 w-4 xs:h-5 xs:w-5" />
                Extension Request Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 xs:space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 xs:gap-4 sm:gap-6">
                <div className="space-y-2 xs:space-y-3">
                  <div>
                    <Label className="text-xs xs:text-sm text-gray-600">Original Return Time</Label>
                    <div className="font-medium text-sm xs:text-base break-words">{formatDateTime(request.original_dropoff_datetime)}</div>
                  </div>
                  <div>
                    <Label className="text-xs xs:text-sm text-gray-600">Requested Return Time</Label>
                    <div className="font-medium text-sm xs:text-base text-green-700 break-words">{formatDateTime(request.requested_dropoff_datetime)}</div>
                  </div>
                </div>
                <div className="space-y-2 xs:space-y-3">
                  <div>
                    <Label className="text-xs xs:text-sm text-gray-600">Extension Duration</Label>
                    <div className="font-medium text-sm xs:text-base">+{request.extension_duration_hours} hours</div>
                  </div>
                  <div>
                    <Label className="text-xs xs:text-sm text-gray-600">Additional Cost</Label>
                    <div className="font-medium text-base xs:text-lg text-green-600">{formatCurrency(request.additional_amount)}</div>
                  </div>
                </div>
              </div>

              {request.request_reason && (
                <div>
                  <Label className="text-xs xs:text-sm text-gray-600">Reason for Extension</Label>
                  <div className="bg-gray-50 rounded-lg p-2 xs:p-3 mt-1">
                    <p className="text-xs xs:text-sm break-words">{request.request_reason}</p>
                  </div>
                </div>
              )}

              {request.request_notes && (
                <div>
                  <Label className="text-xs xs:text-sm text-gray-600">Additional Notes</Label>
                  <div className="bg-gray-50 rounded-lg p-2 xs:p-3 mt-1">
                    <p className="text-xs xs:text-sm break-words">{request.request_notes}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Conflicts & Alternatives */}
          {request.has_conflicts && (
            <Card className="border-orange-200 bg-orange-50">
              <CardHeader className="pb-2 xs:pb-3">
                <CardTitle className="flex items-center gap-2 text-orange-800 text-sm xs:text-base">
                  <AlertTriangle className="h-4 w-4 xs:h-5 xs:w-5" />
                  Scheduling Conflicts Detected
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xs xs:text-sm text-orange-700 mb-2 xs:mb-3">
                  This extension request conflicts with other bookings. Alternative vehicles are available.
                </p>
                {request.alternative_cars && request.alternative_cars.length > 0 && (
                  <div>
                    <Label className="text-xs xs:text-sm text-orange-800">Alternative Vehicles</Label>
                    <div className="flex flex-wrap gap-1 xs:gap-2 mt-1">
                      {request.alternative_cars.map((carId, index) => (
                        <Badge key={index} variant="secondary" className="bg-orange-100 text-orange-800 text-xs">
                          Car ID: {carId}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Admin Action Form */}
          {request.status === 'pending' && (
            <Card>
              <CardHeader className="pb-2 xs:pb-3">
                <CardTitle className="flex items-center gap-2 text-base xs:text-lg">
                  <CheckCircle className="h-4 w-4 xs:h-5 xs:w-5" />
                  Admin Action Required
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 xs:space-y-4">
                {!currentAction ? (
                  <div className="flex flex-col xs:flex-row gap-2 xs:gap-3">
                    <Button
                      onClick={() => setCurrentAction('approve')}
                      className="bg-green-600 hover:bg-green-700 min-h-[48px] xs:min-h-[44px] text-sm xs:text-base"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve Extension
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => setCurrentAction('reject')}
                      className="min-h-[48px] xs:min-h-[44px] text-sm xs:text-base"
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject Extension
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3 xs:space-y-4">
                    {currentAction === 'reject' && (
                      <div>
                        <Label htmlFor="rejection-reason" className="text-xs xs:text-sm">Rejection Reason *</Label>
                        <Textarea
                          id="rejection-reason"
                          placeholder="Please provide a clear reason for rejecting this extension request..."
                          value={rejectionReason}
                          onChange={(e) => setRejectionReason(e.target.value)}
                          className="mt-1 text-xs xs:text-sm min-h-[80px] xs:min-h-[60px]"
                          rows={3}
                        />
                      </div>
                    )}
                    
                    <div>
                      <Label htmlFor="admin-notes" className="text-xs xs:text-sm">Admin Notes (Optional)</Label>
                      <Textarea
                        id="admin-notes"
                        placeholder="Add any internal notes about this decision..."
                        value={adminNotes}
                        onChange={(e) => setAdminNotes(e.target.value)}
                        className="mt-1 text-xs xs:text-sm min-h-[60px] xs:min-h-[50px]"
                        rows={2}
                      />
                    </div>

                    <div className="flex flex-col xs:flex-row gap-2 xs:gap-3">
                      <Button
                        onClick={currentAction === 'approve' ? handleApprove : handleReject}
                        disabled={loading || (currentAction === 'reject' && !rejectionReason.trim())}
                        className={cn(
                          "min-h-[48px] xs:min-h-[44px] text-sm xs:text-base",
                          currentAction === 'approve' && "bg-green-600 hover:bg-green-700"
                        )}
                        variant={currentAction === 'reject' ? "destructive" : "primary"}
                      >
                        {loading ? (
                          <Timer className="h-4 w-4 mr-2 animate-spin" />
                        ) : currentAction === 'approve' ? (
                          <CheckCircle className="h-4 w-4 mr-2" />
                        ) : (
                          <XCircle className="h-4 w-4 mr-2" />
                        )}
                        {loading ? "Processing..." : `${currentAction === 'approve' ? 'Approve' : 'Reject'} Request`}
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={() => setCurrentAction(null)}
                        disabled={loading}
                        className="min-h-[48px] xs:min-h-[44px] text-sm xs:text-base"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Review History */}
          {(request.reviewed_at || request.admin_notes || request.rejection_reason) && (
            <Card>
              <CardHeader className="pb-2 xs:pb-3">
                <CardTitle className="flex items-center gap-2 text-base xs:text-lg">
                  <Info className="h-4 w-4 xs:h-5 xs:w-5" />
                  Review History
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 xs:space-y-3">
                {request.reviewed_at && (
                  <div>
                    <Label className="text-xs xs:text-sm text-gray-600">Reviewed At</Label>
                    <div className="text-xs xs:text-sm break-words">{formatDateTime(request.reviewed_at)}</div>
                  </div>
                )}
                {request.rejection_reason && (
                  <div>
                    <Label className="text-xs xs:text-sm text-gray-600">Rejection Reason</Label>
                    <div className="bg-red-50 rounded-lg p-2 xs:p-3 mt-1">
                      <p className="text-xs xs:text-sm text-red-800 break-words">{request.rejection_reason}</p>
                    </div>
                  </div>
                )}
                {request.admin_notes && (
                  <div>
                    <Label className="text-xs xs:text-sm text-gray-600">Admin Notes</Label>
                    <div className="bg-blue-50 rounded-lg p-2 xs:p-3 mt-1">
                      <p className="text-xs xs:text-sm text-blue-800 break-words">{request.admin_notes}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="pt-3 xs:pt-4">
          <Button 
            variant="secondary" 
            onClick={onClose}
            className="w-full xs:w-auto min-h-[48px] xs:min-h-[44px] text-sm xs:text-base"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
