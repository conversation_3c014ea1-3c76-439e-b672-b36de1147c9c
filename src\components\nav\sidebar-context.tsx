"use client"

import * as React from "react"

type SidebarContextValue = {
  isCollapsed: boolean
  toggle: () => void
  setCollapsed: (v: boolean) => void
}

const SidebarContext = React.createContext<SidebarContextValue | null>(null)

const STORAGE_KEY = "sidebar:collapsed"

export function SidebarProvider({ children }: { children: React.ReactNode }) {
  const [isCollapsed, setIsCollapsed] = React.useState(false)

  React.useEffect(() => {
    try {
      const raw = localStorage.getItem(STORAGE_KEY)
      if (raw != null) setIsCollapsed(raw === "true")
    } catch {}
  }, [])

  React.useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, String(isCollapsed))
    } catch {}
  }, [isCollapsed])

  const value = React.useMemo<SidebarContextValue>(
    () => ({
      isCollapsed,
      toggle: () => setIsCollapsed((v) => !v),
      setCollapsed: (v: boolean) => setIsCollapsed(v),
    }),
    [isCollapsed]
  )

  return <SidebarContext.Provider value={value}>{children}</SidebarContext.Provider>
}

export function useSidebar() {
  const ctx = React.useContext(SidebarContext)
  if (!ctx) throw new Error("useSidebar must be used within SidebarProvider")
  return ctx
}


