# Authentication Cookie Testing Suite

This comprehensive test suite is designed to identify and diagnose authentication cookie inconsistencies between admin and customer accounts, particularly focusing on issues that occur when tabs are closed and reopened.

## Test Structure

### 1. Cookie Isolation Tests (`cookie-isolation.test.tsx`)
- **Purpose**: Tests storage key separation between admin and customer contexts
- **Key Scenarios**:
  - Separate storage keys verification
  - Independent localStorage entries
  - Tab close/reopen without cross-contamination
  - Prevention of customer sessions in admin context
  - Prevention of admin sessions in customer context
  - Simultaneous signout handling
  - Malformed session data handling

### 2. Session Persistence Tests (`session-persistence.test.tsx`)
- **Purpose**: Tests session restoration across browser sessions and tab management
- **Key Scenarios**:
  - Customer session restoration on page reload
  - Admin session restoration on page reload
  - Concurrent sessions without interference
  - Cross-context role validation
  - Session expiration handling
  - Session data clearing on signout
  - Multiple tab simulation

### 3. Role Validation Tests (`role-validation.test.tsx`)
- **Purpose**: Tests role-based authentication validation and prevents cross-context contamination
- **Key Scenarios**:
  - Admin user rejection in customer context
  - Customer user rejection in admin context
  - Legitimate customer user authorization
  - Legitimate admin user authorization
  - Special admin email handling
  - Missing profile data handling
  - Concurrent role validation across contexts

### 4. Browser Integration Tests (`browser-integration.test.tsx`)
- **Purpose**: Simulates real browser behavior and complex scenarios
- **Key Scenarios**:
  - Complete browser session lifecycle
  - Multiple tabs with same context
  - Cross-context isolation in multiple tabs
  - Signout affecting all tabs
  - Corrupted localStorage data handling
  - Network errors during session restoration
  - Rapid tab switching
  - Browser refresh simulation

### 5. Authentication Diagnostics (`diagnostics.test.tsx`)
- **Purpose**: Comprehensive diagnostics with detailed reporting
- **Key Scenarios**:
  - Cookie isolation verification
  - Tab close/reopen consistency
  - Cross-context contamination check
  - Storage key uniqueness verification
  - Session data integrity check
  - Concurrent session handling

## Running the Tests

### Run All Authentication Tests
```bash
pnpm test:auth
```

### Run Specific Test File
```bash
# Cookie isolation tests
pnpm test __tests__/auth/cookie-isolation-fixed.test.tsx

# Session persistence tests
pnpm test __tests__/auth/session-persistence-fixed.test.tsx

# Role validation tests
pnpm test __tests__/auth/role-validation-fixed.test.tsx

# Browser integration tests
pnpm test __tests__/auth/browser-integration-fixed.test.tsx

# Diagnostic tests with detailed reporting
pnpm test __tests__/auth/diagnostics.test.tsx
```

### Run Tests in Watch Mode
```bash
pnpm test:watch
```

### Run Tests with Coverage
```bash
pnpm test:coverage
```

## Identifying Cookie Inconsistencies

The test suite is specifically designed to catch these common cookie inconsistencies:

### 1. **Storage Key Conflicts**
- Same storage key used for both admin and customer
- Missing or incorrect storage key configuration
- Storage key not being applied consistently

### 2. **Cross-Context Contamination**
- Admin sessions appearing in customer context
- Customer sessions appearing in admin context
- Shared session data between contexts

### 3. **Tab Management Issues**
- Sessions lost when tabs are closed and reopened
- Sessions changing between tabs
- Inconsistent session state across tabs

### 4. **Role Validation Failures**
- Wrong user types being authorized in contexts
- Role validation not being enforced
- Fallback role assignment issues

### 5. **Session Persistence Problems**
- Sessions not persisting across page reloads
- Sessions being corrupted in localStorage
- Sessions not being cleared properly on signout

## Test Output and Diagnostics

### Diagnostic Report Format
The diagnostic tests provide detailed reports including:
- Test name and pass/fail status
- Specific issues identified
- Current cookie state (customer and admin)
- Timestamp of test execution

### Example Output
```
=== AUTHENTICATION COOKIE DIAGNOSTIC REPORT ===

❌ Found 2 issues with authentication cookies:

Test: Cross-context contamination check
Issues:
  - Admin user data found in customer cookie
  - Same access token found in both cookies
Cookie State:
  Customer: {"access_token":"admin-token","user":{"role":"admin"}}
  Admin: {"access_token":"admin-token","user":{"role":"admin"}}
Timestamp: 2025-08-16T10:30:00.000Z

=== END DIAGNOSTIC REPORT ===
```

## Mock Configuration

The tests use comprehensive mocking for:
- Supabase client instances with separate storage keys
- localStorage and sessionStorage
- Next.js router and navigation
- Authentication state changes
- Network requests and errors

## Key Test Patterns

### Storage Key Verification
```typescript
// Verify separate storage keys
expect(customerCall[2]?.auth?.storageKey).toBe('sb-customer-auth-token')
expect(adminCall[2]?.auth?.storageKey).toBe('sb-admin-auth-token')
```

### Session Isolation Testing
```typescript
// Test session isolation
window.localStorage.setItem('sb-customer-auth-token', customerSession)
window.localStorage.setItem('sb-admin-auth-token', adminSession)

expect(customerSession).not.toBe(adminSession)
```

### Tab Close/Reopen Simulation
```typescript
// Simulate tab close
unmount()

// Simulate tab reopen
render(<AuthProvider>...</AuthProvider>)

// Verify session persistence
expect(localStorage.getItem('sb-customer-auth-token')).toBeTruthy()
```

## Troubleshooting Guide

### Common Issues and Solutions

1. **Tests failing due to import paths**
   - Check that import paths in test files match your project structure
   - Update paths from `@/` to relative paths if needed

2. **Supabase client mocking errors**
   - Ensure all Supabase methods used in your components are mocked
   - Add missing mock implementations as needed

3. **localStorage not being cleared between tests**
   - Verify `beforeEach` hooks are clearing storage
   - Check for any async operations that might persist state

4. **Authentication context hook errors**
   - Ensure test components are wrapped in the appropriate providers
   - Verify hook names match your implementation

## Expected Test Results

All tests should pass if your authentication system has proper cookie isolation. Failed tests indicate specific areas where cookie inconsistencies exist and need to be addressed.

The diagnostic test provides the most comprehensive overview and should be your first stop for identifying issues.
