"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from './button'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from './card'
import { Download, Heart, Plus, Settings, Trash2, ExternalLink, ArrowRight, Check } from 'lucide-react'

/**
 * Button Showcase Component
 * 
 * This component demonstrates all button variants, sizes, shapes, and states
 * according to the UI/UX checklist requirements.
 */
export function ButtonShowcase() {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({})

  const handleLoadingDemo = (key: string) => {
    setLoadingStates(prev => ({ ...prev, [key]: true }))
    setTimeout(() => {
      setLoadingStates(prev => ({ ...prev, [key]: false }))
    }, 2000)
  }

  return (
    <div className="space-y-8 p-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Button System</h1>
        <p className="text-gray-600">
          Comprehensive button components following UI/UX best practices with clear copy guidelines,
          proper interaction states, and accessibility features.
        </p>
      </div>

      {/* Base Styles - Fill, Outline, Underline */}
      <Card>
        <CardHeader>
          <CardTitle>Base Styles</CardTitle>
          <p className="text-sm text-gray-600">
            Three fundamental button styles: Fill (primary action), Outline (secondary action), and Underline (link action)
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-4">
            <Button variant="primary">Book Your Rental</Button>
            <Button variant="secondary">View Details</Button>
            <Button variant="link">Learn More</Button>
          </div>
          
          <div className="text-sm text-gray-600">
            <strong>Copy Guidelines:</strong> Use action-oriented, specific text that clearly describes what happens when clicked.
          </div>
        </CardContent>
      </Card>

      {/* Variants */}
      <Card>
        <CardHeader>
          <CardTitle>Button Variants</CardTitle>
          <p className="text-sm text-gray-600">
            Different visual types representing button hierarchy and semantic meaning
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="font-medium mb-3">Primary Actions</h4>
            <div className="flex flex-wrap gap-4">
              <Button variant="primary">Confirm Booking</Button>
              <Button variant="primary" leftIcon={<Plus />}>Add Vehicle</Button>
              <Button variant="primary" rightIcon={<ArrowRight />}>Continue</Button>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Secondary Actions</h4>
            <div className="flex flex-wrap gap-4">
              <Button variant="secondary">Cancel</Button>
              <Button variant="secondary" leftIcon={<Download />}>Download Receipt</Button>
              <Button variant="tertiary">Skip for Now</Button>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Semantic Variants</h4>
            <div className="flex flex-wrap gap-4">
              <Button variant="success" leftIcon={<Check />}>Payment Successful</Button>
              <Button variant="warning">Review Required</Button>
              <Button variant="destructive" leftIcon={<Trash2 />}>Delete Booking</Button>
              <Button variant="destructive-outline">Cancel Reservation</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sizes and Shapes */}
      <Card>
        <CardHeader>
          <CardTitle>Sizes and Shapes</CardTitle>
          <p className="text-sm text-gray-600">
            Visual properties including padding, border radius, and overall dimensions
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="font-medium mb-3">Button Sizes</h4>
            <div className="flex flex-wrap items-end gap-4">
              <Button variant="primary" size="xs">Extra Small</Button>
              <Button variant="primary" size="sm">Small</Button>
              <Button variant="primary" size="default">Default</Button>
              <Button variant="primary" size="lg">Large</Button>
              <Button variant="primary" size="xl">Extra Large</Button>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Button Shapes</h4>
            <div className="flex flex-wrap gap-4">
              <Button variant="primary" shape="default">Default</Button>
              <Button variant="primary" shape="rounded">Rounded</Button>
              <Button variant="primary" shape="pill">Pill Shape</Button>
              <Button variant="primary" shape="square">Square</Button>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Icon Buttons</h4>
            <div className="flex flex-wrap gap-4">
              <Button variant="primary" size="icon-sm" aria-label="Add favorite">
                <Heart />
              </Button>
              <Button variant="secondary" size="icon" aria-label="Settings">
                <Settings />
              </Button>
              <Button variant="tertiary" size="icon-lg" aria-label="External link">
                <ExternalLink />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interactive States */}
      <Card>
        <CardHeader>
          <CardTitle>Interactive States</CardTitle>
          <p className="text-sm text-gray-600">
            How buttons change based on user interaction and system state
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="font-medium mb-3">Default State</h4>
            <div className="flex flex-wrap gap-4">
              <Button variant="primary">Book Now</Button>
              <Button variant="secondary">View Catalog</Button>
              <Button variant="tertiary">Learn More</Button>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Button visible with no interaction - clear, actionable copy
            </p>
          </div>

          <div>
            <h4 className="font-medium mb-3">Hover State</h4>
            <div className="flex flex-wrap gap-4">
              <Button variant="primary" className="hover:bg-blue-700">
                Hover Me
              </Button>
              <p className="text-sm text-gray-600 self-center">
                Enhanced shadow, darker background on hover
              </p>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Active State</h4>
            <div className="flex flex-wrap gap-4">
              <Button variant="primary" className="active:scale-95">
                Click Me
              </Button>
              <p className="text-sm text-gray-600 self-center">
                Slight scale animation and darker color when clicked
              </p>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Focused State</h4>
            <div className="flex flex-wrap gap-4">
              <Button variant="primary" className="focus-visible:ring-2 focus-visible:ring-blue-500">
                Tab to Focus
              </Button>
              <p className="text-sm text-gray-600 self-center">
                Clear focus ring for keyboard navigation
              </p>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Disabled State</h4>
            <div className="flex flex-wrap gap-4">
              <Button variant="primary" disabled>
                Unavailable
              </Button>
              <Button variant="secondary" disabled>
                Out of Stock
              </Button>
              <Button variant="destructive" disabled>
                Cannot Delete
              </Button>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Reduced opacity, no pointer events, clear visual indication
            </p>
          </div>

          <div>
            <h4 className="font-medium mb-3">Loading State</h4>
            <div className="flex flex-wrap gap-4">
              <Button 
                variant="primary" 
                loading={loadingStates.booking}
                loadingText="Processing..."
                onClick={() => handleLoadingDemo('booking')}
              >
                Confirm Booking
              </Button>
              <Button 
                variant="secondary" 
                loading={loadingStates.download}
                onClick={() => handleLoadingDemo('download')}
              >
                Download
              </Button>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Spinner animation with optional loading text
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Copy Guidelines */}
      <Card>
        <CardHeader>
          <CardTitle>Copy Guidelines</CardTitle>
          <p className="text-sm text-gray-600">
            Best practices for button text that clearly communicates the action
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3 text-green-600">✅ Good Examples</h4>
              <div className="space-y-2">
                <Button variant="primary" size="sm">Book Your Rental</Button>
                <Button variant="secondary" size="sm">View Vehicle Details</Button>
                <Button variant="destructive" size="sm">Cancel Reservation</Button>
                <Button variant="success" size="sm">Payment Complete</Button>
              </div>
              <ul className="text-sm text-gray-600 mt-3 space-y-1">
                <li>• Action-oriented verbs</li>
                <li>• Specific outcomes</li>
                <li>• Clear expectations</li>
                <li>• Contextual meaning</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-3 text-red-600">❌ Avoid These</h4>
              <div className="space-y-2">
                <Button variant="secondary" size="sm" disabled>Click Here</Button>
                <Button variant="secondary" size="sm" disabled>Submit</Button>
                <Button variant="secondary" size="sm" disabled>OK</Button>
                <Button variant="secondary" size="sm" disabled>Button</Button>
              </div>
              <ul className="text-sm text-gray-600 mt-3 space-y-1">
                <li>• Generic terms</li>
                <li>• Unclear outcomes</li>
                <li>• No context</li>
                <li>• Vague actions</li>
              </ul>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h5 className="font-medium text-blue-900 mb-2">💡 Pro Tips</h5>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Use specific verbs: "Book", "Download", "Cancel" instead of "Submit", "OK"</li>
              <li>• Include the object: "Book Your Rental" instead of just "Book"</li>
              <li>• Match the user's mental model: "Add to Cart" for e-commerce</li>
              <li>• Consider the context: Generic "OK" is fine in confirmation dialogs</li>
              <li>• Keep it concise but descriptive: 2-4 words maximum</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Accessibility Features */}
      <Card>
        <CardHeader>
          <CardTitle>Accessibility Features</CardTitle>
          <p className="text-sm text-gray-600">
            Built-in accessibility support for all users
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">Keyboard Navigation</h4>
              <div className="space-y-2">
                <Button variant="primary">Tab Navigation</Button>
                <Button variant="secondary">Space/Enter to Activate</Button>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-3">Screen Reader Support</h4>
              <div className="space-y-2">
                <Button variant="primary" aria-label="Add vehicle to favorites">
                  <Heart />
                </Button>
                <Button variant="secondary" aria-describedby="help-text">
                  Need Help?
                </Button>
                <div id="help-text" className="text-sm text-gray-600">
                  Contact our support team for assistance
                </div>
              </div>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h5 className="font-medium text-green-900 mb-2">♿ Accessibility Checklist</h5>
            <ul className="text-sm text-green-800 space-y-1">
              <li>✅ Sufficient color contrast (4.5:1 minimum)</li>
              <li>✅ Focus indicators for keyboard navigation</li>
              <li>✅ Semantic HTML button elements</li>
              <li>✅ Proper ARIA labels for icon-only buttons</li>
              <li>✅ Disabled state properly communicated</li>
              <li>✅ Loading states announced to screen readers</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ButtonShowcase
