"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Menu } from "lucide-react"
import { Button } from "../ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu"
import {
  LayoutDashboard,
  CarFront,
  CalendarRange,
  CreditCard,
  Settings,
  MapPin,
  TrendingUp,
  Users,
} from "lucide-react"

const navItems = [
  { label: "Overview", icon: LayoutDashboard, href: "/admin" },
  { label: "Cars", icon: CarFront, href: "/admin/cars" },
  { label: "Bookings", icon: CalendarRange, href: "/admin/bookings" },
  { label: "Sales Tracking", icon: TrendingUp, href: "/admin/sales-tracking" },
  { label: "Tracker", icon: MapPin, href: "/admin/tracker" },
  { label: "Payments", icon: CreditCard, href: "/admin/payments" },
  { label: "Accounts", icon: Users, href: "/admin/accounts" },
  { label: "Settings", icon: Settings, href: "/admin/settings" },
]

export function AdminMobileNav() {
  const [mounted, setMounted] = useState(false)
  const pathname = usePathname()

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  // Find current page for accessibility label
  const currentPage = navItems.find(item => {
    if (item.href === "/admin") {
      return pathname === "/admin"
    }
    return pathname.startsWith(item.href)
  })?.label || "Navigation"

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="secondary" 
          size="sm" 
          className="md:hidden p-2 h-10 w-10 rounded-full flex items-center justify-center touch-manipulation"
          aria-label={`${currentPage} navigation menu`}
        >
          <Menu className="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {navItems.map((item) => (
          <DropdownMenuItem key={item.href} asChild>
            <Link 
              href={item.href}
              className="flex items-center gap-2 cursor-pointer w-full"
            >
              <item.icon className="h-4 w-4" />
              <span>{item.label}</span>
            </Link>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
