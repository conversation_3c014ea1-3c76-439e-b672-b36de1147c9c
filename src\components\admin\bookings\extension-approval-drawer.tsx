"use client";

import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Car,
  User,
  CreditCard,
  MapPin,
  FileText,
  AlertTriangle,
  Info,
} from "lucide-react";
import { format, differenceInHours } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { reviewExtensionRequest } from "@/lib/services/booking-extension-service";

interface ExtensionRequest {
  id: string;
  booking_id: string;
  customer_id: string;
  original_dropoff_datetime: string;
  requested_dropoff_datetime: string;
  extension_duration_hours: number;
  additional_amount: number;
  request_reason?: string;
  request_notes?: string;
  status: string;
  has_conflicts: boolean;
  alternative_cars?: string[];
  alternative_suggestions?: any;
  created_at: string;
  expires_at: string;
  bookings: {
    id: string;
    booking_ref: string;
    pickup_datetime: string;
    dropoff_datetime: string;
    total_amount: number;
    status: string;
    cars: {
      model: string;
      plate_number: string;
      type: string;
    };
  };
  profiles: {
    full_name: string;
    email: string;
    phone?: string;
  };
}

interface ExtensionApprovalDrawerProps {
  extensionRequest: ExtensionRequest | null;
  isOpen: boolean;
  onClose: () => void;
  onRequestProcessed?: () => void;
  currentAdminId: string;
}

export function ExtensionApprovalDrawer({
  extensionRequest,
  isOpen,
  onClose,
  onRequestProcessed,
  currentAdminId,
}: ExtensionApprovalDrawerProps) {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  
  const [processing, setProcessing] = React.useState(false);
  const [action, setAction] = React.useState<"approve" | "reject" | null>(null);
  const [adminNotes, setAdminNotes] = React.useState("");
  const [rejectionReason, setRejectionReason] = React.useState("");

  // Reset form when modal opens
  React.useEffect(() => {
    if (isOpen) {
      setAction(null);
      setAdminNotes("");
      setRejectionReason("");
    }
  }, [isOpen]);

  if (!extensionRequest) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-PH", {
      style: "currency",
      currency: "PHP",
    }).format(amount);
  };

  const formatDateTime = (dateString: string) => {
    return format(new Date(dateString), "MMM dd, yyyy 'at' h:mm a");
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return "bg-yellow-100 text-yellow-700 border-yellow-200";
      case "approved":
        return "bg-green-100 text-green-700 border-green-200";
      case "rejected":
        return "bg-red-100 text-red-700 border-red-200";
      case "expired":
        return "bg-gray-100 text-gray-700 border-gray-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return <Clock className="h-3 w-3" />;
      case "approved":
        return <CheckCircle className="h-3 w-3" />;
      case "rejected":
        return <XCircle className="h-3 w-3" />;
      case "expired":
        return <AlertCircle className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const handleProcessRequest = async (requestAction: "approve" | "reject") => {
    if (requestAction === "reject" && !rejectionReason.trim()) {
      toast({
        variant: "destructive",
        title: "Rejection Reason Required",
        description: "Please provide a reason for rejecting this extension request.",
      });
      return;
    }

    setProcessing(true);
    try {
      const { data, error } = await reviewExtensionRequest(
        extensionRequest.id,
        requestAction,
        currentAdminId,
        adminNotes.trim() || undefined,
        requestAction === "reject" ? rejectionReason.trim() : undefined
      );

      if (error) {
        throw new Error(error.message || `Failed to ${requestAction} extension request`);
      }

      toast({
        title: `Extension Request ${requestAction === "approve" ? "Approved" : "Rejected"}`,
        description: `The booking extension has been ${requestAction}d successfully.`,
      });

      if (onRequestProcessed) {
        onRequestProcessed();
      }
      
      onClose();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Processing Failed",
        description: error.message,
      });
    } finally {
      setProcessing(false);
    }
  };

  const isExpired = new Date(extensionRequest.expires_at) < new Date();
  const canProcess = extensionRequest.status === "pending" && !isExpired;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn(
        "max-w-4xl w-[90vw]",
        isMobile ? "h-[90vh] max-h-[90vh] flex flex-col" : "max-h-[85vh]"
      )}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Extension Request Review
          </DialogTitle>
        </DialogHeader>

        <div className={cn(
          "flex-1 space-y-6",
          isMobile ? "overflow-y-auto pr-2" : ""
        )}>
          {/* Request Status */}
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Request Status</CardTitle>
                <Badge className={cn("text-xs border", getStatusColor(extensionRequest.status))}>
                  {getStatusIcon(extensionRequest.status)}
                  <span className="ml-1 capitalize">{extensionRequest.status}</span>
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Request ID:</span>
                  <p className="font-mono text-xs">{extensionRequest.id.slice(-8).toUpperCase()}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Submitted:</span>
                  <p>{formatDateTime(extensionRequest.created_at)}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Expires:</span>
                  <p className={isExpired ? "text-red-600 font-medium" : ""}>
                    {formatDateTime(extensionRequest.expires_at)}
                  </p>
                </div>
              </div>

              {isExpired && (
                <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <span className="font-medium text-red-900">Request Expired</span>
                  </div>
                  <p className="text-sm text-red-700 mt-1">
                    This extension request has expired and can no longer be processed.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Customer & Booking Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Customer Details */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <User className="h-4 w-4" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="font-medium text-gray-600">Name:</span>
                  <p className="font-semibold">{extensionRequest.profiles.full_name}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Email:</span>
                  <p className="text-sm">{extensionRequest.profiles.email}</p>
                </div>
                {extensionRequest.profiles.phone && (
                  <div>
                    <span className="font-medium text-gray-600">Phone:</span>
                    <p className="text-sm">{extensionRequest.profiles.phone}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Original Booking Details */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <Car className="h-4 w-4" />
                  Booking Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="font-medium text-gray-600">Booking Ref:</span>
                  <p className="font-mono text-sm">{extensionRequest.bookings.booking_ref}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Vehicle:</span>
                  <p className="font-semibold">{extensionRequest.bookings.cars.model}</p>
                  <p className="text-xs text-gray-600">Plate: {extensionRequest.bookings.cars.plate_number}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Original Total:</span>
                  <p className="font-semibold text-green-600">
                    {formatCurrency(extensionRequest.bookings.total_amount)}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Extension Details */}
          <Card className="border-l-4 border-l-green-500">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Extension Request Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-gray-50 p-3 rounded-lg">
                  <span className="font-medium text-gray-600 text-sm">Original Return:</span>
                  <p className="font-semibold">{formatDateTime(extensionRequest.original_dropoff_datetime)}</p>
                </div>
                <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                  <span className="font-medium text-green-700 text-sm">Requested Return:</span>
                  <p className="font-semibold text-green-800">{formatDateTime(extensionRequest.requested_dropoff_datetime)}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="font-medium text-gray-600">Extension Duration:</span>
                  <p className="text-lg font-bold text-blue-600">
                    {extensionRequest.extension_duration_hours} hours
                  </p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Additional Cost:</span>
                  <p className="text-lg font-bold text-green-600">
                    {formatCurrency(extensionRequest.additional_amount)}
                  </p>
                </div>
              </div>

              {extensionRequest.request_reason && (
                <div>
                  <span className="font-medium text-gray-600">Reason:</span>
                  <p className="text-sm bg-blue-50 p-2 rounded border">{extensionRequest.request_reason}</p>
                </div>
              )}

              {extensionRequest.request_notes && (
                <div>
                  <span className="font-medium text-gray-600">Customer Notes:</span>
                  <p className="text-sm bg-blue-50 p-2 rounded border">{extensionRequest.request_notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Conflicts & Alternatives */}
          {extensionRequest.has_conflicts && extensionRequest.alternative_suggestions && (
            <Card className="border-l-4 border-l-amber-500">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-amber-600" />
                  Scheduling Conflicts
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-amber-50 border border-amber-200 p-3 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                    <span className="font-medium text-amber-900">Extension Period Conflicts</span>
                  </div>
                  {extensionRequest.alternative_suggestions.conflicts?.map((conflict: any, index: number) => (
                    <div key={index} className="text-sm text-amber-800 bg-white p-2 rounded border mb-2">
                      <p className="font-medium">{conflict.customerName}</p>
                      <p className="text-xs">
                        {formatDateTime(conflict.conflictPeriod.start)} - {formatDateTime(conflict.conflictPeriod.end)}
                      </p>
                    </div>
                  ))}
                </div>

                {extensionRequest.alternative_suggestions.alternatives?.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <Car className="h-4 w-4" />
                      Alternative Vehicles Available
                    </h4>
                    <div className="space-y-2">
                      {extensionRequest.alternative_suggestions.alternatives.map((car: any, index: number) => (
                        <div key={index} className="flex items-center gap-3 p-2 bg-white rounded border">
                          <div className="flex-1">
                            <p className="font-medium text-sm">{car.model}</p>
                            <p className="text-xs text-gray-600">
                              {car.type} • Plate: {car.plateNumber} • {formatCurrency(car.pricePerDay)}/day
                            </p>
                          </div>
                          <Badge variant="secondary" className="bg-green-100 text-green-700">
                            Available
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Admin Review Section */}
          {canProcess && (
            <Card className="border-l-4 border-l-purple-500">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Admin Review
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="admin-notes">Admin Notes (Optional)</Label>
                  <Textarea
                    id="admin-notes"
                    placeholder="Add any notes about this extension request..."
                    value={adminNotes}
                    onChange={(e) => setAdminNotes(e.target.value)}
                    rows={3}
                  />
                </div>

                {action === "reject" && (
                  <div>
                    <Label htmlFor="rejection-reason">Rejection Reason *</Label>
                    <Textarea
                      id="rejection-reason"
                      placeholder="Please explain why this extension is being rejected..."
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                      rows={3}
                      className="border-red-200 focus:border-red-300"
                    />
                  </div>
                )}

                <div className="flex gap-3 pt-2">
                  <Button
                    onClick={() => setAction(action === "approve" ? null : "approve")}
                    variant={action === "approve" ? "primary" : "secondary"}
                    className={cn(
                      "flex-1",
                      action === "approve" ? "bg-green-600 hover:bg-green-700" : "border-green-300 text-green-700 hover:bg-green-50"
                    )}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    {action === "approve" ? "Confirm Approval" : "Approve Extension"}
                  </Button>
                  
                  <Button
                    onClick={() => setAction(action === "reject" ? null : "reject")}
                    variant={action === "reject" ? "destructive" : "secondary"}
                    className={cn(
                      "flex-1",
                      action !== "reject" && "border-red-300 text-red-700 hover:bg-red-50"
                    )}
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    {action === "reject" ? "Confirm Rejection" : "Reject Extension"}
                  </Button>
                </div>

                {action && (
                  <div className="flex gap-2 pt-2 border-t">
                    <Button
                      variant="secondary"
                      onClick={() => setAction(null)}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={() => handleProcessRequest(action)}
                      disabled={processing || (action === "reject" && !rejectionReason.trim())}
                      className={cn(
                        "flex-1",
                        action === "approve" ? "bg-green-600 hover:bg-green-700" : "bg-red-600 hover:bg-red-700"
                      )}
                    >
                      {processing ? (
                        <>
                          <Clock className="h-4 w-4 mr-2 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        `${action === "approve" ? "Approve" : "Reject"} Request`
                      )}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Information Notice */}
          <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Review Guidelines:</p>
                <ul className="space-y-1 text-xs">
                  <li>• Verify the extension reason and customer history</li>
                  <li>• Check for any scheduling conflicts with other bookings</li>
                  <li>• Consider alternative vehicles if the original is unavailable</li>
                  <li>• Approved extensions automatically update the booking and payment</li>
                  <li>• Customer will be notified via email of the decision</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
