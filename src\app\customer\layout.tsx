"use client"

import * as React from "react"
import { CustomerAuthProvider } from "@/components/auth/customer-auth-context"
import { CustomerProtection } from "@/components/auth/customer-protection"
import { CustomerChatbot } from "@/components/customer-side/chatbot/customer-chatbot"
import { OngoingBookingBanner } from "@/components/customer-side/booking/ongoing-booking-banner"

// Per-segment layout for all /customer routes.
export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <CustomerAuthProvider>
      <CustomerProtection allowPublic={true}>
        <OngoingBookingBanner />
        {children}
        <CustomerChatbot />
      </CustomerProtection>
    </CustomerAuthProvider>
  )
}
