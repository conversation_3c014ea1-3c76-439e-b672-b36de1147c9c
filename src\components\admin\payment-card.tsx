"use client"

import * as React from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ChevronRight } from "lucide-react"
import type { AdminPayment } from "@/app/admin/payments/actions/payment-actions"
import { PaymentDetailsModal } from "./payment-details-modal"

interface PaymentCardProps {
  payment: AdminPayment
}

export function PaymentCard({ payment }: PaymentCardProps) {
  const [isModalOpen, setIsModalOpen] = React.useState(false)
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric"
    })
  }

  // Get payment method badge color
  const getMethodBadgeClass = (method: string) => {
    switch (method.toLowerCase()) {
      case 'gcash':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'bank transfer':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'remittance center':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
    }
  }

  return (
    <>
      <Card className="overflow-hidden border rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
        <div className="p-3 xs:p-4 space-y-2 xs:space-y-3">
          {/* Mobile/tablet view (< 1024px) */}
          <div className="block lg:hidden">
            {/* Header with renter name and payment amount */}
            <div className="flex flex-col xs:flex-row xs:justify-between xs:items-start gap-2 xs:gap-3">
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-sm xs:text-base text-foreground truncate">{payment.renterName}</h3>
                <p className="text-xs xs:text-sm text-muted-foreground truncate">#{payment.paymentRef}</p>
              </div>
              <div className="flex-shrink-0">
                <p className="text-lg xs:text-xl font-bold text-foreground">₱{payment.amount.toLocaleString('en-PH', { minimumFractionDigits: 2 })}</p>
              </div>
            </div>

            {/* Payment method and date */}
            <div className="flex flex-col xs:flex-row xs:justify-between xs:items-center gap-2 xs:gap-3 mt-2">
              <Badge className={`${getMethodBadgeClass(payment.method)} text-xs px-2 py-1 w-fit`}>
                {payment.method}
              </Badge>
              <p className="text-xs xs:text-sm text-muted-foreground">{formatDate(payment.transactionDate)}</p>
            </div>

            {/* View Details button */}
            <div className="pt-2 xs:pt-3 border-t border-muted mt-2 xs:mt-3">
              <Button 
                variant="secondary" 
                className="w-full h-12 min-h-[48px] justify-between text-sm xs:text-base"
                onClick={() => setIsModalOpen(true)}
              >
                <span>View Details</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* Desktop view (≥ 1024px) - Original design */}
          <div className="hidden lg:block">
            {/* Top section: Renter name and payment amount */}
            <div className="flex justify-between items-start">
              <div className="min-w-0 flex-1">
                <h3 className="font-medium text-base truncate">{payment.renterName}</h3>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-foreground">₱{payment.amount.toLocaleString('en-PH', { minimumFractionDigits: 2 })}</p>
              </div>
            </div>

            {/* Middle section: Payment method and date */}
            <div className="flex justify-between items-center">
              <Badge className={`${getMethodBadgeClass(payment.method)} text-xs px-2 py-1`}>
                {payment.method}
              </Badge>
              <p className="text-sm text-muted-foreground">{formatDate(payment.transactionDate)}</p>
            </div>

            {/* View Details button */}
            <div className="pt-2 border-t border-muted">
              <Button 
                variant="secondary" 
                className="w-full h-11 min-h-[44px] justify-between"
                onClick={() => setIsModalOpen(true)}
              >
                <span>View Details</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </Card>
      
      {/* Payment Details Modal */}
      <PaymentDetailsModal 
        payment={payment} 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
      />
    </>
  )
}
