'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createContextClient } from '@/lib/supabase/server'

export async function adminLogin(formData: FormData) {
  const supabase = await createContextClient('admin')
  
  const data = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }

  // First, attempt to sign in
  const { error, data: authData } = await supabase.auth.signInWithPassword(data)

  if (error) {
    redirect(`/admin-auth?error=${encodeURIComponent(error.message)}`)
  }

  // Check if user has admin role - only validate against database role, no email patterns
  if (authData.user) {
    // Allow super admin email to bypass role check temporarily for migration
    const isSuperAdmin = data.email === process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAIL || data.email === '<EMAIL>'
    const userRole = authData.user.user_metadata?.role
    
    if (userRole !== 'admin' && userRole !== 'super_admin' && !isSuperAdmin) {
      // Sign out the user since they're not an admin
      await supabase.auth.signOut()
      redirect('/admin-auth?error=Access denied. Admin privileges required.')
    }
  }

  revalidatePath('/', 'layout')
  redirect('/admin')
}

export async function signOut() {
  const supabase = await createClient()
  
  const { error } = await supabase.auth.signOut()
  
  if (error) {
    console.error('Error signing out:', error)
  }

  revalidatePath('/', 'layout')
  redirect('/')
}
