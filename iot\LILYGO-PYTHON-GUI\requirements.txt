# PathLink GPS Tracker GUI Requirements
# Core dependencies
requests>=2.25.1
websocket-client>=1.2.1

# Optional dependencies for enhanced functionality
# tkinter is included with Python standard library
# configparser is included with Python standard library
# threading is included with Python standard library
# datetime is included with Python standard library
# json is included with Python standard library
# os is included with Python standard library
# queue is included with Python standard library
