/**
 * Test suite for admin authentication persistence bug fix
 * 
 * This test verifies that admin users remain logged in after page refresh
 * and are not incorrectly redirected to the login page.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { NextRequest, NextResponse } from 'next/server'
import { updateSession } from '@/lib/supabase/middleware'

// Mock Next.js modules
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(),
  }),
  usePathname: () => '/admin',
}))

// Mock Supabase
const mockSupabaseClient = {
  auth: {
    getUser: vi.fn(),
    setSession: vi.fn(),
    signOut: vi.fn(),
    onAuthStateChange: vi.fn(() => ({
      data: { subscription: { unsubscribe: vi.fn() } }
    })),
  },
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn(),
      })),
    })),
  })),
}

vi.mock('@supabase/ssr', () => ({
  createServerClient: vi.fn(() => mockSupabaseClient),
  createBrowserClient: vi.fn(() => mockSupabaseClient),
}))

describe('Admin Session Persistence Fix', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
      writable: true,
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Middleware Context-Aware Authentication', () => {
    it('should use admin context client for admin routes', async () => {
      // Mock admin user session
      const mockAdminUser = {
        id: 'admin-user-id',
        email: '<EMAIL>',
        user_metadata: { role: 'admin' }
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockAdminUser },
        error: null
      })

      // Create mock request for admin route
      const request = new NextRequest('http://localhost:3000/admin/dashboard', {
        headers: {
          cookie: 'sb-admin-auth-token.access_token=mock-admin-token; sb-admin-auth-token.refresh_token=mock-refresh-token'
        }
      })

      const response = await updateSession(request)

      // Should not redirect admin user
      expect(response.status).toBe(200)
      expect(response.headers.get('location')).toBeNull()
    })

    it('should redirect unauthenticated users from admin routes', async () => {
      // Mock no user session
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      // Create mock request for admin route without auth cookies
      const request = new NextRequest('http://localhost:3000/admin/dashboard')

      const response = await updateSession(request)

      // Should redirect to admin login
      expect(response.status).toBe(307) // Redirect status
      expect(response.headers.get('location')).toContain('/admin-auth')
    })

    it('should handle customer users trying to access admin routes', async () => {
      // Mock customer user session
      const mockCustomerUser = {
        id: 'customer-user-id',
        email: '<EMAIL>',
        user_metadata: { role: 'customer' }
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockCustomerUser },
        error: null
      })

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn().mockResolvedValue({
              data: { role: 'customer' },
              error: null
            })
          }))
        }))
      })

      // Create mock request for admin route with customer session
      const request = new NextRequest('http://localhost:3000/admin/dashboard', {
        headers: {
          cookie: 'sb-customer-auth-token.access_token=mock-customer-token'
        }
      })

      const response = await updateSession(request)

      // Should redirect customer to admin login with error
      expect(response.status).toBe(307)
      expect(response.headers.get('location')).toContain('/admin-auth')
      expect(response.headers.get('location')).toContain('error=Access%20denied')
    })
  })

  describe('Session Storage Isolation', () => {
    it('should use separate storage keys for admin and customer sessions', () => {
      // This test verifies that the storage adapter uses correct keys
      const { IsolatedStorageAdapter } = require('@/lib/supabase/storage-adapter')
      
      const adminAdapter = new IsolatedStorageAdapter('sb-admin-auth-token')
      const customerAdapter = new IsolatedStorageAdapter('sb-customer-auth-token')

      // Mock localStorage
      const mockLocalStorage = {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
      }
      
      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true,
      })

      // Test admin storage
      adminAdapter.setItem('access_token', 'admin-token')
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'sb-admin-auth-token.access_token',
        'admin-token'
      )

      // Test customer storage
      customerAdapter.setItem('access_token', 'customer-token')
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'sb-customer-auth-token.access_token',
        'customer-token'
      )
    })
  })

  describe('Super Admin Email Handling', () => {
    it('should handle super admin email bypass correctly', async () => {
      const superAdminEmail = '<EMAIL>'
      
      const mockSuperAdminUser = {
        id: 'super-admin-id',
        email: superAdminEmail,
        user_metadata: {}
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockSuperAdminUser },
        error: null
      })

      const request = new NextRequest('http://localhost:3000/admin/dashboard', {
        headers: {
          cookie: 'sb-admin-auth-token.access_token=mock-super-admin-token'
        }
      })

      const response = await updateSession(request)

      // Super admin should have access
      expect(response.status).toBe(200)
      expect(response.headers.get('location')).toBeNull()
    })
  })
})
