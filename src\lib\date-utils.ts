export function toISO(dt: string | Date): string {
  return (typeof dt === "string" ? new Date(dt) : dt).toISOString()
}

export function parseLocalDateTime(input: string): Date {
  // input from <input type="datetime-local" />
  // treat as local time
  return new Date(input.replace(" ", "T"))
}

export function daysBetween(start: Date, end: Date): number {
  const ms = end.getTime() - start.getTime()
  if (ms <= 0) return 0
  return Math.ceil(ms / (1000 * 60 * 60 * 24))
}

export function overlap(aStart: Date, aEnd: Date, bStart: Date, bEnd: Date): boolean {
  return aStart < bEnd && bStart < aEnd
}

export function formatCurrency(n: number): string {
  return `₱${n.toFixed(2)}`
}

export function fmtDT(dt: string | Date): string {
  const d = typeof dt === "string" ? new Date(dt) : dt
  return d.toLocaleString()
}
