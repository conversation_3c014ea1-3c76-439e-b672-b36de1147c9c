import { createClient } from '@/lib/supabase/server';
import OpenAI from 'openai';

// Initialize OpenAI client for embeddings
const openaiClient = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface KnowledgeResult {
  id: string;
  title: string;
  content: string;
  similarity: number;
  source: string;
  category: string;
}

export interface RagContext {
  query: string;
  results: KnowledgeResult[];
  userType: 'customer' | 'admin';
}

// Generate embeddings for text content
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    // Validate input text
    if (!text || typeof text !== 'string' || text.trim() === '') {
      console.error('Invalid text input for embedding:', text);
      throw new Error('Invalid text input for embedding generation');
    }

    const cleanText = text.toString().trim();
    if (cleanText.length === 0) {
      console.error('Empty text provided for embedding generation');
      throw new Error('Empty text provided for embedding generation');
    }

    const response = await openaiClient.embeddings.create({
      model: 'text-embedding-ada-002',
      input: cleanText.replace(/\n/g, ' '),
    });
    
    return response.data[0].embedding;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw new Error('Failed to generate embedding');
  }
}

// Search knowledge base using vector similarity
export async function searchKnowledge(
  query: string,
  userType: 'customer' | 'admin',
  options: {
    matchThreshold?: number;
    matchCount?: number;
    filterSource?: string;
    filterCategory?: string;
  } = {}
): Promise<KnowledgeResult[]> {
  try {
    const supabase = await createClient();
    
    // Generate embedding for the query
    const queryEmbedding = await generateEmbedding(query);
    
    // Set default options
    const {
      matchThreshold = 0.7,
      matchCount = 5,
      filterSource,
      filterCategory
    } = options;
    
    // Search knowledge base
    const { data, error } = await supabase.rpc('search_knowledge', {
      query_embedding: queryEmbedding,
      match_threshold: matchThreshold,
      match_count: matchCount,
      filter_source: filterSource,
      filter_category: filterCategory
    });
    
    if (error) {
      console.error('Error searching knowledge:', error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Error in searchKnowledge:', error);
    return [];
  }
}

// Add knowledge to the database
export async function addKnowledge(
  title: string,
  content: string,
  source: string,
  category: string,
  metadata: Record<string, any> = {}
): Promise<boolean> {
  try {
    const supabase = await createClient();
    
    // Generate embedding for the content
    const embedding = await generateEmbedding(content);
    
    const { error } = await supabase
      .from('chatbot_knowledge')
      .insert({
        title,
        content,
        embedding,
        source,
        category,
        metadata
      });
    
    if (error) {
      console.error('Error adding knowledge:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in addKnowledge:', error);
    return false;
  }
}

// Build context for RAG-enhanced prompts
export function buildRagContext(
  query: string,
  results: KnowledgeResult[],
  userType: 'customer' | 'admin'
): string {
  if (results.length === 0) {
    return `User Query: ${query}\n\nNo relevant context found in knowledge base.`;
  }
  
  const contextSections = results.map((result, index) => 
    `Context ${index + 1} (${result.category} - ${result.source}):\n${result.content}`
  ).join('\n\n');
  
  return `User Query: ${query}

Relevant Context from Knowledge Base:
${contextSections}

Use the above context to provide accurate, helpful responses. If the context doesn't contain relevant information, acknowledge this and provide general assistance.`;
}

// PathLink-specific knowledge categories
export const KNOWLEDGE_CATEGORIES = {
  GENERAL: 'general',
  BOOKING: 'booking',
  PAYMENT: 'payment',
  SUPPORT: 'support',
  CARS: 'cars',
  POLICIES: 'policies',
  ADMIN: 'admin',
  TECHNICAL: 'technical'
} as const;

// PathLink-specific knowledge sources
export const KNOWLEDGE_SOURCES = {
  CUSTOMER_FAQ: 'customer_faq',
  ADMIN_DOCS: 'admin_docs',
  BOOKING_INFO: 'booking_info',
  PAYMENT_INFO: 'payment_info',
  CAR_INFO: 'car_info',
  POLICY_DOCS: 'policy_docs',
  TROUBLESHOOTING: 'troubleshooting'
} as const;
