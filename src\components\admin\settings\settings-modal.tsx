import * as React from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogClose } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Save, X } from "lucide-react";
import type { SettingsSection } from "@/lib/types";

interface SettingsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  activeSection: SettingsSection;
  sectionTitle: string;
  sectionIcon: React.ElementType;
  children: React.ReactNode;
  isDirty: boolean;
  onSave: () => void;
  onDiscard: () => void;
  saving: boolean;
}

export function SettingsModal({
  open,
  onOpenChange,
  activeSection,
  sectionTitle,
  sectionIcon: Icon,
  children,
  isDirty,
  onSave,
  onDiscard,
  saving,
}: SettingsModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[90%] md:max-w-[600px] p-0 h-[90vh] max-h-[90vh] flex flex-col" showCloseButton={false}>
        <DialogHeader className="px-6 py-4 border-b sticky top-0 bg-white z-10 flex flex-row items-center justify-between">
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Icon className="h-5 w-5" />
            {sectionTitle}
          </DialogTitle>
          <DialogClose className="rounded-full h-8 w-8 flex items-center justify-center hover:bg-gray-100">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogHeader>
        <ScrollArea className="flex-1 p-6">
          {children}
        </ScrollArea>
        {isDirty && (
          <div className="border-t p-4 sticky bottom-0 bg-white z-10">
            <div className="flex items-center justify-end gap-3">
              <Button
                variant="secondary"
                onClick={onDiscard}
                className="px-4"
                disabled={saving}
              >
                <X className="h-4 w-4 mr-2" />
                Discard
              </Button>
              <Button
                onClick={onSave}
                className="px-4 bg-blue-600 hover:bg-blue-700"
                disabled={saving}
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
