import dynamic from "next/dynamic"
import { ComponentType } from "react"

// Dynamically import heavy @radix-ui components to reduce bundle size
export const DynamicDialog = dynamic(() => import("@/components/ui/dialog").then(mod => ({ default: mod.Dialog })))

export const DynamicDropdownMenu = dynamic(() => import("@/components/ui/dropdown-menu").then(mod => ({ default: mod.DropdownMenu })))

export const DynamicPopover = dynamic(() => import("@/components/ui/popover").then(mod => ({ default: mod.Popover })))

export const DynamicTabs = dynamic(() => import("@/components/ui/tabs").then(mod => ({ default: mod.Tabs })))

export const DynamicSheet = dynamic(() => import("@/components/ui/sheet").then(mod => ({ default: mod.Sheet })))

export const DynamicCollapsible = dynamic(() => import("@/components/ui/collapsible").then(mod => ({ default: mod.Collapsible })))

// Lazy load chart components
export const DynamicChart = dynamic(() => import("recharts").then(mod => ({ default: mod.ResponsiveContainer })))

// Admin-only components that should be code-split
export const DynamicGPSMap = dynamic(() => import("@/components/admin/gps-tracker-map").then(mod => ({ default: mod.GPSTrackerMap })))

// Customer-only components 
export const DynamicCarCarousel = dynamic(() => import("@/components/customer-side/home/<USER>").then(mod => ({ default: mod.CustomerImagesCarousel })))

// Utility for creating lazy-loaded components
export function createLazyComponent<T = any>(
  importFn: () => Promise<{ default: ComponentType<T> }>
): ComponentType<T> {
  return dynamic(importFn)
}
