// Car Availability Data Adapter
// This module provides a centralized interface for car availability data
// Currently uses mock data, but can be easily swapped to real API/database calls

export interface CarAvailabilityItem {
  id: string
  plateNo: string
  model: string
  location: string
  status: "available" | "booked" | "underMaintenance" | "scheduledMaintenance"
  bookedUntil?: Date | null
  nextMaintenanceDate?: Date | null
}

export interface CarAvailabilityFilters {
  dateRange: {
    from: Date
    to: Date
  }
  location?: string
  vehicleType?: string
  status?: string[]
}

export interface CarAvailabilityStats {
  available: number
  booked: number
  underMaintenance: number
  scheduledMaintenance: number
}

// Helper function to add days to a date
function addDays(date: Date, days: number): Date {
  const result = new Date(date)
  result.setDate(result.getDate() + days)
  return result
}

// Helper function to subtract days from a date
function subDays(date: Date, days: number): Date {
  const result = new Date(date)
  result.setDate(result.getDate() - days)
  return result
}

// Mock data for development
// TODO: Replace with real API calls to Supabase
const mockCarAvailabilityData: CarAvailabilityItem[] = [
  {
    id: "c1",
    plateNo: "ABC-1001",
        model: "Toyota Vios XLE 2023 AT",
    location: "Manila",
    status: "available",
    bookedUntil: null,
    nextMaintenanceDate: addDays(new Date(), 30)
  },
  {
    id: "c2", 
    plateNo: "ABC-1002",
    model: "Toyota Vios E 2016 AT",
    location: "Makati",
    status: "booked",
    bookedUntil: addDays(new Date(), 3),
    nextMaintenanceDate: addDays(new Date(), 45)
  },
  {
    id: "c3",
    plateNo: "SUV-2001", 
    model: "Toyota Fortuner G 2017 AT",
    location: "Quezon City",
    status: "available",
    bookedUntil: null,
    nextMaintenanceDate: addDays(new Date(), 15)
  },
  {
    id: "c4",
    plateNo: "SUV-2002",
    model: "Toyota Fortuner V 2018 AT", 
    location: "BGC Taguig",
    status: "booked",
    bookedUntil: addDays(new Date(), 7),
    nextMaintenanceDate: addDays(new Date(), 60)
  },
  {
    id: "c5",
    plateNo: "MPV-3001",
    model: "Toyota Innova J 2006 MT",
    location: "Manila",
    status: "underMaintenance",
    bookedUntil: null,
    nextMaintenanceDate: null
  },
  {
    id: "c6",
    plateNo: "MPV-3002",
    model: "Toyota GL Grandia 2011 MT",
    location: "Alabang", 
    status: "available",
    bookedUntil: null,
    nextMaintenanceDate: addDays(new Date(), 20)
  },
  {
    id: "c7",
    plateNo: "HAT-4001",
    model: "Mitsubishi Mirage GLS 2019 AT",
    location: "Makati",
    status: "scheduledMaintenance",
    bookedUntil: null,
    nextMaintenanceDate: addDays(new Date(), 5)
  },
  {
    id: "c8",
    plateNo: "HAT-4002",
    model: "Honda CR-V 2007 AT",
    location: "Quezon City",
    status: "available",
    bookedUntil: null,
    nextMaintenanceDate: addDays(new Date(), 25)
  },
  {
    id: "c9",
    plateNo: "SUV-2003",
    model: "Mitsubishi Montero 2014 MT",
    location: "BGC Taguig",
    status: "booked",
    bookedUntil: addDays(new Date(), 14),
    nextMaintenanceDate: addDays(new Date(), 40)
  },
  {
    id: "c10", 
    plateNo: "MPV-3003",
    model: "Mitsubishi Xpander GLS 2019 AT",
    location: "Manila",
    status: "available",
    bookedUntil: null,
    nextMaintenanceDate: addDays(new Date(), 35)
  },
  {
    id: "c11",
    plateNo: "MPV-3004",
    model: "Mitsubishi Xpander GLS 2023 AT", 
    location: "Alabang",
    status: "underMaintenance",
    bookedUntil: null,
    nextMaintenanceDate: null
  },
  {
    id: "c12",
    plateNo: "MCY-5001",
        model: "Yamaha Mio i125 2019 AT",
    location: "Makati",
    status: "available",
    bookedUntil: null,
    nextMaintenanceDate: addDays(new Date(), 10)
  },
  {
    id: "c13",
    plateNo: "MCY-5002",
        model: "Yamaha NMAX 2019 AT",
    location: "Quezon City", 
    status: "scheduledMaintenance",
    bookedUntil: null,
    nextMaintenanceDate: addDays(new Date(), 2)
  }
]

/**
 * Fetches car availability data
 * Currently returns mock data, but can be easily replaced with real API calls
 */
export async function getCarAvailabilityData(filters?: CarAvailabilityFilters): Promise<CarAvailabilityItem[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // TODO: Replace with real API call
  // const response = await fetch('/api/admin/car-availability', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(filters)
  // })
  // return response.json()
  
  return mockCarAvailabilityData
}

/**
 * Gets aggregated statistics for car availability
 */
export async function getCarAvailabilityStats(filters?: CarAvailabilityFilters): Promise<CarAvailabilityStats> {
  const data = await getCarAvailabilityData(filters)
  
  return {
    available: data.filter(item => item.status === "available").length,
    booked: data.filter(item => item.status === "booked").length, 
    underMaintenance: data.filter(item => item.status === "underMaintenance").length,
    scheduledMaintenance: data.filter(item => item.status === "scheduledMaintenance").length
  }
}

/**
 * Feature flag to enable/disable mock data
 * Set to false when real API is ready
 */
export const USE_MOCK_DATA = true

/**
 * Updates a vehicle's availability status
 * TODO: Implement real API call
 */
export async function updateVehicleStatus(
  vehicleId: string, 
  status: CarAvailabilityItem["status"],
  bookedUntil?: Date,
  nextMaintenanceDate?: Date
): Promise<boolean> {
  // TODO: Implement real API call
  console.log(`Updating vehicle ${vehicleId} status to ${status}`)
  return true
}

/**
 * Gets availability trend data for charts
 * TODO: Implement real API call
 */
export async function getAvailabilityTrend(days: number = 30): Promise<Array<{
  date: Date
  available: number
  booked: number
  maintenance: number
}>> {
  // TODO: Replace with real API call
  const trendData = []
  for (let i = days; i >= 0; i--) {
    const date = subDays(new Date(), i)
    trendData.push({
      date,
      available: Math.floor(Math.random() * 8) + 2,
      booked: Math.floor(Math.random() * 6) + 1, 
      maintenance: Math.floor(Math.random() * 3)
    })
  }
  return trendData
}
