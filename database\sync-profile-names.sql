-- Sync profile names to ensure consistency between full_name and individual name fields
-- This addresses issues where payment/sales tracking might fail due to missing name data

-- Update full_name from individual fields where full_name is missing but individual fields exist
UPDATE profiles 
SET full_name = TRIM(
    COALESCE(first_name, '') || 
    CASE WHEN middle_initial IS NOT NULL AND middle_initial != '' 
         THEN ' ' || middle_initial 
         ELSE '' END ||
    CASE WHEN last_name IS NOT NULL AND last_name != '' 
         THEN ' ' || last_name 
         ELSE '' END
),
updated_at = NOW()
WHERE (full_name IS NULL OR full_name = '') 
  AND (first_name IS NOT NULL OR last_name IS NOT NULL);

-- Update individual fields from full_name where individual fields are missing
-- This is a basic split - you may need to adjust based on your naming conventions
UPDATE profiles 
SET 
    first_name = CASE 
        WHEN first_name IS NULL AND full_name IS NOT NULL THEN
            TRIM(SPLIT_PART(full_name, ' ', 1))
        ELSE first_name 
    END,
    last_name = CASE 
        WHEN last_name IS NULL AND full_name IS NOT NULL AND array_length(string_to_array(full_name, ' '), 1) >= 2 THEN
            CASE 
                WHEN array_length(string_to_array(full_name, ' '), 1) = 2 THEN
                    TRIM(SPLIT_PART(full_name, ' ', 2))
                WHEN array_length(string_to_array(full_name, ' '), 1) = 3 THEN
                    TRIM(SPLIT_PART(full_name, ' ', 3))
                ELSE
                    TRIM(SUBSTRING(full_name FROM POSITION(' ' IN full_name) + 1))
            END
        ELSE last_name 
    END,
    updated_at = NOW()
WHERE full_name IS NOT NULL 
  AND full_name != ''
  AND (first_name IS NULL OR last_name IS NULL);

-- Clean up any whitespace issues
UPDATE profiles 
SET 
    full_name = TRIM(full_name),
    first_name = TRIM(first_name),
    last_name = TRIM(last_name),
    middle_initial = TRIM(middle_initial),
    updated_at = NOW()
WHERE full_name IS NOT NULL OR first_name IS NOT NULL OR last_name IS NOT NULL OR middle_initial IS NOT NULL;

-- Ensure no completely empty names
UPDATE profiles 
SET full_name = email,
    updated_at = NOW()
WHERE (full_name IS NULL OR full_name = '') 
  AND (first_name IS NULL OR first_name = '') 
  AND (last_name IS NULL OR last_name = '') 
  AND email IS NOT NULL;
