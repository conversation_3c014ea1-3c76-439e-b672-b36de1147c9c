"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { createBrowserClient } from '@supabase/ssr'
import type { User, Session, AuthError } from '@supabase/supabase-js'
import { IsolatedStorageAdapter } from '../../lib/supabase/storage-adapter'
import { AuthCookieManager } from '../../lib/auth/cookie-manager'
import { logWithContext } from '../../lib/utils/logger'

type UserProfile = {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: 'customer' | 'admin' | 'super_admin'
  avatar_url: string | null
  created_at: string
  updated_at: string
}

type AdminAuthContextType = {
  user: User | null
  session: Session | null
  profile: UserProfile | null
  loading: boolean
  isAdmin: boolean
  isSuperAdmin: boolean
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<{ error: AuthError | null }>
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>
}

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined)

// Create a separate Supabase client instance for admin with a different storage key
let adminSupabaseInstance: ReturnType<typeof createBrowserClient> | null = null

function createAdminClient() {
  if (adminSupabaseInstance) {
    return adminSupabaseInstance
  }
  
  // Create isolated storage adapter for admin auth
  const storageAdapter = new IsolatedStorageAdapter('sb-admin-auth-token')
  
  // Use isolated storage with separate storage key for admin auth
  adminSupabaseInstance = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false, // Disable for better performance
        flowType: 'pkce',
        storageKey: 'sb-admin-auth-token', // Separate storage key for admin
        storage: storageAdapter,
        debug: false // Disable debug logs for better performance
      }
    }
  )
  
  return adminSupabaseInstance
}

export function AdminAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createAdminClient()

  // Initialize cookie manager for admin context
  const cookieManager = React.useMemo(() => {
    if (typeof window !== 'undefined') {
      return AuthCookieManager.getInstance('admin')
    }
    return null
  }, [])

  // Debug: Log storage key being used
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && cookieManager) {
      logWithContext('AdminAuth', '🔑 Using storage key:', 'sb-admin-auth-token')
      logWithContext('AdminAuth', '🔍 Available localStorage keys:', Object.keys(localStorage).filter(k => k.includes('sb-')))
      
      // Run diagnostics
      const diagnostics = cookieManager.getDiagnostics()
      logWithContext('AdminAuth', '🔍 Diagnostics:', diagnostics)
      
      // Prevent cross-contamination
      cookieManager.preventCrossContamination()
    }
  }, [cookieManager])

  const isAdmin = profile?.role === 'admin' || profile?.role === 'super_admin'
  const isSuperAdmin = profile?.role === 'super_admin'

  // Add a timeout to prevent infinite loading during session restoration
  useEffect(() => {
    const timeout = setTimeout(() => {
      setLoading(currentLoading => {
        if (currentLoading) {
          logWithContext('AdminAuth', '⏰ Auth loading timeout reached - forcing loading to false', {
            hasUser: !!user,
            hasSession: !!session,
            hasProfile: !!profile,
            isAdmin,
            isSuperAdmin
          })
          return false
        }
        return currentLoading
      })
    }, 3000) // 3 second timeout - reasonable for session restoration

    return () => clearTimeout(timeout)
  }, []) // Remove dependencies to prevent timeout reset

  // Profile cache to avoid redundant API calls
  const profileCache = React.useRef<Map<string, UserProfile | null>>(new Map())
  
  // Optimized profile fetching with caching and immediate admin check
  const fetchProfile = React.useCallback(async (userId: string, userEmail: string): Promise<UserProfile | null> => {
    // Check cache first
    const cacheKey = `${userId}-${userEmail}`
    if (profileCache.current.has(cacheKey)) {
      return profileCache.current.get(cacheKey) || null
    }

    // Immediate super admin check for configured super admin email - skip DB call
    const superAdminEmail = process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAIL || '<EMAIL>'
    if (userEmail === superAdminEmail) {
      const adminProfile = {
        id: userId,
        email: userEmail,
        full_name: 'Super Admin',
        phone: null,
        role: 'super_admin' as const,
        avatar_url: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      profileCache.current.set(cacheKey, adminProfile)
      return adminProfile
    }

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.warn('Admin profile not found:', error.message)
        profileCache.current.set(cacheKey, null)
        return null
      }

      // Only return the profile if it's an admin or super_admin
      const userProfile = data as UserProfile
      if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
        profileCache.current.set(cacheKey, userProfile)
        return userProfile
      }
      
      profileCache.current.set(cacheKey, null)
      return null // Not an admin user
    } catch (error) {
      console.error('Error fetching admin profile:', error)
      profileCache.current.set(cacheKey, null)
      return null
    }
  }, [])

  useEffect(() => {
    let isMounted = true;
    
    // Get initial session
    const getSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (!isMounted) return;
        
        if (error) {
          console.warn('Admin session error:', error.message)
          logWithContext('AdminAuth', 'Session fetch error:', error)
          // Don't set loading=false immediately on session error during initial check
          // Let the auth state change listener handle the final loading state
          if (process.env.NODE_ENV === 'development') {
            logWithContext('AdminAuth', '⚠️ Session error during initial check, waiting for auth state change...', error)
          }
          return
        }

        logWithContext('AdminAuth', 'Initial session check:', {
          hasSession: !!session,
          userId: session?.user?.id,
          email: session?.user?.email,
          storageKey: 'sb-admin-auth-token'
        })

        // If no client-side session, try to restore from server-side cookies
        if (!session) {
          if (process.env.NODE_ENV === 'development') {
            logWithContext('AdminAuth', '🔄 No client session found, attempting server-side session restoration...')
          }

          try {
            // Call server endpoint to check for server-side session
            const response = await fetch('/api/auth/restore-session', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ context: 'admin' })
            })

            if (response.ok) {
              const { session: serverSession } = await response.json()
              if (serverSession) {
                if (process.env.NODE_ENV === 'development') {
                  logWithContext('AdminAuth', '✅ Server session found, restoring to client...', {
                    userId: serverSession.user?.id,
                    email: serverSession.user?.email,
                    userMetadata: serverSession.user?.user_metadata,
                    expiresAt: serverSession.expires_at
                  })
                }

                // Set the session in the client - this will trigger SIGNED_IN event
                await supabase.auth.setSession({
                  access_token: serverSession.access_token,
                  refresh_token: serverSession.refresh_token
                })

                // The auth state change listener will handle the rest
                return
              }
            }

            if (process.env.NODE_ENV === 'development') {
              logWithContext('AdminAuth', '❌ No server session found or restoration failed')
            }
          } catch (restoreError) {
            if (process.env.NODE_ENV === 'development') {
              logWithContext('AdminAuth', '⚠️ Server session restoration error:', restoreError)
            }
          }
        }

        if (session) {
          // Quick validation - defer heavy operations (customer auth pattern)
          if (cookieManager) {
            const isValidSession = cookieManager.validateSession(session)
            if (process.env.NODE_ENV === 'development') {
              logWithContext('AdminAuth', '🔍 Session validation:', {
                isValid: isValidSession,
                userId: session.user?.id,
                email: session.user?.email,
                hasSession: !!session
              })
            }

            if (!isValidSession) {
              if (process.env.NODE_ENV === 'development') {
                logWithContext('AdminAuth', '❌ Invalid session detected in admin context, clearing session')
              }
              cookieManager.clearAuthData()
              await supabase.auth.signOut()
              // Don't set loading=false immediately when clearing invalid session
              // Let the auth state change listener handle the final loading state
              if (process.env.NODE_ENV === 'development') {
                logWithContext('AdminAuth', '⚠️ Invalid session cleared, waiting for auth state change...')
              }
              return
            }
          }

          // Set session and user immediately for faster UI response (customer auth pattern)
          if (isMounted) {
            setSession(session)
            setUser(session.user)

            // Set immediate fallback profile to prevent loading delays (customer auth pattern)
            const superAdminEmail = process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAIL || '<EMAIL>'
            const isSuper = session.user.email === superAdminEmail
            const immediateProfile: UserProfile = {
              id: session.user.id,
              email: session.user.email || '',
              full_name: isSuper ? 'Super Admin' : 'Admin',
              phone: null,
              role: isSuper ? 'super_admin' : 'admin',
              avatar_url: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
            setProfile(immediateProfile)
            setLoading(false) // Set loading to false immediately like customer auth
          }

          // Sync initial session to server-side cookies for server actions with admin context
          try {
            await fetch('/api/auth/callback', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ session, context: 'admin' }),
            });
          } catch (error) {
            if (process.env.NODE_ENV === 'development') {
              console.warn('Failed to sync initial admin session to server:', error);
            }
          }

          // Fetch actual profile asynchronously in background (customer auth pattern)
          fetchProfile(session.user.id, session.user.email || '').then(async userProfile => {
            if (!isMounted) return;

            // Reject non-admin users in admin context
            if (userProfile && userProfile.role === 'customer') {
              if (process.env.NODE_ENV === 'development') {
                logWithContext('AdminAuth', 'Customer user detected in admin auth context, signing out')
              }
              await supabase.auth.signOut()
              setSession(null)
              setUser(null)
              setProfile(null)
              return
            }

            // Update with actual profile data if different from fallback (customer auth pattern)
            if (userProfile && (userProfile.role === 'admin' || userProfile.role === 'super_admin')) {
              logWithContext('AdminAuth', 'Admin profile fetched successfully:', userProfile)
              setProfile(userProfile)
            }
          }).catch(error => {
            if (process.env.NODE_ENV === 'development') {
              console.error('Failed to fetch admin profile:', error)
            }
            // Keep the fallback profile - no need to update (customer auth pattern)
          })
        } else {
          // Don't set loading=false immediately when no session found during initial check
          // This prevents premature redirects during page reload when session is being restored
          // Let the auth state change listener handle the final loading state
          if (process.env.NODE_ENV === 'development') {
            logWithContext('AdminAuth', '⏳ No session found in initial check, waiting for auth state change...')
          }
        }
      } catch (error) {
        console.warn('Admin session fetch error:', error)
        // Don't set loading=false immediately on error during initial session check
        // Let the auth state change listener handle the final loading state
        if (process.env.NODE_ENV === 'development') {
          logWithContext('AdminAuth', '⚠️ Session fetch error, waiting for auth state change...', error)
        }
      }
    }

    getSession()

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event: any, session: any) => {
      if (!isMounted) return;
      logWithContext('AdminAuth', 'Auth state change:', {
        event,
        email: session?.user?.email,
        hasSession: !!session,
        sessionExpiry: session?.expires_at
      })

      try {
        // Sync auth state to server cookies for SSR/server actions with admin context
        if (session && (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED')) {
          try {
            await fetch('/api/auth/callback', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ session, context: 'admin' }),
            });
          } catch (syncErr) {
            if (process.env.NODE_ENV === 'development') {
              console.warn('Failed to sync admin session to server:', syncErr);
            }
          }
        }

        if (event === 'SIGNED_OUT' || (!session && event !== 'INITIAL_SESSION')) {
          logWithContext('AdminAuth', 'Handling admin sign out event')
          setSession(null)
          setUser(null)
          setProfile(null)
          setLoading(false)
          return
        }

        // Handle INITIAL_SESSION with no session (page reload case)
        if (!session && event === 'INITIAL_SESSION') {
          if (process.env.NODE_ENV === 'development') {
            logWithContext('AdminAuth', '🔄 INITIAL_SESSION with no session - session may still be restoring from localStorage...')
          }
          // Don't set loading=false yet, wait for session restoration or timeout
          // This prevents premature redirects during page reload
          return
        }

        // Handle any session with a user
        if (session?.user && (event === 'INITIAL_SESSION' || event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED')) {
          logWithContext('AdminAuth', `Handling admin ${event} event with session`)
          

          
          // Set session and user immediately (customer auth pattern)
          setSession(session)
          setUser(session.user)

          // Set immediate fallback profile to prevent loading delays (customer auth pattern)
          const superAdminEmail = process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAIL || '<EMAIL>'
          const isSuper = session.user.email === superAdminEmail
          const immediateProfile: UserProfile = {
            id: session.user.id,
            email: session.user.email || '',
            full_name: isSuper ? 'Super Admin' : 'Admin',
            phone: null,
            role: isSuper ? 'super_admin' : 'admin',
            avatar_url: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
          setProfile(immediateProfile)
          setLoading(false) // Set loading to false immediately like customer auth

          // Fetch actual profile asynchronously in background (customer auth pattern)
          fetchProfile(session.user.id, session.user.email || '').then(async userProfile => {
            if (!isMounted) return;

            // Reject non-admin users in admin context
            if (userProfile && userProfile.role === 'customer') {
              if (process.env.NODE_ENV === 'development') {
                logWithContext('AdminAuth', 'Customer user detected in admin auth context, signing out')
              }
              await supabase.auth.signOut()
              setSession(null)
              setUser(null)
              setProfile(null)
              return
            }

            // Update with actual profile data if different from fallback (customer auth pattern)
            if (userProfile && JSON.stringify(userProfile) !== JSON.stringify(immediateProfile)) {
              logWithContext('AdminAuth', 'Admin profile fetched successfully:', userProfile)
              setProfile(userProfile)
            }
          }).catch(error => {
            if (process.env.NODE_ENV === 'development') {
              console.error('Failed to fetch admin profile:', error)
            }
            // Keep the fallback profile - no need to update (customer auth pattern)
          })
        } else if (event !== 'SIGNED_OUT') {
          // Only clear state for actual sign out events, not other auth state changes
          logWithContext('AdminAuth', 'Ignoring auth state change:', event)
        }
      } catch (error) {
        console.error('Error in admin auth state change handler:', error)
        setLoading(false)
      }
    })

    return () => {
      isMounted = false;
      subscription.unsubscribe()
      // Clean up cookie manager to prevent memory leaks
      if (cookieManager) {
        cookieManager.clearCacheTimeouts()
      }
    }
  }, []) // Remove cookieManager dependency to prevent re-runs

  const signIn = async (email: string, password: string) => {
    if (process.env.NODE_ENV === 'development') {
      logWithContext('AdminAuth', `🔐 Starting signIn for email: ${email}`)

      // Test storage adapter before login
      const storageAdapter = new IsolatedStorageAdapter('sb-admin-auth-token')
      try {
        await storageAdapter.setItem('test-key', 'test-value')
        const testValue = await storageAdapter.getItem('test-key')
        logWithContext('AdminAuth', `🧪 Storage adapter test: ${testValue === 'test-value' ? 'WORKING' : 'FAILED'}`)
        await storageAdapter.removeItem('test-key')
      } catch (testError) {
        logWithContext('AdminAuth', `🧪 Storage adapter test ERROR:`, testError)
      }
    }

    const { error, data } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (process.env.NODE_ENV === 'development') {
      logWithContext('AdminAuth', `🔐 SignIn result: error=${error?.message || 'none'}, hasUser=${!!data.user}, hasSession=${!!data.session}`)

      // Check localStorage after login attempt
      const allKeys = Object.keys(localStorage)
      const adminKeys = allKeys.filter(k => k.includes('sb-admin-auth-token'))
      logWithContext('AdminAuth', `🔐 Post-login localStorage check: total=${allKeys.length}, admin=${adminKeys.length}`)
      if (adminKeys.length > 0) {
        logWithContext('AdminAuth', `🔐 Admin keys found:`, adminKeys)
      }
    }

    return { error }
  }

  const signOut = async () => {
    // Clear cookie manager data first
    if (cookieManager) {
      cookieManager.clearAuthData()
    }
    
    const { error } = await supabase.auth.signOut()
    
    // Clear local state
    setUser(null)
    setSession(null)
    setProfile(null)
    
    return { error }
  }

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/admin/reset-password`,
    })
    return { error }
  }

  const contextValue: AdminAuthContextType = {
    user,
    session,
    profile,
    loading,
    isAdmin,
    isSuperAdmin,
    signIn,
    signOut,
    resetPassword,
  }

  return <AdminAuthContext.Provider value={contextValue}>{children}</AdminAuthContext.Provider>
}

export function useAdminAuth() {
  const context = useContext(AdminAuthContext)
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider')
  }
  return context
}
