"use server";

import { createContextClient } from "@/lib/supabase/server";

interface BookingDocument {
  id: string;
  booking_id: string;
  document_type: string;
  file_url: string;
  file_name: string;
  file_size: number;
  file_type: string;
  verification_status: "pending" | "approved" | "rejected" | "requires_resubmission";
  verification_notes?: string;
  verified_by?: string;
  verified_at?: string;
  created_at: string;
  updated_at: string;
}

export async function getBookingDocuments(bookingId: string) {
  const supabase = await createContextClient('admin');

  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Debug auth visibility in server action
  if (process.env.NODE_ENV === 'development') {
    console.log("getBookingDocuments - Auth debug:", {
      user: user ? { id: user.id, email: user.email } : null,
      bookingId,
    });
  }

  if (!user) {
    return { error: { message: "You must be logged in to view documents." } };
  }

  const { data, error } = await supabase
    .from("booking_documents")
    .select(`
      id,
      booking_id,
      document_type,
      file_url,
      file_name,
      file_size,
      file_type,
      verification_status,
      verification_notes,
      verified_by,
      verified_at,
      created_at,
      updated_at
    `)
    .eq("booking_id", bookingId)
    .order("created_at", { ascending: true });

  if (error) {
    console.error("Error fetching booking documents:", error);
    return { error };
  }

  return { data: data as BookingDocument[] };
}

export async function verifyBookingDocument(
  documentId: string,
  action: "approve" | "reject",
  notes?: string
) {
  const supabase = await createContextClient('admin');

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { error: { message: "You must be logged in to verify documents." } };
  }

  // Check if user is admin
  const { data: profile } = await supabase
    .from("profiles")
    .select("role")
    .eq("id", user.id)
    .single();

  if (!profile || (profile.role !== "admin" && profile.role !== "super_admin")) {
    return { error: { message: "Only admins can verify documents." } };
  }

  const verification_status = action === "approve" ? "approved" : "rejected";
  
  const { data, error } = await supabase
    .from("booking_documents")
    .update({
      verification_status,
      verification_notes: notes || null,
      verified_by: user.id,
      verified_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq("id", documentId)
    .select()
    .single();

  if (error) {
    console.error("Error updating document verification:", error);
    return { error };
  }

  return { data };
}

export async function getBookingDocumentStatus(bookingId: string) {
  const supabase = await createContextClient('admin');

  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Debug auth visibility in server action
  if (process.env.NODE_ENV === 'development') {
    console.log("getBookingDocumentStatus - Auth debug:", {
      user: user ? { id: user.id, email: user.email } : null,
      bookingId,
    });
  }

  if (!user) {
    return { error: { message: "You must be logged in to view document status." } };
  }

  // Use the helper function from our schema
  const { data, error } = await supabase.rpc('get_booking_document_status', {
    booking_uuid: bookingId
  });

  if (error) {
    console.error("Error fetching document status:", error);
    return { error };
  }

  return { data: data[0] || null };
}

export async function requireDocumentResubmission(
  documentId: string,
  notes: string
) {
  const supabase = await createContextClient('admin');

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { error: { message: "You must be logged in to request resubmission." } };
  }

  // Check if user is admin
  const { data: profile } = await supabase
    .from("profiles")
    .select("role")
    .eq("id", user.id)
    .single();

  if (!profile || (profile.role !== "admin" && profile.role !== "super_admin")) {
    return { error: { message: "Only admins can request document resubmission." } };
  }

  const { data, error } = await supabase
    .from("booking_documents")
    .update({
      verification_status: "requires_resubmission",
      verification_notes: notes,
      verified_by: user.id,
      verified_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq("id", documentId)
    .select()
    .single();

  if (error) {
    console.error("Error requesting document resubmission:", error);
    return { error };
  }

  return { data };
}
