import { PublicAppShell } from "@/components/layout/public-app-shell"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { CardSkeleton } from "@/components/ui/loading"
import { CarSearchLoading } from "@/components/ui/loading"

export default function CatalogLoading() {
  return (
    <PublicAppShell>
      <div className="space-y-8 px-4 md:px-6 py-8">
        {/* Header skeleton */}
        <div className="text-center space-y-4">
          <div className="h-12 bg-gray-200 rounded w-64 mx-auto animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
        </div>

        {/* Stats skeleton */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
          {Array.from({ length: 4 }, (_, i) => (
            <Card key={i} className="text-center p-4">
              <div className="h-8 bg-gray-200 rounded w-8 mx-auto mb-2 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-16 mx-auto animate-pulse"></div>
            </Card>
          ))}
        </div>

        {/* Search form skeleton */}
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <div className="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              {Array.from({ length: 3 }, (_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                  <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Main content */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Available Vehicles</CardTitle>
          </CardHeader>
          <CardContent>
            {/* Loading state with search indicator */}
            <div className="flex flex-col items-center justify-center py-12">
              <CarSearchLoading />
            </div>
            
            {/* Skeleton cards */}
            <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 mt-8">
              {Array.from({ length: 8 }, (_, i) => (
                <CardSkeleton key={i} />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </PublicAppShell>
  )
}
