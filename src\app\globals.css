@import "tailwindcss";
@import "tw-animate-css";

/* Removed invalid @custom-variant dark (&:is(.dark *)); */

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

/* Tailwind theme configuration - moved to tailwind.config.js */

@layer base {
  * {
    border-color: hsl(var(--border));
    outline-color: hsl(var(--ring) / 0.5);
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

/* Card System Utilities */
@layer components {
  /* 4px Spacing Scale for Cards */
  .card-spacing-xs {
    padding: 0.25rem;
  } /* 4px */
  .card-spacing-sm {
    padding: 0.5rem;
  } /* 8px */
  .card-spacing-md {
    padding: 0.75rem;
  } /* 12px */
  .card-spacing-lg {
    padding: 1rem;
  } /* 16px */
  .card-spacing-xl {
    padding: 1.25rem;
  } /* 20px */
  .card-spacing-2xl {
    padding: 1.5rem;
  } /* 24px */

  /* Content Hierarchy */
  .card-primary-content {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.25rem;
  }

  .card-secondary-content {
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    opacity: 0.8;
  }

  /* Responsive Card Grid */
  .card-grid {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .card-grid-auto {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  /* Responsive Breakpoints */
  @media (min-width: 320px) {
    .card-grid {
      grid-template-columns: 1fr;
    }
  }

  @media (min-width: 768px) {
    .card-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .card-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (min-width: 1440px) {
    .card-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}

/* Car Card Component Styles - Overflow Prevention and Text Handling */
@layer components {
  /* Card Container - Uniform Height Grid */
  .card-container {
    /* Ensure all grid items have equal height */
    display: grid;
    grid-auto-rows: 1fr; /* Make all rows the same height */
    align-items: stretch; /* Stretch all items to fill grid cell height */
  }

  .card-container > * {
    /* Ensure each card stretches to fill the grid cell */
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 100%;
  }

  /* Alternative approach for better browser support */
  @supports not (grid-auto-rows: 1fr) {
    .card-container {
      display: flex;
      flex-wrap: wrap;
      align-items: stretch;
    }

    .card-container > * {
      flex: 1 1 calc(33.333% - 1.5rem);
      min-width: 280px;
      margin: 0.75rem;
    }

    @media (max-width: 1279px) {
      .card-container > * {
        flex: 1 1 calc(50% - 1.5rem);
      }
    }

    @media (max-width: 639px) {
      .card-container > * {
        flex: 1 1 calc(100% - 1.5rem);
      }
    }
  }

  .car-card {
    /* Ensure the card has proper containment and full height */
    contain: layout style paint;
    word-wrap: break-word;
    overflow-wrap: break-word;
    height: 100% !important; /* Force full height */
    display: flex;
    flex-direction: column;
  }

  .car-card * {
    /* Prevent any child element from overflowing */
    max-width: 100%;
    box-sizing: border-box;
  }

  .car-card img {
    /* Responsive images that never overflow */
    max-width: 100%;
    height: auto;
    object-fit: contain;
  }

  /* Card Body - Flexible Content Area */
  .car-card .card-body {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
  }

  .car-card .card-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .car-card .card-footer {
    margin-top: auto;
    flex-shrink: 0;
  }

  /* Enhanced line clamping for better browser support */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  /* Fallback for browsers that don't support -webkit-line-clamp */
  @supports not (-webkit-line-clamp: 2) {
    .line-clamp-2 {
      max-height: 2.5em; /* Approximate height for 2 lines */
      overflow: hidden;
      position: relative;
    }

    .line-clamp-2::after {
      content: "...";
      position: absolute;
      bottom: 0;
      right: 0;
      background: white;
      padding-left: 0.25em;
    }

    .line-clamp-3 {
      max-height: 3.75em; /* Approximate height for 3 lines */
      overflow: hidden;
      position: relative;
    }

    .line-clamp-3::after {
      content: "...";
      position: absolute;
      bottom: 0;
      right: 0;
      background: white;
      padding-left: 0.25em;
    }
  }

  /* Ensure text never breaks container bounds */
  .break-words-strict {
    word-break: break-all;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Responsive button text handling */
  .car-card .responsive-buttons {
    min-width: 0;
  }

  .car-card .responsive-buttons button {
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Grid items should never overflow */
  .car-card .grid > * {
    min-width: 0;
    overflow: hidden;
  }

  /* Sidebar Active Navigation Styles */
  .active-link {
    position: relative;
    background-color: rgb(239 246 255); /* blue-50 */
    color: rgb(29 78 216); /* blue-700 */
    font-weight: 500;
    border-right: 2px solid rgb(37 99 235); /* blue-600 */
  }

  .active-link::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background-color: rgb(37 99 235); /* blue-600 */
    border-radius: 0 2px 2px 0;
  }

  .active-link:hover {
    background-color: rgb(239 246 255); /* blue-50 */
    color: rgb(29 78 216); /* blue-700 */
  }

  /* Admin Sidebar Active Navigation Styles */
  .admin-nav-active {
    position: relative;
    background-color: rgb(239 246 255); /* blue-50 */
    color: rgb(29 78 216); /* blue-700 */
    font-weight: 500;
    border-right: 2px solid rgb(37 99 235); /* blue-600 */
  }

  .admin-nav-active::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background-color: rgb(37 99 235); /* blue-600 */
    border-radius: 0 2px 2px 0;
  }

  .admin-nav-active:hover {
    background-color: rgb(239 246 255); /* blue-50 */
    color: rgb(29 78 216); /* blue-700 */
  }

  /* Sidebar Expand/Collapse Button Styles */
  .sidebar-toggle-btn {
    transition: all 200ms ease-in-out;
    backdrop-filter: blur(8px);
  }

  .sidebar-toggle-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .sidebar-toggle-btn:focus {
    outline: 2px solid rgb(59 130 246); /* blue-500 */
    outline-offset: 2px;
  }

  /* Sidebar transition improvements */
  .admin-sidebar {
    transition: width 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Tooltip improvements for collapsed sidebar */
  [data-radix-tooltip-content] {
    animation-duration: 200ms;
    animation-timing-function: ease-out;
  }

  /* Enhanced Modal Improvements */
  [data-radix-dialog-content] {
    /* Ensure modal doesn't exceed viewport */
    max-height: 90vh;
    /* Better positioning on desktop */
    margin: 1rem;
  }

  /* Mobile responsive modal */
  @media (max-width: 640px) {
    [data-radix-dialog-content] {
      max-height: 95vh;
      height: auto;
      margin: 0.5rem;
      border-radius: 0.75rem;
      width: calc(100vw - 1rem);
      max-width: calc(100vw - 1rem);
    }
  }

  /* Very small screens */
  @media (max-height: 600px) {
    [data-radix-dialog-content] {
      max-height: 98vh;
      margin: 0.25rem;
    }
  }

  /* Enhanced background blur and darkening */
  [data-radix-dialog-overlay] {
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
  }

  /* Improve dropdown positioning within modal */
  [data-radix-select-content] {
    /* Ensure dropdown doesn't exceed modal bounds */
    max-height: min(200px, 40vh);
    z-index: 100;
  }

  /* Booking Modal Specific Styles */
  .booking-modal {
    /* Ensure proper flex layout */
    display: flex !important;
    flex-direction: column !important;
    /* Constrain height properly */
    max-height: 85vh !important;
    height: auto !important;
    /* Ensure modal doesn't grow beyond viewport */
    overflow: hidden !important;
  }

  /* Ensure scrollable content in booking modal */
  .booking-modal .flex-1 {
    /* Force scrolling in the content area */
    overflow-y: auto !important;
    max-height: calc(85vh - 280px) !important;
    /* Smooth scrolling */
    scroll-behavior: smooth;
    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }

  /* Webkit scrollbar styling for booking modal */
  .booking-modal .flex-1::-webkit-scrollbar {
    width: 6px;
  }

  .booking-modal .flex-1::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
  }

  .booking-modal .flex-1::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.4);
    border-radius: 10px;
    transition: background 0.2s ease;
  }

  .booking-modal .flex-1::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.6);
  }

  @media (max-width: 640px) {
    .booking-modal {
      max-height: 95vh !important;
    }

    .booking-modal .flex-1 {
      max-height: calc(95vh - 260px) !important;
    }
  }

  @media (max-height: 600px) {
    .booking-modal {
      max-height: 98vh !important;
    }

    .booking-modal .flex-1 {
      max-height: calc(98vh - 240px) !important;
    }
  }

  /* Fix Select trigger text overflow in booking modal */
  .booking-modal [data-radix-select-trigger] {
    /* Override default w-fit with full width */
    width: 100% !important;
    min-height: 1.5rem;
    justify-content: flex-start !important;
  }

  .booking-modal [data-radix-select-value] {
    /* Prevent text overflow in select value */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    text-align: left;
    line-height: 1.5;
  }

  /* Ensure the select trigger icon stays on the right */
  .booking-modal [data-radix-select-trigger] > [data-radix-select-icon] {
    margin-left: auto;
  }

  /* Modern Input Field Styling */
  .modern-input-container {
    position: relative;
  }

  .modern-input-container::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transform: scaleX(0);
    transition: transform 0.3s ease;
    border-radius: 1px;
  }

  .modern-input-container:focus-within::after {
    transform: scaleX(1);
  }

  /* Enhanced Rent Now Button Styles */
  .rent-now-button {
    position: relative;
    overflow: hidden;
    border: none;
    font-weight: 700;
    letter-spacing: 0.025em;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .rent-now-button:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3),
      0 4px 6px -2px rgba(59, 130, 246, 0.1);
  }

  .rent-now-button:not(:disabled):active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 4px 15px -3px rgba(59, 130, 246, 0.2);
  }

  .rent-now-button:disabled {
    background-color: #d1d5db !important;
    color: #6b7280 !important;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }

  .rent-now-button:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
  }

  /* Enhance symmetrical layout for booking forms */
  .booking-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    align-items: start;
  }

  .booking-form-field {
    min-height: 3.5rem;
    display: flex;
    align-items: center;
  }

  /* Mobile optimizations for modal */
  @media (max-height: 600px) {
    [data-radix-dialog-content] {
      max-height: 98vh;
      margin: 0.25rem;
    }
  }

  /* Very small screens */
  @media (max-height: 500px) {
    [data-radix-dialog-content] {
      max-height: 99vh;
      margin: 0.125rem;
    }
  }
}

/* Typography System */

/* Font pairing: Geist Sans for all text with fallbacks */
.font-heading {
  font-family: var(--font-sans), -apple-system, BlinkMacSystemFont, "Segoe UI",
    "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
}

.font-body {
  font-family: var(--font-sans), -apple-system, BlinkMacSystemFont, "Segoe UI",
    "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif;
  font-feature-settings: "kern" 1, "liga" 1;
}

/* Typography scale with proper contrast */
.text-display-1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  line-height: 1.1;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.text-display-2 {
  font-size: clamp(2rem, 4vw, 3rem);
  line-height: 1.2;
  font-weight: 700;
  letter-spacing: -0.01em;
}

/* Optimal reading widths - 50-60 characters on desktop, 30-40 on mobile */
.prose-optimal {
  max-width: 65ch; /* ~50-60 characters */
}

@media (max-width: 640px) {
  .prose-optimal {
    max-width: 35ch; /* ~30-40 characters on mobile */
  }
}

/* Text contrast classes with WCAG AA compliance */
.text-high-contrast {
  color: #1a1a1a; /* 16.94:1 contrast ratio */
}

.text-medium-contrast {
  color: #4a4a4a; /* 9.35:1 contrast ratio */
}

.text-low-contrast {
  color: #6b7280; /* 4.54:1 contrast ratio - minimum AA */
}

/* Typography spacing system */
.space-text-tight > * + * {
  margin-top: 0.5rem;
}

.space-text-normal > * + * {
  margin-top: 1rem;
}

.space-text-loose > * + * {
  margin-top: 1.5rem;
}

.space-text-extra-loose > * + * {
  margin-top: 2rem;
}

/* Heading spacing - tighter above, looser below */
.heading-spacing {
  margin-top: 2em;
  margin-bottom: 0.5em;
}

.heading-spacing:first-child {
  margin-top: 0;
}

/* Line height adjustments for different text sizes */
.leading-display {
  line-height: 1.1;
}

.leading-heading {
  line-height: 1.25;
}

.leading-body {
  line-height: 1.6;
}

.leading-tight {
  line-height: 1.4;
}

/* Responsive Design System */

/* Container system with max-widths for different breakpoints */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container-responsive {
    max-width: 1400px;
  }
}

@media (min-width: 1920px) {
  .container-responsive {
    max-width: 1600px;
  }
}

@media (min-width: 2560px) {
  .container-responsive {
    max-width: 1800px;
  }
}

/* Fluid grid system */
.grid-responsive {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .grid-responsive {
    gap: 1.25rem;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    gap: 1.5rem;
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .grid-responsive {
    gap: 2rem;
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1920px) {
  .grid-responsive {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Touch targets - mobile-first approach */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

@media (min-width: 768px) {
  .touch-target {
    min-height: 40px;
    min-width: 40px;
  }
}

@media (min-width: 1280px) {
  .touch-target {
    min-height: 36px;
    min-width: 36px;
  }
}

/* Responsive text sizes */
.text-responsive-display {
  font-size: 2rem;
  line-height: 1.1;
  font-weight: 700;
}

@media (min-width: 768px) {
  .text-responsive-display {
    font-size: 3rem;
  }
}

@media (min-width: 1280px) {
  .text-responsive-display {
    font-size: 4rem;
  }
}

@media (min-width: 1920px) {
  .text-responsive-display {
    font-size: 5rem;
  }
}

.text-responsive-heading {
  font-size: 1.5rem;
  line-height: 1.25;
  font-weight: 600;
}

@media (min-width: 768px) {
  .text-responsive-heading {
    font-size: 2rem;
  }
}

@media (min-width: 1280px) {
  .text-responsive-heading {
    font-size: 2.5rem;
  }
}

@media (min-width: 1920px) {
  .text-responsive-heading {
    font-size: 3rem;
  }
}

.text-responsive-body {
  font-size: 0.875rem;
  line-height: 1.6;
}

@media (min-width: 768px) {
  .text-responsive-body {
    font-size: 1rem;
  }
}

@media (min-width: 1280px) {
  .text-responsive-body {
    font-size: 1.125rem;
  }
}

@media (min-width: 1920px) {
  .text-responsive-body {
    font-size: 1.25rem;
  }
}

/* Responsive spacing utilities */
.space-responsive-y > * + * {
  margin-top: 1rem;
}

@media (min-width: 768px) {
  .space-responsive-y > * + * {
    margin-top: 1.5rem;
  }
}

@media (min-width: 1280px) {
  .space-responsive-y > * + * {
    margin-top: 2rem;
  }
}

/* Content visibility utilities */
.mobile-only {
  display: block;
}

@media (min-width: 640px) {
  .mobile-only {
    display: none;
  }
}

.tablet-only {
  display: none;
}

@media (min-width: 768px) and (max-width: 1279px) {
  .tablet-only {
    display: block;
  }
}

.desktop-only {
  display: none;
}

@media (min-width: 1280px) {
  .desktop-only {
    display: block;
  }
}

.hide-mobile {
  display: none;
}

@media (min-width: 640px) {
  .hide-mobile {
    display: block;
  }
}

/* Responsive image utilities */
.img-responsive {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.img-responsive-contain {
  width: 100%;
  height: auto;
  object-fit: contain;
}

/* Aspect ratio utilities for different screen sizes */
.aspect-responsive {
  aspect-ratio: 1 / 1;
}

@media (min-width: 768px) {
  .aspect-responsive {
    aspect-ratio: 4 / 3;
  }
}

@media (min-width: 1280px) {
  .aspect-responsive {
    aspect-ratio: 16 / 9;
  }
}

/* Layout utilities for different screen sizes */
.layout-mobile {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .layout-tablet {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1280px) {
  .layout-desktop {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* Responsive navigation */
.nav-mobile {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem;
}

@media (min-width: 768px) {
  .nav-desktop {
    position: sticky;
    top: 0;
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 2rem;
  }
}

/* Smooth page transitions and loading states */
html {
  scroll-behavior: smooth;
}

body {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Prevent layout shift during loading */
.page-container {
  min-height: 100vh;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-container.loading {
  opacity: 0.8;
  transform: translateY(10px);
}

/* Enhanced button transitions */
button,
a[role="button"] {
  transition: all 0.2s ease-in-out;
}

button:focus-visible,
a[role="button"]:focus-visible {
  outline: 2px solid theme("colors.blue.500");
  outline-offset: 2px;
  transition: outline 0.15s ease;
}

/* Card hover effects */
.card-hover {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
}


/* Improve text contrast for accessibility */
.text-contrast-high {
  color: #1f2937;
}

.text-contrast-medium {
  color: #4b5563;
}

.bg-contrast-surface {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
}

/* Navigation transitions */
.nav-link {
  position: relative;
  transition: color 0.2s ease;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: theme("colors.blue.600");
  transition: width 0.2s ease;
}

.nav-link:hover::after {
  width: 100%;
}

/* Ensure proper contrast ratios (WCAG AA compliance) */
.btn-primary {
  background-color: #2563eb;
  color: #ffffff;
  border: 2px solid #2563eb;
}

.btn-primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
}

.btn-secondary {
  background-color: #ffffff;
  color: #2563eb;
  border: 2px solid #2563eb;
}

.btn-secondary:hover {
  background-color: #eff6ff;
  color: #1d4ed8;
}

.btn-secondary-inverse {
  background-color: transparent;
  color: #ffffff;
  border: 2px solid #ffffff;
}

.btn-secondary-inverse:hover {
  background-color: #ffffff;
  color: #1d4ed8;
}

/* Focus states for accessibility */
.focus-ring {
  transition: box-shadow 0.15s ease;
}

.focus-ring:focus-visible {
  box-shadow: 0 0 0 2px theme("colors.blue.500");
  outline: none;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Responsive Navigation Utilities */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.touch-manipulation {
  touch-action: manipulation;
}

/* Priority+ Navigation Utilities */
.priority-nav-container {
  position: relative;
  overflow: hidden;
}

.priority-nav-item {
  flex-shrink: 0;
  white-space: nowrap;
}

/* Bottom Navigation Styles */
.bottom-nav {
  transform: translateZ(0); /* Force hardware acceleration */
  will-change: transform;
}

/* Improved focus visibility for keyboard navigation */
.nav-focus-visible:focus-visible {
  outline: 2px solid theme("colors.blue.600");
  outline-offset: 2px;
  border-radius: theme("borderRadius.md");
}

/* Smooth transitions for navigation state changes */
.nav-transition {
  transition: background-color 200ms ease, color 200ms ease,
    transform 200ms ease, box-shadow 200ms ease;
}

/* Hover effects for touch devices */
@media (hover: hover) {
  .nav-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

/* Custom breakpoint utilities for navigation */
@media (max-width: 640px) {
  .nav-mobile-only {
    display: block !important;
  }
  .nav-tablet-up {
    display: none !important;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .nav-tablet-only {
    display: block !important;
  }
  .nav-mobile-only {
    display: none !important;
  }
  .nav-desktop-only {
    display: none !important;
  }
}

@media (min-width: 1024px) {
  .nav-desktop-only {
    display: block !important;
  }
  .nav-tablet-only {
    display: none !important;
  }
  .nav-mobile-only {
    display: none !important;
  }
}

/* Ensure bottom navigation doesn't interfere with iOS Safari */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .bottom-nav-safe {
    padding-bottom: calc(env(safe-area-inset-bottom) + 1rem);
  }
}

/* Performance optimizations for mobile navigation */
.nav-optimized {
  contain: layout style;
  content-visibility: auto;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .nav-transition {
    transition: none;
  }

  .nav-hover:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .nav-item {
    border: 1px solid;
  }
}

/* Dropdown menu visibility fixes */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
}

.responsive-navigation {
  overflow: visible !important;
}

/* Ensure dropdown menus are always visible */
[role="menu"],
[role="listbox"] {
  z-index: 9999 !important;
}

/* Fix dropdown menu transparency */
[data-slot="dropdown-menu-content"] {
  background-color: white !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  opacity: 1 !important;
}

/* Alternative selectors for dropdown content */
[data-radix-dropdown-menu-content] {
  background-color: white !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  opacity: 1 !important;
}

/* Target by role */
[role="menu"] {
  background-color: white !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  opacity: 1 !important;
}

/* Fix dropdown menu items */
[data-slot="dropdown-menu-item"] {
  background-color: transparent !important;
  color: #374151 !important;
}

[data-slot="dropdown-menu-item"]:hover {
  background-color: #f3f4f6 !important;
  color: #1f2937 !important;
}
