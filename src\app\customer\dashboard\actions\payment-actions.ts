"use server";

import { createContextClient } from "@/lib/supabase/server";
import { Payment } from "@/lib/types";

// Ensure errors are serializable and descriptive when returned to the client
function normalizeError(err: unknown) {
  if (!err) return { message: "Unknown error" };
  if (typeof err === "string") return { message: err };
  if (err instanceof Error) return { message: err.message, name: err.name };
  const anyErr = err as Record<string, unknown>;
  return {
    message: (anyErr.message as string) || "Unexpected error",
    code: anyErr.code as string | undefined,
    details: anyErr.details as string | undefined,
    hint: anyErr.hint as string | undefined,
    status: anyErr.status as number | undefined,
  };
}

export async function getCustomerPayments(): Promise<{
  data: Payment[] | null;
  error: any;
}> {
  const supabase = await createContextClient('customer');

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      data: null,
      error: { message: "You must be logged in to view payments." },
    };
  }

  // Get customer's bookings first
  const { data: bookings, error: bookingsError } = await supabase
    .from("bookings")
    .select("id")
    .eq("customer_id", user.id);

  if (bookingsError) {
    console.error("Error fetching customer bookings:", bookingsError);
    return { data: null, error: normalizeError(bookingsError) };
  }

  if (!bookings || bookings.length === 0) {
    return { data: [], error: null };
  }

  const bookingIds = bookings.map((b) => b.id);

  // Get payments for these bookings
  const { data: payments, error: paymentsError } = await supabase
    .from("payments")
    .select(`
      id,
      payment_ref,
      booking_id,
      amount,
      method,
      status,
      transaction_date,
      proof_of_payment_url,
      verification_notes,
      created_at,
      updated_at,
      bookings!inner (
        id,
        booking_ref
      )
    `)
    .in("booking_id", bookingIds)
    .order("created_at", { ascending: false });

  if (paymentsError) {
    console.error("Error fetching customer payments:", paymentsError);
    return { data: null, error: normalizeError(paymentsError) };
  }

  return { data: payments || [], error: null };
}

export async function getPaymentStatusForBooking(
  bookingId: string
): Promise<{ data: Payment | null; error: any }> {
  const supabase = await createContextClient('customer');

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      data: null,
      error: { message: "You must be logged in to view payment status." },
    };
  }

  // Verify the booking belongs to the current user
  const { data: booking, error: bookingError } = await supabase
    .from("bookings")
    .select("id, customer_id")
    .eq("id", bookingId)
    .eq("customer_id", user.id)
    .single();

  if (bookingError || !booking) {
    return {
      data: null,
      error: normalizeError(bookingError ?? { message: "Booking not found or access denied." }),
    };
  }

  const { data: payment, error: paymentError } = await supabase
    .from("payments")
    .select("*")
    .eq("booking_id", bookingId)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (paymentError) {
    console.error("Error fetching payment status:", paymentError);
    return { data: null, error: normalizeError(paymentError) };
  }

  return { data: payment ?? null, error: null };
}
