"use client";

import * as React from "react";
import {
  listCars,
  listArchivedCars,
  addCarAction,
  deleteCarAction,
  updateCarAction,
  archiveCarAction,
  unarchiveCarAction,
  type Car,
} from "./actions/car-actions";
import {
  listCategories,
  addCategoryAction,
  updateCategoryAction,
  deleteCategoryAction,
  type CategoryData,
} from "./actions/category-actions";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { ImageUpload } from "@/components/ui/image-upload";
import {
  Car as CarIcon,
  Users,
  Fuel,
  Cog,
  Archive,
  ArchiveRestore,
  Plus,
  Edit,
  Trash2,
  FolderOpen,
  RefreshCw,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { CategoryForm } from "./components/category-form";
import { syncCarAvailability } from "./actions/car-status-actions";
import { useToast } from "@/hooks/use-toast";

// Simplified styling functions - minimal color approach
const getVehicleTypeBadgeStyle = (type: string) => {
  // All vehicle types use neutral styling - no color differentiation needed
  return "font-medium border border-border text-muted-foreground bg-muted/20 text-sm";
};

const getStatusBadgeStyle = (status: string) => {
  // Only use color for critical status information
  const baseStyle = "font-medium border text-sm";
  switch (status) {
    case "Available":
      return `${baseStyle} border-emerald-300 text-emerald-700 bg-emerald-50`;
    case "Rented":
      return `${baseStyle} border-border text-muted-foreground bg-muted/20`;
    case "In Maintenance":
      return `${baseStyle} border-amber-300 text-amber-700 bg-amber-50`;
    default:
      return `${baseStyle} border-border text-muted-foreground bg-muted/20`;
  }
};

const getConditionBadgeStyle = (condition: string) => {
  // Only highlight problems - good condition is neutral
  const baseStyle = "font-medium border text-sm";
  switch (condition) {
    case "Good":
      return `${baseStyle} border-border text-muted-foreground bg-muted/20`;
    case "Needs Repair":
      return `${baseStyle} border-red-300 text-red-700 bg-red-50`;
    default:
      return `${baseStyle} border-border text-muted-foreground bg-muted/20`;
  }
};

const getTransmissionBadgeStyle = (transmission: string) => {
  // All transmission types are neutral - no color coding needed
  return "font-medium border border-border text-muted-foreground bg-muted/20 text-sm";
};

const getSeatsBadgeStyle = (seats: number) => {
  // Neutral styling for all seat counts
  return "font-bold border border-border text-muted-foreground bg-muted/20 text-sm";
};

const getFuelTypeBadgeStyle = (fuelType: string) => {
  // Neutral styling for all fuel types
  return "font-medium border border-border text-muted-foreground bg-muted/20 text-sm";
};

export default function AdminCarsPage() {
  const { toast } = useToast();
  const [open, setOpen] = React.useState(false);
  const [categoryOpen, setCategoryOpen] = React.useState(false);
  const [editing, setEditing] = React.useState<string | null>(null);
  const [editingCategory, setEditingCategory] = React.useState<string | null>(
    null
  );
  const [refresh, setRefresh] = React.useState(0);
  const [showArchived, setShowArchived] = React.useState(false);
  const [showCategories, setShowCategories] = React.useState(false);
  const [showAvailability, setShowAvailability] = React.useState(false);
  const [isSyncing, setIsSyncing] = React.useState(false);
  const [activeCars, setActiveCars] = React.useState<Car[]>([]);
  const [archivedCars, setArchivedCars] = React.useState<Car[]>([]);
  const [categories, setCategories] = React.useState<CategoryData[]>([]);
  const cars = showArchived ? archivedCars : activeCars;

  // Fetch active cars
  React.useEffect(() => {
    async function fetchActiveCars() {
      const result = await listCars(false);
      setActiveCars(result);
    }
    fetchActiveCars();
  }, [refresh]);

  // Fetch archived cars
  React.useEffect(() => {
    async function fetchArchivedCars() {
      const result = await listArchivedCars();
      setArchivedCars(result);
    }
    fetchArchivedCars();
  }, [refresh]);

  // Fetch categories
  React.useEffect(() => {
    async function fetchCategories() {
      const result = await listCategories();
      setCategories(result);
    }
    fetchCategories();
  }, [refresh]);

  function startEdit(id?: string) {
    setEditing(id ?? null);
    setOpen(true);
  }

  async function onDelete(id: string | undefined) {
    if (!id) return;
    await deleteCarAction(id);
    setRefresh((x) => x + 1);
  }

  async function onArchive(id: string | undefined) {
    if (!id) return;
    await archiveCarAction(id);
    setRefresh((x) => x + 1);
  }

  async function onUnarchive(id: string | undefined) {
    if (!id) return;
    await unarchiveCarAction(id);
    setRefresh((x) => x + 1);
  }

  function startEditCategory(id?: string) {
    setEditingCategory(id ?? null);
    setCategoryOpen(true);
  }

  async function onDeleteCategory(id: string | undefined) {
    if (!id) return;
    await deleteCategoryAction(id);
    setRefresh((x) => x + 1);
  }

  async function handleSyncCarAvailability() {
    setIsSyncing(true);
    try {
      const result = await syncCarAvailability();
      
      if (result.error) {
        toast({
          variant: "destructive",
          title: "Sync Failed",
          description: result.error.message,
        });
      } else {
        toast({
          title: "Sync Completed",
          description: result.message,
        });
        // Refresh the cars list to show updated statuses
        setRefresh((x) => x + 1);
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred during sync.",
      });
    } finally {
      setIsSyncing(false);
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="space-y-1">
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-foreground">Fleet Management</h1>
          <p className="text-sm sm:text-base text-muted-foreground">Manage your vehicle inventory, availability, and categories.</p>
        </div>
        {!showArchived && !showCategories && !showAvailability && (
          <div className="flex gap-2">
            <Button
              variant="secondary"
              size="default"
              onClick={handleSyncCarAvailability}
              disabled={isSyncing}
              className="shrink-0 shadow-sm hover:shadow-md transition-shadow focus:ring-2 focus:ring-offset-2"
              aria-label="Sync car availability with bookings"
            >
              <RefreshCw className={cn("h-4 w-4 mr-2", isSyncing && "animate-spin")} />
              {isSyncing ? "Syncing..." : "Sync Status"}
            </Button>
            <Button
              variant="primary"
              size="default"
              onClick={() => startEdit()}
              className="shrink-0 shadow-sm hover:shadow-md transition-shadow focus:ring-2 focus:ring-offset-2"
              aria-label="Add new car to inventory"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Car
            </Button>
          </div>
        )}
        {showCategories && (
          <Button
            variant="primary"
            size="default"
            onClick={() => startEditCategory()}
            className="shrink-0 shadow-sm hover:shadow-md transition-shadow focus:ring-2 focus:ring-offset-2"
            aria-label="Add new vehicle category"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Category
          </Button>
        )}
      </div>

      {/* Mobile Add Car button is now integrated with the main header */}

      {/* Tab Navigation */}
      {/* Mobile/Tablet Vertical Tabs - Only visible below md breakpoint */}
      <div className="md:hidden flex flex-col gap-2 mb-4">
        <div className="grid grid-cols-1 gap-2 w-full">
          <Button
            variant={!showArchived && !showCategories && !showAvailability ? "primary" : "tertiary"}
            size="default"
            onClick={() => {
              setShowArchived(false);
              setShowCategories(false);
              setShowAvailability(false);
            }}
            className={cn(
              "rounded-lg transition-all duration-200 focus:ring-2 focus:ring-offset-2 font-semibold text-sm px-4 py-2",
              !showArchived && !showCategories && !showAvailability
                ? "bg-emerald-600 text-white shadow-md border-0 hover:bg-emerald-700 focus:ring-emerald-500"
                : "bg-transparent text-muted-foreground hover:bg-background hover:text-foreground hover:shadow-sm focus:ring-muted-foreground border border-transparent hover:border-border"
            )}
            aria-pressed={!showArchived && !showCategories && !showAvailability}
            aria-label={`Show active cars. Currently ${activeCars.length} active cars.`}
          >
            Active ({activeCars.length})
          </Button>
          <Button
            variant={showAvailability ? "primary" : "tertiary"}
            size="default"
            onClick={() => {
              setShowArchived(false);
              setShowCategories(false);
              setShowAvailability(true);
            }}
            className={cn(
              "rounded-lg transition-all duration-200 focus:ring-2 focus:ring-offset-2 font-semibold text-sm px-4 py-2",
              showAvailability
                ? "bg-purple-600 text-white shadow-md border-0 hover:bg-purple-700 focus:ring-purple-500"
                : "bg-transparent text-muted-foreground hover:bg-background hover:text-foreground hover:shadow-sm focus:ring-muted-foreground border border-transparent hover:border-border"
            )}
            aria-pressed={showAvailability}
            aria-label={`Show car availability status.`}
          >
            <CarIcon className="h-4 w-4 mr-2" />
            Availability
          </Button>
          <Button
            variant={showArchived ? "primary" : "tertiary"}
            size="default"
            onClick={() => {
              setShowArchived(true);
              setShowCategories(false);
              setShowAvailability(false);
            }}
            className={cn(
              "rounded-lg transition-all duration-200 focus:ring-2 focus:ring-offset-2 font-semibold text-sm px-4 py-2",
              showArchived
                ? "bg-amber-600 text-white shadow-md border-0 hover:bg-amber-700 focus:ring-amber-500"
                : "bg-transparent text-muted-foreground hover:bg-background hover:text-foreground hover:shadow-sm focus:ring-muted-foreground border border-transparent hover:border-border"
            )}
            aria-pressed={showArchived}
            aria-label={`Show archived cars. Currently ${archivedCars.length} archived cars.`}
          >
            <Archive className="h-4 w-4 mr-2" />
            Archived ({archivedCars.length})
          </Button>
          <Button
            variant={showCategories ? "primary" : "tertiary"}
            size="default"
            onClick={() => {
              setShowArchived(false);
              setShowCategories(true);
              setShowAvailability(false);
            }}
            className={cn(
              "rounded-lg transition-all duration-200 focus:ring-2 focus:ring-offset-2 font-semibold text-sm px-4 py-2",
              showCategories
                ? "bg-blue-600 text-white shadow-md border-0 hover:bg-blue-700 focus:ring-blue-500"
                : "bg-transparent text-muted-foreground hover:bg-background hover:text-foreground hover:shadow-sm focus:ring-muted-foreground border border-transparent hover:border-border"
            )}
            aria-pressed={showCategories}
            aria-label={`Show vehicle categories. Currently ${categories.length} categories.`}
          >
            <FolderOpen className="h-4 w-4 mr-2" />
            Categories ({categories.length})
          </Button>
        </div>
      </div>

      {/* Desktop Horizontal Tabs - Only visible at md breakpoint and above */}
      <div className="hidden md:block overflow-x-auto pb-2 -mb-2 scrollbar-thin scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent">
        <div className="flex items-center gap-1 p-1 bg-muted/50 rounded-xl w-fit border border-border min-w-full sm:min-w-0">
          <Button
            variant={!showArchived && !showCategories && !showAvailability ? "primary" : "tertiary"}
            size="default"
            onClick={() => {
              setShowArchived(false);
              setShowCategories(false);
              setShowAvailability(false);
            }}
            className={cn(
              "rounded-lg transition-all duration-200 focus:ring-2 focus:ring-offset-2 font-semibold text-sm px-4 py-2",
              !showArchived && !showCategories && !showAvailability
                ? "bg-emerald-600 text-white shadow-md border-0 hover:bg-emerald-700 focus:ring-emerald-500"
                : "bg-transparent text-muted-foreground hover:bg-background hover:text-foreground hover:shadow-sm focus:ring-muted-foreground border border-transparent hover:border-border"
            )}
            aria-pressed={!showArchived && !showCategories && !showAvailability}
            aria-label={`Show active cars. Currently ${activeCars.length} active cars.`}
          >
            Active ({activeCars.length})
          </Button>
          <Button
            variant={showAvailability ? "primary" : "tertiary"}
            size="default"
            onClick={() => {
              setShowArchived(false);
              setShowCategories(false);
              setShowAvailability(true);
            }}
            className={cn(
              "rounded-lg transition-all duration-200 focus:ring-2 focus:ring-offset-2 font-semibold text-sm px-4 py-2",
              showAvailability
                ? "bg-purple-600 text-white shadow-md border-0 hover:bg-purple-700 focus:ring-purple-500"
                : "bg-transparent text-muted-foreground hover:bg-background hover:text-foreground hover:shadow-sm focus:ring-muted-foreground border border-transparent hover:border-border"
            )}
            aria-pressed={showAvailability}
            aria-label={`Show car availability status.`}
          >
            <CarIcon className="h-4 w-4 mr-2" />
            Availability
          </Button>
          <Button
            variant={showArchived ? "primary" : "tertiary"}
            size="default"
            onClick={() => {
              setShowArchived(true);
              setShowCategories(false);
              setShowAvailability(false);
            }}
            className={cn(
              "rounded-lg transition-all duration-200 focus:ring-2 focus:ring-offset-2 font-semibold text-sm px-4 py-2",
              showArchived
                ? "bg-amber-600 text-white shadow-md border-0 hover:bg-amber-700 focus:ring-amber-500"
                : "bg-transparent text-muted-foreground hover:bg-background hover:text-foreground hover:shadow-sm focus:ring-muted-foreground border border-transparent hover:border-border"
            )}
            aria-pressed={showArchived}
            aria-label={`Show archived cars. Currently ${archivedCars.length} archived cars.`}
          >
            <Archive className="h-4 w-4 mr-2" />
            Archived ({archivedCars.length})
          </Button>
          <Button
            variant={showCategories ? "primary" : "tertiary"}
            size="default"
            onClick={() => {
              setShowArchived(false);
              setShowCategories(true);
              setShowAvailability(false);
            }}
            className={cn(
              "rounded-lg transition-all duration-200 focus:ring-2 focus:ring-offset-2 font-semibold text-sm px-4 py-2",
              showCategories
                ? "bg-blue-600 text-white shadow-md border-0 hover:bg-blue-700 focus:ring-blue-500"
                : "bg-transparent text-muted-foreground hover:bg-background hover:text-foreground hover:shadow-sm focus:ring-muted-foreground border border-transparent hover:border-border"
            )}
            aria-pressed={showCategories}
            aria-label={`Show vehicle categories. Currently ${categories.length} categories.`}
          >
            <FolderOpen className="h-4 w-4 mr-2" />
            Categories ({categories.length})
          </Button>
        </div>
      </div>

      {/* Modern Data Table */}
      <Card className="shadow-sm border-0 ring-1 ring-border">
        <CardHeader className="bg-gradient-to-r from-muted/30 to-muted/10 border-b">
          <CardTitle className="flex items-center gap-2 text-lg font-semibold">
            {showAvailability ? (
              <>
                <CarIcon className="h-5 w-5 text-purple-600" />
                <span>Car Availability Status</span>
                <Badge variant="secondary" className="ml-2">
                  {activeCars.length} vehicles
                </Badge>
              </>
            ) : showCategories ? (
              <>
                <FolderOpen className="h-5 w-5 text-foreground" />
                <span>Vehicle Categories</span>
                <Badge variant="secondary" className="ml-2">
                  {categories.length}
                </Badge>
              </>
            ) : showArchived ? (
              <>
                <Archive className="h-5 w-5 text-amber-600" />
                <span>Archived Cars</span>
                <Badge variant="secondary" className="ml-2">
                  {archivedCars.length}
                </Badge>
              </>
            ) : (
              <>
                <div className="h-5 w-5 rounded-full bg-emerald-500 flex items-center justify-center">
                  <div className="h-2 w-2 rounded-full bg-white"></div>
                </div>
                <span>Active Inventory</span>
                <Badge variant="secondary" className="ml-2">
                  {activeCars.length}
                </Badge>
              </>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {/* Simplified Legend - Desktop Only */}
          {!showCategories ? (
            <div className="hidden lg:block border-b bg-muted/30 px-6 py-3">
              <div className="flex flex-wrap items-center gap-4 text-sm">
                <span className="font-semibold text-muted-foreground">
                  Status Indicators:
                </span>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded border border-emerald-300 bg-emerald-50"></div>
                  <span className="text-emerald-700">Available</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded border border-amber-300 bg-amber-50"></div>
                  <span className="text-amber-700">Maintenance</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded border border-red-300 bg-red-50"></div>
                  <span className="text-red-700">Needs Attention</span>
                </div>
              </div>
            </div>
          ) : (
            <div className="hidden lg:block border-b bg-muted/30 px-6 py-3">
              <div className="flex flex-wrap items-center gap-4 text-sm">
                <span className="font-semibold text-muted-foreground">
                  Category Management:
                </span>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded border border-emerald-300 bg-emerald-50"></div>
                  <span className="text-emerald-700">Pricing Information</span>
                </div>
              </div>
              <div className="mt-2 text-sm text-amber-800 bg-amber-50 p-2 rounded border border-amber-200 inline-block">
                <span className="font-semibold">Note:</span> Categories are
                dynamically generated from the Customer Catalog/Fleet data and
                cannot be deleted. Add or modify vehicles in your fleet to
                update available categories.
              </div>
            </div>
          )}

          {/* Desktop Table View */}
          <div className="hidden lg:block overflow-x-auto">
            {showAvailability ? (
              // Availability View - shows car status with booking information
              <div className="space-y-4 p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  {/* Availability Stats */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-600">Available</p>
                        <p className="text-2xl font-bold text-green-700">
                          {activeCars.filter(car => car.status === "Available").length}
                        </p>
                      </div>
                      <CarIcon className="h-8 w-8 text-green-600" />
                    </div>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-blue-600">Rented</p>
                        <p className="text-2xl font-bold text-blue-700">
                          {activeCars.filter(car => car.status === "Rented").length}
                        </p>
                      </div>
                      <CarIcon className="h-8 w-8 text-blue-600" />
                    </div>
                  </div>
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-amber-600">Maintenance</p>
                        <p className="text-2xl font-bold text-amber-700">
                          {activeCars.filter(car => car.status === "In Maintenance").length}
                        </p>
                      </div>
                      <CarIcon className="h-8 w-8 text-amber-600" />
                    </div>
                  </div>
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Fleet</p>
                        <p className="text-2xl font-bold text-gray-700">{activeCars.length}</p>
                      </div>
                      <CarIcon className="h-8 w-8 text-gray-600" />
                    </div>
                  </div>
                </div>

                {/* Availability Table */}
                <table className="w-full" role="table" aria-label="Car availability status table">
                  <thead>
                    <tr className="border-b bg-gradient-to-r from-muted/30 to-muted/10">
                      <th scope="col" className="text-left py-4 px-6 font-semibold text-base text-foreground border-r border-border">
                        Vehicle
                      </th>
                      <th scope="col" className="text-left py-4 px-4 font-semibold text-base text-foreground border-r border-border">
                        Plate Number
                      </th>
                      <th scope="col" className="text-left py-4 px-4 font-semibold text-base text-emerald-700 border-r border-border">
                        Status
                      </th>
                      <th scope="col" className="text-left py-4 px-4 font-semibold text-base text-foreground border-r border-border">
                        Location
                      </th>
                      <th scope="col" className="text-right py-4 px-4 font-semibold text-base text-emerald-700 border-r border-border">
                        Daily Rate
                      </th>
                      <th scope="col" className="text-right py-4 px-6 font-semibold text-base text-foreground">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {activeCars.length === 0 ? (
                      <tr>
                        <td colSpan={6} className="text-center py-12 text-muted-foreground">
                          <div className="flex flex-col items-center gap-2">
                            <CarIcon className="h-8 w-8 text-muted-foreground/40" />
                            <p>No cars found</p>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      activeCars.map((car, index) => (
                        <tr
                          key={car.id}
                          className={cn(
                            "border-b transition-colors hover:bg-muted/30",
                            index % 2 === 0 ? "bg-white" : "bg-muted/10"
                          )}
                        >
                          <td className="py-4 px-6">
                            <div className="flex items-center gap-3">
                              {car.image_url && (
                                <div className="h-10 w-10 rounded-md overflow-hidden flex-shrink-0 border border-border bg-muted/20">
                                  <img
                                    src={car.image_url}
                                    alt={car.model}
                                    className="h-full w-full object-cover"
                                  />
                                </div>
                              )}
                              <div>
                                <div className="font-medium text-base text-foreground">{car.model}</div>
                                <div className="text-sm text-muted-foreground">{car.type}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-4 font-mono text-sm">{car.plate_number}</td>
                          <td className="py-4 px-4">
                            <Badge variant="outline" className={getStatusBadgeStyle(car.status)}>
                              {car.status}
                            </Badge>
                          </td>
                          <td className="py-4 px-4 text-muted-foreground">
                            {/* This would be populated from booking data in a real implementation */}
                            Manila
                          </td>
                          <td className="py-4 px-4 text-right">
                            <div className="bg-gradient-to-r from-emerald-50 to-emerald-100 px-3 py-2 rounded-lg border border-emerald-300 inline-block">
                              <div className="font-bold text-lg text-emerald-800">
                                ₱{car.price_per_day?.toFixed(2) || "0.00"}
                              </div>
                              <div className="text-xs text-emerald-600 font-medium">per day</div>
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <div className="flex items-center gap-2 justify-end">
                              <Button
                                variant="secondary"
                                size="sm"
                                onClick={() => startEdit(car.id)}
                                className="h-8 px-3"
                              >
                                <Edit className="h-3 w-3 mr-1" />
                                Edit
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            ) : showCategories ? (
              // Categories Table
              <table
                className="w-full"
                role="table"
                aria-label="Vehicle categories table"
              >
                <thead>
                  <tr className="border-b bg-gradient-to-r from-muted/30 to-muted/10">
                    <th
                      scope="col"
                      className="text-left py-4 px-6 font-semibold text-base text-foreground border-r border-border"
                    >
                      Image
                    </th>
                    <th
                      scope="col"
                      className="text-left py-4 px-6 font-semibold text-base text-foreground border-r border-border"
                    >
                      Name
                    </th>
                    <th
                      scope="col"
                      className="text-left py-4 px-4 font-semibold text-base text-foreground border-r border-border"
                    >
                      Type
                    </th>
                    <th
                      scope="col"
                      className="text-center py-4 px-4 font-semibold text-base text-emerald-700 border-r border-border"
                    >
                      Price Range
                    </th>
                    <th
                      scope="col"
                      className="text-left py-4 px-4 font-semibold text-base text-foreground border-r border-border"
                    >
                      Transmission
                    </th>
                    <th
                      scope="col"
                      className="text-center py-4 px-4 font-semibold text-base text-foreground border-r border-border"
                    >
                      Vehicles
                    </th>
                    <th
                      scope="col"
                      className="text-right py-4 px-6 font-semibold text-base text-foreground"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {categories.length === 0 ? (
                    <tr>
                      <td
                        colSpan={6}
                        className="text-center py-12 text-muted-foreground"
                      >
                        <div className="flex flex-col items-center gap-2">
                          <FolderOpen className="h-8 w-8 text-muted-foreground/40" />
                          <p>No vehicle categories found</p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    categories.map((cat, index) => (
                      <tr
                        key={cat.id}
                        className={cn(
                          "border-b transition-colors hover:bg-muted/30",
                          index % 2 === 0 ? "bg-white" : "bg-muted/10"
                        )}
                      >
                        <td className="py-4 px-6">
                          <div className="h-16 w-20 rounded-md overflow-hidden border border-border bg-muted/20">
                            <img
                              src={cat.imageUrl}
                              alt={cat.name}
                              className="h-full w-full object-contain"
                            />
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <div className="font-medium text-base text-foreground">
                            {cat.name}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <Badge
                            variant="outline"
                            className={getVehicleTypeBadgeStyle(cat.type)}
                          >
                            {cat.type}
                          </Badge>
                        </td>
                        <td className="py-4 px-4 text-center">
                          <div className="bg-gradient-to-r from-emerald-50 to-emerald-100 px-3 py-2 rounded-lg border border-emerald-300 inline-block">
                            <div className="font-bold text-lg text-emerald-800">
                              ₱{cat.priceRangeFrom.toLocaleString()} - ₱
                              {cat.priceRangeTo.toLocaleString()}
                            </div>
                            <div className="text-xs text-emerald-600 font-medium">
                              per day
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex flex-wrap gap-2">
                            {cat.transmissionTypes.map((trans) => (
                              <Badge
                                key={trans}
                                variant="outline"
                                className={getTransmissionBadgeStyle(trans)}
                              >
                                {trans}
                              </Badge>
                            ))}
                          </div>
                        </td>
                        <td className="py-4 px-4 text-center">
                          <div className="flex flex-col items-center gap-2">
                            <Badge
                              variant="outline"
                              className="font-bold border border-border text-foreground bg-muted/20 text-sm"
                            >
                              {cat.carCount}{" "}
                              {cat.carCount === 1 ? "vehicle" : "vehicles"}
                            </Badge>
                            {cat.carCount > 0 && (
                              <div className="flex flex-wrap gap-1 justify-center mt-1">
                                {cat.cars.slice(0, 3).map((car) => (
                                  <div
                                    key={car.id}
                                    title={car.model}
                                    className="w-8 h-8 rounded-full border border-border overflow-hidden bg-muted/20"
                                  >
                                    {car.image_url && (
                                      <img
                                        src={car.image_url}
                                        alt={car.model}
                                        className="w-full h-full object-cover"
                                      />
                                    )}
                                  </div>
                                ))}
                                {cat.carCount > 3 && (
                                  <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-sm font-medium text-muted-foreground">
                                    +{cat.carCount - 3}
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex items-center gap-2 justify-end">
                            <Button
                              variant="secondary"
                              size="sm"
                              onClick={() => startEditCategory(cat.id)}
                              className="h-8 px-3"
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                            <Button
                              variant="tertiary"
                              size="sm"
                              title="Categories are dynamically generated and cannot be deleted"
                              className="h-8 px-3 cursor-not-allowed opacity-60"
                              disabled
                            >
                              <Trash2 className="h-3 w-3 mr-1" />
                              Delete
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            ) : (
              // Cars Table
              <table
                className="w-full"
                role="table"
                aria-label={`${
                  showArchived ? "Archived" : "Active"
                } cars inventory table`}
              >
                <thead>
                  <tr className="border-b bg-gradient-to-r from-muted/30 to-muted/10">
                    <th
                      scope="col"
                      className="text-left py-4 px-6 font-semibold text-base text-foreground border-r border-border"
                    >
                      Model
                    </th>
                    <th
                      scope="col"
                      className="text-left py-4 px-4 font-semibold text-base text-foreground border-r border-border"
                    >
                      Type
                    </th>
                    <th
                      scope="col"
                      className="text-left py-4 px-4 font-semibold text-base text-foreground border-r border-border"
                    >
                      Color
                    </th>
                    <th
                      scope="col"
                      className="text-left py-4 px-4 font-semibold text-base text-emerald-700 border-r border-border"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      className="text-left py-4 px-4 font-semibold text-base text-foreground border-r border-border"
                    >
                      Condition
                    </th>
                    <th
                      scope="col"
                      className="text-center py-4 px-4 font-semibold text-base text-foreground border-r border-border"
                    >
                      Seats
                    </th>
                    <th
                      scope="col"
                      className="text-left py-4 px-4 font-semibold text-base text-foreground border-r border-border"
                    >
                      Trans
                    </th>
                    <th
                      scope="col"
                      className="text-left py-4 px-4 font-semibold text-base text-foreground border-r border-border"
                    >
                      Fuel
                    </th>
                    <th
                      scope="col"
                      className="text-right py-4 px-4 font-semibold text-base text-emerald-700 border-r border-border"
                    >
                      Price/day
                    </th>
                    <th
                      scope="col"
                      className="text-right py-4 px-6 font-semibold text-base text-foreground"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {cars.length === 0 ? (
                    <tr>
                      <td
                        colSpan={10}
                        className="text-center py-12 text-muted-foreground"
                      >
                        <div className="flex flex-col items-center gap-2">
                          <Archive className="h-8 w-8 text-muted-foreground/40" />
                          <p>
                            No {showArchived ? "archived" : "active"} cars found
                          </p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    cars.map((c, index) => (
                      <tr
                        key={c.id}
                        className={cn(
                          "border-b transition-colors hover:bg-muted/30",
                          index % 2 === 0 ? "bg-white" : "bg-muted/10",
                          showArchived && "opacity-75"
                        )}
                      >
                        <td className="py-4 px-6">
                          <div className="flex items-center gap-3">
                            {c.image_url && (
                              <div className="h-10 w-10 rounded-md overflow-hidden flex-shrink-0 border border-border bg-muted/20">
                                <img
                                  src={c.image_url}
                                  alt={c.model}
                                  className="h-full w-full object-cover"
                                />
                              </div>
                            )}
                            <div className="font-medium text-base text-foreground">
                              {c.model}
                            </div>
                            {showArchived && (
                              <Badge
                                variant="secondary"
                                className="text-sm bg-amber-100 text-amber-800 border-amber-200"
                              >
                                Archived
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <Badge
                            variant="outline"
                            className={getVehicleTypeBadgeStyle(c.type)}
                          >
                            {c.type}
                          </Badge>
                        </td>
                        <td className="py-4 px-4">
                          <Badge
                            variant="outline"
                            className="font-medium border border-muted-foreground/40 text-muted-foreground bg-muted/20 text-sm"
                          >
                            {c.color || "White"}
                          </Badge>
                        </td>
                        <td className="py-4 px-4">
                          <Badge
                            variant="outline"
                            className={getStatusBadgeStyle(c.status)}
                          >
                            {c.status}
                          </Badge>
                        </td>
                        <td className="py-4 px-4 text-base">
                          <Badge
                            variant="outline"
                            className={getConditionBadgeStyle(c.condition)}
                          >
                            {c.condition}
                          </Badge>
                        </td>
                        <td className="py-4 px-4 text-center text-base">
                          <Badge
                            variant="outline"
                            className={getSeatsBadgeStyle(c.seats)}
                          >
                            {c.seats} seats
                          </Badge>
                        </td>
                        <td className="py-4 px-4 text-base">
                          <Badge
                            variant="outline"
                            className={getTransmissionBadgeStyle(
                              c.transmission
                            )}
                          >
                            {c.transmission}
                          </Badge>
                        </td>
                        <td className="py-4 px-4 text-base">
                          <Badge
                            variant="outline"
                            className={getFuelTypeBadgeStyle(c.fuel_type)}
                          >
                            {c.fuel_type}
                          </Badge>
                        </td>
                        <td className="py-4 px-4 text-right">
                          <div className="bg-gradient-to-r from-emerald-50 to-emerald-100 px-3 py-2 rounded-lg border border-emerald-300">
                            <div className="font-bold text-xl text-emerald-800">
                              ₱{c.price_per_day?.toFixed(2) || "0.00"}
                            </div>
                            <div className="text-xs text-emerald-600 font-medium">
                              per day
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex items-center gap-2 justify-end">
                            {!showArchived ? (
                              <>
                                <Button
                                  variant="secondary"
                                  size="sm"
                                  onClick={() => startEdit(c.id)}
                                  className="h-8 px-3"
                                >
                                  <Edit className="h-3 w-3 mr-1" />
                                  Edit
                                </Button>
                                <Button
                                  variant="warning"
                                  size="sm"
                                  onClick={() => onArchive(c.id)}
                                  className="h-8 px-3"
                                >
                                  <Archive className="h-3 w-3 mr-1" />
                                  Archive
                                </Button>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => onDelete(c.id)}
                                  className="h-8 px-3"
                                >
                                  <Trash2 className="h-3 w-3 mr-1" />
                                  Delete
                                </Button>
                              </>
                            ) : (
                              <>
                                <Button
                                  variant="success"
                                  size="sm"
                                  onClick={() => onUnarchive(c.id)}
                                  className="h-8 px-3"
                                >
                                  <ArchiveRestore className="h-3 w-3 mr-1" />
                                  Restore
                                </Button>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => onDelete(c.id)}
                                  className="h-8 px-3"
                                >
                                  <Trash2 className="h-3 w-3 mr-1" />
                                  Delete
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            )}
          </div>

          {/* Mobile Card View */}
          <div className="lg:hidden">
            {showCategories ? (
              // Categories Mobile View
              categories.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <div className="flex flex-col items-center gap-2">
                    <FolderOpen className="h-8 w-8 text-muted-foreground/40" />
                    <p>No vehicle categories found</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4 p-4">
                  <div className="text-sm text-amber-700 bg-amber-50 p-3 rounded-lg border border-amber-200">
                    <span className="font-semibold">Note:</span> Categories are
                    dynamically generated from the Customer Catalog/Fleet data
                    and cannot be deleted. Add or modify vehicles in your fleet
                    to update available categories.
                  </div>
                  {categories.map((cat) => (
                    <div
                      key={cat.id}
                      className="bg-white rounded-lg border p-4 space-y-4 shadow-sm"
                    >
                      {/* Header */}
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex gap-3 items-start">
                          <div className="h-16 w-16 rounded-md overflow-hidden flex-shrink-0 border border-border bg-muted/20">
                            <img
                              src={cat.imageUrl}
                              alt={cat.name}
                              className="h-full w-full object-contain"
                            />
                          </div>
                          <div className="space-y-1">
                            <h3 className="font-semibold text-foreground break-words">
                              {cat.name}
                            </h3>
                            <Badge
                              variant="outline"
                              className={getVehicleTypeBadgeStyle(cat.type)}
                            >
                              {cat.type}
                            </Badge>
                          </div>
                        </div>
                      </div>

                      {/* Price Range */}
                      <div>
                        <span className="text-muted-foreground text-sm font-medium">
                          Price Range:
                        </span>
                        <div className="mt-1 bg-gradient-to-r from-emerald-50 to-emerald-100 px-4 py-2 rounded-lg border border-emerald-300 text-center">
                          <div className="font-bold text-lg text-emerald-800">
                            ₱{cat.priceRangeFrom.toLocaleString()} - ₱
                            {cat.priceRangeTo.toLocaleString()}
                          </div>
                          <div className="text-sm text-emerald-600 font-medium">
                            per day
                          </div>
                        </div>
                      </div>

                      {/* Transmission */}
                      <div>
                        <span className="text-muted-foreground text-sm font-medium">
                          Available Transmission:
                        </span>
                        <div className="mt-1 flex flex-wrap gap-2">
                          {cat.transmissionTypes.map((trans) => (
                            <Badge
                              key={trans}
                              variant="outline"
                              className={getTransmissionBadgeStyle(trans)}
                            >
                              {trans}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Vehicle Count */}
                      <div>
                        <span className="text-muted-foreground text-sm font-medium">
                          Available Vehicles:
                        </span>
                        <div className="mt-1">
                          <Badge
                            variant="outline"
                            className="text-sm font-bold border border-border text-foreground bg-muted/20"
                          >
                            {cat.carCount}{" "}
                            {cat.carCount === 1 ? "vehicle" : "vehicles"}
                          </Badge>
                          {cat.carCount > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {cat.cars.slice(0, 4).map((car) => (
                                <div
                                  key={car.id}
                                  title={car.model}
                                  className="w-8 h-8 rounded-full border border-border overflow-hidden bg-muted/20"
                                >
                                  {car.image_url && (
                                    <img
                                      src={car.image_url}
                                      alt={car.model}
                                      className="w-full h-full object-cover"
                                    />
                                  )}
                                </div>
                              ))}
                              {cat.carCount > 4 && (
                                <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-sm font-medium text-muted-foreground">
                                  +{cat.carCount - 4}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2 pt-2 border-t">
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => startEditCategory(cat.id)}
                          className="flex-1"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="tertiary"
                          size="sm"
                          disabled
                          title="Categories are dynamically generated and cannot be deleted"
                          className="cursor-not-allowed opacity-60"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )
            ) : // Cars Mobile View
            cars.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                <div className="flex flex-col items-center gap-2">
                  <Archive className="h-8 w-8 text-muted-foreground/40" />
                  <p>No {showArchived ? "archived" : "active"} cars found</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4 p-4">
                {cars.map((c) => (
                  <div
                    key={c.id}
                    className={cn(
                      "bg-white rounded-lg border p-0 shadow-sm overflow-hidden",
                      showArchived && "opacity-75"
                    )}
                  >
                    {/* Card Header with Image, Model and Status Badge */}
                    <div className="relative">
                      {/* Status Badge - Top Right */}
                      <div className="absolute top-2 right-2 z-10">
                        <Badge
                          variant="outline"
                          className={getStatusBadgeStyle(c.status)}
                        >
                          {c.status}
                        </Badge>
                      </div>
                      
                      {/* Car Image */}
                      <div className="h-40 w-full bg-muted/20 border-b border-border">
                        {c.image_url ? (
                          <img
                            src={c.image_url}
                            alt={c.model}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center">
                            <CarIcon className="h-16 w-16 text-muted-foreground/30" />
                          </div>
                        )}
                      </div>
                      
                      {/* Model Name and Type */}
                      <div className="p-4 pb-2">
                        <div className="flex items-start justify-between">
                          <div className="w-full">
                            <h3 className="font-semibold text-lg text-foreground break-words line-clamp-2">
                              {c.model}
                            </h3>
                            <div className="flex flex-wrap items-center gap-2 mt-1">
                              <Badge
                                variant="outline"
                                className={getVehicleTypeBadgeStyle(c.type)}
                              >
                                {c.type}
                              </Badge>
                              <Badge
                                variant="outline"
                                className="text-sm font-medium border border-border text-muted-foreground bg-muted/20"
                              >
                                {c.color || "White"}
                              </Badge>
                              {showArchived && (
                                <Badge
                                  variant="secondary"
                                  className="text-sm bg-amber-100 text-amber-800 border-amber-200"
                                >
                                  Archived
                                </Badge>
                              )}
                            </div>
                            {c.notes && (
                              <div className="mt-2 p-2 bg-muted/10 rounded-md border border-border text-sm text-muted-foreground line-clamp-3 break-words">
                                {c.notes}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        {/* Prominent Price Display */}
                        <div className="mt-3 bg-gradient-to-r from-emerald-50 to-emerald-100 px-3 py-3 rounded-lg border border-emerald-300 overflow-hidden">
                          <div className="font-bold text-xl xs:text-2xl text-emerald-800 truncate">
                            ₱{c.price_per_day?.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) || "0.00"}
                          </div>
                          <div className="text-sm text-emerald-600 font-medium">
                            per day
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Middle Section: Car Details - Hidden on mobile */}
                    <div className="hidden md:grid px-4 py-3 grid-cols-2 gap-x-3 gap-y-2 text-base border-t border-b border-border bg-muted/5 overflow-hidden">
                      <div className="flex items-center gap-2 overflow-hidden">
                        <Users className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                        <Badge
                          variant="outline"
                          className={`${getSeatsBadgeStyle(c.seats)} truncate max-w-full`}
                        >
                          {c.seats} seats
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 overflow-hidden">
                        <Cog className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                        <Badge
                          variant="outline"
                          className={`${getTransmissionBadgeStyle(c.transmission)} truncate max-w-full`}
                        >
                          {c.transmission}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 overflow-hidden">
                        <Fuel className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                        <Badge
                          variant="outline"
                          className={`${getFuelTypeBadgeStyle(c.fuel_type)} truncate max-w-full`}
                        >
                          {c.fuel_type}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 overflow-hidden">
                        <div className={cn(
                          "hidden md:block h-4 w-4 rounded-full flex-shrink-0",
                          c.condition === "Good" ? "bg-emerald-500" : "bg-red-500"
                        )} />
                        <Badge
                          variant="outline"
                          className={`${getConditionBadgeStyle(c.condition)} truncate max-w-full`}
                        >
                          {c.condition}
                        </Badge>
                      </div>
                    </div>

                    {/* Actions - Touch Friendly */}
                    <div className="flex p-4 gap-2">
                      {!showArchived ? (
                        <>
                          <Button
                            variant="secondary"
                            size="default"
                            onClick={() => startEdit(c.id)}
                            className="flex-1 h-11"
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                          <Button
                            variant="warning"
                            size="default"
                            onClick={() => onArchive(c.id)}
                            className="h-11 w-11 p-0 flex items-center justify-center"
                            aria-label="Archive car"
                          >
                            <Archive className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="default"
                            onClick={() => onDelete(c.id)}
                            className="h-11 w-11 p-0 flex items-center justify-center"
                            aria-label="Delete car"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            variant="success"
                            size="default"
                            onClick={() => onUnarchive(c.id)}
                            className="flex-1 h-11"
                          >
                            <ArchiveRestore className="h-4 w-4 mr-2" />
                            Restore
                          </Button>
                          <Button
                            variant="destructive"
                            size="default"
                            onClick={() => onDelete(c.id)}
                            className="h-11 w-11 p-0 flex items-center justify-center"
                            aria-label="Delete car"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Car Dialog */}
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild />
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto bg-white">
          <DialogHeader className="pb-4 border-b">
            <DialogTitle className="text-xl font-semibold">
              {editing ? "Edit Car Details" : "Add New Car"}
            </DialogTitle>
            <p className="text-sm text-muted-foreground mt-1">
              {editing
                ? "Update car information and settings"
                : "Enter the details for the new car"}
            </p>
          </DialogHeader>
          <CarForm
            id={editing ?? undefined}
            onClose={() => {
              setOpen(false);
              setEditing(null);
              setRefresh((x) => x + 1);
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Category Dialog */}
      <Dialog open={categoryOpen} onOpenChange={setCategoryOpen}>
        <DialogTrigger asChild />
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto bg-white">
          <DialogHeader className="pb-4 border-b">
            <DialogTitle className="text-xl font-semibold">
              {editingCategory
                ? "Edit Category Display Settings"
                : "Add New Category"}
            </DialogTitle>
            <p className="text-sm text-muted-foreground mt-1">
              {editingCategory
                ? "Customize how this vehicle category appears to customers"
                : "Add a new vehicle type to your catalog"}
            </p>
          </DialogHeader>
          <CategoryForm
            id={editingCategory ?? undefined}
            categoryData={
              editingCategory
                ? categories.find((c) => c.id === editingCategory) || null
                : null
            }
            onClose={() => {
              setCategoryOpen(false);
              setEditingCategory(null);
              setRefresh((x) => x + 1);
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}

function CarForm({ id, onClose }: { id?: string; onClose: () => void }) {
  const [loadingCar, setLoadingCar] = React.useState(id ? true : false);
  const [carData, setCarData] = React.useState<Car | null>(null);

  // Load car data if editing
  React.useEffect(() => {
    async function fetchCarData() {
      if (id) {
        setLoadingCar(true);
        const cars = await listCars(false);
        const car = cars.find((c) => c.id === id);
        if (car) {
          setCarData(car);
        }
        setLoadingCar(false);
      }
    }
    fetchCarData();
  }, [id]);

  const [form, setForm] = React.useState({
    model: carData?.model ?? "",
    type: carData?.type ?? "Sedan",
    plateNumber: carData?.plate_number ?? "",
    status: carData?.status ?? "Available",
    condition: carData?.condition ?? "Good",
    fuelCapacity: carData?.fuel_capacity ?? 60,
    fuelType: carData?.fuel_type ?? "Gas/Premium",
    transmission: carData?.transmission ?? "Manual",
    seats: carData?.seats ?? 5,
    pricePerDay: carData?.price_per_day ?? 70,
    imageURL: carData?.image_url ?? "",
    notes: carData?.notes ?? "",
    color: carData?.color ?? "White",
  });

  // Update form when car data loads
  React.useEffect(() => {
    if (carData) {
      setForm({
        model: carData.model ?? "",
        type: carData.type ?? "Sedan",
        plateNumber: carData.plate_number ?? "",
        status: carData.status ?? "Available",
        condition: carData.condition ?? "Good",
        fuelCapacity: carData.fuel_capacity ?? 60,
        fuelType: carData.fuel_type ?? "Gas/Premium",
        transmission: carData.transmission ?? "Manual",
        seats: carData.seats ?? 5,
        pricePerDay: carData.price_per_day ?? 70,
        imageURL: carData.image_url ?? "",
        notes: carData.notes ?? "",
        color: carData.color ?? "White",
      });
    }
  }, [carData]);

  function onChange<K extends keyof typeof form>(key: K, value: any) {
    setForm((f) => ({ ...f, [key]: value }));
  }

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();

    // Convert form data to the expected Car interface format
    const carData = {
      model: form.model,
      type: form.type,
      plate_number: form.plateNumber || "",
      status: form.status,
      condition: form.condition,
      fuel_capacity: form.fuelCapacity,
      fuel_type: form.fuelType,
      transmission: form.transmission,
      seats: form.seats,
      price_per_day: form.pricePerDay,
      image_url: form.imageURL,
      notes: form.notes,
      color: form.color,
      is_archived: false,
    };

    if (id) {
      await updateCarAction(id, carData);
    } else {
      await addCarAction(carData);
    }
    onClose();
  }

  return (
    <div className="bg-white">
      <form onSubmit={onSubmit} className="space-y-6 pt-4 bg-white">
        {/* Basic Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-foreground border-b pb-2">
            Basic Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputL
              label="Car Model"
              value={form.model}
              onChange={(v) => onChange("model", v)}
              required
            />
            <InputL
              label="Plate Number"
              value={form.plateNumber}
              onChange={(v) => onChange("plateNumber", v)}
              placeholder="e.g. ABC-123"
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <SelectL
              label="Vehicle Type"
              value={form.type}
              onChange={(v) => onChange("type", v)}
              options={["SUV", "MPV", "Sedan"]}
            />
            <InputL
              label="Vehicle Color"
              value={form.color}
              onChange={(v) => onChange("color", v)}
              placeholder="e.g. White, Black, Red"
            />
            <SelectL
              label="Current Status"
              value={form.status}
              onChange={(v) => onChange("status", v)}
              options={["Available", "Rented", "In Maintenance"]}
            />
            <SelectL
              label="Condition"
              value={form.condition}
              onChange={(v) => onChange("condition", v)}
              options={["Good", "Needs Repair"]}
            />
          </div>
        </div>

        {/* Technical Specifications Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-foreground border-b pb-2">
            Technical Specifications
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <SelectL
              label="Transmission"
              value={form.transmission}
              onChange={(v) => onChange("transmission", v)}
              options={["Manual", "Automatic", "CVT"]}
            />
            <InputL
              label="Seating Capacity"
              type="number"
              value={String(form.seats)}
              onChange={(v) => onChange("seats", Number(v))}
              min="2"
              max="15"
            />
            <InputL
              label="Fuel Capacity (L)"
              type="number"
              value={String(form.fuelCapacity)}
              onChange={(v) => onChange("fuelCapacity", Number(v))}
              min="20"
              max="200"
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SelectL
              label="Fuel Type"
              value={form.fuelType}
              onChange={(v) => onChange("fuelType", v)}
              options={["Gas/Premium", "Diesel", "Unleaded"]}
            />
            <InputL
              label="Daily Rate (₱)"
              type="number"
              value={String(form.pricePerDay)}
              onChange={(v) => onChange("pricePerDay", Number(v))}
              min="500"
              step="50"
            />
          </div>
        </div>

        {/* Additional Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-foreground border-b pb-2">
            Additional Information
          </h3>
          <ImageUpload
            label="Car Image"
            value={form.imageURL}
            onChange={(url) => onChange("imageURL", url)}
          />
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Special Notes
            </label>
            <textarea
              value={form.notes}
              onChange={(e) => onChange("notes", e.target.value)}
              className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm resize-none focus:ring-2 focus:ring-ring focus:border-transparent"
              placeholder="Any special instructions or notes about this vehicle..."
              rows={3}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            className="px-6"
          >
            Cancel
          </Button>
          <Button type="submit" variant="primary" className="px-6">
            {id ? (
              <>
                <Edit className="h-4 w-4 mr-2" />
                Save Changes
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Add Car
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}

function InputL({
  label,
  value,
  onChange,
  type = "text",
  placeholder,
  required,
  min,
  max,
  step,
}: {
  label: string;
  value: string;
  onChange: (v: string) => void;
  type?: string;
  placeholder?: string;
  required?: boolean;
  min?: string;
  max?: string;
  step?: string;
}) {
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-foreground">
        {label}
        {required ? " *" : ""}
      </label>
      <input
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm focus:ring-2 focus:ring-ring focus:border-transparent transition-colors"
        placeholder={placeholder}
        required={required}
        min={min}
        max={max}
        step={step}
      />
    </div>
  );
}

function SelectL({
  label,
  value,
  onChange,
  options,
}: {
  label: string;
  value: string;
  onChange: (v: string) => void;
  options: string[];
}) {
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-foreground">{label}</label>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm focus:ring-2 focus:ring-ring focus:border-transparent transition-colors"
      >
        {options.map((o) => (
          <option key={o} value={o}>
            {o}
          </option>
        ))}
      </select>
    </div>
  );
}
