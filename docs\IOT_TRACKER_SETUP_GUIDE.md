# ESP32-S3 A7670E IoT GPS Tracker – Setup Guide (PlatformIO + MQTT)

This guide explains how to bring up the Waveshare ESP32-S3-A7670E-4G board with your repository, publish GPS via MQTT (or GeoLinker), and connect it to the Admin Tracker page.

Key repo paths:
- Firmware: `iot/GPS/` (PlatformIO project present)
- Admin tracker UI: `src/app/admin/tracker/` and `src/components/admin/gps-tracker-map.tsx`
- Data helpers: `src/lib/gps-data`

References:
- Waveshare ESP32-S3-A7670E-4G Wiki: https://www.waveshare.com/wiki/ESP32-S3-A7670E-4G
- MQTT AT command examples and demos are on the wiki under “Cat-1 Module Command Set” and “Waveshare Cloud Application”.

---

## 1) Hardware checklist

- Insert a valid nano-SIM with data.
- Attach the LTE and GNSS antennas to the A7670E module connectors.
- Power the board (USB‑C and/or 18650 per Waveshare docs). Ensure the module network LED indicates registration once on air.
- DIP/USB channel switches: follow the wiki “Hardware Connection” to select the USB routing needed for your scenario (programming vs 4G module USB). Exact positions vary; use the board silkscreen and wiki images.

---

## 2) PlatformIO project (firmware)

Your repo already contains `iot/GPS/` with `platformio.ini`. Use VS Code + PlatformIO extension.

Recommended approach (flexible):
- Framework: Arduino (fast to iterate). ESP-IDF also works if preferred.
- Connectivity options:
  - Option A – SIMCom MQTT via AT commands (`AT+CMQTT*`).
  - Option B – PPP data via TinyGSM, then standard MQTT client (PubSubClient). This is generally more portable.
- GNSS: Read NMEA from the modem and parse with TinyGPS++ or minmea.

Suggested libraries to add in `platformio.ini` (no code yet):
```
lib_deps =
  knolleary/PubSubClient
  vshymanskyy/TinyGSM
  mikalhart/TinyGPSPlus
  circuit-digest/GeoLinker Lite
```
Note: Use only those needed for the chosen path.

---

## 3) MQTT broker

Pick a broker and create credentials:
- EMQX Cloud / HiveMQ Cloud (quick start), or AWS IoT Core (production-hardening).
- Prefer TLS over port 8883.

Topic scheme (recommended):
- `fleet/{deviceId}/location` (publish with QoS 1; retain the last message per device).

Telemetry JSON:
```json
{
  "id": "esp32s3-01",
  "lat": 14.5995,
  "lon": 120.9842,
  "speed": 32.1,
  "heading": 270,
  "ts": 1724310000000
}
```

Publish rate: every 5–10 seconds. Implement reconnect/backoff logic.

---

## 4) Using SIMCom MQTT AT commands (Option A)

From the wiki (summary; see full page for details):
1. Start MQTT service: `AT+CMQTTSTART`
2. Create client: `AT+CMQTTACCQ=0,"<clientId>",0`
3. Connect: `AT+CMQTTCONNECT=0,"tcp://<broker-host>:1883",20,1` (or MQTTS if supported)
4. Set topic length then topic: `AT+CMQTTTOPIC=0,<len>` then write topic (e.g., `fleet/esp32s3-01/location`)
5. Set payload length then payload JSON: `AT+CMQTTPAYLOAD=0,<len>`
6. Publish: `AT+CMQTTPUB=0,0,60`
7. Subscribe (optional): `AT+CMQTTSUB=0,<len>,1`
8. Stop/release when done: `AT+CMQTTSTOP`, `AT+CMQTTREL`

Also ensure APN/attach is configured per carrier before MQTT (see your operator’s APN). For initial testing, a serial terminal like SSCOM (linked on the wiki) is useful.

---

## 5) Using PPP + TinyGSM + PubSubClient (Option B)

- Bring up PPP data with TinyGSM (A7670E supported). Provide APN, user, pass.
- Once IP is live, connect PubSubClient to your broker and publish the JSON payload periodically.
- For GNSS: enable the modem’s GNSS, read NMEA via UART, parse with TinyGPS++.

This option lets you use standard Arduino MQTT libraries and simplifies switching brokers.

---

## 6) Optional: Circuit Digest GeoLinker

- Add the PlatformIO library “GeoLinker Lite” and follow its README to POST location to their cloud in parallel with MQTT.
- Useful if you want a hosted visualization besides your admin map.

---

## 7) Integrating with your Next.js app

Your current map component: `src/components/admin/gps-tracker-map.tsx`
- It already supports simulated vs API via `NEXT_PUBLIC_GPS_SOURCE` and uses `fetchCurrentGPSLocations()` from `src/lib/gps-data`.

Recommended app flow:
- Create a small backend consumer (worker or server) that subscribes to MQTT topics and upserts latest points into your DB (e.g., Supabase tables `device_locations`, `device_location_events`).
- Expose endpoints for the front-end to fetch:
  - `GET /api/tracker/locations` → array of latest points
  - `GET /api/tracker/stream` (SSE or WebSocket) → live updates
- In `.env.local`, set:
```
NEXT_PUBLIC_GPS_SOURCE=api
NEXT_PUBLIC_GPS_STALE_SECONDS=60
```
- Update `gps-tracker-map.tsx` to hit your new endpoints (no code included here).

Data contract (front-end expects):
```ts
{
  id: string,
  carId?: string,
  carModel?: string,
  carPlate?: string,
  latitude: number,
  longitude: number,
  speed: number,
  heading: number,
  timestamp: string | number // ISO or ms epoch
}
```

---

## 8) Testing

- Outdoor test for GNSS lock; verify lat/lon change.
- Validate MQTT connect/publish using broker dashboard.
- Confirm your backend consumer persists rows and the admin map updates within ≤ 7s P95.

---

## 9) Production considerations

- TLS certs and per-device credentials.
- Rate limits and backoff to protect broker.
- Power budgeting with 18650 + solar options (see wiki “Solar Panel Charging”).
- Monitoring: device heartbeats, last seen timestamps, publish error counts.

---

## 10) Quick checklist

- [ ] SIM inserted, APN known, antennas attached
- [ ] Broker ready (host, port, TLS, creds)
- [ ] PlatformIO builds and flashes
- [ ] Device publishes JSON every 5–10s
- [ ] Backend ingests & stores latest + history
- [ ] Admin map shows live locations
