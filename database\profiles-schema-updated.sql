-- Updated profiles table schema with super_admin role support

CREATE TABLE public.profiles (
  id uuid NOT NULL,
  email text NOT NULL,
  full_name text NULL,
  phone text NULL,
  role text NOT NULL DEFAULT 'customer'::text,
  avatar_url text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  CONSTRAINT profiles_pkey PRIMARY KEY (id),
  CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users (id) ON DELETE CASCADE,
  CONSTRAINT profiles_role_check CHECK (
    (
      role = ANY (ARRAY['customer'::text, 'admin'::text, 'super_admin'::text])
    )
  )
) TABLESPACE pg_default;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles USING btree (role) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles USING btree (email) TABLESPACE pg_default;

-- <PERSON>reate trigger for updated_at timestamp
CREATE TRIGGER handle_updated_at_profiles 
  BEFORE UPDATE ON profiles 
  FOR EACH ROW 
  EXECUTE FUNCTION handle_updated_at();

-- Add comment for documentation
COMMENT ON COLUMN public.profiles.role IS 'User role: customer, admin, or super_admin. Super admin has full system access including account management.';
