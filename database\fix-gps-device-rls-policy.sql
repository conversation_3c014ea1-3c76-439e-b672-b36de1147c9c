-- ============================================
-- FIX: GPS Device RLS Policy - Allow Admins
-- ============================================
-- Issue: Only super_admin can manage GPS devices
-- Solution: Allow both admin and super_admin roles

-- Drop the existing restrictive policy
DROP POLICY IF EXISTS "GPS device mappings are manageable by super admin" ON public.gps_device_mapping;

-- Create new policy allowing both admin and super_admin
CREATE POLICY "GPS device mappings are manageable by admins" ON public.gps_device_mapping
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role IN ('admin', 'super_admin')
    )
  );

-- Verify the policy was created successfully
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'gps_device_mapping';

-- Test policy with current user (should show if you have permission)
SELECT 
    auth.uid() as current_user_id,
    p.role as user_role,
    CASE 
        WHEN p.role IN ('admin', 'super_admin') THEN '✅ Can manage GPS devices'
        ELSE '❌ Cannot manage GPS devices'
    END as permission_status
FROM profiles p 
WHERE p.id = auth.uid();
