/*
 * ESP32-S3-A7670E-4G Automotive GPS Tracker
 * Optimized for real-time car tracking with enhanced features
 */

#include <TinyGPS++.h>
#include <ArduinoJson.h>
#include <Arduino.h>
#include <PubSubClient.h>
#include <WiFi.h>
#include <Wire.h>
#include <EEPROM.h>

#define MSG_BUFFER_SIZE (50)
#define MAX17048_I2C_ADDRESS 0x36

// ✅ AUTOMOTIVE CONFIG - Update these for your setup
// IMPORTANT: Change these values to match your Waveshare Cloud account
const char *clientID = "your_client_id";              // Your unique tracker ID
char sub[] = "Sub/1149/37/your_client_id";            // Subscription topic  
char pub[] = "Pub/1149/37/your_client_id";            // Publishing topic
const char *mqtt_server = "mqtt.waveshare.cloud";

// ✅ AUTOMOTIVE-SPECIFIC SETTINGS (Customize as needed)
#define IGNITION_PIN 35              // Monitor ignition status (connect to 12V ignition)
#define GPS_UPDATE_INTERVAL 10000    // 10 seconds for real-time tracking
#define STATIONARY_INTERVAL 60000    // 1 minute when parked
#define MAX_SPEED_THRESHOLD 120.0    // km/h speed alert threshold
#define GEOFENCE_RADIUS 100.0        // meters for geofencing
#define EMERGENCY_PIN 0              // Emergency/panic button (optional)
#define STATUS_LED_PIN 2             // Status LED for visual feedback

StaticJsonDocument<500> sendJson;
StaticJsonDocument<400> readJson;
WiFiClient espClient;
PubSubClient client(espClient);

// GPS and Hardware Configuration
static const int RXPin = 18, TXPin = 17;
static const uint32_t GPSBaud = 9600;
TinyGPSPlus gps;
String rev;

// A7670E Control Pins
#define MODEM_PWRKEY_PIN 4
#define MODEM_POWER_ON_PIN 33

// Automotive State Variables
bool ignitionOn = false;
bool isMoving = false;
bool modemReady = false;
bool gnssEnabled = false;
float lastLat = 0.0, lastLng = 0.0;
unsigned long lastGPSUpdate = 0;
unsigned long lastMotionTime = 0;
unsigned long tripDistance = 0.0;
float maxSpeed = 0.0;

// Vehicle tracking data
struct VehicleState {
  float latitude;
  float longitude;
  float speed;
  float course;
  float altitude;
  int satellites;
  float hdop;
  bool engineOn;
  float batteryVoltage;
  unsigned long timestamp;
  float tripOdometer;
} vehicle;

void SentSerial(const char *p_char) {
  Serial.printf("AT: %s\n", p_char);
  for (int i = 0; i < strlen(p_char); i++) {
    Serial1.write(p_char[i]);
    delay(5);
  }
  Serial1.write('\r');
  delay(5);
  Serial1.write('\n');
  delay(100);
}

bool SentMessage(const char *p_char, unsigned long timeout = 5000) {
  SentSerial(p_char);
  unsigned long start = millis();
  String response = "";
  
  while (millis() - start < timeout) {
    if (Serial1.available()) {
      char c = Serial1.read();
      response += c;
      
      if (response.indexOf("OK") >= 0) {
        Serial.printf("✓ %s\n", response.c_str());
        return true;
      }
      if (response.indexOf("ERROR") >= 0) {
        Serial.printf("✗ %s\n", response.c_str());
        return false;
      }
    }
    yield();
  }
  Serial.printf("✗ Timeout: %s\n", p_char);
  return false;
}

void setup() {
  Serial.begin(115200);
  Serial.println();
  Serial.println("=================================");
  Serial.println("  ESP32-S3-A7670E GPS TRACKER");
  Serial.println("   Automotive Real-Time Tracking");
  Serial.println("     Version 2.0 - Enhanced");
  Serial.println("=================================");
  
  EEPROM.begin(512);
  Wire.begin(3, 2);
  
  // ✅ AUTOMOTIVE POWER MANAGEMENT & GPIO SETUP
  pinMode(IGNITION_PIN, INPUT_PULLUP);     // Ignition detection
  pinMode(EMERGENCY_PIN, INPUT_PULLUP);    // Emergency button
  pinMode(STATUS_LED_PIN, OUTPUT);         // Status LED
  pinMode(MODEM_POWER_ON_PIN, OUTPUT);     // A7670E power control
  pinMode(MODEM_PWRKEY_PIN, OUTPUT);       // A7670E power key
  
  // Initial LED status
  digitalWrite(STATUS_LED_PIN, HIGH);      // Power on indicator
  
  Serial.println("\n🚗 AUTOMOTIVE FEATURES:");
  Serial.println("   • Real-time GPS tracking (10s updates)");
  Serial.println("   • Ignition detection & trip logging");
  Serial.println("   • Speed monitoring & alerts");
  Serial.println("   • 4G connectivity with MQTT cloud");
  Serial.println("   • Battery monitoring & power management");
  Serial.println("   • Emergency features & geofencing");
  Serial.println();
  
  // Enhanced power sequence for A7670E
  initializeModem();
  
  // Initialize GPS tracking
  Serial1.begin(GPSBaud, SERIAL_8N1, RXPin, TXPin);
  
  // Test A7670E communication
  if (testModemCommunication()) {
    modemReady = true;
    enableGNSS();
    setupCellularConnection();
  } else {
    Serial.println("❌ Modem communication failed - check hardware");
  }
  
  // Initialize MQTT for cloud tracking
  client.setServer(mqtt_server, 1883);
  client.setCallback(mqttCallback);
  
  // Load trip data from EEPROM
  loadTripData();
  
  Serial.println("✅ Automotive GPS Tracker Ready");
  Serial.println("Waiting for GPS fix...");
}

void loop() {
  // ✅ AUTOMOTIVE MAIN LOOP
  
  // Check ignition status
  checkIgnitionStatus();
  
  // Update GPS data
  updateGPSData();
  
  // Determine vehicle state
  analyzeVehicleMovement();
  
  // Handle MQTT connection
  if (!client.connected()) {
    reconnectMQTT();
  }
  client.loop();
  
  // Send tracking data based on vehicle state
  unsigned long updateInterval = isMoving ? GPS_UPDATE_INTERVAL : STATIONARY_INTERVAL;
  
  if (millis() - lastGPSUpdate > updateInterval) {
    if (gps.location.isValid()) {
      updateVehicleState();
      sendTrackingData();
      lastGPSUpdate = millis();
    }
  }
  
  // ✅ SAFETY & SECURITY MONITORING
  checkSpeedAlert();
  checkGeofence();
  
  // Check emergency button
  if (digitalRead(EMERGENCY_PIN) == LOW) {
    static unsigned long lastEmergency = 0;
    if (millis() - lastEmergency > 5000) {  // Debounce 5 seconds
      sendEmergencyAlert();
      lastEmergency = millis();
    }
  }
  
  // Smart delay for GPS processing
  smartDelay(1000);
  
  // Periodic status report and diagnostics
  static unsigned long lastStatusReport = 0;
  if (millis() - lastStatusReport > 300000) {  // Every 5 minutes
    printVehicleStatus();
    lastStatusReport = millis();
  }
  
  // Run diagnostics every 30 minutes
  static unsigned long lastDiagnostics = 0;
  if (millis() - lastDiagnostics > 1800000) {  // Every 30 minutes
    runDiagnostics();
    lastDiagnostics = millis();
  }
}

void initializeModem() {
  Serial.println("🔋 Initializing A7670E Modem...");
  
  digitalWrite(MODEM_POWER_ON_PIN, LOW);
  digitalWrite(MODEM_PWRKEY_PIN, HIGH);
  delay(100);
  
  digitalWrite(MODEM_POWER_ON_PIN, HIGH);
  delay(1000);
  
  digitalWrite(MODEM_PWRKEY_PIN, LOW);
  delay(3000);  // Extended PWRKEY pulse
  digitalWrite(MODEM_PWRKEY_PIN, HIGH);
  
  delay(10000);  // Wait for modem boot
}

bool testModemCommunication() {
  Serial.println("📡 Testing A7670E Communication...");
  
  for (int attempt = 1; attempt <= 10; attempt++) {
    Serial.printf("Attempt %d/10...\n", attempt);
    
    if (SentMessage("AT")) {
      Serial.println("✅ A7670E Communication Established!");
      return true;
    }
    delay(2000);
  }
  return false;
}

void enableGNSS() {
  Serial.println("🛰️ Enabling GNSS...");
  
  SentMessage("AT+CGNSSPWR=1", 10000);
  delay(5000);
  
  SentMessage("AT+CGNSSTST=1", 3000);
  SentMessage("AT+CGNSSPORTSWITCH=0,1", 3000);
  
  // Configure for automotive use - GPS + GLONASS + BDS
  SentMessage("AT+CGNSSMODESELN=1", 3000);
  
  gnssEnabled = true;
  Serial.println("✅ GNSS Enabled for Automotive Tracking");
}

void setupCellularConnection() {
  Serial.println("📶 Setting up Cellular Connection...");
  
  // Check network registration and signal quality
  Serial.println("Checking network registration...");
  SentMessage("AT+CREG?", 3000);    // Network registration status
  
  Serial.println("Checking signal quality...");
  SentMessage("AT+CSQ", 3000);      // Signal strength
  
  // Additional network information
  SentMessage("AT+COPS?", 3000);    // Current operator
  SentMessage("AT+CIMI", 3000);     // SIM card IMSI
  
  // Set SMS notifications for remote monitoring
  SentMessage("AT+CNMI=2,2,0,0,0", 3000);
  
  Serial.println("✅ Cellular setup complete");
}

void checkIgnitionStatus() {
  bool currentIgnition = digitalRead(IGNITION_PIN) == LOW;  // Assuming active low
  
  if (currentIgnition != ignitionOn) {
    ignitionOn = currentIgnition;
    Serial.printf("🔑 Ignition: %s\n", ignitionOn ? "ON" : "OFF");
    
    if (ignitionOn) {
      // Trip started
      maxSpeed = 0.0;
      tripDistance = 0.0;
      Serial.println("🚗 Trip Started");
    } else {
      // Trip ended
      saveTripData();
      Serial.println("🅿️ Vehicle Parked");
    }
  }
}

void updateGPSData() {
  // Process incoming GPS data
  while (Serial1.available()) {
    if (gps.encode(Serial1.read())) {
      // New GPS sentence processed
    }
  }
}

void analyzeVehicleMovement() {
  if (gps.location.isValid() && gps.speed.isValid()) {
    float currentSpeed = gps.speed.kmph();
    
    // Determine if vehicle is moving (>5 km/h to avoid GPS noise)
    bool wasMoving = isMoving;
    isMoving = (currentSpeed > 5.0);
    
    if (isMoving) {
      lastMotionTime = millis();
      if (currentSpeed > maxSpeed) {
        maxSpeed = currentSpeed;
      }
    }
    
    // Log movement state changes
    if (wasMoving != isMoving) {
      Serial.printf("🚗 Vehicle %s (Speed: %.1f km/h)\n", 
                   isMoving ? "MOVING" : "STOPPED", currentSpeed);
    }
  }
}

void updateVehicleState() {
  vehicle.latitude = gps.location.lat();
  vehicle.longitude = gps.location.lng();
  vehicle.speed = gps.speed.kmph();
  vehicle.course = gps.course.deg();
  vehicle.altitude = gps.altitude.meters();
  vehicle.satellites = gps.satellites.value();
  vehicle.hdop = gps.hdop.hdop();
  vehicle.engineOn = ignitionOn;
  vehicle.batteryVoltage = readBatteryVoltage();
  vehicle.timestamp = millis();
  
  // Calculate trip distance
  if (lastLat != 0.0 && lastLng != 0.0) {
    float distance = TinyGPSPlus::distanceBetween(
      lastLat, lastLng, vehicle.latitude, vehicle.longitude);
    tripDistance += distance;
    vehicle.tripOdometer = tripDistance;
  }
  
  lastLat = vehicle.latitude;
  lastLng = vehicle.longitude;
}

void sendTrackingData() {
  // ✅ COMPREHENSIVE AUTOMOTIVE TRACKING DATA
  sendJson.clear();
  
  // Core GPS data (matches Waveshare Cloud format)
  sendJson["data"]["Latitude"] = vehicle.latitude;
  sendJson["data"]["Longitude"] = vehicle.longitude;
  sendJson["data"]["speed"] = vehicle.speed;
  sendJson["data"]["course"] = vehicle.course;
  sendJson["data"]["altitude"] = vehicle.altitude;
  sendJson["data"]["satellites"] = vehicle.satellites;
  sendJson["data"]["hdop"] = vehicle.hdop;
  
  // Automotive-specific data
  sendJson["data"]["ignition"] = vehicle.engineOn;
  sendJson["data"]["moving"] = isMoving;
  sendJson["data"]["batteryLevel"] = vehicle.batteryVoltage;
  sendJson["data"]["timestamp"] = vehicle.timestamp;
  sendJson["data"]["maxSpeed"] = maxSpeed;
  sendJson["data"]["tripDistance"] = tripDistance / 1000.0;  // km
  
  // Vehicle identification and status
  sendJson["data"]["vehicleId"] = clientID;
  sendJson["data"]["trackingMode"] = isMoving ? "ACTIVE" : "PARKED";
  
  // GPS quality indicators
  sendJson["data"]["gpsQuality"] = (vehicle.satellites >= 4 && vehicle.hdop < 5.0) ? "GOOD" : "POOR";
  sendJson["data"]["lastUpdate"] = millis() / 1000;  // seconds since boot
  
  // Publish to cloud
  String jsonString;
  serializeJson(sendJson, jsonString);
  
  // Visual feedback
  digitalWrite(STATUS_LED_PIN, LOW);  // Flash LED for transmission
  
  if (client.publish(pub, jsonString.c_str())) {
    Serial.printf("✅ Data sent: %.6f,%.6f | Speed:%.1fkm/h | Sats:%d\n", 
                 vehicle.latitude, vehicle.longitude, vehicle.speed, vehicle.satellites);
    digitalWrite(STATUS_LED_PIN, HIGH);
  } else {
    Serial.println("❌ MQTT publish failed - check connection");
    // Keep LED off to indicate transmission failure
  }
}

void checkSpeedAlert() {
  if (gps.speed.isValid() && gps.speed.kmph() > MAX_SPEED_THRESHOLD) {
    Serial.printf("🚨 SPEED ALERT: %.1f km/h (Limit: %.1f)\n", 
                 gps.speed.kmph(), MAX_SPEED_THRESHOLD);
    
    // Flash LED rapidly for speed warning
    for (int i = 0; i < 6; i++) {
      digitalWrite(STATUS_LED_PIN, LOW);
      delay(100);
      digitalWrite(STATUS_LED_PIN, HIGH);
      delay(100);
    }
    
    // Send speed alert to cloud
    StaticJsonDocument<300> alertJson;
    alertJson["alert"]["type"] = "SPEED_VIOLATION";
    alertJson["alert"]["vehicleId"] = clientID;
    alertJson["alert"]["speed"] = gps.speed.kmph();
    alertJson["alert"]["speedLimit"] = MAX_SPEED_THRESHOLD;
    alertJson["alert"]["timestamp"] = millis();
    alertJson["alert"]["location"]["lat"] = gps.location.lat();
    alertJson["alert"]["location"]["lng"] = gps.location.lng();
    
    String alertString;
    serializeJson(alertJson, alertString);
    
    // Publish to alerts topic
    String alertTopic = "alerts/1149/37/" + String(clientID);
    client.publish(alertTopic.c_str(), alertString.c_str());
  }
}

// ✅ EMERGENCY ALERT FUNCTION (matches guide documentation)
void sendEmergencyAlert() {
  Serial.println("🚨 EMERGENCY ALERT TRIGGERED!");
  
  StaticJsonDocument<300> emergencyJson;
  emergencyJson["emergency"]["type"] = "PANIC_BUTTON";
  emergencyJson["emergency"]["vehicleId"] = clientID;
  emergencyJson["emergency"]["timestamp"] = millis();
  emergencyJson["emergency"]["location"]["lat"] = gps.location.lat();
  emergencyJson["emergency"]["location"]["lng"] = gps.location.lng();
  emergencyJson["emergency"]["speed"] = gps.speed.kmph();
  emergencyJson["emergency"]["course"] = gps.course.deg();
  
  String emergencyString;
  serializeJson(emergencyJson, emergencyString);
  
  // Send to emergency topic with high priority
  String emergencyTopic = "emergency/1149/37/" + String(clientID);
  client.publish(emergencyTopic.c_str(), emergencyString.c_str());
  
  // Flash LED rapidly for emergency indication
  for (int i = 0; i < 20; i++) {
    digitalWrite(STATUS_LED_PIN, LOW);
    delay(50);
    digitalWrite(STATUS_LED_PIN, HIGH);
    delay(50);
  }
}

// ✅ GEOFENCING FUNCTION (matches guide documentation)
bool isInGeofence(float lat, float lng, float centerLat, float centerLng, float radius) {
  float distance = TinyGPSPlus::distanceBetween(lat, lng, centerLat, centerLng);
  return distance <= radius;
}

void checkGeofence() {
  // Example geofence - update coordinates for your area
  float homeLat = 14.123456;  // Update with your coordinates
  float homeLng = 121.654321;
  
  if (gps.location.isValid()) {
    bool inGeofence = isInGeofence(gps.location.lat(), gps.location.lng(), 
                                  homeLat, homeLng, GEOFENCE_RADIUS);
    
    static bool wasInGeofence = true;
    if (wasInGeofence != inGeofence) {
      Serial.printf("🏠 Geofence %s\n", inGeofence ? "ENTERED" : "EXITED");
      
      // Send geofence alert
      StaticJsonDocument<300> geoJson;
      geoJson["geofence"]["type"] = inGeofence ? "ENTER" : "EXIT";
      geoJson["geofence"]["vehicleId"] = clientID;
      geoJson["geofence"]["timestamp"] = millis();
      geoJson["geofence"]["location"]["lat"] = gps.location.lat();
      geoJson["geofence"]["location"]["lng"] = gps.location.lng();
      
      String geoString;
      serializeJson(geoJson, geoString);
      
      String geoTopic = "geofence/1149/37/" + String(clientID);
      client.publish(geoTopic.c_str(), geoString.c_str());
      
      wasInGeofence = inGeofence;
    }
  }
}

void printVehicleStatus() {
  Serial.println("\n=================================");
  Serial.println("      VEHICLE STATUS REPORT");
  Serial.println("=================================");
  Serial.printf("📍 Position: %.6f, %.6f\n", vehicle.latitude, vehicle.longitude);
  Serial.printf("🚗 Speed: %.1f km/h (Max: %.1f km/h)\n", vehicle.speed, maxSpeed);
  Serial.printf("🔑 Ignition: %s | Moving: %s\n", 
               vehicle.engineOn ? "ON" : "OFF",
               isMoving ? "YES" : "NO");
  Serial.printf("🛰️ Satellites: %d | HDOP: %.1f\n", vehicle.satellites, vehicle.hdop);
  Serial.printf("📏 Trip Distance: %.2f km\n", tripDistance / 1000.0);
  Serial.printf("🔋 Battery: %.1fV\n", vehicle.batteryVoltage);
  Serial.printf("📡 MQTT: %s\n", client.connected() ? "Connected" : "Disconnected");
  Serial.printf("⏰ Uptime: %.1f hours\n", millis() / 3600000.0);
  
  // GPS Quality Assessment
  String gpsQuality = "UNKNOWN";
  if (vehicle.satellites >= 6 && vehicle.hdop < 2.0) gpsQuality = "EXCELLENT";
  else if (vehicle.satellites >= 4 && vehicle.hdop < 5.0) gpsQuality = "GOOD";
  else if (vehicle.satellites >= 3) gpsQuality = "FAIR";
  else gpsQuality = "POOR";
  
  Serial.printf("📊 GPS Quality: %s\n", gpsQuality.c_str());
  Serial.println("=================================\n");
}

float readBatteryVoltage() {
  Wire.beginTransmission(MAX17048_I2C_ADDRESS);
  Wire.write(0x02);
  if (Wire.endTransmission() != 0) {
    return 12.0;  // Assume car battery voltage
  }

  Wire.requestFrom(MAX17048_I2C_ADDRESS, 2);
  if (Wire.available() >= 2) {
    uint16_t soc = (Wire.read() << 8) | Wire.read();
    return (float)soc / 65535.0 * 15.0;  // Scale for automotive voltage
  }
  return 12.0;
}

void smartDelay(unsigned long ms) {
  unsigned long start = millis();
  do {
    while (Serial1.available()) {
      gps.encode(Serial1.read());
    }
    yield();
  } while (millis() - start < ms);
}

void mqttCallback(char *topic, byte *payload, unsigned int length) {
  String message;
  for (int i = 0; i < length; i++) {
    message += (char)payload[i];
  }
  
  Serial.println("📨 MQTT Command: " + message);
  
  // ✅ REMOTE COMMAND PROCESSING (matches guide documentation)
  if (message.indexOf("\"cmd\":\"locate\"") >= 0) {
    Serial.println("🎯 Location request received");
    if (gps.location.isValid()) {
      sendTrackingData();
    } else {
      Serial.println("⚠️ No GPS fix available");
    }
  }
  
  if (message.indexOf("\"cmd\":\"status\"") >= 0) {
    Serial.println("📊 Status request received");
    printVehicleStatus();
    sendTrackingData();
  }
  
  if (message.indexOf("\"cmd\":\"emergency\"") >= 0) {
    Serial.println("🚨 Emergency command received");
    sendEmergencyAlert();
  }
  
  // Parse JSON for more complex commands
  StaticJsonDocument<200> cmdJson;
  if (deserializeJson(cmdJson, message) == DeserializationError::Ok) {
    if (cmdJson.containsKey("updateInterval")) {
      // Dynamic update interval adjustment
      int newInterval = cmdJson["updateInterval"];
      if (newInterval >= 5000 && newInterval <= 300000) {
        Serial.printf("📡 Update interval changed to %d ms\n", newInterval);
        // Could implement dynamic interval change here
      }
    }
  }
}

void reconnectMQTT() {
  while (!client.connected()) {
    Serial.print("Connecting to MQTT...");
    if (client.connect(clientID)) {
      Serial.println(" connected!");
      client.subscribe(sub);
    } else {
      Serial.printf(" failed, rc=%d retrying...\n", client.state());
      delay(5000);
    }
  }
}

void loadTripData() {
  // Load persistent trip data from EEPROM
  EEPROM.get(0, tripDistance);
  EEPROM.get(sizeof(float), maxSpeed);
}

void saveTripData() {
  Serial.println("💾 Saving trip data to EEPROM...");
  
  // Save trip data to EEPROM with validation
  EEPROM.put(0, tripDistance);
  EEPROM.put(sizeof(float), maxSpeed);
  
  // Save trip statistics
  unsigned long tripDuration = millis() - lastMotionTime;
  EEPROM.put(sizeof(float) * 2, tripDuration);
  
  if (EEPROM.commit()) {
    Serial.printf("✅ Trip data saved: %.2f km, %.1f km/h max\n", 
                 tripDistance / 1000.0, maxSpeed);
  } else {
    Serial.println("❌ Failed to save trip data");
  }
}

// ✅ DIAGNOSTIC FUNCTIONS (matches troubleshooting guide)
void runDiagnostics() {
  Serial.println("\n🔧 RUNNING SYSTEM DIAGNOSTICS...");
  
  // Test A7670E communication
  Serial.println("Testing A7670E communication:");
  bool modemOk = SentMessage("AT", 3000);
  Serial.printf("  Modem response: %s\n", modemOk ? "✅ OK" : "❌ FAIL");
  
  // Check network status
  Serial.println("Checking network status:");
  SentMessage("AT+CREG?", 3000);  // Registration
  SentMessage("AT+CSQ", 3000);    // Signal quality
  SentMessage("AT+COPS?", 3000);  // Operator
  
  // Check GNSS status
  Serial.println("Checking GNSS status:");
  SentMessage("AT+CGNSSINFO", 3000);
  
  // Test GPIO pins
  Serial.println("Testing GPIO status:");
  Serial.printf("  Ignition pin (GPIO%d): %s\n", IGNITION_PIN, 
               digitalRead(IGNITION_PIN) ? "HIGH" : "LOW");
  Serial.printf("  Emergency pin (GPIO%d): %s\n", EMERGENCY_PIN,
               digitalRead(EMERGENCY_PIN) ? "HIGH" : "LOW");
  
  // Memory usage
  Serial.printf("Free heap: %d bytes\n", ESP.getFreeHeap());
  Serial.printf("Free PSRAM: %d bytes\n", ESP.getFreePsram());
  
  Serial.println("🔧 Diagnostics complete\n");
}
