"use client";

import * as React from "react";
import { startOfDay, endOfDay, startOfWeek, endOfWeek } from "date-fns";
import { db } from "@/lib/supabase/database";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ChevronLeft, ChevronRight, RefreshCw, Calendar, Settings, FileText, DollarSign, CheckCircle2, Info } from "lucide-react"
import { subWeeks, addWeeks, subMonths, addMonths } from "date-fns";
import {
  BookingToolbar,
  type BookingFilters,
} from "@/components/admin/bookings/booking-toolbar";
import { type Booking, type Payment, type TransmissionType, type CarType, type CarStatus, type CarCondition } from "@/lib/types";
import { Button } from "@/components/ui/button";
import { BookingTable } from "@/components/admin/bookings/booking-table";
import { BookingCalendar } from "@/components/admin/bookings/booking-calendar";
import { BookingDetailsDrawer } from "@/components/admin/bookings/booking-details-drawer";
import { ExtensionManagement } from "@/components/admin/extensions/extension-management";
import { verifyPayment, getPaymentForBooking } from "./actions/payment-actions";
import { useAdminAuth } from "@/components/auth/admin-auth-context";
import { getAllExtensionRequests } from "@/lib/services/admin-extension-service";
import { cn } from "@/lib/utils";
import { parseISO } from "date-fns";
import { logger, logWithContext, logError } from "@/lib/utils/logger";
import { AdminBookingsSkeleton } from "@/components/admin/loading/skeleton-components";


interface BookingRowData {
  id: string;
  booking_ref: string;
  customer_id: string;
  car_id: string;
  pickup_location: string;
  dropoff_location: string;
  pickup_datetime: string;
  dropoff_datetime: string;
  special_requests?: string;
  status: "Pending" | "Active" | "Completed" | "Cancelled";
  total_amount: number;
  created_at: string;
  updated_at: string;
  userName: string;
  carModel: string;
  from: Date;
  to: Date;
  days: number;
  payStatus: "Paid" | "Unpaid" | "Partial" | "Refunded";
  totalAmount: number;
}

export default function AdminBookingsPage() {
  const { user, profile, loading: authLoading } = useAdminAuth();

  // State management
  const [currentView, setCurrentView] = React.useState<"table" | "calendar" | "extensions">(
    "table"
  );
  const [calendarView, setCalendarView] = React.useState<"week" | "month">(
    "week"
  );
  const [isDrawerOpen, setIsDrawerOpen] = React.useState(false);
  const [selectedCalendarDate, setSelectedCalendarDate] = React.useState(new Date());
  const [isLegendOpen, setIsLegendOpen] = React.useState(false);
  const [selectedBooking, setSelectedBooking] = React.useState<any>(null);
  const [selectedPayment, setSelectedPayment] = React.useState<any>(null);
  const [selectedCustomer, setSelectedCustomer] = React.useState<any>(null);
  const [selectedCar, setSelectedCar] = React.useState<any>(null);
  const [page, setPage] = React.useState(1);
  // Removed redundant date filter state - filtering handled by BookingToolbar only
  const [loading, setLoading] = React.useState(true);
  const [rawBookings, setRawBookings] = React.useState<any[]>([]);
  const pageSize = 50;

  // Separate filter states for each view
  const [bookingFilters, setBookingFilters] = React.useState<BookingFilters>({
    search: "",
    status: "all",
    paymentStatus: "all",
    vehicle: "all",
    location: "all",
    extensionStatus: "all",
    dateRange: { from: null, to: null },
  });

  const [calendarFilters, setCalendarFilters] = React.useState<BookingFilters>({
    search: "",
    status: "all",
    paymentStatus: "all",
    vehicle: "all",
    location: "all",
    extensionStatus: "all",
    dateRange: { from: null, to: null },
  });

  const [extensionFilters, setExtensionFilters] = React.useState<BookingFilters>({
    search: "",
    status: "all",
    paymentStatus: "all",
    vehicle: "all",
    location: "all",
    extensionStatus: "all",
    dateRange: { from: null, to: null },
  });

  // Extension requests state
  const [extensionRequests, setExtensionRequests] = React.useState<any[]>([]);
  const [extensionLoading, setExtensionLoading] = React.useState(false);

  // Fetch extension requests data
  const fetchExtensionRequests = React.useCallback(async () => {
    try {
      setExtensionLoading(true);
      const { data, error } = await getAllExtensionRequests();
      if (error) {
        throw new Error(error.message || 'Failed to fetch extension requests');
      }
      setExtensionRequests(data || []);
    } catch (error) {
      logError("AdminBookings", "Error fetching extension requests", error);
      setExtensionRequests([]);
    } finally {
      setExtensionLoading(false);
    }
  }, []);

  // Fetch bookings data from Supabase
  const fetchBookings = React.useCallback(async () => {
    try {
      setLoading(true);
      const bookings = await db.getAllBookings();
      setRawBookings(bookings);
    } catch (error) {
      logError("AdminBookings", "Error fetching bookings", error);
      setRawBookings([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch initial data and subscribe to real-time updates
  React.useEffect(() => {
    fetchBookings();
    fetchExtensionRequests();

    const subscription = db.subscribeToBookings((payload) => {
      logWithContext("AdminBookings", "Booking change received!", payload);
      // Re-fetch all bookings to ensure data consistency with joins
      fetchBookings();
    });

    // Also subscribe to payments to reflect verification/rejection updates immediately
    const paymentsSub = db.subscribeToPayments((payload) => {
      logWithContext("AdminBookings", "Payment change received!", payload);
      fetchBookings();
    });

    // Cleanup subscription on component unmount
    return () => {
      subscription.unsubscribe();
      paymentsSub.unsubscribe();
    };
  }, [fetchBookings, fetchExtensionRequests]);

  // Transform raw booking data
  const allBookings = React.useMemo(() => {
    return rawBookings.map((booking: any) => {
      const from = new Date(booking.pickup_datetime);
      const to = new Date(booking.dropoff_datetime);
      
      // Calculate days properly for multi-day bookings (inclusive of both start and end dates)
      // Use start of day for both dates to ensure accurate day count
      const startDate = new Date(from.getFullYear(), from.getMonth(), from.getDate());
      const endDate = new Date(to.getFullYear(), to.getMonth(), to.getDate());
      const days = Math.max(
        1,
        Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1
      );
      // Determine payment status from latest payment record when available
      const payStatus = (() => {
        if (booking.status === "Cancelled") return "Refunded";
        const latest = booking.latest_payment_status as string | null | undefined;
        if (latest && typeof latest === "string") {
          const norm = latest.trim().toLowerCase();
          // Map database status values to admin display values
          if (norm === "paid") {
            return "Paid";
          }
          if (norm === "pending verification" || norm === "pending") {
            return "Unpaid";
          }
          if (norm === "rejected" || norm === "failed") {
            return "Unpaid";
          }
          if (norm === "refunded") {
            return "Refunded";
          }
        }
        return "Unpaid";
      })();

      return {
        id: booking.id,
        booking_ref: booking.booking_ref,
        customer_id: booking.customer_id,
        car_id: booking.car_id,
        pickup_location: booking.pickup_location,
        dropoff_location: booking.dropoff_location,
        pickup_datetime: booking.pickup_datetime,
        dropoff_datetime: booking.dropoff_datetime,
        special_requests: booking.special_requests,
        status: booking.status,
        total_amount: booking.total_amount,
        created_at: booking.created_at,
        updated_at: booking.updated_at,
        userName:
          booking.customer?.full_name ||
          booking.customer?.email ||
          "Unknown User",
        carModel: booking.car?.model || "Unknown Vehicle",
        from,
        to,
        days,
        payStatus: payStatus as "Paid" | "Unpaid" | "Partial" | "Refunded",
        totalAmount: booking.total_amount || 0,
      } as BookingRowData;
    });
  }, [rawBookings]);

  // Get unique vehicles for filter
  const availableVehicles = React.useMemo(() => {
    const vehicles = allBookings.map((b: BookingRowData) => b.carModel);
    return Array.from(new Set(vehicles)).filter(Boolean) as string[];
  }, [allBookings]);


  // Removed redundant date filter handler - filtering handled by BookingToolbar only

  // Get current filters based on active view
  const currentFilters = React.useMemo(() => {
    switch (currentView) {
      case "calendar":
        return calendarFilters;
      case "extensions":
        return extensionFilters;
      default:
        return bookingFilters;
    }
  }, [currentView, bookingFilters, calendarFilters, extensionFilters]);

  // Apply filters to bookings
  const filteredBookings = React.useMemo(() => {
    const query = currentFilters.search.trim().toLowerCase();

    return allBookings.filter((booking: BookingRowData) => {
      // Text search
      if (
        query &&
        !(
          booking.id.toLowerCase().includes(query) ||
          (booking.booking_ref && booking.booking_ref.toLowerCase().includes(query)) ||
          booking.userName.toLowerCase().includes(query) ||
          booking.carModel.toLowerCase().includes(query) ||
          booking.pickup_location.toLowerCase().includes(query) ||
          booking.dropoff_location.toLowerCase().includes(query)
        )
      ) {
        return false;
      }

      // Status filter
      if (currentFilters.status !== "all" && booking.status !== currentFilters.status) {
        return false;
      }

      // Payment status filter
      if (
        currentFilters.paymentStatus !== "all" &&
        booking.payStatus !== currentFilters.paymentStatus
      ) {
        return false;
      }

      // Vehicle filter
      if (currentFilters.vehicle !== "all" && booking.carModel !== currentFilters.vehicle) {
        return false;
      }

      // Date range filter from toolbar
      if (currentFilters.dateRange.from && currentFilters.dateRange.to) {
        const bookingDate = booking.from;
        if (
          bookingDate < currentFilters.dateRange.from ||
          bookingDate > currentFilters.dateRange.to
        ) {
          return false;
        }
      }

      return true;
    });
  }, [allBookings, currentFilters]);

  // Sort bookings by pickup date (newest first)
  const sortedBookings = React.useMemo(() => {
    return [...filteredBookings].sort(
      (a, b) => b.from.getTime() - a.from.getTime()
    );
  }, [filteredBookings]);

  // Paginate for table view
  const paginatedBookings = React.useMemo(() => {
    const start = (page - 1) * pageSize;
    return sortedBookings.slice(start, start + pageSize);
  }, [sortedBookings, page, pageSize]);

  const totalPages = Math.ceil(sortedBookings.length / pageSize);

  // Event handlers
  const handleBookingClick = async (booking: BookingRowData) => {
    logWithContext("AdminBookings", "Booking clicked:", booking);
    setSelectedBooking(booking);
    setSelectedPayment(null); // Reset payment data
    setIsDrawerOpen(true);

    // Find the original booking data with customer and car details
    const originalBooking = rawBookings.find(b => b.id === booking.id);
    logWithContext("AdminBookings", "Original booking data:", originalBooking);

    // Set customer and car data from original booking
    if (originalBooking) {
      setSelectedCustomer(originalBooking.customer);
      setSelectedCar(originalBooking.car);
    }

    // Fetch payment data for this booking
    try {
      logWithContext("AdminBookings", "Fetching payment for booking ID:", booking.id);
      const result = await getPaymentForBooking(booking.id);
      logWithContext("AdminBookings", "Payment fetch result:", result);
      if (result.data) {
        logWithContext("AdminBookings", "Setting payment data:", result.data);
        setSelectedPayment(result.data);
      } else {
        logWithContext("AdminBookings", "No payment data found for booking:", booking.id);
      }
    } catch (error) {
      logError("AdminBookings", "Error fetching payment data", error);
    }
  };

  // Handle adding booking to on-page calendar
  const handleAddToOnPageCalendar = (booking: BookingRowData) => {
    logWithContext("AdminBookings", "Adding booking to on-page calendar:", booking);
    // Switch to calendar view if not already there
    if (currentView !== "calendar") {
      setCurrentView("calendar");
    }
    
    // Set calendar date to booking start date
    setSelectedCalendarDate(booking.from);
    
    // Close any open drawer
    setIsDrawerOpen(false);
  };

  // Get current filter setter based on active view
  const getCurrentFilterSetter = React.useCallback(() => {
    switch (currentView) {
      case "calendar":
        return setCalendarFilters;
      case "extensions":
        return setExtensionFilters;
      default:
        return setBookingFilters;
    }
  }, [currentView]);

  const handleStatusFilterClick = (status: string | null) => {
    const setCurrentFilters = getCurrentFilterSetter();
    setCurrentFilters((prev: BookingFilters) => ({ ...prev, status: status || "all" }));
    setPage(1);
  };

  const handleStatusChange = async (bookingId: string, newStatus: string) => {
    try {
      const success = await db.updateBookingStatus(bookingId, newStatus as any);
      if (success) {
        // Refresh bookings data
        const bookings = await db.getAllBookings();
        setRawBookings(bookings);
      }
    } catch (error) {
      logError("AdminBookings", "Error updating booking status", error);
    }
  };

  const handlePaymentStatusChange = async (
    bookingId: string,
    newStatus: string
  ) => {
    try {
      // For now, just log - payment status updates would need separate implementation
      logWithContext("AdminBookings", "Change payment status:", { bookingId, newStatus });
    } catch (error) {
      logError("AdminBookings", "Error updating payment status", error);
    }
  };

  const handlePaymentVerification = async (
    paymentId: string,
    action: "verify" | "reject",
    notes?: string
  ) => {
    try {
      logWithContext("AdminBookings", "Payment verification:", { paymentId, action, notes });
      const result = await verifyPayment(paymentId, action, notes);
      logWithContext("AdminBookings", "Payment verification result:", result);
      if (result.error) {
        logError("AdminBookings", "Payment verification error", result.error);
        return;
      }

      logWithContext("AdminBookings", 
        `Payment ${action === "verify" ? "verified" : "rejected"} successfully!`
      );

      // Refresh payment data
      if (selectedBooking) {
        logWithContext("AdminBookings", "Refreshing payment data for booking:", selectedBooking.id);
        const paymentResult = await getPaymentForBooking(selectedBooking.id);
        logWithContext("AdminBookings", "Refreshed payment result:", paymentResult);
        if (paymentResult.data) {
          setSelectedPayment(paymentResult.data);
        }
      }

      // Refresh bookings list
      const bookings = await db.getAllBookings();
      setRawBookings(bookings);
    } catch (error) {
      logError("AdminBookings", "Error verifying payment", error);
      logger.error("Failed to verify payment. Please try again.");
    }
  };

  // Reset page when filters change
  React.useEffect(() => {
    setPage(1);
  }, [currentFilters]);

  // Wait for auth loading to complete before rendering
  if (authLoading) {
    return <AdminBookingsSkeleton />;
  }

  if (loading) {
    return <AdminBookingsSkeleton />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Modern Header with Gradient Background */}
      <div className="relative bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 text-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-black/10">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-y-1 scale-110"></div>
        </div>
        
        <div className="relative px-3 xs:px-4 py-4 xs:py-6 sm:py-8 lg:px-8 lg:py-12">
          <header className="max-w-7xl mx-auto">
            <div className="flex items-center gap-2 xs:gap-3 mb-2">
              <div className="w-8 h-8 xs:w-10 xs:h-10 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm flex-shrink-0">
                <span className="text-lg xs:text-xl">📋</span>
              </div>
              <h1 className="text-xl xs:text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight min-w-0">
                Bookings
              </h1>
            </div>
            
            {/* Mobile Stats and View Toggle */}
            <div className="flex flex-col space-y-3 xs:space-y-4 mt-4 xs:mt-6">
              {/* Stats Pill */}
              <div className="bg-white/10 backdrop-blur-sm rounded-full px-3 xs:px-4 py-2 border border-white/20 w-fit">
                <span className="text-xs xs:text-sm font-medium">{filteredBookings.length} Total Bookings</span>
              </div>
              
              {/* Mobile-Optimized View Toggle */}
              <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-full p-1 border border-white/20 w-full xs:w-fit overflow-hidden">
                <Button
                  variant={currentView === "table" ? "primary" : "secondary"}
                  size="sm"
                  onClick={() => setCurrentView("table")}
                  className={cn(
                    "flex-1 xs:flex-none px-2 xs:px-3 sm:px-4 py-2 rounded-full text-xs xs:text-sm font-semibold transition-all duration-300 border-0 min-h-[44px] whitespace-nowrap",
                    currentView === "table"
                      ? "bg-gradient-to-r from-emerald-500 to-green-600 text-white shadow-lg"
                      : "text-white/70 bg-transparent hover:text-white hover:bg-white/20"
                  )}
                >
                  <span className="hidden xs:inline">Table</span>
                  <span className="xs:hidden">📊</span>
                </Button>
                <Button
                  variant={currentView === "calendar" ? "primary" : "secondary"}
                  size="sm"
                  onClick={() => setCurrentView("calendar")}
                  className={cn(
                    "flex-1 xs:flex-none px-2 xs:px-3 sm:px-4 py-2 rounded-full text-xs xs:text-sm font-semibold transition-all duration-300 border-0 min-h-[44px] whitespace-nowrap",
                    currentView === "calendar"
                      ? "bg-gradient-to-r from-purple-500 to-indigo-600 text-white shadow-lg"
                      : "text-white/70 bg-transparent hover:text-white hover:bg-white/20"
                  )}
                >
                  <span className="hidden xs:inline">Calendar</span>
                  <span className="xs:hidden">📅</span>
                </Button>
                <Button
                  variant={currentView === "extensions" ? "primary" : "secondary"}
                  size="sm"
                  onClick={() => setCurrentView("extensions")}
                  className={cn(
                    "flex-1 xs:flex-none px-2 xs:px-3 sm:px-4 py-2 rounded-full text-xs xs:text-sm font-semibold transition-all duration-300 border-0 min-h-[44px] whitespace-nowrap",
                    currentView === "extensions"
                      ? "bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg"
                      : "text-white/70 bg-transparent hover:text-white hover:bg-white/20"
                  )}
                >
                  <span className="hidden xs:inline">Extensions</span>
                  <span className="xs:hidden">⏱️</span>
                </Button>
              </div>
            </div>
          </header>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="space-y-4 xs:space-y-6 lg:space-y-8 p-3 xs:p-4 lg:p-8 max-w-7xl mx-auto">
        {/* Enhanced Toolbar with Card Design */}
        <div className="bg-white rounded-xl xs:rounded-2xl shadow-lg xs:shadow-xl border border-gray-200 xs:border-2 xs:border-gray-300 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 px-3 xs:px-4 sm:px-6 py-3 xs:py-4 sm:py-5 border-b border-gray-200 xs:border-b-2 xs:border-gray-300">
            <h2 className="text-base xs:text-lg font-bold text-gray-900 flex items-center gap-2 xs:gap-3">
              <div className="w-6 h-6 xs:w-8 xs:h-8 bg-blue-500 rounded-lg xs:rounded-xl flex items-center justify-center shadow-md flex-shrink-0">
                <span className="text-sm xs:text-lg text-white">🔍</span>
              </div>
              <span className="min-w-0 truncate">Filter & Search</span>
            </h2>
          </div>
          <div className="p-3 xs:p-4 sm:p-6 lg:p-8 bg-gradient-to-br from-gray-50 to-white">
            <BookingToolbar
              filters={currentFilters}
              onFiltersChange={getCurrentFilterSetter()}
              availableVehicles={availableVehicles}
              currentView={currentView}
            />
          </div>
        </div>

        {/* Enhanced Content Area */}
        <div className="bg-white rounded-xl xs:rounded-2xl shadow-lg xs:shadow-xl border border-gray-200 xs:border-2 xs:border-gray-300 overflow-hidden">
          <div className="bg-gradient-to-r from-green-50 via-emerald-50 to-teal-50 px-3 xs:px-4 sm:px-6 py-3 xs:py-4 sm:py-5 border-b border-gray-200 xs:border-b-2 xs:border-gray-300">
            <div className="flex flex-col xs:flex-row xs:items-center gap-2 xs:gap-3">
              <div className="flex items-center gap-2 xs:gap-3 min-w-0">
                <div className="w-6 h-6 xs:w-8 xs:h-8 bg-green-500 rounded-lg xs:rounded-xl flex items-center justify-center shadow-md flex-shrink-0">
                  <span className="text-sm xs:text-lg text-white">{currentView === 'table' ? '📊' : currentView === 'calendar' ? '📅' : '⏱️'}</span>
                </div>
                <h2 className="text-base xs:text-lg font-bold text-gray-900 truncate">
                  {currentView === 'table' ? 'Bookings Table' : currentView === 'calendar' ? 'Calendar View' : 'Extension Requests'}
                </h2>
              </div>
              <div className="flex items-center gap-2 xs:gap-3 flex-wrap xs:ml-auto">
                <span className="text-xs xs:text-sm font-semibold text-green-800 bg-green-100 px-2 xs:px-3 sm:px-4 py-1 xs:py-2 rounded-full border border-green-300 whitespace-nowrap">
                  {currentView === 'extensions' ? extensionRequests.length : filteredBookings.length} results
                </span>
                
                {/* Calendar View Controls - Hidden on mobile, show on tablet+ */}
                {currentView === 'calendar' && (
                  <>
                    {/* Mobile Calendar Controls - Status Legend Popup */}
                    <div className="sm:hidden">
                      <Popover open={isLegendOpen} onOpenChange={setIsLegendOpen}>
                        <PopoverTrigger asChild>
                          <Button 
                            variant="secondary"
                            size="sm"
                            className="bg-white border border-green-300 text-green-700 hover:bg-green-50 min-h-[48px] xs:min-h-[44px] px-3 text-xs xs:text-sm font-medium"
                          >
                            <Info className="h-4 w-4 mr-2" />
                            Status Legend
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent 
                          className="w-80 xs:w-72 p-3 xs:p-4 bg-white border-2 border-green-200 shadow-xl rounded-xl" 
                          align="center"
                          side="bottom"
                        >
                          <div className="space-y-3">
                            <h4 className="font-semibold text-sm xs:text-base text-green-800 flex items-center gap-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              Booking Status Legend
                            </h4>
                            <div className="grid grid-cols-2 gap-2 xs:gap-3">
                              <div className="flex items-center gap-2 bg-amber-50 p-2 rounded-lg border border-amber-200">
                                <div className="w-3 h-3 bg-amber-500 rounded-full flex-shrink-0"></div>
                                <span className="text-xs xs:text-sm font-medium text-amber-800">Pending</span>
                              </div>
                              <div className="flex items-center gap-2 bg-green-50 p-2 rounded-lg border border-green-200">
                                <div className="w-3 h-3 bg-green-500 rounded-full flex-shrink-0"></div>
                                <span className="text-xs xs:text-sm font-medium text-green-800">Active</span>
                              </div>
                              <div className="flex items-center gap-2 bg-blue-50 p-2 rounded-lg border border-blue-200">
                                <div className="w-3 h-3 bg-blue-500 rounded-full flex-shrink-0"></div>
                                <span className="text-xs xs:text-sm font-medium text-blue-800">Completed</span>
                              </div>
                              <div className="flex items-center gap-2 bg-red-50 p-2 rounded-lg border border-red-200">
                                <div className="w-3 h-3 bg-red-500 rounded-full flex-shrink-0"></div>
                                <span className="text-xs xs:text-sm font-medium text-red-800">Cancelled</span>
                              </div>
                            </div>
                            <div className="text-xs text-gray-600 pt-2 border-t border-gray-200 text-center">
                              Color-coded booking events on calendar
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </div>
                    
                    {/* Desktop Calendar Controls */}
                    <div className="hidden sm:flex sm:items-center sm:gap-2 md:gap-3 sm:flex-wrap">
                      {/* Booking Status Legend */}
                      <div className="flex items-center gap-1 sm:gap-2 bg-white border border-green-300 px-2 sm:px-3 py-1 sm:py-2 rounded-lg">
                        <span className="text-xs font-semibold text-green-700 hidden md:inline">📊 Status Legend:</span>
                        <div className="flex items-center gap-1 sm:gap-2">
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 sm:w-3 sm:h-3 bg-amber-500 rounded-full"></div>
                            <span className="text-xs text-green-700 font-medium hidden sm:inline">Pending</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 sm:w-3 sm:h-3 bg-green-500 rounded-full"></div>
                            <span className="text-xs text-green-700 font-medium hidden sm:inline">Active</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 sm:w-3 sm:h-3 bg-blue-500 rounded-full"></div>
                            <span className="text-xs text-green-700 font-medium hidden sm:inline">Completed</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-500 rounded-full"></div>
                            <span className="text-xs text-green-700 font-medium hidden sm:inline">Cancelled</span>
                          </div>
                        </div>
                      </div>
                      
                      {/* View Toggle */}
                      <div className="flex items-center bg-green-100 rounded-full p-1 border border-green-300">
                        <Button
                          variant={calendarView === "week" ? "primary" : "secondary"}
                          size="xs"
                          onClick={() => setCalendarView("week")}
                          className={cn(
                            "rounded-full text-xs px-2 sm:px-3 py-1 transition-all duration-300 border-0 min-h-[32px] sm:min-h-[36px]",
                            calendarView === "week" 
                              ? "bg-green-600 text-white shadow-md hover:bg-green-700" 
                              : "text-green-700 bg-transparent hover:bg-green-200"
                          )}
                        >
                          Week
                        </Button>
                        <Button
                          variant={calendarView === "month" ? "primary" : "secondary"}
                          size="xs"
                          onClick={() => setCalendarView("month")}
                          className={cn(
                            "rounded-full text-xs px-2 sm:px-3 py-1 transition-all duration-300 border-0 min-h-[32px] sm:min-h-[36px]",
                            calendarView === "month" 
                              ? "bg-green-600 text-white shadow-md hover:bg-green-700" 
                              : "text-green-700 bg-transparent hover:bg-green-200"
                          )}
                        >
                          Month
                        </Button>
                      </div>
                      
                      {/* Navigation */}
                      <div className="flex items-center gap-1">
                        <Button 
                          variant="secondary" 
                          size="xs"
                          onClick={() => setSelectedCalendarDate(calendarView === "week" ? subWeeks(selectedCalendarDate, 1) : subMonths(selectedCalendarDate, 1))}
                          className="bg-white border border-green-300 text-green-700 hover:bg-green-50 h-8 w-8 p-0 rounded-lg min-h-[44px] sm:min-h-[32px] min-w-[44px] sm:min-w-[32px]"
                          title="Previous"
                        >
                          <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4" />
                        </Button>
                        <Button 
                          variant="secondary" 
                          size="xs"
                          onClick={() => setSelectedCalendarDate(new Date())}
                          className="bg-white border border-green-300 text-green-700 hover:bg-green-50 text-xs px-2 sm:px-3 py-1 rounded-lg min-h-[44px] sm:min-h-[32px]"
                        >
                          Today
                        </Button>
                        <Button 
                          variant="secondary" 
                          size="xs"
                          onClick={() => setSelectedCalendarDate(calendarView === "week" ? addWeeks(selectedCalendarDate, 1) : addMonths(selectedCalendarDate, 1))}
                          className="bg-white border border-green-300 text-green-700 hover:bg-green-50 h-8 w-8 p-0 rounded-lg min-h-[44px] sm:min-h-[32px] min-w-[44px] sm:min-w-[32px]"
                          title="Next"
                        >
                          <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4" />
                        </Button>
                      </div>
                    </div>
                  </>
                )}
                
                {currentView === 'extensions' && (
                  <Button 
                    onClick={fetchExtensionRequests} 
                    disabled={extensionLoading}
                    size="sm"
                    variant="secondary"
                    className="bg-white border border-green-300 text-green-700 hover:bg-green-50 hover:text-green-800 transition-colors shadow-sm min-h-[48px] xs:min-h-[44px] sm:min-h-[36px] px-2 xs:px-3 text-xs xs:text-sm"
                  >
                    <RefreshCw className={`h-4 w-4 xs:mr-2 ${extensionLoading ? 'animate-spin' : ''}`} />
                    <span className="hidden xs:inline">Refresh</span>
                  </Button>
                )}
              </div>
            </div>
          </div>
          <div className="p-0">
            {currentView === "table" ? (
              <BookingTable
                bookings={filteredBookings}
                onRowClick={handleBookingClick}
                onAddToOnPageCalendar={handleAddToOnPageCalendar}
              />
            ) : currentView === "calendar" ? (
              <div className="p-3 xs:p-4 sm:p-6">
                <BookingCalendar
                  bookings={filteredBookings}
                  onEventClick={handleBookingClick}
                  view={calendarView}
                  onViewChange={setCalendarView}
                  currentDate={selectedCalendarDate}
                  onDateChange={setSelectedCalendarDate}
                />
              </div>
            ) : (
              <div className="p-3 xs:p-4 sm:p-6">
                <ExtensionManagement />
              </div>
            )}
          </div>
        </div>
        
        {/* Booking Details Drawer */}
        {selectedBooking && (
          <BookingDetailsDrawer
            booking={selectedBooking}
            customer={selectedCustomer}
            car={selectedCar}
            payment={selectedPayment}
            open={!!selectedBooking}
            onOpenChange={(open) => {
              if (!open) {
                setSelectedBooking(null);
                setSelectedCustomer(null);
                setSelectedCar(null);
                setSelectedPayment(null);
              }
            }}
            onPaymentVerification={handlePaymentVerification}
            currentAdminId={profile?.id}
          />
        )}
      </div>
    </div>
  );
}
