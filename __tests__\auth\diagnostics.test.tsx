/**
 * Authentication Cookie Diagnostics
 * 
 * This test file runs comprehensive diagnostics to identify specific
 * cookie inconsistencies and provides detailed reporting.
 */

import { render, screen, waitFor, act, cleanup } from '@testing-library/react'
import '@testing-library/jest-dom'
import React from 'react'
import { createBrowserClient } from '@supabase/ssr'
import { CustomerAuthProvider } from '../../src/components/auth/customer-auth-context'
import { AdminAuthProvider } from '../../src/components/auth/admin-auth-context'

// Mock Supabase client
jest.mock('@supabase/ssr', () => ({
  createBrowserClient: jest.fn()
}))

const mockCreateBrowserClient = createBrowserClient as jest.MockedFunction<typeof createBrowserClient>

interface DiagnosticReport {
  testName: string
  passed: boolean
  issues: string[]
  cookieState: {
    customerCookie: string | null
    adminCookie: string | null
  }
  timestamp: Date
}

describe('Authentication Cookie Diagnostics', () => {
  let diagnosticReports: DiagnosticReport[] = []
  let mockSupabaseCustomer: any
  let mockSupabaseAdmin: any

  beforeEach(() => {
    jest.clearAllMocks()
    
    mockSupabaseCustomer = {
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        onAuthStateChange: jest.fn().mockReturnValue({
          data: { subscription: { unsubscribe: jest.fn() } }
        }),
        signInWithPassword: jest.fn(),
        signOut: jest.fn()
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } })
          })
        })
      })
    }

    mockSupabaseAdmin = {
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        onAuthStateChange: jest.fn().mockReturnValue({
          data: { subscription: { unsubscribe: jest.fn() } }
        }),
        signInWithPassword: jest.fn(),
        signOut: jest.fn()
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } })
          })
        })
      })
    }

    mockCreateBrowserClient.mockImplementation((url, key, options) => {
      const storageKey = options?.auth?.storageKey
      return storageKey === 'sb-customer-auth-token' ? mockSupabaseCustomer : mockSupabaseAdmin
    })
  })

  afterEach(() => {
    cleanup()
  })

  afterAll(() => {
    // Print diagnostic report
    console.log('\n=== AUTHENTICATION COOKIE DIAGNOSTIC REPORT ===\n')
    
    const failedTests = diagnosticReports.filter(report => !report.passed)
    
    if (failedTests.length === 0) {
      console.log('✅ All authentication cookie tests passed!')
    } else {
      console.log(`❌ Found ${failedTests.length} issues with authentication cookies:\n`)
      
      failedTests.forEach(report => {
        console.log(`Test: ${report.testName}`)
        console.log(`Issues:`)
        report.issues.forEach(issue => console.log(`  - ${issue}`))
        console.log(`Cookie State:`)
        console.log(`  Customer: ${report.cookieState.customerCookie}`)
        console.log(`  Admin: ${report.cookieState.adminCookie}`)
        console.log(`Timestamp: ${report.timestamp.toISOString()}\n`)
      })
    }
    
    console.log('=== END DIAGNOSTIC REPORT ===\n')
  })

  function createDiagnosticReport(testName: string, passed: boolean, issues: string[] = []): DiagnosticReport {
    const report: DiagnosticReport = {
      testName,
      passed,
      issues,
      cookieState: {
        customerCookie: window.localStorage.getItem('sb-customer-auth-token'),
        adminCookie: window.localStorage.getItem('sb-admin-auth-token')
      },
      timestamp: new Date()
    }
    
    diagnosticReports.push(report)
    return report
  }

  test('DIAGNOSTIC: Cookie isolation verification', async () => {
    const issues: string[] = []
    
    // Test 1: Set customer session
    const customerSession = { access_token: 'customer-token', user: { email: '<EMAIL>' } }
    window.localStorage.setItem('sb-customer-auth-token', JSON.stringify(customerSession))
    
    // Test 2: Set admin session
    const adminSession = { access_token: 'admin-token', user: { email: '<EMAIL>' } }
    window.localStorage.setItem('sb-admin-auth-token', JSON.stringify(adminSession))
    
    // Verify isolation
    const customerCookie = window.localStorage.getItem('sb-customer-auth-token')
    const adminCookie = window.localStorage.getItem('sb-admin-auth-token')
    
    if (!customerCookie) {
      issues.push('Customer cookie not found after setting')
    }
    
    if (!adminCookie) {
      issues.push('Admin cookie not found after setting')
    }
    
    if (customerCookie === adminCookie) {
      issues.push('Customer and admin cookies are identical - NO ISOLATION!')
    }
    
    try {
      const customerData = JSON.parse(customerCookie || '{}')
      const adminData = JSON.parse(adminCookie || '{}')
      
      if (customerData.access_token === adminData.access_token) {
        issues.push('Customer and admin access tokens are identical')
      }
      
      if (customerData.user?.email === adminData.user?.email) {
        issues.push('Customer and admin user emails are identical')
      }
    } catch (error) {
      issues.push(`Cookie parsing error: ${error}`)
    }
    
    const report = createDiagnosticReport('Cookie isolation verification', issues.length === 0, issues)
    expect(report.passed).toBe(true)
  })

  test('DIAGNOSTIC: Tab close/reopen consistency', async () => {
    const issues: string[] = []
    
    // Setup initial state
    const originalSession = { 
      access_token: 'original-token', 
      user: { id: 'user-id', email: '<EMAIL>' }
    }
    
    window.localStorage.setItem('sb-customer-auth-token', JSON.stringify(originalSession))
    
    // Simulate tab close
    const beforeCloseState = window.localStorage.getItem('sb-customer-auth-token')
    
    // Verify state persists after "tab close"
    const afterReopenState = window.localStorage.getItem('sb-customer-auth-token')
    
    if (beforeCloseState !== afterReopenState) {
      issues.push('Cookie state changed between tab close and reopen')
    }
    
    if (!afterReopenState) {
      issues.push('Cookie lost after tab reopen')
    }
    
    try {
      const restoredSession = JSON.parse(afterReopenState || '{}')
      if (restoredSession.access_token !== originalSession.access_token) {
        issues.push('Access token changed after tab reopen')
      }
      
      if (restoredSession.user?.email !== originalSession.user.email) {
        issues.push('User email changed after tab reopen')
      }
    } catch (error) {
      issues.push(`Session restoration error: ${error}`)
    }
    
    const report = createDiagnosticReport('Tab close/reopen consistency', issues.length === 0, issues)
    expect(report.passed).toBe(true)
  })

  test('DIAGNOSTIC: Cross-context contamination check', async () => {
    const issues: string[] = []
    
    // Setup customer session
    const customerSession = { 
      access_token: 'customer-token', 
      user: { id: 'customer-id', email: '<EMAIL>', role: 'customer' }
    }
    window.localStorage.setItem('sb-customer-auth-token', JSON.stringify(customerSession))
    
    // Setup admin session  
    const adminSession = { 
      access_token: 'admin-token', 
      user: { id: 'admin-id', email: '<EMAIL>', role: 'admin' }
    }
    window.localStorage.setItem('sb-admin-auth-token', JSON.stringify(adminSession))
    
    // Check for cross-contamination
    const customerCookie = window.localStorage.getItem('sb-customer-auth-token')
    const adminCookie = window.localStorage.getItem('sb-admin-auth-token')
    
    if (customerCookie && adminCookie) {
      try {
        const customerData = JSON.parse(customerCookie)
        const adminData = JSON.parse(adminCookie)
        
        // Check if admin data appears in customer cookie
        if (customerData.user?.role === 'admin') {
          issues.push('Admin user data found in customer cookie')
        }
        
        // Check if customer data appears in admin cookie
        if (adminData.user?.role === 'customer') {
          issues.push('Customer user data found in admin cookie')
        }
        
        // Check for token cross-contamination
        if (customerData.access_token === adminData.access_token) {
          issues.push('Same access token found in both cookies')
        }
        
        // Check for user ID cross-contamination
        if (customerData.user?.id === adminData.user?.id) {
          issues.push('Same user ID found in both cookies')
        }
      } catch (error) {
        issues.push(`Cross-contamination check error: ${error}`)
      }
    }
    
    const report = createDiagnosticReport('Cross-context contamination check', issues.length === 0, issues)
    expect(report.passed).toBe(true)
  })

  test('DIAGNOSTIC: Storage key uniqueness verification', async () => {
    const issues: string[] = []
    
    // Track storage keys used
    const usedStorageKeys: string[] = []
    
    // Override createBrowserClient to capture storage keys
    mockCreateBrowserClient.mockImplementation((url, key, options) => {
      const storageKey = options?.auth?.storageKey
      if (storageKey) {
        usedStorageKeys.push(storageKey)
      }
      return storageKey === 'sb-customer-auth-token' ? mockSupabaseCustomer : mockSupabaseAdmin
    })
    
    // Render both providers to trigger client creation
    render(
      <div>
        <CustomerAuthProvider>
          <div>Customer</div>
        </CustomerAuthProvider>
        <AdminAuthProvider>
          <div>Admin</div>
        </AdminAuthProvider>
      </div>
    )
    
    // Check for unique storage keys
    const uniqueKeys = new Set(usedStorageKeys)
    
    if (uniqueKeys.size < 2) {
      issues.push(`Expected 2 unique storage keys, found ${uniqueKeys.size}`)
    }
    
    if (!usedStorageKeys.includes('sb-customer-auth-token')) {
      issues.push('Customer storage key not found')
    }
    
    if (!usedStorageKeys.includes('sb-admin-auth-token')) {
      issues.push('Admin storage key not found')
    }
    
    // Check for potential key conflicts
    const customerKeys = usedStorageKeys.filter(key => key === 'sb-customer-auth-token')
    const adminKeys = usedStorageKeys.filter(key => key === 'sb-admin-auth-token')
    
    if (customerKeys.length > 1) {
      issues.push(`Multiple customer storage keys detected: ${customerKeys.length}`)
    }
    
    if (adminKeys.length > 1) {
      issues.push(`Multiple admin storage keys detected: ${adminKeys.length}`)
    }
    
    const report = createDiagnosticReport('Storage key uniqueness verification', issues.length === 0, issues)
    expect(report.passed).toBe(true)
  })

  test('DIAGNOSTIC: Session data integrity check', async () => {
    const issues: string[] = []
    
    // Create well-formed session data
    const validSession = {
      access_token: 'valid-access-token-12345',
      refresh_token: 'valid-refresh-token-67890',
      expires_at: Date.now() + 3600000, // 1 hour from now
      user: {
        id: 'user-uuid-12345',
        email: '<EMAIL>',
        role: 'customer'
      }
    }
    
    // Set session
    window.localStorage.setItem('sb-customer-auth-token', JSON.stringify(validSession))
    
    // Retrieve and validate
    const storedSession = window.localStorage.getItem('sb-customer-auth-token')
    
    if (!storedSession) {
      issues.push('Session not found in storage')
    } else {
      try {
        const parsedSession = JSON.parse(storedSession)
        
        // Validate required fields
        if (!parsedSession.access_token) {
          issues.push('Missing access_token in session')
        }
        
        if (!parsedSession.user) {
          issues.push('Missing user object in session')
        }
        
        if (!parsedSession.user?.id) {
          issues.push('Missing user ID in session')
        }
        
        if (!parsedSession.user?.email) {
          issues.push('Missing user email in session')
        }
        
        // Validate data integrity
        if (parsedSession.access_token !== validSession.access_token) {
          issues.push('Access token corrupted in storage')
        }
        
        if (parsedSession.user?.email !== validSession.user.email) {
          issues.push('User email corrupted in storage')
        }
        
        // Check for unexpected modifications
        const originalKeys = Object.keys(validSession)
        const storedKeys = Object.keys(parsedSession)
        
        if (originalKeys.length !== storedKeys.length) {
          issues.push('Session structure modified in storage')
        }
        
      } catch (error) {
        issues.push(`Session parsing error: ${error}`)
      }
    }
    
    const report = createDiagnosticReport('Session data integrity check', issues.length === 0, issues)
    expect(report.passed).toBe(true)
  })

  test('DIAGNOSTIC: Concurrent session handling', async () => {
    const issues: string[] = []
    
    // Setup concurrent sessions
    const customerSession = { 
      access_token: 'customer-token', 
      user: { email: '<EMAIL>', role: 'customer' }
    }
    
    const adminSession = { 
      access_token: 'admin-token', 
      user: { email: '<EMAIL>', role: 'admin' }
    }
    
    // Set both sessions simultaneously
    window.localStorage.setItem('sb-customer-auth-token', JSON.stringify(customerSession))
    window.localStorage.setItem('sb-admin-auth-token', JSON.stringify(adminSession))
    
    // Verify both exist
    const customerCookie = window.localStorage.getItem('sb-customer-auth-token')
    const adminCookie = window.localStorage.getItem('sb-admin-auth-token')
    
    if (!customerCookie) {
      issues.push('Customer session lost when admin session was set')
    }
    
    if (!adminCookie) {
      issues.push('Admin session lost when customer session was set')
    }
    
    // Verify independence
    if (customerCookie && adminCookie) {
      try {
        const customerData = JSON.parse(customerCookie)
        const adminData = JSON.parse(adminCookie)
        
        if (customerData.access_token === adminData.access_token) {
          issues.push('Sessions sharing the same access token')
        }
        
        if (customerData.user?.email === adminData.user?.email) {
          issues.push('Sessions sharing the same user email')
        }
        
        // Test individual session removal
        window.localStorage.removeItem('sb-customer-auth-token')
        
        const remainingAdmin = window.localStorage.getItem('sb-admin-auth-token')
        if (!remainingAdmin) {
          issues.push('Admin session removed when customer session was removed')
        }
        
        // Restore and test other direction
        window.localStorage.setItem('sb-customer-auth-token', JSON.stringify(customerSession))
        window.localStorage.removeItem('sb-admin-auth-token')
        
        const remainingCustomer = window.localStorage.getItem('sb-customer-auth-token')
        if (!remainingCustomer) {
          issues.push('Customer session removed when admin session was removed')
        }
        
      } catch (error) {
        issues.push(`Concurrent session test error: ${error}`)
      }
    }
    
    const report = createDiagnosticReport('Concurrent session handling', issues.length === 0, issues)
    expect(report.passed).toBe(true)
  })
})
