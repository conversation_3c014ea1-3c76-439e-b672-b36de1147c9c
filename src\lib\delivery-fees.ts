/**
 * Delivery and return fee configuration for OllieTrack
 * Defines location-based fees with day/night pricing
 */

export const GARAGE_LOCATION = "#9 Lubnac, Vintar, Ilocos Norte";

// Time windows for day and night pricing
export const TIME_WINDOWS = {
  DAY_START: "07:00", // 7:00 AM
  DAY_END: "17:59",   // 5:59 PM
  NIGHT_START: "18:00", // 6:00 PM
  NIGHT_END: "06:59",  // 6:59 AM
};

// Fee structure by location and time of day
export interface LocationFee {
  day: number;   // Fee during day time (07:00-17:59)
  night: number; // Fee during night time (18:00-06:59)
}

// Map of location names to their respective fees
export const DELIVERY_FEES: Record<string, LocationFee> = {
  "Laoag Bus Terminal": { day: 250, night: 350 },
  "Laoag Centro": { day: 250, night: 350 },
  "SM/Robinsons": { day: 300, night: 400 },
  "Sarrat/Bacarra Centro": { day: 250, night: 350 },
  "Laoag Airport": { day: 500, night: 600 },
  "Batac/Paoay": { day: 800, night: 1000 },
  "Pasuquin": { day: 500, night: 700 },
  "Dingras": { day: 500, night: 700 },
  "Buttong/Nalbo": { day: 300, night: 400 },
  "Airport Road": { day: 350, night: 450 },
  "Vigan/Pagudpud": { day: 1500, night: 1500 },
  "Sinait/Cabugao/Badoc/Bangui": { day: 1200, night: 1200 },
};

// Return fees are the same as delivery fees for now
export const RETURN_FEES: Record<string, LocationFee> = { ...DELIVERY_FEES };

/**
 * Determines if a given time is during night hours (18:00-06:59)
 * @param time - Time string in 24-hour format (HH:MM)
 * @returns boolean - True if time is during night hours
 */
export function isNightTime(time: string): boolean {
  if (!time) return false;
  
  const hour = parseInt(time.split(":")[0], 10);
  return hour >= 18 || hour < 7;
}

/**
 * Calculates the delivery fee based on location and time
 * @param location - Delivery location
 * @param time - Delivery time in 24-hour format (HH:MM)
 * @returns number - Fee amount in PHP
 */
export function calculateDeliveryFee(location: string, time: string): number {
  // If location is the garage/office or empty, no fee
  if (!location || location === GARAGE_LOCATION) {
    return 0;
  }

  // Find the location in our fee structure
  // Note: Need to handle slight variations in location names
  const locationKey = Object.keys(DELIVERY_FEES).find(
    key => location.includes(key) || key.includes(location)
  );

  if (!locationKey) {
    console.warn(`No delivery fee found for location: ${location}`);
    return 0;
  }

  // Determine if it's night time
  const isNight = isNightTime(time);
  
  // Return the appropriate fee
  return isNight ? DELIVERY_FEES[locationKey].night : DELIVERY_FEES[locationKey].day;
}

/**
 * Calculates the return fee based on location and time
 * @param location - Return location
 * @param time - Return time in 24-hour format (HH:MM)
 * @returns number - Fee amount in PHP
 */
export function calculateReturnFee(location: string, time: string): number {
  // If location is the garage/office or empty, no fee
  if (!location || location === GARAGE_LOCATION) {
    return 0;
  }

  // Find the location in our fee structure
  // Note: Need to handle slight variations in location names
  const locationKey = Object.keys(RETURN_FEES).find(
    key => location.includes(key) || key.includes(location)
  );

  if (!locationKey) {
    console.warn(`No return fee found for location: ${location}`);
    return 0;
  }

  // Determine if it's night time
  const isNight = isNightTime(time);
  
  // Return the appropriate fee
  return isNight ? RETURN_FEES[locationKey].night : RETURN_FEES[locationKey].day;
}
