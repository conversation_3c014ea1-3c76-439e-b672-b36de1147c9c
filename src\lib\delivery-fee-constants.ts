/**
 * Delivery and Return Fee Configuration
 * 
 * Day Time: 7:00 AM – 5:59 PM
 * Night Time: 6:00 PM – 6:59 AM
 * 
 * Garage/Office Location: #9 Lubnac, Vintar, Ilocos Norte (FREE)
 */

// Time ranges for Day/Night classification
export const TIME_CATEGORIES = {
  DAY: {
    label: "Day Time",
    range: "7:00 AM – 5:59 PM",
    startHour: 7,
    endHour: 17, // 5:59 PM in 24-hour format
  },
  NIGHT: {
    label: "Night Time", 
    range: "6:00 PM – 6:59 AM",
    startHour: 18, // 6:00 PM
    endHour: 6, // 6:59 AM
  },
} as const;

// Free pickup/return location
export const GARAGE_OFFICE_LOCATION = {
  name: "Company Garage/Office",
  address: "#9 Lubnac, Vintar, Ilocos Norte",
  fee: 0,
} as const;

// Drop-Off Fee Table
export const DROP_OFF_FEES = {
  "Laoag Bus Terminal": {
    day: 250,
    night: 350,
  },
  "Laoag Centro": {
    day: 250,
    night: 350,
  },
  "SM / Robinsons": {
    day: 300,
    night: 400,
  },
  "Sarrat Centro": {
    day: 250,
    night: 350,
  },
  "Bacarra Centro": {
    day: 250,
    night: 350,
  },
  "Laoag Airport": {
    day: 500,
    night: 600,
  },
  "Batac / Paoay": {
    day: 800,
    night: 1000,
  },
  "Pasuquin": {
    day: 500,
    night: 700,
  },
  "Dingras": {
    day: 500,
    night: 700,
  },
  "Buttong / Nalbo": {
    day: 300,
    night: 400,
  },
  "Airport Road": {
    day: 350,
    night: 450,
  },
  "Vigan / Pagudpud": {
    day: 1500,
    night: 1500, // Same fee for day/night
  },
  "Sinait / Cabugao / Badoc / Bangui": {
    day: 1200,
    night: 1200, // Same fee for day/night
  },
} as const;

// Return Fee Table
export const RETURN_FEES = {
  "Laoag Bus Terminal": {
    day: 250,
    night: 350,
  },
  "Laoag Centro": {
    day: 250,
    night: 350,
  },
  "SM / Robinsons": {
    day: 300,
    night: 400,
  },
  "Sarrat Centro": {
    day: 250,
    night: 350,
  },
  "Bacarra Centro": {
    day: 250,
    night: 350,
  },
  "Laoag Airport": {
    day: 500,
    night: 600,
  },
  "Batac / Paoay": {
    day: 800,
    night: 1000,
  },
  "Pasuquin": {
    day: 500,
    night: 700,
  },
  "Dingras": {
    day: 500,
    night: 700,
  },
  "Buttong / Nalbo": {
    day: 300,
    night: 400,
  },
  "Airport Road": {
    day: 350,
    night: 450,
  },
  "Vigan / Pagudpud": {
    day: 1500,
    night: 1500, // Same fee for day/night
  },
  "Sinait / Cabugao / Badoc / Bangui": {
    day: 1200,
    night: 1200, // Same fee for day/night
  },
} as const;

// Type definitions
export type LocationName = keyof typeof DROP_OFF_FEES;
export type TimePeriod = "day" | "night";
export type FeeType = "pickup" | "return";

// Helper function to check if a location is the garage/office (free)
export function isGarageOfficeLocation(location: string): boolean {
  return location === GARAGE_OFFICE_LOCATION.address ||
         location === GARAGE_OFFICE_LOCATION.name ||
         location.includes("Lubnac, Vintar, Ilocos Norte") ||
         location.includes("#9") ||
         location.toLowerCase().includes("garage") ||
         location.toLowerCase().includes("office");
}

// Helper function to determine day/night based on time
export function getTimePeriod(timeString: string): TimePeriod {
  // Extract hour from time string (format: "HH:MM")
  const hour = parseInt(timeString.split(":")[0]);
  
  // Day time: 7:00 AM (7) to 5:59 PM (17)
  if (hour >= TIME_CATEGORIES.DAY.startHour && hour <= TIME_CATEGORIES.DAY.endHour) {
    return "day";
  }
  
  // Night time: 6:00 PM (18) to 6:59 AM (6)
  return "night";
}

// Helper function to get available locations as array
export function getAvailableLocations(): LocationName[] {
  return Object.keys(DROP_OFF_FEES) as LocationName[];
}

// Helper function to check if location has same day/night fees
export function hasSameDayNightFees(location: LocationName): boolean {
  const dropOffFee = DROP_OFF_FEES[location];
  return dropOffFee.day === dropOffFee.night;
}
