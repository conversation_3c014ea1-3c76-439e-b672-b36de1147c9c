"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MockDocumentUpload, type MockDocumentFile } from "@/components/ui/mock-document-upload";
import { MockRequirementsUploadStep } from "./flow/mock-requirements-upload-step";
import { MockPaymentProofStep } from "./flow/mock-payment-proof-step";
import { ArrowLeft, ArrowRight, FileText, CreditCard, Smartphone } from "lucide-react";

export function MockBookingDemo() {
  const [currentDemo, setCurrentDemo] = React.useState<"requirements" | "payment">("requirements");
  
  // Mock data for requirements
  const [requirementsData, setRequirementsData] = React.useState<{
    driversLicense?: MockDocumentFile[];
    governmentId?: MockDocumentFile[];
    proofOfAge?: MockDocumentFile[];
    securityDeposit?: MockDocumentFile[];
  }>({
    driversLicense: [],
    governmentId: [],
    proofOfAge: [],
    securityDeposit: [],
  });

  // Mock data for payment
  const [paymentData, setPaymentData] = React.useState<{
    proofOfPayment?: MockDocumentFile[];
    paymentMethod?: "GCash" | "PayMaya" | "Cash" | "BankTransfer";
    totalAmount?: number;
  }>({
    proofOfPayment: [],
    paymentMethod: undefined,
    totalAmount: 25000,
  });

  const demos = [
    {
      id: "requirements" as const,
      title: "Step 2: Requirements Upload",
      description: "Upload required documents for verification",
      icon: FileText,
      color: "bg-blue-500",
    },
    {
      id: "payment" as const,
      title: "Step 4: Payment Proof Upload",
      description: "Select payment method and upload proof",
      icon: CreditCard,
      color: "bg-green-500",
    },
  ];

  const currentDemoInfo = demos.find(d => d.id === currentDemo);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900">Mock Booking Flow Demo</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Interactive demonstration of the upload picture functionality for Step 2 (Requirements) 
            and Step 4 (Payment Proof) without database integration.
          </p>
        </div>

        {/* Demo Selector */}
        <Card className="mx-auto max-w-2xl">
          <CardHeader>
            <CardTitle className="text-center">Select Demo to View</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {demos.map((demo) => {
                const Icon = demo.icon;
                const isSelected = currentDemo === demo.id;

                return (
                  <Card
                    key={demo.id}
                    className={`cursor-pointer transition-all duration-200 ${
                      isSelected
                        ? "border-blue-500 bg-blue-50 shadow-md"
                        : "border-gray-200 hover:border-gray-300 hover:shadow-sm"
                    }`}
                    onClick={() => setCurrentDemo(demo.id)}
                  >
                    <CardContent className="p-6">
                      <div className="text-center space-y-3">
                        <div className={`mx-auto w-12 h-12 rounded-full ${demo.color} flex items-center justify-center`}>
                          <Icon className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{demo.title}</h3>
                          <p className="text-sm text-gray-600 mt-1">{demo.description}</p>
                        </div>
                        {isSelected && (
                          <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                            Currently Viewing
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Current Demo Display */}
        <Card>
          <CardHeader className="border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {currentDemoInfo && (
                  <div className={`p-2 rounded-full ${currentDemoInfo.color}`}>
                    <currentDemoInfo.icon className="h-5 w-5 text-white" />
                  </div>
                )}
                <div>
                  <CardTitle>{currentDemoInfo?.title}</CardTitle>
                  <p className="text-sm text-gray-600 mt-1">{currentDemoInfo?.description}</p>
                </div>
              </div>
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                Mock Demo
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {currentDemo === "requirements" && (
              <MockRequirementsUploadStep
                bookingData={requirementsData}
                onUpdate={(updates) => {
                  setRequirementsData(prev => ({ ...prev, ...updates }));
                }}
              />
            )}

            {currentDemo === "payment" && (
              <MockPaymentProofStep
                bookingData={paymentData}
                onUpdate={(updates) => {
                  setPaymentData(prev => ({ ...prev, ...updates }));
                }}
              />
            )}
          </CardContent>
        </Card>

        {/* Features List */}
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-900">Demo Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-blue-900 mb-3">Requirements Upload (Step 2)</h4>
                <ul className="text-sm text-blue-800 space-y-2">
                  <li>• Interactive document upload with drag & drop</li>
                  <li>• Real-time progress simulation</li>
                  <li>• File type and size validation</li>
                  <li>• Visual completion indicators</li>
                  <li>• Requirements checklist with details</li>
                  <li>• Image preview for uploaded files</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-blue-900 mb-3">Payment Proof Upload (Step 4)</h4>
                <ul className="text-sm text-blue-800 space-y-2">
                  <li>• Payment method selection (GCash, PayMaya, etc.)</li>
                  <li>• Dynamic payment instructions</li>
                  <li>• Account details with copy functionality</li>
                  <li>• Proof of payment upload</li>
                  <li>• Step-by-step payment guides</li>
                  <li>• Upload tips and validation</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Technical Notes */}
        <Card className="bg-gray-50 border-gray-200">
          <CardHeader>
            <CardTitle className="text-gray-900">Technical Implementation Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm text-gray-700">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h5 className="font-semibold text-gray-900 mb-2">Mock File Handling</h5>
                  <ul className="space-y-1">
                    <li>• Uses URL.createObjectURL() for preview</li>
                    <li>• Simulated upload progress</li>
                    <li>• Local state management</li>
                    <li>• No server integration</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-semibold text-gray-900 mb-2">UI Components</h5>
                  <ul className="space-y-1">
                    <li>• Responsive design</li>
                    <li>• Accessibility compliant</li>
                    <li>• Consistent styling</li>
                    <li>• Interactive feedback</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-semibold text-gray-900 mb-2">Future Integration</h5>
                  <ul className="space-y-1">
                    <li>• Easy database integration</li>
                    <li>• Modular component design</li>
                    <li>• Type-safe interfaces</li>
                    <li>• Error handling ready</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Demo Navigation */}
        <div className="flex justify-center">
          <div className="flex items-center gap-4">
            <Button
              variant="secondary"
              onClick={() => setCurrentDemo("requirements")}
              disabled={currentDemo === "requirements"}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Requirements Demo
            </Button>
            <Button
              onClick={() => setCurrentDemo("payment")}
              disabled={currentDemo === "payment"}
              className="flex items-center gap-2"
            >
              Payment Demo
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
