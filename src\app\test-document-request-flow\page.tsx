"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  TestTube,
  Send,
  FileText,
  User,
  Mail
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { 
  createDocumentRequests,
  getCustomerPendingDocumentRequests,
  getDocumentRequests,
  autoFulfillDocumentRequests
} from "@/lib/services/document-request-service";

interface TestStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'success' | 'error';
  result?: string;
  error?: string;
}

export default function DocumentRequestFlowTestPage() {
  const { toast } = useToast();
  const [testSteps, setTestSteps] = useState<TestStep[]>([
    {
      id: 'admin-create-request',
      title: 'Admin Creates Document Request',
      description: 'Admin sends document requests to customer',
      status: 'pending'
    },
    {
      id: 'customer-receives-email',
      title: 'Customer Receives Email',
      description: 'Email notification sent to customer',
      status: 'pending'
    },
    {
      id: 'customer-sees-requests',
      title: 'Customer Views Pending Requests',
      description: 'Customer sees document requests in their account',
      status: 'pending'
    },
    {
      id: 'document-upload-simulation',
      title: 'Document Upload Simulation',
      description: 'Simulate customer uploading documents',
      status: 'pending'
    },
    {
      id: 'auto-fulfill-requests',
      title: 'Auto-fulfill Document Requests',
      description: 'Document requests automatically resolved',
      status: 'pending'
    },
    {
      id: 'admin-sees-completion',
      title: 'Admin Sees Completion',
      description: 'Admin views fulfilled requests',
      status: 'pending'
    }
  ]);

  // Test form data
  const [testData, setTestData] = useState({
    customerId: '',
    customerEmail: '',
    customerName: '',
    bookingId: '',
    adminNotes: 'Please upload clear, high-quality images of your documents for verification.',
    documentTypes: ['drivers_license', 'government_id'] as string[]
  });

  const updateStepStatus = (stepId: string, status: TestStep['status'], result?: string, error?: string) => {
    setTestSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { ...step, status, result, error }
        : step
    ));
  };

  const runTest = async (stepId: string) => {
    updateStepStatus(stepId, 'running');
    
    try {
      switch (stepId) {
        case 'admin-create-request':
          await testAdminCreateRequest();
          break;
        case 'customer-sees-requests':
          await testCustomerSeesRequests();
          break;
        case 'document-upload-simulation':
          await testDocumentUpload();
          break;
        case 'auto-fulfill-requests':
          await testAutoFulfillRequests();
          break;
        case 'admin-sees-completion':
          await testAdminSeesCompletion();
          break;
        default:
          throw new Error(`Unknown test step: ${stepId}`);
      }
    } catch (error) {
      updateStepStatus(stepId, 'error', undefined, error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const testAdminCreateRequest = async () => {
    if (!testData.customerId || !testData.customerEmail || !testData.bookingId) {
      throw new Error('Please fill in all required test data fields');
    }

    const result = await createDocumentRequests({
      bookingId: testData.bookingId,
      customerId: testData.customerId,
      customerEmail: testData.customerEmail,
      customerName: testData.customerName,
      documentTypes: testData.documentTypes,
      requestType: 'missing',
      adminNotes: testData.adminNotes
    });

    if (!result.success) {
      throw new Error(result.error || 'Failed to create document requests');
    }

    updateStepStatus('admin-create-request', 'success', `Created ${result.data?.length || 0} document requests`);
    updateStepStatus('customer-receives-email', 'success', 'Email sent automatically during request creation');
  };

  const testCustomerSeesRequests = async () => {
    if (!testData.customerId) {
      throw new Error('Customer ID is required');
    }

    const result = await getCustomerPendingDocumentRequests(testData.customerId);
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to fetch pending requests');
    }

    const pendingCount = result.data?.length || 0;
    updateStepStatus('customer-sees-requests', 'success', `Found ${pendingCount} pending document requests`);
  };

  const testDocumentUpload = async () => {
    // This step is informational since we can't actually upload files in a test
    updateStepStatus('document-upload-simulation', 'success', 'Document upload would trigger auto-fulfill logic in real usage');
  };

  const testAutoFulfillRequests = async () => {
    if (!testData.customerId) {
      throw new Error('Customer ID is required');
    }

    let totalFulfilled = 0;
    for (const docType of testData.documentTypes) {
      const result = await autoFulfillDocumentRequests(testData.customerId, docType);
      if (result.success && result.data) {
        totalFulfilled += result.data.fulfilledCount;
      }
    }

    updateStepStatus('auto-fulfill-requests', 'success', `Auto-fulfilled ${totalFulfilled} document requests`);
  };

  const testAdminSeesCompletion = async () => {
    if (!testData.bookingId) {
      throw new Error('Booking ID is required');
    }

    const result = await getDocumentRequests(testData.bookingId);
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to fetch document requests for admin');
    }

    const requests = result.data || [];
    const fulfilledCount = requests.filter(req => req.status === 'fulfilled').length;
    const pendingCount = requests.filter(req => req.status === 'pending').length;
    
    updateStepStatus('admin-sees-completion', 'success', 
      `Admin can see ${requests.length} total requests (${fulfilledCount} fulfilled, ${pendingCount} pending)`);
  };

  const runAllTests = async () => {
    const steps = ['admin-create-request', 'customer-sees-requests', 'document-upload-simulation', 'auto-fulfill-requests', 'admin-sees-completion'];
    
    for (const stepId of steps) {
      await runTest(stepId);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  const resetTests = () => {
    setTestSteps(prev => prev.map(step => ({ ...step, status: 'pending' as const, result: undefined, error: undefined })));
  };

  const getStatusIcon = (status: TestStep['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'running':
        return <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: TestStep['status']) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Success</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Error</Badge>;
      case 'running':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Running</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Pending</Badge>;
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-2">
          <TestTube className="w-8 h-8 text-blue-600" />
          Document Request Flow Test
        </h1>
        <p className="text-gray-600">
          Comprehensive testing of the admin-to-customer document request workflow
        </p>
      </div>

      {/* Test Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Test Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="customerId">Customer ID *</Label>
              <Input
                id="customerId"
                value={testData.customerId}
                onChange={(e) => setTestData(prev => ({ ...prev, customerId: e.target.value }))}
                placeholder="Enter valid customer UUID"
              />
            </div>
            <div>
              <Label htmlFor="bookingId">Booking ID *</Label>
              <Input
                id="bookingId"
                value={testData.bookingId}
                onChange={(e) => setTestData(prev => ({ ...prev, bookingId: e.target.value }))}
                placeholder="Enter valid booking UUID"
              />
            </div>
            <div>
              <Label htmlFor="customerEmail">Customer Email *</Label>
              <Input
                id="customerEmail"
                type="email"
                value={testData.customerEmail}
                onChange={(e) => setTestData(prev => ({ ...prev, customerEmail: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <Label htmlFor="customerName">Customer Name</Label>
              <Input
                id="customerName"
                value={testData.customerName}
                onChange={(e) => setTestData(prev => ({ ...prev, customerName: e.target.value }))}
                placeholder="John Doe"
              />
            </div>
          </div>
          <div>
            <Label htmlFor="adminNotes">Admin Notes</Label>
            <Textarea
              id="adminNotes"
              value={testData.adminNotes}
              onChange={(e) => setTestData(prev => ({ ...prev, adminNotes: e.target.value }))}
              placeholder="Notes for the customer..."
              rows={3}
            />
          </div>
          
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Note:</strong> Make sure the Customer ID and Booking ID exist in your database.
              You can find these values in the admin panel or database.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Test Control Buttons */}
      <div className="flex gap-4 justify-center">
        <Button onClick={runAllTests} className="bg-blue-600 hover:bg-blue-700">
          <TestTube className="w-4 h-4 mr-2" />
          Run All Tests
        </Button>
        <Button onClick={resetTests} variant="secondary">
          Reset Tests
        </Button>
      </div>

      {/* Test Steps */}
      <div className="grid gap-4">
        {testSteps.map((step, index) => (
          <Card key={step.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-sm font-medium">
                    {index + 1}
                  </div>
                  {getStatusIcon(step.status)}
                  <div>
                    <CardTitle className="text-lg">{step.title}</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(step.status)}
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => runTest(step.id)}
                    disabled={step.status === 'running'}
                  >
                    Run Test
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            {(step.result || step.error) && (
              <CardContent className="pt-0">
                {step.result && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-green-800 text-sm">
                      <strong>Result:</strong> {step.result}
                    </p>
                  </div>
                )}
                {step.error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-800 text-sm">
                      <strong>Error:</strong> {step.error}
                    </p>
                  </div>
                )}
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* Test Results Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Test Results Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-700">
                {testSteps.filter(step => step.status === 'success').length}
              </div>
              <div className="text-sm text-green-600">Passed</div>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-700">
                {testSteps.filter(step => step.status === 'error').length}
              </div>
              <div className="text-sm text-red-600">Failed</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-700">
                {testSteps.filter(step => step.status === 'running').length}
              </div>
              <div className="text-sm text-blue-600">Running</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-700">
                {testSteps.filter(step => step.status === 'pending').length}
              </div>
              <div className="text-sm text-gray-600">Pending</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Testing Instructions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Prerequisites:</h4>
              <ul className="list-disc pl-5 space-y-1 text-sm text-gray-600">
                <li>Valid customer ID and booking ID from your database</li>
                <li>Supabase connection configured</li>
                <li>Email service (Supabase Edge Functions) configured</li>
                <li>Document request schema deployed to database</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">What this test covers:</h4>
              <ul className="list-disc pl-5 space-y-1 text-sm text-gray-600">
                <li>Creating document requests from admin side</li>
                <li>Sending email notifications to customers</li>
                <li>Fetching pending requests for customers</li>
                <li>Auto-fulfilling requests when documents are uploaded</li>
                <li>Admin viewing completed requests</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">Manual steps to complete the flow:</h4>
              <ol className="list-decimal pl-5 space-y-1 text-sm text-gray-600">
                <li>Run the automated tests above</li>
                <li>Log in as the customer and check their email</li>
                <li>Visit the customer settings page to see document requests</li>
                <li>Upload documents through the legal documents section</li>
                <li>Verify toast notifications appear</li>
                <li>Check admin panel to see fulfilled requests</li>
              </ol>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
