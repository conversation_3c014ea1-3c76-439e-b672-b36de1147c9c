{"designSystem": {"colors": {"primary": {"base": "#2196F3", "hover": "#42A5F5", "active": "#1E88E5"}, "secondary": {"base": "#FFFFFF", "hover": "#F5F5F5", "active": "#E0E0E0"}, "background": {"main": "#FFFFFF", "modalOverlay": "rgba(0,0,0,0.5)", "card": "#FFFFFF", "inputField": "#FAFAFA"}, "borders": {"default": "#E0E0E0", "focus": "#2196F3", "dashed": "#BDBDBD"}, "text": {"primary": "#212121", "secondary": "#616161", "muted": "#9E9E9E", "inverse": "#FFFFFF"}, "status": {"success": "#4CAF50", "error": "#E53935", "warning": "#FB8C00"}}, "typography": {"fontFamily": "'Inter','Helvetica Neue',<PERSON><PERSON>,sans-serif", "heading": {"size": "20px", "weight": 600, "lineHeight": "28px"}, "subheading": {"size": "14px", "weight": 400, "lineHeight": "20px", "color": "#616161"}, "body": {"size": "14px", "weight": 400, "lineHeight": "20px"}, "button": {"size": "14px", "weight": 600, "lineHeight": "18px"}}, "spacing": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px"}, "layout": {"modal": {"width": "420px", "padding": "24px", "borderRadius": "12px", "shadow": "0 6px 20px rgba(0,0,0,0.15)"}, "grid": {"columns": 12, "gutter": "16px"}}, "components": {"button": {"primary": {"background": "#2196F3", "textColor": "#FFFFFF", "padding": "12px 20px", "borderRadius": "8px", "hoverBackground": "#42A5F5", "activeBackground": "#1E88E5"}, "secondary": {"background": "#FFFFFF", "textColor": "#212121", "border": "1px solid #E0E0E0", "padding": "12px 20px", "borderRadius": "8px", "hoverBackground": "#F5F5F5"}}, "uploadSlot": {"default": {"border": "1px solid #E0E0E0", "borderRadius": "10px", "padding": "16px", "iconColor": "#2196F3", "labelStyle": {"size": "14px", "weight": 500, "color": "#212121"}, "hintStyle": {"size": "12px", "color": "#616161"}}, "dashed": {"border": "2px dashed #BDBDBD", "background": "#FAFAFA", "hoverBackground": "#F5F5F5"}}, "thumbnailPreview": {"borderRadius": "10px", "overlayActions": {"replace": {"iconColor": "#2196F3", "background": "#FFFFFF", "borderRadius": "50%"}, "delete": {"iconColor": "#E53935", "background": "#FFFFFF", "borderRadius": "50%"}}}, "card": {"background": "#FFFFFF", "borderRadius": "12px", "shadow": "0 2px 10px rgba(0,0,0,0.08)", "padding": "16px"}}}}