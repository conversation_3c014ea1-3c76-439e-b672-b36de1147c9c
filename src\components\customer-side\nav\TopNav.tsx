"use client"

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ChevronDown, MoreHorizontal, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useCustomerAuth } from '@/components/auth/customer-auth-context'
import { getFilteredNavItems, NavItem } from './navItems'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface TopNavProps {
  className?: string
  maxVisibleItems?: number // For Priority+ pattern
}

// Safe hook that doesn't throw if outside provider
function useSafeCustomerAuth() {
  try {
    return useCustomerAuth();
  } catch (error) {
    // If we're not within a CustomerAuthProvider, return null user
    console.debug('TopNav: outside provider');
    return { user: null };
  }
}

export function TopNav({ className, maxVisibleItems = 6 }: TopNavProps) {
  const { user } = useSafeCustomerAuth()
  const pathname = usePathname()
  const containerRef = React.useRef<HTMLDivElement>(null)
  const [visibleItems, setVisibleItems] = React.useState<NavItem[]>([])
  const [overflowItems, setOverflowItems] = React.useState<NavItem[]>([])
  
  const isAuthenticated = Boolean(user)
  const allNavItems = React.useMemo(() => getFilteredNavItems(isAuthenticated), [isAuthenticated])
  
  const isActive = React.useCallback((href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }, [pathname])

  // Priority+ logic: Calculate which items fit and which overflow
  React.useEffect(() => {
    if (!containerRef.current) return

    const calculateVisibleItems = () => {
      // Simple implementation: use maxVisibleItems for now
      // In a production app, you'd measure actual element widths
      const priorityItems = allNavItems
        .sort((a, b) => a.priority - b.priority)
        .slice(0, maxVisibleItems)
      
      const remaining = allNavItems.filter(item => 
        !priorityItems.some(pItem => pItem.key === item.key)
      )

      setVisibleItems(priorityItems)
      setOverflowItems(remaining)
    }

    calculateVisibleItems()

    // Re-calculate on resize
    const handleResize = () => calculateVisibleItems()
    window.addEventListener('resize', handleResize)
    
    return () => window.removeEventListener('resize', handleResize)
  }, [allNavItems, maxVisibleItems]) // Added missing dependencies

  return (
    <nav 
      ref={containerRef}
      className={cn(
        "flex items-center gap-1 lg:gap-2 overflow-visible",
        className
      )}
      role="navigation"
      aria-label="Main navigation"
    >
      {/* Visible Navigation Items */}
      {visibleItems.map((item) => {
        const active = isActive(item.href)
        const IconComponent = item.icon
        
        if (item.isAction) {
          // Render CTA as a button
          return (
            <Link key={item.key} href={item.href}>
              <Button 
                size="sm" 
                className="ml-2 lg:ml-4"
                aria-current={active ? "page" : undefined}
              >
                <IconComponent className="h-4 w-4 mr-1.5" />
                {item.label}
              </Button>
            </Link>
          )
        }
        
        return (
          <Link
            key={item.key}
            href={item.href}
            className={cn(
              "flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium",
              "min-h-[44px] touch-manipulation nav-transition nav-focus-visible", // Accessibility
              active
                ? "text-blue-700 bg-blue-50 shadow-sm"
                : "text-gray-600 hover:text-blue-600 hover:bg-gray-50 active:bg-gray-100"
            )}
            aria-current={active ? "page" : undefined}
          >
            <IconComponent className="h-4 w-4 flex-shrink-0" />
            <span className="hidden lg:inline">{item.label}</span>
            <span className="lg:hidden">{item.label}</span>
          </Link>
        )
      })}
      
      {/* Overflow "More" Dropdown */}
      {overflowItems.length > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="tertiary"
              size="sm"
              className={cn(
                "flex items-center gap-1 px-3 py-2",
                "min-h-[44px] touch-manipulation" // Accessibility
              )}
              aria-label="More navigation options"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="hidden sm:inline">More</span>
              <ChevronDown className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          
          <DropdownMenuContent 
            align="end" 
            className="w-48 z-50 !bg-white !border !border-gray-200 !shadow-lg !rounded-md !p-1"
            sideOffset={8}
            avoidCollisions={true}
            collisionPadding={8}
          >
            {overflowItems.map((item) => {
              const active = isActive(item.href)
              const IconComponent = item.icon
              
              return (
                <DropdownMenuItem key={item.key} asChild>
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center gap-2 w-full",
                      active && "text-blue-700 bg-blue-50"
                    )}
                    aria-current={active ? "page" : undefined}
                  >
                    <IconComponent className="h-4 w-4" />
                    <span>{item.label}</span>
                  </Link>
                </DropdownMenuItem>
              )
            })}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </nav>
  )
}

// Desktop navigation with full visibility
export function DesktopNav({ className }: { className?: string }) {
  const { user } = useSafeCustomerAuth()
  const pathname = usePathname()
  
  const isAuthenticated = Boolean(user)
  const allNavItems = React.useMemo(() => getFilteredNavItems(isAuthenticated), [isAuthenticated])
  
  const isActive = React.useCallback((href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }, [pathname])

  // Group items by type for better organization - memoize to prevent recalculation
  const { primaryItems, secondaryItems, actionItems } = React.useMemo(() => {
    return {
      primaryItems: allNavItems.filter(item => item.priority <= 2 && !item.isAction),
      secondaryItems: allNavItems.filter(item => item.priority > 2 && !item.isAction),
      actionItems: allNavItems.filter(item => item.isAction)
    }
  }, [allNavItems])

  return (
    <nav 
      className={cn(
        "flex items-center gap-6 overflow-visible",
        className
      )}
      role="navigation"
      aria-label="Main navigation"
    >
      {/* Primary Items */}
      <div className="flex items-center gap-4">
        {primaryItems.map((item) => {
          const active = isActive(item.href)
          const IconComponent = item.icon
          
          return (
            <Link
              key={item.key}
              href={item.href}
              className={cn(
                "flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium",
                "min-h-[44px] touch-manipulation nav-transition nav-focus-visible", // Accessibility
                active
                  ? "text-blue-700 bg-blue-50"
                  : "text-gray-600 hover:text-blue-600 hover:bg-gray-50"
              )}
              aria-current={active ? "page" : undefined}
            >
              <IconComponent className="h-4 w-4" />
              <span>{item.label}</span>
            </Link>
          )
        })}
      </div>

      {/* Secondary Items Dropdown */}
      {secondaryItems.length > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="tertiary"
              size="sm"
              className="flex items-center gap-1 min-h-[44px]"
              aria-label="Additional options"
            >
              <span>More</span>
              <ChevronDown className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          
          <DropdownMenuContent 
            align="center" 
            className="w-48 z-50 !bg-white !border !border-gray-200 !shadow-lg !rounded-md !p-1"
            sideOffset={8}
            avoidCollisions={true}
            collisionPadding={8}
          >
            {secondaryItems.map((item) => {
              const active = isActive(item.href)
              const IconComponent = item.icon
              
              return (
                <DropdownMenuItem key={item.key} asChild>
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center gap-2 w-full",
                      active && "text-blue-700 bg-blue-50"
                    )}
                    aria-current={active ? "page" : undefined}
                  >
                    <IconComponent className="h-4 w-4" />
                    <span>{item.label}</span>
                  </Link>
                </DropdownMenuItem>
              )
            })}
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* Action Items */}
      {actionItems.map((item) => {
        const active = isActive(item.href)
        const IconComponent = item.icon
        
        return (
          <Link key={item.key} href={item.href}>
            <Button 
              size="sm"
              aria-current={active ? "page" : undefined}
            >
              <IconComponent className="h-4 w-4 mr-1.5" />
              {item.label}
            </Button>
          </Link>
        )
      })}
    </nav>
  )
}
