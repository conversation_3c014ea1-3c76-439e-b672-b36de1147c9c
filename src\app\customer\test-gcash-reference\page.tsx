"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PublicAppShell } from "@/components/layout/public-app-shell";
import { 
  Smartphone, 
  CheckCircle, 
  AlertCircle, 
  InfoIcon,
  CreditCard,
  TestTube,
  Monitor,
  Tablet,
  Smartphone as SmartphoneIcon
} from "lucide-react";
import { cn } from "@/lib/utils";

export default function TestGCashReferencePage() {
  const [paymentMethod, setPaymentMethod] = React.useState<"GCash" | "Bank Transfer" | "Remittance Center" | null>(null);
  const [gcashReference, setGcashReference] = React.useState("");
  const [gcashReferenceError, setGcashReferenceError] = React.useState("");
  const [viewportWidth, setViewportWidth] = React.useState(0);

  // Update viewport width on resize
  React.useEffect(() => {
    const updateViewportWidth = () => setViewportWidth(window.innerWidth);
    updateViewportWidth();
    window.addEventListener('resize', updateViewportWidth);
    return () => window.removeEventListener('resize', updateViewportWidth);
  }, []);

  const validateGCashReference = (value: string): string => {
    if (!value || value.trim().length === 0) {
      return "GCash reference number is required";
    }
    
    // Remove spaces and special characters for validation
    const cleanValue = value.replace(/[^0-9]/g, "");
    
    // GCash reference numbers are typically 13 digits
    if (cleanValue.length < 10) {
      return "Reference number is too short (minimum 10 digits)";
    }
    
    if (cleanValue.length > 20) {
      return "Reference number is too long (maximum 20 digits)";
    }
    
    return "";
  };

  const handleGCashReferenceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setGcashReference(value);
    
    // Validate in real-time
    const error = validateGCashReference(value);
    setGcashReferenceError(error);
  };

  const getBreakpointInfo = (width: number) => {
    if (width < 375) return { name: "Mobile S", icon: SmartphoneIcon, color: "text-red-600" };
    if (width < 425) return { name: "Mobile M", icon: SmartphoneIcon, color: "text-orange-600" };
    if (width < 768) return { name: "Mobile L", icon: SmartphoneIcon, color: "text-yellow-600" };
    if (width < 1024) return { name: "Tablet", icon: Tablet, color: "text-blue-600" };
    if (width < 1440) return { name: "Desktop", icon: Monitor, color: "text-green-600" };
    return { name: "Large Desktop", icon: Monitor, color: "text-purple-600" };
  };

  const breakpointInfo = getBreakpointInfo(viewportWidth);
  const BreakpointIcon = breakpointInfo.icon;

  const testCases = [
    { label: "Valid Short", value: "1234567890", expectedValid: true },
    { label: "Valid Long", value: "12345678901234567890", expectedValid: true },
    { label: "Too Short", value: "123456789", expectedValid: false },
    { label: "Too Long", value: "123456789012345678901", expectedValid: false },
    { label: "Empty", value: "", expectedValid: false },
    { label: "With Spaces", value: "1234 5678 9012 3456", expectedValid: true },
  ];

  return (
    <PublicAppShell>
      <div className="min-h-screen bg-gray-50 p-2 sm:p-4 lg:p-8">
        <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6">
          {/* Header */}
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader className="p-4 sm:p-6">
              <CardTitle className="text-xl sm:text-2xl flex items-center gap-2 text-blue-900">
                <TestTube className="h-5 w-5 sm:h-6 sm:w-6" />
                GCash Reference Number Test Page
              </CardTitle>
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                <p className="text-blue-700 text-sm sm:text-base">
                  Test the GCash reference number input functionality across different screen sizes
                </p>
                <div className="flex items-center gap-2">
                  <BreakpointIcon className={cn("h-4 w-4", breakpointInfo.color)} />
                  <Badge variant="secondary" className={cn("text-xs", breakpointInfo.color)}>
                    {breakpointInfo.name} ({viewportWidth}px)
                  </Badge>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Payment Method Selection Test */}
          <Card>
            <CardHeader className="p-4 sm:p-6">
              <CardTitle className="text-lg sm:text-xl flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payment Method Selection
              </CardTitle>
              <p className="text-gray-600 text-sm">
                Select a payment method to test the conditional GCash reference field
              </p>
            </CardHeader>
            <CardContent className="p-4 sm:p-6 pt-0">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
                {(["GCash", "Bank Transfer", "Remittance Center"] as const).map((method) => {
                  const isSelected = paymentMethod === method;
                  return (
                    <button
                      key={method}
                      onClick={() => setPaymentMethod(method)}
                      className={cn(
                        "p-3 sm:p-4 text-left rounded-lg border-2 transition-all",
                        isSelected
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-200 bg-white hover:border-gray-300"
                      )}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        {method === "GCash" && <Smartphone className="h-4 w-4" />}
                        <span className="font-medium text-sm sm:text-base">{method}</span>
                        {isSelected && <CheckCircle className="h-4 w-4 text-blue-500 ml-auto" />}
                      </div>
                    </button>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* GCash Reference Number Input Test */}
          {paymentMethod === "GCash" && (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader className="p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl flex items-center gap-2 text-blue-900">
                  <Smartphone className="h-4 w-4 sm:h-5 sm:w-5" />
                  GCash Reference Number
                </CardTitle>
                <p className="text-blue-700 text-sm">
                  Enter the reference number from your GCash transaction
                </p>
              </CardHeader>
              <CardContent className="p-4 sm:p-6 pt-0">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label 
                      htmlFor="gcash-reference" 
                      className="text-sm font-medium text-blue-900"
                    >
                      Reference Number <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="gcash-reference"
                      type="text"
                      placeholder="Enter GCash reference number (e.g., 1234567890123)"
                      value={gcashReference}
                      onChange={handleGCashReferenceChange}
                      className={cn(
                        "font-mono",
                        gcashReferenceError 
                          ? "border-red-500 focus:border-red-500 focus:ring-red-500" 
                          : "border-blue-300 focus:border-blue-500 focus:ring-blue-500"
                      )}
                      required
                    />
                    {gcashReferenceError && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {gcashReferenceError}
                      </p>
                    )}
                    {gcashReference && !gcashReferenceError && (
                      <p className="text-sm text-green-600 flex items-center gap-1">
                        <CheckCircle className="h-3 w-3" />
                        Valid reference number
                      </p>
                    )}
                  </div>
                  
                  <Alert className="border-blue-200 bg-blue-50">
                    <InfoIcon className="h-4 w-4 text-blue-600" />
                    <AlertDescription className="text-blue-800">
                      <strong>Where to find your reference number:</strong>
                      <ul className="mt-2 space-y-1 text-sm">
                        <li>• Check your GCash transaction history in the app</li>
                        <li>• Look for "Reference No." or "Transaction ID" in your receipt</li>
                        <li>• It's usually a 10-20 digit number</li>
                        <li>• Take a screenshot of the transaction details for your records</li>
                      </ul>
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Validation Test Cases */}
          <Card>
            <CardHeader className="p-4 sm:p-6">
              <CardTitle className="text-lg sm:text-xl">Validation Test Cases</CardTitle>
              <p className="text-gray-600 text-sm">
                Click these buttons to test different validation scenarios
              </p>
            </CardHeader>
            <CardContent className="p-4 sm:p-6 pt-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3">
                {testCases.map((testCase) => {
                  const error = validateGCashReference(testCase.value);
                  const isValid = !error;
                  const expectedResult = testCase.expectedValid;
                  const testPassed = isValid === expectedResult;

                  return (
                    <Button
                      key={testCase.label}
                      variant="secondary"
                      size="sm"
                      onClick={() => {
                        setPaymentMethod("GCash");
                        setGcashReference(testCase.value);
                        setGcashReferenceError(error);
                      }}
                      className={cn(
                        "flex flex-col items-start p-3 h-auto text-left",
                        testPassed ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"
                      )}
                    >
                      <div className="flex items-center justify-between w-full mb-1">
                        <span className="font-medium text-xs">{testCase.label}</span>
                        {testPassed ? (
                          <CheckCircle className="h-3 w-3 text-green-600" />
                        ) : (
                          <AlertCircle className="h-3 w-3 text-red-600" />
                        )}
                      </div>
                      <span className="font-mono text-xs text-gray-600 break-all">
                        {testCase.value || "(empty)"}
                      </span>
                      <span className={cn(
                        "text-xs mt-1",
                        testPassed ? "text-green-600" : "text-red-600"
                      )}>
                        Expected: {expectedResult ? "Valid" : "Invalid"}
                      </span>
                    </Button>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Responsive Design Test */}
          <Card>
            <CardHeader className="p-4 sm:p-6">
              <CardTitle className="text-lg sm:text-xl">Responsive Design Test</CardTitle>
              <p className="text-gray-600 text-sm">
                Current breakpoint information and design verification
              </p>
            </CardHeader>
            <CardContent className="p-4 sm:p-6 pt-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="bg-gray-50 p-3 rounded-lg">
                  <h4 className="font-medium text-sm mb-2">Current Viewport</h4>
                  <p className="text-lg font-bold">{viewportWidth}px</p>
                  <p className="text-sm text-gray-600">{breakpointInfo.name}</p>
                </div>
                
                <div className="bg-gray-50 p-3 rounded-lg">
                  <h4 className="font-medium text-sm mb-2">Input Field</h4>
                  <p className="text-sm">
                    {paymentMethod === "GCash" ? "✅ Visible" : "❌ Hidden"}
                  </p>
                  <p className="text-xs text-gray-600">
                    Shows only when GCash selected
                  </p>
                </div>

                <div className="bg-gray-50 p-3 rounded-lg">
                  <h4 className="font-medium text-sm mb-2">Validation</h4>
                  <p className="text-sm">
                    {gcashReference && paymentMethod === "GCash" 
                      ? (gcashReferenceError ? "❌ Invalid" : "✅ Valid")
                      : "⏳ Pending"
                    }
                  </p>
                  <p className="text-xs text-gray-600">
                    Real-time validation active
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Testing Instructions */}
          <Card className="border-green-200 bg-green-50">
            <CardHeader className="p-4 sm:p-6">
              <CardTitle className="text-lg sm:text-xl text-green-900">Testing Instructions</CardTitle>
            </CardHeader>
            <CardContent className="p-4 sm:p-6 pt-0">
              <div className="space-y-3 text-green-800">
                <div>
                  <h4 className="font-medium mb-1">1. Test Conditional Visibility</h4>
                  <p className="text-sm">Select different payment methods and verify the GCash reference field only appears when GCash is selected.</p>
                </div>
                
                <div>
                  <h4 className="font-medium mb-1">2. Test Validation</h4>
                  <p className="text-sm">Use the validation test case buttons to verify different input scenarios work correctly.</p>
                </div>
                
                <div>
                  <h4 className="font-medium mb-1">3. Test Responsive Design</h4>
                  <p className="text-sm">Resize your browser window to test different breakpoints: 320px, 375px, 425px, 768px, 1024px, 1440px+</p>
                </div>
                
                <div>
                  <h4 className="font-medium mb-1">4. Test Real Booking Flow</h4>
                  <p className="text-sm">Navigate to the actual booking flow and verify the GCash reference number appears during payment step when GCash is selected.</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PublicAppShell>
  );
}
