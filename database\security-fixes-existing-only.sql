-- First, let's identify which functions actually exist in your database
-- Run this query first to see what functions need fixing:

SELECT 
  p.proname as function_name,
  pg_get_function_identity_arguments(p.oid) as arguments,
  p.prosecdef as security_definer,
  p.proconfig as search_path_config,
  CASE WHEN p.prosecdef THEN '✅ SECURE' ELSE '❌ NEEDS SECURITY DEFINER' END as security_status,
  CASE WHEN 'search_path=public' = ANY(p.proconfig) THEN '✅ FIXED' ELSE '❌ NEEDS SEARCH_PATH' END as search_path_status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
  AND p.proname IN (
    'get_vehicle_categories_with_stats',
    'get_cars_by_category', 
    'update_category_pricing',
    'check_booking_documents_complete',
    'get_booking_document_status',
    'get_archived_cars',
    'can_archive_car',
    'car_has_archive_history',
    'get_car_archive_history',
    'sync_car_archive_status',
    'handle_new_user',
    'handle_updated_at'
  )
ORDER BY p.proname;

-- ========================================
-- SECURITY FIXES FOR EXISTING FUNCTIONS ONLY
-- ========================================

-- Enable RLS on vehicle_categories table (if not already enabled)
DO $$ 
BEGIN
  IF NOT (SELECT rowsecurity FROM pg_tables WHERE tablename = 'vehicle_categories' AND schemaname = 'public') THEN
    ALTER TABLE public.vehicle_categories ENABLE ROW LEVEL SECURITY;
  END IF;
END $$;

-- Create policies only if they don't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'vehicle_categories' 
    AND policyname = 'Allow public read access to vehicle categories'
  ) THEN
    CREATE POLICY "Allow public read access to vehicle categories" ON public.vehicle_categories
    FOR SELECT 
    USING (is_active = true);
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'vehicle_categories' 
    AND policyname = 'Allow admin full access to vehicle categories'
  ) THEN
    CREATE POLICY "Allow admin full access to vehicle categories" ON public.vehicle_categories
    FOR ALL 
    USING (
      EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
      )
    );
  END IF;
END $$;

-- Grant necessary permissions (safe to re-run)
GRANT SELECT ON public.vehicle_categories TO anon, authenticated;
GRANT ALL ON public.vehicle_categories TO authenticated;

-- ========================================
-- FIX FUNCTIONS CONDITIONALLY (ONLY IF THEY EXIST)
-- ========================================

-- Fix get_vehicle_categories_with_stats (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_vehicle_categories_with_stats' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.get_vehicle_categories_with_stats() SET search_path = public;
    ALTER FUNCTION public.get_vehicle_categories_with_stats() SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed get_vehicle_categories_with_stats()';
  ELSE
    RAISE NOTICE '❌ Function get_vehicle_categories_with_stats() does not exist';
  END IF;
END $$;

-- Fix get_cars_by_category (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_cars_by_category' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.get_cars_by_category(text) SET search_path = public;
    ALTER FUNCTION public.get_cars_by_category(text) SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed get_cars_by_category(text)';
  ELSE
    RAISE NOTICE '❌ Function get_cars_by_category(text) does not exist';
  END IF;
END $$;

-- Fix update_category_pricing (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_category_pricing' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.update_category_pricing(uuid, numeric, numeric) SET search_path = public;
    ALTER FUNCTION public.update_category_pricing(uuid, numeric, numeric) SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed update_category_pricing(uuid, numeric, numeric)';
  ELSE
    RAISE NOTICE '❌ Function update_category_pricing(uuid, numeric, numeric) does not exist';
  END IF;
END $$;

-- Fix check_booking_documents_complete (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'check_booking_documents_complete' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.check_booking_documents_complete(uuid) SET search_path = public;
    ALTER FUNCTION public.check_booking_documents_complete(uuid) SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed check_booking_documents_complete(uuid)';
  ELSE
    RAISE NOTICE '❌ Function check_booking_documents_complete(uuid) does not exist';
  END IF;
END $$;

-- Fix get_booking_document_status (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_booking_document_status' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.get_booking_document_status(uuid) SET search_path = public;
    ALTER FUNCTION public.get_booking_document_status(uuid) SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed get_booking_document_status(uuid)';
  ELSE
    RAISE NOTICE '❌ Function get_booking_document_status(uuid) does not exist';
  END IF;
END $$;

-- Fix get_archived_cars (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_archived_cars' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.get_archived_cars() SET search_path = public;
    ALTER FUNCTION public.get_archived_cars() SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed get_archived_cars()';
  ELSE
    RAISE NOTICE '❌ Function get_archived_cars() does not exist';
  END IF;
END $$;

-- Fix can_archive_car (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'can_archive_car' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.can_archive_car(uuid) SET search_path = public;
    ALTER FUNCTION public.can_archive_car(uuid) SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed can_archive_car(uuid)';
  ELSE
    RAISE NOTICE '❌ Function can_archive_car(uuid) does not exist';
  END IF;
END $$;

-- Fix car_has_archive_history (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'car_has_archive_history' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.car_has_archive_history(uuid) SET search_path = public;
    ALTER FUNCTION public.car_has_archive_history(uuid) SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed car_has_archive_history(uuid)';
  ELSE
    RAISE NOTICE '❌ Function car_has_archive_history(uuid) does not exist';
  END IF;
END $$;

-- Fix get_car_archive_history (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_car_archive_history' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.get_car_archive_history(uuid) SET search_path = public;
    ALTER FUNCTION public.get_car_archive_history(uuid) SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed get_car_archive_history(uuid)';
  ELSE
    RAISE NOTICE '❌ Function get_car_archive_history(uuid) does not exist';
  END IF;
END $$;

-- Fix sync_car_archive_status (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'sync_car_archive_status' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.sync_car_archive_status(uuid, boolean) SET search_path = public;
    ALTER FUNCTION public.sync_car_archive_status(uuid, boolean) SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed sync_car_archive_status(uuid, boolean)';
  ELSE
    RAISE NOTICE '❌ Function sync_car_archive_status(uuid, boolean) does not exist';
  END IF;
END $$;

-- Fix handle_new_user (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'handle_new_user' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.handle_new_user() SET search_path = public;
    ALTER FUNCTION public.handle_new_user() SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed handle_new_user()';
  ELSE
    RAISE NOTICE '❌ Function handle_new_user() does not exist';
  END IF;
END $$;

-- Fix handle_updated_at (if exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'handle_updated_at' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.handle_updated_at() SET search_path = public;
    ALTER FUNCTION public.handle_updated_at() SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed handle_updated_at()';
  ELSE
    RAISE NOTICE '❌ Function handle_updated_at() does not exist';
  END IF;
END $$;

-- ========================================
-- FINAL VERIFICATION
-- ========================================

-- Show RLS status
SELECT 
  schemaname, 
  tablename, 
  rowsecurity,
  CASE WHEN rowsecurity THEN '✅ ENABLED' ELSE '❌ DISABLED' END as status
FROM pg_tables 
WHERE tablename = 'vehicle_categories';

-- Show updated function security settings
SELECT 
  p.proname as function_name,
  pg_get_function_identity_arguments(p.oid) as arguments,
  p.prosecdef as security_definer,
  p.proconfig as search_path_config,
  CASE WHEN p.prosecdef THEN '✅ SECURE' ELSE '❌ INSECURE' END as security_status,
  CASE WHEN 'search_path=public' = ANY(p.proconfig) THEN '✅ FIXED' ELSE '❌ MUTABLE' END as search_path_status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
  AND (p.proname LIKE '%vehicle%' OR p.proname LIKE '%car%' OR p.proname LIKE '%booking%' OR p.proname = 'handle_new_user' OR p.proname = 'handle_updated_at')
ORDER BY p.proname;
