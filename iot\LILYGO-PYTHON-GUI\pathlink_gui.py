#!/usr/bin/env python3
"""
PathLink GPS Tracker - GUI Control Panel
=======================================

A desktop-based GUI application for configuring and monitoring the PathLink GPS system.
Features:
- Configure URLs (Dev/Prod)
- Switch between HTTP POST, HTTP GET, or WebSockets
- Send test data (uses default coordinates from config; no manual GPS entry)
- Monitor ESP32 responses
- Real-time status monitoring
- Dual-send mode (DEV + PROD)

Author: PathLink Development Team
Version: 1.1.0
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("Warning: requests package not found. HTTP functionality will be disabled.")
try:
    import websocket
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False
    print("Warning: websocket-client package not found. WebSocket functionality will be disabled.")
import threading
import time
from datetime import datetime
import os
import configparser
from typing import Dict, Any, Optional
import queue


class PathLinkGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PathLink GPS Tracker - Control Panel")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # Configuration
        self.config = configparser.ConfigParser()
        self.config_file = "pathlink_config.ini"

        # Communication settings (must exist before load_config)
        self.current_protocol = tk.StringVar(value="HTTP_POST")
        self.environment = tk.StringVar(value="DEV")
        self.dual_send = tk.BooleanVar(value=False)  # NEW: Dual-send toggle

        # Status variables
        self.connection_status = tk.StringVar(value="Disconnected")  # HTTP/WS status
        self.device_status = tk.StringVar(value="Disconnected")      # Device status (separate)
        self.last_response = tk.StringVar(value="No response yet")
        self.response_queue = queue.Queue()

        # WebSocket connections (support DEV + PROD when dual_send is True)
        self.ws_connection_dev = None
        self.ws_thread_dev = None
        self.ws_connection_prod = None
        self.ws_thread_prod = None

        # Defaults for test data (no manual GPS fields in UI)
        self.default_car_id = "lilygo-esp32-01"
        self.default_test_lat = 14.5995    # Manila City Hall (example)
        self.default_test_lon = 120.9842

        # Load config now that vars exist
        self.load_config()

        # Build UI
        self.create_widgets()
        self.setup_styles()

        # Initialize widgets based on env/dual-send
        self.update_environment_widgets()

        # Start response monitor loop
        self.start_monitoring()

    # -------------------- UI BUILD --------------------

    def create_widgets(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)

        title_label = ttk.Label(
            main_frame,
            text="PathLink GPS Tracker Control Panel",
            font=("Arial", 16, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        self.create_config_section(main_frame)
        self.create_protocol_section(main_frame)
        self.create_device_comm_section(main_frame)    # renamed section
        self.create_monitoring_section(main_frame)
        self.create_status_bar(main_frame)

    def create_config_section(self, parent):
        config_frame = ttk.LabelFrame(parent, text="Configuration", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)

        # Environment select + Dual-send toggle
        ttk.Label(config_frame, text="Environment:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        env_combo = ttk.Combobox(
            config_frame, textvariable=self.environment,
            values=["DEV", "PROD"], state="readonly", width=10
        )
        env_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        env_combo.bind("<<ComboboxSelected>>", self.on_environment_change)

        dual_chk = ttk.Checkbutton(
            config_frame,
            text="Dual-send to DEV & PROD",
            variable=self.dual_send,
            command=self.on_dual_send_toggle
        )
        dual_chk.grid(row=0, column=2, sticky=tk.W, padx=(0, 10))

        # DEV config
        dev_frame = ttk.LabelFrame(config_frame, text="Development Environment", padding="5")
        dev_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        dev_frame.columnconfigure(1, weight=1)

        ttk.Label(dev_frame, text="HTTP Host:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.dev_http_host = ttk.Entry(dev_frame, width=30)
        self.dev_http_host.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.dev_http_host.insert(0, self.config.get('DEV', 'http_host', fallback='***************'))

        ttk.Label(dev_frame, text="HTTP Port:").grid(row=0, column=2, sticky=tk.W, padx=(20, 10))
        self.dev_http_port = ttk.Entry(dev_frame, width=10)
        self.dev_http_port.grid(row=0, column=3, sticky=tk.W, padx=(0, 20))
        self.dev_http_port.insert(0, self.config.get('DEV', 'http_port', fallback='3000'))

        ttk.Label(dev_frame, text="HTTP Path:").grid(row=0, column=4, sticky=tk.W, padx=(0, 10))
        self.dev_http_path = ttk.Entry(dev_frame, width=20)
        self.dev_http_path.grid(row=0, column=5, sticky=tk.W, padx=(0, 10))
        self.dev_http_path.insert(0, self.config.get('DEV', 'http_path', fallback='/api/gps/ingest'))

        ttk.Label(dev_frame, text="WebSocket Host:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.dev_ws_host = ttk.Entry(dev_frame, width=30)
        self.dev_ws_host.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.dev_ws_host.insert(0, self.config.get('DEV', 'ws_host', fallback='***************'))

        ttk.Label(dev_frame, text="WebSocket Port:").grid(row=1, column=2, sticky=tk.W, padx=(20, 10))
        self.dev_ws_port = ttk.Entry(dev_frame, width=10)
        self.dev_ws_port.grid(row=1, column=3, sticky=tk.W, padx=(0, 20))
        self.dev_ws_port.insert(0, self.config.get('DEV', 'ws_port', fallback='3000'))

        ttk.Label(dev_frame, text="WebSocket Path:").grid(row=1, column=4, sticky=tk.W, padx=(0, 10))
        self.dev_ws_path = ttk.Entry(dev_frame, width=20)
        self.dev_ws_path.grid(row=1, column=5, sticky=tk.W, padx=(0, 10))
        self.dev_ws_path.insert(0, self.config.get('DEV', 'ws_path', fallback='/api/ws/tracker'))

        # PROD config
        prod_frame = ttk.LabelFrame(config_frame, text="Production Environment", padding="5")
        prod_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        prod_frame.columnconfigure(1, weight=1)

        ttk.Label(prod_frame, text="HTTP Host:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.prod_http_host = ttk.Entry(prod_frame, width=30)
        self.prod_http_host.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.prod_http_host.insert(0, self.config.get('PROD', 'http_host', fallback='olliesrentalcar.pathlinkio.app'))

        ttk.Label(prod_frame, text="HTTP Port:").grid(row=0, column=2, sticky=tk.W, padx=(20, 10))
        self.prod_http_port = ttk.Entry(prod_frame, width=10)
        self.prod_http_port.grid(row=0, column=3, sticky=tk.W, padx=(0, 20))
        self.prod_http_port.insert(0, self.config.get('PROD', 'http_port', fallback='443'))

        ttk.Label(prod_frame, text="HTTP Path:").grid(row=0, column=4, sticky=tk.W, padx=(0, 10))
        self.prod_http_path = ttk.Entry(prod_frame, width=20)
        self.prod_http_path.grid(row=0, column=5, sticky=tk.W, padx=(0, 10))
        self.prod_http_path.insert(0, self.config.get('PROD', 'http_path', fallback='/api/gps/ingest'))

        ttk.Label(prod_frame, text="WebSocket Host:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.prod_ws_host = ttk.Entry(prod_frame, width=30)
        self.prod_ws_host.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.prod_ws_host.insert(0, self.config.get('PROD', 'ws_host', fallback='olliesrentalcar.pathlinkio.app'))

        ttk.Label(prod_frame, text="WebSocket Port:").grid(row=1, column=2, sticky=tk.W, padx=(20, 10))
        self.prod_ws_port = ttk.Entry(prod_frame, width=10)
        self.prod_ws_port.grid(row=1, column=3, sticky=tk.W, padx=(0, 20))
        self.prod_ws_port.insert(0, self.config.get('PROD', 'ws_port', fallback='443'))

        ttk.Label(prod_frame, text="WebSocket Path:").grid(row=1, column=4, sticky=tk.W, padx=(0, 10))
        self.prod_ws_path = ttk.Entry(prod_frame, width=20)
        self.prod_ws_path.grid(row=1, column=5, sticky=tk.W, padx=(0, 10))
        self.prod_ws_path.insert(0, self.config.get('PROD', 'ws_path', fallback='/api/ws/tracker'))

        save_btn = ttk.Button(config_frame, text="Save Configuration", command=self.save_config)
        save_btn.grid(row=3, column=0, columnspan=3, pady=(15, 0))

    def create_protocol_section(self, parent):
        protocol_frame = ttk.LabelFrame(parent, text="Communication Protocol", padding="10")
        protocol_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        protocols = [
            ("HTTP POST (Default)", "HTTP_POST"),
            ("HTTP GET (Fallback)", "HTTP_GET"),
            ("WebSocket (Real-time)", "WEBSOCKET"),
            ("CoAP (Lightweight)", "COAP"),
            ("AMQP (Message Broker)", "AMQP"),
        ]

        for i, (text, value) in enumerate(protocols):
            radio = ttk.Radiobutton(
                protocol_frame, text=text, value=value,
                variable=self.current_protocol, command=self.on_protocol_change
            )
            if value == "WEBSOCKET" and not WEBSOCKET_AVAILABLE:
                radio.config(state="disabled")
            elif value in ["HTTP_POST", "HTTP_GET"] and not REQUESTS_AVAILABLE:
                radio.config(state="disabled")
            radio.grid(row=i // 3, column=i % 3, sticky=tk.W, padx=(0, 20), pady=2)

        # Connection controls + statuses
        conn_frame = ttk.Frame(protocol_frame)
        conn_frame.grid(row=2, column=0, columnspan=3, pady=(15, 0), sticky=tk.W)

        self.connect_btn = ttk.Button(conn_frame, text="Connect", command=self.connect)
        self.connect_btn.grid(row=0, column=0, padx=(0, 10))

        self.disconnect_btn = ttk.Button(conn_frame, text="Disconnect", command=self.disconnect, state="disabled")
        self.disconnect_btn.grid(row=0, column=1, padx=(0, 10))

        ttk.Label(conn_frame, text="Protocol Status:").grid(row=0, column=2, padx=(20, 10))
        self.protocol_status_label = ttk.Label(
            conn_frame, textvariable=self.connection_status,
            foreground="red", font=("Arial", 9, "bold")
        )
        self.protocol_status_label.grid(row=0, column=3)

        ttk.Label(conn_frame, text="Device Status:").grid(row=0, column=4, padx=(20, 10))
        self.device_status_label = ttk.Label(
            conn_frame, textvariable=self.device_status,
            foreground="red", font=("Arial", 9, "bold")
        )
        self.device_status_label.grid(row=0, column=5)

    def create_device_comm_section(self, parent):
        """
        Device Communication & Monitoring
        (renamed; manual GPS inputs removed)
        """
        test_frame = ttk.LabelFrame(parent, text="Device Communication & Monitoring", padding="10")
        test_frame.grid(row=3, column=0, columnspan=3,
                        sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        test_frame.columnconfigure(1, weight=1)
        test_frame.rowconfigure(1, weight=1)

        # Inputs row (car ID only; no manual GPS fields)
        input_frame = ttk.Frame(test_frame)
        input_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(1, weight=1)

        ttk.Label(input_frame, text="Car ID:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.car_id_entry = ttk.Entry(input_frame, width=20)
        self.car_id_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        self.car_id_entry.insert(0, self.config.get('GENERAL', 'car_id', fallback=self.default_car_id))

        send_btn = ttk.Button(input_frame, text="Send Test Data", command=self.send_test_data)
        send_btn.grid(row=0, column=2, padx=(20, 0))

        # Response Monitor
        monitor_frame = ttk.Frame(test_frame)
        monitor_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        monitor_frame.columnconfigure(0, weight=1)
        monitor_frame.rowconfigure(0, weight=1)

        ttk.Label(monitor_frame, text="ESP32 Response Monitor:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.response_text = scrolledtext.ScrolledText(monitor_frame, height=15, width=80)
        self.response_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        clear_btn = ttk.Button(monitor_frame, text="Clear Log", command=self.clear_log)
        clear_btn.grid(row=2, column=0, pady=(5, 0), sticky=tk.W)

        # Serial section
        serial_monitor_frame = ttk.LabelFrame(test_frame, text="Serial Communication", padding="5")
        serial_monitor_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        serial_monitor_frame.columnconfigure(1, weight=1)

        ttk.Label(serial_monitor_frame, text="Send Command:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.serial_input = ttk.Entry(serial_monitor_frame, width=50)
        self.serial_input.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.serial_input.bind("<Return>", self.send_serial_command)

        send_serial_btn = ttk.Button(serial_monitor_frame, text="Send", command=self.send_serial_command)
        send_serial_btn.grid(row=0, column=2, padx=(0, 10))

        ttk.Label(serial_monitor_frame, text="Device Response:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.serial_output = scrolledtext.ScrolledText(serial_monitor_frame, height=8, width=80)
        self.serial_output.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))

        clear_serial_btn = ttk.Button(serial_monitor_frame, text="Clear Serial", command=self.clear_serial)
        clear_serial_btn.grid(row=3, column=0, pady=(5, 0), sticky=tk.W)

    def create_monitoring_section(self, parent):
        monitor_frame = ttk.LabelFrame(parent, text="System Status & Monitoring", padding="10")
        monitor_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        monitor_frame.columnconfigure(1, weight=1)

        ttk.Label(monitor_frame, text="Last Response:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        response_label = ttk.Label(monitor_frame, textvariable=self.last_response, font=("Arial", 9))
        response_label.grid(row=0, column=1, sticky=tk.W)

        self.auto_refresh = tk.BooleanVar(value=True)
        refresh_check = ttk.Checkbutton(monitor_frame, text="Auto-refresh status", variable=self.auto_refresh)
        refresh_check.grid(row=0, column=2, padx=(20, 0))

        refresh_btn = ttk.Button(monitor_frame, text="Refresh Now", command=self.refresh_status)
        refresh_btn.grid(row=0, column=3, padx=(20, 0))

    def create_status_bar(self, parent):
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)

        ttk.Separator(status_frame, orient="horizontal").grid(row=0, column=0, columnspan=2,
                                                             sticky=(tk.W, tk.E), pady=(0, 5))

        status_info = ttk.Label(status_frame, text="Ready", font=("Arial", 8))
        status_info.grid(row=1, column=0, sticky=tk.W)

        version_info = ttk.Label(status_frame, text="v1.1.0", font=("Arial", 8))
        version_info.grid(row=1, column=1, sticky=tk.E)

    # -------------------- STYLES --------------------

    def setup_styles(self):
        style = ttk.Style()
        style.configure("Title.TLabel", font=("Arial", 16, "bold"))
        style.configure("Status.TLabel", font=("Arial", 9, "bold"))

    # -------------------- CONFIG --------------------

    def load_config(self):
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file)
                if 'GENERAL' in self.config:
                    if 'default_environment' in self.config['GENERAL']:
                        self.environment.set(self.config['GENERAL']['default_environment'])
                    if 'default_protocol' in self.config['GENERAL']:
                        self.current_protocol.set(self.config['GENERAL']['default_protocol'])
                    if 'car_id' in self.config['GENERAL']:
                        self.default_car_id = self.config['GENERAL']['car_id']
                    if 'dual_send' in self.config['GENERAL']:
                        self.dual_send.set(self.config.getboolean('GENERAL', 'dual_send', fallback=False))
                    if 'test_lat' in self.config['GENERAL']:
                        self.default_test_lat = float(self.config['GENERAL']['test_lat'])
                    if 'test_lon' in self.config['GENERAL']:
                        self.default_test_lon = float(self.config['GENERAL']['test_lon'])
            except Exception as e:
                messagebox.showerror("Configuration Error", f"Failed to load configuration: {e}")
        else:
            # Defaults to write on first Save
            self.config['DEV'] = {
                'http_host': '***************',
                'http_port': '3000',
                'http_path': '/api/gps/ingest',
                'ws_host': '***************',
                'ws_port': '3000',
                'ws_path': '/api/ws/tracker'
            }
            self.config['PROD'] = {
                'http_host': 'olliesrentalcar.pathlinkio.app',
                'http_port': '443',
                'http_path': '/api/gps/ingest',
                'ws_host': 'olliesrentalcar.pathlinkio.app',
                'ws_port': '443',
                'ws_path': '/api/ws/tracker'
            }
            self.config['GENERAL'] = {
                'car_id': self.default_car_id,
                'default_protocol': 'HTTP_POST',
                'default_environment': 'DEV',
                'dual_send': 'false',
                'test_lat': str(self.default_test_lat),
                'test_lon': str(self.default_test_lon),
            }

    def save_config(self):
        try:
            # DEV
            self.config['DEV']['http_host'] = self.dev_http_host.get()
            self.config['DEV']['http_port'] = self.dev_http_port.get()
            self.config['DEV']['http_path'] = self.dev_http_path.get()
            self.config['DEV']['ws_host'] = self.dev_ws_host.get()
            self.config['DEV']['ws_port'] = self.dev_ws_port.get()
            self.config['DEV']['ws_path'] = self.dev_ws_path.get()

            # PROD
            self.config['PROD']['http_host'] = self.prod_http_host.get()
            self.config['PROD']['http_port'] = self.prod_http_port.get()
            self.config['PROD']['http_path'] = self.prod_http_path.get()
            self.config['PROD']['ws_host'] = self.prod_ws_host.get()
            self.config['PROD']['ws_port'] = self.prod_ws_port.get()
            self.config['PROD']['ws_path'] = self.prod_ws_path.get()

            # GENERAL
            self.config['GENERAL']['car_id'] = self.car_id_entry.get()
            self.config['GENERAL']['default_protocol'] = self.current_protocol.get()
            self.config['GENERAL']['default_environment'] = self.environment.get()
            self.config['GENERAL']['dual_send'] = 'true' if self.dual_send.get() else 'false'
            self.config['GENERAL']['test_lat'] = str(self.default_test_lat)
            self.config['GENERAL']['test_lon'] = str(self.default_test_lon)

            with open(self.config_file, 'w') as configfile:
                self.config.write(configfile)

            messagebox.showinfo("Success", "Configuration saved successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {e}")

    # -------------------- ENV / PROTOCOL HANDLERS --------------------

    def on_environment_change(self, event=None):
        self.update_environment_widgets()

    def on_dual_send_toggle(self):
        self.update_environment_widgets()

    def update_environment_widgets(self):
        """Enable/disable config fields based on env & dual-send."""
        dual = self.dual_send.get()
        env = self.environment.get()

        def set_dev(state):
            for w in (self.dev_http_host, self.dev_http_port, self.dev_http_path,
                      self.dev_ws_host, self.dev_ws_port, self.dev_ws_path):
                w.config(state=state)

        def set_prod(state):
            for w in (self.prod_http_host, self.prod_http_port, self.prod_http_path,
                      self.prod_ws_host, self.prod_ws_port, self.prod_ws_path):
                w.config(state=state)

        if dual:
            set_dev("normal")
            set_prod("normal")
        else:
            if env == "DEV":
                set_dev("normal"); set_prod("disabled")
            else:
                set_dev("disabled"); set_prod("normal")

    def on_protocol_change(self):
        protocol = self.current_protocol.get()
        if protocol == "WEBSOCKET":
            if WEBSOCKET_AVAILABLE:
                self.connect_btn.config(text="Connect WebSocket")
            else:
                self.current_protocol.set("HTTP_POST")
                messagebox.showwarning("Protocol Warning", "WebSocket not available, falling back to HTTP POST")
        elif protocol in ["HTTP_POST", "HTTP_GET"]:
            if REQUESTS_AVAILABLE:
                self.connect_btn.config(text="Connect")
            else:
                self.current_protocol.set("WEBSOCKET" if WEBSOCKET_AVAILABLE else "HTTP_POST")
                messagebox.showwarning("Protocol Warning", "HTTP not available, switching protocol")
        else:
            self.connect_btn.config(text="Connect")

    # -------------------- CONNECT / DISCONNECT --------------------

    def connect(self):
        protocol = self.current_protocol.get()
        try:
            if protocol == "WEBSOCKET":
                if not WEBSOCKET_AVAILABLE:
                    messagebox.showerror("WebSocket Error", "websocket-client package not installed.")
                    return
                self.connect_websocket()
            else:
                if not REQUESTS_AVAILABLE:
                    messagebox.showerror("HTTP Error", "requests package not installed.")
                    return
                self.connect_http()
        except Exception as e:
            messagebox.showerror("Connection Error", f"Failed to connect: {e}")
            self.set_protocol_status("Connection Failed", "red")

    def connect_websocket(self):
        dual = self.dual_send.get()
        env = self.environment.get()

        # Helper to create WS app with tagged callbacks
        def make_ws(url, tag):
            def on_open(ws):
                self.on_ws_open(ws, tag)
            def on_message(ws, msg):
                self.on_ws_message(ws, msg, tag)
            def on_error(ws, err):
                self.on_ws_error(ws, err, tag)
            def on_close(ws, code, msg):
                self.on_ws_close(ws, code, msg, tag)
            return websocket.WebSocketApp(
                url, on_open=on_open, on_message=on_message,
                on_error=on_error, on_close=on_close
            )

        # Build endpoints
        dev_url = f"ws://{self.dev_ws_host.get()}:{self.dev_ws_port.get()}{self.dev_ws_path.get()}"
        prod_url = f"wss://{self.prod_ws_host.get()}:{self.prod_ws_port.get()}{self.prod_ws_path.get()}"

        # Connect based on mode
        # DEV
        if dual or env == "DEV":
            try:
                self.ws_connection_dev = make_ws(dev_url, "DEV")
                self.ws_thread_dev = threading.Thread(target=self.ws_connection_dev.run_forever, daemon=True)
                self.ws_thread_dev.start()
            except Exception as e:
                raise Exception(f"WebSocket DEV failed: {e}")

        # PROD
        if dual or env == "PROD":
            try:
                self.ws_connection_prod = make_ws(prod_url, "PROD")
                self.ws_thread_prod = threading.Thread(target=self.ws_connection_prod.run_forever, daemon=True)
                self.ws_thread_prod.start()
            except Exception as e:
                raise Exception(f"WebSocket PROD failed: {e}")

    def connect_http(self):
        dual = self.dual_send.get()
        env = self.environment.get()

        statuses = []

        def test_http(host, port, path, secure, tag):
            scheme = "https" if secure else "http"
            url = f"{scheme}://{host}:{port}{path}"
            try:
                r = requests.get(url, timeout=10)
                if r.status_code < 400:
                    statuses.append(f"{tag}: OK")
                    return True
                else:
                    statuses.append(f"{tag}: HTTP {r.status_code}")
                    return False
            except Exception as e:
                statuses.append(f"{tag}: {e}")
                return False

        ok_any = False
        if dual or env == "DEV":
            ok_any |= test_http(self.dev_http_host.get(), self.dev_http_port.get(),
                                self.dev_http_path.get(), False, "DEV")
        if dual or env == "PROD":
            ok_any |= test_http(self.prod_http_host.get(), self.prod_http_port.get(),
                                self.prod_http_path.get(), True, "PROD")

        if ok_any:
            status_text = " | ".join(statuses)
            self.set_protocol_status(status_text, "green")
            self.connect_btn.config(state="disabled")
            self.disconnect_btn.config(state="normal")
            self.set_device_status("Connected", "green")
        else:
            status_text = " | ".join(statuses) if statuses else "No endpoints tested"
            self.set_protocol_status(status_text, "red")

    def disconnect(self):
        # Close DEV WS
        if self.ws_connection_dev:
            try:
                self.ws_connection_dev.close()
            except:
                pass
            self.ws_connection_dev = None

        # Close PROD WS
        if self.ws_connection_prod:
            try:
                self.ws_connection_prod.close()
            except:
                pass
            self.ws_connection_prod = None

        self.set_protocol_status("Disconnected", "red")
        self.set_device_status("Disconnected", "red")
        self.connect_btn.config(state="normal")
        self.disconnect_btn.config(state="disabled")

    # -------------------- STATUS HELPERS --------------------

    def set_protocol_status(self, text, color="black"):
        self.connection_status.set(text)
        self.protocol_status_label.config(foreground=color)

    def set_device_status(self, text, color="black"):
        self.device_status.set(text)
        self.device_status_label.config(foreground=color)

    # -------------------- TEST DATA --------------------

    def send_test_data(self):
        """Send a sample GPS payload (no manual entry fields)."""
        car_id = self.car_id_entry.get() or self.default_car_id

        payload = {
            "carId": car_id,
            "latitude": float(self.config.get('GENERAL', 'test_lat', fallback=str(self.default_test_lat))),
            "longitude": float(self.config.get('GENERAL', 'test_lon', fallback=str(self.default_test_lon))),
            "speed_kmh": 0.0,
            "altitude": 0.0,
            "accuracy_m": 5.0,
            "sat_visible": 8,
            "sat_used": 6,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }

        protocol = self.current_protocol.get()
        dual = self.dual_send.get()
        env = self.environment.get()

        try:
            if protocol == "WEBSOCKET" and WEBSOCKET_AVAILABLE:
                sent_any = False
                if (dual or env == "DEV") and self.ws_connection_dev:
                    self.ws_connection_dev.send(json.dumps(payload))
                    self.log_response("WebSocket DEV: Sent test data")
                    sent_any = True
                if (dual or env == "PROD") and self.ws_connection_prod:
                    self.ws_connection_prod.send(json.dumps(payload))
                    self.log_response("WebSocket PROD: Sent test data")
                    sent_any = True
                if not sent_any:
                    messagebox.showwarning("WebSocket", "No active WebSocket connection(s) to send.")
            else:
                if not REQUESTS_AVAILABLE:
                    messagebox.showerror("HTTP Error", "requests package not installed.")
                    return
                self._send_http_payload(payload, dual, env)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to send test data: {e}")

    def _send_http_payload(self, payload, dual, env):
        headers = {"Content-Type": "application/json"}
        ok_any = False
        msgs = []

        # DEV
        if dual or env == "DEV":
            url = f"http://{self.dev_http_host.get()}:{self.dev_http_port.get()}{self.dev_http_path.get()}"
            try:
                r = requests.post(url, json=payload, headers=headers, timeout=10)
                if r.status_code < 400:
                    self.log_response(f"HTTP DEV {r.status_code}: Test data sent")
                    ok_any = True
                else:
                    self.log_response(f"HTTP DEV {r.status_code}: {r.text}")
                msgs.append(f"DEV->{r.status_code}")
            except Exception as e:
                self.log_response(f"HTTP DEV error: {e}")
                msgs.append(f"DEV->ERR")

        # PROD
        if dual or env == "PROD":
            url = f"https://{self.prod_http_host.get()}:{self.prod_http_port.get()}{self.prod_http_path.get()}"
            try:
                r = requests.post(url, json=payload, headers=headers, timeout=10)
                if r.status_code < 400:
                    self.log_response(f"HTTP PROD {r.status_code}: Test data sent")
                    ok_any = True
                else:
                    self.log_response(f"HTTP PROD {r.status_code}: {r.text}")
                msgs.append(f"PROD->{r.status_code}")
            except Exception as e:
                self.log_response(f"HTTP PROD error: {e}")
                msgs.append(f"PROD->ERR")

        self.last_response.set(" | ".join(msgs))
        if ok_any:
            self.set_device_status("Active", "blue")

    # -------------------- WEBSOCKET CALLBACKS --------------------

    def on_ws_open(self, ws, tag):
        self.set_protocol_status(f"WebSocket Connected ({tag})", "green")
        self.set_device_status("Connected", "green")
        self.connect_btn.config(state="disabled")
        self.disconnect_btn.config(state="normal")
        self.log_response(f"WebSocket {tag}: Connection established")

    def on_ws_message(self, ws, message, tag):
        try:
            data = json.loads(message)
            self.log_response(f"WebSocket {tag}: Received: {json.dumps(data, indent=2)}")
            self.last_response.set(f"WebSocket {tag}: Message received")
            self.set_device_status("Active", "blue")
        except json.JSONDecodeError:
            self.log_response(f"WebSocket {tag}: Received non-JSON: {message}")

    def on_ws_error(self, ws, error, tag):
        self.log_response(f"WebSocket {tag} Error: {error}")
        self.set_protocol_status(f"WebSocket {tag} Error", "orange")
        self.set_device_status("Error", "orange")

    def on_ws_close(self, ws, close_status_code, close_msg, tag):
        self.log_response(f"WebSocket {tag}: Connection closed")
        self.set_protocol_status("Disconnected", "red")
        self.set_device_status("Disconnected", "red")
        self.connect_btn.config(state="normal")
        self.disconnect_btn.config(state="disabled")

    # -------------------- MONITORING / SERIAL --------------------

    def log_response(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.response_text.insert(tk.END, log_entry)
        self.response_text.see(tk.END)
        # Trim
        if self.response_text.index(tk.END).split('.')[0] > '1000':
            self.response_text.delete('1.0', '100.0')

    def clear_log(self):
        self.response_text.delete('1.0', tk.END)

    def refresh_status(self):
        self.log_response("Status refresh requested")

    def start_monitoring(self):
        def monitor():
            while True:
                try:
                    while not self.response_queue.empty():
                        response = self.response_queue.get_nowait()
                        self.root.after(0, self.log_response, response)
                    time.sleep(0.1)
                except Exception as e:
                    print(f"Monitoring error: {e}")
                    time.sleep(1)
        threading.Thread(target=monitor, daemon=True).start()

    def send_serial_command(self, event=None):
        command = self.serial_input.get()
        if command.strip():
            self.log_response(f"Serial Command Sent: {command}")
            # (stub) simulate response
            response = f"Device acknowledged: {command}"
            self.serial_output.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] {response}\n")
            self.serial_output.see(tk.END)
            self.serial_input.delete(0, tk.END)
            if self.serial_output.index(tk.END).split('.')[0] > '500':
                self.serial_output.delete('1.0', '100.0')

    def clear_serial(self):
        self.serial_output.delete('1.0', tk.END)


def main():
    root = tk.Tk()
    app = PathLinkGUI(root)

    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    root.mainloop()


if __name__ == "__main__":
    main()
