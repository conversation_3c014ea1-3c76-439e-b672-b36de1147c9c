import { streamText } from 'ai';
import { NextRequest } from 'next/server';
import { openai, MODEL_SETTINGS } from '@/lib/openrouter';
import { searchKnowledge, buildRagContext } from '@/lib/rag';
import { getLiveWebsiteContext } from '@/lib/data-ingestion/live-data-fetcher';
import { createContextClient } from '@/lib/supabase/server';

// System prompt for customer chatbot
const CUSTOMER_SYSTEM_PROMPT = `You are PathLink AI, an intelligent AI assistant specifically created for <PERSON><PERSON>'s car rental services. You are the friendly digital assistant that represents PathLink's commitment to exceptional customer service, helping customers navigate their car rental journey with ease and confidence.

IDENTITY & INTRODUCTION:
When users first interact with you, introduce yourself as: "Hi! I'm PathLink AI 🤖✨, your personal car rental assistant built specifically for <PERSON><PERSON>'s car rental services! I'm here to make your car rental experience smooth and hassle-free. Whether you need help with vehicle bookings, pricing information, rental policies, or have questions about our fleet, I've got you covered! I have access to real-time availability, pricing, and can guide you through every step of the rental process. What can I help you with today? 🚗😊"

IMPORTANT CONTENT POLICY:
- ONLY respond to queries related to car rental services, vehicle bookings, payments, policies, pricing, availability, and PathLink customer support
- REJECT personal questions, emotional support, relationship advice, general life advice, or unrelated topics
- If asked about off-topic subjects, politely redirect to PathLink-related assistance
- Example rejection response: "I'm here to help with PathLink car rental services! Please ask about vehicle bookings, pricing, policies, or our rental process. 🚗😊"

Key Guidelines:
- Be friendly, helpful, and professional 😊
- Focus on car rental services, bookings, and customer support
- If you don't have specific information, acknowledge this and suggest contacting support
- Always prioritize customer safety and satisfaction 🛡️
- Provide clear, concise answers
- Guide customers through booking processes when needed 🚗✨
- Use appropriate emojis to make responses more engaging and helpful
- All currency amounts must be displayed in Philippine Peso (₱) format

PathLink Services:
- Car rental services with various vehicle categories 🚗🚙🚐
- Online booking system 💻📱
- Document verification process 📄✅
- Payment processing 💳💰
- Customer support 🤝📞

Currency Format:
- Always use Philippine Peso symbol (₱) instead of dollar sign ($)
- Example: ₱1,800/day instead of $1800/day
- Format large amounts with commas: ₱15,000, ₱125,500

Response Style:
- Use relevant emojis to enhance clarity and engagement
- Vehicle mentions: 🚗 🚙 🚐 🏎️
- Money/pricing: ₱ 💰 💳 💵
- Booking process: 📅 📋 ✅ 📄
- Support/help: 🤝 💬 📞 ❓
- Safety/security: 🛡️ 🔒 ✅
- Use plain text format without markdown symbols (no ### headers, no **bold** markers)
- List vehicle information in numbered format for clear presentation

If customers need human assistance, direct them to contact support or use the admin contact options 📞💬.`;

export async function POST(request: NextRequest) {
  try {
    const supabase = await createContextClient('customer');
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { messages, sessionId } = await request.json();
    
    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid messages format', { status: 400 });
    }

    // Get the latest user message for RAG search
    const userMessage = messages[messages.length - 1];
    if (!userMessage || userMessage.role !== 'user') {
      return new Response('No valid user message found', { status: 400 });
    }

    // Handle multiple possible content formats from AI SDK
    let messageContent = '';
    
    if (typeof userMessage.content === 'string') {
      messageContent = userMessage.content.trim();
    } else if (Array.isArray(userMessage.content)) {
      // Handle array format where content is an array of parts
      const textParts = userMessage.content.filter((part: any) => part.type === 'text');
      messageContent = textParts.map((part: any) => part.text).join(' ').trim();
    } else if (userMessage.text) {
      messageContent = userMessage.text.trim();
    }
    
    if (!messageContent) {
      return new Response('Empty message content', { status: 400 });
    }

    // Get live website context
    const liveContext = await getLiveWebsiteContext(messageContent, 'customer', user.id);

    // Search knowledge base for relevant context
    const knowledgeResults = await searchKnowledge(
      messageContent,
      'customer',
      {
        matchThreshold: 0.6,
        matchCount: 5,
        filterSource: undefined // Allow broader search for customer
      }
    );

    // Build comprehensive RAG context
    const ragContext = buildRagContext(
      messageContent,
      knowledgeResults,
      'customer'
    );

    // Combine static knowledge and live data
    const combinedContext = `
LIVE WEBSITE DATA:
${liveContext}

${ragContext}
    `.trim();

    // Create conversation if needed
    if (sessionId && messageContent) {
      await supabase
        .from('chatbot_conversations')
        .upsert({
          user_id: user.id,
          user_type: 'customer',
          session_id: sessionId,
          title: messageContent.substring(0, Math.min(100, messageContent.length))
        }, { onConflict: 'session_id' });
    }

    // Prepare messages with enhanced system prompt and combined context
    const systemMessage = {
      role: 'system' as const,
      content: `${CUSTOMER_SYSTEM_PROMPT}\n\n${combinedContext}`
    };

    const conversationMessages = [systemMessage, ...messages];

    // Generate streaming response
    const result = await streamText({
      model: openai(MODEL_SETTINGS.model),
      messages: conversationMessages,
      temperature: MODEL_SETTINGS.temperature,
      frequencyPenalty: MODEL_SETTINGS.frequencyPenalty,
      presencePenalty: MODEL_SETTINGS.presencePenalty,
    });

    return result.toTextStreamResponse();
  } catch (error) {
    console.error('Customer chat error:', error);
    return new Response('Internal server error', { status: 500 });
  }
}
