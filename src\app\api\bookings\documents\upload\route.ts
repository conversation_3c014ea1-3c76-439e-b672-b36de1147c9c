import { createContextClient } from "@/lib/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const supabase = await createContextClient('customer');
    const body = await request.json();
    
    const { bookingId, documentType, fileUrl, fileName, fileSize, fileType } = body;

    if (!bookingId || !documentType || !fileUrl || !fileName) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user owns this booking
    const { data: booking } = await supabase
      .from("bookings")
      .select("customer_id, status")
      .eq("id", bookingId)
      .single();

    if (!booking) {
      return NextResponse.json(
        { error: "Booking not found" },
        { status: 404 }
      );
    }

    if (booking.customer_id !== user.id) {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      );
    }

    // Check if document already exists for this booking and type
    const { data: existingDoc } = await supabase
      .from("booking_documents")
      .select("id")
      .eq("booking_id", bookingId)
      .eq("document_type", documentType)
      .single();

    if (existingDoc) {
      // Update existing document
      const { data, error } = await supabase
        .from("booking_documents")
        .update({
          file_url: fileUrl,
          file_name: fileName,
          file_size: fileSize,
          file_type: fileType,
          verification_status: "pending",
          verification_notes: null,
          verified_by: null,
          verified_at: null,
          updated_at: new Date().toISOString(),
        })
        .eq("id", existingDoc.id)
        .select()
        .single();

      if (error) {
        console.error("Error updating document:", error);
        return NextResponse.json(
          { error: "Failed to update document" },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: "Document updated successfully",
        document: data,
      });
    } else {
      // Create new document
      const { data, error } = await supabase
        .from("booking_documents")
        .insert({
          booking_id: bookingId,
          document_type: documentType,
          file_url: fileUrl,
          file_name: fileName,
          file_size: fileSize,
          file_type: fileType,
          verification_status: "pending",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error("Error creating document:", error);
        return NextResponse.json(
          { error: "Failed to create document" },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: "Document uploaded successfully",
        document: data,
      });
    }
  } catch (error) {
    console.error("Document upload API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
