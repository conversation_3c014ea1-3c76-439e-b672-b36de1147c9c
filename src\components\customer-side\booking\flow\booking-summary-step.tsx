"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FixedPickupLocationField,
  DropOffLocationDropdown,
  FIXED_PICKUP_LOCATION,
  PickupLocationField,
  DropOffLocationField,
} from "@/components/ui/booking-location-components";
import {
  PickupGarageCheckbox,
  DropoffSameAsPickupCheckbox,
} from "@/components/ui/booking-location-checkboxes";
import { DateTimePicker } from "@/components/ui/datetime-picker";
import {
  Calendar,
  Clock,
  MapPin,
  Car,
  Edit2,
  Users,
  Fuel,
  Cog,
  ArrowLeft,
} from "lucide-react";
import { formatCurrency } from "@/lib/date-utils";
import type { BookingData } from "./booking-flow";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { CUSTOMER_PATHS } from "@/lib/customer-paths";
import { SPECIAL_SERVICES } from "@/lib/pricing-constants";
import { DayNightSelector } from "@/components/ui/day-night-selector";
import { calculateDeliveryFees } from "@/lib/delivery-fee-utils";
import { getTimePeriod, type TimePeriod } from "@/lib/delivery-fee-constants";
import { 
  calculateDayOnlyRental, 
  formatRentalDuration,
  formatRentalDurationWithDateRange,
  validateBookingDateTime,
  getMinimumDate,
  getMinimumDropoffDate,
  calculateDayBasedCost
} from "@/utils/booking-date-validation";
import { calculateBookingTotal, getBookingCostBreakdown } from "@/utils/booking-total-calculation";

interface BookingSummaryStepProps {
  bookingData: BookingData;
  onUpdate: (updates: Partial<BookingData>) => void;
}

export function BookingSummaryStep({
  bookingData,
  onUpdate,
}: BookingSummaryStepProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const router = useRouter();

  const { selectedCar } = bookingData;

  // Handle pickup garage checkbox change
  const handlePickupGarageChange = (checked: boolean) => {
    onUpdate({ pickupGarageChecked: checked });
    if (checked) {
      onUpdate({ pickUpLocation: "#9 Lubnac, Vintar, Ilocos Norte" });
    } else {
      onUpdate({ pickUpLocation: "" });
    }
  };

  // Handle drop-off same as pickup checkbox change
  const handleDropoffSameAsPickupChange = (checked: boolean) => {
    onUpdate({ dropoffSameAsPickupChecked: checked });
    if (checked && bookingData.pickUpLocation) {
      onUpdate({ dropOffLocation: bookingData.pickUpLocation });
    } else if (!checked) {
      onUpdate({ dropOffLocation: "" });
    }
  };

  // Update drop-off location when pickup location changes and "same as pickup" is checked
  // Using useRef to store the latest values without triggering effects
  const bookingDataRef = React.useRef(bookingData);
  bookingDataRef.current = bookingData;
  
  // Store the onUpdate function in a ref to avoid dependency issues
  const onUpdateRef = React.useRef(onUpdate);
  onUpdateRef.current = onUpdate;
  
  React.useEffect(() => {
    // Only run the effect when these specific dependencies change
    if (bookingData.dropoffSameAsPickupChecked && bookingData.pickUpLocation) {
      // Use the ref's current value to avoid the dependency loop
      onUpdateRef.current({ dropOffLocation: bookingData.pickUpLocation });
    }
  }, [bookingData.pickUpLocation, bookingData.dropoffSameAsPickupChecked]);

  const handleChangeVehicle = () => {
    // Navigate back to catalog
    router.push(CUSTOMER_PATHS.CATALOG);
  };

  // Auto-detect time period from time inputs and calculate delivery fees
  React.useEffect(() => {
    if (bookingData.pickUpTime && bookingData.dropOffTime) {
      const detectedPickupPeriod = getTimePeriod(bookingData.pickUpTime);
      const detectedReturnPeriod = getTimePeriod(bookingData.dropOffTime);
      
      // Update time periods if they're different from detected ones
      if (detectedPickupPeriod !== bookingData.pickupTimePeriod || 
          detectedReturnPeriod !== bookingData.returnTimePeriod) {
        onUpdate({
          pickupTimePeriod: detectedPickupPeriod,
          returnTimePeriod: detectedReturnPeriod,
        });
      }
    }
  }, [bookingData.pickUpTime, bookingData.dropOffTime, bookingData.pickupTimePeriod, bookingData.returnTimePeriod, onUpdate]);

  // Calculate delivery fees when locations or time periods change
  React.useEffect(() => {
    const deliveryFees = calculateDeliveryFees(
      bookingData.pickUpLocation,
      bookingData.dropOffLocation,
      bookingData.pickupTimePeriod,
      bookingData.returnTimePeriod
    );

    // Update delivery fees if they're different
    if (deliveryFees.pickupFee !== bookingData.deliveryFee || 
        deliveryFees.returnFee !== bookingData.returnFee ||
        deliveryFees.totalFee !== bookingData.totalDeliveryFees) {
      onUpdate({
        deliveryFee: deliveryFees.pickupFee,
        returnFee: deliveryFees.returnFee,
        totalDeliveryFees: deliveryFees.totalFee,
      });
    }
  }, [
    bookingData.pickUpLocation,
    bookingData.dropOffLocation,
    bookingData.pickupTimePeriod,
    bookingData.returnTimePeriod,
    bookingData.deliveryFee,
    bookingData.returnFee,
    bookingData.totalDeliveryFees,
    onUpdate
  ]);

  // Handle Day/Night selector changes with auto-fill functionality
  const handlePickupTimePeriodChange = (period: TimePeriod) => {
    onUpdate({ pickupTimePeriod: period });
  };

  const handleReturnTimePeriodChange = (period: TimePeriod) => {
    onUpdate({ returnTimePeriod: period });
  };

  // Handle auto-fill time when Day/Night buttons are clicked
  const handlePickupTimeAutoFill = (time: string) => {
    onUpdate({ pickUpTime: time });
  };

  const handleReturnTimeAutoFill = (time: string) => {
    onUpdate({ dropOffTime: time });
  };

  // Special service booking doesn't require a car selection
  if (!selectedCar && !bookingData.isSpecialService) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <Car className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No car selected
          </h3>
          <p className="text-gray-600 mb-4">
            Please select a vehicle from our catalog first.
          </p>
          <Button
            onClick={handleChangeVehicle}
            variant="secondary"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Go to Catalog
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Use centralized calculation utility for consistent totals
  const bookingTotals = calculateBookingTotal(bookingData, selectedCar);
  const costBreakdown = getBookingCostBreakdown(bookingData, selectedCar);

  const formatDateTime = (date: string, time: string) => {
    if (!date || !time) return "Not selected";
    const dateTime = new Date(`${date}T${time}:00`);
    return dateTime.toLocaleString("en-US", {
      weekday: "short",
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getShortFuelType = (fuelType: string | undefined) => {
    if (!fuelType) return "Unknown";
    if (fuelType.includes("Gas/Premium")) return "Gas";
    if (fuelType.includes("Diesel")) return "Diesel";
    if (fuelType.includes("Unleaded")) return "Unleaded";
    return fuelType.split("/")[0];
  };

  // Remove this line as we now use bookingTotals and costBreakdown

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Selected Vehicle or Special Service Summary */}
      <Card className="overflow-hidden">
        <CardHeader className="p-4 sm:p-6">
          <CardTitle className="text-lg sm:text-xl break-words">
            {bookingData.isSpecialService ? "Special Service" : "Selected Vehicle"}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4 sm:p-6 pt-0">
          {bookingData.isSpecialService ? (
            <div className="flex flex-col gap-4">
              <div className="relative w-full h-40 sm:h-48 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg overflow-hidden">
                <Image
                  src="/images/van-with-driver.jpg"
                  alt="Van Rental with Driver"
                  fill
                  sizes="(max-width: 1024px) 100vw, 256px"
                  style={{ objectFit: "contain" }}
                  className="transition-transform duration-200"
                />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 break-words">
                  Van Rental with Driver
                </h3>
                <p className="text-sm sm:text-base text-gray-600">
                  Premium service with professional driver
                </p>
              </div>
              
              {/* Special Service Pricing */}
              <div className="bg-blue-50 rounded-lg p-3 sm:p-4 mt-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Service Fee</span>
                    <span className="font-medium text-sm sm:text-base">
                      {SPECIAL_SERVICES.VAN_WITH_DRIVER.CURRENCY}{SPECIAL_SERVICES.VAN_WITH_DRIVER.BASE_PRICE.toFixed(2)}
                    </span>
                  </div>
                  <div className="border-t border-blue-200 pt-2">
                    <div className="flex justify-between items-center">
                      <span className="font-semibold text-gray-900 text-sm sm:text-base">
                        Total Amount
                      </span>
                      <span className="text-lg sm:text-xl font-bold text-blue-600">
                        {SPECIAL_SERVICES.VAN_WITH_DRIVER.CURRENCY}{SPECIAL_SERVICES.VAN_WITH_DRIVER.BASE_PRICE.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <>
              {/* Mobile/Tablet Layout (< 1024px) - Original Design */}
              <div className="flex flex-col lg:hidden gap-4 sm:gap-6">
                {/* Car Image */}
                <div className="w-full flex-shrink-0">
                  <div className="relative w-full h-40 sm:h-48 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg overflow-hidden">
                    <Image
                      src={selectedCar?.image_url || "/placeholder.svg"}
                      alt={selectedCar?.model || "Vehicle"}
                      fill
                      sizes="(max-width: 1024px) 100vw, 256px"
                      style={{ objectFit: "contain" }}
                      className="transition-transform duration-200"
                    />
                  </div>
                </div>

                {/* Car Details */}
                <div className="flex-1 space-y-3 sm:space-y-4 min-w-0">
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                    <div className="min-w-0">
                      <h3 className="text-lg sm:text-xl font-semibold text-gray-900 break-words">
                        {selectedCar?.model || "Vehicle"}
                      </h3>
                      <p className="text-sm sm:text-base text-gray-600">
                        {selectedCar?.type || ""}
                      </p>
                    </div>
                    <Button
                      onClick={handleChangeVehicle}
                      variant="secondary"
                      size="sm"
                      className="flex items-center gap-2 w-full sm:w-auto flex-shrink-0"
                    >
                      <ArrowLeft className="h-4 w-4" />
                      Change Vehicle
                    </Button>
                  </div>
                </div>

                {/* Detailed Specifications */}
                <div className="grid grid-cols-2 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div className="flex items-center gap-2 min-w-0">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full flex-shrink-0">
                      <Users className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="min-w-0">
                      <div className="text-sm font-medium text-gray-900 break-words">
                        {selectedCar?.seats ?? 0} seats
                      </div>
                      <div className="text-xs text-gray-500">Seating</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 min-w-0">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full flex-shrink-0">
                      <Cog className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="min-w-0">
                      <div className="text-sm font-medium text-gray-900 break-words">
                        {selectedCar?.transmission ?? ""}
                      </div>
                      <div className="text-xs text-gray-500">Transmission</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 min-w-0">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full flex-shrink-0">
                      <Fuel className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="min-w-0">
                      <div className="text-sm font-medium text-gray-900 break-words">
                        {getShortFuelType(selectedCar?.fuel_type)}
                      </div>
                      <div className="text-xs text-gray-500">Fuel Type</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 min-w-0">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full flex-shrink-0">
                      <Fuel className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="min-w-0">
                      <div className="text-sm font-medium text-gray-900 break-words">
                        {selectedCar?.fuel_capacity ?? 0}L
                      </div>
                      <div className="text-xs text-gray-500">Fuel Capacity</div>
                    </div>
                  </div>
                </div>

                {/* Vehicle Status & Condition */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full flex-shrink-0"></div>
                    <span className="text-sm font-medium text-green-700">
                      Available
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full flex-shrink-0"></div>
                    <span className="text-sm text-gray-600">
                      Condition: {selectedCar?.condition ?? "Good"}
                    </span>
                  </div>
                </div>

                {/* Additional Notes */}
                {selectedCar?.notes && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <div className="text-sm font-medium text-yellow-800 mb-1">
                      Important Note:
                    </div>
                    <div className="text-sm text-yellow-700 break-words">
                      {selectedCar?.notes}
                    </div>
                  </div>
                )}

                {/* Pricing */}
                <div className="bg-blue-50 rounded-lg p-3 sm:p-4">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Daily Rate</span>
                      <span className="font-medium text-sm sm:text-base">
                        ₱{(selectedCar?.price_per_day ?? 0).toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">
                        Rental Duration (Day-Only)
                      </span>
                      <span className="font-medium text-sm sm:text-base">
                        {formatRentalDurationWithDateRange(bookingData.pickUpDate, bookingData.dropOffDate)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">
                        Subtotal ({bookingTotals.days} × ₱{(selectedCar?.price_per_day ?? 0).toFixed(2)})
                      </span>
                      <span className="font-medium text-sm sm:text-base">
                        ₱{bookingTotals.baseRentalCost.toFixed(2)}
                      </span>
                    </div>
                    <div className="border-t border-blue-200 pt-2">
                      <div className="flex justify-between items-center">
                        <span className="font-semibold text-gray-900 text-sm sm:text-base">
                          Total Amount
                        </span>
                        <span className="text-lg sm:text-xl font-bold text-blue-600">
                          ₱{bookingTotals.totalAmount.toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Desktop Layout (≥ 1024px) - Reference Design Style */}
              <div className="hidden lg:flex gap-6">
                {/* Left Column - Vehicle Image */}
                <div className="w-72 flex-shrink-0">
                  <div className="relative w-full h-48 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg overflow-hidden mb-4">
                    <Image
                      src={selectedCar?.image_url || "/placeholder.svg"}
                      alt={selectedCar?.model || "Vehicle"}
                      fill
                      sizes="288px"
                      style={{ objectFit: "contain" }}
                      className="transition-transform duration-200"
                    />
                  </div>
                  <Button
                    onClick={handleChangeVehicle}
                    variant="secondary"
                    size="sm"
                    className="w-full flex items-center justify-center gap-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Change Vehicle
                  </Button>
                </div>

                {/* Middle Column - Vehicle Details */}
                <div className="flex-1 space-y-6">
                  {/* Vehicle Header */}
                  <div className="space-y-3">
                    <h3 className="text-2xl font-bold text-gray-900">
                      {selectedCar?.model || "Vehicle"}
                    </h3>
                    <p className="text-sm text-gray-600 font-medium">
                      {selectedCar?.type || ""} • {selectedCar?.seats ?? 0} seats
                    </p>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <div className="w-2.5 h-2.5 bg-green-500 rounded-full flex-shrink-0"></div>
                        <span className="text-xs font-medium text-green-700">
                          Available
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2.5 h-2.5 bg-blue-500 rounded-full flex-shrink-0"></div>
                        <span className="text-xs text-gray-600">
                          Condition: {selectedCar?.condition ?? "Good"}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Vehicle Specifications */}
                  <div className="grid grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-full mx-auto mb-3">
                        <Cog className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        {selectedCar?.transmission ?? ""}
                      </div>
                      <div className="text-xs text-gray-500">Transmission</div>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-full mx-auto mb-3">
                        <Fuel className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        {getShortFuelType(selectedCar?.fuel_type)}
                      </div>
                      <div className="text-xs text-gray-500">Fuel Type</div>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-full mx-auto mb-3">
                        <Fuel className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        {selectedCar?.fuel_capacity ?? 0}L
                      </div>
                      <div className="text-xs text-gray-500">Fuel Capacity</div>
                    </div>
                  </div>


                  {/* Additional Notes */}
                  {selectedCar?.notes && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="text-sm font-medium text-yellow-800 mb-2">
                        Important Note:
                      </div>
                      <div className="text-sm text-yellow-700">
                        {selectedCar?.notes}
                      </div>
                    </div>
                  )}
                </div>

                {/* Right Column - Pricing Information */}
                <div className="w-80 flex-shrink-0">
                  <div className="text-right mb-6">
                    <div className="text-4xl font-bold text-gray-900 mb-1">
                      ₱{Math.floor(bookingTotals.totalAmount).toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600 mb-4">
                      Total Cost
                    </div>
                  </div>
                  
                  <div className="space-y-4 border-t border-gray-200 pt-6">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Daily Rate</span>
                      <span className="text-sm font-medium text-gray-900">
                        ₱{(selectedCar?.price_per_day ?? 0).toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Rental Duration (Day-Only)</span>
                      <span className="text-sm font-medium text-gray-900">
                        {formatRentalDurationWithDateRange(bookingData.pickUpDate, bookingData.dropOffDate)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">
                        Subtotal ({bookingTotals.days} × ₱{(selectedCar?.price_per_day ?? 0).toFixed(2)})
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        ₱{bookingTotals.baseRentalCost.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Booking Details */}
      <Card className="overflow-hidden">
        <CardHeader className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
            <CardTitle className="text-lg sm:text-xl break-words">
              Booking Details
            </CardTitle>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setIsEditing(!isEditing)}
              className="flex items-center gap-2 w-full sm:w-auto"
            >
              <Edit2 className="h-4 w-4" />
              {isEditing ? "Save" : "Edit"}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-4 sm:p-6 pt-0 space-y-4 sm:space-y-6">
          {isEditing ? (
            <div className="space-y-4 sm:space-y-6">
              {/* Location Details Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-blue-600" />
                  Location Details
                </h3>
                
                {/* Pickup Location with Checkbox */}
                <div className="space-y-3">
                  <PickupLocationField
                    value={bookingData.pickUpLocation}
                    onValueChange={(value: string) =>
                      onUpdate({ pickUpLocation: value })
                    }
                    placeholder="Select pickup location"
                    showLabel={true}
                    className="w-full"
                  />
                  <PickupGarageCheckbox
                    checked={bookingData.pickupGarageChecked}
                    onCheckedChange={handlePickupGarageChange}
                    className="w-full"
                  />
                </div>

                {/* Drop-off Location with Checkbox */}
                <div className="space-y-3">
                  <DropOffLocationField
                    value={bookingData.dropOffLocation}
                    onValueChange={(value: string) =>
                      onUpdate({ dropOffLocation: value })
                    }
                    placeholder="Select drop-off location"
                    showLabel={true}
                    className="w-full"
                  />
                  <DropoffSameAsPickupCheckbox
                    checked={bookingData.dropoffSameAsPickupChecked}
                    onCheckedChange={handleDropoffSameAsPickupChange}
                    pickupLocation={bookingData.pickUpLocation}
                    className="w-full"
                  />
                </div>
              </div>

              {/* Date and Time Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  Rental Schedule
                </h3>
                <div className="space-y-4">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800">Day-Only Rental</span>
                    </div>
                    <p className="text-xs text-green-700">
                      Rentals are charged per day only. Times are for pickup/delivery scheduling and don't affect rental fees.
                    </p>
                  </div>
                  <DateTimePicker
                    pickupDate={bookingData.pickUpDate}
                    pickupTime={bookingData.pickUpTime}
                    dropoffDate={bookingData.dropOffDate}
                    dropoffTime={bookingData.dropOffTime}
                    onPickupDateChange={(date) => {
                      // Validate and update pickup date
                      const validation = validateBookingDateTime(date, bookingData.pickUpTime, bookingData.dropOffDate, bookingData.dropOffTime);
                      onUpdate({ pickUpDate: date });
                    }}
                    onPickupTimeChange={(time) => onUpdate({ pickUpTime: time })}
                    onDropoffDateChange={(date) => {
                      // Validate and update dropoff date
                      const validation = validateBookingDateTime(bookingData.pickUpDate, bookingData.pickUpTime, date, bookingData.dropOffTime);
                      onUpdate({ dropOffDate: date });
                    }}
                    onDropoffTimeChange={(time) => onUpdate({ dropOffTime: time })}
                  />
                </div>
              </div>

              {/* Day/Night Selection Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Clock className="h-5 w-5 text-blue-600" />
                  Delivery Time Categories
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Pickup Time Category */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Pickup Time Category
                    </Label>
                    <DayNightSelector
                      value={bookingData.pickupTimePeriod}
                      onChange={handlePickupTimePeriodChange}
                      onTimeAutoFill={handlePickupTimeAutoFill}
                      currentTime={bookingData.pickUpTime}
                      size="sm"
                      showTimeRanges={true}
                      enableAutoFill={true}
                    />
                    {bookingData.deliveryFee > 0 && (
                      <p className="text-xs text-gray-600">
                        Delivery fee: <span className="font-medium">₱{bookingData.deliveryFee.toFixed(2)}</span>
                      </p>
                    )}
                    {bookingData.deliveryFee === 0 && (
                      <p className="text-xs text-green-600 font-medium">
                        Free delivery (Garage/Office location)
                      </p>
                    )}
                  </div>

                  {/* Return Time Category */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium text-gray-700">
                      Return Time Category
                    </Label>
                    <DayNightSelector
                      value={bookingData.returnTimePeriod}
                      onChange={handleReturnTimePeriodChange}
                      onTimeAutoFill={handleReturnTimeAutoFill}
                      currentTime={bookingData.dropOffTime}
                      size="sm"
                      showTimeRanges={true}
                      enableAutoFill={true}
                    />
                    {bookingData.returnFee > 0 && (
                      <p className="text-xs text-gray-600">
                        Return fee: <span className="font-medium">₱{bookingData.returnFee.toFixed(2)}</span>
                      </p>
                    )}
                    {bookingData.returnFee === 0 && (
                      <p className="text-xs text-green-600 font-medium">
                        Free return (Garage/Office location)
                      </p>
                    )}
                  </div>
                </div>

                {/* Total Delivery Fees Summary */}
                {bookingData.totalDeliveryFees > 0 && (
                  <div className="bg-blue-50 rounded-lg p-3 mt-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-700">
                        Total Delivery Fees
                      </span>
                      <span className="text-sm font-bold text-blue-600">
                        ₱{bookingData.totalDeliveryFees.toFixed(2)}
                      </span>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">
                      Pickup: ₱{bookingData.deliveryFee.toFixed(2)} + Return: ₱{bookingData.returnFee.toFixed(2)}
                    </p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-4 sm:space-y-6">
              {/* Read-only view */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                {/* Pick-up Details */}
                <div className="bg-green-50 rounded-lg p-3 sm:p-4">
                  <div className="flex items-start gap-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full mt-0.5 flex-shrink-0">
                      <MapPin className="h-4 w-4 text-green-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 text-sm sm:text-base">
                        Pick-up Location
                      </div>
                      <div className="text-sm text-gray-600 break-words">
                        {bookingData.pickUpLocation || "Not specified"}
                      </div>
                      <div className="flex items-center gap-2 mt-1 text-xs sm:text-sm text-gray-500">
                        <Calendar className="h-3 w-3 flex-shrink-0" />
                        <span className="break-words">
                          {formatDateTime(
                            bookingData.pickUpDate,
                            bookingData.pickUpTime
                          )}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
                        <Clock className="h-3 w-3 flex-shrink-0" />
                        <span className="capitalize">{bookingData.pickupTimePeriod} time</span>
                        {bookingData.deliveryFee > 0 ? (
                          <span className="text-green-600 font-medium">
                            (₱{bookingData.deliveryFee.toFixed(2)} delivery)
                          </span>
                        ) : (
                          <span className="text-green-600 font-medium">(Free delivery)</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Drop-off Details */}
                <div className="bg-orange-50 rounded-lg p-3 sm:p-4">
                  <div className="flex items-start gap-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full mt-0.5 flex-shrink-0">
                      <MapPin className="h-4 w-4 text-orange-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 text-sm sm:text-base">
                        Drop-off Location
                      </div>
                      <div className="text-sm text-gray-600 break-words">
                        {bookingData.dropOffLocation || "Not specified"}
                      </div>
                      <div className="flex items-center gap-2 mt-1 text-xs sm:text-sm text-gray-500">
                        <Calendar className="h-3 w-3 flex-shrink-0" />
                        <span className="break-words">
                          {formatDateTime(
                            bookingData.dropOffDate,
                            bookingData.dropOffTime
                          )}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
                        <Clock className="h-3 w-3 flex-shrink-0" />
                        <span className="capitalize">{bookingData.returnTimePeriod} time</span>
                        {bookingData.returnFee > 0 ? (
                          <span className="text-orange-600 font-medium">
                            (₱{bookingData.returnFee.toFixed(2)} return)
                          </span>
                        ) : (
                          <span className="text-orange-600 font-medium">(Free return)</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

    </div>
  );
}
