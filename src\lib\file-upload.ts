import { createClient } from "@/lib/supabase/client";

export interface FileUploadResult {
  url: string | null;
  error: string | null;
}

/**
 * Upload file to Supabase Storage
 * @param file - The file to upload
 * @param bucket - The storage bucket name
 * @param folder - Optional folder path within the bucket
 * @param allowedTypes - Array of allowed MIME types
 * @returns Promise with the public URL or error
 */
export async function uploadFile(
  file: File,
  bucket: string = "car-images",
  folder: string = "cars",
  allowedTypes: string[] = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/webp",
    "image/gif",
  ]
): Promise<FileUploadResult> {
  try {
    const supabase = createClient();

    // Validate file type
    if (!allowedTypes.includes(file.type)) {
      const typeNames = allowedTypes
        .map((type) => {
          if (type.startsWith("image/")) return "Image";
          if (type === "application/pdf") return "PDF";
          return "Document";
        })
        .filter((value, index, self) => self.indexOf(value) === index);

      return {
        url: null,
        error: `Please select a valid file type (${typeNames.join(", ")})`,
      };
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      return {
        url: null,
        error: "File size must be less than 5MB",
      };
    }

    // Generate unique filename
    const fileExt = file.name.split(".").pop();
    const fileName = `${folder}/${Date.now()}-${Math.random()
      .toString(36)
      .substring(2)}.${fileExt}`;

    // Upload file
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(fileName, file, {
        cacheControl: "3600",
        upsert: false,
      });

    if (error) {
      console.error("Storage upload error:", error);
      return {
        url: null,
        error: "Failed to upload file. Please try again.",
      };
    }

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from(bucket).getPublicUrl(data.path);

    return {
      url: publicUrl,
      error: null,
    };
  } catch (error) {
    console.error("Upload error:", error);
    return {
      url: null,
      error: "An unexpected error occurred during upload",
    };
  }
}

/**
 * Delete file from Supabase Storage
 * @param filePath - The path of the file to delete
 * @param bucket - The storage bucket name
 * @returns Promise with success/error status
 */
export async function deleteFile(
  filePath: string,
  bucket: string = "car-images"
): Promise<{ success: boolean; error: string | null }> {
  try {
    const supabase = createClient();

    // Extract path from URL if full URL is provided
    const path = filePath.includes("/storage/v1/object/public/")
      ? filePath.split("/storage/v1/object/public/" + bucket + "/")[1]
      : filePath;

    const { error } = await supabase.storage.from(bucket).remove([path]);

    if (error) {
      console.error("Storage delete error:", error);
      return {
        success: false,
        error: "Failed to delete file",
      };
    }

    return {
      success: true,
      error: null,
    };
  } catch (error) {
    console.error("Delete error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during deletion",
    };
  }
}

/**
 * Upload document file (supports images and PDFs)
 * @param file - The file to upload
 * @param folder - Document category folder
 * @returns Promise with the public URL or error
 */
export async function uploadDocument(
  file: File,
  folder: string = "requirements"
): Promise<FileUploadResult> {
  const allowedTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/webp",
    "application/pdf",
  ];

  return uploadFile(file, "customer-documents", folder, allowedTypes);
}

/**
 * Upload payment proof file with standardized naming
 * @param file - The file to upload
 * @param userId - Customer's user ID
 * @param bookingId - Associated booking ID (optional)
 * @returns Promise with the public URL or error
 */
export async function uploadPaymentProof(
  file: File,
  userId: string,
  bookingId?: string
): Promise<FileUploadResult> {
  try {
    const supabase = createClient();

    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/webp",
      "application/pdf",
    ];

    // Validate file type
    if (!allowedTypes.includes(file.type)) {
      const typeNames = allowedTypes
        .map((type) => {
          if (type.startsWith("image/")) return "Image";
          if (type === "application/pdf") return "PDF";
          return "Document";
        })
        .filter((value, index, self) => self.indexOf(value) === index);

      return {
        url: null,
        error: `Please select a valid file type (${typeNames.join(", ")})`,
      };
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      return {
        url: null,
        error: "File size must be less than 5MB",
      };
    }

    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { 
        url: null, 
        error: "User not authenticated" 
      };
    }

    // Generate standardized filename with user ID and booking ID
    const timestamp = Date.now();
    const fileExt = file.name.split('.').pop()?.toLowerCase();
    const bookingPart = bookingId ? `_${bookingId}` : '';
    const filename = `${userId}${bookingPart}_${timestamp}.${fileExt}`;

    // Upload to payments folder within customer-documents bucket
    const folderPath = `payments/${filename}`;

    const { data, error } = await supabase.storage
      .from("customer-documents")
      .upload(folderPath, file, {
        cacheControl: "3600",
        upsert: false,
      });

    if (error) {
      console.error("Payment file upload error:", error);
      return { 
        url: null, 
        error: error.message 
      };
    }

    if (!data?.path) {
      return { 
        url: null, 
        error: "Upload failed - no file path returned" 
      };
    }

    // Get public URL for the uploaded file
    const { data: urlData } = supabase.storage
      .from("customer-documents")
      .getPublicUrl(data.path);

    return { 
      url: urlData.publicUrl, 
      error: null 
    };
  } catch (error) {
    console.error("Payment upload error:", error);
    return { 
      url: null,
      error: error instanceof Error ? error.message : "Unknown upload error" 
    };
  }
}
