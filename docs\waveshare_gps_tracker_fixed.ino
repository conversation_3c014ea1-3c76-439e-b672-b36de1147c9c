/*
 * Waveshare ESP32-S3-A7670E-4G GPS Tracker - Fixed Version
 * Based on official Waveshare documentation and wiki
 * 
 * Hardware Setup:
 * - DIP Switch Settings: 4G=ON, USB=OFF  
 * - GNSS Antenna connected to IPEX1 connector
 * - SIM card inserted and activated
 * 
 * Key Fixes:
 * - Corrected UART pin configuration (RX=18, TX=17)
 * - Added proper A7670E initialization sequence
 * - Fixed GNSS enable commands (AT+CGNSSPWR=1, AT+CGNSSTST=1)
 * - Improved error handling and diagnostics
 * - Added WiFi credential validation
 */

#include <TinyGPS++.h>
#include <ArduinoJson.h>
#include <Arduino.h>
#include <PubSubClient.h>
#include <WiFi.h>
#include <Wire.h>

#define MSG_BUFFER_SIZE (50)

// ⚠️ UPDATE THESE CREDENTIALS BEFORE UPLOAD ⚠️
#define STASSID "YourWiFiNetwork"     // Replace with your WiFi SSID
#define STAPSK "YourWiFiPassword"     // Replace with your WiFi password

// Waveshare Cloud MQTT Configuration
#define MAX17048_I2C_ADDRESS 0x36
const char *clientID = "b4922afa"; // Client ID
char sub[] = "Sub/1149/37/b4922afa"; // Sub Topic
char pub[] = "Pub/1149/37/b4922afa"; // Pub Topic
const char *mqtt_server = "mqtt.waveshare.cloud";

// JSON and MQTT variables
StaticJsonDocument<400> sendJson;
StaticJsonDocument<400> readJson;
unsigned long lastUpdateTime = 0;
const char *ssid = STASSID;
const char *password = STAPSK;
char msg[MSG_BUFFER_SIZE];
WiFiClient espClient;
PubSubClient client(espClient);

const unsigned long updateInterval = 5000;

// ✅ CORRECTED: ESP32-S3-A7670E-4G UART Pin Configuration
// Based on Waveshare wiki: ESP32-S3 UART2 standard pins
static const int RXPin = 18;  // ESP32-S3 RX <- A7670E TX
static const int TXPin = 17;  // ESP32-S3 TX -> A7670E RX
static const uint32_t GPSBaud = 115200;  // A7670E standard baud rate

// A7670E Control Pins (based on wiki)
#define MODEM_PWRKEY_PIN 4    // Power control pin
#define MODEM_POWER_ON_PIN 33 // Module power enable

TinyGPSPlus gps;
String rev;
bool modemReady = false;
bool gnssEnabled = false;

// Function to send data to the A7670E module
void SentSerial(const char *p_char) {
  Serial.printf("Sending AT: %s\n", p_char);
  for (int i = 0; i < strlen(p_char); i++) {
    Serial1.write(p_char[i]);
    delay(5);
  }
  Serial1.write('\r');
  delay(5);
  Serial1.write('\n');
  delay(100); // Increased delay for A7670E response time
}

// Function to send a message and wait for a response with better error handling
bool SentMessage(const char *p_char, unsigned long timeout = 5000) {
  SentSerial(p_char);
  unsigned long start = millis();
  String response = "";
  
  while (millis() - start < timeout) {
    if (Serial1.available()) {
      char c = Serial1.read();
      response += c;
      
      // Check for complete response
      if (response.indexOf("OK") >= 0) {
        Serial.printf("✓ AT Response: %s\n", response.c_str());
        return true;
      }
      if (response.indexOf("ERROR") >= 0) {
        Serial.printf("✗ AT Error: %s\n", response.c_str());
        return false;
      }
      if (response.indexOf("+CGNSSPWR: READY!") >= 0) {
        Serial.println("✓ GNSS Power Ready!");
        return true;
      }
    }
    yield(); // Feed watchdog
  }
  Serial.printf("✗ AT Timeout for: %s\n", p_char);
  return false;
}

// Initialize A7670E module with proper power-on sequence
bool initializeA7670E() {
  Serial.println("\n=== Initializing A7670E Module ===");
  
  // Power control sequence
  pinMode(MODEM_POWER_ON_PIN, OUTPUT);
  pinMode(MODEM_PWRKEY_PIN, OUTPUT);
  
  Serial.println("Powering on A7670E module...");
  digitalWrite(MODEM_POWER_ON_PIN, HIGH);  // Enable power
  delay(1000);
  
  // PWRKEY pulse to turn on module
  digitalWrite(MODEM_PWRKEY_PIN, LOW);
  delay(1000);
  digitalWrite(MODEM_PWRKEY_PIN, HIGH);
  delay(3000); // Wait for module boot
  
  // Test AT communication with retries
  Serial.println("Testing AT communication...");
  int attempts = 0;
  while (attempts < 10) {
    if (SentMessage("AT", 3000)) {
      Serial.println("✓ A7670E responding to AT commands");
      modemReady = true;
      return true;
    }
    attempts++;
    Serial.printf("AT attempt %d/10 failed, retrying...\n", attempts);
    delay(2000);
  }
  
  Serial.println("✗ Failed to establish AT communication");
  return false;
}

// Enable GNSS with correct A7670E commands
bool enableGNSS() {
  if (!modemReady) {
    Serial.println("✗ Modem not ready, cannot enable GNSS");
    return false;
  }
  
  Serial.println("\n=== Enabling GNSS ===");
  
  // Enable GNSS power (Waveshare official command)
  Serial.println("Enabling GNSS power (AT+CGNSSPWR=1)...");
  if (!SentMessage("AT+CGNSSPWR=1", 10000)) {
    Serial.println("✗ Failed to enable GNSS power");
    return false;
  }
  
  delay(2000);
  
  // Enable GNSS data output
  Serial.println("Enabling GNSS data output (AT+CGNSSTST=1)...");
  if (!SentMessage("AT+CGNSSTST=1", 3000)) {
    Serial.println("✗ Failed to enable GNSS data output");
    return false;
  }
  
  // Set GNSS output port (if needed)
  Serial.println("Setting GNSS port switch...");
  SentMessage("AT+CGNSSPORTSWITCH=0,1", 3000); // Optional: may not be needed
  
  Serial.println("✓ GNSS enabled successfully");
  gnssEnabled = true;
  return true;
}

void setup() {
  Serial.begin(115200); // Increased baud for debugging
  
  Serial.println("\n==================================================");
  Serial.println("🛰️ Waveshare ESP32-S3-A7670E-4G GPS Tracker v2.0");
  Serial.println("==================================================");
  Serial.println("🔧 CRITICAL HARDWARE CHECKS:");
  Serial.println("1. DIP Switches: 4G=ON, USB=OFF");
  Serial.println("2. GNSS Antenna connected to IPEX1");
  Serial.println("3. SIM card inserted and activated");
  Serial.println("4. Outdoor location for GPS signal");
  Serial.println("==================================================\n");
  
  // Initialize I2C for battery monitoring (if available)
  Wire.begin(3, 2);
  
  // Initialize UART for A7670E communication
  Serial1.begin(GPSBaud, SERIAL_8N1, RXPin, TXPin);
  delay(1000);
  
  // Initialize A7670E module
  if (!initializeA7670E()) {
    Serial.println("⚠️ A7670E initialization failed!");
    Serial.println("Check hardware connections and DIP switches");
    // Continue anyway for debugging
  }
  
  // Enable GNSS if modem is ready
  if (modemReady) {
    enableGNSS();
  }
  
  // Initialize WiFi
  setup_wifi();
  
  // Initialize MQTT
  client.setServer(mqtt_server, 1883);
  client.setCallback(callback);
  
  Serial.println(F("\n🚀 GPS Tracker initialized!"));
  Serial.println(F("📊 GPS Data Output:"));
  Serial.println(F("Sats HDOP  Latitude   Longitude   Fix  Date        Time      Alt    Course Speed Battery"));
  Serial.println(F("----------------------------------------------------------------------------------------"));
}

void loop() {
  // Read battery level (if MAX17048 is available)
  float batteryLevel = readBatteryLevel();
  sendJson["data"]["batteryLevel"] = batteryLevel;
  
  // MQTT connection management
  if (!client.connected()) {
    reconnect();
  }
  client.loop();
  
  // Process GPS data from A7670E
  static const double LONDON_LAT = 51.508131, LONDON_LON = -0.128002;
  
  // Output GPS data in formatted table
  printInt(gps.satellites.value(), gps.satellites.isValid(), 5);
  printFloat(gps.hdop.hdop(), gps.hdop.isValid(), 6, 1);
  printFloat(gps.location.lat(), gps.location.isValid(), 11, 6);
  printFloat(gps.location.lng(), gps.location.isValid(), 12, 6);
  printInt(gps.location.age(), gps.location.isValid(), 5);
  printDateTime(gps.date, gps.time);
  printFloat(gps.altitude.meters(), gps.altitude.isValid(), 7, 2);
  printFloat(gps.course.deg(), gps.course.isValid(), 7, 2);
  printFloat(gps.speed.kmph(), gps.speed.isValid(), 6, 2);
  Serial.printf("%6.2fV", batteryLevel);
  
  // Calculate distance to London (example)
  if (gps.location.isValid()) {
    sendJson["data"]["Latitude"] = gps.location.lat();
    sendJson["data"]["Longitude"] = gps.location.lng();
    sendJson["data"]["altitude"] = gps.altitude.meters();
    sendJson["data"]["speed"] = gps.speed.kmph();
    sendJson["data"]["course"] = gps.course.deg();
    sendJson["data"]["satellites"] = gps.satellites.value();
    
    // Send data to Waveshare Cloud
    sendJsonData();
  }
  
  Serial.println();
  smartDelay(5000); // Update every 5 seconds
  
  // GPS health check
  if (millis() > 30000 && gps.charsProcessed() < 10) {
    Serial.println(F("⚠️ No GPS data received - check GNSS antenna and outdoor location"));
    
    // Try to re-enable GNSS if no data
    if (modemReady && !gnssEnabled) {
      Serial.println("Attempting to re-enable GNSS...");
      enableGNSS();
    }
  }
}

// Enhanced smartDelay with better GPS data processing
static void smartDelay(unsigned long ms) {
  unsigned long start = millis();
  do {
    while (Serial1.available()) {
      char c = Serial1.read();
      gps.encode(c);
      // Echo raw NMEA data for debugging (optional)
      // Serial.write(c);
    }
    yield();
  } while (millis() - start < ms);
}

// Read battery level from MAX17048 (if available)
float readBatteryLevel() {
  Wire.beginTransmission(MAX17048_I2C_ADDRESS);
  Wire.write(0x02);
  if (Wire.endTransmission() != 0) {
    return 0.0; // Battery monitor not available
  }

  Wire.requestFrom(MAX17048_I2C_ADDRESS, 2);
  if (Wire.available() >= 2) {
    uint16_t soc = (Wire.read() << 8) | Wire.read();
    if (soc > 65535) soc = 65535;
    return (float)soc / 65535.0 * 5.0; // Convert to voltage estimate
  }
  return 0.0;
}

// WiFi setup with credential validation
void setup_wifi() {
  // Check if credentials are updated
  if (String(ssid) == "YourWiFiNetwork" || String(password) == "YourWiFiPassword") {
    Serial.println("⚠️ WiFi credentials not updated!");
    Serial.println("Please update STASSID and STAPSK in code");
    return;
  }
  
  Serial.println();
  Serial.print("Connecting to WiFi: ");
  Serial.println(ssid);
  
  WiFi.mode(WIFI_STA);
  WiFi.begin(ssid, password);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\n✓ WiFi connected!");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
  } else {
    Serial.println("\n✗ WiFi connection failed");
  }
}

// MQTT reconnection
void reconnect() {
  while (!client.connected()) {
    Serial.print("Connecting to MQTT...");
    if (client.connect(clientID)) {
      Serial.println(" ✓ Connected");
      client.subscribe(sub);
    } else {
      Serial.printf(" ✗ Failed, rc=%d, retrying in 5s\n", client.state());
      delay(5000);
    }
  }
}

// Send JSON data to Waveshare Cloud
void sendJsonData() {
  if (client.connected()) {
    String pubres;
    serializeJson(sendJson, pubres);
    client.publish(pub, pubres.c_str());
    Serial.printf("📤 Data sent to cloud: %s\n", pubres.c_str());
  }
}

// MQTT callback for received messages
void callback(char *topic, byte *payload, unsigned int length) {
  String inputString;
  for (int i = 0; i < length; i++) {
    inputString += (char)payload[i];
  }
  Serial.printf("📥 MQTT received: %s\n", inputString.c_str());
  
  int jsonBeginAt = inputString.indexOf("{");
  int jsonEndAt = inputString.lastIndexOf("}");
  if (jsonBeginAt != -1 && jsonEndAt != -1) {
    inputString = inputString.substring(jsonBeginAt, jsonEndAt + 1);
    deserializeJson(readJson, inputString);
  }
}

// Utility functions for formatted output
static void printFloat(float val, bool valid, int len, int prec) {
  if (!valid) {
    while (len-- > 1) Serial.print('*');
    Serial.print(' ');
  } else {
    Serial.print(val, prec);
    int vi = abs((int)val);
    int flen = prec + (val < 0.0 ? 2 : 1);
    flen += vi >= 1000 ? 4 : vi >= 100 ? 3 : vi >= 10 ? 2 : 1;
    for (int i = flen; i < len; ++i) Serial.print(' ');
  }
  smartDelay(0);
}

static void printInt(unsigned long val, bool valid, int len) {
  char sz[32] = "*****************";
  if (valid) sprintf(sz, "%ld", val);
  sz[len] = 0;
  for (int i = strlen(sz); i < len; ++i) sz[i] = ' ';
  if (len > 0) sz[len - 1] = ' ';
  Serial.print(sz);
  smartDelay(0);
}

static void printDateTime(TinyGPSDate &d, TinyGPSTime &t) {
  if (!d.isValid()) {
    Serial.print(F("********** "));
  } else {
    char sz[32];
    sprintf(sz, "%02d/%02d/%02d ", d.month(), d.day(), d.year());
    Serial.print(sz);
  }

  if (!t.isValid()) {
    Serial.print(F("******** "));
  } else {
    char sz[32];
    sprintf(sz, "%02d:%02d:%02d ", t.hour(), t.minute(), t.second());
    Serial.print(sz);
  }

  printInt(d.age(), d.isValid(), 5);
  smartDelay(0);
}

static void printStr(const char *str, int len) {
  int slen = strlen(str);
  for (int i = 0; i < len; ++i)
    Serial.print(i < slen ? str[i] : ' ');
  smartDelay(0);
}
