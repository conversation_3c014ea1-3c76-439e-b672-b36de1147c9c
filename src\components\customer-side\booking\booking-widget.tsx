"use client";

import { MapPin, Calendar, Users, Car } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import { buildBookingFlowUrl } from "@/lib/customer-paths";
import { TimePicker } from "@/components/customer-side/time";
import { DatePicker } from "@/components/customer-side/date";
import {
  FixedPickupLocationField,
  DropOffLocationDropdown,
  PickupLocationField,
  DropOffLocationField,
} from "@/components/ui/booking-location-components";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { AuthRequiredModal } from "@/components/customer-side/auth/auth-required-modal";
import * as React from "react";

// Helper function for today's date
function getTodayDate(): string {
  const now = new Date();
  return now.toISOString().split("T")[0]; // YYYY-MM-DD format
}

export function BookingWidget() {
  const [pickUpLocation, setPickUpLocation] = React.useState("");
  const [dropOffLocation, setDropOffLocation] = React.useState("");
  const [pickUpDate, setPickUpDate] = React.useState("");
  const [pickUpTime, setPickUpTime] = React.useState("");
  const [dropOffDate, setDropOffDate] = React.useState("");
  const [dropOffTime, setDropOffTime] = React.useState("");
  const router = useRouter();
  const { user } = useCustomerAuth();
  const [showAuthModal, setShowAuthModal] = React.useState(false);

  function goSearch() {
    // Check if user is authenticated
    if (!user) {
      setShowAuthModal(true);
      return;
    }

    const pickUpDateTime = `${pickUpDate}T${pickUpTime}`;
    const dropOffDateTime = `${dropOffDate}T${dropOffTime}`;

    const bookingUrl = buildBookingFlowUrl({
      pickUpLocation,
      dropOffLocation,
      pickUpDateTime,
      dropOffDateTime,
    });
    router.push(bookingUrl);
  }

  return (
    <Card className="shadow-lg border-0 bg-white">
      <CardHeader className="pb-6">
        <CardTitle className="text-xl font-semibold text-gray-900">
          Book Your Rental
        </CardTitle>
        <p className="text-sm text-gray-600 mt-2">
          Enter your details to find available cars
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Dynamic Pick-up Location */}
        <PickupLocationField
          value={pickUpLocation}
          onValueChange={setPickUpLocation}
          placeholder="Select pickup location"
          showLabel={true}
          className="w-full"
        />

        {/* Dynamic Drop-off Location */}
        <DropOffLocationField
          value={dropOffLocation}
          onValueChange={setDropOffLocation}
          placeholder="Select drop-off location"
          showLabel={true}
          className="w-full"
        />

        {/* Date and Time Inputs */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Pick-up Date & Time */}
          <div className="space-y-4">
            <Label className="text-sm font-medium text-gray-700">
              Pick-up Date & Time
            </Label>
            <div className="grid grid-cols-2 gap-3">
              <DatePicker
                value={pickUpDate}
                onChange={setPickUpDate}
                placeholder="Select date"
                minDate={getTodayDate()}
                aria-label="Pick-up date"
                className="border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100"
              />
              <div className="space-y-2">
                <TimePicker
                  value={pickUpTime}
                  onChange={setPickUpTime}
                  placeholder="Select time"
                  minTime="06:00"
                  maxTime="22:00"
                  step={60}
                  showQuickActions={true}
                  showSecondaryFormat={false}
                  aria-label="Pick-up time"
                  className="border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100"
                />
              </div>
            </div>
          </div>

          {/* Drop-off Date & Time */}
          <div className="space-y-4">
            <Label className="text-sm font-medium text-gray-700">
              Drop-off Date & Time
            </Label>
            <div className="grid grid-cols-2 gap-3">
              <DatePicker
                value={dropOffDate}
                onChange={setDropOffDate}
                placeholder="Select date"
                minDate={getTodayDate()}
                aria-label="Drop-off date"
                className="border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100"
              />
              <div className="space-y-2">
                <TimePicker
                  value={dropOffTime}
                  onChange={setDropOffTime}
                  placeholder="Select time"
                  minTime="06:00"
                  maxTime="22:00"
                  step={60}
                  showQuickActions={true}
                  showSecondaryFormat={false}
                  aria-label="Drop-off time"
                  className="border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Search Button */}
        <div className="pt-4">
          <Button
            onClick={goSearch}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 text-base"
            size="lg"
          >
            Search Available Cars
          </Button>
        </div>
      </CardContent>

      {/* Authentication Modal */}
      <AuthRequiredModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        action="search for available cars"
      />
    </Card>
  );
}

function CarIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox="0 0 24 24" fill="none" {...props}>
      <path
        d="M3 13l2-5 3-2h6l3 2 2 5"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <circle cx="7" cy="16" r="2" stroke="currentColor" strokeWidth="1.5" />
      <circle cx="17" cy="16" r="2" stroke="currentColor" strokeWidth="1.5" />
    </svg>
  );
}
