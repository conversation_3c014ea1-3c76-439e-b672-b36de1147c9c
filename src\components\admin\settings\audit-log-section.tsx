"use client";

import * as React from "react";
import { History } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import type { AuditLogEntry } from "@/lib/types";

export function AuditLogSection({ auditLogs }: { auditLogs: AuditLogEntry[] }) {
  return (
    <Card data-testid="admin-settings-card-audit">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Audit Log
        </CardTitle>
        <p className="text-sm text-gray-600">
          View recent system changes and user activities
        </p>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-2">
            {auditLogs.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <History className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No audit logs available</p>
              </div>
            ) : (
              auditLogs.map((log) => (
                <div
                  key={log.id}
                  className="flex items-start gap-3 p-3 border rounded-lg"
                >
                  <div className="w-2 h-2 rounded-full bg-blue-500 mt-2 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium">{log.action}</span>
                      <Badge variant="secondary" className="text-xs">
                        {log.section}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      {log.field_name && log.old_value && log.new_value
                        ? `Changed ${log.field_name} from "${log.old_value}" to "${log.new_value}"`
                        : log.action}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>By: {log.user_email}</span>
                      <span>{new Date(log.timestamp).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
