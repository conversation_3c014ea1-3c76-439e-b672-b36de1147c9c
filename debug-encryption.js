// Debug script to test encryption/decryption compatibility
const ENCRYPT_KEY = "OllieGPS2024";

// Test data that should match ESP32 output
const testData = "14.691452,121.042686,1.3,lilygo-esp32-01";
console.log("🔍 Test data:", testData);
console.log("🔍 Test data length:", testData.length);

// JavaScript encryption (mimicking NEW ESP32 implementation)
function encryptLikeNewESP32(data) {
  const hexDigits = "0123456789abcdef";
  let encrypted = "";
  
  for (let i = 0; i < data.length; i++) {
    const dataChar = data.charCodeAt(i);
    const keyChar = ENCRYPT_KEY.charCodeAt(i % ENCRYPT_KEY.length);
    const encChar = dataChar ^ keyChar;
    
    // Manual hex encoding like ESP32
    encrypted += hexDigits[(encChar >> 4) & 0x0F];  // High nibble
    encrypted += hexDigits[encChar & 0x0F];         // Low nibble
  }
  
  return encrypted;
}

// JavaScript decryption (current server implementation)
function decryptFromServer(encryptedToken) {
  const hexPairs = encryptedToken.match(/.{1,2}/g) || [];
  let decrypted = "";
  
  for (let i = 0; i < hexPairs.length; i++) {
    const hexChar = parseInt(hexPairs[i], 16);
    const keyChar = ENCRYPT_KEY.charCodeAt(i % ENCRYPT_KEY.length);
    const decryptedChar = String.fromCharCode(hexChar ^ keyChar);
    decrypted += decryptedChar;
  }
  
  return decrypted;
}

// Test NEW encryption/decryption
console.log("\n--- Testing NEW ESP32 implementation ---");
const newEncrypted = encryptLikeNewESP32(testData);
console.log("🔐 NEW Encrypted:", newEncrypted);
console.log("🔐 NEW Length:", newEncrypted.length);
console.log("🔐 Expected length:", testData.length * 2);

const newDecrypted = decryptFromServer(newEncrypted);
console.log("🔓 NEW Decrypted:", newDecrypted);
console.log("✅ NEW Match:", testData === newDecrypted);

// Test with the actual token from the user
console.log("\n--- Testing actual token ---");
const actualToken = "7e58425f5c76646b31c367e425c5d577064631e11c1630551c203f7e57434277d415c58";
console.log("🔍 Actual token:", actualToken);
console.log("🔍 Token length:", actualToken.length);
console.log("🔍 Is even length:", actualToken.length % 2 === 0);

try {
  const actualDecrypted = decryptFromServer(actualToken);
  console.log("🔓 Actual decrypted:", actualDecrypted);
  console.log("🔓 Actual decrypted (JSON):", JSON.stringify(actualDecrypted));
  
  const parts = actualDecrypted.split(',');
  console.log("📊 Parts:", parts);
  console.log("📊 Parts count:", parts.length);
} catch (error) {
  console.error("❌ Error:", error.message);
}
