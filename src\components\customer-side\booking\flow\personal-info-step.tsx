"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { User, Mail, Phone, MapPin, MessageSquare, Shield } from "lucide-react";
import { FLAT_PHILIPPINES_LOCATIONS } from "@/lib/philippines-locations";
import type { BookingData } from "./booking-flow";
import { createClient } from "@/lib/supabase/client";
import { type User as SupabaseUser } from "@supabase/supabase-js";

interface PersonalInfoStepProps {
  bookingData: BookingData;
  onUpdate: (updates: Partial<BookingData>) => void;
}

async function getProfile(user: SupabaseUser) {
  const supabase = createClient();
  const { data, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", user.id)
    .single();

  if (error) {
    console.error("Error fetching profile:", error);
    return null;
  }

  return data;
}

export function PersonalInfoStep({
  bookingData,
  onUpdate,
}: PersonalInfoStepProps) {
  const [errors, setErrors] = React.useState<Record<string, string>>({});

  React.useEffect(() => {
    const fetchUser = async () => {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (user) {
        const profile = await getProfile(user);
        onUpdate({
          customerName: profile?.full_name ?? "",
          customerEmail: user.email ?? "",
          customerPhone: profile?.phone ?? "",
        });
      }
    };

    fetchUser();
  }, [onUpdate]);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string) => {
    // Basic phone validation - allow Philippine and international formats
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,15}$/;
    return phoneRegex.test(phone.replace(/\s/g, ""));
  };

  const handleInputChange = (field: keyof BookingData, value: string) => {
    onUpdate({ [field]: value });

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateField = (field: keyof BookingData, value: string) => {
    let error = "";

    switch (field) {
      case "customerName":
        if (!value.trim()) error = "Name is required";
        else if (value.trim().length < 2)
          error = "Name must be at least 2 characters";
        break;
      case "customerEmail":
        if (!value.trim()) error = "Email is required";
        else if (!validateEmail(value))
          error = "Please enter a valid email address";
        break;
      case "customerPhone":
        if (!value.trim()) error = "Phone number is required";
        else if (!validatePhone(value))
          error = "Please enter a valid phone number";
        break;
      case "familyMemberPhone":
        if (!value.trim()) error = "Family member's phone number is required";
        else if (!validatePhone(value))
          error = "Please enter a valid phone number";
        break;
      case "familyMemberFacebook":
        if (!value.trim()) error = "Family member's Facebook account is required";
        else if (value.trim().length < 3)
          error = "Facebook account must be at least 3 characters";
        break;
    }

    setErrors((prev) => ({ ...prev, [field]: error }));
    return !error;
  };

  const handleBlur = (field: keyof BookingData, value: string) => {
    validateField(field, value);
  };

  return (
    <div className="space-y-4 sm:space-y-6 max-w-full overflow-x-hidden">
      <Card className="overflow-hidden">
        <CardHeader className="p-4 sm:p-6">
          <CardTitle className="text-lg sm:text-xl break-words">
            Personal Information
          </CardTitle>
          <p className="text-sm text-gray-600 break-words">
            Please provide your contact details to complete the booking process.
          </p>
        </CardHeader>
        <CardContent className="p-4 sm:p-6 pt-0 space-y-4 sm:space-y-6">
          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-base sm:text-lg font-medium text-gray-900 break-words">
              Contact Details
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 max-w-full">
              {/* Full Name */}
              <div className="space-y-2 min-w-0">
                <Label
                  htmlFor="customerName"
                  className="text-sm font-medium text-gray-700"
                >
                  Full Name *
                </Label>
                <div
                  className={`flex items-center gap-3 border rounded-lg px-3 sm:px-4 py-3 bg-white min-w-0 ${
                    errors.customerName ? "border-red-300" : "border-gray-300"
                  }`}
                >
                  <User className="h-5 w-5 text-blue-600 flex-shrink-0" />
                  <Input
                    id="customerName"
                    placeholder="Enter your full name"
                    value={bookingData.customerName}
                    onChange={(e) =>
                      handleInputChange("customerName", e.target.value)
                    }
                    onBlur={(e) => handleBlur("customerName", e.target.value)}
                    className="border-0 p-0 focus-visible:ring-0 text-sm sm:text-base"
                  />
                </div>
                {errors.customerName && (
                  <p className="text-sm text-red-600 break-words">
                    {errors.customerName}
                  </p>
                )}
              </div>

              {/* Email */}
              <div className="space-y-2 min-w-0">
                <Label
                  htmlFor="customerEmail"
                  className="text-sm font-medium text-gray-700"
                >
                  Email Address *
                </Label>
                <div
                  className={`flex items-center gap-3 border rounded-lg px-3 sm:px-4 py-3 bg-white min-w-0 ${
                    errors.customerEmail ? "border-red-300" : "border-gray-300"
                  }`}
                >
                  <Mail className="h-5 w-5 text-blue-600 flex-shrink-0" />
                  <Input
                    id="customerEmail"
                    type="email"
                    placeholder="Enter your email address"
                    value={bookingData.customerEmail}
                    onChange={(e) =>
                      handleInputChange("customerEmail", e.target.value)
                    }
                    onBlur={(e) => handleBlur("customerEmail", e.target.value)}
                    className="border-0 p-0 focus-visible:ring-0 text-sm sm:text-base"
                  />
                </div>
                {errors.customerEmail && (
                  <p className="text-sm text-red-600 break-words">
                    {errors.customerEmail}
                  </p>
                )}
              </div>

              {/* Phone Number */}
              <div className="space-y-2 lg:col-span-2 min-w-0">
                <Label
                  htmlFor="customerPhone"
                  className="text-sm font-medium text-gray-700"
                >
                  Phone Number *
                </Label>
                <div
                  className={`flex items-center gap-3 border rounded-lg px-3 sm:px-4 py-3 bg-white min-w-0 ${
                    errors.customerPhone ? "border-red-300" : "border-gray-300"
                  }`}
                >
                  <Phone className="h-5 w-5 text-blue-600 flex-shrink-0" />
                  <Input
                    id="customerPhone"
                    type="tel"
                    placeholder="Enter your phone number (e.g., +639998810866)"
                    value={bookingData.customerPhone}
                    onChange={(e) =>
                      handleInputChange("customerPhone", e.target.value)
                    }
                    onBlur={(e) => handleBlur("customerPhone", e.target.value)}
                    className="border-0 p-0 focus-visible:ring-0 text-sm sm:text-base"
                  />
                </div>
                {errors.customerPhone && (
                  <p className="text-sm text-red-600 break-words">
                    {errors.customerPhone}
                  </p>
                )}
                <p className="text-xs text-gray-500 break-words">
                  We'll use this number to contact you about your booking and
                  for pickup coordination.
                </p>
              </div>

              {/* Family Member Phone */}
              <div className="space-y-2 min-w-0">
                <Label
                  htmlFor="familyMemberPhone"
                  className="text-sm font-medium text-gray-700"
                >
                  Immediate Family Member's Phone Number *
                </Label>
                <div
                  className={`flex items-center gap-3 border rounded-lg px-3 sm:px-4 py-3 bg-white min-w-0 ${
                    errors.familyMemberPhone ? "border-red-300" : "border-gray-300"
                  }`}
                >
                  <Phone className="h-5 w-5 text-blue-600 flex-shrink-0" />
                  <Input
                    id="familyMemberPhone"
                    type="tel"
                    placeholder="Enter family member's phone number"
                    value={bookingData.familyMemberPhone}
                    onChange={(e) =>
                      handleInputChange("familyMemberPhone", e.target.value)
                    }
                    onBlur={(e) => handleBlur("familyMemberPhone", e.target.value)}
                    className="border-0 p-0 focus-visible:ring-0 text-sm sm:text-base"
                  />
                </div>
                {errors.familyMemberPhone && (
                  <p className="text-sm text-red-600 break-words">
                    {errors.familyMemberPhone}
                  </p>
                )}
              </div>

              {/* Family Member Facebook */}
              <div className="space-y-2 min-w-0">
                <Label
                  htmlFor="familyMemberFacebook"
                  className="text-sm font-medium text-gray-700"
                >
                  Immediate Family Member's Facebook Account *
                </Label>
                <div
                  className={`flex items-center gap-3 border rounded-lg px-3 sm:px-4 py-3 bg-white min-w-0 ${
                    errors.familyMemberFacebook ? "border-red-300" : "border-gray-300"
                  }`}
                >
                  <User className="h-5 w-5 text-blue-600 flex-shrink-0" />
                  <Input
                    id="familyMemberFacebook"
                    type="text"
                    placeholder="Enter Facebook profile name or URL"
                    value={bookingData.familyMemberFacebook}
                    onChange={(e) =>
                      handleInputChange("familyMemberFacebook", e.target.value)
                    }
                    onBlur={(e) => handleBlur("familyMemberFacebook", e.target.value)}
                    className="border-0 p-0 focus-visible:ring-0 text-sm sm:text-base"
                  />
                </div>
                {errors.familyMemberFacebook && (
                  <p className="text-sm text-red-600 break-words">
                    {errors.familyMemberFacebook}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Additional Notes */}
          <div className="space-y-4">
            <h3 className="text-base sm:text-lg font-medium text-gray-900">
              Additional Information
            </h3>

            <div className="space-y-2">
              <Label
                htmlFor="notes"
                className="text-sm font-medium text-gray-700"
              >
                Special Requests or Notes (Optional)
              </Label>
              <div className="flex items-start gap-3 border border-gray-300 rounded-lg px-3 sm:px-4 py-3 bg-white min-w-0">
                <MessageSquare className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
                <Textarea
                  id="notes"
                  placeholder="Any special requests, driving preferences, or additional information..."
                  value={bookingData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  className="border-0 p-0 focus-visible:ring-0 resize-none text-sm sm:text-base min-w-0"
                  rows={3}
                />
              </div>
              <p className="text-xs text-gray-500 break-words">
                Let us know if you have any special requirements or requests for
                your rental.
              </p>
            </div>
          </div>

          {/* Privacy & Data Protection Notice */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 sm:p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2 flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Privacy & Data Protection Notice
            </h4>
            <div className="text-sm text-blue-800 space-y-2">
              <p className="break-words">
                Your personal information and family member contact details are securely encrypted and stored. We collect this information solely for:
              </p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>Identity verification and booking management</li>
                <li>Emergency contact purposes during rental period</li>
                <li>Customer service and support communications</li>
                <li>Legal compliance and safety requirements</li>
              </ul>
              <p className="break-words">
                We do not share your data with third parties without your explicit consent, except as required by law. Your information is protected under our strict privacy policy.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
