"use client"

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { LogOut, User } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useCustomerAuth } from '@/components/auth/customer-auth-context'
import { useRouter } from 'next/navigation'
import { NavItem } from './navItems'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'

interface MoreMenuProps {
  items: NavItem[]
  onItemClick?: () => void
  showUserActions?: boolean
  className?: string
}

// Safe hook that doesn't throw if outside provider
function useSafeCustomerAuth() {
  try {
    return useCustomerAuth();
  } catch (error) {
    // If we're not within a CustomerAuthProvider, return null user and empty signOut
    console.debug('MoreMenu: outside provider');
    return { user: null, signOut: async () => ({ error: null }) };
  }
}

export function MoreMenu({ 
  items, 
  onItemClick, 
  showUserActions = true, 
  className 
}: MoreMenuProps) {
  const { user, signOut } = useSafeCustomerAuth()
  const router = useRouter()
  const pathname = usePathname()
  
  const isActive = React.useCallback((href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }, [pathname])

  const handleSignOut = async () => {
    await signOut()
    router.push('/customer/login')
    onItemClick?.()
  }

  const handleItemClick = () => {
    onItemClick?.()
  }

  return (
    <div className={cn("space-y-2 bg-white", className)}>
      {/* Navigation Items */}
      {items.length > 0 && (
        <div className="space-y-1">
          {items.map((item) => {
            const active = isActive(item.href)
            const IconComponent = item.icon
            
            return (
              <Link
                key={item.key}
                href={item.href}
                onClick={handleItemClick}
                className={cn(
                  "flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200",
                  "min-h-[44px] touch-manipulation nav-transition nav-focus-visible", // Accessibility
                  active
                    ? "text-blue-700 bg-blue-50 font-semibold border border-blue-100"
                    : "text-gray-700 hover:text-blue-600 hover:bg-gray-50 active:bg-gray-100 border border-transparent"
                )}
                aria-current={active ? "page" : undefined}
              >
                <IconComponent className="h-5 w-5 flex-shrink-0" />
                <span className="font-medium">{item.label}</span>
              </Link>
            )
          })}
        </div>
      )}

      {/* User Actions */}
      {showUserActions && user && (
        <>
          {items.length > 0 && <Separator className="my-4 bg-gray-200" />}
          
          <div className="space-y-1 bg-gray-50 rounded-lg p-3">
            {/* User Info */}
            <div className="px-3 py-2 text-sm text-gray-600 bg-white rounded-md">
              <div className="flex items-center gap-3">
                <User className="h-4 w-4 text-gray-500" />
                <div className="min-w-0 flex-1">
                  <div className="font-semibold text-gray-900 truncate">
                    {user.email?.split('@')[0] || 'User'}
                  </div>
                  <div className="text-xs text-gray-500 truncate">
                    {user.email}
                  </div>
                </div>
              </div>
            </div>
            
            {/* Sign Out */}
            <Button
              variant="tertiary"
              onClick={handleSignOut}
              className="w-full justify-start gap-3 text-gray-700 hover:text-red-600 hover:bg-red-50 mt-2 bg-white border border-gray-200"
            >
              <LogOut className="h-4 w-4" />
              <span>Sign Out</span>
            </Button>
          </div>
        </>
      )}
      
      {/* Sign In/Up for non-authenticated users */}
      {showUserActions && !user && (
        <>
          {items.length > 0 && <Separator className="my-4 bg-gray-200" />}
          
          <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
            <div className="text-sm text-gray-600 text-center mb-3">
              Sign in to access your bookings and more features
            </div>
            <Link href="/customer/login" onClick={handleItemClick}>
              <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium">
                Sign In
              </Button>
            </Link>
            <Link href="/customer/signup" onClick={handleItemClick}>
              <Button variant="tertiary" className="w-full border border-gray-300 bg-white hover:bg-gray-50">
                Create Account
              </Button>
            </Link>
          </div>
        </>
      )}
    </div>
  )
}

export default MoreMenu
