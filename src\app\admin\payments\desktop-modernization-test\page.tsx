"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"

// Enhanced sample payment data for desktop testing
const samplePayments = [
  {
    id: "PAY123456",
    bookingId: "BK789012",
    renterName: "<PERSON>",
    renterEmail: "<EMAIL>",
    renterPhone: "+63 ************",
    amount: 25000,
    method: "GCash",
    status: "Completed",
    transactionDate: "2025-08-20T14:30:00Z",
    proofOfPaymentUrl: "https://example.com/receipt/123"
  },
  {
    id: "PAY123457",
    bookingId: "BK789013", 
    renterName: "<PERSON>",
    renterEmail: "<EMAIL>",
    renterPhone: "+63 ************",
    amount: 3750,
    method: "Bank Transfer",
    status: "Completed",
    transactionDate: "2025-08-19T10:15:00Z",
    proofOfPaymentUrl: undefined
  },
  {
    id: "PAY123458",
    bookingId: "BK789014",
    renterName: "Robert Christopher Johnson",
    renterEmail: "<EMAIL>",
    renterPhone: "+63 ************",
    amount: 18500,
    method: "Remittance Center",
    status: "Completed",
    transactionDate: "2025-08-18T16:45:00Z",
    proofOfPaymentUrl: "https://example.com/receipt/125"
  },
  {
    id: "PAY123459",
    bookingId: "BK789015",
    renterName: "Maria Carmen Santos-Rodriguez",
    renterEmail: "<EMAIL>",
    renterPhone: "+63 ************",
    amount: 42750,
    method: "GCash",
    status: "Completed",
    transactionDate: "2025-08-17T09:20:00Z",
    proofOfPaymentUrl: "https://example.com/receipt/126"
  },
  {
    id: "PAY123460",
    bookingId: "BK789016",
    renterName: "Alexander Benjamin Thompson",
    renterEmail: "<EMAIL>",
    renterPhone: "N/A",
    amount: 15250,
    method: "Bank Transfer",
    status: "Completed",
    transactionDate: "2025-08-16T13:10:00Z",
    proofOfPaymentUrl: undefined
  }
]

export default function DesktopModernizationTestPage() {
  const [windowWidth, setWindowWidth] = React.useState(0)
  const [query, setQuery] = React.useState("")
  const [method, setMethod] = React.useState<string>("all")
  const [sortBy, setSortBy] = React.useState<string>("date-desc")
  const [page, setPage] = React.useState(1)
  const pageSize = 10
  
  React.useEffect(() => {
    // Set initial window width
    setWindowWidth(window.innerWidth)
    
    // Update window width on resize
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])
  
  // Determine current breakpoint
  const getBreakpointName = (width: number) => {
    if (width < 1024) return "< Desktop (Mobile/Tablet)"
    if (width < 1280) return "Laptop (1024px)"
    if (width < 1440) return "Desktop (1280px)"
    if (width < 1920) return "Large Desktop (1440px)"
    return "Extra Large Desktop (1920px+)"
  }
  
  // Get background color based on breakpoint
  const getBreakpointColor = (width: number) => {
    if (width < 1024) return "bg-red-100 border-red-300" // Should not be used on desktop
    if (width < 1280) return "bg-blue-100 border-blue-300" // Laptop
    if (width < 1440) return "bg-green-100 border-green-300" // Desktop
    if (width < 1920) return "bg-purple-100 border-purple-300" // Large Desktop
    return "bg-yellow-100 border-yellow-300" // Extra Large
  }

  // Filter and sort logic (simplified for testing)
  const filtered = samplePayments.filter((p) => 
    (method === "all" ? true : p.method === method) &&
    (query === "" ? true : 
      (p.id + " " + p.bookingId + " " + p.renterName + " " + p.renterEmail + " " + p.amount + " " + p.method)
        .toLowerCase()
        .includes(query.toLowerCase())
    )
  )

  const sorted = [...filtered].sort((a, b) => {
    switch (sortBy) {
      case "amount-desc": return b.amount - a.amount
      case "amount-asc": return a.amount - b.amount
      case "date-asc": return +new Date(a.transactionDate) - +new Date(b.transactionDate)
      default: return +new Date(b.transactionDate) - +new Date(a.transactionDate)
    }
  })

  const totalPages = Math.max(1, Math.ceil(sorted.length / pageSize))
  const pageItems = sorted.slice((page - 1) * pageSize, page * pageSize)

  // Get payment method badge styling
  const getMethodBadgeClass = (method: string) => {
    switch (method.toLowerCase()) {
      case 'gcash':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'bank transfer':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'remittance center':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-slate-100 text-slate-800 border-slate-200'
    }
  }

  return (
    <div className="space-y-6 p-4 lg:p-6">
      <header className="space-y-3">
        <div>
          <h1 className="text-3xl lg:text-4xl font-bold tracking-tight text-foreground">
            Payments Desktop Modernization Test
          </h1>
          <p className="text-base lg:text-lg text-muted-foreground mt-2">
            Test the modernized desktop design across different screen widths
          </p>
        </div>
      </header>
      
      {/* Viewport Size Indicator */}
      <Card className={`${getBreakpointColor(windowWidth)} border-2 border-dashed`}>
        <CardContent className="p-4">
          <div className="text-center">
            <p className="text-lg font-bold">Current Viewport: {windowWidth}px</p>
            <p className="text-sm font-medium">{getBreakpointName(windowWidth)}</p>
            <p className="text-xs mt-2">
              {windowWidth >= 1024 ? "✅ Desktop View (Modernized Table)" : "❌ Mobile/Tablet View (Original Cards)"}
            </p>
          </div>
        </CardContent>
      </Card>
      
      {/* Desktop Breakpoint Guide */}
      <Card>
        <CardHeader>
          <CardTitle>Desktop Breakpoint Testing Guide</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-3 bg-blue-100 rounded-md">
              <p className="font-medium">Laptop</p>
              <p className="text-sm">1024px - 1279px</p>
              <p className="text-xs text-gray-600">Minimum desktop width</p>
            </div>
            <div className="p-3 bg-green-100 rounded-md">
              <p className="font-medium">Desktop</p>
              <p className="text-sm">1280px - 1439px</p>
              <p className="text-xs text-gray-600">Standard desktop</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-md">
              <p className="font-medium">Large Desktop</p>
              <p className="text-sm">1440px - 1919px</p>
              <p className="text-xs text-gray-600">Large monitors</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-md">
              <p className="font-medium">Extra Large</p>
              <p className="text-sm">1920px+</p>
              <p className="text-xs text-gray-600">4K and ultrawide</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Modernized Payments Table */}
      <Card className="shadow-sm">
        <CardHeader className="pb-4 lg:pb-6">
          <div className="flex flex-col gap-4 lg:gap-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <CardTitle className="text-xl lg:text-2xl">Payment Management</CardTitle>
              <div className="flex flex-col sm:flex-row gap-3 lg:w-auto">
                <Input 
                  placeholder="Search by payment ID, renter name, or email..." 
                  value={query} 
                  onChange={(e) => setQuery(e.target.value)} 
                  className="w-full lg:w-80 xl:w-96" 
                />
              </div>
            </div>
            
            {/* Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-3 lg:gap-4">
              <div className="flex flex-col xs:flex-row gap-3 flex-1">
                <Select value={method} onValueChange={setMethod}>
                  <SelectTrigger className="w-full xs:w-[180px] lg:w-[200px]">
                    <SelectValue placeholder="Payment Method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Methods</SelectItem>
                    <SelectItem value="GCash">GCash</SelectItem>
                    <SelectItem value="Bank Transfer">Bank Transfer</SelectItem>
                    <SelectItem value="Remittance Center">Remittance Center</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full xs:w-[200px] lg:w-[220px]">
                    <SelectValue placeholder="Sort Options" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">Newest first</SelectItem>
                    <SelectItem value="date-asc">Oldest first</SelectItem>
                    <SelectItem value="amount-desc">Amount: high to low</SelectItem>
                    <SelectItem value="amount-asc">Amount: low to high</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-0">
          {/* Desktop Table View Only */}
          <div className="max-h-[75vh] overflow-auto lg:rounded-lg lg:border">
            <Table>
              <TableHeader className="sticky top-0 z-10 bg-gradient-to-r from-slate-50 to-slate-100 shadow-md">
                <TableRow className="border-b-2 border-slate-200">
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide">
                    Payment ID
                  </TableHead>
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide">
                    Renter Information
                  </TableHead>
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide">
                    Booking Ref.
                  </TableHead>
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide text-right">
                    Amount Paid
                  </TableHead>
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide">
                    Payment Date
                  </TableHead>
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide">
                    Method
                  </TableHead>
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide text-right">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {pageItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="px-4 py-12 lg:px-6 lg:py-16 text-center">
                      <div className="text-slate-500 font-medium">No payments found</div>
                      <p className="text-sm text-slate-400 mt-1">Try adjusting your search or filter criteria</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  pageItems.map((p, index) => (
                    <TableRow key={p.id} className={`transition-colors hover:bg-slate-50/70 ${
                      index % 2 === 0 ? 'bg-white' : 'bg-slate-25'
                    } border-b border-slate-100`}>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5">
                        <div className="font-mono text-sm font-medium text-slate-700">#{p.id}</div>
                      </TableCell>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5">
                        <div className="space-y-1">
                          <div className="font-semibold text-slate-900 text-base">{p.renterName}</div>
                          <div className="text-sm text-slate-600">{p.renterEmail}</div>
                          {p.renterPhone !== "N/A" && (
                            <div className="text-sm text-slate-500">{p.renterPhone}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5">
                        <a 
                          className="inline-flex items-center font-mono text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline transition-colors" 
                          href={`/admin/bookings?ref=${p.bookingId}`}
                        >
                          #{p.bookingId}
                        </a>
                      </TableCell>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5 text-right">
                        <div className="font-bold text-lg text-slate-900">
                          ₱{p.amount.toLocaleString('en-PH', { minimumFractionDigits: 2 })}
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5">
                        <div className="text-sm font-medium text-slate-700">
                          {new Date(p.transactionDate).toLocaleDateString('en-PH', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })}
                        </div>
                        <div className="text-xs text-slate-500">
                          {new Date(p.transactionDate).toLocaleTimeString('en-PH', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5">
                        <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${
                          getMethodBadgeClass(p.method)
                        }`}>
                          {p.method}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5 text-right">
                        {p.proofOfPaymentUrl ? (
                          <a 
                            href={p.proofOfPaymentUrl} 
                            target="_blank" 
                            rel="noopener noreferrer" 
                            className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                          >
                            View Receipt
                          </a>
                        ) : (
                          <span className="text-sm text-slate-400 font-medium">No receipt</span>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          
          {/* Modernized Pagination */}
          <div className="flex justify-between items-center gap-4 p-4 lg:p-6 border-t border-slate-200 bg-slate-50/50">
            <div className="text-sm text-slate-600">
              Showing {(page - 1) * pageSize + 1} to {Math.min(page * pageSize, sorted.length)} of {sorted.length} payments
            </div>
            <div className="flex items-center gap-3">
              <button 
                className="px-4 py-2 text-sm font-medium rounded-lg border border-slate-300 bg-white hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px] transition-colors" 
                onClick={() => setPage((p) => Math.max(1, p - 1))} 
                disabled={page === 1}
              >
                Previous
              </button>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-slate-700">Page {page} of {totalPages}</span>
              </div>
              <button 
                className="px-4 py-2 text-sm font-medium rounded-lg border border-slate-300 bg-white hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px] transition-colors" 
                onClick={() => setPage((p) => Math.min(totalPages, p + 1))} 
                disabled={page === totalPages}
              >
                Next
              </button>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Testing Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Desktop Modernization Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal list-inside space-y-3">
            <li><strong>Viewport Testing:</strong> Resize browser window to test all desktop breakpoints (1024px, 1280px, 1440px, 1920px+)</li>
            <li><strong>Header Alignment:</strong> Verify title, search bar, and filters are properly aligned and balanced</li>
            <li><strong>Table Styling:</strong> Check gradient header, proper cell padding, and alternating row colors</li>
            <li><strong>Typography:</strong> Confirm font sizes, weights, and color hierarchy are consistent</li>
            <li><strong>Amount Alignment:</strong> Verify monetary values are right-aligned and properly formatted</li>
            <li><strong>Badge Styling:</strong> Test payment method badges with proper colors and borders</li>
            <li><strong>Link Interactions:</strong> Test booking reference links and receipt links hover states</li>
            <li><strong>Pagination:</strong> Verify pagination shows proper counts and button states</li>
            <li><strong>Search & Filter:</strong> Test search functionality and filter dropdowns</li>
            <li><strong>Responsive Spacing:</strong> Check that padding and margins scale properly with lg: and xl: breakpoints</li>
          </ol>
        </CardContent>
      </Card>
      
      {/* Modernization Improvements Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Modernization Improvements Applied</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3">Layout & Alignment</h4>
              <ul className="text-sm space-y-2">
                <li>• Enhanced page spacing (space-y-6, lg:p-6)</li>
                <li>• Improved header typography and hierarchy</li>
                <li>• Better filter control alignment and spacing</li>
                <li>• Responsive search bar sizing (lg:w-80 xl:w-96)</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">Table Design</h4>
              <ul className="text-sm space-y-2">
                <li>• Gradient header with improved styling</li>
                <li>• Enhanced cell padding (lg:px-6 lg:py-5)</li>
                <li>• Right-aligned monetary values</li>
                <li>• Colored payment method badges</li>
                <li>• Better hover states and transitions</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">Typography</h4>
              <ul className="text-sm space-y-2">
                <li>• Improved font hierarchy and weights</li>
                <li>• Better color contrast (slate color palette)</li>
                <li>• Proper date and time formatting</li>
                <li>• Enhanced readability for all text elements</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3">User Experience</h4>
              <ul className="text-sm space-y-2">
                <li>• Enhanced pagination with record counts</li>
                <li>• Better loading and empty states</li>
                <li>• Improved interactive elements</li>
                <li>• Consistent spacing across all desktop widths</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
