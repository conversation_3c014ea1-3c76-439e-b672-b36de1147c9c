# Button Component Guide

## Overview
The Button component is a comprehensive, accessible button system that follows UI/UX best practices and provides clear visual hierarchy through variants, sizes, and interaction states.

## Quick Start

```tsx
import { But<PERSON> } from '@/components/ui/button'

// Basic usage
<Button variant="primary">Book Now</Button>
<Button variant="secondary">View Details</Button>
<Button variant="link">Learn More</Button>
```

## Variants

### Primary Actions (Fill Style)
```tsx
<Button variant="primary">Confirm Booking</Button>
<Button variant="success">Payment Complete</Button>
<Button variant="destructive">Delete Item</Button>
<Button variant="warning">Review Required</Button>
```

### Secondary Actions (Outline Style)
```tsx
<Button variant="secondary">Cancel</Button>
<Button variant="destructive-outline">Remove Item</Button>
```

### Tertiary Actions (Ghost Style)
```tsx
<Button variant="tertiary">Skip for Now</Button>
<Button variant="link">Learn More</Button>
```

## Sizes
```tsx
<Button size="xs">Extra Small</Button>
<Button size="sm">Small</Button>
<Button size="default">Default</Button>
<Button size="lg">Large</Button>
<Button size="xl">Extra Large</Button>
```

## Shapes
```tsx
<Button shape="default">Default</Button>
<Button shape="rounded">Rounded</Button>
<Button shape="pill">Pill</Button>
<Button shape="square">Square</Button>
```

## Interactive States
```tsx
<Button disabled>Disabled</Button>
<Button loading loadingText="Processing...">Submit</Button>
```

## Icons
```tsx
<Button leftIcon={<Plus />}>Add Item</Button>
<Button rightIcon={<ArrowRight />}>Continue</Button>
<Button size="icon" aria-label="Settings"><Settings /></Button>
```

## Copy Guidelines

### ✅ Good Examples
- "Book Your Rental" (specific action + object)
- "Download Receipt" (clear outcome)
- "Cancel Reservation" (specific action)
- "Payment Complete" (status update)

### ❌ Avoid
- "Click Here" (no context)
- "Submit" (generic)
- "OK" (unless in dialog context)
- "Button" (not descriptive)

## Accessibility

### Required for Icon-Only Buttons
```tsx
<Button size="icon" aria-label="Add to favorites">
  <Heart />
</Button>
```

### With Descriptions
```tsx
<Button aria-describedby="help-text">
  Need Help?
</Button>
<div id="help-text">Contact support team</div>
```

## Legacy Support

The component includes automatic mapping for legacy variants:
- `default` → `primary`
- `outline` → `secondary` 
- `ghost` → `tertiary`

```tsx
// Legacy (still works)
<Button variant="default">Old Style</Button>
<Button variant="outline">Old Style</Button>

// New (recommended)
<Button variant="primary">New Style</Button>
<Button variant="secondary">New Style</Button>
```

## Development Validation

In development mode, the component will warn if:
- Button has no text, aria-label, or title
- Copy doesn't follow best practices

This helps maintain consistent, accessible button usage across the application.
