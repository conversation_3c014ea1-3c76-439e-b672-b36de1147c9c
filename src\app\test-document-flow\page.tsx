"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  FileText, 
  Shield, 
  Calendar, 
  CheckCircle2, 
  XCircle, 
  Clock,
  AlertTriangle,
  RefreshCw
} from "lucide-react";
import {
  checkUserLegalDocuments,
  saveUserLegalDocument,
  deleteUserLegalDocument,
  getStandardDocumentType,
  type UserLegalDocument,
  type DocumentCheckResult,
} from "@/lib/services/document-service";
import { useToast } from "@/hooks/use-toast";
import { createClient } from "@/lib/supabase/client";

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: any;
}

export default function TestDocumentFlowPage() {
  const { toast } = useToast();
  const [documentCheck, setDocumentCheck] = React.useState<DocumentCheckResult | null>(null);
  const [testResults, setTestResults] = React.useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = React.useState(false);
  const [userBookings, setUserBookings] = React.useState<any[]>([]);

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result]);
  };

  const runTest = async (testName: string, testFn: () => Promise<void>) => {
    addTestResult({ name: testName, status: 'pending', message: 'Running...' });
    
    try {
      await testFn();
      setTestResults(prev => prev.map(r => 
        r.name === testName 
          ? { ...r, status: 'success', message: 'Passed' }
          : r
      ));
    } catch (error: any) {
      setTestResults(prev => prev.map(r => 
        r.name === testName 
          ? { 
              ...r, 
              status: 'error', 
              message: error.message || 'Failed',
              details: error
            }
          : r
      ));
    }
  };

  const runAllTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);
    
    try {
      // Test 1: Check Supabase Authentication
      await runTest("Authentication Check", async () => {
        const supabase = createClient();
        const { data: { user }, error } = await supabase.auth.getUser();
        
        if (error) throw new Error(`Auth error: ${error.message}`);
        if (!user) throw new Error("No authenticated user found");
        
        console.log("✓ User authenticated:", user.email);
      });

      // Test 2: Check User Bookings
      await runTest("User Bookings Check", async () => {
        const supabase = createClient();
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) throw new Error("No user found");
        
        const { data: bookings, error } = await supabase
          .from('bookings')
          .select('*')
          .eq('customer_id', user.id)
          .order('created_at', { ascending: false });
        
        if (error) throw new Error(`Bookings query error: ${error.message}`);
        
        setUserBookings(bookings || []);
        console.log(`✓ Found ${bookings?.length || 0} bookings`);
      });

      // Test 3: Check Document Service Function
      await runTest("Document Service Check", async () => {
        const result = await checkUserLegalDocuments();
        setDocumentCheck(result);
        
        if (!result) throw new Error("No result from checkUserLegalDocuments");
        
        console.log("✓ Document check result:", result);
      });

      // Test 4: Test Document Type Mapping
      await runTest("Document Type Mapping", async () => {
        const mappings = [
          { ui: 'driversLicense', db: 'drivers_license' },
          { ui: 'governmentId', db: 'government_id' },
          { ui: 'proofOfBilling', db: 'proof_of_billing' }
        ];
        
        for (const mapping of mappings) {
          const result = getStandardDocumentType(mapping.ui);
          if (result !== mapping.db) {
            throw new Error(`Mapping failed: ${mapping.ui} -> expected ${mapping.db}, got ${result}`);
          }
        }
        
        console.log("✓ All document type mappings correct");
      });

      // Test 5: Database Schema Check
      await runTest("Database Schema Check", async () => {
        const supabase = createClient();
        
        // Test booking_documents table structure
        const { data, error } = await supabase
          .from('booking_documents')
          .select('*')
          .limit(1);
        
        if (error && !error.message.includes('No rows')) {
          throw new Error(`Database schema error: ${error.message}`);
        }
        
        console.log("✓ booking_documents table accessible");
      });

      // Test 6: Storage Bucket Check
      await runTest("Storage Bucket Check", async () => {
        const supabase = createClient();
        
        const { data: buckets, error } = await supabase.storage.listBuckets();
        
        if (error) throw new Error(`Storage error: ${error.message}`);
        
        const legalDocsBucket = buckets?.find(b => b.name === 'legal-documents');
        const setupAccountBucket = buckets?.find(b => b.name === 'setup-account');
        
        if (!legalDocsBucket) throw new Error("legal-documents bucket not found");
        if (!setupAccountBucket) throw new Error("setup-account bucket not found");
        
        console.log("✓ Required storage buckets exist");
      });

      toast({
        title: "Tests Completed",
        description: "All document flow tests have been executed. Check results above.",
      });

    } catch (error) {
      console.error('Test execution error:', error);
      toast({
        variant: "destructive",
        title: "Test Error",
        description: "An error occurred while running tests.",
      });
    } finally {
      setIsRunningTests(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-600" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved': return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case 'pending': return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'rejected': return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      case 'requires_resubmission': return <Badge className="bg-orange-100 text-orange-800">Resubmit</Badge>;
      default: return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">Document Flow Test Page</h1>
          <p className="text-muted-foreground">
            Test the end-to-end document upload and verification functionality
          </p>
        </div>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Test Controls
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button 
                onClick={runAllTests}
                disabled={isRunningTests}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${isRunningTests ? 'animate-spin' : ''}`} />
                {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(result.status)}
                      <span className="font-medium">{result.name}</span>
                    </div>
                    <div className="text-right">
                      <span className="text-sm text-muted-foreground">{result.message}</span>
                      {result.details && (
                        <details className="text-xs text-red-600 mt-1">
                          <summary className="cursor-pointer">View Error</summary>
                          <pre className="mt-2 p-2 bg-red-50 rounded overflow-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Document Check Results */}
        {documentCheck && (
          <Card>
            <CardHeader>
              <CardTitle>Document Check Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                
                {/* Summary */}
                <div className="space-y-4">
                  <h3 className="font-semibold">Summary</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Has All Documents:</span>
                      <Badge className={documentCheck.hasAllDocuments ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {documentCheck.hasAllDocuments ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Documents:</span>
                      <span>{documentCheck.documentSummary.total}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Approved:</span>
                      <span className="text-green-600">{documentCheck.documentSummary.approved}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Pending:</span>
                      <span className="text-yellow-600">{documentCheck.documentSummary.pending}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Rejected:</span>
                      <span className="text-red-600">{documentCheck.documentSummary.rejected}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Missing:</span>
                      <span className="text-gray-600">{documentCheck.documentSummary.missing}</span>
                    </div>
                  </div>
                </div>

                {/* Missing Documents */}
                <div className="space-y-4">
                  <h3 className="font-semibold">Missing Documents</h3>
                  {documentCheck.missingDocuments.length > 0 ? (
                    <div className="space-y-2">
                      {documentCheck.missingDocuments.map((docType) => (
                        <div key={docType} className="flex items-center gap-2">
                          <XCircle className="h-4 w-4 text-red-500" />
                          <span className="text-sm">{docType.replace('_', ' ').toUpperCase()}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No missing documents</p>
                  )}
                </div>
              </div>

              {/* Existing Documents */}
              {documentCheck.existingDocuments.length > 0 && (
                <div className="mt-6">
                  <h3 className="font-semibold mb-4">Existing Documents</h3>
                  <div className="space-y-3">
                    {documentCheck.existingDocuments.map((doc) => (
                      <div key={doc.id} className="flex items-center justify-between p-3 rounded-lg border">
                        <div className="flex items-center gap-3">
                          <FileText className="h-4 w-4" />
                          <div>
                            <span className="font-medium">{doc.document_type.replace('_', ' ').toUpperCase()}</span>
                            <p className="text-xs text-muted-foreground">{doc.file_name}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          {getStatusBadge(doc.verification_status)}
                          <p className="text-xs text-muted-foreground mt-1">
                            {new Date(doc.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* User Bookings Info */}
        {userBookings.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>User Bookings</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Found {userBookings.length} booking(s) for document association
              </p>
              <div className="space-y-2">
                {userBookings.slice(0, 5).map((booking) => (
                  <div key={booking.id} className="flex justify-between p-2 rounded border">
                    <span className="text-sm">Booking #{booking.id.slice(0, 8)}...</span>
                    <span className="text-xs text-muted-foreground">
                      {new Date(booking.created_at).toLocaleDateString()}
                    </span>
                  </div>
                ))}
                {userBookings.length > 5 && (
                  <p className="text-xs text-muted-foreground">
                    ...and {userBookings.length - 5} more
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Links */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Navigation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <Button asChild variant="secondary" className="justify-start">
                <a href="/customer/booking/flow">
                  <Shield className="h-4 w-4 mr-2" />
                  Booking Flow (Step 2)
                </a>
              </Button>
              <Button asChild variant="secondary" className="justify-start">
                <a href="/customer/dashboard">
                  <FileText className="h-4 w-4 mr-2" />
                  Account Settings
                </a>
              </Button>
              <Button asChild variant="secondary" className="justify-start">
                <a href="/auth/setup-account">
                  <Calendar className="h-4 w-4 mr-2" />
                  Account Setup
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  );
}
