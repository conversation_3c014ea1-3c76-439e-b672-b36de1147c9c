-- ============================================
-- NON-DESTRUCTIVE: Add Admin GPS Device Policy
-- ============================================
-- Issue: Only super_admin can manage GPS devices
-- Solution: Add additional policy for regular admin users

-- Add a new policy for regular admin users (non-destructive)
-- This works alongside the existing super_admin policy
DO $$
BEGIN
  -- Only create if it doesn't already exist
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'gps_device_mapping' 
    AND policyname = 'GPS device mappings are manageable by regular admins'
  ) THEN
    CREATE POLICY "GPS device mappings are manageable by regular admins" ON public.gps_device_mapping
      FOR ALL USING (
        EXISTS (
          SELECT 1 FROM profiles 
          WHERE profiles.id = auth.uid() 
          AND profiles.role = 'admin'
        )
      );
    RAISE NOTICE 'Added policy for regular admin users';
  ELSE
    RAISE NOTICE 'Admin policy already exists';
  END IF;
END $$;

-- Verify both policies exist
SELECT 
    policyname,
    cmd,
    CASE 
        WHEN policyname LIKE '%super admin%' THEN 'Super Admin Access'
        WHEN policyname LIKE '%regular admin%' THEN 'Regular Admin Access' 
        ELSE 'Other Access'
    END as policy_type
FROM pg_policies 
WHERE tablename = 'gps_device_mapping'
ORDER BY policyname;

-- Test current user permissions
SELECT 
    auth.uid() as current_user_id,
    p.role as user_role,
    CASE 
        WHEN p.role IN ('admin', 'super_admin') THEN '✅ Can manage GPS devices'
        ELSE '❌ Cannot manage GPS devices'
    END as permission_status
FROM profiles p 
WHERE p.id = auth.uid();
