#!/usr/bin/env python3
"""
PathLink GPS Tracker - Installation Test Script
=================================================

This script tests if all required dependencies are properly installed
and if the GUI application can be imported successfully.

Run this script to verify your installation before launching the main GUI.
"""

import sys
import importlib

def test_import(module_name, package_name=None):
    """Test if a module can be imported"""
    try:
        if package_name:
            importlib.import_module(module_name)
            print(f"✓ {package_name} ({module_name}) - OK")
            return True
        else:
            importlib.import_module(module_name)
            print(f"✓ {module_name} - OK")
            return True
    except ImportError as e:
        print(f"✗ {package_name or module_name} - FAILED: {e}")
        return False

def test_builtin_modules():
    """Test built-in Python modules"""
    print("\n=== Testing Built-in Modules ===")
    modules = [
        'tkinter',
        'json',
        'threading',
        'time',
        'datetime',
        'os',
        'configparser',
        'typing',
        'queue'
    ]
    
    all_ok = True
    for module in modules:
        if not test_import(module):
            all_ok = False
    
    return all_ok

def test_external_packages():
    """Test external Python packages"""
    print("\n=== Testing External Packages ===")
    packages = [
        ('requests', 'requests'),
        ('websocket', 'websocket-client'),
        ('urllib3', 'urllib3')
    ]
    
    all_ok = True
    for module, package in packages:
        if not test_import(module, package):
            all_ok = False
    
    return all_ok

def test_gui_import():
    """Test if the GUI application can be imported"""
    print("\n=== Testing GUI Application ===")
    try:
        # Try to import the main GUI class
        from pathlink_gui import PathLinkGUI
        print("✓ PathLinkGUI class - OK")
        return True
    except ImportError as e:
        print(f"✗ PathLinkGUI class - FAILED: {e}")
        return False
    except Exception as e:
        print(f"✗ PathLinkGUI class - ERROR: {e}")
        return False

def test_python_version():
    """Test Python version compatibility"""
    print("\n=== Testing Python Version ===")
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 7:
        print("✓ Python version - OK (3.7+ required)")
        return True
    else:
        print("✗ Python version - FAILED (3.7+ required)")
        return False

def main():
    """Main test function"""
    print("PathLink GPS Tracker - Installation Test")
    print("=" * 50)
    
    # Test Python version first
    version_ok = test_python_version()
    
    # Test built-in modules
    builtin_ok = test_builtin_modules()
    
    # Test external packages
    external_ok = test_external_packages()
    
    # Test GUI import
    gui_ok = test_gui_import()
    
    # Summary
    print("\n" + "=" * 50)
    print("INSTALLATION TEST SUMMARY")
    print("=" * 50)
    
    if version_ok and builtin_ok and external_ok and gui_ok:
        print("🎉 ALL TESTS PASSED! Installation is complete.")
        print("\nYou can now run the PathLink GUI with:")
        print("  python pathlink_gui.py")
        print("\nOr use the launcher scripts:")
        print("  Windows: run_pathlink.bat")
        print("  Unix:    ./run_pathlink.sh")
        return True
    else:
        print("❌ SOME TESTS FAILED! Please fix the issues above.")
        
        if not version_ok:
            print("\n• Update Python to version 3.7 or higher")
        
        if not builtin_ok:
            print("\n• This is unusual - Python installation may be corrupted")
        
        if not external_ok:
            print("\n• Install missing packages with: pip install -r requirements.txt")
        
        if not gui_ok:
            print("\n• Check that pathlink_gui.py is in the current directory")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
