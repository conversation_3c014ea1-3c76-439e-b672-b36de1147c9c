"use client";

import { type Car } from "@/lib/types";
import {
  Card,
  CardContent,
  CardTitle,
  CardDescription,
  CardAction,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useRouter } from "next/navigation";
import * as React from "react";

interface CategoryCardProps {
  category: any; // Vehicle category object from database
  cars: Car[];
  onSelect: (category: string) => void;
}

export function CategoryCard({ category, cars, onSelect }: CategoryCardProps) {
  const router = useRouter();

  const availableCars = cars.filter((car) => car.status === "Available");
  const carCount = availableCars.length;

  if (carCount === 0) return null;

  // Use category data from vehicle_categories table
  const minPrice =
    category.actual_min_price ||
    category.min_price ||
    Math.min(...availableCars.map((car) => car.price_per_day));
  const maxPrice =
    category.actual_max_price ||
    category.max_price ||
    Math.max(...availableCars.map((car) => car.price_per_day));

  // Get transmission types from category or fallback to cars
  const transmissionTypes = category.available_transmissions || [
    ...new Set(availableCars.map((car) => car.transmission)),
  ];

  // Use category image or fallback to first car image
  const representativeImage =
    category.image_url || availableCars[0]?.image_url || "/placeholder.svg";

  const handleSelect = () => {
    onSelect(category.type || category);
  };

  // Use category data from database or fallback to generated info
  const categoryInfo = {
    title:
      category.name || `${(category.type || category).toUpperCase()} VEHICLES`,
    description: category.description || "Quality vehicles for your journey",
    icon:
      category.type === "SUV"
        ? "🚙"
        : category.type === "MPV"
        ? "🚐"
        : category.type === "Sport"
        ? "🏎️"
        : category.type === "Hatchback"
        ? "🚗"
        : category.type === "Coupe"
        ? "🚗"
        : category.type === "Sedan"
        ? "🚘"
        : "🚗",
  };

  return (
    <Card
      variant="elevated"
      className="category-card overflow-hidden cursor-pointer group flex flex-col h-full w-full max-w-sm mx-auto"
      onClick={() => onSelect(category.type || category)}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            onSelect(category.type || category)
          }
        }}
        role="button"
        tabIndex={0}
    >
      <CardContent className="p-0 flex flex-col flex-1">
        {/* Representative Image */}
        <div className="relative w-full h-48 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden flex items-center justify-center">
          <img
            src={representativeImage}
            alt={`${categoryInfo.title} category`}
            className="max-w-full max-h-full object-contain transition-transform duration-300 group-hover:scale-105"
          />
        </div>

        {/* Content Section */}
        <div className="p-6 text-center flex flex-col flex-1 justify-between">
          {/* Category Badge */}
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white font-bold text-lg">
                {categoryInfo.icon}
              </span>
            </div>
          </div>

          {/* Category Title */}
          <div className="mb-4">
            <CardTitle className="text-xl font-bold mb-2 tracking-wide text-gray-900 text-center">
              {categoryInfo.title}
            </CardTitle>
          </div>

          {/* Price Range */}
          <div className="mb-4 flex-1 flex flex-col justify-center">
            <div className="text-gray-600 text-sm mb-1">From</div>
            <div className="text-2xl font-bold text-gray-900 mb-1">
              ₱ {minPrice.toLocaleString()}
            </div>
            {minPrice !== maxPrice && (
              <div className="text-lg font-semibold text-gray-700">
                to ₱ {maxPrice.toLocaleString()}
              </div>
            )}
            <div className="text-gray-500 text-xs mt-1">per day</div>
          </div>

          {/* Transmission Info */}
          <div className="mb-6 flex flex-col items-center justify-center">
            <CardDescription className="text-sm text-gray-500 text-center">
              {transmissionTypes.join(" • ")} Transmission
            </CardDescription>
          </div>

          {/* Browse Now Button */}
          <div className="mt-auto">
            <Button
              type="button"
              onClick={handleSelect}
              className="w-full bg-white border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-semibold py-3 rounded-full transition-all duration-200 text-sm tracking-wide"
              variant="secondary"
            >
              BROWSE NOW
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
