{"$schema": "https://json.schemastore.org/package.json", "name": "ollie-track", "version": "1.0.0", "type": "module", "private": true, "scripts": {"build": "next build", "build:static": "next build", "build:analyze": "ANALYZE=true next build", "dev": "next dev", "dev:with-mqtt": "concurrently \"npm run dev\" \"npm run mqtt:consumer\"", "lint": "next lint", "start": "next start", "mqtt:consumer": "tsx src/services/mqtt-gps-consumer.ts", "mqtt:dev": "cross-env START_MQTT_CONSUMER=true tsx src/services/mqtt-gps-consumer.ts", "mqtt:debug": "node scripts/debug-mqtt-traffic.js", "mqtt:hunt-phantom": "node scripts/stop-phantom-gps.js", "mqtt:force-disconnect": "node scripts/force-disconnect-esp32.js", "mqtt:detect-devices": "node scripts/detect-esp32-devices.js", "azure:test": "node test-azure-iot.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:auth": "jest __tests__/auth", "test:auth-fixed": "jest __tests__/auth/*-fixed.test.tsx", "perf": "npm run build:analyze"}, "dependencies": {"@ai-sdk/openai": "^2.0.22", "@ai-sdk/react": "^2.0.26", "@hookform/resolvers": "^3.10.0", "@next/bundle-analyzer": "^15.5.2", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.54.0", "@types/leaflet": "^1.9.20", "ai": "^5.0.26", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "geist": "latest", "input-otp": "1.4.1", "leaflet": "^1.9.4", "lucide-react": "^0.454.0", "mqtt": "^5.14.0", "next": "15.5.2", "next-themes": "^0.4.6", "openai": "^5.16.0", "react": "^19", "react-day-picker": "9.8.0", "react-dom": "^19", "react-hook-form": "^7.60.0", "react-leaflet": "^5.0.0", "react-resizable-panels": "^2.1.7", "recharts": "latest", "sonner": "^1.7.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.5", "vaul": "^1.1.2", "web-vitals": "^5.1.0", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.9", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "concurrently": "^9.1.0", "cross-env": "^10.0.0", "dotenv": "^17.2.1", "eslint": "^9.33.0", "eslint-config-next": "15.5.2", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8.5", "tailwindcss": "^4.1.9", "tsx": "^4.19.2", "tw-animate-css": "1.3.3", "typescript": "^5", "uuid": "^11.1.0"}}