# Ollie Track – Car Rental Management System

Version: 1.0  
Date: 2025-08-08  
Prepared by: [<PERSON>]

## Overview
Ollie Track is a responsive web-based car rental management system designed for both customers and administrators. The platform enables users to browse available cars, book rentals, manage reservations, and make payments online. Administrators can manage inventory, bookings, and car status via a secure backend. The system will be built with React + Vite (frontend) and Convex (backend).

## Goals
- Provide an intuitive, mobile-first booking experience for customers.
- Offer real-time availability and pricing.
- Enable admins to manage cars, bookings, and payments efficiently.
- Ensure scalable, modular architecture for future expansion.

## Features & Requirements

### Customer-Facing UI

#### Booking Widget (Homepage)
- Dropdown for pick-up method:
  - “Pick-up at <PERSON><PERSON>’s Garage”
  - “Drop-off Delivery” (reveals delivery location dropdown)
- Location selector (city or delivery address)
- Pick-up date & time selectors
- Drop-off date & time selectors
- Car model selector (optional at this stage)
- Special requests text area
- “Search” or “View Available Cars” button

#### Car Catalog Page
- Search & filter options:
  - Car type (SUV, Sport, Coupe, etc.)
  - Availability dates
  - Condition (Good, Needs Repair)
- Car cards with:
  - Image
  - Name & type
  - Rental cost/day (with discount if applicable)
  - Transmission, fuel capacity, seating capacity
  - “Rent Now” button
- Responsive grid layout

#### Booking Summary Page
- Display selected car details
- Pick-up & drop-off details
- Total rental cost (including discounts, if any)
- “Confirm Booking” button
- Payment section (integrated with Stripe/PayMongo API)
- Error handling for:
  - Car unavailable
  - Invalid date/time selection

#### User Dashboard
- Current bookings (status: Active, Completed, Cancelled)
- Past bookings history
- Payment history
- Cancel booking option (if within allowed cancellation window)
- Download invoice option

### Admin-Facing UI

#### Dashboard
- Summary widgets:
  - Cars currently rented
  - Cars under maintenance
  - Recent bookings
  - Revenue overview
- Alerts for:
  - Overlapping bookings
  - Cars needing maintenance
  - Payment issues

#### Car Management
- CRUD for car records:
  - Model, type, plate number
  - Status (Available, Booked, Unavailable)
  - Condition (Good, Needs Repair)
  - Availability window
  - Image upload
  - Maintenance notes

#### Booking Management
- View all bookings
- Edit/update booking status
- Archive booking histories
- Manually adjust availability

#### Payment Management
- View all transactions
- Payment status (Pending, Paid, Failed)
- Refund processing

### Backend Requirements (Convex)

#### Data Models

##### Cars
- id
- model
- type
- plateNumber
- status
- condition
- availability
- fuelCapacity
- transmission
- seats
- pricePerDay
- imageURL

##### Bookings
- id
- customerId
- carId
- pickUpLocation
- dropOffLocation
- pickUpDateTime
- dropOffDateTime
- specialRequests
- status

##### Users
- id
- name
- email
- phone
- role

##### Payments
- id
- bookingId
- amount
- status
- method
- transactionDate

#### APIs
- CRUD for cars, bookings, users
- Availability check logic
- Payment integration endpoint

#### Validation & Security
- Sanitize all inputs
- Role-based access control (Admin/Customer)
- Error handling with meaningful messages

## Technical Stack
- Frontend: React + Vite, Tailwind CSS for styling
- Backend: Convex for real-time database & functions
- Payment API: Stripe or PayMongo
- Hosting: Vercel/Netlify (Frontend), Convex cloud (Backend)
- Auth: JWT or OAuth2 (Convex Auth)

## Non-Functional Requirements
- Fully responsive (mobile, tablet, desktop)
- Fast loading (under 2s for main pages)
- Accessible design (WCAG 2.1 compliant)
- Scalable for >10,000 concurrent users

## Milestones
- Week 1–2: UI prototyping (Customer side)
- Week 3: Admin panel base setup
- Week 4: Convex backend setup with CRUD
- Week 5: Payment integration
- Week 6: Testing & bug fixes
- Week 7: Deployment