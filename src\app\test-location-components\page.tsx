"use client";

import React from "react";
import {
  FixedPickupLocationField,
  DropOffLocationDropdown,
  FIXED_PICKUP_LOCATION,
  DROP_OFF_LOCATIONS,
} from "@/components/ui/booking-location-components";

function TestLocationComponents() {
  const [dropOffLocation, setDropOffLocation] = React.useState("");

  return (
    <div className="p-8 space-y-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold">Location Components Test</h1>

      <div>
        <h2 className="text-lg font-semibold mb-4">Fixed Pickup Location</h2>
        <FixedPickupLocationField showLabel={true} />
        <p className="text-sm text-gray-600 mt-2">
          Value: {FIXED_PICKUP_LOCATION}
        </p>
      </div>

      <div>
        <h2 className="text-lg font-semibold mb-4">
          Drop-off Location Selection
        </h2>
        <DropOffLocationDropdown
          value={dropOffLocation}
          onValueChange={setDropOffLocation}
          placeholder="Select drop-off location"
        />
        <p className="text-sm text-gray-600 mt-2">
          Selected: {dropOffLocation || "None"}
        </p>
      </div>

      <div>
        <h2 className="text-lg font-semibold mb-4">
          Available Drop-off Locations
        </h2>
        <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
          {DROP_OFF_LOCATIONS.map((location, index) => (
            <li key={index}>{location}</li>
          ))}
        </ul>
      </div>
    </div>
  );
}

export default TestLocationComponents;
