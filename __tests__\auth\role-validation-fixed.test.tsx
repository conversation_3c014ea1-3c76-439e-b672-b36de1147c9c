/**
 * Role Validation Test Suite
 * 
 * Tests role-based authentication validation and prevents 
 * cross-context contamination between admin and customer contexts.
 */

import { render, screen, waitFor, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import React from 'react'
import { createBrowserClient } from '@supabase/ssr'
import { CustomerAuthProvider } from '../../src/components/auth/customer-auth-context'
import { AdminAuthProvider } from '../../src/components/auth/admin-auth-context'

// Mock Supabase client
jest.mock('@supabase/ssr', () => ({
  createBrowserClient: jest.fn()
}))

const mockCreateBrowserClient = createBrowserClient as jest.MockedFunction<typeof createBrowserClient>

// Test component to check role validation
const RoleValidationTestComponent = ({ context }: { context: 'customer' | 'admin' }) => {
  const [authState, setAuthState] = React.useState<any>(null)
  
  React.useEffect(() => {
    try {
      if (context === 'customer') {
        const { useCustomerAuth } = require('../../src/components/auth/customer-auth-context')
        const auth = useCustomerAuth()
        setAuthState(auth)
      } else {
        const { useAdminAuth } = require('../../src/components/auth/admin-auth-context')
        const auth = useAdminAuth()
        setAuthState(auth)
      }
    } catch (error) {
      // Context not available
      setAuthState({ 
        session: null, 
        user: null, 
        profile: null, 
        isCustomer: false, 
        isAdmin: false,
        loading: false 
      })
    }
  }, [context])

  if (!authState) {
    return <div data-testid="loading">Loading...</div>
  }

  return (
    <div>
      <div data-testid="context-type">{context}</div>
      <div data-testid="user-email">{authState.user?.email || 'No user'}</div>
      <div data-testid="user-role">{authState.profile?.role || 'No role'}</div>
      <div data-testid="role-check">
        {(context === 'customer' && authState.isCustomer) || (context === 'admin' && authState.isAdmin) 
          ? 'authorized' 
          : 'unauthorized'}
      </div>
    </div>
  )
}

describe('Role Validation Tests', () => {
  let mockSupabaseCustomer: any
  let mockSupabaseAdmin: any

  beforeEach(() => {
    // Reset all mocks and clear storage
    jest.clearAllMocks()
    window.localStorage.clear()
    window.sessionStorage.clear()
    
    // Create separate mock clients
    mockSupabaseCustomer = {
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        onAuthStateChange: jest.fn().mockReturnValue({
          data: { subscription: { unsubscribe: jest.fn() } }
        }),
        signInWithPassword: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn().mockResolvedValue({ error: null }),
        resetPasswordForEmail: jest.fn(),
        verifyOtp: jest.fn(),
        resend: jest.fn()
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } })
          })
        })
      })
    }

    mockSupabaseAdmin = {
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        onAuthStateChange: jest.fn().mockReturnValue({
          data: { subscription: { unsubscribe: jest.fn() } }
        }),
        signInWithPassword: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn().mockResolvedValue({ error: null }),
        resetPasswordForEmail: jest.fn(),
        verifyOtp: jest.fn(),
        resend: jest.fn()
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } })
          })
        })
      })
    }

    // Setup createBrowserClient mock
    mockCreateBrowserClient.mockImplementation((url, key, options) => {
      if (options?.auth?.storageKey === 'sb-customer-auth-token') {
        return mockSupabaseCustomer
      } else if (options?.auth?.storageKey === 'sb-admin-auth-token') {
        return mockSupabaseAdmin
      }
      return mockSupabaseCustomer
    })
  })

  test('should reject admin user in customer context', async () => {
    // Setup admin user session
    const adminSession = {
      access_token: 'admin-token',
      user: { id: 'admin-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: adminSession },
      error: null
    })

    // Customer context gets admin profile and should reject it
    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
      error: null
    })

    await act(async () => {
      render(
        <CustomerAuthProvider>
          <RoleValidationTestComponent context="customer" />
        </CustomerAuthProvider>
      )
    })

    // Wait for authentication processing
    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
    })

    // Customer context should reject admin users and sign out
    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.signOut).toHaveBeenCalled()
    })

    // Component should show unauthorized state
    await waitFor(() => {
      expect(screen.getByTestId('role-check')).toHaveTextContent('unauthorized')
    })
  })

  test('should reject customer user in admin context', async () => {
    // Setup customer user session
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    mockSupabaseAdmin.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    // Admin context gets customer profile and should reject it
    mockSupabaseAdmin.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    await act(async () => {
      render(
        <AdminAuthProvider>
          <RoleValidationTestComponent context="admin" />
        </AdminAuthProvider>
      )
    })

    // Wait for authentication processing
    await waitFor(() => {
      expect(mockSupabaseAdmin.auth.getSession).toHaveBeenCalled()
    })

    // Admin context should reject customer users and sign out
    await waitFor(() => {
      expect(mockSupabaseAdmin.auth.signOut).toHaveBeenCalled()
    })

    // Component should show unauthorized state
    await waitFor(() => {
      expect(screen.getByTestId('role-check')).toHaveTextContent('unauthorized')
    })
  })

  test('should allow legitimate customer user in customer context', async () => {
    // Setup customer user session
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    // Customer context gets customer profile - should work
    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    await act(async () => {
      render(
        <CustomerAuthProvider>
          <RoleValidationTestComponent context="customer" />
        </CustomerAuthProvider>
      )
    })

    // Should NOT call signOut for legitimate customer
    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
    })

    // Verify signOut was NOT called
    expect(mockSupabaseCustomer.auth.signOut).not.toHaveBeenCalled()
  })

  test('should allow legitimate admin user in admin context', async () => {
    // Setup admin user session
    const adminSession = {
      access_token: 'admin-token',
      user: { id: 'admin-1', email: '<EMAIL>' }
    }

    mockSupabaseAdmin.auth.getSession.mockResolvedValue({
      data: { session: adminSession },
      error: null
    })

    // Admin context gets admin profile - should work
    mockSupabaseAdmin.from().select().eq().single.mockResolvedValue({
      data: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
      error: null
    })

    await act(async () => {
      render(
        <AdminAuthProvider>
          <RoleValidationTestComponent context="admin" />
        </AdminAuthProvider>
      )
    })

    // Should NOT call signOut for legitimate admin
    await waitFor(() => {
      expect(mockSupabaseAdmin.auth.getSession).toHaveBeenCalled()
    })

    // Verify signOut was NOT called
    expect(mockSupabaseAdmin.auth.signOut).not.toHaveBeenCalled()
  })

  test('should handle special case admin email in customer context', async () => {
    // Setup special admin email (<EMAIL>)
    const specialAdminSession = {
      access_token: 'admin-token',
      user: { id: 'admin-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: specialAdminSession },
      error: null
    })

    // Even with special email, customer context should reject admin users
    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
      error: null
    })

    await act(async () => {
      render(
        <CustomerAuthProvider>
          <RoleValidationTestComponent context="customer" />
        </CustomerAuthProvider>
      )
    })

    // Customer context should still reject even special admin email
    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.signOut).toHaveBeenCalled()
    })
  })

  test('should handle missing profile data gracefully', async () => {
    // Setup session with user but no profile
    const sessionWithoutProfile = {
      access_token: 'token',
      user: { id: 'user-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: sessionWithoutProfile },
      error: null
    })

    // Profile fetch returns error (not found)
    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: null,
      error: { message: 'Profile not found' }
    })

    await act(async () => {
      render(
        <CustomerAuthProvider>
          <RoleValidationTestComponent context="customer" />
        </CustomerAuthProvider>
      )
    })

    // Should handle gracefully without crashing
    await waitFor(() => {
      expect(screen.getByTestId('context-type')).toHaveTextContent('customer')
    })

    // Should show unauthorized since no profile found
    await waitFor(() => {
      expect(screen.getByTestId('role-check')).toHaveTextContent('unauthorized')
    })
  })

  test('should handle concurrent role validation across contexts', async () => {
    // Setup different valid sessions
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    const adminSession = {
      access_token: 'admin-token',
      user: { id: 'admin-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseAdmin.auth.getSession.mockResolvedValue({
      data: { session: adminSession },
      error: null
    })

    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    mockSupabaseAdmin.from().select().eq().single.mockResolvedValue({
      data: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
      error: null
    })

    await act(async () => {
      render(
        <div>
          <div data-testid="customer-wrapper">
            <CustomerAuthProvider>
              <RoleValidationTestComponent context="customer" />
            </CustomerAuthProvider>
          </div>
          <div data-testid="admin-wrapper">
            <AdminAuthProvider>
              <RoleValidationTestComponent context="admin" />
            </AdminAuthProvider>
          </div>
        </div>
      )
    })

    // Both contexts should initialize independently
    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
      expect(mockSupabaseAdmin.auth.getSession).toHaveBeenCalled()
    })

    // Both should work without interfering with each other
    await waitFor(() => {
      const customerContext = screen.getByTestId('customer-wrapper')
      const adminContext = screen.getByTestId('admin-wrapper')
      
      expect(customerContext).toBeInTheDocument()
      expect(adminContext).toBeInTheDocument()
    })
  })
})
