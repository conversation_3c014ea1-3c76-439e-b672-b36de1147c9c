"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { 
  User, 
  UserPlus, 
  Lock, 
  Car, 
  CheckCircle,
  ArrowRight,
  Shield,
  X
} from "lucide-react";

interface AuthRequiredModalProps {
  isOpen: boolean;
  onClose: () => void;
  action?: string; // e.g., "book this car", "start booking"
  carModel?: string;
}

export function AuthRequiredModal({ 
  isOpen, 
  onClose, 
  action = "continue with booking",
  carModel 
}: AuthRequiredModalProps) {
  const router = useRouter();

  const handleLogin = () => {
    onClose();
    router.push("/customer/login");
  };

  const handleSignup = () => {
    onClose();
    router.push("/customer/signup");
  };

  const benefits = [
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      text: "Secure booking management"
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      text: "Track your rental history"
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      text: "Faster future bookings"
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      text: "Exclusive member offers"
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md mx-auto p-0 gap-0 overflow-hidden">
        {/* Header with gradient background */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-8 text-center relative">
          <Button 
            variant="secondary" 
            size="icon" 
            className="absolute right-2 top-2 flex items-center justify-center h-8 w-8 rounded-full bg-white/20 hover:bg-white/30 text-white" 
            onClick={onClose}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
          <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
            <Shield className="h-8 w-8 text-white" />
          </div>
          <DialogTitle className="text-xl font-bold text-white mb-2">
            Account Required
          </DialogTitle>
          <DialogDescription className="text-blue-100">
            Please sign in or create an account to {action}
            {carModel && (
              <span className="block mt-1 font-medium text-white">
                {carModel}
              </span>
            )}
          </DialogDescription>
        </div>

        {/* Content */}
        <div className="px-6 py-6">
          {/* Benefits section */}
          <div className="mb-6">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">
              Why create an account?
            </h3>
            <div className="grid grid-cols-1 gap-2">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-3">
                  {benefit.icon}
                  <span className="text-sm text-gray-700">{benefit.text}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Action buttons */}
          <div className="space-y-3">
            {/* Sign Up - Primary action */}
            <Button
              onClick={handleSignup}
              className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 h-auto shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
            >
              <div className="flex items-center justify-center gap-2">
                <UserPlus className="h-5 w-5" />
                <span>Create Account</span>
                <ArrowRight className="h-4 w-4" />
              </div>
            </Button>

            {/* Sign In - Secondary action */}
            <Button
              onClick={handleLogin}
              variant="secondary"
              className="w-full border-2 border-gray-300 hover:border-blue-600 hover:text-blue-600 font-semibold py-3 h-auto transition-all duration-200"
            >
              <div className="flex items-center justify-center gap-2">
                <User className="h-5 w-5" />
                <span>Sign In</span>
              </div>
            </Button>
          </div>

          {/* Quick info */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-start gap-3">
              <Lock className="h-5 w-5 text-gray-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-900 mb-1">
                  Secure & Fast
                </p>
                <p className="text-xs text-gray-600 leading-relaxed">
                  Your information is protected with industry-standard security. 
                  Account creation takes less than 2 minutes.
                </p>
              </div>
            </div>
          </div>

          {/* Cancel option */}
          <div className="mt-4 text-center">
            <Button
              onClick={onClose}
              variant="link"
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Maybe later
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
