-- Safe migration script to add separate name fields to existing profiles table
-- This migration ONLY adds new columns without modifying existing data

-- Step 1: Add new columns to existing table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS first_name text,
ADD COLUMN IF NOT EXISTS middle_initial text,
ADD COLUMN IF NOT EXISTS last_name text;

-- Step 2: Add constraint for middle_initial length (optional field)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'profiles_middle_initial_check' 
        AND table_name = 'profiles'
    ) THEN
        ALTER TABLE public.profiles 
        ADD CONSTRAINT profiles_middle_initial_check 
        CHECK (middle_initial IS NULL OR length(middle_initial) <= 1);
    END IF;
END $$;

-- Step 3: Create indexes for new name fields (for performance)
CREATE INDEX IF NOT EXISTS idx_profiles_first_name ON public.profiles USING btree (first_name);
CREATE INDEX IF NOT EXISTS idx_profiles_last_name ON public.profiles USING btree (last_name);

-- Step 4: Add comments for documentation
COMMENT ON COLUMN public.profiles.first_name IS 'First name field for new customer settings form';
COMMENT ON COLUMN public.profiles.middle_initial IS 'Middle initial (optional, max 1 character)';
COMMENT ON COLUMN public.profiles.last_name IS 'Last name field for new customer settings form';

-- That's it! No automatic data modification.
-- The existing full_name field remains unchanged and your application will handle the logic.

-- Verification query (read-only) - run this after migration to confirm success:
-- SELECT 
--   count(*) as total_profiles,
--   count(first_name) as profiles_with_first_name,
--   count(middle_initial) as profiles_with_middle_initial,
--   count(last_name) as profiles_with_last_name
-- FROM public.profiles;
