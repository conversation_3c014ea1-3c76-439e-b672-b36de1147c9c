"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Calendar,
  Clock,
  Search,
  Filter,
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  Car,
  RefreshCw,
  AlertTriangle,
} from "lucide-react";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import { ExtensionApprovalDrawer } from "./extension-approval-drawer";
import { getPendingExtensionRequests } from "@/lib/services/booking-extension-service";

interface ExtensionRequest {
  id: string;
  booking_id: string;
  customer_id: string;
  original_dropoff_datetime: string;
  requested_dropoff_datetime: string;
  extension_duration_hours: number;
  additional_amount: number;
  request_reason?: string;
  request_notes?: string;
  status: string;
  has_conflicts: boolean;
  alternative_cars?: string[];
  alternative_suggestions?: any;
  created_at: string;
  expires_at: string;
  bookings: {
    id: string;
    booking_ref: string;
    pickup_datetime: string;
    dropoff_datetime: string;
    total_amount: number;
    status: string;
    cars: {
      model: string;
      plate_number: string;
      type: string;
    };
  };
  profiles: {
    full_name: string;
    email: string;
    phone?: string;
  };
}

interface ExtensionRequestsSectionProps {
  currentAdminId: string;
}

// Mobile Extension Request Card Component
function ExtensionRequestCard({ 
  request, 
  onViewDetails 
}: { 
  request: ExtensionRequest; 
  onViewDetails: (request: ExtensionRequest) => void;
}) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-PH", {
      style: "currency",
      currency: "PHP",
    }).format(amount);
  };

  const formatDateTime = (dateString: string) => {
    return format(new Date(dateString), "MMM dd, h:mm a");
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return "bg-yellow-100 text-yellow-700 border-yellow-200";
      case "approved":
        return "bg-green-100 text-green-700 border-green-200";
      case "rejected":
        return "bg-red-100 text-red-700 border-red-200";
      case "expired":
        return "bg-gray-100 text-gray-700 border-gray-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return <Clock className="h-3 w-3" />;
      case "approved":
        return <CheckCircle className="h-3 w-3" />;
      case "rejected":
        return <XCircle className="h-3 w-3" />;
      case "expired":
        return <AlertCircle className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const isExpired = new Date(request.expires_at) < new Date();
  const actualStatus = isExpired && request.status === "pending" ? "expired" : request.status;

  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        {/* Top Section: Customer and Status */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <User className="h-4 w-4 text-gray-500 flex-shrink-0" />
              <span className="font-semibold text-sm truncate">{request.profiles.full_name}</span>
            </div>
            <p className="text-xs text-gray-600 truncate">{request.profiles.email}</p>
            <p className="text-xs text-gray-500 font-mono">ID: {request.id.slice(-8).toUpperCase()}</p>
          </div>
          <div className="flex flex-col items-end gap-1 ml-2">
            <Badge className={cn("text-xs border", getStatusColor(actualStatus))}>
              {getStatusIcon(actualStatus)}
              <span className="ml-1 capitalize">{actualStatus}</span>
            </Badge>
            {request.has_conflicts && (
              <Badge variant="secondary" className="bg-amber-100 text-amber-700 border-amber-200 text-xs">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Conflicts
              </Badge>
            )}
          </div>
        </div>

        {/* Middle Section: Booking and Vehicle Info */}
        <div className="space-y-2 mb-3">
          <div className="flex items-center gap-2 text-sm">
            <Car className="h-4 w-4 text-gray-500" />
            <span className="font-medium">{request.bookings.cars.model}</span>
            <span className="text-gray-500">•</span>
            <span className="text-gray-600 font-mono text-xs">{request.bookings.booking_ref}</span>
          </div>
          <div className="text-xs text-gray-600">
            <p>Plate: {request.bookings.cars.plate_number}</p>
          </div>
        </div>

        {/* Extension Details */}
        <div className="bg-blue-50 p-3 rounded-lg mb-3">
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-600">Extension:</span>
              <p className="font-semibold text-blue-700">{request.extension_duration_hours}h</p>
            </div>
            <div>
              <span className="text-gray-600">Cost:</span>
              <p className="font-semibold text-green-600">{formatCurrency(request.additional_amount)}</p>
            </div>
            <div>
              <span className="text-gray-600">New Return:</span>
              <p className="font-medium">{formatDateTime(request.requested_dropoff_datetime)}</p>
            </div>
            <div>
              <span className="text-gray-600">Submitted:</span>
              <p className="font-medium">{formatDateTime(request.created_at)}</p>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className="flex gap-2">
          <Button
            onClick={() => onViewDetails(request)}
            size="sm"
            className="flex-1"
            variant={actualStatus === "pending" && !isExpired ? "primary" : "secondary"}
          >
            <Eye className="h-4 w-4 mr-2" />
            {actualStatus === "pending" && !isExpired ? "Review Request" : "View Details"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

export function ExtensionRequestsSection({ currentAdminId }: ExtensionRequestsSectionProps) {
  const { toast } = useToast();
  const isMobile = useIsMobile();

  const [requests, setRequests] = React.useState<ExtensionRequest[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [statusFilter, setStatusFilter] = React.useState<string>("all");
  const [selectedRequest, setSelectedRequest] = React.useState<ExtensionRequest | null>(null);
  const [drawerOpen, setDrawerOpen] = React.useState(false);

  const fetchRequests = React.useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await getPendingExtensionRequests();
      
      if (error) {
        throw new Error(error.message || "Failed to fetch extension requests");
      }

      // Update expired requests
      const updatedRequests = (data || []).map((request: ExtensionRequest) => {
        const isExpired = new Date(request.expires_at) < new Date();
        return {
          ...request,
          status: isExpired && request.status === "pending" ? "expired" : request.status
        };
      });

      setRequests(updatedRequests);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Loading Failed",
        description: error.message,
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  React.useEffect(() => {
    fetchRequests();
  }, [fetchRequests]);

  const handleViewDetails = (request: ExtensionRequest) => {
    setSelectedRequest(request);
    setDrawerOpen(true);
  };

  const handleRequestProcessed = () => {
    fetchRequests(); // Refresh the list
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-PH", {
      style: "currency",
      currency: "PHP",
    }).format(amount);
  };

  const formatDateTime = (dateString: string) => {
    return format(new Date(dateString), "MMM dd, yyyy 'at' h:mm a");
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return "bg-yellow-100 text-yellow-700 border-yellow-200";
      case "approved":
        return "bg-green-100 text-green-700 border-green-200";
      case "rejected":
        return "bg-red-100 text-red-700 border-red-200";
      case "expired":
        return "bg-gray-100 text-gray-700 border-gray-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return <Clock className="h-3 w-3" />;
      case "approved":
        return <CheckCircle className="h-3 w-3" />;
      case "rejected":
        return <XCircle className="h-3 w-3" />;
      case "expired":
        return <AlertCircle className="h-3 w-3" />;
      default:
        return null;
    }
  };

  // Filter requests based on search and status
  const filteredRequests = requests.filter((request) => {
    const matchesSearch = !searchQuery || 
      request.profiles.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.profiles.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.bookings.booking_ref.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.bookings.cars.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.bookings.cars.plate_number.toLowerCase().includes(searchQuery.toLowerCase());

    const actualStatus = new Date(request.expires_at) < new Date() && request.status === "pending" ? "expired" : request.status;
    const matchesStatus = statusFilter === "all" || actualStatus === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const pendingCount = requests.filter(r => {
    const isExpired = new Date(r.expires_at) < new Date();
    return r.status === "pending" && !isExpired;
  }).length;

  const conflictsCount = requests.filter(r => r.has_conflicts && r.status === "pending").length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Extension Requests</h2>
          <p className="text-gray-600 text-sm">
            Review and manage booking extension requests from customers
          </p>
        </div>
        <Button onClick={fetchRequests} disabled={loading} size="sm">
          <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
          {!isMobile && <span className="ml-2">Refresh</span>}
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{pendingCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
              <div>
                <p className="text-sm text-gray-600">Conflicts</p>
                <p className="text-2xl font-bold text-amber-600">{conflictsCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total</p>
                <p className="text-2xl font-bold">{requests.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Processed</p>
                <p className="text-2xl font-bold text-green-600">
                  {requests.filter(r => r.status === "approved" || r.status === "rejected").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by customer, booking ref, or vehicle..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="md:w-48">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Requests List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Extension Requests ({filteredRequests.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-600">Loading requests...</span>
            </div>
          ) : filteredRequests.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Extension Requests</h3>
              <p className="text-gray-600">
                {searchQuery || statusFilter !== "all" 
                  ? "No requests match your current filters."
                  : "There are no extension requests at the moment."
                }
              </p>
            </div>
          ) : isMobile ? (
            <div className="p-4">
              {filteredRequests.map((request) => (
                <ExtensionRequestCard
                  key={request.id}
                  request={request}
                  onViewDetails={handleViewDetails}
                />
              ))}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Customer</TableHead>
                  <TableHead>Booking</TableHead>
                  <TableHead>Vehicle</TableHead>
                  <TableHead>Extension</TableHead>
                  <TableHead>Cost</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Submitted</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRequests.map((request) => {
                  const isExpired = new Date(request.expires_at) < new Date();
                  const actualStatus = isExpired && request.status === "pending" ? "expired" : request.status;
                  
                  return (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium text-sm">{request.profiles.full_name}</p>
                          <p className="text-xs text-gray-600">{request.profiles.email}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-mono text-sm">{request.bookings.booking_ref}</p>
                          <p className="text-xs text-gray-600">ID: {request.id.slice(-8).toUpperCase()}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium text-sm">{request.bookings.cars.model}</p>
                          <p className="text-xs text-gray-600">{request.bookings.cars.plate_number}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-blue-600">{request.extension_duration_hours}h</span>
                          {request.has_conflicts && (
                            <Badge variant="secondary" className="bg-amber-100 text-amber-700 border-amber-200 text-xs">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Conflicts
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-semibold text-green-600">
                          {formatCurrency(request.additional_amount)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge className={cn("text-xs border", getStatusColor(actualStatus))}>
                          {getStatusIcon(actualStatus)}
                          <span className="ml-1 capitalize">{actualStatus}</span>
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm">{formatDateTime(request.created_at)}</p>
                      </TableCell>
                      <TableCell>
                        <Button
                          onClick={() => handleViewDetails(request)}
                          size="sm"
                          variant={actualStatus === "pending" && !isExpired ? "primary" : "secondary"}
                        >
                          <Eye className="h-4 w-4" />
                          {!isMobile && (
                            <span className="ml-2">
                              {actualStatus === "pending" && !isExpired ? "Review" : "View"}
                            </span>
                          )}
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Extension Approval Drawer */}
      <ExtensionApprovalDrawer
        extensionRequest={selectedRequest}
        isOpen={drawerOpen}
        onClose={() => {
          setDrawerOpen(false);
          setSelectedRequest(null);
        }}
        onRequestProcessed={handleRequestProcessed}
        currentAdminId={currentAdminId}
      />
    </div>
  );
}
