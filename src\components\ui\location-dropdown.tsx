"use client"

import * as React from "react"
import { MapPin, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { FLAT_PHILIPPINES_LOCATIONS, POPULAR_DESTINATIONS, getLocationsByRegion } from "@/lib/philippines-locations"

interface LocationDropdownProps {
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  showSearchable?: boolean
  showGrouped?: boolean
  showPopularFirst?: boolean
}

export function LocationDropdown({
  value,
  onValueChange,
  placeholder = "Select location",
  className,
  disabled = false,
  showSearchable = false,
  showGrouped = true,
  showPopularFirst = true
}: LocationDropdownProps) {
  const [searchTerm, setSearchTerm] = React.useState("")
  const [isOpen, setIsOpen] = React.useState(false)

  const filteredLocations = React.useMemo(() => {
    if (!searchTerm) return FLAT_PHILIPPINES_LOCATIONS
    
    return FLAT_PHILIPPINES_LOCATIONS.filter(location =>
      location.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      location.region.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [searchTerm])

  const groupedLocations = React.useMemo(() => {
    if (!showGrouped) return null
    
    const regions = getLocationsByRegion()
    
    if (searchTerm) {
      return regions
        .map(region => ({
          ...region,
          cities: region.cities.filter(city =>
            city.toLowerCase().includes(searchTerm.toLowerCase())
          )
        }))
        .filter(region => region.cities.length > 0)
    }
    
    return regions
  }, [showGrouped, searchTerm])

  const popularLocations = React.useMemo(() => {
    if (!showPopularFirst) return []
    
    return POPULAR_DESTINATIONS.filter(location =>
      !searchTerm || location.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [showPopularFirst, searchTerm])

  const handleValueChange = (newValue: string) => {
    onValueChange?.(newValue)
    setIsOpen(false)
  }

  const renderContent = () => {
    if (showGrouped && groupedLocations) {
      return (
        <>
          {showPopularFirst && popularLocations.length > 0 && (
            <SelectGroup>
              <SelectLabel className="flex items-center gap-2 text-blue-600 font-medium px-2 py-2 text-sm">
                <MapPin className="w-3 h-3" />
                Popular Destinations
              </SelectLabel>
              {popularLocations.map((location) => (
                <SelectItem key={`popular-${location}`} value={location} className="py-2 px-2 hover:bg-blue-50">
                  {location}
                </SelectItem>
              ))}
            </SelectGroup>
          )}
          
          {groupedLocations.map((region) => (
            <SelectGroup key={region.region}>
              <SelectLabel className="text-xs text-gray-500 uppercase tracking-wide px-2 py-2">
                {region.region}
              </SelectLabel>
              {region.cities
                .filter(city => !showPopularFirst || !popularLocations.includes(city))
                .map((city) => (
                <SelectItem key={`${region.region}-${city}`} value={city} className="py-2 px-2 hover:bg-gray-50">
                  {city}
                </SelectItem>
              ))}
            </SelectGroup>
          ))}
        </>
      )
    }

    // Simple flat list
    return (
      <>
        {showPopularFirst && popularLocations.length > 0 && (
          <SelectGroup>
            <SelectLabel className="flex items-center gap-2 text-blue-600">
              <MapPin className="w-3 h-3" />
              Popular Destinations
            </SelectLabel>
            {popularLocations.map((location) => (
              <SelectItem key={`popular-${location}`} value={location}>
                {location}
              </SelectItem>
            ))}
          </SelectGroup>
        )}
        
        {filteredLocations
          .filter(location => !popularLocations.includes(location.label))
          .map((location) => (
            <SelectItem key={location.value} value={location.value}>
              {location.label}
            </SelectItem>
          ))}
      </>
    )
  }

  return (
    <Select
      value={value}
      onValueChange={handleValueChange}
      disabled={disabled}
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <SelectTrigger className={cn(
        "w-full h-10 border border-gray-300 bg-white text-gray-900 hover:border-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}>
        <div className="flex items-center gap-2 w-full">
          <MapPin className="w-4 h-4 text-gray-400 shrink-0" />
          <SelectValue placeholder={placeholder} className="text-gray-900 placeholder:text-gray-500" />
        </div>
      </SelectTrigger>
      <SelectContent className="max-h-80 bg-white border border-gray-200 shadow-lg">
        {showSearchable && (
          <div className="p-2 border-b border-gray-100">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search locations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 h-8 border border-gray-200 bg-white"
              />
            </div>
          </div>
        )}
        
        {filteredLocations.length === 0 && searchTerm ? (
          <div className="p-4 text-center text-gray-500">
            No locations found for "{searchTerm}"
          </div>
        ) : (
          renderContent()
        )}
      </SelectContent>
    </Select>
  )
}

// Simplified version for basic usage
export function SimpleLocationDropdown({
  value,
  onValueChange,
  placeholder = "Select location",
  className,
  disabled = false
}: Omit<LocationDropdownProps, 'showSearchable' | 'showGrouped' | 'showPopularFirst'>) {
  return (
    <LocationDropdown
      value={value}
      onValueChange={onValueChange}
      placeholder={placeholder}
      className={cn("h-10 border border-gray-300 bg-white", className)}
      disabled={disabled}
      showSearchable={false}
      showGrouped={false}
      showPopularFirst={true}
    />
  )
}
