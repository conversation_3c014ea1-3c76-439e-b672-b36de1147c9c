-- Fix MITSU<PERSON><PERSON>H<PERSON> MIRAGE GLS 2019 AT car status from RENTED to Available
-- This script addresses the issue where booking data was manually deleted but car status remained RENTED

-- Update the specific car status to Available
UPDATE cars 
SET status = 'Available', 
    updated_at = now()
WHERE model ILIKE '%MITSUBISHI MIRAGE GLS%' 
   OR model <PERSON><PERSON><PERSON> '%MIRAGE GLS%'
   OR (model ILIKE '%MITSUBISHI%' AND model ILIKE '%MIRAGE%' AND model ILIKE '%2019%');

-- Verify the update
SELECT id, model, status, updated_at 
FROM cars 
WHERE model ILIKE '%MITSUBISHI MIRAGE GLS%' 
   OR model ILIKE '%MIRAGE GLS%'
   OR (model ILI<PERSON> '%MITSUBISHI%' AND model <PERSON>I<PERSON> '%MIRAGE%' AND model ILIKE '%2019%');

-- Also check for any orphaned car statuses (cars marked as Rented but no active bookings)
SELECT c.id, c.model, c.status, c.plate_number
FROM cars c
LEFT JOIN bookings b ON c.id = b.car_id AND b.status IN ('Active', 'Pending')
WHERE c.status = 'Rented' AND b.id IS NULL;

-- Fix any orphaned cars (optional - uncomment if needed)
-- UPDATE cars 
-- SET status = 'Available', updated_at = now()
-- WHERE id IN (
--   SELECT c.id 
--   FROM cars c
--   LEFT JOIN bookings b ON c.id = b.car_id AND b.status IN ('Active', 'Pending')
--   WHERE c.status = 'Rented' AND b.id IS NULL
-- );
