/**
 * Environment-aware logging utility
 * Logs messages in development, removes them in production
 */

type LogLevel = 'log' | 'warn' | 'error' | 'info' | 'debug';

interface Logger {
  log: (...args: any[]) => void;
  warn: (...args: any[]) => void;
  error: (...args: any[]) => void;
  info: (...args: any[]) => void;
  debug: (...args: any[]) => void;
}

const createLogger = (): Logger => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const createLogFunction = (level: LogLevel) => {
    return (...args: any[]) => {
      if (isDevelopment) {
        console[level](...args);
      }
    };
  };

  return {
    log: createLogFunction('log'),
    warn: createLogFunction('warn'),
    error: createLogFunction('error'),
    info: createLogFunction('info'),
    debug: createLogFunction('debug'),
  };
};

// Export singleton logger instance
export const logger = createLogger();

// Helper function for conditional logging with context
export const logWithContext = (context: string, message: string, data?: any) => {
  logger.log(`[${context}]`, message, data || '');
};

// Helper for performance logging
export const logPerformance = (operation: string, startTime: number) => {
  const duration = Date.now() - startTime;
  logger.log(`⚡ ${operation} completed in ${duration}ms`);
};

// Helper for error logging with stack trace
export const logError = (context: string, error: Error | string, data?: any) => {
  if (typeof error === 'string') {
    logger.error(`❌ [${context}]`, error, data || '');
  } else {
    logger.error(`❌ [${context}]`, error.message, {
      stack: error.stack,
      data: data || {}
    });
  }
};
