import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { createContextClient } from '@/lib/supabase/server'
import { sendAdminAccountCreationEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('Admin create user API called');
    }
    
    // Verify the requesting user is super admin
    const supabase = await createContextClient('admin')
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Auth error:', authError);
      }
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('User authenticated:', user.email);
    }

    // Check if user is super admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (process.env.NODE_ENV === 'development') {
      console.log('Profile check:', { profile, profileError });
    }

    if (profileError || profile?.role !== 'super_admin') {
      return NextResponse.json({ error: 'Forbidden: Super admin access required' }, { status: 403 })
    }

    // Get request body
    const body = await request.json()
    const { email, password, full_name, phone, role } = body

    if (process.env.NODE_ENV === 'development') {
      console.log('Request body:', { email, full_name, phone, role });
    }

    if (!email || !password || !role) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Create admin client with service role
    const serviceUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!serviceUrl || !serviceKey) {
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 })
    }

    const adminClient = createClient(serviceUrl, serviceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Create auth user
    if (process.env.NODE_ENV === 'development') {
      console.log('Creating auth user for:', email);
    }
    const { data: authData, error: authUserError } = await adminClient.auth.admin.createUser({
      email,
      password,
      email_confirm: true
    })

    if (authUserError) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Auth user creation error:', authUserError);
      }
      return NextResponse.json({ error: authUserError.message }, { status: 400 })
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('Auth user created successfully:', authData.user.id);
    }

    // Check if profile already exists
    const { data: existingProfile } = await adminClient
      .from('profiles')
      .select('id')
      .eq('id', authData.user.id)
      .single()

    if (existingProfile) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Profile already exists, updating instead');
      }
      // Update existing profile
      const { error: profileUpdateError } = await adminClient
        .from('profiles')
        .update({
          email,
          full_name: full_name || null,
          phone: phone || null,
          role
        })
        .eq('id', authData.user.id)

      if (profileUpdateError) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Profile update error:', profileUpdateError);
        }
        return NextResponse.json({ error: profileUpdateError.message }, { status: 400 })
      }
    } else {
      // Create new profile
      if (process.env.NODE_ENV === 'development') {
        console.log('Creating profile for user:', authData.user.id);
      }
      const { error: profileCreateError } = await adminClient
        .from('profiles')
        .insert({
          id: authData.user.id,
          email,
          full_name: full_name || null,
          phone: phone || null,
          role
        })

      if (profileCreateError) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Profile creation error:', profileCreateError);
        }
        // If profile creation fails, clean up the auth user
        await adminClient.auth.admin.deleteUser(authData.user.id)
        return NextResponse.json({ error: profileCreateError.message }, { status: 400 })
      }
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('Profile created successfully');
    }

    // Get the super admin's profile for the "created by" field
    const { data: creatorProfile } = await supabase
      .from('profiles')
      .select('full_name, email')
      .eq('id', user.id)
      .single()

    // Send welcome email to the new admin
    if (role === 'admin') {
      try {
        const emailResult = await sendAdminAccountCreationEmail({
          adminEmail: email,
          adminName: full_name,
          temporaryPassword: password,
          createdBy: creatorProfile?.full_name || creatorProfile?.email || 'Super Administrator'
        })
        
        if (process.env.NODE_ENV === 'development') {
          console.log('Email notification result:', emailResult);
        }
      } catch (emailError) {
        console.error('Failed to send admin welcome email:', emailError);
        // Don't fail the user creation if email fails
      }
    }

    return NextResponse.json({ 
      success: true, 
      user: {
        id: authData.user.id,
        email: authData.user.email
      }
    })

  } catch (error: any) {
    console.error('Admin user creation error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 })
  }
}
