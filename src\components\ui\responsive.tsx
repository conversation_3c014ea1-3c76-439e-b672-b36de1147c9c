"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { useResponsive, type BreakpointKey, type ResponsiveValue } from "@/lib/responsive"

/**
 * Responsive Component System
 * 
 * Comprehensive responsive components that adapt to different screen sizes:
 * - Fluid layouts with proper grid ratios
 * - Responsive typography scaling
 * - Touch-optimized selection areas
 * - Content scalability (mobile-first approach)
 */

// Responsive Container Component
interface ResponsiveContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  maxWidth?: BreakpointKey | 'full' | 'none'
  padding?: ResponsiveValue<'none' | 'sm' | 'md' | 'lg' | 'xl'>
  centerContent?: boolean
}

export function ResponsiveContainer({
  maxWidth = '2xl',
  padding = { xs: 'md', md: 'lg', xl: 'xl' },
  centerContent = true,
  className,
  children,
  ...props
}: ResponsiveContainerProps) {
  const maxWidthClass = {
    xs: 'max-w-none sm:max-w-sm',
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-4xl',
    '4xl': 'max-w-6xl',
    full: 'max-w-full',
    none: 'max-w-none',
  }[maxWidth]

  const paddingClass = typeof padding === 'object' 
    ? Object.entries(padding).map(([bp, value]) => {
        const prefix = bp === 'xs' ? '' : `${bp}:`
        const paddingValue = {
          none: 'p-0',
          sm: 'p-4',
          md: 'p-6',
          lg: 'p-8',
          xl: 'p-12',
        }[value as string]
        return `${prefix}${paddingValue}`
      }).join(' ')
    : {
        none: 'p-0',
        sm: 'p-4',
        md: 'p-6', 
        lg: 'p-8',
        xl: 'p-12',
      }[padding as string]

  return (
    <div
      className={cn(
        'w-full',
        maxWidthClass,
        paddingClass,
        centerContent && 'mx-auto',
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Responsive Grid Component
interface ResponsiveGridProps extends React.HTMLAttributes<HTMLDivElement> {
  columns?: ResponsiveValue<1 | 2 | 3 | 4 | 5 | 6>
  gap?: ResponsiveValue<'sm' | 'md' | 'lg' | 'xl'>
  equalHeight?: boolean
}

export function ResponsiveGrid({
  columns = { xs: 1, sm: 2, lg: 3, xl: 4 },
  gap = { xs: 'md', md: 'lg' },
  equalHeight = true,
  className,
  children,
  ...props
}: ResponsiveGridProps) {
  const getColumnClass = (cols: number, breakpoint: string = '') => {
    const prefix = breakpoint ? `${breakpoint}:` : ''
    const colsMap = {
      1: `${prefix}grid-cols-1`,
      2: `${prefix}grid-cols-2`,
      3: `${prefix}grid-cols-3`,
      4: `${prefix}grid-cols-4`,
      5: `${prefix}grid-cols-5`,
      6: `${prefix}grid-cols-6`,
    }
    return colsMap[cols as keyof typeof colsMap]
  }

  const getGapClass = (gapSize: string, breakpoint: string = '') => {
    const prefix = breakpoint ? `${breakpoint}:` : ''
    const gapMap = {
      sm: `${prefix}gap-3`,
      md: `${prefix}gap-4`,
      lg: `${prefix}gap-6`,
      xl: `${prefix}gap-8`,
    }
    return gapMap[gapSize as keyof typeof gapMap]
  }

  const columnClasses = typeof columns === 'object'
    ? Object.entries(columns).map(([bp, cols]) => 
        getColumnClass(cols, bp === 'xs' ? '' : bp)
      ).join(' ')
    : getColumnClass(columns)

  const gapClasses = typeof gap === 'object'
    ? Object.entries(gap).map(([bp, gapSize]) =>
        getGapClass(gapSize, bp === 'xs' ? '' : bp)
      ).join(' ')
    : getGapClass(gap)

  return (
    <div
      className={cn(
        'grid w-full',
        columnClasses,
        gapClasses,
        equalHeight && 'grid-rows-[masonry] items-stretch',
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Responsive Typography Component
const responsiveTypographyVariants = cva(
  "leading-relaxed transition-all duration-200",
  {
    variants: {
      variant: {
        'display-1': "font-bold tracking-tight text-3xl md:text-5xl xl:text-6xl 3xl:text-7xl",
        'display-2': "font-bold tracking-tight text-2xl md:text-4xl xl:text-5xl 3xl:text-6xl",
        'h1': "font-bold tracking-tight text-2xl md:text-3xl xl:text-4xl 3xl:text-5xl",
        'h2': "font-semibold tracking-tight text-xl md:text-2xl xl:text-3xl 3xl:text-4xl",
        'h3': "font-semibold tracking-tight text-lg md:text-xl xl:text-2xl 3xl:text-3xl",
        'h4': "font-semibold tracking-tight text-base md:text-lg xl:text-xl 3xl:text-2xl",
        'h5': "font-semibold tracking-tight text-sm md:text-base xl:text-lg 3xl:text-xl",
        'h6': "font-semibold tracking-tight text-xs md:text-sm xl:text-base 3xl:text-lg",
        'body-large': "text-base md:text-lg xl:text-xl 3xl:text-2xl max-w-prose",
        'body': "text-sm md:text-base xl:text-lg 3xl:text-xl max-w-prose",
        'body-small': "text-xs md:text-sm xl:text-base 3xl:text-lg max-w-prose",
        'caption': "text-xs md:text-sm xl:text-base 3xl:text-lg text-gray-600",
      },
      align: {
        left: "text-left",
        center: "text-center",
        right: "text-right",
      },
      spacing: {
        tight: "leading-tight",
        normal: "leading-normal",
        relaxed: "leading-relaxed",
        loose: "leading-loose",
      }
    },
    defaultVariants: {
      variant: "body",
      align: "left",
      spacing: "relaxed",
    },
  }
)

interface ResponsiveTypographyProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof responsiveTypographyVariants> {
  as?: keyof JSX.IntrinsicElements
  responsive?: boolean
}

export function ResponsiveTypography({
  className,
  variant,
  align,
  spacing,
  as,
  responsive = true,
  children,
  ...props
}: ResponsiveTypographyProps) {
  const Element = as || 'p'
  
  return (
    <Element
      className={cn(
        responsiveTypographyVariants({ variant, align, spacing }),
        className
      )}
      {...props}
    >
      {children}
    </Element>
  )
}

// Touch-Optimized Button Component
const touchButtonVariants = cva(
  "inline-flex items-center justify-center font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        primary: "bg-blue-600 text-white hover:bg-blue-700 focus-visible:ring-blue-500",
        secondary: "border-2 border-blue-600 bg-white text-blue-600 hover:bg-blue-50 focus-visible:ring-blue-500",
        tertiary: "bg-transparent text-blue-600 hover:bg-blue-50 focus-visible:ring-blue-500",
      },
      size: {
        // Mobile-optimized sizes (44px minimum touch target)
        mobile: "h-11 px-4 text-sm min-w-[44px] sm:h-10 sm:px-3 sm:text-sm",
        
        // Tablet-optimized sizes
        tablet: "h-10 px-4 text-sm min-w-[40px] md:h-9 md:px-3 md:text-sm",
        
        // Desktop-optimized sizes
        desktop: "h-9 px-3 text-sm min-w-[32px] xl:h-8 xl:px-2 xl:text-xs",
        
        // Adaptive size that changes per device
        adaptive: "h-11 px-4 text-sm min-w-[44px] md:h-10 md:px-3 xl:h-9 xl:px-2",
      },
      shape: {
        default: "rounded-lg",
        rounded: "rounded-full",
        square: "rounded-none",
      }
    },
    defaultVariants: {
      variant: "primary",
      size: "adaptive",
      shape: "default",
    },
  }
)

interface TouchButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof touchButtonVariants> {
  loading?: boolean
}

export function TouchButton({
  className,
  variant,
  size,
  shape,
  loading = false,
  children,
  disabled,
  ...props
}: TouchButtonProps) {
  return (
    <button
      className={cn(touchButtonVariants({ variant, size, shape }), className)}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"/>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
        </svg>
      )}
      {children}
    </button>
  )
}

// Responsive Spacing Component
interface ResponsiveSpacingProps {
  size?: ResponsiveValue<'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'>
  direction?: 'vertical' | 'horizontal' | 'all'
  children: React.ReactNode
}

export function ResponsiveSpacing({
  size = { xs: 'md', md: 'lg', xl: 'xl' },
  direction = 'vertical',
  children
}: ResponsiveSpacingProps) {
  const getSpacingClass = (spacing: string, dir: string, breakpoint: string = '') => {
    const prefix = breakpoint ? `${breakpoint}:` : ''
    
    const spacingMap = {
      xs: '2',
      sm: '4',
      md: '6',
      lg: '8',
      xl: '12',
      '2xl': '16',
    }
    
    const directionMap = {
      vertical: `${prefix}space-y-${spacingMap[spacing as keyof typeof spacingMap]}`,
      horizontal: `${prefix}space-x-${spacingMap[spacing as keyof typeof spacingMap]}`,
      all: `${prefix}gap-${spacingMap[spacing as keyof typeof spacingMap]}`,
    }
    
    return directionMap[dir as keyof typeof directionMap]
  }

  const spacingClasses = typeof size === 'object'
    ? Object.entries(size).map(([bp, spacing]) =>
        getSpacingClass(spacing, direction, bp === 'xs' ? '' : bp)
      ).join(' ')
    : getSpacingClass(size, direction)

  const wrapperClass = direction === 'all' ? 'flex flex-col' : ''

  return (
    <div className={cn(wrapperClass, spacingClasses)}>
      {children}
    </div>
  )
}

// Content Visibility Component
interface ContentVisibilityProps {
  show?: Array<'mobile' | 'tablet' | 'desktop' | 'ultrawide'>
  hide?: Array<'mobile' | 'tablet' | 'desktop' | 'ultrawide'>
  children: React.ReactNode
  className?: string
}

export function ContentVisibility({
  show,
  hide,
  children,
  className
}: ContentVisibilityProps) {
  let visibilityClasses = ''

  if (show) {
    const showClasses = show.map(device => {
      switch (device) {
        case 'mobile': return 'block sm:hidden'
        case 'tablet': return 'hidden md:block lg:hidden'
        case 'desktop': return 'hidden xl:block 3xl:hidden'
        case 'ultrawide': return 'hidden 3xl:block'
        default: return ''
      }
    }).join(' ')
    visibilityClasses = showClasses
  }

  if (hide) {
    const hideClasses = hide.map(device => {
      switch (device) {
        case 'mobile': return 'hidden sm:block'
        case 'tablet': return 'block md:hidden lg:block'
        case 'desktop': return 'block xl:hidden 3xl:block'
        case 'ultrawide': return 'block 3xl:hidden'
        default: return ''
      }
    }).join(' ')
    visibilityClasses = hideClasses
  }

  return (
    <div className={cn(visibilityClasses, className)}>
      {children}
    </div>
  )
}

// Responsive Image Component
interface ResponsiveImageProps extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, 'src'> {
  src: string | ResponsiveValue<string>
  alt: string
  aspectRatio?: ResponsiveValue<'square' | 'video' | 'portrait' | 'landscape'>
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
}

export function ResponsiveImage({
  src,
  alt,
  aspectRatio = 'landscape',
  objectFit = 'cover',
  className,
  ...props
}: ResponsiveImageProps) {
  const { breakpoint } = useResponsive()
  
  const currentSrc = typeof src === 'object' 
    ? src[breakpoint] || src.xs || Object.values(src)[0]
    : src

  const aspectRatioClass = typeof aspectRatio === 'object'
    ? aspectRatio[breakpoint] || aspectRatio.xs || 'landscape'
    : aspectRatio

  const aspectClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    portrait: 'aspect-[3/4]',
    landscape: 'aspect-[4/3]',
  }[aspectRatioClass]

  const objectFitClass = {
    contain: 'object-contain',
    cover: 'object-cover',
    fill: 'object-fill',
    none: 'object-none',
    'scale-down': 'object-scale-down',
  }[objectFit]

  return (
    <div className={cn('relative overflow-hidden', aspectClasses, className)}>
      <img
        src={currentSrc}
        alt={alt}
        className={cn('w-full h-full', objectFitClass)}
        {...props}
      />
    </div>
  )
}

export { useResponsive }
