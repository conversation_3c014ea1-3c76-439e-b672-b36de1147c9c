# Setting Up Supabase Email with SMTP

This guide explains how to set up and configure email sending in your PathLink application using Supabase's SMTP integration.

## Prerequisites
- Supabase project
- SMTP server credentials (from services like SendGrid, Mailgun, etc.)

## Step 1: Configure SMTP in Supabase Dashboard

1. Log in to your Supabase Dashboard at https://app.supabase.com
2. Select your project
3. Go to **Project Settings** → **Email**
4. Enable **Custom SMTP** and provide the following information:
   - SMTP Host (e.g., smtp.sendgrid.net)
   - Port (usually 587 or 465)
   - Username (your SMTP username)
   - Password (your SMTP password)
   - Sender Name (e.g., OllieTrack Rentals)
   - Sender Email (e.g., <EMAIL>)

5. Click **Save Changes**
6. Test the configuration by sending a test email

## Step 2: Deploy the SQL Function

1. Navigate to the **SQL Editor** in your Supabase Dashboard
2. Copy and paste the contents of `database/send-email-smtp-function.sql`
3. Run the SQL query to create the `send_email_smtp` function

## Step 3: Deploy the Edge Function

1. Make sure you have the Supabase CLI installed and configured
2. Open your terminal and navigate to your project root
3. Run the following command:

```bash
supabase functions deploy send-email --no-verify-jwt
```

## Testing

To verify the email system is working correctly:

1. Navigate to the admin dashboard and select a pending booking
2. Click on "Finalize Booking"
3. The system will send both a confirmation email and a detailed receipt email
4. Check the logs in the Supabase dashboard (Functions → Logs) to verify email delivery

## Troubleshooting

### Common Issues:

#### Emails not sending
- Verify your SMTP credentials in the Supabase dashboard
- Check the Edge Function logs for errors
- Make sure the SQL function is properly deployed

#### Authentication errors
- Ensure your SMTP credentials are correct
- Some providers may require app-specific passwords

#### Rate limiting
- Many SMTP providers have sending limits
- Check your provider's documentation for limits

## Additional Information

- The emails are sent using the templates defined in `src/lib/email.ts`
- The Edge Function `send-email` handles the actual sending via the SQL function
- For advanced customization, edit the SQL function or Edge Function code
