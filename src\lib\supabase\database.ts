// Supabase database operations for Ollie Track
import { createClient } from '@/lib/supabase/client'
import type { 
  Car, 
  Booking, 
  Payment, 
  Profile, 
  GPSLocation, 
  CarRoute,
  CreateBookingRequest,
  UpdateCarRequest,
  CreatePaymentRequest,
  CarSearchFilters,
  BookingSearchFilters
} from '@/lib/types'

// Client-side database operations
export class SupabaseClient {
  private supabase = createClient()

  // Profile operations
  async getProfile(userId?: string): Promise<Profile | null> {
    try {
      const uid = userId || (await this.supabase.auth.getUser()).data.user?.id
      if (!uid) return null

      const { data, error } = await this.supabase
        .from('profiles')
        .select('*')
        .eq('id', uid)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching profile:', error)
      return null
    }
  }

  async updateProfile(updates: Partial<Profile>): Promise<Profile | null> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser()
      if (!user) return null

      const { data, error } = await this.supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating profile:', error)
      return null
    }
  }

  // Car operations
  async getCars(filters?: CarSearchFilters): Promise<Car[]> {
    try {
      let query = this.supabase
        .from('cars')
        .select('*')
        .eq('is_archived', false)
        .order('created_at', { ascending: false })

      if (filters?.type) {
        query = query.eq('type', filters.type)
      }
      if (filters?.status) {
        query = query.eq('status', filters.status)
      }
      if (filters?.condition) {
        query = query.eq('condition', filters.condition)
      }
      if (filters?.transmission) {
        query = query.eq('transmission', filters.transmission)
      }
      if (filters?.priceRange) {
        query = query
          .gte('price_per_day', filters.priceRange.min)
          .lte('price_per_day', filters.priceRange.max)
      }
      if (filters?.seatsRange) {
        query = query
          .gte('seats', filters.seatsRange.min)
          .lte('seats', filters.seatsRange.max)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching cars:', error)
      return []
    }
  }

  async getCar(id: string): Promise<Car | null> {
    try {
      const { data, error } = await this.supabase
        .from('cars')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching car:', error)
      return null
    }
  }

  async getAvailableCars(from: Date, to: Date, filters?: CarSearchFilters): Promise<Car[]> {
    try {
      // Get cars that are not booked during the specified period
      const { data: bookedCarIds, error: bookingError } = await this.supabase
        .from('bookings')
        .select('car_id')
        .in('status', ['Pending', 'Active', 'Confirmed'])
        .or(`pickup_datetime.lte.${to.toISOString()},dropoff_datetime.gte.${from.toISOString()}`)

      if (bookingError) throw bookingError

      const bookedIds = bookedCarIds?.map(b => b.car_id) || []

      let query = this.supabase
        .from('cars')
        .select('*')
        .eq('is_archived', false)
        .eq('status', 'Available')
        .neq('condition', 'Needs Repair') // Exclude cars that need repair

      // Only exclude booked cars if there are any
      if (bookedIds.length > 0) {
        query = query.not('id', 'in', `(${bookedIds.join(',')})`)
      }

      // Apply additional filters
      if (filters?.type) query = query.eq('type', filters.type)
      if (filters?.condition) query = query.eq('condition', filters.condition)
      if (filters?.transmission) query = query.eq('transmission', filters.transmission)
      if (filters?.priceRange) {
        query = query
          .gte('price_per_day', filters.priceRange.min)
          .lte('price_per_day', filters.priceRange.max)
      }
      if (filters?.seatsRange) {
        query = query
          .gte('seats', filters.seatsRange.min)
          .lte('seats', filters.seatsRange.max)
      }

      const { data, error } = await query.order('price_per_day', { ascending: true })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching available cars:', error)
      return []
    }
  }

  // Booking operations
  async getMyBookings(): Promise<Booking[]> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser()
      if (!user) return []

      const { data, error } = await this.supabase
        .from('bookings')
        .select('*')
        .eq('customer_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching my bookings:', error)
      return []
    }
  }

  async getBooking(id: string): Promise<Booking | null> {
    try {
      const { data, error } = await this.supabase
        .from('bookings')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching booking:', error)
      return null
    }
  }

  async createBooking(booking: CreateBookingRequest): Promise<Booking | null> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      // Calculate total amount
      const car = await this.getCar(booking.carId)
      if (!car) throw new Error('Car not found')

      const days = Math.ceil(
        (new Date(booking.dropOffDateTime).getTime() - new Date(booking.pickUpDateTime).getTime()) 
        / (1000 * 60 * 60 * 24)
      )
      const totalAmount = car.price_per_day * Math.max(1, days)

      const { data, error } = await this.supabase
        .from('bookings')
        .insert({
          customer_id: user.id,
          car_id: booking.carId,
          pickup_location: booking.pickUpLocation,
          dropoff_location: booking.dropOffLocation,
          pickup_datetime: booking.pickUpDateTime,
          dropoff_datetime: booking.dropOffDateTime,
          special_requests: booking.specialRequests,
          total_amount: totalAmount,
          status: 'Pending'
        })
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating booking:', error)
      return null
    }
  }

  async updateBookingStatus(id: string, status: Booking['status']): Promise<boolean> {
    try {
      // Get booking details first
      const { data: booking, error: bookingError } = await this.supabase
        .from('bookings')
        .select('car_id, status')
        .eq('id', id)
        .single()

      if (bookingError || !booking) {
        console.error('Error fetching booking:', bookingError)
        return false
      }

      // Update booking status
      const { error } = await this.supabase
        .from('bookings')
        .update({ status })
        .eq('id', id)

      if (error) throw error

      // Update car status based on booking status
      let carStatus = "Available"
      if (status === "Active") {
        carStatus = "Rented"
      } else if (status === "Completed" || status === "Cancelled") {
        // Check if there are other active or confirmed bookings for this car
        const { data: activeBookings } = await this.supabase
          .from('bookings')
          .select('id')
          .eq('car_id', booking.car_id)
          .in('status', ['Active', 'Confirmed'])
          .neq('id', id)

        carStatus = activeBookings && activeBookings.length > 0 ? "Rented" : "Available"
      }
      // For Pending status, check if there are any Active bookings
      else if (status === "Pending") {
        const { data: activeBookings } = await this.supabase
          .from('bookings')
          .select('id')
          .eq('car_id', booking.car_id)
          .in('status', ['Active', 'Confirmed'])
          .neq('id', id)

        carStatus = activeBookings && activeBookings.length > 0 ? "Rented" : "Available"
      }

      // Update car status
      const { error: carUpdateError } = await this.supabase
        .from('cars')
        .update({ status: carStatus })
        .eq('id', booking.car_id)

      if (carUpdateError) {
        console.error('Error updating car status:', carUpdateError)
        // Don't fail the operation if car status update fails
      }

      return true
    } catch (error) {
      console.error('Error updating booking status:', error)
      return false
    }
  }

  async cancelBooking(id: string): Promise<boolean> {
    return this.updateBookingStatus(id, 'Cancelled')
  }

  // Payment operations
  async getPaymentsForBooking(bookingId: string): Promise<Payment[]> {
    try {
      const { data, error } = await this.supabase
        .from('payments')
        .select('*')
        .eq('booking_id', bookingId)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching payments:', error)
      return []
    }
  }

  async getMyPayments(): Promise<Payment[]> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser()
      if (!user) return []

      const { data, error } = await this.supabase
        .from('payments')
        .select(`
          *,
          booking:bookings!inner(customer_id)
        `)
        .eq('booking.customer_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching my payments:', error)
      return []
    }
  }

  // Vehicle Categories operations
  async getVehicleCategories(): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .rpc('get_vehicle_categories_with_stats')

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching vehicle categories:', error)
      return []
    }
  }

  async getCarsByCategory(categoryType: string): Promise<Car[]> {
    try {
      const { data, error } = await this.supabase
        .rpc('get_cars_by_category', { category_type_param: categoryType })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching cars by category:', error)
      return []
    }
  }

  // Admin operations (client-side)
  async getActiveRentals(): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('bookings')
        .select(`
          *,
          car:cars(*),
          customer:profiles(*)
        `)
        .eq('status', 'Active')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching active rentals:', error)
      return []
    }
  }

  async getAllBookings(): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('bookings')
        .select(`
          *,
          booking_ref,
          car:cars(*),
          customer:profiles(*)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      const bookings = data || []

      // Attach latest payment status to each booking for accurate admin display
      if (bookings.length > 0) {
        const bookingIds = bookings.map((b: { id: string }) => b.id)

        const { data: payments, error: paymentsError } = await this.supabase
          .from('payments')
          .select('id, booking_id, status, amount, created_at, transaction_date')
          .in('booking_id', bookingIds)
          .order('created_at', { ascending: false })

        if (!paymentsError && payments) {
          const latestByBooking = new Map<string, { status?: string; amount?: number }>()
          for (const p of payments as Array<{ booking_id: string; status?: string; amount?: number }>) {
            // Since results are ordered desc by created_at, first seen is latest
            if (!latestByBooking.has(p.booking_id)) {
              latestByBooking.set(p.booking_id, { status: p.status, amount: p.amount })
            }
          }

          for (const b of bookings as Array<Record<string, unknown>>) {
            const lp = latestByBooking.get(b.id as string)
            if (lp) {
              b.latest_payment_status = lp.status || null
              b.latest_payment_amount = lp.amount ?? null
            } else {
              b.latest_payment_status = null
              b.latest_payment_amount = null
            }
          }
        }
      }

      return bookings
    } catch (error) {
      console.error('Error fetching all bookings:', error)
      return []
    }
  }

  async getFleetStats(): Promise<{
    available: number
    rented: number
    maintenance: number
  }> {
    try {
      const { data: cars, error } = await this.supabase
        .from('cars')
        .select('id, model, status, condition')
        .eq('is_archived', false)

      if (error) throw error

      if (process.env.NODE_ENV === 'development') {
        console.log('Fleet Stats Debug - Cars fetched:', cars);
      }

      const stats = {
        available: 0,
        rented: 0,
        maintenance: 0
      }

      cars?.forEach((car: { id: string; model: string; status: string; condition: string }) => {
        if (process.env.NODE_ENV === 'development') {
          console.log(`Car ${car.model} (${car.id}): status="${car.status}", condition="${car.condition}"`);
        }
        
        // Priority check: condition first, then status
        if (car.condition === 'Needs Repair' || car.status === 'In Maintenance') {
          stats.maintenance++
          if (process.env.NODE_ENV === 'development') {
            console.log(`  -> Counted as maintenance`);
          }
        } else if (car.status === 'Rented') {
          stats.rented++
          if (process.env.NODE_ENV === 'development') {
            console.log(`  -> Counted as rented`);
          }
        } else if (car.status === 'Available') {
          stats.available++
          if (process.env.NODE_ENV === 'development') {
            console.log(`  -> Counted as available`);
          }
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.log(`  -> Status "${car.status}" not recognized`);
          }
        }
      })

      if (process.env.NODE_ENV === 'development') {
        console.log('Fleet Stats Result:', stats);
      }
      return stats
    } catch (error) {
      console.error('Error fetching fleet stats:', error)
      return { available: 0, rented: 0, maintenance: 0 }
    }
  }

  // Debug function to check car status synchronization
  async debugCarStatusSync(): Promise<void> {
    try {
      // Get all active bookings
      const { data: activeBookings } = await this.supabase
        .from('bookings')
        .select('id, car_id, status')
        .in('status', ['Active', 'Confirmed'])

      if (process.env.NODE_ENV === 'development') {
        console.log('Active/Confirmed Bookings:', activeBookings);
      }

      // Get all cars
      const { data: cars } = await this.supabase
        .from('cars')
        .select('id, model, status')
        .eq('is_archived', false)

      if (process.env.NODE_ENV === 'development') {
        console.log('All Cars:', cars);
      }

      // Check for inconsistencies
      const activeCarIds = new Set(activeBookings?.map(b => b.car_id) || [])
      const carsNeedingRentedStatus: Array<{id: string, model: string, currentStatus: string}> = []
      const carsNeedingAvailableStatus: Array<{id: string, model: string, currentStatus: string}> = []

      cars?.forEach(car => {
        const shouldBeRented = activeCarIds.has(car.id)
        if (shouldBeRented && car.status !== 'Rented') {
          carsNeedingRentedStatus.push({
            id: car.id,
            model: car.model,
            currentStatus: car.status
          })
          if (process.env.NODE_ENV === 'development') {
            console.log(`❌ Car ${car.model} (${car.id}) should be Rented but is ${car.status}`);
          }
        } else if (!shouldBeRented && car.status === 'Rented') {
          carsNeedingAvailableStatus.push({
            id: car.id,
            model: car.model,
            currentStatus: car.status
          })
          if (process.env.NODE_ENV === 'development') {
            console.log(`❌ Car ${car.model} (${car.id}) is marked as Rented but has no active bookings`);
          }
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.log(`✅ Car ${car.model} (${car.id}) status ${car.status} is correct`);
          }
        }
      })

      // Auto-fix inconsistencies - Update cars to Rented
      if (carsNeedingRentedStatus.length > 0) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Fixing cars that should be Rented...');
        }
        for (const car of carsNeedingRentedStatus) {
          await this.supabase
            .from('cars')
            .update({ status: 'Rented' })
            .eq('id', car.id)
          if (process.env.NODE_ENV === 'development') {
            console.log(`Fixed ${car.model}: ${car.currentStatus} -> Rented`);
          }
        }
      }

      // Auto-fix inconsistencies - Update cars to Available
      if (carsNeedingAvailableStatus.length > 0) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Fixing cars that should be Available...');
        }
        for (const car of carsNeedingAvailableStatus) {
          await this.supabase
            .from('cars')
            .update({ status: 'Available' })
            .eq('id', car.id)
          if (process.env.NODE_ENV === 'development') {
            console.log(`Fixed ${car.model}: ${car.currentStatus} -> Available`);
          }
        }
      }

    } catch (error) {
      console.error('Error in debugCarStatusSync:', error)
    }
  }

  // Real-time subscriptions
  subscribeToBookings(callback: (payload: any) => void) {
    return this.supabase
      .channel('bookings')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'bookings' }, 
        callback
      )
      .subscribe()
  }

  subscribeToCars(callback: (payload: any) => void) {
    return this.supabase
      .channel('cars')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'cars' }, 
        callback
      )
      .subscribe()
  }

  subscribeToVehicleCategories(callback: (payload: any) => void) {
    return this.supabase
      .channel('vehicle_categories')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'vehicle_categories' }, 
        callback
      )
      .subscribe()
  }

  subscribeToPayments(callback: (payload: any) => void) {
    return this.supabase
      .channel('payments')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'payments' },
        callback
      )
      .subscribe()
  }
}


// Export instances for use throughout the app
export const db = new SupabaseClient()
