"use client";

import { createClient } from "@/lib/supabase/client";
import { uploadDocument } from "@/lib/file-upload";

export interface UserLegalDocument {
  id: string;
  booking_id?: string;
  document_type: string;
  file_url: string;
  file_name: string;
  file_size: number;
  file_type: string;
  verification_status: 'pending' | 'approved' | 'rejected' | 'requires_resubmission';
  verification_notes?: string;
  verified_by?: string;
  verified_at?: string;
  created_at: string;
  updated_at: string;
}

export interface DocumentUploadResult {
  success: boolean;
  document?: UserLegalDocument;
  error?: string;
}

export interface DocumentCheckResult {
  hasAllDocuments: boolean;
  missingDocuments: string[];
  existingDocuments: UserLegalDocument[];
  documentSummary: {
    total: number;
    approved: number;
    pending: number;
    rejected: number;
    missing: number;
  };
}

// Document type mapping between UI and database
export const DOCUMENT_TYPE_MAPPING = {
  driversLicense: 'drivers_license',
  governmentId: 'government_id', 
  proofOfBilling: 'proof_of_billing',
  // Legacy mappings for backward compatibility
  drivers_license: 'drivers_license',
  government_id: 'government_id',
  proof_of_billing: 'proof_of_billing'
} as const;

export const REQUIRED_DOCUMENT_TYPES = [
  'drivers_license',
  'government_id', 
  'proof_of_billing'
] as const;

export type RequiredDocumentType = typeof REQUIRED_DOCUMENT_TYPES[number];

/**
 * Get the standardized document type for database operations
 */
export function getStandardDocumentType(uiType: string): string {
  return DOCUMENT_TYPE_MAPPING[uiType as keyof typeof DOCUMENT_TYPE_MAPPING] || uiType;
}

/**
 * Check if user has uploaded all required legal documents
 */
export async function checkUserLegalDocuments(userId?: string): Promise<DocumentCheckResult> {
  const supabase = createClient();
  
  try {
    // Get current user if not provided
    if (!userId) {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error("No authenticated user found");
      }
      userId = user.id;
    }

    // First, get user's bookings to find documents
    const { data: bookings, error: bookingsError } = await supabase
      .from("bookings")
      .select("id")
      .eq("customer_id", userId);

    if (bookingsError) {
      console.error("Error fetching user bookings:", bookingsError);
      throw bookingsError;
    }

    const bookingIds = bookings?.map(b => b.id) || [];
    
    // Get documents from booking_documents table
    let existingDocuments: UserLegalDocument[] = [];
    
    if (bookingIds.length > 0) {
      const { data: documents, error: documentsError } = await supabase
        .from("booking_documents")
        .select("*")
        .in("booking_id", bookingIds)
        .in("document_type", REQUIRED_DOCUMENT_TYPES);

      if (documentsError) {
        console.error("Error fetching documents:", documentsError);
        throw documentsError;
      }

      existingDocuments = documents || [];
    }

    // Check which document types are missing
    const existingTypes = new Set(existingDocuments.map(doc => doc.document_type));
    const missingDocuments = REQUIRED_DOCUMENT_TYPES.filter(type => !existingTypes.has(type));
    
    // Calculate summary
    const documentSummary = {
      total: REQUIRED_DOCUMENT_TYPES.length,
      approved: existingDocuments.filter(doc => doc.verification_status === 'approved').length,
      pending: existingDocuments.filter(doc => doc.verification_status === 'pending').length,
      rejected: existingDocuments.filter(doc => 
        doc.verification_status === 'rejected' || doc.verification_status === 'requires_resubmission'
      ).length,
      missing: missingDocuments.length
    };

    return {
      hasAllDocuments: missingDocuments.length === 0,
      missingDocuments,
      existingDocuments,
      documentSummary
    };

  } catch (error) {
    console.error("Error checking user legal documents:", error);
    return {
      hasAllDocuments: false,
      missingDocuments: [...REQUIRED_DOCUMENT_TYPES],
      existingDocuments: [],
      documentSummary: {
        total: REQUIRED_DOCUMENT_TYPES.length,
        approved: 0,
        pending: 0,
        rejected: 0,
        missing: REQUIRED_DOCUMENT_TYPES.length
      }
    };
  }
}

/**
 * Get user's legal documents by type
 */
export async function getUserLegalDocumentByType(
  documentType: string, 
  userId?: string
): Promise<UserLegalDocument | null> {
  const standardType = getStandardDocumentType(documentType);
  const result = await checkUserLegalDocuments(userId);
  return result.existingDocuments.find(doc => doc.document_type === standardType) || null;
}

/**
 * Save or update a legal document for a user
 */
export async function saveUserLegalDocument(
  documentType: string,
  file: File,
  userId?: string,
  bookingId?: string
): Promise<DocumentUploadResult> {
  const supabase = createClient();
  
  try {
    // Get current user if not provided
    if (!userId) {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { success: false, error: "No authenticated user found" };
      }
      userId = user.id;
    }

    // Get or create booking if not provided
    if (!bookingId) {
      // First try to find an existing real booking for this user (excluding placeholder bookings)
      const { data: existingBookings } = await supabase
        .from("bookings")
        .select("id, status")
        .eq("customer_id", userId)
        .neq("special_requests", "DOCUMENT_STORAGE_PLACEHOLDER")
        .order("created_at", { ascending: false })
        .limit(1);

      if (existingBookings && existingBookings.length > 0) {
        bookingId = existingBookings[0].id;
      } else {
        // Check for existing placeholder booking
        const { data: storageBooking } = await supabase
          .from("bookings")
          .select("id")
          .eq("customer_id", userId)
          .eq("special_requests", "DOCUMENT_STORAGE_PLACEHOLDER")
          .limit(1)
          .single();

        if (storageBooking) {
          bookingId = storageBooking.id;
        } else {
          // Get a default car_id for placeholder bookings (first available car)
          const { data: defaultCar } = await supabase
            .from("cars")
            .select("id")
            .eq("is_archived", false)
            .limit(1)
            .single();

          if (!defaultCar) {
            return { success: false, error: "No cars available for document storage booking" };
          }

          // Create a placeholder booking for document storage
          const futureDate = new Date();
          futureDate.setFullYear(futureDate.getFullYear() + 1); // Set to future date
          
          const { data: newBooking, error: bookingError } = await supabase
            .from("bookings")
            .insert({
              customer_id: userId,
              car_id: defaultCar.id, // Use default car for placeholder
              pickup_location: "Document Storage Placeholder",
              dropoff_location: "Document Storage Placeholder", 
              pickup_datetime: futureDate.toISOString(),
              dropoff_datetime: futureDate.toISOString(),
              total_amount: 0,
              status: "Pending", // Use valid status from CHECK constraint
              special_requests: "DOCUMENT_STORAGE_PLACEHOLDER" // Mark as placeholder
            })
            .select()
            .single();

          if (bookingError) {
            console.error("Error creating placeholder booking:", bookingError);
            return { success: false, error: "Failed to create document storage" };
          }

          bookingId = newBooking.id;
        }
      }
    }

    // Upload file to Supabase storage
    const uploadResult = await uploadDocument(file, "legal-documents");
    
    if (uploadResult.error || !uploadResult.url) {
      return { success: false, error: uploadResult.error || "Failed to upload file" };
    }

    const standardType = getStandardDocumentType(documentType);

    // Check if document already exists for this booking and type
    const { data: existingDoc } = await supabase
      .from("booking_documents")
      .select("id")
      .eq("booking_id", bookingId)
      .eq("document_type", standardType)
      .single();

    const documentData = {
      booking_id: bookingId,
      document_type: standardType,
      file_url: uploadResult.url,
      file_name: file.name,
      file_size: file.size,
      file_type: file.type,
      verification_status: 'pending' as const,
    };

    if (existingDoc) {
      // Update existing document
      const { data, error } = await supabase
        .from("booking_documents")
        .update(documentData)
        .eq("id", existingDoc.id)
        .select()
        .single();

      if (error) {
        console.error("Error updating document:", error);
        return { success: false, error: "Failed to update document" };
      }

      return { success: true, document: data };
    } else {
      // Insert new document
      const { data, error } = await supabase
        .from("booking_documents")
        .insert(documentData)
        .select()
        .single();

      if (error) {
        console.error("Error inserting document:", error);
        return { success: false, error: "Failed to save document" };
      }

      return { success: true, document: data };
    }

  } catch (error) {
    console.error("Error saving user legal document:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "An unexpected error occurred" 
    };
  }
}

/**
 * Delete a legal document
 */
export async function deleteUserLegalDocument(documentId: string): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();
  
  try {
    const { error } = await supabase
      .from("booking_documents")
      .delete()
      .eq("id", documentId);

    if (error) {
      console.error("Error deleting document:", error);
      return { success: false, error: "Failed to delete document" };
    }

    return { success: true };
  } catch (error) {
    console.error("Error deleting document:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "An unexpected error occurred" 
    };
  }
}

/**
 * Copy document from one folder to another (for autofill functionality)
 */
export async function copyDocumentBetweenFolders(
  sourceUrl: string,
  targetFolder: string,
  fileName: string
): Promise<{ success: boolean; url?: string; error?: string }> {
  const supabase = createClient();
  
  try {
    // Download the file from source URL
    const response = await fetch(sourceUrl);
    if (!response.ok) {
      return { success: false, error: "Failed to download source document" };
    }
    
    const blob = await response.blob();
    const file = new File([blob], fileName, { type: blob.type });
    
    // Upload to target folder
    const uploadResult = await uploadDocument(file, targetFolder);
    
    if (uploadResult.error) {
      return { success: false, error: uploadResult.error };
    }
    
    return { success: true, url: uploadResult.url || undefined };
  } catch (error) {
    console.error("Error copying document:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to copy document" 
    };
  }
}
