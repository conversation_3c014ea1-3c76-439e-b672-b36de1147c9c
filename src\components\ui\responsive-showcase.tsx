"use client"

import React from 'react'
import { 
  ResponsiveContainer, 
  ResponsiveGrid, 
  ResponsiveTypography, 
  TouchButton, 
  ResponsiveSpacing, 
  ContentVisibility, 
  ResponsiveImage,
  useResponsive 
} from './responsive'
import { Card, CardContent, CardHeader, CardTitle } from './card'
import { Badge } from './badge'
import { Button } from './button'

/**
 * Responsive Design Showcase Component
 * 
 * Demonstrates the comprehensive responsive system with:
 * - Fluid layouts and grid systems
 * - Responsive typography scaling
 * - Touch-optimized interaction areas
 * - Content scalability and visibility controls
 * - Breakpoint-aware components
 */
export function ResponsiveShowcase() {
  const { breakpoint, isMobile, isTablet, isDesktop, isUltrawide, width } = useResponsive()

  return (
    <div className="space-y-12 p-6">
      <ResponsiveContainer maxWidth="4xl">
        <div className="text-center space-y-4 mb-12">
          <ResponsiveTypography variant="display-1" align="center">
            Responsive Design System
          </ResponsiveTypography>
          <ResponsiveTypography variant="body-large" align="center">
            A comprehensive responsive system that adapts beautifully across all device sizes
          </ResponsiveTypography>
          
          {/* Current breakpoint indicator */}
          <div className="flex items-center justify-center gap-2 text-sm">
            <Badge variant="outline">Current: {breakpoint.toUpperCase()}</Badge>
            <Badge variant="outline">Width: {width}px</Badge>
            <Badge variant={isMobile ? "default" : "outline"}>Mobile</Badge>
            <Badge variant={isTablet ? "default" : "outline"}>Tablet</Badge>
            <Badge variant={isDesktop ? "default" : "outline"}>Desktop</Badge>
            <Badge variant={isUltrawide ? "default" : "outline"}>Ultra-wide</Badge>
          </div>
        </div>

        {/* Breakpoint System */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Breakpoint System</CardTitle>
            <p className="text-sm text-gray-600">
              Mobile-first approach with focus on ultra-wide, desktop, tablet, and mobile devices
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-medium mb-3">Breakpoint Definitions</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span>Mobile (xs)</span>
                    <span className="font-mono">0px - 639px</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span>Small (sm)</span>
                    <span className="font-mono">640px - 767px</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span>Tablet (md)</span>
                    <span className="font-mono">768px - 1023px</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span>Large (lg)</span>
                    <span className="font-mono">1024px - 1279px</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span>Desktop (xl)</span>
                    <span className="font-mono">1280px - 1535px</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span>Large Desktop (2xl)</span>
                    <span className="font-mono">1536px - 1919px</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span>Ultra-wide (3xl)</span>
                    <span className="font-mono">1920px - 2559px</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 rounded">
                    <span>4K (4xl)</span>
                    <span className="font-mono">2560px+</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-3">Current Device Info</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between p-2 bg-blue-50 rounded">
                    <span>Active Breakpoint</span>
                    <Badge>{breakpoint.toUpperCase()}</Badge>
                  </div>
                  <div className="flex justify-between p-2 bg-blue-50 rounded">
                    <span>Screen Width</span>
                    <span className="font-mono">{width}px</span>
                  </div>
                  <div className="flex justify-between p-2 bg-blue-50 rounded">
                    <span>Device Category</span>
                    <span>
                      {isMobile && "Mobile"}
                      {isTablet && "Tablet"}
                      {isDesktop && "Desktop"}
                      {isUltrawide && "Ultra-wide"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fluid Layout System */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Fluid Layout System</CardTitle>
            <p className="text-sm text-gray-600">
              Responsive grids and containers that adapt to screen size with proper ratios
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-8">
              <div>
                <h4 className="font-medium mb-4">Responsive Grid</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Grid automatically adjusts columns: 1 on mobile, 2 on small, 3 on large, 4 on desktop, 5 on ultra-wide
                </p>
                <ResponsiveGrid 
                  columns={{ xs: 1, sm: 2, lg: 3, xl: 4, '3xl': 5 }}
                  gap={{ xs: 'md', md: 'lg' }}
                >
                  {Array.from({ length: 8 }, (_, i) => (
                    <Card key={i} className="h-24 flex items-center justify-center bg-gradient-to-br from-blue-50 to-blue-100">
                      <span className="font-medium text-blue-700">Item {i + 1}</span>
                    </Card>
                  ))}
                </ResponsiveGrid>
              </div>

              <div>
                <h4 className="font-medium mb-4">Responsive Container</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Container with adaptive max-widths and padding for optimal content presentation
                </p>
                <ResponsiveContainer 
                  maxWidth="lg" 
                  padding={{ xs: 'md', md: 'lg', xl: 'xl' }}
                  className="bg-gray-50 rounded-lg"
                >
                  <div className="text-center py-8">
                    <p className="text-gray-700">
                      This container adapts its maximum width and padding based on screen size.
                      Try resizing your browser to see how it responds.
                    </p>
                  </div>
                </ResponsiveContainer>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Responsive Typography */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Responsive Typography</CardTitle>
            <p className="text-sm text-gray-600">
              Typography that scales appropriately - headings are noticeably different sizes across devices
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-4">Typography Scale</h4>
                <div className="space-y-4">
                  <ResponsiveTypography variant="display-1">
                    Display 1 - Scales from 48px to 80px
                  </ResponsiveTypography>
                  <ResponsiveTypography variant="h1">
                    Heading 1 - Scales from 36px to 72px
                  </ResponsiveTypography>
                  <ResponsiveTypography variant="h2">
                    Heading 2 - Scales from 32px to 64px
                  </ResponsiveTypography>
                  <ResponsiveTypography variant="h3">
                    Heading 3 - Scales from 28px to 56px
                  </ResponsiveTypography>
                  <ResponsiveTypography variant="body">
                    Body text - Scales from 14px to 22px for optimal readability
                  </ResponsiveTypography>
                  <ResponsiveTypography variant="caption">
                    Caption text - Scales from 12px to 20px
                  </ResponsiveTypography>
                </div>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h5 className="font-medium text-green-900 mb-2">📱 Mobile-First Scaling</h5>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• H1 starts at 36px on mobile, grows to 72px on ultra-wide</li>
                  <li>• Body text starts at 14px, grows to 22px for better readability</li>
                  <li>• Line heights adjust automatically for optimal reading</li>
                  <li>• Letter spacing tightens on larger sizes for better proportion</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Touch Targets */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Touch Targets & Selection Areas</CardTitle>
            <p className="text-sm text-gray-600">
              Mobile devices get larger touch targets (44px) while desktop uses smaller, precise targets (36px)
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-4">Adaptive Button Sizes</h4>
                <div className="flex flex-wrap gap-4">
                  <TouchButton size="adaptive" variant="primary">
                    Adaptive Button
                  </TouchButton>
                  <TouchButton size="mobile" variant="secondary">
                    Mobile Optimized
                  </TouchButton>
                  <TouchButton size="tablet" variant="tertiary">
                    Tablet Optimized
                  </TouchButton>
                  <TouchButton size="desktop" variant="primary">
                    Desktop Optimized
                  </TouchButton>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  The adaptive button changes size automatically: 44px on mobile, 40px on tablet, 36px on desktop
                </p>
              </div>

              <div>
                <h4 className="font-medium mb-4">Touch Target Guidelines</h4>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="w-11 h-11 bg-red-200 rounded-lg mx-auto mb-2 flex items-center justify-center">
                      <span className="text-xs font-mono">44px</span>
                    </div>
                    <p className="font-medium text-red-800">Mobile</p>
                    <p className="text-xs text-red-600">Minimum iOS/Android</p>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="w-10 h-10 bg-orange-200 rounded-lg mx-auto mb-2 flex items-center justify-center">
                      <span className="text-xs font-mono">40px</span>
                    </div>
                    <p className="font-medium text-orange-800">Tablet</p>
                    <p className="text-xs text-orange-600">Stylus-friendly</p>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="w-9 h-9 bg-blue-200 rounded-lg mx-auto mb-2 flex items-center justify-center">
                      <span className="text-xs font-mono">36px</span>
                    </div>
                    <p className="font-medium text-blue-800">Desktop</p>
                    <p className="text-xs text-blue-600">Mouse precision</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Content Scalability */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Content Scalability</CardTitle>
            <p className="text-sm text-gray-600">
              Mobile-first approach: minimal content on mobile, progressively enhanced on larger screens
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-4">Progressive Content Disclosure</h4>
                <div className="space-y-4">
                  <ContentVisibility show={['mobile']}>
                    <Card className="bg-red-50 border-red-200">
                      <CardContent className="p-4">
                        <p className="text-red-800 font-medium">📱 Mobile Only Content</p>
                        <p className="text-sm text-red-600">
                          Essential information shown on mobile devices. Simplified and focused.
                        </p>
                      </CardContent>
                    </Card>
                  </ContentVisibility>

                  <ContentVisibility show={['tablet']}>
                    <Card className="bg-orange-50 border-orange-200">
                      <CardContent className="p-4">
                        <p className="text-orange-800 font-medium">📱 Tablet Only Content</p>
                        <p className="text-sm text-orange-600">
                          Additional context and details for tablet users with more screen space.
                        </p>
                      </CardContent>
                    </Card>
                  </ContentVisibility>

                  <ContentVisibility show={['desktop']}>
                    <Card className="bg-blue-50 border-blue-200">
                      <CardContent className="p-4">
                        <p className="text-blue-800 font-medium">🖥️ Desktop Only Content</p>
                        <p className="text-sm text-blue-600">
                          Rich content, detailed descriptions, and advanced features for desktop users.
                        </p>
                      </CardContent>
                    </Card>
                  </ContentVisibility>

                  <ContentVisibility show={['ultrawide']}>
                    <Card className="bg-purple-50 border-purple-200">
                      <CardContent className="p-4">
                        <p className="text-purple-800 font-medium">🖥️ Ultra-wide Only Content</p>
                        <p className="text-sm text-purple-600">
                          Premium content and immersive experiences for ultra-wide displays.
                        </p>
                      </CardContent>
                    </Card>
                  </ContentVisibility>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-4">Always Visible Content</h4>
                <Card className="bg-green-50 border-green-200">
                  <CardContent className="p-4">
                    <p className="text-green-800 font-medium">✅ Core Content</p>
                    <p className="text-sm text-green-600">
                      Essential content that appears on all devices, optimized for each screen size.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Responsive Images */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Responsive Images</CardTitle>
            <p className="text-sm text-gray-600">
              Images that adapt their aspect ratio and source based on screen size
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-4">Adaptive Aspect Ratios</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Square on mobile, 4:3 on tablet, 16:9 on desktop
                </p>
                <ResponsiveImage
                  src="/placeholder.jpg"
                  alt="Responsive image example"
                  aspectRatio={{ xs: 'square', md: 'portrait', xl: 'video' }}
                  className="max-w-md mx-auto"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Responsive Spacing */}
        <Card>
          <CardHeader>
            <CardTitle>Responsive Spacing</CardTitle>
            <p className="text-sm text-gray-600">
              Spacing that scales with screen size for optimal visual rhythm
            </p>
          </CardHeader>
          <CardContent>
            <ResponsiveSpacing size={{ xs: 'sm', md: 'md', xl: 'lg' }}>
              <Card className="bg-gray-50">
                <CardContent className="p-4">
                  <p>First item with responsive spacing</p>
                </CardContent>
              </Card>
              <Card className="bg-gray-50">
                <CardContent className="p-4">
                  <p>Second item - spacing increases on larger screens</p>
                </CardContent>
              </Card>
              <Card className="bg-gray-50">
                <CardContent className="p-4">
                  <p>Third item - maintains consistent visual rhythm</p>
                </CardContent>
              </Card>
            </ResponsiveSpacing>
            
            <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h5 className="font-medium text-blue-900 mb-2">📏 Spacing Scale</h5>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Mobile: 16px spacing for compact layouts</li>
                <li>• Tablet: 24px spacing for comfortable viewing</li>
                <li>• Desktop: 32px spacing for generous layouts</li>
                <li>• Maintains visual hierarchy across all sizes</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </ResponsiveContainer>
    </div>
  )
}

export default ResponsiveShowcase
