import * as React from "react"
import { cn } from "@/lib/utils"

interface ModernInputProps extends React.ComponentProps<"input"> {
  className?: string
}

function ModernInput({ className, type, ...props }: ModernInputProps) {
  return (
    <div className="modern-input-container">
      <input
        type={type}
        className={cn(
          "w-full bg-transparent px-3 py-3 text-base transition-colors duration-200",
          "border-0 border-b-2 border-gray-300 rounded-none",
          "focus:outline-none focus:border-blue-600",
          "placeholder:text-gray-400",
          "disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        {...props}
      />
    </div>
  )
}

export { ModernInput }
