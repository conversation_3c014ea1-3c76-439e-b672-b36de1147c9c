import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  // Base styles with comprehensive interaction states
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all duration-200 ease-in-out disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-offset-2 active:scale-95 select-none cursor-pointer",
  {
    variants: {
      variant: {
        // Primary - Fill style (main action)
        primary:
          "bg-blue-600 text-white shadow-sm hover:bg-blue-700 hover:shadow-md active:bg-blue-800 focus-visible:ring-blue-500 disabled:bg-gray-300 disabled:text-gray-500",
        
        // Secondary - Outline style (secondary action)
        secondary:
          "border-2 border-blue-600 bg-white text-blue-600 shadow-sm hover:bg-blue-50 hover:text-blue-700 active:bg-blue-100 focus-visible:ring-blue-500 disabled:border-gray-300 disabled:text-gray-400 disabled:bg-gray-50",
        
        // Secondary inverse - For use on colored backgrounds
        "secondary-inverse":
          "border-2 border-white bg-transparent text-white shadow-sm hover:bg-white hover:text-blue-700 active:bg-blue-50 active:text-blue-800 focus-visible:ring-white disabled:border-gray-400 disabled:text-gray-400 disabled:bg-transparent",
        
        // Tertiary - Ghost style (subtle action)
        tertiary:
          "bg-transparent text-blue-600 hover:bg-blue-50 hover:text-blue-700 active:bg-blue-100 focus-visible:ring-blue-500 disabled:text-gray-400 disabled:hover:bg-transparent",
        
        // Destructive - For dangerous actions
        destructive:
          "bg-red-600 text-white shadow-sm hover:bg-red-700 hover:shadow-md active:bg-red-800 focus-visible:ring-red-500 disabled:bg-gray-300 disabled:text-gray-500",
        
        // Destructive outline
        "destructive-outline":
          "border-2 border-red-600 bg-white text-red-600 shadow-sm hover:bg-red-50 hover:shadow-md active:bg-red-100 focus-visible:ring-red-500 disabled:border-gray-300 disabled:text-gray-400 disabled:bg-gray-50",
        
        // Success
        success:
          "bg-green-600 text-white shadow-sm hover:bg-green-700 hover:shadow-md active:bg-green-800 focus-visible:ring-green-500 disabled:bg-gray-300 disabled:text-gray-500",
        
        // Warning
        warning:
          "bg-amber-500 text-white shadow-sm hover:bg-amber-600 hover:shadow-md active:bg-amber-700 focus-visible:ring-amber-500 disabled:bg-gray-300 disabled:text-gray-500",
        
        // Link style - Underline
        link: 
          "text-blue-600 underline-offset-4 hover:underline hover:text-blue-700 active:text-blue-800 focus-visible:ring-blue-500 disabled:text-gray-400 disabled:no-underline bg-transparent shadow-none p-0 h-auto",
      },
      size: {
        xs: "h-7 px-2 text-xs rounded-md gap-1",
        sm: "h-8 px-3 text-sm rounded-md gap-1.5",
        default: "h-10 px-4 text-sm rounded-lg gap-2",
        lg: "h-12 px-6 text-base rounded-lg gap-2",
        xl: "h-14 px-8 text-lg rounded-xl gap-3",
        icon: "size-10 rounded-lg",
        "icon-sm": "size-8 rounded-md",
        "icon-lg": "size-12 rounded-lg",
      },
      shape: {
        default: "",
        rounded: "rounded-full",
        square: "rounded-none",
        pill: "rounded-full px-6",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
      shape: "default",
    },
  }
)

interface ButtonProps 
  extends React.ComponentProps<"button">,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  loadingText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

function Button({
  className,
  variant,
  size,
  shape,
  asChild = false,
  loading = false,
  loadingText,
  leftIcon,
  rightIcon,
  children,
  disabled,
  ...props
}: ButtonProps) {
  const Comp = asChild ? Slot : "button"
  
  // Validate that button has meaningful copy
  if (process.env.NODE_ENV === 'development' && !asChild) {
    if (!children && !props['aria-label'] && !props.title) {
      console.warn('Button should have meaningful text, aria-label, or title for accessibility')
    }
  }

  const isDisabled = disabled || loading

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, shape }), className)}
      disabled={isDisabled}
      aria-disabled={isDisabled}
      data-loading={loading}
      {...props}
    >
      {asChild ? (
        // When using asChild, pass children directly to avoid multiple children error
        children
      ) : (
        <>
          {loading && (
            <svg
              className="animate-spin -ml-1 mr-2 h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
          )}
          {leftIcon && !loading && leftIcon}
          {loading && loadingText ? loadingText : children}
          {rightIcon && !loading && rightIcon}
        </>
      )}
    </Comp>
  )
}

// Legacy support - map old variants to new ones
const LegacyButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant, ...props }, ref) => {
    return <Button ref={ref} variant={variant} {...props} />
  }
)
LegacyButton.displayName = "LegacyButton"

export { Button, LegacyButton, buttonVariants, type ButtonProps }
