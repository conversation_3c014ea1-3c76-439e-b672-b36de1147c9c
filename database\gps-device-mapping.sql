-- ============================================
-- GPS DEVICE MAPPING TABLE SCHEMA
-- ============================================
-- This table maps GPS device IDs to actual car UUIDs
-- Solves the issue where GPS devices send device IDs that aren't UUIDs

-- Create the GPS device mapping table (if not exists)
create table if not exists public.gps_device_mapping (
  id uuid not null default extensions.uuid_generate_v4(),
  device_id text not null unique,
  car_id uuid not null,
  device_name text null,
  device_type text not null default 'esp32',
  is_active boolean not null default true,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint gps_device_mapping_pkey primary key (id),
  constraint gps_device_mapping_car_id_fkey foreign key (car_id) references cars (id) on delete cascade,
  constraint gps_device_mapping_device_id_check check (char_length(device_id) > 0),
  constraint gps_device_mapping_device_type_check check (
    device_type = any (array['esp32'::text, 'lilygo'::text, 'generic'::text])
  )
) tablespace pg_default;

-- Create indexes for performance
create index if not exists idx_gps_device_mapping_device_id on public.gps_device_mapping using btree (device_id) tablespace pg_default;
create index if not exists idx_gps_device_mapping_car_id on public.gps_device_mapping using btree (car_id) tablespace pg_default;
create index if not exists idx_gps_device_mapping_active on public.gps_device_mapping using btree (is_active) tablespace pg_default;

-- Function to get car_id from device_id
CREATE OR REPLACE FUNCTION public.get_car_id_from_device(device_id_param text)
returns uuid
language plpgsql
security definer
set search_path = public
as $$
declare
  car_uuid uuid;
begin
  select car_id into car_uuid
  from public.gps_device_mapping
  where device_id = device_id_param
    and is_active = true
  limit 1;
  
  return car_uuid;
end;
$$;

-- Function to get all GPS devices with car info
create or replace function public.get_gps_devices_with_cars()
returns table (
  id uuid,
  device_id text,
  car_id uuid,
  device_name text,
  device_type text,
  is_active boolean,
  car_model text,
  car_plate text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone
)
language plpgsql
security definer
set search_path = public
as $$
begin
  return query
  select 
    gdm.id,
    gdm.device_id,
    gdm.car_id,
    gdm.device_name,
    gdm.device_type,
    gdm.is_active,
    c.model as car_model,
    c.plate_number as car_plate,
    gdm.created_at,
    gdm.updated_at
  from public.gps_device_mapping gdm
  left join public.cars c on gdm.car_id = c.id
  order by gdm.created_at desc;
end;
$$;

-- Function to get latest GPS location per car with active device mappings
CREATE OR REPLACE FUNCTION get_latest_gps_per_car()
RETURNS TABLE (
  id uuid,
  car_id uuid,
  latitude double precision,
  longitude double precision,
  speed double precision,
  heading double precision,
  status text,
  "timestamp" timestamptz,
  driver_id uuid,
  created_at timestamptz,
  car jsonb
) 
LANGUAGE sql
SECURITY DEFINER
AS $$
  WITH latest_per_car AS (
    SELECT DISTINCT ON (gl.car_id) 
      gl.id,
      gl.car_id,
      gl.latitude,
      gl.longitude,
      gl.speed,
      gl.heading,
      CASE 
        -- If GPS data is older than 2 minutes (120 seconds), mark as offline
        WHEN EXTRACT(EPOCH FROM (NOW() - gl."timestamp")) > 120 THEN 'offline'
        -- If explicitly set as offline in database, keep offline
        WHEN gl.status = 'offline' THEN 'offline'
        -- Otherwise use the original status
        ELSE gl.status
      END as status,
      gl."timestamp",
      gl.driver_id,
      gl.created_at,
      jsonb_build_object(
        'model', c.model,
        'plate_number', c.plate_number,
        'data_age_seconds', EXTRACT(EPOCH FROM (NOW() - gl."timestamp"))
      ) as car_info
    FROM gps_locations gl
    INNER JOIN cars c ON gl.car_id = c.id
    INNER JOIN gps_device_mapping gdm ON c.id = gdm.car_id
    WHERE gdm.is_active = true
    ORDER BY gl.car_id, gl."timestamp" DESC
  )
  SELECT 
    lpc.id,
    lpc.car_id,
    lpc.latitude,
    lpc.longitude,
    lpc.speed,
    lpc.heading,
    lpc.status,
    lpc."timestamp",
    lpc.driver_id,
    lpc.created_at,
    lpc.car_info as car
  FROM latest_per_car lpc;
$$;

-- Insert sample mapping for the current ESP32 device
-- Map to specific Mitsubishi Mirage GLS 2019 AT
do $$
begin
  -- Check if mapping already exists
  if not exists (select 1 from public.gps_device_mapping where device_id = 'lilygo-esp32-01') then
    
    -- Insert mapping to specific Mitsubishi Mirage car
    INSERT INTO gps_device_mapping (device_id, car_id, device_name, device_type, is_active) 
    VALUES ('lilygo-esp32-01', '63c48d2e-a48e-4aa4-88f0-7560867899b4', 'LilyGO T-Call A7670E', 'esp32', true);
    
    raise notice 'Created GPS device mapping for lilygo-esp32-01 → Mitsubishi Mirage GLS 2019 AT (car_id: 63c48d2e-a48e-4aa4-88f0-7560867899b4)';
  else
    raise notice 'GPS device mapping for lilygo-esp32-01 already exists';
  end if;
end $$;

-- Grant necessary permissions
grant select on public.gps_device_mapping to authenticated;
grant select on public.gps_device_mapping to anon;

-- Super admin and service role can manage GPS device mappings
grant all on public.gps_device_mapping to service_role;

-- Allow authenticated users to read for tracking purposes
grant select on public.gps_device_mapping to authenticated;

-- Enable RLS
alter table public.gps_device_mapping enable row level security;

-- RLS policies (only create if they don't exist)
DO $$
BEGIN
  -- Create readable policy if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'gps_device_mapping' 
    AND policyname = 'GPS device mappings are readable by authenticated users'
  ) THEN
    CREATE POLICY "GPS device mappings are readable by authenticated users" ON public.gps_device_mapping
      FOR SELECT USING (true);
  END IF;

  -- Create manageable policy if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'gps_device_mapping' 
    AND policyname = 'GPS device mappings are manageable by super admin'
  ) THEN
    CREATE POLICY "GPS device mappings are manageable by super admin" ON public.gps_device_mapping
      FOR ALL USING (
        EXISTS (
          SELECT 1 FROM profiles 
          WHERE profiles.id = auth.uid() 
          AND profiles.role = 'super_admin'
        )
      );
  END IF;
END $$;

-- Comments for documentation
comment on table public.gps_device_mapping is 'Maps GPS device IDs to car UUIDs for tracking system';
comment on column public.gps_device_mapping.device_id is 'Unique identifier sent by GPS device (e.g., lilygo-esp32-01)';
comment on column public.gps_device_mapping.car_id is 'UUID of the car this device is installed in';
comment on column public.gps_device_mapping.device_name is 'Human-readable name for the GPS device';
comment on column public.gps_device_mapping.device_type is 'Type of GPS device (esp32, lilygo, generic)';
comment on column public.gps_device_mapping.is_active is 'Whether this device mapping is currently active';
