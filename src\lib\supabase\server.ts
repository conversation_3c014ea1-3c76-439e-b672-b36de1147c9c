import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function createClient() {
  const cookieStore = await cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        }
      }
    }
  )
}

/**
 * Creates a context-specific server client for isolated auth sessions
 */
export async function createContextClient(context: 'admin' | 'customer') {
  const cookieStore = await cookies()

  // Use context-specific cookie names for proper isolation
  const cookiePrefix = `sb-${context}-auth-token`

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        storageKey: cookiePrefix, // Context-specific storage key
      },
      cookies: {
        getAll() {
          // Get all cookies and filter for context-specific ones
          const allCookies = cookieStore.getAll()

          // Filter for cookies that match the client-side storage format
          const contextCookies = allCookies.filter(cookie => {
            const name = cookie.name
            return (
              // Direct prefix match (sb-admin-auth-token.access_token, etc.)
              name.startsWith(cookiePrefix + '.') ||
              // Exact prefix match
              name.startsWith(cookiePrefix) ||
              // Legacy patterns for backward compatibility
              name.startsWith(`sb-${context}-`) ||
              // Standard Supabase cookies that should be context-aware
              (name.startsWith('sb-') && (
                name.includes(`-${context}-`) ||
                name.includes(context)
              ))
            )
          })

          if (process.env.NODE_ENV === 'development') {
            console.log(`[${context}] Server cookies found:`, contextCookies.map(c => c.name))
          }

          return contextCookies
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              // Use the exact cookie prefix format that matches client-side storage
              let contextName = name

              // Transform generic Supabase cookie names to match client-side format
              if (name.startsWith('sb-') && !name.includes(context)) {
                // Replace 'sb-' with 'sb-{context}-auth-token.' for consistency with client
                contextName = name.replace('sb-', `${cookiePrefix}.`)
              }

              if (process.env.NODE_ENV === 'development') {
                console.log(`[${context}] Setting server cookie: ${name} → ${contextName}`)
              }

              cookieStore.set(contextName, value, options)
            })
          } catch (error) {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
            if (process.env.NODE_ENV === 'development') {
              console.warn(`[${context}] Cookie setting error:`, error)
            }
          }
        }
      }
    }
  )
}
