import React from 'react';

interface MessageFormatterProps {
  content: string;
  className?: string;
}

export function MessageFormatter({ content, className = "" }: MessageFormatterProps) {
  // Clean and format the content
  const formatContent = (text: string) => {
    // First, clean markdown symbols and format text
    let cleanedText = text
      // Remove markdown headers (### or ##)
      .replace(/^#{1,6}\s*/gm, '')
      // Remove bold/italic markers (**text** or *text*)
      .replace(/\*\*([^*]+)\*\*/g, '$1')
      .replace(/\*([^*]+)\*/g, '$1')
      // Clean up extra whitespace
      .replace(/\s+/g, ' ')
      .trim();

    // Split by double line breaks for paragraphs
    const paragraphs = cleanedText.split(/\n\s*\n/);
    
    return paragraphs.map((paragraph, pIndex) => {
      // Handle vehicle listings (numbered items with details)
      const vehicleMatch = paragraph.match(/(\d+\.\s.*?(?=\d+\.|$))/g);
      if (vehicleMatch && vehicleMatch.length > 1) {
        return (
          <div key={pIndex} className="space-y-3 mb-4">
            {vehicleMatch.map((vehicle, vIndex) => {
              const cleanVehicle = formatVehicleText(vehicle.trim());
              return (
                <div key={vIndex} className="bg-gray-50 rounded-lg p-3 border-l-4 border-blue-500">
                  <div className="text-sm leading-relaxed" dangerouslySetInnerHTML={{ __html: cleanVehicle }} />
                </div>
              );
            })}
          </div>
        );
      }
      
      // Check if it's a numbered list
      const numberedListMatch = paragraph.match(/^\d+\.\s/);
      if (numberedListMatch) {
        const items = paragraph.split(/(?=\d+\.\s)/).filter(item => item.trim());
        return (
          <div key={pIndex} className="space-y-2 mb-4">
            {items.map((item, iIndex) => {
              const cleanItem = item.replace(/^\d+\.\s/, '').trim();
              const formattedItem = formatVehicleText(cleanItem);
              return (
                <div key={iIndex} className="bg-gray-50 rounded-lg p-3 border-l-4 border-blue-500">
                  <div className="text-sm leading-relaxed" dangerouslySetInnerHTML={{ __html: formattedItem }} />
                </div>
              );
            })}
          </div>
        );
      }
      
      // Check if it's a bullet list
      const bulletListMatch = paragraph.match(/^[•\-\*]\s/);
      if (bulletListMatch) {
        const items = paragraph.split(/(?=[•\-\*]\s)/).filter(item => item.trim());
        return (
          <ul key={pIndex} className="list-disc list-inside space-y-1 mb-3">
            {items.map((item, iIndex) => {
              const cleanItem = item.replace(/^[•\-\*]\s/, '').trim();
              return (
                <li key={iIndex} className="text-sm leading-relaxed">
                  {cleanItem}
                </li>
              );
            })}
          </ul>
        );
      }
      
      // Handle code blocks (basic)
      if (paragraph.includes('```') || paragraph.includes('`')) {
        const parts = paragraph.split(/(`[^`]+`)/);
        return (
          <p key={pIndex} className="mb-3 text-sm leading-relaxed">
            {parts.map((part, partIndex) => {
              if (part.startsWith('`') && part.endsWith('`')) {
                return (
                  <code key={partIndex} className="bg-gray-200 px-1 py-0.5 rounded text-xs font-mono">
                    {part.slice(1, -1)}
                  </code>
                );
              }
              return part;
            })}
          </p>
        );
      }
      
      // Regular paragraph
      if (paragraph.trim()) {
        return (
          <p key={pIndex} className="mb-3 text-sm leading-relaxed">
            {paragraph.trim()}
          </p>
        );
      }
      
      return null;
    }).filter(Boolean);
  };

  // Format vehicle information with enhanced styling
  const formatVehicleText = (text: string): string => {
    return text
      // Format vehicle names (make them prominent)
      .replace(/^([A-Za-z\s]+(?:\d{4})?(?:\s+[A-Z]{2,})*)/g, '<strong class="text-blue-700 text-base">🚗 $1</strong>')
      // Format daily rates
      .replace(/(Daily Rate:)\s*(₱[\d,]+)/g, '<br/><span class="text-green-600 font-semibold">💰 $1 $2</span>')
      // Format seats
      .replace(/(Seats:)\s*(\d+)/g, '<br/><span class="text-gray-600">👥 $1 $2</span>')
      // Format transmission
      .replace(/(Transmission:)\s*([A-Za-z]+)/g, '<br/><span class="text-gray-600">⚙️ $1 $2</span>')
      // Format color
      .replace(/(Color:)\s*([A-Za-z]+)/g, '<br/><span class="text-gray-600">🎨 $1 $2</span>')
      // Format plate
      .replace(/(Plate:)\s*([A-Z0-9\-]+)/g, '<br/><span class="text-gray-600">🔢 $1 $2</span>')
      // Format condition
      .replace(/(Condition:)\s*([A-Za-z]+)/g, '<br/><span class="text-green-600">✅ $1 $2</span>')
      // Remove extra dashes
      .replace(/\s*-\s*/g, ' ');
  };

  return (
    <div className={`message-content ${className}`} style={{ wordWrap: 'break-word', overflowWrap: 'anywhere' }}>
      {formatContent(content)}
    </div>
  );
}
