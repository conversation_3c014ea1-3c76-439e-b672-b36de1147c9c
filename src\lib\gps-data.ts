export interface GPSLocation {
  id: string
  carId: string
  carModel: string
  carPlate: string
  latitude: number
  longitude: number
  speed: number // km/h
  heading: number // degrees
  timestamp: Date
  status: 'active' | 'idle' | 'offline'
  driverId?: string
  driverName?: string
}

export interface CarRoute {
  carId: string
  route: Array<{
    lat: number
    lng: number
    timestamp: Date
  }>
}

// No mock data - using real GPS data from devices and API only

// Map database row (via API) to UI GPSLocation shape
function mapApiRowToGPSLocation(row: any): GPSLocation {
  return {
    id: row.id || `${row.car_id}-${row.timestamp}`,
    carId: row.car_id || row.carId,
    carModel: row.car?.model || row.carModel || 'Unknown',
    carPlate: row.car?.plate_number || row.carPlate || '—',
    latitude: Number(row.latitude),
    longitude: Number(row.longitude),
    speed: Number(row.speed || 0),
    heading: Number(row.heading || 0),
    timestamp: new Date(row.timestamp || row.created_at || Date.now()),
    status: (row.status as GPSLocation['status']) || 'active',
    driverId: row.driver_id,
    driverName: row.driver_name || row.driverName
  }
}

export async function fetchCurrentGPSLocations(): Promise<GPSLocation[]> {
  try {
    // Add trailing slash to avoid 308 redirects with trailingSlash: true configuration
    const res = await fetch('/api/gps/current/', { cache: 'no-store' })
    if (!res.ok) throw new Error(`HTTP ${res.status}`)
    const json = await res.json()
    const rows = Array.isArray(json?.data) ? json.data : []
    const base = rows.map(mapApiRowToGPSLocation)
    
    return base
  } catch (error) {
    console.error('Failed to fetch GPS locations:', error)
    return []
  }
}

// ❌ DISABLED: Development GPS location generator completely removed
// This function was causing phantom GPS data when ESP32 was offline
function getDevEsp32Location(): GPSLocation | null {
  // Development GPS data generation is permanently disabled
  // Only real ESP32 devices should provide GPS data
  console.warn('⚠️ getDevEsp32Location() called but disabled - no development GPS data will be generated');
  return null;
}

// Function to get all current GPS locations - now uses API by default
export function getCurrentGPSLocations(): GPSLocation[] {
  // ❌ DISABLED: No more mock or development GPS data
  // Only return real GPS data from database via API
  console.warn('⚠️ getCurrentGPSLocations() called but disabled - use fetchCurrentGPSLocations() for real GPS data only');
  return [];
}

// Function to simulate real-time GPS updates - now returns null since we use real data
export function simulateGPSUpdate(carId: string): GPSLocation | null {
  // No simulation needed - using real GPS data from devices
  console.warn('simulateGPSUpdate is deprecated - using real GPS data from devices')
  return null
}

// Function to generate simulated track data for testing
export function generateSimulatedTrack(carId: string, timeWindow: 'live' | '15m' | '1h' | '24h' = '1h'): CarRoute | null {
  if (timeWindow === 'live') {
    return null;
  }

  // Manila center coordinates
  const centerLat = 14.5995;
  const centerLng = 120.9842;
  
  // Generate track points based on time window
  let numPoints: number;
  let timeStep: number; // in minutes
  
  switch (timeWindow) {
    case '15m':
      numPoints = 10;
      timeStep = 1.5;
      break;
    case '1h':
      numPoints = 20;
      timeStep = 3;
      break;
    case '24h':
      numPoints = 50;
      timeStep = 30;
      break;
    default:
      return null;
  }

  const route = [];
  const now = new Date();
  
  // Create a circular path for simulation
  for (let i = 0; i < numPoints; i++) {
    const angle = (i / numPoints) * 2 * Math.PI;
    const radius = 0.01; // ~1km radius
    
    // Add some randomness to make it more realistic
    const randomOffset = 0.002 * (Math.random() - 0.5);
    
    const lat = centerLat + (Math.cos(angle) * radius) + randomOffset;
    const lng = centerLng + (Math.sin(angle) * radius) + randomOffset;
    
    // Calculate timestamp going backwards from now
    const timestamp = new Date(now.getTime() - (numPoints - i - 1) * timeStep * 60 * 1000);
    
    route.push({
      lat,
      lng,
      timestamp
    });
  }

  return {
    carId,
    route
  };
}

// Function to get historical route data for a car - now implemented for real tracking
export async function getCarRoute(carId: string, timeWindow: 'live' | '15m' | '1h' | '24h' = '1h'): Promise<CarRoute | null> {
  try {
    // Calculate time window
    const now = new Date();
    let startTime = new Date();
    switch (timeWindow) {
      case 'live':
        // For live mode, show recent trail (last 15 minutes) for track logs
        startTime.setMinutes(now.getMinutes() - 15);
        break;
      case '15m':
        startTime.setMinutes(now.getMinutes() - 15);
        break;
      case '1h':
        startTime.setHours(now.getHours() - 1);
        break;
      case '24h':
        startTime.setHours(now.getHours() - 24);
        break;
    }

    // Fetch historical GPS data from API
    const res = await fetch(`/api/gps/history/?carId=${encodeURIComponent(carId)}&from=${startTime.toISOString()}&to=${now.toISOString()}`, { 
      cache: 'no-store' 
    });
    
    if (!res.ok) {
      console.warn(`Failed to fetch route for car ${carId}: HTTP ${res.status} - Using simulation`);
      // Fallback to simulated track for testing
      return generateSimulatedTrack(carId, timeWindow);
    }
    
    const json = await res.json();
    const points = Array.isArray(json?.data) ? json.data : [];
    
    if (points.length === 0) {
      console.warn(`No historical data for car ${carId} - Using simulation`);
      // Fallback to simulated track for testing
      return generateSimulatedTrack(carId, timeWindow);
    }

    // Convert to route format
    const route = points.map((point: any) => ({
      lat: point.latitude,
      lng: point.longitude,
      timestamp: new Date(point.timestamp)
    }));

    return {
      carId,
      route
    };
  } catch (error) {
    console.error('Failed to fetch car route:', error);
    // Fallback to simulated track for testing
    console.warn(`Using simulated track for car ${carId} due to error`);
    return generateSimulatedTrack(carId, timeWindow);
  }
}

// Function to get GPS statistics - works with real API data
export function getGPSStatistics() {
  // Note: This function works with static data. For real-time stats,
  // the UI should pass current locations array to this function.
  return {
    totalCars: 0,
    activeCars: 0,
    idleCars: 0,
    offlineCars: 0,
    averageSpeed: 0,
  }
}

// Function to calculate GPS statistics from provided locations array
export function calculateGPSStatistics(locations: GPSLocation[]) {
  const staleSeconds = Number(process.env.NEXT_PUBLIC_GPS_STALE_SECONDS || '60')
  const maxAgeMs = Math.max(0, staleSeconds) * 1000
  const now = Date.now()
  
  // Classify locations based on freshness and status
  const freshLocations = locations.filter(l => {
    const age = now - l.timestamp.getTime()
    return age <= maxAgeMs && age >= 0 // Also filter out future timestamps
  })
  const staleLocations = locations.filter(l => {
    const age = now - l.timestamp.getTime()
    return age > maxAgeMs || age < 0 // Include future timestamps as stale/invalid
  })
  
  const activeLocations = freshLocations.filter(l => l.status === 'active')
  const idleLocations = freshLocations.filter(l => l.status === 'idle')
  // Offline = explicitly offline status in fresh data OR all stale data (devices not sending fresh data)
  const explicitlyOfflineFresh = freshLocations.filter(l => l.status === 'offline')
  const offlineLocations = explicitlyOfflineFresh.concat(staleLocations)
  
  const averageSpeed = activeLocations.length > 0
    ? activeLocations.reduce((sum, l) => sum + l.speed, 0) / activeLocations.length
    : 0

  return {
    totalCars: locations.length,
    activeCars: activeLocations.length,
    idleCars: idleLocations.length,
    offlineCars: offlineLocations.length,
    averageSpeed: Math.round(averageSpeed * 10) / 10, // Round to 1 decimal
    freshDataCount: freshLocations.length, // Added for debugging/monitoring
    staleDataCount: staleLocations.length, // Added for debugging/monitoring
  }
}

// Utility function to calculate distance between GPS coordinates (Haversine formula)
// Used for GPS drift detection and movement analysis
export function calculateGPSDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371000; // Earth's radius in meters
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c; // Distance in meters
}