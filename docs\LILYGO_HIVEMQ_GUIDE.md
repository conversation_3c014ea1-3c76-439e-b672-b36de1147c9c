# Full Guide: Connecting the LILYGO T-Call A7670E to HiveMQ Cloud

This guide provides a complete walkthrough for setting up a real-time GPS tracking system using the LILYGO board and the HiveMQ MQTT cloud service. 

---

### Part 1: Set Up Your Free HiveMQ Cloud Broker

First, we need the central MQTT broker. We'll use HiveMQ's free plan, which is perfect for development and testing.

1.  **Sign Up for HiveMQ Cloud**
    *   Go to the [HiveMQ Cloud Portal](https://console.hivemq.cloud/).
    *   Sign up for a new account. Choose the **"Free"** plan. It requires no credit card and supports up to 100 device connections.

2.  **Create a New Cluster**
    *   Once logged in, you will be prompted to create a new cluster. Give it a name (e.g., `pathlink-tracker`).
    *   The cluster will take a minute or two to provision. Once it's ready, click on it to open its dashboard.

3.  **Get Your Broker Credentials**
    *   On the cluster's "Overview" page, you will find the essential connection details:
        *   **Hostname:** It will look something like `xxxxxxxxxxxx.s1.eu.hivemq.cloud`.
        *   **Port:** Use the **TLS-enabled port**, which is typically `8883`.

4.  **Create Device Access Credentials**
    *   In the left-hand menu, navigate to the **"Access Management"** tab.
    *   Click **"Add New Credentials"** to create a username and password for your LILYGO device.
    *   **Username:** `device-01` (or any name you prefer)
    *   **Password:** Use the auto-generated secure password or create your own.
    *   **Important:** Securely copy the Hostname, Port, Username, and Password. You will need them in the next part.

---

### Part 2: Configure and Flash the LILYGO Firmware

Now, we'll use the credentials from HiveMQ to configure the firmware I previously provided.

1.  **Open the Firmware File**
    *   In your IDE, open the file: `iot/esp32-firmware-template.ino`.

2.  **Install Required Libraries**
    *   If you haven't already, open the Arduino IDE, go to the Library Manager, and install:
        *   `TinyGsm` by Volodymyr Shymanskyy
        *   `PubSubClient` by Nick O'Leary
        *   `TinyGPS++` by Mikal Hart

3.  **Update the Configuration Section**
    *   Find the `CONFIGURATION` section in the code.
    *   Carefully replace the placeholder values with the credentials you just got from HiveMQ.

    ```cpp
    // =========================================================================
    // CONFIGURATION
    // (Replace with values from your PathLink database for each device)
    // =========================================================================

    // -- Cellular Configuration --
    const char apn[]      = "internet"; // Keep this or change to your provider's APN
    const char gsmUser[]  = "";
    const char gsmPass[]  = "";

    // -- MQTT Broker Configuration --
    const char* mqtt_broker_host = "xxxxxxxxxxxx.s1.eu.hivemq.cloud"; // <-- PASTE YOUR HIVEMQ HOSTNAME HERE
    const int   mqtt_broker_port = 8883;                               // <-- USE PORT 8883 for TLS
    const char* mqtt_client_id   = "lilygo-a7670e-01";                 // A unique ID for this device
    const char* mqtt_username    = "device-01";                        // <-- PASTE YOUR DEVICE USERNAME HERE
    const char* mqtt_password    = "YourSecurePassword123";            // <-- PASTE YOUR DEVICE PASSWORD HERE
    const char* mqtt_pub_topic   = "pathlink/gps/device-01";           // The topic to publish data to
    ```

    **Note on TLS:** The A7670E module requires a secure client for TLS connections on port 8883. The template uses `TinyGsmClient.h`. For a production setup, you would add the HiveMQ root CA certificate to the firmware for full server verification, but for initial testing, this setup will work.

4.  **Flash the Firmware**
    *   Connect your LILYGO board to your computer via USB.
    *   In the Arduino IDE, select the correct board and port.
    *   Click **Upload** to flash the firmware to the device.

---

### Part 3: Test the Real-Time Connection

Finally, let's verify that the device is online and publishing data.

1.  **Open the Serial Monitor**
    *   In the Arduino IDE, open the Serial Monitor and set the baud rate to `115200`.
    *   You should see logs as the device powers on, connects to the cellular network, and then connects to the MQTT broker.
    *   Look for a **"MQTT Connected!"** message.
    *   Once it gets a GPS fix, you will see it publishing a JSON payload.

2.  **Use the HiveMQ Web Client**
    *   Go back to your HiveMQ Cloud Portal.
    *   In the top menu, click the **"Web Client"** tab.
    *   The client will automatically connect using your admin credentials.

3.  **Subscribe to the GPS Topic**
    *   In the "Subscriptions" card, click **"Add New Topic Subscription"**.
    *   Enter the topic your device is publishing to. To catch all device messages, you can use a wildcard: `pathlink/gps/#`
    *   Click **Subscribe**.

4.  **Watch the Messages Arrive!**
    *   As soon as your LILYGO board gets a GPS fix and publishes, you will see the JSON messages appear in real-time in the web client.

    ![HiveMQ Web Client](https://www.hivemq.com/img/blog/connect-mqtt-client-to-hivemq-cloud/web-client-connected.png)

    You have now successfully connected your GPS device to a cloud MQTT broker. The next step will be to integrate an MQTT client into your React admin page to subscribe to these messages and display the location on a map.
