@echo off
echo Installing PathLink GUI Dependencies...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo Python found. Installing dependencies...
echo.

REM Install required packages
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo Error: Failed to install some dependencies
    echo Please check the error messages above
    pause
    exit /b 1
)

echo.
echo Dependencies installed successfully!
echo You can now run: python pathlink_gui.py
echo.
pause
