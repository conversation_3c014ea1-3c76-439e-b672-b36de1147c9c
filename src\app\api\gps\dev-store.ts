// Simple in-memory DEV store for GPS points when carId is not a UUID or DB insert fails
// This is for development only; data is volatile and resets on server restart.

export interface DevGpsPoint {
  id: string
  car_id?: string | null
  latitude: number
  longitude: number
  speed?: number
  heading?: number
  status?: string
  timestamp: string
  created_at: string
  car?: { model?: string; plate_number?: string }
}

const store: Map<string, DevGpsPoint> = new Map()

export function saveDevPoint(label: string, point: Omit<DevGpsPoint, 'id' | 'timestamp' | 'created_at'>) {
  const nowIso = new Date().toISOString();
  const id = `dev_${label}_${Date.now()}`;
  
  // Add driver name for ESP32 devices
  if (label.includes('esp32') || label.includes('lilygo')) {
    point.car = {
      ...point.car,
      model: point.car?.model || 'ESP32 Tracker',
      plate_number: point.car?.plate_number || 'ESP32-GPS'
    };
  }
  
  // Ensure status is set for all devices
  if (!point.status) {
    point.status = 'active';
  }
  
  store.set(label, {
    id,
    timestamp: nowIso,
    created_at: nowIso,
    ...point,
  });
  
  console.log(`💾 Saved point in dev-store for ${label} at ${nowIso}`);
}

export function readDevPoints(maxAgeMs: number = 60000): DevGpsPoint[] {
  const now = Date.now()
  const result: DevGpsPoint[] = []
  for (const [label, point] of store.entries()) {
    const ts = Date.parse(point.timestamp)
    if (!Number.isNaN(ts) && now - ts <= maxAgeMs) {
      result.push(point)
    } else {
      store.delete(label)
    }
  }
  return result
}
