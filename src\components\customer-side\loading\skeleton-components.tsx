"use client"

import React from "react"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"

// Customer Dashboard Skeleton with modern design
export function CustomerDashboardSkeleton({ 
  className,
  variant = "default" 
}: { 
  className?: string
  variant?: "default" | "compact" 
}) {
  const isCompact = variant === "compact"
  
  return (
    <div className={cn(
      "max-w-7xl mx-auto py-8",
      isCompact ? "px-3 sm:px-4 lg:px-6" : "px-4 sm:px-6 lg:px-8",
      className
    )} role="status" aria-busy="true" aria-label="Loading dashboard">
      {/* Header skeleton */}
      <div className={cn("mb-8 space-y-3", isCompact && "mb-6 space-y-2")}>
        <Skeleton 
          className={isCompact ? "h-7 w-48" : "h-8 w-64"} 
          
          aria-label="Loading dashboard title"
        />
        <Skeleton 
          className={isCompact ? "h-3 w-40" : "h-4 w-48"} 
          
          aria-label="Loading dashboard subtitle" 
        />
      </div>

      {/* Stats skeleton */}
      <div className={cn(
        "grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",
        isCompact && "gap-4 mb-6"
      )}>
        {Array.from({ length: 3 }, (_, i) => (
          <div key={i} className={cn(
            "bg-card border rounded-lg transition-all duration-200 hover:shadow-sm",
            isCompact ? "p-4" : "p-6"
          )}>
            <div className="flex items-center justify-between">
              <div className="space-y-2 flex-1">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-2 w-2" />
                  <Skeleton className={isCompact ? "h-3 w-20" : "h-4 w-24"} />
                </div>
                <Skeleton className={isCompact ? "h-6 w-12" : "h-8 w-16"} />
                <Skeleton className={isCompact ? "h-2 w-28" : "h-3 w-32"} />
              </div>
              <Skeleton className={isCompact ? "h-8 w-8" : "h-10 w-10"} />
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions skeleton */}
      <div className={cn("mb-8 bg-card rounded-lg border overflow-hidden", isCompact && "mb-6")}>
        <div className="bg-gradient-to-r from-primary/90 to-primary/70 text-primary-foreground">
          <div className={cn("flex items-center gap-3", isCompact ? "p-4" : "p-6")}>
            <Skeleton className="h-8 w-8 bg-white/20" />
            <div className="space-y-1 flex-1">
              <Skeleton className="h-4 w-28 bg-white/20" />
              <Skeleton className="h-3 w-40 bg-white/10" />
            </div>
          </div>
        </div>
        <div className={cn(isCompact ? "p-4" : "p-6")}>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }, (_, i) => (
              <div key={i} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-10 w-10" />
                  <div className="flex-1 space-y-1">
                    <Skeleton className="h-3 w-20" />
                    <Skeleton className="h-2 w-16" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Table skeleton */}
      <div className="space-y-3">
        {/* Header */}
        <div className={cn(
          "grid grid-cols-6 gap-4 rounded-lg font-medium",
          isCompact ? "p-3 bg-muted/30" : "p-4 bg-muted/50"
        )}>
          {Array.from({ length: 6 }, (_, i) => (
            <Skeleton key={i} className={isCompact ? "h-3" : "h-4"} />
          ))}
        </div>
        {/* Rows */}
        <div className="space-y-1">
          {Array.from({ length: isCompact ? 3 : 5 }, (_, i) => (
            <div key={i} className={cn(
              "grid grid-cols-6 gap-4 rounded-lg transition-colors hover:bg-muted/20 border",
              isCompact ? "p-3" : "p-4"
            )}>
              {Array.from({ length: 6 }, (_, j) => (
                <Skeleton key={j} className={isCompact ? "h-3" : "h-4"} />
              ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Customer Booking Flow Skeleton
export function CustomerBookingFlowSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("max-w-4xl mx-auto px-4 py-8", className)} role="status" aria-busy="true" aria-label="Loading booking form">
      {/* Progress bar skeleton */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          {Array.from({ length: 4 }, (_, i) => (
            <div key={i} className="flex items-center">
              <Skeleton className="h-8 w-8 rounded-full" />
              {i < 3 && <Skeleton className="h-1 w-16 mx-2" />}
            </div>
          ))}
        </div>
        <Skeleton className="h-6 w-48 mx-auto" />
      </div>

      {/* Content skeleton */}
      <div className="space-y-6">
        <div className="rounded-lg border bg-card p-6 space-y-4">
          <Skeleton className="h-48 w-full rounded-md" />
          <div className="space-y-2">
            <Skeleton className="h-5 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
          </div>
        </div>
        
        {/* Form fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full rounded-md" />
          </div>
          <div className="space-y-4">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full rounded-md" />
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex gap-4 pt-6">
          <Skeleton className="h-10 flex-1" />
          <Skeleton className="h-10 flex-1" />
        </div>
      </div>
    </div>
  )
}

// Customer Vehicle Catalog Skeleton with modern responsive design
export function CustomerVehicleCatalogSkeleton({ 
  className,
  variant = "default",
  itemCount = 9
}: { 
  className?: string
  variant?: "default" | "compact" | "grid-dense"
  itemCount?: number
}) {
  const isCompact = variant === "compact"
  const isDense = variant === "grid-dense"
  
  const getGridCols = () => {
    if (isDense) return "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
    if (isCompact) return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
    return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
  }
  
  return (
    <div className={cn(
      "max-w-7xl mx-auto py-8",
      isCompact ? "px-3" : "px-4",
      className
    )} role="status" aria-busy="true" aria-label="Loading vehicle catalog">
      {/* Header */}
      <div className={cn("mb-8 space-y-4", isCompact && "mb-6 space-y-3")}>
        <Skeleton 
          className={isCompact ? "h-6 w-40" : "h-8 w-48"} 
          
          aria-label="Loading catalog title"
        />
        
        {/* Search and filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Skeleton 
              className={isCompact ? "h-9 w-full" : "h-10 w-full"} 
              
              aria-label="Loading search bar" 
            />
          </div>
          <div className="flex gap-2 flex-wrap">
            {Array.from({ length: 3 }, (_, i) => (
              <Skeleton 
                key={i} 
                className={isCompact ? "h-9 w-20" : "h-10 w-24"} 
                
                aria-label={`Loading filter ${i + 1}`}
              />
            ))}
          </div>
        </div>
        
        {/* Filter tags */}
        <div className="flex gap-2 flex-wrap">
          {Array.from({ length: 2 }, (_, i) => (
            <Skeleton 
              key={i} 
              className="h-6 w-16" 
              
              aria-label={`Loading filter tag ${i + 1}`}
            />
          ))}
        </div>
      </div>

      {/* Results count */}
      <div className="mb-6">
        <Skeleton 
          className="h-4 w-32" 
          
          aria-label="Loading results count"
        />
      </div>

      {/* Vehicle grid */}
      <div className={cn(
        getGridCols(),
        "gap-6 mb-8",
        isCompact && "gap-4 mb-6",
        isDense && "gap-4"
      )}>
        {Array.from({ length: itemCount }, (_, i) => {
          const cardHeight = isCompact || isDense ? "h-32" : "h-48"
          const cardPadding = isCompact || isDense ? "p-4 space-y-3" : "p-6 space-y-4"
          return (
            <div key={i} className={cn("rounded-lg border bg-card", cardPadding)}>
              <Skeleton className={cn(cardHeight, "w-full rounded-md")} />
              <div className="space-y-2">
                <Skeleton className="h-5 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-4/5" />
              </div>
              <div className="flex gap-2 pt-2 border-t">
                <Skeleton className="h-10 flex-1" />
                <Skeleton className="h-10 flex-1" />
              </div>
            </div>
          )
        })}
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <Skeleton 
          className="h-4 w-24" 
          
          aria-label="Loading pagination info"
        />
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-20" aria-label="Loading previous button" />
          {Array.from({ length: 5 }, (_, i) => (
            <Skeleton 
              key={i} 
              className="h-8 w-8" 
              aria-label={`Loading page ${i + 1} button`}
            />
          ))}
          <Skeleton className="h-8 w-20" aria-label="Loading next button" />
        </div>
      </div>
    </div>
  )
}

// Customer Settings Skeleton
export function CustomerSettingsSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("max-w-4xl mx-auto px-4 py-8", className)} role="status" aria-busy="true" aria-label="Loading settings">
      {/* Header */}
      <div className="mb-8 space-y-2">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-4 w-64" />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="space-y-2">
          {Array.from({ length: 5 }, (_, i) => (
            <div key={i} className="flex items-center gap-3 p-3">
              <Skeleton className="h-5 w-5" />
              <Skeleton className="h-4 w-24" />
            </div>
          ))}
        </div>

        {/* Content */}
        <div className="lg:col-span-3 space-y-8">
          {/* Profile section */}
          <div className="bg-white rounded-lg border p-6">
            <div className="flex items-center gap-4 mb-6">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-48" />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Array.from({ length: 4 }, (_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ))}
            </div>
          </div>

          {/* Documents section */}
          <div className="bg-white rounded-lg border p-6">
            <Skeleton className="h-6 w-40 mb-6" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Array.from({ length: 3 }, (_, i) => (
                <div key={i} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <Skeleton className="h-5 w-28" />
                    <Skeleton className="h-6 w-16 rounded-full" />
                  </div>
                  <Skeleton className="h-32 w-full rounded-md mb-3" />
                  <Skeleton className="h-8 w-full" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Customer Modal Skeleton
export function CustomerModalSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("p-6 space-y-6", className)} role="status" aria-busy="true" aria-label="Loading modal content">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-6 w-6 rounded" />
      </div>

      {/* Content */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-4/5" />
          <Skeleton className="h-4 w-3/5" />
        </div>
        <Skeleton className="h-48 w-full rounded-md" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-3 justify-end">
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-24" />
      </div>
    </div>
  )
}

// Customer Unified Table Skeleton 
export function CustomerUnifiedTableSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-4", className)} role="status" aria-busy="true" aria-label="Loading booking and payment data">
      {/* Filter tabs */}
      <div className="flex gap-2 mb-4">
        <Skeleton className="h-8 w-20 rounded-full" />
        <Skeleton className="h-8 w-20 rounded-full" />
        <Skeleton className="h-8 w-16 rounded-full" />
      </div>

      {/* Mobile cards (visible on small screens) */}
      <div className="block md:hidden space-y-4">
        {Array.from({ length: 5 }, (_, i) => (
          <div key={i} className="bg-white border rounded-lg p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="space-y-1">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-24" />
              </div>
              <Skeleton className="h-6 w-16 rounded-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-3/4" />
            </div>
            <div className="flex gap-2 mt-4">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-20" />
            </div>
          </div>
        ))}
      </div>

      {/* Desktop table (visible on larger screens) */}
      <div className="hidden md:block">
        <div className="space-y-3">
          {/* Header */}
          <div className="grid grid-cols-7 gap-4 p-4 bg-muted/50 rounded-lg font-medium">
            {Array.from({ length: 7 }, (_, i) => (
              <Skeleton key={i} className="h-4" />
            ))}
          </div>
          {/* Rows */}
          <div className="space-y-1">
            {Array.from({ length: 8 }, (_, i) => (
              <div key={i} className="grid grid-cols-7 gap-4 p-4 border rounded-lg transition-colors hover:bg-muted/20">
                {Array.from({ length: 7 }, (_, j) => (
                  <Skeleton key={j} className="h-4" />
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Customer Booking Summary Skeleton
export function CustomerBookingSummarySkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-6", className)} role="status" aria-busy="true" aria-label="Loading booking summary">
      {/* Vehicle details */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex flex-col lg:flex-row gap-6">
          <Skeleton className="h-48 w-full lg:w-72 rounded-md" />
          <div className="flex-1 space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              {Array.from({ length: 4 }, (_, i) => (
                <div key={i} className="text-center">
                  <Skeleton className="h-4 w-full mb-1" />
                  <Skeleton className="h-3 w-3/4 mx-auto" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Booking details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <Skeleton className="h-5 w-24 mb-4" />
          <div className="space-y-3">
            {Array.from({ length: 4 }, (_, i) => (
              <div key={i} className="flex justify-between">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-32" />
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <Skeleton className="h-5 w-32 mb-4" />
          <div className="space-y-3">
            {Array.from({ length: 5 }, (_, i) => (
              <div key={i} className="flex justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-20" />
              </div>
            ))}
            <div className="border-t pt-3 mt-4">
              <div className="flex justify-between">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-5 w-24" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
