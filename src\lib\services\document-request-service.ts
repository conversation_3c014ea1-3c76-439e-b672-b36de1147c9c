"use server";

import { createClient } from "@/lib/supabase/server";
import { sendEmail } from "@/lib/email";
import { buildDocumentRequestEmail } from "@/lib/email-templates/document-request-template";

export interface DocumentRequest {
  id: string;
  booking_id: string;
  customer_id: string;
  document_type: string;
  request_type: 'missing' | 'revision';
  admin_notes?: string;
  status: 'pending' | 'fulfilled' | 'expired';
  requested_by: string;
  requested_at: string;
  fulfilled_at?: string;
  expires_at: string;
  email_sent: boolean;
  email_sent_at?: string;
  created_at: string;
  updated_at: string;
}

export interface DocumentRequestWithDetails extends DocumentRequest {
  booking?: {
    id: string;
    status: string;
    pickup_datetime: string;
    dropoff_datetime: string;
    car_model?: string;
    car_plate?: string;
  };
  customer?: {
    id: string;
    email: string;
    full_name?: string;
  };
  requested_by_admin?: {
    id: string;
    full_name?: string;
    email: string;
  };
}

export interface MissingDocumentCheck {
  document_type: string;
  is_missing: boolean;
  current_status: string;
}

export interface CreateDocumentRequestParams {
  bookingId: string;
  customerId: string;
  customerEmail: string;
  customerName?: string;
  documentTypes: string[];
  requestType: 'missing' | 'revision';
  adminNotes?: string;
}

/**
 * Check which documents are missing or need revision for a booking
 */
export async function checkMissingDocumentsForBooking(
  bookingId: string
): Promise<{ success: boolean; data?: MissingDocumentCheck[]; error?: string }> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase.rpc('check_missing_documents_for_booking', {
      booking_uuid: bookingId
    });

    if (error) {
      console.error("Error checking missing documents:", error);
      return { success: false, error: error.message };
    }

    return { success: true, data: data || [] };
  } catch (error) {
    console.error("Error checking missing documents:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to check missing documents" 
    };
  }
}

/**
 * Get pending document requests for a customer
 */
export async function getCustomerPendingDocumentRequests(
  customerId: string
): Promise<{ success: boolean; data?: any[]; error?: string }> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase.rpc('get_customer_pending_document_requests', {
      customer_uuid: customerId
    });

    if (error) {
      console.error("Error fetching pending document requests:", error);
      return { success: false, error: error.message };
    }

    return { success: true, data: data || [] };
  } catch (error) {
    console.error("Error fetching pending document requests:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to fetch document requests" 
    };
  }
}

/**
 * Create document requests for missing/revision documents
 */
export async function createDocumentRequests(
  params: CreateDocumentRequestParams
): Promise<{ success: boolean; data?: DocumentRequest[]; error?: string }> {
  const supabase = await createClient();

  try {
    // Get current admin user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: "Admin authentication required" };
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from("profiles")
      .select("role, full_name")
      .eq("id", user.id)
      .single();

    if (!profile || (profile.role !== "admin" && profile.role !== "super_admin")) {
      return { success: false, error: "Admin privileges required" };
    }

    // First, expire any existing pending requests for these document types
    await supabase
      .from("document_requests")
      .update({
        status: 'expired',
        updated_at: new Date().toISOString()
      })
      .eq('booking_id', params.bookingId)
      .eq('customer_id', params.customerId)
      .in('document_type', params.documentTypes)
      .eq('status', 'pending');

    // Create new document requests
    const requestsToCreate = params.documentTypes.map(docType => ({
      booking_id: params.bookingId,
      customer_id: params.customerId,
      document_type: docType,
      request_type: params.requestType,
      admin_notes: params.adminNotes,
      requested_by: user.id,
      status: 'pending' as const
    }));

    const { data: requests, error: insertError } = await supabase
      .from("document_requests")
      .insert(requestsToCreate)
      .select();

    if (insertError) {
      console.error("Error creating document requests:", insertError);
      return { success: false, error: insertError.message };
    }

    // Send email notification
    if (params.customerEmail) {
      try {
        const emailResult = await sendDocumentRequestEmail({
          customerEmail: params.customerEmail,
          customerName: params.customerName,
          documentTypes: params.documentTypes,
          requestType: params.requestType,
          adminNotes: params.adminNotes,
          adminName: profile.full_name
        });

        // Update email_sent status for successful sends
        if (emailResult.success && requests) {
          const requestIds = requests.map(r => r.id);
          await supabase
            .from("document_requests")
            .update({
              email_sent: true,
              email_sent_at: new Date().toISOString()
            })
            .in('id', requestIds);
        }
      } catch (emailError) {
        console.error("Failed to send email notification:", emailError);
        // Don't fail the whole operation if email fails
      }
    }

    return { success: true, data: requests };
  } catch (error) {
    console.error("Error creating document requests:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to create document requests" 
    };
  }
}

/**
 * Get all document requests with details (admin view)
 */
export async function getDocumentRequests(
  bookingId?: string,
  customerId?: string,
  status?: string
): Promise<{ success: boolean; data?: DocumentRequestWithDetails[]; error?: string }> {
  const supabase = await createClient();

  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: "Authentication required" };
    }

    let query = supabase
      .from("document_requests")
      .select(`
        *,
        profiles!document_requests_customer_id_fkey (
          id,
          email,
          full_name
        ),
        profiles!document_requests_requested_by_fkey (
          id,
          email,
          full_name
        ),
        bookings (
          id,
          status,
          pickup_datetime,
          dropoff_datetime,
          cars (
            model,
            plate_number
          )
        )
      `)
      .order('created_at', { ascending: false });

    if (bookingId) {
      query = query.eq('booking_id', bookingId);
    }
    
    if (customerId) {
      query = query.eq('customer_id', customerId);
    }
    
    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching document requests:", error);
      return { success: false, error: error.message };
    }

    // Transform the data to match our interface
    const transformedData: DocumentRequestWithDetails[] = data?.map(request => ({
      ...request,
      customer: request.profiles[0], // customer profile
      requested_by_admin: request.profiles[1], // admin who requested
      booking: request.bookings ? {
        id: request.bookings.id,
        status: request.bookings.status,
        pickup_datetime: request.bookings.pickup_datetime,
        dropoff_datetime: request.bookings.dropoff_datetime,
        car_model: request.bookings.cars?.model,
        car_plate: request.bookings.cars?.plate_number
      } : undefined
    })) || [];

    return { success: true, data: transformedData };
  } catch (error) {
    console.error("Error fetching document requests:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to fetch document requests" 
    };
  }
}

/**
 * Mark document request as fulfilled (when customer uploads document)
 */
export async function fulfillDocumentRequest(
  requestId: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  try {
    const { error } = await supabase
      .from("document_requests")
      .update({
        status: 'fulfilled',
        fulfilled_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId);

    if (error) {
      console.error("Error fulfilling document request:", error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error("Error fulfilling document request:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to fulfill document request" 
    };
  }
}

/**
 * Expire old document requests (can be called by cron job)
 */
export async function expireOldDocumentRequests(): Promise<{ success: boolean; expiredCount?: number; error?: string }> {
  const supabase = await createClient();

  try {
    const { data, error } = await supabase.rpc('expire_old_document_requests');

    if (error) {
      console.error("Error expiring old document requests:", error);
      return { success: false, error: error.message };
    }

    return { success: true, expiredCount: data || 0 };
  } catch (error) {
    console.error("Error expiring old document requests:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to expire old document requests" 
    };
  }
}

/**
 * Send document request email notification
 */
async function sendDocumentRequestEmail(params: {
  customerEmail: string;
  customerName?: string;
  documentTypes: string[];
  requestType: 'missing' | 'revision';
  adminNotes?: string;
  adminName?: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const { subject, html } = buildDocumentRequestEmail({
      customerName: params.customerName,
      documentTypes: params.documentTypes,
      requestType: params.requestType,
      adminNotes: params.adminNotes,
      adminName: params.adminName
    });

    const result = await sendEmail({
      to: params.customerEmail,
      subject,
      html
    });

    return { success: result.ok, error: result.error };
  } catch (error) {
    console.error("Error sending document request email:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to send email" 
    };
  }
}

/**
 * Auto-fulfill document requests when a document is uploaded
 */
export async function autoFulfillDocumentRequests(
  userId: string, 
  documentType: string
): Promise<{ success: boolean; data?: { fulfilledCount: number }; error?: string }> {
  try {
    const supabase = await createClient();

    // Get pending document requests for this user and document type
    const { data: requests, error: fetchError } = await supabase
      .from('document_requests')
      .select('id, booking_id')
      .eq('customer_id', userId)
      .eq('document_type', documentType)
      .eq('status', 'pending');

    if (fetchError) {
      console.error('Error fetching document requests to fulfill:', fetchError);
      return {
        success: false,
        error: `Failed to fetch document requests: ${fetchError.message}`
      };
    }

    if (!requests || requests.length === 0) {
      return {
        success: true,
        data: { fulfilledCount: 0 }
      };
    }

    // Mark all matching requests as fulfilled
    const { data: updatedRequests, error: updateError } = await supabase
      .from('document_requests')
      .update({
        status: 'fulfilled',
        fulfilled_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .in('id', requests.map((req: any) => req.id))
      .select();

    if (updateError) {
      console.error('Error fulfilling document requests:', updateError);
      return {
        success: false,
        error: `Failed to fulfill document requests: ${updateError.message}`
      };
    }

    return {
      success: true,
      data: { fulfilledCount: updatedRequests?.length || 0 }
    };

  } catch (error) {
    console.error('Unexpected error auto-fulfilling document requests:', error);
    return {
      success: false,
      error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}
