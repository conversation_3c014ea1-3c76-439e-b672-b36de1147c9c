"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DocumentUpload,
  type DocumentFile,
} from "@/components/ui/document-upload";
import {
  Shield,
  FileText,
  Home,
  AlertTriangle,
  CheckCircle2,
  Clock,
  XCircle,
  Upload,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  checkUserLegalDocuments,
  saveUserLegalDocument,
  deleteUserLegalDocument,
  getStandardDocumentType,
  type UserLegalDocument,
  type DocumentCheckResult,
} from "@/lib/services/document-service";
import { autoFulfillDocumentRequests } from "@/lib/services/document-request-service";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";

interface RequirementConfig {
  key: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  required: boolean;
  accept: string;
  details: string[];
}

const REQUIREMENTS: RequirementConfig[] = [
  {
    key: "driversLicense",
    title: "Driver's License",
    description: "Valid Philippine driver's license",
    icon: Shield,
    required: true,
    accept: "image/jpeg,image/jpg,image/png,application/pdf",
    details: [
      "Must be a valid Philippine driver's license",
      "License should have at least 1 year validity remaining",
      "Clear photo showing all details",
      "Both front and back if applicable",
    ],
  },
  {
    key: "governmentId",
    title: "Government ID",
    description: "Valid government-issued identification",
    icon: FileText,
    required: true,
    accept: "image/jpeg,image/jpg,image/png,application/pdf",
    details: [
      "Passport, National ID, SSS ID, or other government-issued ID",
      "Must be currently valid and unexpired",
      "Clear and readable photo",
      "Name must match driver's license",
    ],
  },
  {
    key: "proofOfBilling",
    title: "Proof of Billing",
    description: "Recent utility bill or billing statement",
    icon: Home,
    required: true,
    accept: "image/jpeg,image/jpg,image/png,application/pdf",
    details: [
      "Recent utility bill (electricity, water, internet, etc.)",
      "Credit card or bank statement",
      "Must be dated within the last 3 months",
      "Address must be clearly visible and readable",
    ],
  },
];

// UserLegalDocument interface is now imported from document-service

// Custom interface for document files with optional file property
interface CustomDocumentFile {
  id: string;
  file?: File;
  fileName?: string;
  fileSize?: number;
  fileType?: string;
  url?: string;
  status: "uploading" | "completed" | "error";
  error?: string;
  progress?: number;
}

export function LegalDocumentsSection() {
  const { toast } = useToast();
  const { user } = useCustomerAuth();
  const [loading, setLoading] = React.useState(true);
  const [documents, setDocuments] = React.useState<Record<string, CustomDocumentFile[]>>({
    driversLicense: [],
    governmentId: [],
    proofOfBilling: [],
  });
  const [userDocuments, setUserDocuments] = React.useState<UserLegalDocument[]>([]);
  const [documentCheck, setDocumentCheck] = React.useState<DocumentCheckResult | null>(null);
  const [pendingUploads, setPendingUploads] = React.useState<Record<string, CustomDocumentFile[]>>({});
  const [hasChanges, setHasChanges] = React.useState(false);
  const [saving, setSaving] = React.useState(false);

  // Fetch user's legal documents
  React.useEffect(() => {
    async function fetchUserDocuments() {
      setLoading(true);
      try {
        const result = await checkUserLegalDocuments();
        setDocumentCheck(result);
        setUserDocuments(result.existingDocuments);
        
        // Convert to DocumentFile format for the DocumentUpload component
        const docMap: Record<string, CustomDocumentFile[]> = {
          driversLicense: [],
          governmentId: [],
          proofOfBilling: [],
        };
        
        result.existingDocuments.forEach((doc: UserLegalDocument) => {
          // Map database document_type to UI key format
          let uiKey = "";
          switch (doc.document_type) {
            case "drivers_license":
              uiKey = "driversLicense";
              break;
            case "government_id":
              uiKey = "governmentId";
              break;
            case "proof_of_billing":
              uiKey = "proofOfBilling";
              break;
            default:
              return; // Skip unknown document types
          }
          
          if (docMap[uiKey]) {
            docMap[uiKey].push({
              id: doc.id,
              fileName: doc.file_name,
              fileSize: doc.file_size,
              fileType: doc.file_type,
              url: doc.file_url,
              status: "completed",
              progress: 100,
              file: new File([], doc.file_name || 'Document', { 
                type: doc.file_type || 'application/pdf' 
              }),
            });
          }
        });
        
        setDocuments(docMap);
      } catch (error) {
        console.error("Unexpected error:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load your legal documents. Please try again.",
        });
      } finally {
        setLoading(false);
      }
    }

    fetchUserDocuments();
  }, [toast]);

  const handleFileChange = (requirementKey: string, files: CustomDocumentFile[]) => {
    // Update local state for immediate UI feedback
    setDocuments(prev => ({
      ...prev,
      [requirementKey]: files
    }));
    
    // Track pending uploads for batch confirmation
    setPendingUploads(prev => ({
      ...prev,
      [requirementKey]: files
    }));
    
    setHasChanges(true);
  };

  const handleConfirmUploads = async () => {
    setSaving(true);
    let uploadCount = 0;
    let errorCount = 0;
    
    try {
      for (const [requirementKey, files] of Object.entries(pendingUploads)) {
        if (files.length === 0) {
          // Handle deletion
          const docToDelete = userDocuments.find(doc => doc.document_type === requirementKey);
          if (docToDelete) {
            const deleteResult = await deleteUserLegalDocument(docToDelete.id);
            if (deleteResult.success) {
              uploadCount++;
            } else {
              errorCount++;
              console.error(`Failed to delete ${requirementKey}:`, deleteResult.error);
            }
          }
        } else if (files.length > 0 && files[0].file && files[0].status === "completed") {
          // Handle upload
          const file = files[0].file;
          const result = await saveUserLegalDocument(requirementKey, file);
          
          if (result.success) {
            uploadCount++;
            
            // Auto-fulfill any pending document requests for this document type
            if (user?.id) {
              const standardType = getStandardDocumentType(requirementKey);
              try {
                const fulfillResult = await autoFulfillDocumentRequests(user.id, standardType);
                if (fulfillResult.success && fulfillResult.data && fulfillResult.data.fulfilledCount > 0) {
                  console.info(`Auto-fulfilled ${fulfillResult.data.fulfilledCount} document request(s) for ${standardType}`);
                }
              } catch (error) {
                console.error('Error auto-fulfilling document requests:', error);
                // Don't fail the main upload process if auto-fulfill fails
              }
            }
          } else {
            errorCount++;
            console.error(`Failed to save ${requirementKey}:`, result.error);
          }
        }
      }
      
      if (uploadCount > 0) {
        toast({
          title: "Documents Saved",
          description: `${uploadCount} document(s) have been successfully saved.`,
        });
        
        // Reload documents to get updated data
        const result = await checkUserLegalDocuments();
        setDocumentCheck(result);
        setUserDocuments(result.existingDocuments);
        
        // Update documents display
        const docMap: Record<string, CustomDocumentFile[]> = {
          driversLicense: [],
          governmentId: [],
          proofOfBilling: [],
        };
        
        result.existingDocuments.forEach((doc: UserLegalDocument) => {
          // Map database document_type to UI key format
          let uiKey = "";
          switch (doc.document_type) {
            case "drivers_license":
              uiKey = "driversLicense";
              break;
            case "government_id":
              uiKey = "governmentId";
              break;
            case "proof_of_billing":
              uiKey = "proofOfBilling";
              break;
            default:
              return; // Skip unknown document types
          }
          
          if (docMap[uiKey]) {
            docMap[uiKey].push({
              id: doc.id,
              fileName: doc.file_name,
              fileSize: doc.file_size,
              fileType: doc.file_type,
              url: doc.file_url,
              status: "completed",
              progress: 100,
              file: new File([], doc.file_name || 'Document', { 
                type: doc.file_type || 'application/pdf' 
              }),
            });
          }
        });
        
        setDocuments(docMap);
      }
      
      if (errorCount > 0) {
        toast({
          variant: "destructive",
          title: "Upload Errors",
          description: `${errorCount} document(s) failed to save. Please try again.`,
        });
      }
      
      // Clear pending uploads
      setPendingUploads({});
      setHasChanges(false);
      
    } catch (error) {
      console.error("Error saving documents:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to save documents. Please try again.",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleDiscardChanges = () => {
    // Reset to original state
    const docMap: Record<string, CustomDocumentFile[]> = {
      driversLicense: [],
      governmentId: [],
      proofOfBilling: [],
    };
    
    userDocuments.forEach((doc: UserLegalDocument) => {
      // Map database document_type to UI key format
      let uiKey = "";
      switch (doc.document_type) {
        case "drivers_license":
          uiKey = "driversLicense";
          break;
        case "government_id":
          uiKey = "governmentId";
          break;
        case "proof_of_billing":
          uiKey = "proofOfBilling";
          break;
        default:
          return; // Skip unknown document types
      }
      
      if (docMap[uiKey]) {
        docMap[uiKey].push({
          id: doc.id,
          fileName: doc.file_name,
          fileSize: doc.file_size,
          fileType: doc.file_type,
          url: doc.file_url,
          status: "completed",
          progress: 100,
          file: undefined,
        });
      }
    });
    
    setDocuments(docMap);
    setPendingUploads({});
    setHasChanges(false);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return (
          <Badge className="bg-green-100 text-green-700 border-green-200">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Approved
          </Badge>
        );
      case "pending":
        return (
          <Badge className="bg-yellow-100 text-yellow-700 border-yellow-200">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case "rejected":
        return (
          <Badge className="bg-red-100 text-red-700 border-red-200">
            <XCircle className="h-3 w-3 mr-1" />
            Rejected
          </Badge>
        );
      case "requires_resubmission":
        return (
          <Badge className="bg-orange-100 text-orange-700 border-orange-200">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Resubmit
          </Badge>
        );
      default:
        return null;
    }
  };

  const completedCount = documentCheck?.documentSummary.approved || 0;
  const pendingCount = documentCheck?.documentSummary.pending || 0;
  const rejectedCount = documentCheck?.documentSummary.rejected || 0;
  const missingCount = documentCheck?.documentSummary.missing || 0;
  const totalCount = REQUIREMENTS.length;

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            Legal Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="border border-gray-200 rounded-lg p-4 animate-pulse">
                <div className="flex items-start justify-between mb-3">
                  <div className="h-6 bg-gray-200 rounded w-40"></div>
                </div>
                <div className="h-24 bg-gray-200 rounded-md"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card id="legal-documents-section">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-blue-600" />
          Legal Documents
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Status Summary */}
        <div className="mb-6 grid grid-cols-2 sm:grid-cols-4 gap-4">
          <div className="bg-green-50 border border-green-100 rounded-lg p-3 text-center">
            <div className="text-green-700 text-xl font-semibold">{completedCount}</div>
            <div className="text-green-600 text-sm">Approved</div>
          </div>
          <div className="bg-yellow-50 border border-yellow-100 rounded-lg p-3 text-center">
            <div className="text-yellow-700 text-xl font-semibold">{pendingCount}</div>
            <div className="text-yellow-600 text-sm">Pending</div>
          </div>
          <div className="bg-red-50 border border-red-100 rounded-lg p-3 text-center">
            <div className="text-red-700 text-xl font-semibold">{rejectedCount}</div>
            <div className="text-red-600 text-sm">Rejected</div>
          </div>
          <div className="bg-gray-50 border border-gray-100 rounded-lg p-3 text-center">
            <div className="text-gray-700 text-xl font-semibold">{missingCount}</div>
            <div className="text-gray-600 text-sm">Missing</div>
          </div>
        </div>

        {/* Pending Changes Banner */}
        {hasChanges && (
          <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <span className="text-yellow-800 font-medium">You have unsaved changes</span>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={handleDiscardChanges}
                  disabled={saving}
                >
                  Discard Changes
                </Button>
                <Button
                  size="sm"
                  onClick={handleConfirmUploads}
                  disabled={saving}
                  className="flex items-center gap-1"
                >
                  <Upload className="h-3 w-3" />
                  {saving ? "Saving..." : "Save All Documents"}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Document Upload Sections */}
        <div className="space-y-6">
          {REQUIREMENTS.map((requirement) => {
            const files = documents[requirement.key] || [];
            const standardType = getStandardDocumentType(requirement.key);
            const userDoc = userDocuments.find(doc => doc.document_type === standardType);
            
            return (
              <div
                key={requirement.key}
                className="border rounded-lg p-6 bg-white"
                style={{
                  borderColor: '#E0E0E0',
                  borderRadius: '12px',
                  backgroundColor: '#FFFFFF'
                }}
              >
                {/* Document Header */}
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 
                      className="font-semibold mb-2 flex items-center gap-2"
                      style={{
                        fontSize: '18px',
                        fontWeight: 600,
                        color: '#212121',
                        lineHeight: '24px'
                      }}
                    >
                      <requirement.icon className="h-5 w-5 text-blue-600" />
                      {requirement.title}
                      {userDoc && getStatusBadge(userDoc.verification_status)}
                    </h3>
                    <p 
                      style={{
                        fontSize: '14px',
                        color: '#616161',
                        lineHeight: '20px'
                      }}
                    >
                      {requirement.description}
                    </p>
                  </div>
                </div>

                {/* Verification Notes (if any) */}
                {userDoc?.verification_notes && (
                  <div className="mb-4 p-3 bg-yellow-50 border border-yellow-100 rounded-lg text-sm text-yellow-800">
                    <p className="font-medium mb-1">Verification Notes:</p>
                    <p>{userDoc.verification_notes}</p>
                  </div>
                )}

                {/* Upload Area */}
                <DocumentUpload
                  label=""
                  files={files as DocumentFile[]}
                  onChange={(newFiles) => handleFileChange(requirement.key, newFiles as CustomDocumentFile[])}
                  accept={requirement.accept}
                  maxFiles={1}
                  required={false}
                  className="w-full"
                  uploadFolder="legal-documents"
                />

                {/* Document Requirements */}
                <div className="mt-4 text-sm text-gray-500">
                  <p className="font-medium mb-1">Requirements:</p>
                  <ul className="list-disc pl-5 space-y-1">
                    {requirement.details.map((detail, idx) => (
                      <li key={idx}>{detail}</li>
                    ))}
                  </ul>
                </div>
              </div>
            );
          })}
        </div>

        {/* Help Text */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-100 rounded-lg">
          <h4 className="text-blue-700 font-medium mb-2 flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Why do we need these documents?
          </h4>
          <p className="text-sm text-blue-600">
            These documents help us verify your identity and address for security purposes. 
            They also speed up the booking process by pre-filling your information. 
            All documents are securely stored and handled according to our privacy policy.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
