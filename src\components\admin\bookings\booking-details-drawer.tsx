"use client";

import * as React from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { StatusBadge, PaymentBadge } from "./status-badges";
import { RenterIssueTracking } from "./renter-issue-tracking";
import { useRenterIssueData } from "@/hooks/use-renter-issues";
import { logWithContext } from "@/lib/utils/logger";
import {
  CalendarIcon,
  MapPinIcon,
  PhoneIcon,
  MailIcon,
  CopyIcon,
  ExternalLinkIcon,
  CreditCardIcon,
  ClockIcon,
  CarIcon,
  UserIcon,
  ReceiptIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  FileTextIcon,
  UploadIcon,
  AlertCircleIcon,
  ShieldCheckIcon,
  X as XIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { format, formatDuration, intervalToDuration } from "date-fns";
import { type Booking, type Car, type User, type Payment } from "@/lib/types";
import { formatBookingIdForDisplay, formatPaymentIdForDisplay } from "@/lib/reference-ids";
import { 
  getBookingDocuments, 
  verifyBookingDocument, 
  requireDocumentResubmission,
  getBookingDocumentStatus 
} from "@/app/admin/bookings/actions/document-actions";
import { adminDecideBooking } from "@/app/admin/bookings/actions/decision-actions";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { RejectionReasonModal } from "./rejection-reason-modal";

interface BookingDetailsDrawerProps {
  booking:
    | (Booking & {
        userName: string;
        carModel: string;
        from: Date;
        to: Date;
        days: number;
        payStatus: "Paid" | "Unpaid" | "Partial" | "Refunded";
        totalAmount: number;
      })
    | null;
  car?: Car | null;
  customer?: User | null;
  payment?: Payment | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStatusChange?: (bookingId: string, status: string) => void;
  onPaymentStatusChange?: (bookingId: string, status: string) => void;
  onPaymentVerification?: (
    paymentId: string,
    action: "verify" | "reject",
    notes?: string
  ) => void;
  currentAdminId?: string; // Add admin ID for issue tracking
  onAddToCalendar?: () => void; // External calendar integration
  onAddToOnPageCalendar?: () => void; // In-app calendar integration
  useOnPageCalendar?: boolean; // Whether to use in-app calendar integration
}

interface BookingDocument {
  id: string;
  booking_id: string;
  document_type: string;
  file_url: string;
  file_name: string;
  file_size: number;
  file_type: string;
  verification_status: "pending" | "approved" | "rejected" | "requires_resubmission";
  verification_notes?: string;
  verified_by?: string;
  verified_at?: string;
  created_at: string;
  updated_at: string;
}

export function BookingDetailsDrawer({
  booking,
  car,
  customer,
  payment,
  open,
  onOpenChange,
  onStatusChange,
  onPaymentStatusChange,
  onPaymentVerification,
  currentAdminId,
  onAddToCalendar,
  onAddToOnPageCalendar,
  useOnPageCalendar,
}: BookingDetailsDrawerProps) {
  const { toast } = useToast();
  const [documents, setDocuments] = React.useState<BookingDocument[]>([]);
  const [documentStatus, setDocumentStatus] = React.useState<any>(null);
  const [loadingDocuments, setLoadingDocuments] = React.useState(false);

  // Debug logging for data flow
  React.useEffect(() => {
    if (open) {
      logWithContext("BookingDetailsDrawer", "Props received:", {
        booking: booking ? { id: booking.id, payStatus: booking.payStatus, userName: booking.userName } : null,
        customer: customer ? {
          id: customer.id,
          full_name: customer.full_name,
          first_name: customer.first_name,
          middle_initial: customer.middle_initial,
          last_name: customer.last_name,
          email: customer.email
        } : null,
        payment: payment ? { id: payment.id, status: payment.status } : null
      });
    }
  }, [open, booking, customer, payment]);
  // STREAMLINED WORKFLOW: Modal viewer is now used ONLY for viewing content, not for verification actions
  // Verification actions (approve/reject/request resubmission) are handled directly from cards
  const [viewerOpen, setViewerOpen] = React.useState(false);
  const [viewerNotes, setViewerNotes] = React.useState(""); // Kept for future extensibility
  const [viewerItem, setViewerItem] = React.useState<
    | { type: "document"; url: string; title: string; mime?: string; document: BookingDocument }
    | { type: "payment"; url: string; title: string; mime?: string }
    | null
  >(null);
  const [decisionDialogOpen, setDecisionDialogOpen] = React.useState(false);
  const [decisionAction, setDecisionAction] = React.useState<"confirm" | null>(null);
  const [decisionNotes, setDecisionNotes] = React.useState("");
  
  // Request Resubmission modal state (only for resubmission, not reject)
  const [resubmissionModalOpen, setResubmissionModalOpen] = React.useState(false);
  const [resubmissionDocument, setResubmissionDocument] = React.useState<BookingDocument | null>(null);
  

  const documentTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      drivers_license: "Driver's License",
      government_id: "Government ID",
      proof_of_billing: "Proof of Billing",
      proof_of_payment: "Payment Proof",
    };
    return labels[type] || type;
  };

  // STREAMLINED: Document viewer is now purely for viewing, not for verification actions
  const openDocumentViewer = (doc: BookingDocument) => {
    setViewerItem({
      type: "document",
      url: doc.file_url,
      title: `${documentTypeLabel(doc.document_type)} • ${doc.file_name}`,
      mime: doc.file_type,
      document: doc,
    });
    setViewerNotes("");
    setViewerOpen(true);
  };




  const handleBookingDecision = async (
    notes?: string
  ) => {
    if (!booking) return;
    try {
      const result = await adminDecideBooking(booking.id, "confirm", notes);
      if ((result as any)?.error) {
        toast({
          variant: "destructive",
          title: "Booking decision failed",
          description: (result as any).error.message || "An error occurred",
        });
        return;
      }
      const newStatus = "Active";
      if (onStatusChange) {
        onStatusChange(booking.id, newStatus);
      }
      toast({
        title: "Booking finalized",
        description: notes ? `Notes: ${notes}` : undefined,
      });
    } catch (err) {
      console.error("Error deciding booking:", err);
      toast({
        variant: "destructive",
        title: "Failed to process booking decision",
        description: "Please try again.",
      });
    }
  };

  // Fetch booking documents when drawer opens
  React.useEffect(() => {
    if (open && booking?.id) {
      logWithContext("BookingDetailsDrawer", "Fetching documents for booking:", booking.id);
      fetchBookingDocuments();
    }
  }, [open, booking?.id]);

  // Debug payment data
  React.useEffect(() => {
    logWithContext("BookingDetailsDrawer", "Payment data in drawer:", payment);
  }, [payment]);

  const fetchBookingDocuments = async () => {
    if (!booking?.id) return;
    
    setLoadingDocuments(true);
    try {
      logWithContext("BookingDetailsDrawer", "Starting document fetch for booking:", booking.id);
      const [documentsResult, statusResult] = await Promise.all([
        getBookingDocuments(booking.id),
        getBookingDocumentStatus(booking.id)
      ]);

      logWithContext("BookingDetailsDrawer", "Documents result:", documentsResult);
      logWithContext("BookingDetailsDrawer", "Status result:", statusResult);

      if (documentsResult.data) {
        setDocuments(documentsResult.data);
        logWithContext("BookingDetailsDrawer", "Set documents:", documentsResult.data);
      } else {
        logWithContext("BookingDetailsDrawer", "No documents data received:", documentsResult.error);
      }
      if (statusResult.data) {
        setDocumentStatus(statusResult.data);
      }
    } catch (error) {
      logWithContext("BookingDetailsDrawer", "Error fetching booking documents:", error);
    } finally {
      setLoadingDocuments(false);
    }
  };

  const handleDocumentVerification = async (
    documentId: string,
    action: "approve" | "reject",
    notes?: string
  ) => {
    try {
      const result = await verifyBookingDocument(documentId, action, notes);
      if (result.error) {
        toast({
          variant: "destructive",
          title: "Document verification failed",
          description: result.error.message,
        });
        return;
      }

      // Refresh documents
      await fetchBookingDocuments();
      toast({
        title: `Document ${action === "approve" ? "approved" : "rejected"}`,
        description: notes ? `Notes: ${notes}` : undefined,
      });
    } catch (error) {
      console.error("Error verifying document:", error);
      toast({
        variant: "destructive",
        title: "Failed to verify document",
        description: "Please try again.",
      });
    }
  };

  const handleRequireResubmission = async (documentId: string, notes: string) => {
    try {
      const result = await requireDocumentResubmission(documentId, notes);
      if (result.error) {
        toast({
          variant: "destructive",
          title: "Resubmission request failed",
          description: result.error.message,
        });
        return;
      }

      // Refresh documents
      await fetchBookingDocuments();
      toast({
        title: "Resubmission requested",
        description: `Notes: ${notes}`,
      });
    } catch (error) {
      console.error("Error requesting resubmission:", error);
      toast({
        variant: "destructive",
        title: "Failed to request resubmission",
        description: "Please try again.",
      });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({ title: "Copied to clipboard", description: text });
  };

  const generateGoogleCalendarUrl = () => {
    if (!booking) return "";

    const title = `${booking.userName} – ${booking.carModel}`;
    const startTime =
      booking.from.toISOString().replace(/[-:]/g, "").split(".")[0] + "Z";
    const endTime =
      booking.to.toISOString().replace(/[-:]/g, "").split(".")[0] + "Z";
    const location = `${booking.pickup_location} to ${booking.dropoff_location}`;
    const details = `Car Rental Booking\\n\\nRenter: ${
      booking.userName
    }\\nVehicle: ${booking.carModel}\\nPickup: ${
      booking.pickup_location
    }\\nDrop-off: ${
      booking.dropoff_location
    }\\nTotal: ₱${booking.totalAmount.toFixed(2)}`;

    const params = new URLSearchParams({
      action: "TEMPLATE",
      text: title,
      dates: `${startTime}/${endTime}`,
      location: location,
      details: details,
    });

    return `https://calendar.google.com/calendar/render?${params.toString()}`;
  };

  const generateICSFile = () => {
    if (!booking) return;

    const startTime =
      booking.from.toISOString().replace(/[-:]/g, "").split(".")[0] + "Z";
    const endTime =
      booking.to.toISOString().replace(/[-:]/g, "").split(".")[0] + "Z";
    const now =
      new Date().toISOString().replace(/[-:]/g, "").split(".")[0] + "Z";

    const icsContent = `BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//OllieTrack//Car Rental Booking//EN
BEGIN:VEVENT
UID:booking-${booking.id}@ollietrack.com
DTSTAMP:${now}
DTSTART:${startTime}
DTEND:${endTime}
SUMMARY:${booking.userName} – ${booking.carModel}
DESCRIPTION:Car Rental Booking\\n\\nRenter: ${booking.userName}\\nVehicle: ${
      booking.carModel
    }\\nPickup: ${booking.pickup_location}\\nDrop-off: ${
      booking.dropoff_location
    }\\nTotal: ₱${booking.totalAmount.toFixed(2)}
LOCATION:${booking.pickup_location} to ${booking.dropoff_location}
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR`;

    const blob = new Blob([icsContent], {
      type: "text/calendar;charset=utf-8",
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `booking-${booking.id}.ics`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const formatDateTime = (date: Date) => {
    return {
      friendly: format(date, "EEE, MMM d • h:mm a"),
      iso: date.toISOString(),
      timezone: "Asia/Manila (UTC+8)",
    };
  };

  const getDuration = () => {
    if (!booking) return "";
    const duration = intervalToDuration({
      start: booking.from,
      end: booking.to,
    });
    return formatDuration(duration, { format: ["days", "hours", "minutes"] });
  };

  if (!booking) return null;

  const pickupTime = formatDateTime(booking.from);
  const returnTime = formatDateTime(booking.to);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl w-[90vw] h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileTextIcon className="h-5 w-5" />
            Booking Details • {booking ? formatBookingIdForDisplay(booking) : '#LOADING'}
          </DialogTitle>
          <DialogDescription>
            Complete information for this car rental booking
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="flex-1 pr-6">

        <div className="mt-8 space-y-6">
          {/* Comprehensive Booking Information */}
          <Card className="shadow-lg border-2 border-blue-200 bg-white">
            <CardHeader className="pb-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
              <CardTitle className="text-lg font-bold flex items-center gap-3 text-blue-900">
                <UserIcon className="h-5 w-5" />
                Booking Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 p-6">
              {/* Renter Information */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <UserIcon className="h-4 w-4" />
                  Renter Information
                </h3>
                {/* Name Components */}
                <div className="space-y-2 mb-3">
                  {customer?.first_name || customer?.last_name ? (
                    <div className="grid grid-cols-1 gap-2">
                      {customer.first_name && (
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-gray-500 w-20">First Name:</span>
                          <span className="font-semibold text-gray-900">{customer.first_name}</span>
                        </div>
                      )}
                      {customer.middle_initial && (
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-gray-500 w-20">Middle:</span>
                          <span className="font-semibold text-gray-900">{customer.middle_initial}</span>
                        </div>
                      )}
                      {customer.last_name && (
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-gray-500 w-20">Last Name:</span>
                          <span className="font-semibold text-gray-900">{customer.last_name}</span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="font-bold text-lg text-gray-900">
                      {booking.userName}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  {customer?.phone && (
                    <div className="flex items-center gap-3 text-base text-gray-700 bg-gray-50 p-3 rounded-lg border">
                      <PhoneIcon className="h-4 w-4 text-green-600" />
                      <span className="font-medium">{customer.phone}</span>
                      <Button
                        variant="tertiary"
                        size="xs"
                        onClick={() => copyToClipboard(customer.phone!)}
                        className="ml-auto hover:bg-blue-100"
                      >
                        <CopyIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                  {customer?.email && (
                    <div className="flex items-center gap-3 text-base text-gray-700 bg-gray-50 p-3 rounded-lg border">
                      <MailIcon className="h-4 w-4 text-blue-600" />
                      <span className="font-medium">{customer.email}</span>
                      <Button
                        variant="tertiary"
                        size="xs"
                        onClick={() => copyToClipboard(customer.email)}
                        className="ml-auto hover:bg-blue-100"
                      >
                        <CopyIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  )}

                  {/* Payment ID */}
                  <div className="flex items-center gap-3 text-base text-gray-700 bg-gray-50 p-3 rounded-lg border">
                    <CreditCardIcon className="h-4 w-4 text-purple-600" />
                    <span className="font-medium">
                      {payment ? formatPaymentIdForDisplay(payment) :
                        booking?.payStatus === "Paid" ? "Payment completed" :
                        booking?.payStatus === "Refunded" ? "Payment refunded" :
                        "Payment pending"}
                    </span>
                    {payment && (
                      <Button
                        variant="tertiary"
                        size="xs"
                        onClick={() => copyToClipboard(formatPaymentIdForDisplay(payment))}
                        className="ml-auto hover:bg-blue-100"
                      >
                        <CopyIcon className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              <Separator />

              {/* Vehicle Details */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <CarIcon className="h-4 w-4" />
                  Vehicle Details
                </h3>
                <p className="font-bold text-lg text-gray-900 mb-3">
                  {booking.carModel}
                </p>
                {car && (
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="bg-gray-50 p-3 rounded-lg border">
                      <span className="font-medium text-gray-700">Plate:</span>
                      <p className="font-semibold">{car.plate_number}</p>
                    </div>
                    <div className="bg-gray-50 p-3 rounded-lg border">
                      <span className="font-medium text-gray-700">Transmission:</span>
                      <p className="font-semibold">{car.transmission}</p>
                    </div>
                    <div className="bg-gray-50 p-3 rounded-lg border">
                      <span className="font-medium text-gray-700">Category:</span>
                      <p className="font-semibold">{car.type}</p>
                    </div>
                    <div className="bg-gray-50 p-3 rounded-lg border">
                      <span className="font-medium text-gray-700">Seats:</span>
                      <p className="font-semibold">{car.seats}</p>
                    </div>
                  </div>
                )}
              </div>

              <Separator />

              {/* Booking Period */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <ClockIcon className="h-4 w-4" />
                  Booking Period
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded-lg border">
                    <p className="text-sm font-medium text-gray-700 mb-1">Pickup</p>
                    <p className="font-semibold">{pickupTime.friendly}</p>
                    <p className="text-xs text-gray-600" title={pickupTime.iso}>
                      {pickupTime.timezone}
                    </p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg border">
                    <p className="text-sm font-medium text-gray-700 mb-1">Return</p>
                    <p className="font-semibold">{returnTime.friendly}</p>
                    <p className="text-xs text-gray-600" title={returnTime.iso}>
                      {returnTime.timezone}
                    </p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg border md:col-span-2">
                    <p className="text-sm font-medium text-gray-700 mb-1">Duration</p>
                    <p className="font-semibold">{getDuration()}</p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Locations */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <MapPinIcon className="h-4 w-4" />
                  Locations
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded-lg border">
                    <p className="text-sm font-medium text-gray-700 mb-1">Pickup Location</p>
                    <p className="font-semibold">{booking.pickup_location}</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg border">
                    <p className="text-sm font-medium text-gray-700 mb-1">Drop-off Location</p>
                    <p className="font-semibold">{booking.dropoff_location}</p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Price Breakdown */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <ReceiptIcon className="h-4 w-4" />
                  Price Breakdown
                </h3>
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-700">
                        Base Rate ({booking.days} day{booking.days > 1 ? "s" : ""})
                      </span>
                      <span className="font-medium">₱{(car?.price_per_day || 0) * booking.days}</span>
                    </div>
                    <Separator className="bg-green-200" />
                    <div className="flex justify-between font-bold text-lg">
                      <span className="text-green-900">Total Amount</span>
                      <span className="text-green-700">₱{booking.totalAmount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>



          {/* Document Verification */}
          <Card className="shadow-lg border-2 border-orange-200 bg-white">
            <CardHeader className="pb-4 bg-gradient-to-r from-orange-50 to-amber-50 rounded-t-lg">
              <CardTitle className="text-lg font-bold flex items-center gap-3 text-orange-900">
                <ShieldCheckIcon className="h-5 w-5" />
                Document Verification
                {documentStatus && (
                  <Badge
                    variant={documentStatus.is_complete ? "default" : "secondary"}
                    className={
                      documentStatus.is_complete
                        ? "bg-green-100 text-green-700 border-green-200"
                        : "bg-yellow-100 text-yellow-700 border-yellow-200"
                    }
                  >
                    {documentStatus.uploaded_count}/4 Uploaded
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              {loadingDocuments ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-600"></div>
                  <span className="ml-2 text-gray-600">Loading documents...</span>
                </div>
              ) : documents.length === 0 ? (
                <div className="text-center py-6 text-gray-500">
                  <UploadIcon className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                  <p className="font-medium">No documents uploaded yet</p>
                  <p className="text-sm">Customer needs to upload 4 required documents</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Document Status Summary */}
                  {documentStatus && (
                    <div className="bg-gray-50 p-4 rounded-lg border grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Pending:</span>
                        <span className="ml-1 text-yellow-600">{documentStatus.pending_count}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Approved:</span>
                        <span className="ml-1 text-green-600">{documentStatus.approved_count}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Rejected:</span>
                        <span className="ml-1 text-red-600">{documentStatus.rejected_count}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Complete:</span>
                        <span className="ml-1">{documentStatus.is_complete ? "✅ Yes" : "❌ No"}</span>
                      </div>
                    </div>
                  )}

                  {/* Individual Documents */}
                  {documents.map((doc) => (
                    <DocumentCard
                      key={doc.id}
                      document={doc}
                      onVerify={handleDocumentVerification}
                      onRequireResubmission={handleRequireResubmission}
                      onOpenViewer={openDocumentViewer}
                      documentTypeLabel={documentTypeLabel}
                      onRequestResubmission={(doc) => {
                        setResubmissionDocument(doc);
                        setResubmissionModalOpen(true);
                      }}
                    />
                  ))}
                </div>
              )}
              
              {/* Finalize Booking Button */}
              {booking.status === "Pending" && (
                <div className="pt-4 border-t mt-4">
                  {documentStatus?.is_complete ? (
                    <p className="text-sm text-gray-700 mb-3">
                      All required documents have been verified. You can now finalize this booking.
                    </p>
                  ) : (
                    <div className="mb-3">
                      <p className="text-sm text-gray-700 mb-2">
                        Some documents are still pending verification.
                      </p>
                      <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
                        <AlertCircleIcon className="h-4 w-4 inline mr-1" />
                        Admin Override: You can finalize this booking at your discretion even with pending documents.
                      </p>
                    </div>
                  )}
                  <Button
                    size="sm"
                    onClick={() => { setDecisionAction("confirm"); setDecisionDialogOpen(true); }}
                    className={cn(
                      "flex items-center gap-2",
                      documentStatus?.is_complete 
                        ? "bg-green-600 hover:bg-green-700" 
                        : "bg-amber-600 hover:bg-amber-700"
                    )}
                  >
                    <CheckCircleIcon className="h-4 w-4" />
                    Finalize Booking
                    {!documentStatus?.is_complete && (
                      <span className="text-xs">(Admin Override)</span>
                    )}
                  </Button>
                </div>
              )}
              
              {/* Booking Status */}
              {booking.status !== "Pending" && (
                <div className="pt-4 border-t mt-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">Booking Status</p>
                  <div className="text-sm text-gray-700">
                    {booking.status === "Active" && (
                      <div className="flex items-center gap-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-600" />
                        Booking has been finalized and is active.
                      </div>
                    )}
                    {booking.status === "Cancelled" && (
                      <div className="flex items-center gap-2">
                        <AlertCircleIcon className="h-4 w-4 text-red-600" />
                        Booking has been cancelled.
                      </div>
                    )}
                    {booking.status === "Completed" && (
                      <div className="flex items-center gap-2">
                        <ShieldCheckIcon className="h-4 w-4 text-indigo-600" />
                        Booking is already completed.
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>


          {/* Audit Trail */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Audit Trail</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm text-muted-foreground">
              <div className="flex justify-between">
                <span>Created</span>
                <span>
                  {format(new Date(booking.created_at), "MMM d, yyyy • h:mm a")}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Last Updated</span>
                <span>
                  {format(new Date(booking.updated_at), "MMM d, yyyy • h:mm a")}
                </span>
              </div>
            </CardContent>
          </Card>

        </div>
        </ScrollArea>
        
        {/* Dialog Footer with Calendar Actions */}
        <div className="mt-6 border-t pt-4 px-6 flex flex-wrap justify-end gap-3">
          <Button variant="secondary" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          
          {useOnPageCalendar ? (
            <Button 
              onClick={onAddToOnPageCalendar} 
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
            >
              <CalendarIcon className="h-4 w-4" />
              Add to On-Page Calendar
            </Button>
          ) : (
            <Button 
              onClick={onAddToCalendar} 
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
            >
              <CalendarIcon className="h-4 w-4" />
              Add to Calendar
            </Button>
          )}
        </div>
      </DialogContent>
      {/* STREAMLINED MODAL VIEWER: Now used ONLY for viewing content, not for verification actions.
           Removed redundant verification buttons that duplicated card actions.
           This improves UX by eliminating the need for users to click twice to perform actions. */}
      <Dialog open={viewerOpen} onOpenChange={(o) => { if (!o) { setViewerOpen(false); setViewerItem(null); setViewerNotes(""); } }}>
        <DialogContent className="max-w-4xl w-[90vw] h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>{viewerItem?.title || "Viewer"}</DialogTitle>
            <DialogDescription>
              Review the content below. Use the action buttons on the document/payment cards for verification.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {viewerItem && (
              <div className="border rounded-md overflow-hidden">
                {(() => {
                  const url = viewerItem.url;
                  const mime = (viewerItem as any).mime as string | undefined;
                  const isImage = (mime?.startsWith("image/") ?? /\.(png|jpg|jpeg|gif|webp|bmp|tiff)$/i.test(url));
                  const isPdf = (mime === "application/pdf") || /\.pdf$/i.test(url);
                  
                  logWithContext("BookingDetailsDrawer", "Viewer item:", { url, mime, isImage, isPdf, title: viewerItem.title });
                  
                  if (isImage) {
                    return (
                      <img 
                        src={url} 
                        alt={viewerItem.title} 
                        className="w-full max-h-[70vh] object-contain bg-black/5" 
                        onError={(e) => {
                          logWithContext("BookingDetailsDrawer", "Image failed to load:", url);
                          (e.target as HTMLImageElement).style.display = 'none';
                          const parent = (e.target as HTMLImageElement).parentElement;
                          if (parent) {
                            parent.innerHTML = `
                              <div class="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded">
                                <p class="font-medium">Image failed to load</p>
                                <p class="text-xs mt-1">URL: ${url}</p>
                                <div class="mt-2">
                                  <a href="${url}" target="_blank" rel="noreferrer" class="text-blue-600 underline">Open in new tab</a>
                                </div>
                              </div>
                            `;
                          }
                        }}
                        onLoad={() => logWithContext("BookingDetailsDrawer", "Image loaded successfully:", url)}
                      />
                    );
                  }
                  if (isPdf) {
                    return <iframe src={url} className="w-full h-[70vh]" onError={() => logWithContext("BookingDetailsDrawer", "PDF failed to load:", url)} />;
                  }
                  return (
                    <div className="p-4 text-sm">
                      Unsupported preview. You can open the file in a new tab:
                      <div className="mt-2">
                        <a href={url} target="_blank" rel="noreferrer" className="text-blue-600 underline">Open in new tab</a>
                      </div>
                      <div className="mt-2 text-xs text-gray-500">
                        File type: {mime || "Unknown"} • URL: {url}
                      </div>
                    </div>
                  );
                })()}
              </div>
            )}

            {/* STREAMLINED: Removed notes textarea as verification actions 
                are now handled directly with prompts for better workflow */}
          </div>
          {/* Removed duplicate close button - Dialog header already has built-in close (X) button */}
        </DialogContent>
      </Dialog>
      
      
      {/* Request Resubmission Modal - Only for resubmission actions */}
      {resubmissionDocument && (
        <RejectionReasonModal
          open={resubmissionModalOpen}
          onOpenChange={setResubmissionModalOpen}
          title="Request Document Resubmission"
          description={`Please explain what needs to be corrected in this ${documentTypeLabel(resubmissionDocument.document_type).toLowerCase()} so the customer knows exactly what to fix.`}
          onConfirm={(reason) => {
            handleRequireResubmission(resubmissionDocument.id, reason);
            setResubmissionDocument(null);
          }}
          onCancel={() => {
            setResubmissionDocument(null);
          }}
          confirmButtonText="Request Resubmission"
          confirmButtonIcon={<AlertCircleIcon className="h-4 w-4" />}
          placeholder="Explain what needs to be corrected (e.g., 'Document is blurry', 'Expiration date not visible', 'Please upload front and back')..."
        />
      )}
      
      {/* Booking Decision Notes Dialog */}
      <Dialog open={decisionDialogOpen} onOpenChange={(o) => { setDecisionDialogOpen(o); if (!o) { setDecisionAction(null); setDecisionNotes(""); } }}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Finalize Booking</DialogTitle>
            <DialogDescription>
              Optionally add confirmation notes for this booking.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-3">
            <Textarea
              placeholder="Confirmation notes (optional)..."
              value={decisionNotes}
              onChange={(e) => setDecisionNotes(e.target.value)}
              rows={3}
            />
          </div>
          <DialogFooter>
            <Button
              onClick={async () => {
                await handleBookingDecision(decisionNotes || undefined);
                setDecisionDialogOpen(false); setDecisionAction(null); setDecisionNotes("");
              }}
              className="bg-green-600 hover:bg-green-700"
            >
              Finalize
            </Button>
            <DialogClose asChild>
              <Button variant="secondary">Cancel</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}

// Wrapper component for renter issue tracking with data loading
function RenterIssueTrackingSection({
  customerId,
  customerName,
  currentAdminId,
}: {
  customerId: string;
  customerName: string;
  currentAdminId: string;
}) {
  const {
    status,
    categoryTags,
    issues,
    behaviorSummary,
    availableCategories,
    loading,
    error,
    updateStatus,
    addCategoryTag,
    removeCategoryTag,
    addIssue,
    updateIssue,
    removeIssue,
  } = useRenterIssueData(customerId, currentAdminId);

  if (loading) {
    return (
      <Card className="border-2 border-gray-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading renter data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-2 border-red-200 bg-red-50">
        <CardContent className="p-6">
          <div className="text-red-700 text-sm">
            Error loading renter data: {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <RenterIssueTracking
      customerId={customerId}
      customerName={customerName}
      status={status}
      categoryTags={categoryTags}
      issues={issues}
      behaviorSummary={behaviorSummary || undefined}
      availableCategories={availableCategories}
      currentAdminId={currentAdminId}
      onStatusUpdate={updateStatus}
      onAddCategoryTag={addCategoryTag}
      onRemoveCategoryTag={removeCategoryTag}
      onAddIssue={addIssue}
      onUpdateIssue={updateIssue}
      onDeleteIssue={removeIssue}
    />
  );
}

// Document Card Component
function DocumentCard({
  document,
  onVerify,
  onRequireResubmission,
  onOpenViewer,
  documentTypeLabel,
  onRequestResubmission,
}: {
  document: BookingDocument;
  onVerify: (documentId: string, action: "approve" | "reject", notes?: string) => void;
  onRequireResubmission: (documentId: string, notes: string) => void;
  onOpenViewer: (doc: BookingDocument) => void;
  documentTypeLabel: (type: string) => string;
  onRequestResubmission: (doc: BookingDocument) => void;
}) {
  // documentTypeLabel function is now passed as prop from parent component

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-700 border-green-200";
      case "rejected":
        return "bg-red-100 text-red-700 border-red-200";
      case "requires_resubmission":
        return "bg-orange-100 text-orange-700 border-orange-200";
      case "pending":
      default:
        return "bg-yellow-100 text-yellow-700 border-yellow-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircleIcon className="h-4 w-4" />;
      case "rejected":
        return <XCircleIcon className="h-4 w-4" />;
      case "requires_resubmission":
        return <AlertCircleIcon className="h-4 w-4" />;
      case "pending":
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div
      className={cn(
        "border-2 rounded-lg p-4",
        document.verification_status === "approved"
          ? "border-green-200 bg-green-50"
          : document.verification_status === "rejected"
          ? "border-red-200 bg-red-50"
          : document.verification_status === "requires_resubmission"
          ? "border-orange-200 bg-orange-50"
          : "border-yellow-200 bg-yellow-50"
      )}
    >
      <div className="flex items-start justify-between mb-3">
        <div>
          <h4 className="font-medium text-gray-900">
            {documentTypeLabel(document.document_type)}
          </h4>
          <p className="text-sm text-gray-600">{document.file_name}</p>
          <p className="text-xs text-gray-500">
            {formatFileSize(document.file_size)} • {document.file_type}
          </p>
        </div>
        <Badge
          variant="secondary"
          className={getStatusColor(document.verification_status)}
        >
          {getStatusIcon(document.verification_status)}
          <span className="ml-1 capitalize">
            {document.verification_status.replace("_", " ")}
          </span>
        </Badge>
      </div>

      <div className="flex items-center gap-2 mb-3">
        <Button
          variant="secondary"
          size="sm"
          onClick={() => onOpenViewer(document)}
          className="flex items-center gap-2"
        >
          <EyeIcon className="h-4 w-4" />
          View Document
        </Button>
        <Button
          variant="secondary"
          size="sm"
          onClick={() => navigator.clipboard.writeText(document.file_url)}
          className="flex items-center gap-2"
        >
          <CopyIcon className="h-4 w-4" />
          Copy URL
        </Button>
      </div>

      {document.verification_status === "pending" && (
        <div className="border-t pt-3">
          <p className="text-sm font-medium text-gray-700 mb-2">
            Verification Actions
          </p>
          <div className="flex gap-2 flex-wrap">
            {/* STREAMLINED: Direct document actions without modal preview */}
            <Button
              size="sm"
              onClick={() => onVerify(document.id, "approve")}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
            >
              <CheckCircleIcon className="h-4 w-4" />
              Approve
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={() => onVerify(document.id, "reject", "Document was rejected by admin.")}
              className="flex items-center gap-2"
            >
              <XCircleIcon className="h-4 w-4" />
              Reject
            </Button>
            <Button
              size="sm"
              variant="secondary"
              onClick={() => onRequestResubmission(document)}
              className="flex items-center gap-2"
            >
              <AlertCircleIcon className="h-4 w-4" />
              Request Resubmission
            </Button>
          </div>
        </div>
      )}

      {document.verification_notes && (
        <div className="border-t pt-3 mt-3">
          <p className="text-sm font-medium text-gray-700 mb-1">
            Verification Notes
          </p>
          <p className="text-sm bg-white p-2 rounded border">
            {document.verification_notes}
          </p>
        </div>
      )}

      {document.verified_at && (
        <div className="text-xs text-gray-500 mt-2">
          Verified on {format(new Date(document.verified_at), "MMM d, yyyy • h:mm a")}
        </div>
      )}
    </div>
  );
}
