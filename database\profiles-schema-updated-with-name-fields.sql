-- Updated profiles table schema with separate name fields
-- Maintains backward compatibility with existing full_name field

create table public.profiles (
  id uuid not null,
  email text not null,
  full_name text null,
  first_name text null,
  middle_initial text null,
  last_name text null,
  phone text null,
  role text not null default 'customer'::text,
  avatar_url text null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint profiles_pkey primary key (id),
  constraint profiles_id_fkey foreign KEY (id) references auth.users (id) on delete CASCADE,
  constraint profiles_role_check check (
    (
      role = any (
        array[
          'customer'::text,
          'admin'::text,
          'super_admin'::text
        ]
      )
    )
  ),
  -- Constraint to ensure middle_initial is single character if provided
  constraint profiles_middle_initial_check check (
    middle_initial is null or length(middle_initial) <= 1
  )
) TABLESPACE pg_default;

-- Existing indexes
create index IF not exists idx_profiles_role on public.profiles using btree (role) TABLESPACE pg_default;
create index IF not exists idx_profiles_email on public.profiles using btree (email) TABLESPACE pg_default;

-- New indexes for name fields
create index IF not exists idx_profiles_first_name on public.profiles using btree (first_name) TABLESPACE pg_default;
create index IF not exists idx_profiles_last_name on public.profiles using btree (last_name) TABLESPACE pg_default;

-- Existing trigger
create trigger handle_updated_at_profiles BEFORE
update on profiles for EACH row
execute FUNCTION handle_updated_at ();

-- Function to automatically sync full_name when individual name fields are updated
create or replace function sync_full_name_from_parts()
returns trigger as $$
begin
  -- Only update full_name if individual name fields are provided
  if NEW.first_name is not null or NEW.middle_initial is not null or NEW.last_name is not null then
    NEW.full_name := trim(
      concat_ws(' ', 
        nullif(trim(NEW.first_name), ''),
        nullif(trim(NEW.middle_initial), ''),
        nullif(trim(NEW.last_name), '')
      )
    );
    -- Set to null if result is empty string
    if NEW.full_name = '' then
      NEW.full_name := null;
    end if;
  end if;
  
  return NEW;
end;
$$ language plpgsql;

-- Trigger to automatically sync full_name when name parts are updated
create trigger sync_full_name_trigger
  before insert or update on public.profiles
  for each row
  execute function sync_full_name_from_parts();

-- Function to parse full_name into separate fields (for data migration)
create or replace function parse_full_name_to_parts()
returns trigger as $$
begin
  -- Only parse if individual fields are null but full_name exists
  if NEW.full_name is not null and 
     NEW.first_name is null and 
     NEW.middle_initial is null and 
     NEW.last_name is null then
    
    declare
      name_parts text[];
      part_count integer;
    begin
      -- Split full name by spaces
      name_parts := string_to_array(trim(NEW.full_name), ' ');
      part_count := array_length(name_parts, 1);
      
      if part_count = 1 then
        NEW.first_name := name_parts[1];
      elsif part_count = 2 then
        NEW.first_name := name_parts[1];
        NEW.last_name := name_parts[2];
      elsif part_count >= 3 then
        NEW.first_name := name_parts[1];
        -- Check if second part looks like middle initial (single char or ends with .)
        if length(name_parts[2]) = 1 or name_parts[2] like '%.' then
          NEW.middle_initial := replace(name_parts[2], '.', '');
          NEW.last_name := array_to_string(name_parts[3:], ' ');
        else
          -- Treat as compound last name
          NEW.last_name := array_to_string(name_parts[2:], ' ');
        end if;
      end if;
    end;
  end if;
  
  return NEW;
end;
$$ language plpgsql;

-- Trigger to parse full_name into parts (runs before the sync trigger)
create trigger parse_full_name_trigger
  before insert or update on public.profiles
  for each row
  execute function parse_full_name_to_parts();
