"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { 
  FileText, 
  CheckCircle, 
  XCircle, 
  <PERSON>ert<PERSON>riangle, 
  Upload, 
  Mail,
  Clock,
  User
} from "lucide-react";
import { verifyBookingDocument, requireDocumentResubmission } from "@/app/admin/bookings/actions/document-actions";
import { createDocumentRequests, checkMissingDocumentsForBooking } from "@/lib/services/document-request-service";

interface BookingDocument {
  id: string;
  booking_id: string;
  document_type: string;
  file_url: string;
  file_name: string;
  file_size: number;
  file_type: string;
  verification_status: "pending" | "approved" | "rejected" | "requires_resubmission";
  verification_notes?: string;
  verified_by?: string;
  verified_at?: string;
  created_at: string;
  updated_at: string;
}

interface MissingDocumentCheck {
  document_type: string;
  is_missing: boolean;
  current_status: string;
}

interface DocumentCardProps {
  bookingId: string;
  customerId: string;
  customerEmail: string;
  customerName?: string;
  documents: BookingDocument[];
  onDocumentUpdate?: () => void;
}

const DOCUMENT_DISPLAY_NAMES: { [key: string]: string } = {
  'drivers_license': "Driver's License",
  'government_id': "Government ID",
  'proof_of_billing': "Proof of Billing"
};

const REQUIRED_DOCUMENT_TYPES = ['drivers_license', 'government_id', 'proof_of_billing'];

export function DocumentCard({ 
  bookingId, 
  customerId, 
  customerEmail, 
  customerName,
  documents, 
  onDocumentUpdate 
}: DocumentCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [verificationNotes, setVerificationNotes] = useState("");
  const [requestNotes, setRequestNotes] = useState("");
  const [selectedMissingDocs, setSelectedMissingDocs] = useState<string[]>([]);
  const [missingDocs, setMissingDocs] = useState<MissingDocumentCheck[]>([]);
  const [showRequestModal, setShowRequestModal] = useState(false);
  const { toast } = useToast();

  const getDocumentStatus = (docType: string) => {
    const doc = documents.find(d => d.document_type === docType);
    if (!doc) return { status: 'missing', doc: null };
    return { status: doc.verification_status, doc };
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800 border-green-200">
          <CheckCircle className="w-3 h-3 mr-1" />
          Approved
        </Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
          <Clock className="w-3 h-3 mr-1" />
          Pending
        </Badge>;
      case 'rejected':
      case 'requires_resubmission':
        return <Badge className="bg-red-100 text-red-800 border-red-200">
          <XCircle className="w-3 h-3 mr-1" />
          Needs Revision
        </Badge>;
      case 'missing':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">
          <AlertTriangle className="w-3 h-3 mr-1" />
          Missing
        </Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const handleVerifyDocument = async (documentId: string, action: "approve" | "reject") => {
    setIsLoading(true);
    try {
      const result = await verifyBookingDocument(documentId, action, verificationNotes);
      
      if (result.error) {
        toast({
          title: "Error",
          description: result.error.message || "Failed to verify document",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: `Document ${action}d successfully`,
        });
        setVerificationNotes("");
        onDocumentUpdate?.();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRequestResubmission = async (documentId: string) => {
    if (!requestNotes.trim()) {
      toast({
        title: "Error",
        description: "Please provide notes for resubmission request",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const result = await requireDocumentResubmission(documentId, requestNotes);
      
      if (result.error) {
        toast({
          title: "Error",
          description: result.error.message || "Failed to request resubmission",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: "Resubmission requested successfully",
        });
        setRequestNotes("");
        onDocumentUpdate?.();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkMissingDocuments = async () => {
    setIsLoading(true);
    try {
      const result = await checkMissingDocumentsForBooking(bookingId);
      
      if (result.success && result.data) {
        const missing = result.data.filter(doc => doc.is_missing);
        setMissingDocs(missing);
        setSelectedMissingDocs(missing.map(doc => doc.document_type));
        setShowRequestModal(true);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to check missing documents",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendDocumentRequest = async () => {
    if (selectedMissingDocs.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one document type",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const requestType = missingDocs.some(doc => 
        selectedMissingDocs.includes(doc.document_type) && doc.current_status === 'not_uploaded'
      ) ? 'missing' : 'revision';

      const result = await createDocumentRequests({
        bookingId,
        customerId,
        customerEmail,
        customerName,
        documentTypes: selectedMissingDocs,
        requestType,
        adminNotes: requestNotes
      });

      if (result.success) {
        toast({
          title: "Success",
          description: `Document request sent successfully. Email notification sent to ${customerEmail}`,
        });
        setShowRequestModal(false);
        setRequestNotes("");
        setSelectedMissingDocs([]);
        onDocumentUpdate?.();
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to send document request",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const allDocumentsComplete = REQUIRED_DOCUMENT_TYPES.every(docType => {
    const { status } = getDocumentStatus(docType);
    return status === 'approved';
  });

  const hasProblematicDocuments = REQUIRED_DOCUMENT_TYPES.some(docType => {
    const { status } = getDocumentStatus(docType);
    return status === 'missing' || status === 'rejected' || status === 'requires_resubmission';
  });

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Legal Documents Verification
          </CardTitle>
          <div className="flex gap-2">
            {allDocumentsComplete && (
              <Badge className="bg-green-100 text-green-800 border-green-200">
                <CheckCircle className="w-3 h-3 mr-1" />
                Complete
              </Badge>
            )}
            {hasProblematicDocuments && (
              <Button
                onClick={checkMissingDocuments}
                disabled={isLoading}
                size="sm"
                className="flex items-center gap-1"
              >
                <Mail className="w-4 h-4" />
                Request Documents
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {REQUIRED_DOCUMENT_TYPES.map((docType) => {
          const { status, doc } = getDocumentStatus(docType);
          
          return (
            <div key={docType} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium">{DOCUMENT_DISPLAY_NAMES[docType]}</h4>
                  {getStatusBadge(status)}
                </div>
                {doc && (
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(doc.file_url, '_blank')}
                    >
                      View
                    </Button>
                  </div>
                )}
              </div>

              {doc && (
                <div className="text-sm text-gray-600 space-y-1">
                  <p><strong>File:</strong> {doc.file_name}</p>
                  <p><strong>Size:</strong> {(doc.file_size / 1024).toFixed(1)} KB</p>
                  <p><strong>Uploaded:</strong> {new Date(doc.created_at).toLocaleDateString()}</p>
                  {doc.verification_notes && (
                    <p><strong>Notes:</strong> {doc.verification_notes}</p>
                  )}
                </div>
              )}

              {doc && status === 'pending' && (
                <div className="flex gap-2 pt-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button size="sm" className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Approve
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Approve Document</DialogTitle>
                        <DialogDescription>
                          Approve {DOCUMENT_DISPLAY_NAMES[docType]} for this booking.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-2">
                        <Label htmlFor="approval-notes">Notes (Optional)</Label>
                        <Textarea
                          id="approval-notes"
                          placeholder="Add any notes about this approval..."
                          value={verificationNotes}
                          onChange={(e) => setVerificationNotes(e.target.value)}
                        />
                      </div>
                      <DialogFooter>
                        <Button
                          onClick={() => handleVerifyDocument(doc.id, "approve")}
                          disabled={isLoading}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          {isLoading ? "Approving..." : "Approve Document"}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>

                  <Dialog>
                    <DialogTrigger asChild>
                      <Button size="sm" variant="destructive">
                        <XCircle className="w-4 h-4 mr-1" />
                        Reject
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Reject Document</DialogTitle>
                        <DialogDescription>
                          Reject {DOCUMENT_DISPLAY_NAMES[docType]} and request resubmission.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-2">
                        <Label htmlFor="rejection-notes">Reason for Rejection *</Label>
                        <Textarea
                          id="rejection-notes"
                          placeholder="Explain why this document is being rejected..."
                          value={verificationNotes}
                          onChange={(e) => setVerificationNotes(e.target.value)}
                          required
                        />
                      </div>
                      <DialogFooter>
                        <Button
                          onClick={() => handleVerifyDocument(doc.id, "reject")}
                          disabled={isLoading || !verificationNotes.trim()}
                          variant="destructive"
                        >
                          {isLoading ? "Rejecting..." : "Reject Document"}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              )}

              {doc && (status === 'approved' || status === 'rejected' || status === 'requires_resubmission') && (
                <div className="pt-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button size="sm" variant="outline">
                        <Upload className="w-4 h-4 mr-1" />
                        Request New Upload
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Request Document Resubmission</DialogTitle>
                        <DialogDescription>
                          Request customer to resubmit {DOCUMENT_DISPLAY_NAMES[docType]}.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-2">
                        <Label htmlFor="resubmission-notes">Reason for Resubmission *</Label>
                        <Textarea
                          id="resubmission-notes"
                          placeholder="Explain why the customer needs to resubmit this document..."
                          value={requestNotes}
                          onChange={(e) => setRequestNotes(e.target.value)}
                          required
                        />
                      </div>
                      <DialogFooter>
                        <Button
                          onClick={() => handleRequestResubmission(doc.id)}
                          disabled={isLoading || !requestNotes.trim()}
                        >
                          {isLoading ? "Requesting..." : "Request Resubmission"}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              )}

              {status === 'missing' && (
                <div className="bg-gray-50 p-3 rounded border-l-4 border-l-orange-500">
                  <p className="text-sm text-gray-700">
                    <AlertTriangle className="w-4 h-4 inline mr-1" />
                    This document has not been uploaded yet.
                  </p>
                </div>
              )}
            </div>
          );
        })}

        {/* Document Request Modal */}
        <Dialog open={showRequestModal} onOpenChange={setShowRequestModal}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Request Missing Documents</DialogTitle>
              <DialogDescription>
                Send an email request to {customerName || customerEmail} for missing or problematic documents.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Documents to Request:</Label>
                <div className="space-y-2 mt-2">
                  {missingDocs.map((doc) => (
                    <div key={doc.document_type} className="flex items-center space-x-2">
                      <Checkbox
                        id={doc.document_type}
                        checked={selectedMissingDocs.includes(doc.document_type)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedMissingDocs([...selectedMissingDocs, doc.document_type]);
                          } else {
                            setSelectedMissingDocs(selectedMissingDocs.filter(d => d !== doc.document_type));
                          }
                        }}
                      />
                      <Label htmlFor={doc.document_type} className="text-sm">
                        {DOCUMENT_DISPLAY_NAMES[doc.document_type]} 
                        <span className="text-gray-500 ml-1">
                          ({doc.current_status === 'not_uploaded' ? 'Missing' : 'Needs Revision'})
                        </span>
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <Label htmlFor="admin-notes">Additional Notes (Optional)</Label>
                <Textarea
                  id="admin-notes"
                  placeholder="Add any specific instructions for the customer..."
                  value={requestNotes}
                  onChange={(e) => setRequestNotes(e.target.value)}
                  className="mt-1"
                />
              </div>

              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-start gap-2">
                  <Mail className="w-4 h-4 text-blue-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-blue-900">Email will be sent to:</p>
                    <p className="text-blue-700">{customerEmail}</p>
                    <p className="text-blue-600 text-xs mt-1">
                      The customer will receive detailed instructions and a direct link to upload documents.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowRequestModal(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleSendDocumentRequest} 
                disabled={isLoading || selectedMissingDocs.length === 0}
              >
                {isLoading ? "Sending..." : "Send Request"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
