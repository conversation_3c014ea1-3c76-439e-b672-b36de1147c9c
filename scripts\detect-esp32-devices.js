#!/usr/bin/env node
/**
 * ESP32 Device Detection Script
 * Identifies all ESP32 devices publishing to MQTT
 */
import mqtt from 'mqtt';

const BROKER_URL = 'mqtt://broker.emqx.io:1883';
const GPS_TOPICS = ['pathlink/gps/+'];

console.log('🔍 ESP32 Device Detection Started...');
console.log(`🔗 Broker: ${BROKER_URL}`);
console.log('🎯 Scanning for all ESP32 devices publishing GPS data\n');

const client = mqtt.connect(BROKER_URL, {
  clientId: `device_detector_${Math.random().toString(16).substr(2, 8)}`,
  clean: true,
  keepalive: 60,
  connectTimeout: 30000
});

const devices = new Map();

client.on('connect', () => {
  console.log('✅ Connected to MQTT broker');
  console.log('👂 Listening for ESP32 devices...\n');
  
  GPS_TOPICS.forEach(topic => {
    client.subscribe(topic, { qos: 1 }, (error) => {
      if (error) {
        console.error(`❌ Failed to subscribe to ${topic}:`, error);
      } else {
        console.log(`✅ Subscribed to ${topic}`);
      }
    });
  });
});

client.on('message', (topic, payload) => {
  try {
    const data = JSON.parse(payload.toString());
    const deviceId = data.deviceId || 'UNKNOWN';
    const timestamp = new Date().toISOString();
    
    // Track device information
    if (!devices.has(deviceId)) {
      devices.set(deviceId, {
        deviceId,
        firstSeen: timestamp,
        messageCount: 0,
        lastCoordinates: null,
        lastAccuracy: null,
        isRealESP32: false
      });
    }
    
    const device = devices.get(deviceId);
    device.messageCount++;
    device.lastSeen = timestamp;
    device.lastCoordinates = `${data.lat}, ${data.lng}`;
    device.lastAccuracy = data.accuracy;
    device.isRealESP32 = (
      data.accuracy !== undefined &&
      data.satellites !== undefined &&
      data.fixMode !== undefined
    );
    
    // Real-time device summary
    console.log(`\n📱 DEVICE DETECTED: ${deviceId}`);
    console.log(`   Type: ${device.isRealESP32 ? '🟢 REAL ESP32' : '🔴 PHANTOM/TEST'}`);
    console.log(`   Location: ${device.lastCoordinates}`);
    console.log(`   Accuracy: ${device.lastAccuracy}m`);
    console.log(`   Messages: ${device.messageCount}`);
    console.log(`   Time: ${timestamp}`);
    
    if (device.isRealESP32) {
      console.log(`   🚨 THIS IS YOUR ACTIVE ESP32 DEVICE!`);
      console.log(`   📍 GPS: ${data.lat}, ${data.lng} (±${data.accuracy}m)`);
      console.log(`   🛰️ Satellites: ${data.satellites} | Fix: ${data.fixMode}D`);
      console.log(`   🏃 Speed: ${data.speed}km/h | Heading: ${data.heading}°`);
    }
    
  } catch (error) {
    console.error('❌ Error parsing message:', error);
  }
});

// Summary after 30 seconds
setTimeout(() => {
  console.log(`\n\n📊 === DEVICE DETECTION SUMMARY ===`);
  
  if (devices.size === 0) {
    console.log(`✅ No ESP32 devices detected - system is clean`);
  } else {
    console.log(`🚨 Found ${devices.size} active device(s):`);
    
    devices.forEach((device, deviceId) => {
      console.log(`\n📱 Device: ${deviceId}`);
      console.log(`   Type: ${device.isRealESP32 ? '🟢 REAL ESP32' : '🔴 PHANTOM/TEST'}`);
      console.log(`   Messages: ${device.messageCount}`);
      console.log(`   First seen: ${device.firstSeen}`);
      console.log(`   Last seen: ${device.lastSeen}`);
      console.log(`   Last location: ${device.lastCoordinates}`);
      
      if (device.isRealESP32) {
        console.log(`\n   🎯 ACTION REQUIRED FOR ${deviceId}:`);
        console.log(`   1. 🔌 Physically disconnect this ESP32 device`);
        console.log(`   2. 💡 Check for power LEDs and turn them off`);
        console.log(`   3. 🔄 Remove USB cable or external power`);
        console.log(`   4. 📱 Close Arduino IDE if still connected`);
      }
    });
  }
  
  console.log(`\n=====================================`);
  client.end();
  process.exit(0);
}, 30000);

client.on('error', (error) => {
  console.error('❌ MQTT Connection Error:', error);
});

// Manual exit
process.on('SIGINT', () => {
  console.log('\n🛑 Device detection stopped');
  
  if (devices.size > 0) {
    console.log(`\n📊 Detected ${devices.size} device(s) before stopping:`);
    devices.forEach((device, deviceId) => {
      console.log(`   - ${deviceId}: ${device.messageCount} messages (${device.isRealESP32 ? 'REAL ESP32' : 'PHANTOM'})`);
    });
  }
  
  client.end();
  process.exit(0);
});
