# Booking Document Requirements

## Overview
This document outlines the required documents for car rental bookings in the PathLink system. As of August 2025, there are **four (4) required document types** that must be uploaded, verified, and approved before a booking can be finalized.

## Required Document Types

| Document Type       | Description                        | Format            | Verification Requirements |
|--------------------|------------------------------------|-------------------|--------------------|
| `drivers_license`   | Valid driver's license             | JPG, PNG, PDF     | Must be valid and not expired |
| `government_id`     | Valid government-issued ID         | JPG, PNG, PDF     | Must match the name on driver's license |
| `proof_of_billing`  | Recent utility bill or statement   | JPG, PNG, PDF     | Must be dated within the last 3 months |
| `proof_of_payment`  | Confirmation of payment made       | JPG, PNG, PDF     | Must show transaction details and amount |

## Document Status Flow

Documents go through the following status workflow:

1. **Pending**: Initial state when document is uploaded
2. **Approved**: Document has been verified and approved by admin
3. **Rejected**: Document has been rejected and requires a new upload
4. **Requires Resubmission**: Document needs to be replaced with a better version

## Admin Verification Process

1. Admin reviews each uploaded document via the admin booking details drawer
2. Documents can be approved, rejected, or resubmission can be requested
3. Verification notes can be added for rejected documents or resubmission requests
4. All four required documents must be approved before a booking can be finalized

## Technical Implementation

### Database Schema

The `booking_documents` table has the following constraints:

```sql
ALTER TABLE public.booking_documents
  ADD CONSTRAINT booking_documents_document_type_check
  CHECK (
    document_type IN (
      'drivers_license',
      'government_id',
      'proof_of_billing',
      'proof_of_payment'
    )
  );
```

### Helper Functions

Two key functions handle document verification status:

1. `check_booking_documents_complete(booking_uuid)`: Returns boolean indicating if all required documents are uploaded
2. `get_booking_document_status(booking_uuid)`: Returns detailed status counts for documents

### UI Components

- **Customer Side**: Document upload forms in the booking flow
- **Admin Side**: Document verification interface in the booking details drawer

## Change History

- **August 2025**: Updated from 5 to 4 required document types
  - Removed: `proof_of_age`, `security_deposit_confirmation`
  - Added: `proof_of_billing`
  - Kept: `drivers_license`, `government_id`, `proof_of_payment`
