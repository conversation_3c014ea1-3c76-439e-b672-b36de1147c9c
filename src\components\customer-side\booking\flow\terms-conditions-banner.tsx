"use client";

import * as React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { AlertTriangle, Shield, Eye, FileText } from "lucide-react";
import Link from "next/link";

interface TermsConditionsBannerProps {
  className?: string;
}

export function TermsConditionsBanner({
  className,
}: TermsConditionsBannerProps) {
  return (
    <Card className={`border-blue-200 bg-blue-50 overflow-hidden ${className}`}>
      <CardContent className="p-3 sm:p-4">
        <div className="flex items-start gap-2 sm:gap-3">
          <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-lg bg-blue-100 flex items-center justify-center flex-shrink-0">
            <FileText className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600" />
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="text-sm sm:text-base font-semibold text-blue-900 mb-2">
              Terms & Conditions Notice
            </h4>

            <div className="space-y-2 text-xs sm:text-sm text-blue-800">
              <div className="flex items-start gap-2">
                <Shield className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600 flex-shrink-0 mt-0.5" />
                <p className="break-words leading-tight">
                  <strong>GPS Tracking:</strong> All vehicles are GPS-tracked
                  during rental for security and safety purposes.
                </p>
              </div>

              <div className="flex items-start gap-2">
                <AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4 text-amber-600 flex-shrink-0 mt-0.5" />
                <p className="break-words leading-tight">
                  By proceeding with this booking, you acknowledge that you
                  understand and will comply with our rental terms.
                </p>
              </div>

              <div className="flex items-start gap-2">
                <Eye className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600 flex-shrink-0 mt-0.5" />
                <p className="break-words leading-tight">
                  Please review our complete{" "}
                  <Link
                    href="/customer/terms"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline hover:text-blue-700 font-medium"
                  >
                    Terms & Conditions
                  </Link>{" "}
                  and{" "}
                  <Link
                    href="/customer/terms#privacy-policy"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline hover:text-blue-700 font-medium"
                  >
                    Privacy Policy
                  </Link>{" "}
                  before completing your booking.
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
