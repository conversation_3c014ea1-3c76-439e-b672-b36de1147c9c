"use client"

import React from 'react'
import { cn } from "@/lib/utils"

// Animated car illustration for vehicle-related loading
export function CarLoadingIllustration({ className }: { className?: string }) {
  return (
    <div className={cn("relative w-32 h-20", className)}>
      <svg
        viewBox="0 0 200 120"
        className="w-full h-full"
        aria-hidden="true"
      >
        {/* Road */}
        <rect x="0" y="90" width="200" height="30" fill="#6b7280" />
        <rect x="0" y="105" width="200" height="3" fill="#ffffff" className="opacity-60" />
        
        {/* Car body */}
        <g className="animate-bounce" style={{ transformOrigin: '100px 60px', animationDuration: '2s' }}>
          <rect x="60" y="60" width="80" height="30" rx="8" fill="#3b82f6" />
          <rect x="70" y="45" width="60" height="20" rx="10" fill="#1e40af" />
          
          {/* Windows */}
          <rect x="75" y="48" width="15" height="12" rx="2" fill="#e5e7eb" />
          <rect x="95" y="48" width="15" height="12" rx="2" fill="#e5e7eb" />
          <rect x="115" y="48" width="10" height="12" rx="2" fill="#e5e7eb" />
          
          {/* Wheels */}
          <circle cx="75" cy="90" r="8" fill="#1f2937" />
          <circle cx="125" cy="90" r="8" fill="#1f2937" />
          <circle cx="75" cy="90" r="4" fill="#6b7280" />
          <circle cx="125" cy="90" r="4" fill="#6b7280" />
        </g>
        
        {/* Motion lines */}
        <g className="animate-pulse opacity-40">
          <line x1="10" y1="70" x2="30" y2="70" stroke="#3b82f6" strokeWidth="2" />
          <line x1="15" y1="75" x2="35" y2="75" stroke="#3b82f6" strokeWidth="2" />
          <line x1="5" y1="80" x2="25" y2="80" stroke="#3b82f6" strokeWidth="2" />
        </g>
      </svg>
    </div>
  )
}

// Animated booking process illustration
export function BookingProcessIllustration({ className }: { className?: string }) {
  return (
    <div className={cn("relative w-40 h-32", className)}>
      <svg
        viewBox="0 0 240 180"
        className="w-full h-full"
        aria-hidden="true"
      >
        {/* Calendar */}
        <g className="animate-pulse" style={{ animationDuration: '1.5s' }}>
          <rect x="20" y="20" width="60" height="50" rx="4" fill="#f3f4f6" stroke="#d1d5db" strokeWidth="2" />
          <rect x="20" y="20" width="60" height="12" rx="4" fill="#3b82f6" />
          <circle cx="35" cy="45" r="3" fill="#3b82f6" />
          <circle cx="50" cy="45" r="3" fill="#e5e7eb" />
          <circle cx="65" cy="45" r="3" fill="#e5e7eb" />
          <circle cx="35" cy="55" r="3" fill="#e5e7eb" />
          <circle cx="50" cy="55" r="3" fill="#e5e7eb" />
        </g>
        
        {/* Arrow */}
        <g className="animate-bounce" style={{ animationDuration: '2s', animationDelay: '0.5s' }}>
          <path d="M90 45 L130 45 M125 40 L130 45 L125 50" stroke="#3b82f6" strokeWidth="3" fill="none" />
        </g>
        
        {/* Car */}
        <g className="animate-pulse" style={{ animationDuration: '1.8s', animationDelay: '1s' }}>
          <rect x="150" y="35" width="50" height="20" rx="6" fill="#3b82f6" />
          <rect x="155" y="25" width="40" height="15" rx="8" fill="#1e40af" />
          <circle cx="160" cy="55" r="5" fill="#1f2937" />
          <circle cx="190" cy="55" r="5" fill="#1f2937" />
        </g>
        
        {/* Checkmark */}
        <g className="animate-ping" style={{ animationDuration: '2.5s', animationDelay: '1.5s' }}>
          <circle cx="120" cy="120" r="20" fill="#10b981" className="opacity-20" />
          <circle cx="120" cy="120" r="15" fill="#10b981" />
          <path d="M110 120 L118 128 L130 112" stroke="white" strokeWidth="3" fill="none" />
        </g>
      </svg>
    </div>
  )
}

// Animated search illustration
export function SearchIllustration({ className }: { className?: string }) {
  return (
    <div className={cn("relative w-36 h-28", className)}>
      <svg
        viewBox="0 0 200 160"
        className="w-full h-full"
        aria-hidden="true"
      >
        {/* Search magnifying glass */}
        <g className="animate-bounce" style={{ animationDuration: '2s' }}>
          <circle cx="80" cy="60" r="25" fill="none" stroke="#3b82f6" strokeWidth="4" />
          <line x1="100" y1="80" x2="120" y2="100" stroke="#3b82f6" strokeWidth="4" />
        </g>
        
        {/* Search results (cars) */}
        <g className="animate-fade-in" style={{ animationDuration: '1s', animationDelay: '1s' }}>
          <rect x="40" y="110" width="30" height="15" rx="3" fill="#e5e7eb" />
          <rect x="80" y="110" width="30" height="15" rx="3" fill="#e5e7eb" />
          <rect x="120" y="110" width="30" height="15" rx="3" fill="#e5e7eb" />
          
          <circle cx="45" cy="125" r="3" fill="#6b7280" />
          <circle cx="65" cy="125" r="3" fill="#6b7280" />
          <circle cx="85" cy="125" r="3" fill="#6b7280" />
          <circle cx="105" cy="125" r="3" fill="#6b7280" />
          <circle cx="125" cy="125" r="3" fill="#6b7280" />
          <circle cx="145" cy="125" r="3" fill="#6b7280" />
        </g>
        
        {/* Scanning effect */}
        <g className="animate-pulse opacity-30">
          <rect x="20" y="40" width="120" height="2" fill="#3b82f6" />
          <rect x="20" y="50" width="140" height="2" fill="#3b82f6" />
          <rect x="20" y="60" width="100" height="2" fill="#3b82f6" />
        </g>
      </svg>
    </div>
  )
}

// Animated payment processing illustration
export function PaymentIllustration({ className }: { className?: string }) {
  return (
    <div className={cn("relative w-40 h-32", className)}>
      <svg
        viewBox="0 0 240 180"
        className="w-full h-full"
        aria-hidden="true"
      >
        {/* Credit card */}
        <g className="animate-pulse" style={{ animationDuration: '2s' }}>
          <rect x="40" y="60" width="80" height="50" rx="8" fill="#1f2937" />
          <rect x="40" y="70" width="80" height="8" fill="#3b82f6" />
          <rect x="50" y="85" width="15" height="3" fill="#e5e7eb" />
          <rect x="50" y="90" width="25" height="3" fill="#e5e7eb" />
          <rect x="50" y="95" width="20" height="3" fill="#e5e7eb" />
        </g>
        
        {/* Processing waves */}
        <g className="animate-ping" style={{ animationDuration: '1.5s' }}>
          <circle cx="160" cy="85" r="15" fill="none" stroke="#10b981" strokeWidth="2" className="opacity-60" />
          <circle cx="160" cy="85" r="25" fill="none" stroke="#10b981" strokeWidth="2" className="opacity-40" />
          <circle cx="160" cy="85" r="35" fill="none" stroke="#10b981" strokeWidth="2" className="opacity-20" />
        </g>
        
        {/* Success checkmark */}
        <g className="animate-bounce" style={{ animationDuration: '2s', animationDelay: '1s' }}>
          <circle cx="160" cy="85" r="12" fill="#10b981" />
          <path d="M153 85 L158 90 L167 78" stroke="white" strokeWidth="2" fill="none" />
        </g>
        
        {/* Security shield */}
        <g className="animate-pulse opacity-60" style={{ animationDuration: '2.5s' }}>
          <path d="M200 60 L200 100 L220 90 L220 70 Z" fill="#3b82f6" />
          <path d="M205 75 L210 80 L215 70" stroke="white" strokeWidth="2" fill="none" />
        </g>
      </svg>
    </div>
  )
}

// CSS animations for fade-in effect
const fadeInKeyframes = `
  @keyframes fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .animate-fade-in {
    animation: fade-in 1s ease-out forwards;
  }
`

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style")
  styleSheet.textContent = fadeInKeyframes
  document.head.appendChild(styleSheet)
}
