# PathLink GPS Tracker - Product Requirements Document (PRD)

## 1. Overview
PathLink is a GPS tracking system that integrates with an ESP32 device. 
The ESP32 collects GPS data and sends it to a server for visualization and tracking via an admin dashboard.

---

## 2. Objectives
- Provide real-time location updates from ESP32-based GPS trackers.
- Support multiple environments (Dev and Prod URLs).
- Ensure reliable communication between device and server.
- Enable flexibility to switch between communication protocols (HTTP POST, WebSockets, MQTT alternatives).

---

## 3. Features

### 3.1 Device (ESP32)
- Collect GPS coordinates via GPS module.
- Send GPS data to server using:
  - **HTTP POST (default)** → `gps=<data>` body.
  - **HTTP GET (fallback)** → `/api/gps/tracker/?gps=<data>`.
  - **Optional WebSockets mode** → real-time, persistent connection.
- Configurable Dev and Prod URLs.
- Optional support for MQTT/CoAP/AMQP in future expansion.

### 3.2 Server (Admin/Tracker Map)
- Receive GPS data via API endpoint: `/api/gps/tracker/`.
- Store GPS data in backend database.
- Display live location updates on Admin Tracker Map.
- Support for real-time data ingestion (HTTP, WebSockets).

### 3.3 GUI Control Panel (Future)
- Desktop-based GUI (e.g., Python Tkinter or Electron) to:
  - Configure URLs (Dev/Prod).
  - Switch between HTTP POST, WebSockets, or other protocols.
  - Send test data.
  - Monitor ESP32 responses.

---

## 4. Alternatives to MQTT
While MQTT is commonly used in IoT projects, alternatives are considered for OllieTrack:

- **HTTP/HTTPS (Current Default):**
  - Easy integration with REST API.
  - Higher overhead for frequent updates.

- **WebSockets:**
  - Full-duplex persistent communication.
  - Near real-time updates.
  - Server can push messages back to ESP32.

- **CoAP (Constrained Application Protocol):**
  - Lightweight, runs over UDP.
  - Low bandwidth, IoT-optimized.
  - Requires backend CoAP support.

- **AMQP:**
  - Works with RabbitMQ or similar brokers.
  - Heavy-duty, more complex routing.
  - Better suited for large-scale deployments.

- **LoRaWAN (Hardware Expansion):**
  - Long-range, low-power.
  - Useful if avoiding SIM/data costs.

---

## 5. Updated ESP32 Code (with POST Fix)
```cpp
// ============================================================
// LILYGO T-Call A7670E (ESP32-WROVER-E) — Continuous GNSS + 4G + Push to Map
// Keeps GNSS running, attaches LTE (APN), prints clickable links,
// and actively pushes every fix to your website (HTTP + optional WebSocket).
// ============================================================

// ---- SELECT YOUR BOARD (ONE ONLY) ----
#define LILYGO_T_CALL_A7670_V1_0
// #define LILYGO_T_CALL_A7670_V1_1
// --------------------------------------

#include "utilities.h"  // must come right after the board define

#define TINY_GSM_RX_BUFFER 1024
#define SerialMon Serial
#define TINY_GSM_DEBUG SerialMon
// #define DUMP_AT_COMMANDS  // uncomment to see every AT command

#include <TinyGsmClient.h>
#include <ArduinoHttpClient.h>   // HttpClient + WebSocketClient

#ifdef DUMP_AT_COMMANDS
  #include <StreamDebugger.h>
  StreamDebugger debugger(SerialAT, SerialMon);
  TinyGsm modem(debugger);
#else
  TinyGsm modem(SerialAT);
#endif

// ====== LTE / APN (EDIT IF NEEDED) ======
static const char APN[]       = "internet";   // your carrier APN
static const char GPRS_USER[] = "";           // often empty
static const char GPRS_PASS[] = "";           // often empty
// ========================================

// ====== Your tracker page URLs (clickable) ======
#define MAP_BASE_URL_PROD "https://olliesrentalcar.pathlinkio.app/admin/tracker"
#define MAP_BASE_URL_DEV  "http://***************:3000/admin/tracker"
#define MAP_LAT_PARAM   "lat"
#define MAP_LON_PARAM   "lon"
#define MAP_ACC_PARAM   "acc"
#define MAP_ZOOM_PARAM  "z"
#define MAP_CAR_PARAM   "carId"
#define DEFAULT_ZOOM    16
static const char CAR_ID[] = "lilygo-esp32-01";
// ===============================================

// ====== Active push targets ======
// HTTP (POST JSON)
#define HTTP_PROD_HOST  "olliesrentalcar.pathlinkio.app"
#define HTTP_PROD_PORT  443      // https
#define HTTP_DEV_HOST   "***************"
#define HTTP_DEV_PORT   3000     // http
#define HTTP_INGEST_PATH "/api/gps/ingest"

// WebSocket (send JSON frames)
#define USE_WEBSOCKET   1        // set 0 to disable WS entirely
#define WS_PROD_HOST    "olliesrentalcar.pathlinkio.app"
#define WS_PROD_PORT    443      // wss
#define WS_DEV_HOST     "***************"
#define WS_DEV_PORT     3000     // ws
#define WS_PATH         "/api/ws/tracker"   // adjust if different
// =================================

// ====== Tracking cadence ======
static const uint32_t FIX_INTERVAL_MS   = 5000;
static const uint32_t NOFIX_INTERVAL_MS = 15000;
static const uint8_t  KICK_AFTER_N_NOFIX = 10;
// ==============================

// ----- helpers you already use -----
static bool looksLikeA7670E(const String &s) {
  String u = s; u.toUpperCase();
  return u.indexOf("A7670") >= 0 || u.indexOf("SIMCOM") >= 0;
}

static void printTrackerLinks(double lat, double lon, double accuracyMeters) {
  SerialMon.println();
  SerialMon.println("=== Tracker Links ===");

  // PROD
  SerialMon.print("Prod: ");
  SerialMon.print(MAP_BASE_URL_PROD);
  SerialMon.print("?");
  SerialMon.print(MAP_LAT_PARAM);  SerialMon.print("="); SerialMon.print(lat, 6);
  SerialMon.print("&");
  SerialMon.print(MAP_LON_PARAM);  SerialMon.print("="); SerialMon.print(lon, 6);
  SerialMon.print("&");
  SerialMon.print(MAP_ACC_PARAM);  SerialMon.print("="); SerialMon.print(accuracyMeters, 1);
  SerialMon.print("&");
  SerialMon.print(MAP_ZOOM_PARAM); SerialMon.print("="); SerialMon.print(DEFAULT_ZOOM);
  SerialMon.print("&");
  SerialMon.print(MAP_CAR_PARAM);  SerialMon.print("="); SerialMon.println(CAR_ID);

  // LOCAL DEV
  SerialMon.print("Local: ");
  SerialMon.print(MAP_BASE_URL_DEV);
  SerialMon.print("?");
  SerialMon.print(MAP_LAT_PARAM);  SerialMon.print("="); SerialMon.print(lat, 6);
  SerialMon.print("&");
  SerialMon.print(MAP_LON_PARAM);  SerialMon.print("="); SerialMon.print(lon, 6);
  SerialMon.print("&");
  SerialMon.print(MAP_ACC_PARAM);  SerialMon.print("="); SerialMon.print(accuracyMeters, 1);
  SerialMon.print("&");
  SerialMon.print(MAP_ZOOM_PARAM); SerialMon.print("="); SerialMon.print(DEFAULT_ZOOM);
  SerialMon.print("&");
  SerialMon.print(MAP_CAR_PARAM);  SerialMon.print("="); SerialMon.println(CAR_ID);

  SerialMon.println("====================");
  SerialMon.println();
}

// Kick GNSS on A7670: try both modern (+CGNSS…) and legacy (+CGPS…) paths
static void gnssKick() {
  String r;
  SerialMon.println("GNSS: power on (CGNSS)...");
  modem.sendAT("+CGNSSPWR?");     modem.waitResponse(1000, r); SerialMon.println(r);
  modem.sendAT("+CGNSSPWR=1");    modem.waitResponse(2000, r); SerialMon.println(r);

  modem.sendAT("+CGNSSMODE?");    modem.waitResponse(1000, r); SerialMon.println(r);
  modem.sendAT("+CGNSSMODE=3,1"); modem.waitResponse(2000, r); SerialMon.println(r);

  modem.sendAT("+CGNSSNMEA=1");   modem.waitResponse(2000, r); SerialMon.println(r);

  SerialMon.println("GNSS: legacy fallback (CGPS)...");
  modem.sendAT("+CGPS?");         modem.waitResponse(1000, r); SerialMon.println(r);
  modem.sendAT("+CGPS=1,1");      modem.waitResponse(2000, r); SerialMon.println(r);

  modem.sendAT("+CGNSSPWR=0");    modem.waitResponse(1000, r);
  delay(300);
  modem.sendAT("+CGNSSPWR=1");    modem.waitResponse(2000, r); SerialMon.println(r);
}

// ---------- 4G helpers ----------
static bool waitAT(unsigned tries = 10, unsigned ms = 1000) {
  modem.sendAT("E0"); modem.waitResponse(1000);
  while (tries--) if (modem.testAT(ms)) return true;
  return false;
}

static bool bringUp4G() {
  SerialMon.println("Waiting for network (registration)...");
  if (!modem.waitForNetwork(60000L)) {
    SerialMon.println("Network registration FAILED");
    return false;
  }
  SerialMon.println("Network registered");

  SerialMon.print("Attaching PDP (APN="); SerialMon.print(APN); SerialMon.println(") …");
  if (!modem.gprsConnect(APN, GPRS_USER, GPRS_PASS)) {
    SerialMon.println("PDP attach FAILED");
    return false;
  }
  SerialMon.println("PDP attached");

  String oper = modem.getOperator();       SerialMon.print("Operator: "); SerialMon.println(oper);
  int16_t csq  = modem.getSignalQuality(); SerialMon.print("Signal (CSQ): "); SerialMon.println(csq);
  String ip   = modem.getLocalIP();        SerialMon.print("IP: "); SerialMon.println(ip);
  return true;
}

static void keep4GAlive() {
  if (!modem.isNetworkConnected()) {
    SerialMon.println("Cellular not registered — trying to re-register…");
    if (!modem.waitForNetwork(60000L, true)) return;
  }
  if (!modem.isGprsConnected()) {
    SerialMon.println("PDP down — reattaching…");
    modem.gprsConnect(APN, GPRS_USER, GPRS_PASS);
  }
}
// -------------------------------

// ====== NEW: actively push each fix to your server ======

// Minimal JSON builder (no ArduinoJson dependency)
static String buildJson(float lat, float lon, float speed, float alt, float acc,
                        int vsat, int usat, int year, int month, int day,
                        int hour, int minute, int sec) {
  String j = "{";
  j += "\"carId\":\""; j += CAR_ID; j += "\",";
  j += "\"latitude\":";  j += String(lat, 6); j += ",";
  j += "\"longitude\":"; j += String(lon, 6); j += ",";
  j += "\"speed_kmh\":"; j += String(speed, 2); j += ",";
  j += "\"altitude\":";  j += String(alt, 2); j += ",";
  j += "\"accuracy_m\":";j += String(acc, 1); j += ",";
  j += "\"sat_visible\":"; j += vsat; j += ",";
  j += "\"sat_used\":";    j += usat; j += ",";
  j += "\"timestamp\":\"";
  j += year; j += "-"; if (month<10) j+='0'; j += month;
  j += "-"; if (day<10) j+='0'; j += day;
  j += "T"; if (hour<10) j+='0'; j += hour;
  j += ":"; if (minute<10) j+='0'; j += minute;
  j += ":"; if (sec<10) j+='0'; j += sec;
  j += "Z\"}";
  return j;
}

// --- HTTP POST: tries PROD (HTTPS) then DEV (HTTP) ---
static bool pushHTTP(const String &payload) {
  bool ok = false;

  // PROD over HTTPS
  {
    TinyGsmClientSecure net(modem);
    net.setInsecure(); // NOTE: quick start. Replace with CA cert for full TLS verify.
    HttpClient http(net, HTTP_PROD_HOST, HTTP_PROD_PORT);

    SerialMon.print("HTTP POST https://"); SerialMon.print(HTTP_PROD_HOST);
    SerialMon.println(HTTP_INGEST_PATH);
    http.beginRequest();
    http.post(HTTP_INGEST_PATH);
    http.sendHeader("Content-Type", "application/json");
    http.sendHeader("Content-Length", payload.length());
    http.endRequest();
    http.print(payload);

    int status = http.responseStatusCode();
    String body = http.responseBody();
    http.stop();

    SerialMon.print("  -> PROD status: "); SerialMon.print(status);
    SerialMon.print(" body: "); SerialMon.println(body);
    ok = (status >= 200 && status < 300);
  }

  // Fallback to DEV over HTTP if PROD failed
  if (!ok) {
    TinyGsmClient net(modem);
    HttpClient http(net, HTTP_DEV_HOST, HTTP_DEV_PORT);

    SerialMon.print("HTTP POST http://"); SerialMon.print(HTTP_DEV_HOST);
    SerialMon.println(HTTP_INGEST_PATH);
    http.beginRequest();
    http.post(HTTP_INGEST_PATH);
    http.sendHeader("Content-Type", "application/json");
    http.sendHeader("Content-Length", payload.length());
    http.endRequest();
    http.print(payload);

    int status = http.responseStatusCode();
    String body = http.responseBody();
    http.stop();

    SerialMon.print("  -> DEV status: "); SerialMon.print(status);
    SerialMon.print(" body: "); SerialMon.println(body);
    ok = (status >= 200 && status < 300);
  }

  return ok;
}

// --- WebSocket: tries PROD (wss) then DEV (ws). Falls back to HTTP on failure. ---
static bool pushWS(const String &payload) {
#if USE_WEBSOCKET
  // PROD wss
  {
    TinyGsmClientSecure wsNet(modem);
    wsNet.setInsecure(); // quick start; use CA cert for strict TLS
    WebSocketClient ws(wsNet, WS_PROD_HOST, WS_PROD_PORT);

    SerialMon.print("WS connect wss://"); SerialMon.print(WS_PROD_HOST);
    SerialMon.println(WS_PATH);
    if (ws.begin(WS_PATH) == 0) {
      SerialMon.println("  -> WS PROD connected");
      ws.beginMessage(TYPE_TEXT);
      ws.print(payload);
      ws.endMessage();
      ws.stop();
      return true;
    } else {
      SerialMon.println("  -> WS PROD handshake failed");
      ws.stop();
    }
  }

  // DEV ws
  {
    TinyGsmClient wsNet(modem);
    WebSocketClient ws(wsNet, WS_DEV_HOST, WS_DEV_PORT);

    SerialMon.print("WS connect ws://"); SerialMon.print(WS_DEV_HOST);
    SerialMon.println(WS_PATH);
    if (ws.begin(WS_PATH) == 0) {
      SerialMon.println("  -> WS DEV connected");
      ws.beginMessage(TYPE_TEXT);
      ws.print(payload);
      ws.endMessage();
      ws.stop();
      return true;
    } else {
      SerialMon.println("  -> WS DEV handshake failed");
      ws.stop();
    }
  }
#endif
  return false;
}

static void pushToTracker(float lat, float lon, float speed, float alt, float acc,
                          int vsat, int usat, int year, int month, int day,
                          int hour, int minute, int sec) {
  if (!modem.isGprsConnected()) {
    SerialMon.println("No data connection; skipping push");
    return;
  }
  const String payload = buildJson(lat, lon, speed, alt, acc, vsat, usat,
                                   year, month, day, hour, minute, sec);
  SerialMon.print("Push payload: "); SerialMon.println(payload);

  bool ok = false;
#if USE_WEBSOCKET
  ok = pushWS(payload);
#endif
  if (!ok) {
    ok = pushHTTP(payload);
  }

  if (ok) SerialMon.println("Push OK");
  else    SerialMon.println("Push FAILED");
}
// =======================================================

void setup() {
  SerialMon.begin(115200);

#ifdef BOARD_POWERON_PIN
  pinMode(BOARD_POWERON_PIN, OUTPUT);
  digitalWrite(BOARD_POWERON_PIN, HIGH);
#endif

#ifdef MODEM_RESET_PIN
  pinMode(MODEM_RESET_PIN, OUTPUT);
  digitalWrite(MODEM_RESET_PIN, !MODEM_RESET_LEVEL); delay(100);
  digitalWrite(MODEM_RESET_PIN,  MODEM_RESET_LEVEL);  delay(2600);
  digitalWrite(MODEM_RESET_PIN, !MODEM_RESET_LEVEL);
#endif

  pinMode(MODEM_DTR_PIN, OUTPUT);
  digitalWrite(MODEM_DTR_PIN, LOW);

  pinMode(BOARD_PWRKEY_PIN, OUTPUT);
  digitalWrite(BOARD_PWRKEY_PIN, LOW);
  delay(100);
  digitalWrite(BOARD_PWRKEY_PIN, HIGH);
  delay(MODEM_POWERON_PULSE_WIDTH_MS);
  digitalWrite(BOARD_PWRKEY_PIN, LOW);

  SerialAT.begin(115200, SERIAL_8N1, MODEM_RX_PIN, MODEM_TX_PIN);

  SerialMon.println("Start modem...");
  delay(6000);

  if (!waitAT()) {
    SerialMon.println("AT not responding — continuing for debug");
  } else {
    SerialMon.println("AT OK");
  }

  // Identify modem (optional)
  String modemName = modem.getModemName();
  SerialMon.print("TinyGSM modemName: "); SerialMon.println(modemName);
  String id;
  modem.sendAT("I");          modem.waitResponse(2000, id);
  modem.sendAT("+CGMM");      modem.waitResponse(2000, id);
  modem.sendAT("+SIMCOMATI"); modem.waitResponse(4000, id);
  SerialMon.println("ID block:"); SerialMon.println(id);

  SerialMon.println("Enabling GPS/GNSS/GLONASS");
  while (!modem.enableGPS(MODEM_GPS_ENABLE_GPIO, MODEM_GPS_ENABLE_LEVEL)) {
    SerialMon.print(".");
    delay(500);
  }
  SerialMon.println();
  SerialMon.println("GPS Enabled");
  modem.setGPSBaud(115200);
  gnssKick();

  if (!bringUp4G()) {
    SerialMon.println("WARN: 4G bring-up failed; GNSS will still run. Check APN/coverage/SIM.");
  } else {
    SerialMon.println("4G connected");
  }
}

void loop() {
  static uint32_t nextPollMs = 0;
  static uint8_t  nofixCount = 0;

  keep4GAlive();

  const uint32_t now = millis();
  if ((int32_t)(now - nextPollMs) < 0) {
    delay(5);
    return;
  }

  float lat=0, lon=0, speed=0, alt=0, acc=0;
  int vsat=0, usat=0, year=0, month=0, day=0, hour=0, minute=0, sec=0;
  uint8_t fixMode = 0;

  SerialMon.println("Requesting current GPS/GNSS/GLONASS location");
  bool ok = modem.getGPS(&fixMode, &lat, &lon, &speed, &alt, &vsat, &usat, &acc,
                         &year, &month, &day, &hour, &minute, &sec);

  if (ok) {
    nofixCount = 0;

    SerialMon.print("FixMode: "); SerialMon.println(fixMode);
    SerialMon.print("Latitude: "); SerialMon.print(lat, 6);
    SerialMon.print("\tLongitude: "); SerialMon.println(lon, 6);
    SerialMon.print("Speed: "); SerialMon.print(speed);
    SerialMon.print("\tAltitude: "); SerialMon.println(alt);
    SerialMon.print("Visible Satellites: "); SerialMon.print(vsat);
    SerialMon.print("\tUsed Satellites: "); SerialMon.println(usat);
    SerialMon.print("Accuracy: "); SerialMon.println(acc);
    SerialMon.print("UTC: ");
    SerialMon.print(year); SerialMon.print("-");
    SerialMon.print(month); SerialMon.print("-");
    SerialMon.print(day); SerialMon.print(" ");
    SerialMon.print(hour); SerialMon.print(":");
    SerialMon.print(minute); SerialMon.print(":");
    SerialMon.println(sec);

    // keep your printable links
    printTrackerLinks(lat, lon, acc);

    // NEW: push to your site (WS first if enabled, then HTTP)
    pushToTracker(lat, lon, speed, alt, acc, vsat, usat, year, month, day, hour, minute, sec);

    nextPollMs = now + FIX_INTERVAL_MS;
  } else {
    String resp;
    modem.sendAT("+CGNSSINFO"); modem.waitResponse(2000, resp); SerialMon.println(resp);
    modem.sendAT("+CGPSINFO");  modem.waitResponse(2000, resp); SerialMon.println(resp);

    nofixCount++;
    if (nofixCount >= KICK_AFTER_N_NOFIX) {
      SerialMon.println("No fix for a while — reconfiguring GNSS…");
      gnssKick();
      nofixCount = 0;
    }
    SerialMon.println("No fix yet… Retrying.");
    nextPollMs = now + NOFIX_INTERVAL_MS;
  }
}


```

---

## 6. Future Enhancements
- Implement WebSocket communication for lower latency.
- Add GUI-based configuration tool for easier protocol switching.
- Explore CoAP for more lightweight communication.
- Optional device-side data caching in case of network loss.

---

## 7. Success Criteria
- GPS data automatically appears on Admin Tracker Map without manually entering URL.
- Device supports both Dev and Prod environments.
- Supports multiple communication methods (HTTP, WebSockets, MQTT alternatives).
