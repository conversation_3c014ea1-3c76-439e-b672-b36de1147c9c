import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

// Test the document verification API endpoints
describe('Document Verification API', () => {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL as string,
    process.env.SUPABASE_SERVICE_KEY as string
  );
  
  let testBookingId: string;
  let testDocIds: Record<string, string> = {};
  
  const documentTypes = [
    'drivers_license',
    'government_id',
    'proof_of_billing',
    'proof_of_payment'
  ];
  
  beforeAll(async () => {
    // Create a test booking
    const { data: booking } = await supabase
      .from('bookings')
      .insert({
        user_id: process.env.TEST_USER_ID,
        car_id: process.env.TEST_CAR_ID,
        from_date: new Date(),
        to_date: new Date(Date.now() + 86400000 * 3), // 3 days from now
        status: 'Pending',
        pickup_location: 'Test Location',
        dropoff_location: 'Test Location'
      })
      .select()
      .single();
      
    testBookingId = booking!.id;
    
    // Create test documents
    for (const docType of documentTypes) {
      const { data: document } = await supabase
        .from('booking_documents')
        .insert({
          booking_id: testBookingId,
          document_type: docType,
          file_url: 'https://example.com/test.jpg',
          file_name: 'test.jpg',
          file_size: 1024,
          file_type: 'image/jpeg',
          verification_status: 'pending'
        })
        .select()
        .single();
        
      testDocIds[docType] = document!.id;
    }
  });
  
  afterAll(async () => {
    // Clean up test data
    if (testBookingId) {
      await supabase.from('booking_documents').delete().eq('booking_id', testBookingId);
      await supabase.from('bookings').delete().eq('id', testBookingId);
    }
  });
  
  test('Check document status shows 4 required documents', async () => {
    const { data, error } = await supabase.rpc('get_booking_document_status', {
      booking_uuid: testBookingId
    });
    
    expect(error).toBeNull();
    expect(data).not.toBeNull();
    expect(data.total_required).toBe(4);
    expect(data.uploaded_count).toBe(4);
    expect(data.pending_count).toBe(4);
    expect(data.approved_count).toBe(0);
    expect(data.rejected_count).toBe(0);
    expect(data.is_complete).toBe(true);
  });
  
  test('Test document approval workflow', async () => {
    // Approve driver's license
    await supabase
      .from('booking_documents')
      .update({
        verification_status: 'approved',
        verified_by: 'test-admin',
        verified_at: new Date().toISOString()
      })
      .eq('id', testDocIds['drivers_license']);
      
    // Reject government ID
    await supabase
      .from('booking_documents')
      .update({
        verification_status: 'rejected',
        verification_notes: 'Document is not clear',
        verified_by: 'test-admin',
        verified_at: new Date().toISOString()
      })
      .eq('id', testDocIds['government_id']);
      
    // Request resubmission for proof of billing
    await supabase
      .from('booking_documents')
      .update({
        verification_status: 'requires_resubmission',
        verification_notes: 'Please provide more recent bill',
        verified_by: 'test-admin',
        verified_at: new Date().toISOString()
      })
      .eq('id', testDocIds['proof_of_billing']);
      
    // Check status again
    const { data, error } = await supabase.rpc('get_booking_document_status', {
      booking_uuid: testBookingId
    });
    
    expect(error).toBeNull();
    expect(data).not.toBeNull();
    expect(data.total_required).toBe(4);
    expect(data.uploaded_count).toBe(4);
    expect(data.pending_count).toBe(1); // Only proof_of_payment should be pending
    expect(data.approved_count).toBe(1);
    expect(data.rejected_count).toBe(1);
    // Note: requires_resubmission is not counted as rejected in the function
    expect(data.is_complete).toBe(false); // Should be false since not all are approved
  });
});
