"use client"

import * as React from "react"
import { BookingCard } from "@/components/admin/bookings/booking-card"
import { BookingDetailsModal } from "@/components/admin/bookings/booking-details-modal"
import { addDays, subDays } from "date-fns"
import { useIsMobile } from "@/hooks/use-mobile"
import { Button } from "@/components/ui/button"

export default function ResponsiveBookingCardsTest() {
  const isMobile = useIsMobile()
  const [selectedBooking, setSelectedBooking] = React.useState<any>(null)
  // Mock data for testing
  const today = new Date()
  const mockBookings = [
    {
      id: "B001-2023",
      userName: "<PERSON> Smith",
      carModel: "Toyota Camry",
      from: subDays(today, 2),
      to: addDays(today, 3),
      days: 5,
      status: "Active" as const,
      payStatus: "Paid" as const,
      totalAmount: 25000,
      pickup_location: "Manila Airport Terminal 1",
      dropoff_location: "Manila Airport Terminal 1",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: "B002-2023",
      userName: "<PERSON> with a very long name that might overflow",
      carModel: "Honda Civic",
      from: subDays(today, 1),
      to: addDays(today, 2),
      days: 3,
      status: "Pending" as const,
      payStatus: "Partial" as const,
      totalAmount: 15000,
      pickup_location: "Cebu City Downtown",
      dropoff_location: "Cebu City Downtown",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: "B003-2023",
      userName: "Robert Johnson",
      carModel: "Ford Explorer",
      from: addDays(today, 1),
      to: addDays(today, 8),
      days: 7,
      status: "Pending" as const,
      payStatus: "Unpaid" as const,
      totalAmount: 35000,
      pickup_location: "Davao International Airport",
      dropoff_location: "Davao International Airport",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ]

  const handleAddToCalendar = (booking: any) => {
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log("Add to calendar", booking.id);
    }
    window.open(`https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(`${booking.userName} – ${booking.carModel}`)}&dates=${booking.from.toISOString().replace(/[-:]/g, '').split('.')[0]}Z/${booking.to.toISOString().replace(/[-:]/g, '').split('.')[0]}Z`, '_blank')
  }
  
  // Mock data for detailed testing
  const mockCustomer = {
    name: "John Smith",
    email: "<EMAIL>",
    phone: "+63 ************"
  }
  
  const mockCar = {
    model: "Toyota Camry",
    plate_number: "ABC 123",
    transmission: "Automatic",
    type: "Sedan",
    seats: 5
  }
  
  const mockPayment = {
    id: "PAY-123456",
    amount: 25000,
    method: "Bank Transfer",
    status: "Paid",
    transaction_id: "TXN-987654",
    proof_of_payment_url: "https://example.com/receipt.pdf"
  }
  
  const handleStatusChange = (bookingId: string, status: any) => {
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log(`Booking ${bookingId} status changed to ${status}`);
    }
    alert(`Booking ${bookingId} status changed to ${status}`)
  }
  
  const handlePaymentVerification = (paymentId: string, action: string, reason?: string) => {
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log(`Payment ${paymentId} ${action}`, reason || '');
    }
    alert(`Payment ${paymentId} ${action} ${reason || ''}`)
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Responsive Booking Cards Test</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-3">Current Viewport Size</h2>
        <div className="grid grid-cols-6 gap-2 text-center">
          <div className="bg-red-100 p-2 rounded block xs:hidden">xs: &lt;375px</div>
          <div className="bg-orange-100 p-2 rounded hidden xs:block sm:hidden">sm: 375px</div>
          <div className="bg-yellow-100 p-2 rounded hidden sm:block md:hidden">md: 425px</div>
          <div className="bg-green-100 p-2 rounded hidden md:block lg:hidden">lg: 768px</div>
          <div className="bg-blue-100 p-2 rounded hidden lg:block xl:hidden">xl: 1024px</div>
          <div className="bg-purple-100 p-2 rounded hidden xl:block">2xl: 1280px+</div>
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-3">Updated Booking Cards</h2>
        
        <div className="p-4 bg-yellow-50 rounded-lg mb-4">
          <p className="font-medium">
            Current view: {isMobile ? "Mobile/Tablet" : "Desktop"} - 
            {isMobile ? "Only showing name, car model, and icon-only buttons" : "Showing all details"}
          </p>
          <p className="text-sm mt-2">
            Container width: <span className="font-mono">{typeof window !== 'undefined' ? `${window.innerWidth}px` : 'Loading...'}</span>
          </p>
        </div>
        
        {/* Testing with different widths */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Default Grid Layout</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {mockBookings.map((booking) => (
                <BookingCard 
                  key={booking.id}
                  booking={booking}
                  onAddToCalendar={handleAddToCalendar}
                  onStatusChange={handleStatusChange}
                />
              ))}
            </div>
          </div>
          
          {isMobile && (
            <div>
              <h3 className="text-lg font-medium mb-2">Mobile Constrained Width (320px)</h3>
              <div className="max-w-[320px] mx-auto border border-dashed border-red-300 p-2">
                <BookingCard 
                  booking={mockBookings[0]}
                  onAddToCalendar={handleAddToCalendar}
                  onStatusChange={handleStatusChange}
                />
              </div>
            </div>
          )}
        </div>
      </div>
      
      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Responsive Testing Instructions</h2>
        <ul className="list-disc pl-5 space-y-2">
          <li><strong>Mobile S (320px):</strong> Verify icon-only buttons display properly without text</li>
          <li><strong>Mobile M (375px):</strong> Check that buttons are properly centered with only icons</li>
          <li><strong>Mobile L (425px):</strong> Test button interactions and modal opening</li>
          <li><strong>Tablet (768px):</strong> Verify card layout transitions properly to desktop view with text buttons</li>
          <li><strong>Desktop (1024px+):</strong> Confirm full details and text buttons display correctly</li>
          <li><strong>Modal Testing:</strong> Verify modal buttons also show icon-only on mobile and text on desktop</li>
          <li><strong>Button Accessibility:</strong> Check that title attributes work when hovering over icon-only buttons</li>
        </ul>
      </div>
      
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">BookingDetailsModal Testing</h2>
        <p className="mb-4">Click "View Details" on any card to test the modal with all sections, or use the direct test button below:</p>
        
        <div className="flex flex-wrap gap-2 mb-4">
          <Button 
            onClick={() => setSelectedBooking(mockBookings[0])}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Open Test Modal
          </Button>
        </div>
        
        <ul className="list-disc pl-5 space-y-2 mb-4">
          <li><strong>Customer Information:</strong> With contact details and copy buttons</li>
          <li><strong>Vehicle Information:</strong> With detailed car specs</li>
          <li><strong>Booking Schedule:</strong> With pickup/dropoff locations and times</li>
          <li><strong>Payment Information:</strong> With verification controls</li>
          <li><strong>Legal Documents:</strong> With document viewer and verification</li>
          <li><strong>Booking Management:</strong> With status controls</li>
          <li><strong>Audit Trail:</strong> With booking history</li>
        </ul>
        
        <p className="font-medium">Test the following responsive behaviors:</p>
        <ul className="list-disc pl-5 space-y-2">
          <li>Modal sizing and scrolling on different screen sizes</li>
          <li>Icon-only buttons on mobile vs text buttons on desktop</li>
          <li>Card styling and shadows across all sections</li>
          <li>Document viewer modal opening and controls</li>
          <li>Copy-to-clipboard functionality for contact info</li>
        </ul>
      </div>
      
      {/* Direct BookingDetailsModal test with all data */}
      {selectedBooking && (
        <BookingDetailsModal
          booking={selectedBooking}
          isOpen={!!selectedBooking}
          onClose={() => setSelectedBooking(null)}
          onAddToCalendar={() => handleAddToCalendar(selectedBooking)}
          onPaymentVerification={handlePaymentVerification}
          onStatusChange={handleStatusChange}
          customer={mockCustomer}
          car={mockCar}
          payment={mockPayment}
          currentAdminId="ADMIN-123"
        />
      )}
    </div>
  )
}
