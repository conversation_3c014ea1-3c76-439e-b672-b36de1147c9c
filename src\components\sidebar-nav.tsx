"use client"

import { Car, CircleHelp, Cog, LayoutDashboard, LogOut, Package, Users, Scale, Phone } from 'lucide-react'
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useCustomerAuth, CustomerAuthContext } from "./auth/customer-auth-context"
import { useRouter } from "next/navigation"
import { useSidebar } from "./nav/sidebar-context"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip"
import { useContext } from "react"

// Navigation structure with clear hierarchy
const primaryItems = [
  { label: "Overview", icon: LayoutDashboard, href: "/" },
  { label: "Catalog", icon: Package, href: "/customer/catalog" },
  { label: "Dashboard", icon: Users, href: "/customer/dashboard" },
]

const supportItems = [
  { label: "FAQ", icon: CircleHelp, href: "/customer/faq" },
  { label: "Contact Us", icon: Phone, href: "/customer/contact" },
]

const accountItems = [
  { label: "Terms", icon: Scale, href: "/customer/terms" },
  { label: "Settings", icon: Cog, href: "/customer/settings" },
]

export function SidebarNav() {
  // Check if we're within a CustomerAuthProvider
  const context = useContext(CustomerAuthContext)
  
  // Use auth safely - provide noop function if not in provider
  const signOut = context ? useCustomerAuth().signOut : async () => {}
  
  const router = useRouter()
  const pathname = usePathname()
  const { isCollapsed } = useSidebar()

  const isActive = (href: string) => {
    if (href === "/") {
      return pathname === "/"
    }
    return pathname.startsWith(href)
  }

  return (
    <div className="h-screen sticky top-0 p-4">
      <Link href="/" className="flex items-center gap-3 px-2 py-4 hover:bg-gray-50 rounded-lg transition-colors">
        <div className="relative">
          <Image
            src="/ollie_logo.svg"
            alt="Ollie Track Logo"
            width={32}
            height={32}
            className="w-8 h-8"
          />
        </div>
        {!isCollapsed && (
          <div className="flex flex-col">
            <span className="font-bold text-gray-900 text-lg">Ollie Track</span>
            <span className="text-xs text-gray-500 font-medium">Car Rental</span>
          </div>
        )}
      </Link>
      <nav className="mt-6 space-y-6">
        {/* Primary Navigation */}
        <div>
          {!isCollapsed && (
            <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
              Main
            </h3>
          )}
          <div className="space-y-1">
            {primaryItems.map((item) => (
              <TooltipProvider key={item.href}>
                <Tooltip disableHoverableContent={!isCollapsed}>
                  <TooltipTrigger asChild>
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center gap-3 rounded-md px-3 py-2.5 text-sm transition-colors",
                        isActive(item.href)
                          ? "active-link bg-blue-50 text-blue-700 font-medium border-r-2 border-blue-600"
                          : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                      )}
                    >
                      <item.icon className="h-4 w-4" />
                      {!isCollapsed && <span>{item.label}</span>}
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="px-2 py-1 text-xs">
                    {item.label}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
          </div>
        </div>

        {/* Support Navigation */}
        <div>
          {!isCollapsed && (
            <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
              Support
            </h3>
          )}
          <div className="space-y-1">
            {supportItems.map((item) => (
              <TooltipProvider key={item.href}>
                <Tooltip disableHoverableContent={!isCollapsed}>
                  <TooltipTrigger asChild>
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center gap-3 rounded-md px-3 py-2.5 text-sm transition-colors",
                        isActive(item.href)
                          ? "active-link bg-blue-50 text-blue-700 font-medium border-r-2 border-blue-600"
                          : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                      )}
                    >
                      <item.icon className="h-4 w-4" />
                      {!isCollapsed && <span>{item.label}</span>}
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="px-2 py-1 text-xs">
                    {item.label}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
          </div>
        </div>

        {/* Account Navigation */}
        <div>
          {!isCollapsed && (
            <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
              Account
            </h3>
          )}
          <div className="space-y-1">
            {accountItems.map((item) => (
              <TooltipProvider key={item.href}>
                <Tooltip disableHoverableContent={!isCollapsed}>
                  <TooltipTrigger asChild>
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center gap-3 rounded-md px-3 py-2.5 text-sm transition-colors",
                        isActive(item.href)
                          ? "active-link bg-blue-50 text-blue-700 font-medium border-r-2 border-blue-600"
                          : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                      )}
                    >
                      <item.icon className="h-4 w-4" />
                      {!isCollapsed && <span>{item.label}</span>}
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="px-2 py-1 text-xs">
                    {item.label}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
          </div>
        </div>
        <button
          onClick={() => {
            signOut()
            router.push("/customer/login")
          }}
          className="w-full text-left flex items-center gap-3 rounded-md px-3 py-2 text-sm text-muted-foreground hover:bg-muted hover:text-foreground transition-colors"
        >
          <LogOut className="h-4 w-4" />
          {!isCollapsed && <span>Log out</span>}
        </button>
      </nav>
    </div>
  )
}
