import { type Booking, type Car, type Payment, type User } from "./types"
import { overlap } from "./date-utils"

// Legacy interface for backward compatibility with existing UI components
interface LegacyCar {
  id: string
  model: string
  type: "SUV" | "Sport" | "Coupe" | "Hatchback" | "MPV" | "Sedan"
  plateNumber: string
  status: "Available" | "Rented" | "In Maintenance"
  condition: "Good" | "Needs Repair"
  availability: null | { from: string; to: string }
  fuelCapacity: number
  fuelType: string
  transmission: "Manual" | "Automatic"
  seats: number
  pricePerDay: number
  imageURL: string
  notes?: string
  isArchived?: boolean
}

interface LegacyBooking {
  id: string
  customerId: string
  carId: string
  pickUpLocation: string
  dropOffLocation: string
  pickUpDateTime: string
  dropOffDateTime: string
  specialRequests?: string
  status: "Pending" | "Active" | "Completed" | "Cancelled"
  totalAmount: number
}

interface LegacyPayment {
  id: string
  bookingId: string
  amount: number
  method: "Card" | "Wallet" | "Cash" | "GCash" | "PayMaya"
  status: "Pending" | "Paid" | "Failed" | "Refunded" | "Pending Verification" | "Rejected"
  transactionDate: string
}

// Conversion functions to map between legacy and new types
function legacyCarToNew(legacyCar: LegacyCar): Car {
  return {
    id: legacyCar.id,
    model: legacyCar.model,
    type: legacyCar.type,
    plate_number: legacyCar.plateNumber,
    status: legacyCar.status,
    condition: legacyCar.condition,
    fuel_capacity: legacyCar.fuelCapacity,
    fuel_type: legacyCar.fuelType,
    transmission: legacyCar.transmission,
    seats: legacyCar.seats,
    price_per_day: legacyCar.pricePerDay,
    image_url: legacyCar.imageURL,
    notes: legacyCar.notes,
    is_archived: legacyCar.isArchived || false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }
}

function newCarToLegacy(car: Car): LegacyCar {
  return {
    id: car.id,
    model: car.model,
    type: car.type,
    plateNumber: car.plate_number,
    status: car.status,
    condition: car.condition,
    availability: null,
    fuelCapacity: car.fuel_capacity,
    fuelType: car.fuel_type,
    transmission: car.transmission,
    seats: car.seats,
    pricePerDay: car.price_per_day,
    imageURL: car.image_url || "",
    notes: car.notes,
    isArchived: car.is_archived,
  }
}

function legacyBookingToNew(legacyBooking: LegacyBooking): Booking {
  return {
    id: legacyBooking.id,
    customer_id: legacyBooking.customerId,
    car_id: legacyBooking.carId,
    pickup_location: legacyBooking.pickUpLocation,
    dropoff_location: legacyBooking.dropOffLocation,
    pickup_datetime: legacyBooking.pickUpDateTime,
    dropoff_datetime: legacyBooking.dropOffDateTime,
    special_requests: legacyBooking.specialRequests,
    status: legacyBooking.status,
    total_amount: legacyBooking.totalAmount,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }
}

function legacyPaymentToNew(legacyPayment: LegacyPayment): Payment {
  return {
    id: legacyPayment.id,
    booking_id: legacyPayment.bookingId,
    amount: legacyPayment.amount,
    status: legacyPayment.status,
    method: legacyPayment.method,
    transaction_date: legacyPayment.transactionDate,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }
}

// Simulated in-memory store. In real app, replace with Convex functions.
const users: User[] = [
  { 
    id: "u1", 
    name: "Charlie Richard", 
    email: "<EMAIL>", 
    role: "customer",
    full_name: "Charlie Richard",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  { 
    id: "u2", 
    name: "Maria Santos", 
    email: "<EMAIL>", 
    role: "customer",
    full_name: "Maria Santos",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  { 
    id: "u3", 
    name: "John Dela Cruz", 
    email: "<EMAIL>", 
    role: "customer",
    full_name: "John Dela Cruz",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  { 
    id: "u4", 
    name: "Ana Rodriguez", 
    email: "<EMAIL>", 
    role: "customer",
    full_name: "Ana Rodriguez",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  { 
    id: "u5", 
    name: "David Kim", 
    email: "<EMAIL>", 
    role: "customer",
    full_name: "David Kim",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  { 
    id: "admin", 
    name: "Admin User", 
    email: "<EMAIL>", 
    role: "admin",
    full_name: "Admin User",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
]

// Static admin credentials for authentication
const ADMIN_CREDENTIALS = {
  email: "<EMAIL>",
  password: "OlliesAdmin123!"
}

let cars: LegacyCar[] = [
  // Cars (11 total)
  {
    id: "c1",
    model: "Toyota Vios XLE 2023 CVT",
    type: "Hatchback",
    plateNumber: "ABC-1001",
    status: "Available",
    condition: "Good",
    availability: null,
    fuelCapacity: 42,
    fuelType: "Gas/Premium",
    transmission: "Automatic",
    seats: 5,
    pricePerDay: 2000,
    imageURL: "/toyota_vios_xle_2023_cvt.png",
  },
  {
    id: "c2",
    model: "Toyota Vios E 2016 AT",
    type: "Hatchback",
    plateNumber: "ABC-1002",
    status: "Rented",
    condition: "Good",
    availability: null,
    fuelCapacity: 42,
    fuelType: "Gas/Premium",
    transmission: "Automatic",
    seats: 5,
    pricePerDay: 1600,
    imageURL: "/toyota_vios_e_2016_at.png",
  },
  {
    id: "c3",
    model: "Toyota Fortuner G 2017 AT",
    type: "SUV",
    plateNumber: "SUV-2001",
    status: "Available",
    condition: "Good",
    availability: null,
    fuelCapacity: 80,
    fuelType: "Diesel",
    transmission: "Automatic",
    seats: 8,
    pricePerDay: 2700,
    imageURL: "/toyota_fortuner_g_2017_at.png",
  },
  {
    id: "c4",
    model: "Toyota Fortuner V 2018 AT",
    type: "SUV",
    plateNumber: "SUV-2002",
    status: "Rented",
    condition: "Good",
    availability: null,
    fuelCapacity: 80,
    fuelType: "Diesel",
    transmission: "Automatic",
    seats: 8,
    pricePerDay: 3500,
    imageURL: "/toyota_fortuner_v_2018_at.png",
  },
  {
    id: "c5",
    model: "Toyota Innova J 2006 MT",
    type: "MPV",
    plateNumber: "MPV-3001",
    status: "Available",
    condition: "Good",
    availability: null,
    fuelCapacity: 65,
    fuelType: "Gas/Premium",
    transmission: "Manual",
    seats: 8,
    pricePerDay: 1600,
    imageURL: "/toyota_innova_j_2006_mt.png",
  },
  {
    id: "c6",
    model: "Toyota GL Grandia 2011 MT",
    type: "MPV",
    plateNumber: "MPV-3002",
    status: "Available",
    condition: "Good",
    availability: null,
    fuelCapacity: 70,
    fuelType: "Diesel",
    transmission: "Manual",
    seats: 12,
    pricePerDay: 3800,
    imageURL: "/toyota_gl_grandia_2011_mt.png",
    notes: "With Driver only. Rate depends on destination.",
  },
  {
    id: "c7",
    model: "Mitsubishi Mirage GLS 2019 AT",
    type: "Hatchback",
    plateNumber: "HAT-4001",
    status: "Available",
    condition: "Good",
    availability: null,
    fuelCapacity: 35,
    fuelType: "Gas/Premium",
    transmission: "Automatic",
    seats: 5,
    pricePerDay: 1600,
    imageURL: "/mitsubishi_mirage_gls_2019_at.png",
  },
  {
    id: "c8",
    model: "Mitsubishi Xpander GLS 2019 AT",
    type: "MPV",
    plateNumber: "MPV-4001",
    status: "In Maintenance",
    condition: "Needs Repair",
    availability: null,
    fuelCapacity: 45,
    fuelType: "Gas/Premium",
    transmission: "Automatic",
    seats: 7,
    pricePerDay: 2300,
    imageURL: "/mitsubishi_xpander_gls_2019_at.png",
  },
  {
    id: "c9",
    model: "Mitsubishi Xpander GLS 2023 AT",
    type: "MPV",
    plateNumber: "MPV-4002",
    status: "Available",
    condition: "Good",
    availability: null,
    fuelCapacity: 45,
    fuelType: "Gas/Premium",
    transmission: "Automatic",
    seats: 7,
    pricePerDay: 2500,
    imageURL: "/mitsubishi_xpander_gls_2023_at.png",
  },
  {
    id: "c10",
    model: "Mitsubishi Montero 2014 MT",
    type: "SUV",
    plateNumber: "SUV-4001",
    status: "Available",
    condition: "Good",
    availability: null,
    fuelCapacity: 88,
    fuelType: "Diesel",
    transmission: "Manual",
    seats: 8,
    pricePerDay: 2500,
    imageURL: "/mitsubishi_montero_2014_mt.png",
  },
  {
    id: "c11",
    model: "Honda CRV 2007 AT",
    type: "SUV",
    plateNumber: "SUV-5001",
    status: "Available",
    condition: "Good",
    availability: null,
    fuelCapacity: 58,
    fuelType: "Unleaded",
    transmission: "Automatic",
    seats: 6,
    pricePerDay: 2000,
    imageURL: "/honda_crv_2007_at.png",
  },
  // Archived Cars (Sample Templates)
  {
    id: "c12",
    model: "BMW 3 Series 2019 AT",
    type: "Sport",
    plateNumber: "SPT-1001",
    status: "Available",
    condition: "Good",
    availability: null,
    fuelCapacity: 59,
    fuelType: "Gas/Premium",
    transmission: "Automatic",
    seats: 4,
    pricePerDay: 4500,
    imageURL: "/placeholder.svg",
    isArchived: true,
  },
  {
    id: "c13",
    model: "Mercedes-Benz C-Class Coupe 2020",
    type: "Coupe",
    plateNumber: "COU-2001",
    status: "In Maintenance",
    condition: "Needs Repair",
    availability: null,
    fuelCapacity: 66,
    fuelType: "Gas/Premium",
    transmission: "Automatic",
    seats: 4,
    pricePerDay: 5200,
    imageURL: "/placeholder.svg",
    isArchived: true,
  },
  {
    id: "c14",
    model: "Toyota Hiace Grandia 2018 MT",
    type: "MPV",
    plateNumber: "MPV-5001",
    status: "Rented",
    condition: "Good",
    availability: null,
    fuelCapacity: 70,
    fuelType: "Diesel",
    transmission: "Manual",
    seats: 15,
    pricePerDay: 4800,
    imageURL: "/placeholder.svg",
    isArchived: true,
  },
  {
    id: "c15",
    model: "Nissan GT-R 2021 AT",
    type: "Sport",
    plateNumber: "SPT-1002",
    status: "Available",
    condition: "Good",
    availability: null,
    fuelCapacity: 74,
    fuelType: "Gas/Premium",
    transmission: "Automatic",
    seats: 2,
    pricePerDay: 8500,
    imageURL: "/placeholder.svg",
    isArchived: true,
  },
  {
    id: "c16",
    model: "Porsche 911 Carrera 2022",
    type: "Coupe",
    plateNumber: "COU-2002",
    status: "Available",
    condition: "Good",
    availability: null,
    fuelCapacity: 64,
    fuelType: "Gas/Premium",
    transmission: "Automatic",
    seats: 2,
    pricePerDay: 12000,
    imageURL: "/placeholder.svg",
    isArchived: true,
  },
  {
    id: "c17",
    model: "Ford Ranger Raptor 2020 AT",
    type: "SUV",
    plateNumber: "SUV-6001",
    status: "In Maintenance",
    condition: "Needs Repair",
    availability: null,
    fuelCapacity: 80,
    fuelType: "Diesel",
    transmission: "Automatic",
    seats: 5,
    pricePerDay: 3800,
    imageURL: "/placeholder.svg",
    isArchived: true,
  },
]

let bookings: LegacyBooking[] = [
  {
    id: "b1",
    customerId: "u1",
    carId: "c2",
    pickUpLocation: "Laoag City",
    dropOffLocation: "Manila",
    pickUpDateTime: new Date(Date.now() + 24 * 3600 * 1000).toISOString(),
    dropOffDateTime: new Date(Date.now() + 3 * 24 * 3600 * 1000).toISOString(),
    specialRequests: "Child seat",
    status: "Active",
    totalAmount: 4800,
  },
  {
    id: "b2",
    customerId: "u2",
    carId: "c4",
    pickUpLocation: "Makati City",
    dropOffLocation: "Cebu City",
    pickUpDateTime: new Date(Date.now() - 2 * 24 * 3600 * 1000).toISOString(),
    dropOffDateTime: new Date(Date.now() + 5 * 24 * 3600 * 1000).toISOString(),
    specialRequests: "GPS tracker and phone mount",
    status: "Active",
    totalAmount: 24500,
  },
  {
    id: "b3",
    customerId: "u3",
    carId: "c6",
    pickUpLocation: "Manila Airport",
    dropOffLocation: "Baguio City",
    pickUpDateTime: new Date(Date.now() - 5 * 24 * 3600 * 1000).toISOString(),
    dropOffDateTime: new Date(Date.now() - 2 * 24 * 3600 * 1000).toISOString(),
    specialRequests: "Professional driver required",
    status: "Completed",
    totalAmount: 11400,
  },
  {
    id: "b4",
    customerId: "u4",
    carId: "c7",
    pickUpLocation: "Quezon City",
    dropOffLocation: "Tagaytay",
    pickUpDateTime: new Date(Date.now() - 10 * 24 * 3600 * 1000).toISOString(),
    dropOffDateTime: new Date(Date.now() - 8 * 24 * 3600 * 1000).toISOString(),
    specialRequests: undefined,
    status: "Completed",
    totalAmount: 3200,
  },
  {
    id: "b5",
    customerId: "u5",
    carId: "c1",
    pickUpLocation: "Pasig City",
    dropOffLocation: "Antipolo",
    pickUpDateTime: new Date(Date.now() + 7 * 24 * 3600 * 1000).toISOString(),
    dropOffDateTime: new Date(Date.now() + 10 * 24 * 3600 * 1000).toISOString(),
    specialRequests: "Car wash before pickup",
    status: "Pending",
    totalAmount: 6000,
  },
  {
    id: "b6",
    customerId: "u2",
    carId: "c9",
    pickUpLocation: "BGC Taguig",
    dropOffLocation: "Subic Bay",
    pickUpDateTime: new Date(Date.now() - 15 * 24 * 3600 * 1000).toISOString(),
    dropOffDateTime: new Date(Date.now() - 12 * 24 * 3600 * 1000).toISOString(),
    specialRequests: "Additional insurance coverage",
    status: "Cancelled",
    totalAmount: 7500,
  },
  // Additional sample bookings to showcase the new UI
  {
    id: "b7",
    customerId: "u3",
    carId: "c2",
    pickUpLocation: "NAIA Terminal 3",
    dropOffLocation: "Alabang, Muntinlupa",
    pickUpDateTime: new Date(Date.now() - 1 * 24 * 3600 * 1000).toISOString(),
    dropOffDateTime: new Date(Date.now() + 2 * 24 * 3600 * 1000).toISOString(),
    specialRequests: "Pick up from airport arrival area",
    status: "Active",
    totalAmount: 3600,
  },
  {
    id: "b8",
    customerId: "u4",
    carId: "c10",
    pickUpLocation: "Eastwood City, Quezon City",
    dropOffLocation: "La Union",
    pickUpDateTime: new Date(Date.now()).toISOString(),
    dropOffDateTime: new Date(Date.now() + 4 * 24 * 3600 * 1000).toISOString(),
    specialRequests: "Include child car seat and beach equipment",
    status: "Active",
    totalAmount: 12000,
  },
]

let payments: LegacyPayment[] = [
  {
    id: "p1",
    bookingId: "b1",
    amount: 4800,
    method: "Card",
    status: "Paid",
    transactionDate: new Date().toISOString(),
  },
  {
    id: "p2",
    bookingId: "b2",
    amount: 24500,
    method: "Wallet",
    status: "Paid",
    transactionDate: new Date(Date.now() - 2 * 24 * 3600 * 1000).toISOString(),
  },
  {
    id: "p3",
    bookingId: "b3",
    amount: 11400,
    method: "Cash",
    status: "Paid",
    transactionDate: new Date(Date.now() - 5 * 24 * 3600 * 1000).toISOString(),
  },
  {
    id: "p4",
    bookingId: "b4",
    amount: 3200,
    method: "Card",
    status: "Paid",
    transactionDate: new Date(Date.now() - 10 * 24 * 3600 * 1000).toISOString(),
  },
  {
    id: "p5",
    bookingId: "b5",
    amount: 6000,
    method: "Card",
    status: "Pending",
    transactionDate: new Date().toISOString(),
  },
  {
    id: "p6",
    bookingId: "b6",
    amount: 7500,
    method: "Card",
    status: "Refunded",
    transactionDate: new Date(Date.now() - 15 * 24 * 3600 * 1000).toISOString(),
  },
  {
    id: "p7",
    bookingId: "b7",
    amount: 3600,
    method: "Wallet",
    status: "Paid",
    transactionDate: new Date(Date.now() - 1 * 24 * 3600 * 1000).toISOString(),
  },
  {
    id: "p8",
    bookingId: "b8",
    amount: 12000,
    method: "Card",
    status: "Paid",
    transactionDate: new Date().toISOString(),
  },
]

export function getCurrentUser(): User {
  return users[0]
}

export function getUser(userId: string): User | null {
  return users.find(user => user.id === userId) || null
}

export function getPaymentByBookingId(bookingId: string): Payment | null {
  const legacyPayment = payments.find(payment => payment.bookingId === bookingId)
  return legacyPayment ? legacyPaymentToNew(legacyPayment) : null
}

export function listCars(includeArchived = false): Car[] {
  const filteredCars = includeArchived 
    ? cars 
    : cars.filter(car => !car.isArchived)
  return filteredCars.map(legacyCarToNew)
}

export function listArchivedCars(): Car[] {
  return cars.filter(car => car.isArchived).map(legacyCarToNew)
}

export function addCar(data: Omit<Car, "id">) {
  const id = `c${cars.length + 1}`
  const legacyCar: LegacyCar = {
    id,
    model: data.model,
    type: data.type,
    plateNumber: data.plate_number,
    status: data.status,
    condition: data.condition,
    availability: null,
    fuelCapacity: data.fuel_capacity,
    fuelType: data.fuel_type,
    transmission: data.transmission,
    seats: data.seats,
    pricePerDay: data.price_per_day,
    imageURL: data.image_url || "",
    notes: data.notes,
    isArchived: data.is_archived,
  }
  cars = [legacyCar, ...cars]
  return id
}

export function updateCar(id: string, patch: Partial<Car>) {
  cars = cars.map((c) => (c.id === id ? { ...c, ...patch } : c))
}

export function deleteCar(id: string) {
  cars = cars.filter((c) => c.id !== id)
}

export function archiveCar(id: string) {
  cars = cars.map((c) => (c.id === id ? { ...c, isArchived: true } : c))
}

export function unarchiveCar(id: string) {
  cars = cars.map((c) => (c.id === id ? { ...c, isArchived: false } : c))
}

export function listBookingsByUser(userId: string): Booking[] {
  return bookings.filter((b) => b.customerId === userId).map(legacyBookingToNew)
}

export function listAllBookings(): Booking[] {
  return bookings.map(legacyBookingToNew)
}

export function listPaymentsByUser(userId: string): Payment[] {
  const bookingIds = new Set(bookings.filter((b) => b.customerId === userId).map((b) => b.id))
  return payments.filter((p) => bookingIds.has(p.bookingId)).map(legacyPaymentToNew)
}

export function topTypes(): { type: Car["type"]; count: number }[] {
  const counts: Record<string, number> = { Sport: 17439, SUV: 9478, Coupe: 18197, Hatchback: 12510, MPV: 14406 }
  return (["Sport", "SUV", "Coupe", "Hatchback", "MPV"] as Car["type"][]).map((t) => ({ type: t, count: counts[t] }))
}

export function isCarAvailable(carId: string, from: Date, to: Date): boolean {
  if (to <= from) return false
  const related = bookings.filter((b) => b.carId === carId && b.status !== "Cancelled")
  for (const b of related) {
    const bs = new Date(b.pickUpDateTime)
    const be = new Date(b.dropOffDateTime)
    if (overlap(from, to, bs, be)) return false
  }
  const car = cars.find((c) => c.id === carId)
  if (!car) return false
  if (car.status !== "Available") return false
  if (car.availability) {
    const aFrom = new Date(car.availability.from)
    const aTo = new Date(car.availability.to)
    if (!(from >= aFrom && to <= aTo)) return false
  }
  return true
}

export function availableCars(from: Date, to: Date, opts?: { type?: Car["type"]; condition?: Car["condition"] }): Car[] {
  return cars.filter((c) => {
    if (opts?.type && c.type !== opts.type) return false
    if (opts?.condition && c.condition !== opts.condition) return false
    return isCarAvailable(c.id, from, to)
  }).map(legacyCarToNew)
}

export function getCar(id: string): Car | null {
  const legacyCar = cars.find((c) => c.id === id)
  return legacyCar ? legacyCarToNew(legacyCar) : null
}

export function createBooking(input: Omit<Booking, "id" | "status" | "totalAmount"> & { totalAmount?: number }): Booking {
  const id = `b${bookings.length + 1}`
  const legacyBooking: LegacyBooking = {
    id,
    customerId: input.customer_id,
    carId: input.car_id,
    pickUpLocation: input.pickup_location,
    dropOffLocation: input.dropoff_location,
    pickUpDateTime: input.pickup_datetime,
    dropOffDateTime: input.dropoff_datetime,
    specialRequests: input.special_requests,
    status: "Pending",
    totalAmount: input.totalAmount ?? 0,
  }
  bookings.push(legacyBooking)
  return legacyBookingToNew(legacyBooking)
}

export function updateBookingStatus(id: string, status: Booking["status"]): Booking | null {
  const b = bookings.find((x) => x.id === id)
  if (b) {
    b.status = status
    return legacyBookingToNew(b)
  }
  return null
}

export function cancelBooking(id: string): Booking | null {
  const b = bookings.find((x) => x.id === id)
  if (b) {
    b.status = "Cancelled"
    return legacyBookingToNew(b)
  }
  return null
}

export function addPayment(p: Omit<Payment, "id" | "transactionDate">) {
  const id = `p${payments.length + 1}`
  const legacyPayment: LegacyPayment = {
    id,
    bookingId: p.booking_id,
    amount: p.amount,
    status: p.status,
    method: p.method,
    transactionDate: new Date().toISOString(),
  }
  payments.push(legacyPayment)
  return legacyPaymentToNew(legacyPayment)
}

// Authentication functions
export function validateCredentials(email: string, password: string): { isValid: boolean; user: User | null } {
  // Check static admin credentials
  if (email === ADMIN_CREDENTIALS.email && password === ADMIN_CREDENTIALS.password) {
    const adminUser = users.find(u => u.role === "admin" && u.email === email)
    return { isValid: true, user: adminUser || null }
  }

  // For customers, we'll simulate password validation (in real app, use proper hashing)
  const customerUser = users.find(u => u.role === "customer" && u.email === email)
  if (customerUser) {
    // In a real app, you'd hash and compare passwords
    // For demo purposes, we'll accept any password for existing customers
    return { isValid: true, user: customerUser }
  }

  return { isValid: false, user: null }
}

export function createCustomerUser(name: string, email: string): User {
  const id = `u${users.length + 1}`
  const newUser: User = { 
    id, 
    name, 
    email, 
    role: "customer",
    full_name: name,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }
  users.push(newUser)
  return newUser
}
