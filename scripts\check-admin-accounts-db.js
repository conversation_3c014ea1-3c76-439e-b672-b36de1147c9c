#!/usr/bin/env node

/**
 * Database Check Script for Admin Accounts
 * 
 * This script provides SQL queries to verify that both admin accounts
 * are properly configured in the database.
 */

console.log(`
🔧 ADMIN ACCOUNTS DATABASE CHECK
===============================

Use these SQL queries in your Supabase dashboard to verify both admin accounts are properly configured.

🔍 DATABASE VERIFICATION QUERIES:
=================================

**1. Check Both Admin Profiles**:
\`\`\`sql
SELECT 
  id,
  email,
  full_name,
  role,
  created_at,
  updated_at
FROM profiles 
WHERE email IN ('<EMAIL>', '<EMAIL>')
ORDER BY email;
\`\`\`

**Expected Results**:
- <EMAIL>: role = 'super_admin'
- <EMAIL>: role = 'admin'

**2. Check Auth Users Table**:
\`\`\`sql
SELECT 
  id,
  email,
  email_confirmed_at,
  last_sign_in_at,
  user_metadata,
  created_at
FROM auth.users 
WHERE email IN ('<EMAIL>', '<EMAIL>')
ORDER BY email;
\`\`\`

**Expected Results**:
- Both users should exist
- Both should have email_confirmed_at set
- user_metadata might contain role information

**3. Check for Missing Profiles**:
\`\`\`sql
SELECT 
  u.id,
  u.email,
  p.role
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
ORDER BY u.email;
\`\`\`

**Expected Results**:
- No NULL roles
- Both users should have matching profile records

**4. Verify Admin Role Counts**:
\`\`\`sql
SELECT 
  role,
  COUNT(*) as count
FROM profiles 
WHERE role IN ('admin', 'super_admin')
GROUP BY role;
\`\`\`

**Expected Results**:
- admin: 1 (<EMAIL>)
- super_admin: 1 (<EMAIL>)

🛠️ POTENTIAL FIXES:
===================

**If Regular Admin Profile Missing**:
\`\`\`sql
INSERT INTO profiles (id, email, full_name, role, created_at, updated_at)
SELECT 
  id,
  email,
  'Regular Admin' as full_name,
  'admin' as role,
  NOW() as created_at,
  NOW() as updated_at
FROM auth.users 
WHERE email = '<EMAIL>'
AND NOT EXISTS (
  SELECT 1 FROM profiles WHERE id = auth.users.id
);
\`\`\`

**If Regular Admin Has Wrong Role**:
\`\`\`sql
UPDATE profiles 
SET role = 'admin', updated_at = NOW()
WHERE email = '<EMAIL>';
\`\`\`

**If User Metadata Needs Update**:
\`\`\`sql
UPDATE auth.users 
SET user_metadata = jsonb_set(
  COALESCE(user_metadata, '{}'),
  '{role}',
  '"admin"'
)
WHERE email = '<EMAIL>';
\`\`\`

🧪 BROWSER CONSOLE TESTS:
=========================

**Test Profile Fetch for Both Accounts**:

After logging in as each account, run in browser console:

\`\`\`javascript
// Test profile endpoint
fetch('/api/admin/get-profile')
  .then(r => r.json())
  .then(data => {
    console.log('Profile data:', data)
    console.log('Role:', data.profile?.role)
    console.log('Email:', data.profile?.email)
  })
  .catch(console.error)

// Test session restoration
fetch('/api/auth/restore-session', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ context: 'admin' })
}).then(r => r.json()).then(data => {
  console.log('Session restoration:', data)
  console.log('User email:', data.session?.user?.email)
  console.log('User metadata:', data.session?.user?.user_metadata)
}).catch(console.error)
\`\`\`

🔍 TROUBLESHOOTING CHECKLIST:
============================

✅ **Database Level**:
- [ ] Both users exist in auth.users table
- [ ] Both users have profiles in profiles table
- [ ] Regular admin has role = 'admin'
- [ ] Super admin has role = 'super_admin'
- [ ] No orphaned or duplicate records

✅ **Authentication Level**:
- [ ] Both accounts can log in successfully
- [ ] Both accounts get valid sessions
- [ ] Session restoration works for both
- [ ] Profile fetch works for both

✅ **Application Level**:
- [ ] Both accounts can access admin pages
- [ ] Page reload works for both accounts
- [ ] Role-specific features work correctly
- [ ] No authentication errors in console

❌ **COMMON ISSUES**:

**Issue 1: Missing Profile Record**
- Symptom: User can log in but gets "Forbidden: Admin access required"
- Fix: Create profile record with correct role

**Issue 2: Wrong Role in Profile**
- Symptom: User can log in but gets insufficient permissions
- Fix: Update profile role to 'admin'

**Issue 3: Inconsistent User Metadata**
- Symptom: Role validation fails in some contexts
- Fix: Update auth.users.user_metadata to match profile role

**Issue 4: Session Restoration Fails**
- Symptom: Page reload redirects to login
- Fix: Check server-side cookie handling and context client

🎯 SUCCESS CRITERIA:
===================

After database verification:
- ✅ Both admin accounts exist with correct roles
- ✅ Profile fetch returns correct data for both
- ✅ Session restoration works for both
- ✅ No database-level inconsistencies
- ✅ Both accounts have identical authentication behavior

Run these checks to ensure the database foundation is solid for both admin account types!
`);

console.log('\n🔧 ADMIN ACCOUNTS DATABASE CHECK READY:');
console.log('======================================');
console.log('1. Run SQL queries in Supabase dashboard');
console.log('2. Verify both admin accounts exist with correct roles');
console.log('3. Test profile and session endpoints for both accounts');
console.log('4. Fix any database-level inconsistencies');
console.log('\n🧪 VERIFY DATABASE FOUNDATION:');
console.log('1. Check profiles table for both accounts');
console.log('2. Verify auth.users table entries');
console.log('3. Test API endpoints with both accounts');
console.log('\n🎯 Ensure solid database foundation for both admin types!');
