"use client";

import * as React from "react";
import { DesktopAuthPage } from "@/components/admin/auth/desktop-auth-page";

export default function AdminAuthResponsiveTest() {
  const [viewportWidth, setViewportWidth] = React.useState<number>(0);
  const [breakpoint, setBreakpoint] = React.useState<string>("");

  React.useEffect(() => {
    // Update viewport width on initial load and resize
    const updateViewportWidth = () => {
      const width = window.innerWidth;
      setViewportWidth(width);
      
      // Determine current breakpoint
      if (width < 320) setBreakpoint("< Mobile S");
      else if (width < 375) setBreakpoint("Mobile S (320px)");
      else if (width < 425) setBreakpoint("Mobile M (375px)");
      else if (width < 768) setBreakpoint("Mobile L (425px)");
      else if (width < 1024) setBreakpoint("Tablet (768px)");
      else if (width < 1440) setBreakpoint("Laptop (1024px)");
      else setBreakpoint("Desktop (1440px+)");
    };

    updateViewportWidth();
    window.addEventListener("resize", updateViewportWidth);
    return () => window.removeEventListener("resize", updateViewportWidth);
  }, []);

  return (
    <>
      {/* Viewport Size Indicator */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-black/80 text-white p-2 text-center text-sm">
        <div>Current Width: {viewportWidth}px</div>
        <div>Breakpoint: {breakpoint}</div>
      </div>
      
      {/* Admin Auth Page */}
      <DesktopAuthPage />
    </>
  );
}
