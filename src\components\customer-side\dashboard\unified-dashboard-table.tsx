"use client";

import * as React from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { CustomerUnifiedTableSkeleton } from "@/components/customer-side/loading/skeleton-components";
import {
  CalendarDays,
  Car,
  CreditCard,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Filter,
  Search,
  MapPin,
  Receipt,
  Eye,
  FileText,
  X,
  Plus,
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { getBookingStatusDisplay, BOOKING_STATUS_STYLES } from "@/lib/utils/booking-status";
import Image from "next/image";

interface UnifiedBookingData {
  id: string;
  booking_ref?: string;
  type: "booking" | "payment";
  // Booking fields
  status?: "Pending" | "Active" | "Completed" | "Cancelled";
  pickup_location?: string;
  dropoff_location?: string;
  pickup_datetime?: string;
  dropoff_datetime?: string;
  total_amount?: number;
  cars?: {
    id: string;
    model: string;
    type: string;
    image_url: string;
    price_per_day: number;
  } | null;
  // Payment fields
  payment_id?: string;
  payment_ref?: string;
  payment_method?: string;
  payment_status?: "Paid" | "Pending" | "Rejected" | "Pending Verification";
  payment_amount?: number;
  payment_date?: string;
  booking_reference?: string;
  proof_of_payment_url?: string;
  admin_notes?: string;
}

interface UnifiedDashboardTableProps {
  bookings: UnifiedBookingData[];
  payments: UnifiedBookingData[];
  loading?: boolean;
  onFinishTrip?: (bookingId: string) => void;
  onViewReceipt?: (paymentId: string) => void;
  onReuploadPayment?: (bookingId: string) => void;
  onViewBookingDetails?: (bookingId: string) => void;
  onCancelBooking?: (bookingId: string) => void;
  onExtensionRequest?: (booking: UnifiedBookingData) => void;
  completingTrips?: Set<string>;
  cancellingBookings?: Set<string>;
}

export function UnifiedDashboardTable({
  bookings,
  payments,
  loading = false,
  onFinishTrip,
  onViewReceipt,
  onReuploadPayment,
  onViewBookingDetails,
  onCancelBooking,
  onExtensionRequest,
  completingTrips = new Set(),
  cancellingBookings = new Set(),
}: UnifiedDashboardTableProps) {
  const isMobile = useIsMobile();
  const [searchQuery, setSearchQuery] = React.useState("");
  const [statusFilter, setStatusFilter] = React.useState("all");
  const [typeFilter, setTypeFilter] = React.useState("all");

  // Combine bookings and payments into unified data
  const unifiedData = React.useMemo(() => {
    const allData: UnifiedBookingData[] = [
      ...bookings.map(b => ({ ...b, type: "booking" as const })),
      ...payments.map(p => ({ ...p, type: "payment" as const }))
    ];

    // Apply filters
    return allData
      .filter(item => {
        if (searchQuery) {
          const query = searchQuery.toLowerCase();
          const searchableText = `${item.id} ${item.cars?.model || ''} ${item.pickup_location || ''} ${item.dropoff_location || ''} ${item.payment_method || ''}`.toLowerCase();
          if (!searchableText.includes(query)) return false;
        }

        if (statusFilter !== "all") {
          if (item.type === "booking" && item.status?.toLowerCase() !== statusFilter.toLowerCase()) {
            return false;
          }
          if (item.type === "payment" && item.payment_status?.toLowerCase() !== statusFilter.toLowerCase()) {
            return false;
          }
        }

        if (typeFilter !== "all" && item.type !== typeFilter) {
          return false;
        }

        return true;
      })
      .sort((a, b) => {
        // Sort by date - most recent first
        const aDate = new Date(a.pickup_datetime || a.payment_date || 0);
        const bDate = new Date(b.pickup_datetime || b.payment_date || 0);
        return bDate.getTime() - aDate.getTime();
      });
  }, [bookings, payments, searchQuery, statusFilter, typeFilter]);

  const getStatusColor = (status: string, type: "booking" | "payment") => {
    if (type === "booking") {
      const bookingStatus = status as "Pending" | "Active" | "Completed" | "Cancelled";
      const statusStyle = BOOKING_STATUS_STYLES[bookingStatus];
      return statusStyle ? statusStyle.className : "bg-gray-100 text-gray-700 border-gray-200";
    }
    
    // Payment status colors
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-gradient-to-r from-green-100 to-emerald-100 text-green-900 border-2 border-green-400 hover:from-green-200 hover:to-emerald-200 shadow-md";
      case "pending verification":
        return "bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-900 border-2 border-amber-400 hover:from-amber-200 hover:to-yellow-200 shadow-md";
      case "rejected":
        return "bg-gradient-to-r from-red-100 to-pink-100 text-red-900 border-2 border-red-400 hover:from-red-200 hover:to-pink-200 shadow-md";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  const getStatusIcon = (status: string, type: "booking" | "payment") => {
    if (type === "booking") {
      const bookingStatus = status as "Pending" | "Active" | "Completed" | "Cancelled";
      const statusStyle = BOOKING_STATUS_STYLES[bookingStatus];
      return statusStyle ? (
        <span className="mr-1">{statusStyle.icon}</span>
      ) : (
        <Clock className="h-3 w-3 mr-1 text-gray-600" />
      );
    } else {
      // Payment status icons
      switch (status.toLowerCase()) {
        case "paid":
          return <span className="mr-1">💰</span>;
        case "pending verification":
          return <span className="mr-1">⏳</span>;
        case "rejected":
          return <span className="mr-1">❌</span>;
        default:
          return <Clock className="h-3 w-3 mr-1 text-gray-600" />;
      }
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-PH", {
      style: "currency",
      currency: "PHP",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM d, yyyy");
  };

  const formatTime = (dateString: string) => {
    return format(new Date(dateString), "h:mm a");
  };

  if (loading) {
    return (
      <Card className="overflow-hidden" data-testid="unified-dashboard-table">
        <CardHeader className="pb-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <Car className="h-5 w-5" />
                My Dashboard
              </CardTitle>
              <p className="text-muted-foreground text-sm mt-1">
                Loading your bookings and payment history...
              </p>
            </div>
            <div className="flex gap-2">
              <Skeleton className="h-10 w-[140px]" />
              <Skeleton className="h-10 w-[140px]" />
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <CustomerUnifiedTableSkeleton />
        </CardContent>
      </Card>
    );
  }

  // Mobile Card View
  if (isMobile) {
    return (
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2">
            <Car className="h-5 w-5" />
            My Dashboard
          </CardTitle>
          <div className="space-y-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search bookings and payments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="booking">Bookings</SelectItem>
                  <SelectItem value="payment">Payments</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {unifiedData.length === 0 ? (
            <div className="text-center py-12">
              <Car className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No records found</h3>
              <p className="text-gray-600">Your bookings and payments will appear here.</p>
            </div>
          ) : (
            <div className="space-y-3 p-4">
              {unifiedData.map((item) => (
                <Card key={`${item.type}-${item.id}`} className="border border-gray-200">
                  <CardContent className="p-4">
                    {item.type === "booking" ? (
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            {item.cars?.image_url && (
                              <div className="relative h-12 w-16 rounded-lg overflow-hidden bg-gray-100">
                                <Image
                                  src={item.cars.image_url}
                                  alt={item.cars.model}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                            )}
                            <div>
                              <h4 className="font-medium text-gray-900">
                                {item.cars?.model || "Unknown Vehicle"}
                              </h4>
                              <p className="text-sm text-gray-600">#{item.booking_ref || item.id.slice(0, 8)}</p>
                            </div>
                          </div>
                          <Badge className={cn("text-xs border", getStatusColor(item.status || "", "booking"))}>
                            {getStatusIcon(item.status || "", "booking")}
                            {item.type === "booking" ? getBookingStatusDisplay(item.status as any) : item.status}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <CalendarDays className="h-4 w-4" />
                          {item.pickup_datetime && format(new Date(item.pickup_datetime), "MMM d")} - 
                          {item.dropoff_datetime && format(new Date(item.dropoff_datetime), "MMM d, yyyy")}
                        </div>

                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <MapPin className="h-4 w-4" />
                          {item.pickup_location} → {item.dropoff_location}
                        </div>

                        <div className="flex items-center justify-between pt-2 border-t">
                          <span className="font-semibold text-green-600">
                            {formatCurrency(item.total_amount || 0)}
                          </span>
                          {item.status === "Active" && onFinishTrip && (
                            <Button
                              size="sm"
                              onClick={() => onFinishTrip(item.id)}
                              disabled={completingTrips.has(item.id)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              {completingTrips.has(item.id) ? (
                                <>
                                  <Clock className="h-3 w-3 mr-1 animate-spin" />
                                  Finishing...
                                </>
                              ) : (
                                <>
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Trip Finished
                                </>
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium text-gray-900">
                              Payment #{item.payment_ref || item.payment_id || item.id}
                            </h4>
                            <p className="text-sm text-gray-600">
                              Booking: #{item.booking_reference}
                            </p>
                          </div>
                          <Badge className={cn("text-xs border", getStatusColor(item.payment_status || "", "payment"))}>
                            {getStatusIcon(item.payment_status || "", "payment")}
                            {item.payment_status}
                          </Badge>
                        </div>

                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <CreditCard className="h-4 w-4" />
                          {item.payment_method}
                        </div>

                        {item.payment_date && (
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <CalendarDays className="h-4 w-4" />
                            {formatDate(item.payment_date)}
                          </div>
                        )}

                        <div className="flex items-center justify-between pt-2 border-t">
                          <span className="font-semibold text-green-600">
                            {formatCurrency(item.payment_amount || 0)}
                          </span>
                          <div className="flex gap-2">
                            {item.proof_of_payment_url && onViewReceipt && (
                              <Button
                                size="sm"
                                variant="secondary"
                                onClick={() => onViewReceipt(item.payment_id || item.id)}
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                View
                              </Button>
                            )}
                            {item.payment_status === "Rejected" && onReuploadPayment && (
                              <Button
                                size="sm"
                                onClick={() => onReuploadPayment(item.booking_reference || "")}
                              >
                                <Receipt className="h-3 w-3 mr-1" />
                                Reupload
                              </Button>
                            )}
                          </div>
                        </div>

                        {item.admin_notes && (
                          <div className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
                            <strong>Admin Notes:</strong> {item.admin_notes}
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Desktop Table View
  return (
    <Card className="overflow-hidden" data-testid="unified-dashboard-table">
      <CardHeader className="pb-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle className="text-xl font-semibold">My Dashboard</CardTitle>
            <p className="text-muted-foreground text-sm mt-1">
              Manage your bookings and view payment history
            </p>
          </div>
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search bookings and payments..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="booking">Bookings</SelectItem>
                <SelectItem value="payment">Payments</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {unifiedData.length === 0 ? (
          <div className="text-center py-12">
            <Car className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No records found</h3>
            <p className="text-gray-600">Your bookings and payments will appear here.</p>
          </div>
        ) : (
          <div className="max-h-[600px] overflow-auto">
            <Table>
              <TableHeader className="sticky top-0 bg-white z-10">
                <TableRow>
                  <TableHead className="px-4 py-3">Type</TableHead>
                  <TableHead className="px-4 py-3">Details</TableHead>
                  <TableHead className="px-4 py-3">Date</TableHead>
                  <TableHead className="px-4 py-3">Status</TableHead>
                  <TableHead className="px-4 py-3 text-right">Amount</TableHead>
                  <TableHead className="px-4 py-3 text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {unifiedData.map((item) => (
                  <TableRow key={`${item.type}-${item.id}`} className="hover:bg-gray-50">
                    <TableCell className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        {item.type === "booking" ? (
                          <>
                            <Car className="h-4 w-4 text-blue-600" />
                            <span className="font-medium capitalize">Booking</span>
                          </>
                        ) : (
                          <>
                            <CreditCard className="h-4 w-4 text-green-600" />
                            <span className="font-medium capitalize">Payment</span>
                          </>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="px-4 py-4">
                      {item.type === "booking" ? (
                        <div className="flex items-center gap-3">
                          {item.cars?.image_url && (
                            <div className="relative h-10 w-12 rounded overflow-hidden bg-gray-100 flex-shrink-0">
                              <Image
                                src={item.cars.image_url}
                                alt={item.cars.model}
                                fill
                                className="object-cover"
                              />
                            </div>
                          )}
                          <div>
                            <p className="font-medium">{item.cars?.model || "Unknown Vehicle"}</p>
                            <p className="text-sm text-gray-600">#{item.booking_ref || item.id.slice(0, 8)}</p>
                            <p className="text-sm text-gray-500">{item.pickup_location} → {item.dropoff_location}</p>
                          </div>
                        </div>
                      ) : (
                        <div>
                          <p className="font-medium">Payment #{item.payment_ref || item.payment_id || item.id}</p>
                          <p className="text-sm text-gray-600">Booking: #{item.booking_reference}</p>
                          <p className="text-sm text-gray-500">{item.payment_method}</p>
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="px-4 py-4">
                      <div className="text-sm">
                        {item.type === "booking" && item.pickup_datetime ? (
                          <>
                            <p>{formatDate(item.pickup_datetime)}</p>
                            <p className="text-gray-500">{formatTime(item.pickup_datetime)}</p>
                          </>
                        ) : item.payment_date ? (
                          <p>{formatDate(item.payment_date)}</p>
                        ) : (
                          <span className="text-gray-400">N/A</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="px-4 py-4">
                      <Badge className={cn("text-xs border", getStatusColor(
                        item.type === "booking" ? (item.status || "") : (item.payment_status || ""),
                        item.type
                      ))}>
                        {getStatusIcon(
                          item.type === "booking" ? (item.status || "") : (item.payment_status || ""),
                          item.type
                        )}
                        {item.type === "booking" ? getBookingStatusDisplay(item.status as any) : item.payment_status}
                      </Badge>
                    </TableCell>
                    <TableCell className="px-4 py-4 text-right font-semibold">
                      {formatCurrency(
                        item.type === "booking" ? (item.total_amount || 0) : (item.payment_amount || 0)
                      )}
                    </TableCell>
                    <TableCell className="px-4 py-4 text-right">
                      <div className="flex justify-end gap-2">
                        {item.type === "booking" && (
                          <>
                            {/* View Details - Available for all booking statuses */}
                            {onViewBookingDetails && (
                              <Button
                                size="sm"
                                variant="secondary"
                                onClick={() => onViewBookingDetails(item.id)}
                              >
                                <FileText className="h-3 w-3 mr-1" />
                                View Details
                              </Button>
                            )}
                            
                            {/* Cancel Booking - Only for Pending bookings */}
                            {item.status === "Pending" && onCancelBooking && (
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => onCancelBooking(item.id)}
                                disabled={cancellingBookings.has(item.id)}
                              >
                                {cancellingBookings.has(item.id) ? (
                                  <>
                                    <Clock className="h-3 w-3 mr-1 animate-spin" />
                                    Cancelling...
                                  </>
                                ) : (
                                  <>
                                    <X className="h-3 w-3 mr-1" />
                                    Cancel
                                  </>
                                )}
                              </Button>
                            )}
                            
                            {/* Extension Request - Only for Active bookings */}
                            {item.status === "Active" && onExtensionRequest && (
                              <Button
                                size="sm"
                                variant="secondary"
                                onClick={() => onExtensionRequest(item)}
                                className="border-green-300 text-green-700 hover:bg-green-50"
                              >
                                <Plus className="h-3 w-3 mr-1" />
                                Extend
                              </Button>
                            )}
                            
                            {/* Finish Trip - Only for Active bookings */}
                            {item.status === "Active" && onFinishTrip && (
                              <Button
                                size="sm"
                                onClick={() => onFinishTrip(item.id)}
                                disabled={completingTrips.has(item.id)}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                {completingTrips.has(item.id) ? (
                                  <>
                                    <Clock className="h-3 w-3 mr-1 animate-spin" />
                                    Finishing...
                                  </>
                                ) : (
                                  <>
                                    <CheckCircle className="h-3 w-3 mr-1" />
                                    Finish Trip
                                  </>
                                )}
                              </Button>
                            )}
                          </>
                        )}
                        
                        {/* Payment Actions */}
                        {item.type === "payment" && item.proof_of_payment_url && onViewReceipt && (
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => onViewReceipt(item.payment_id || item.id)}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            View Receipt
                          </Button>
                        )}
                        {item.type === "payment" && item.payment_status === "Rejected" && onReuploadPayment && (
                          <Button
                            size="sm"
                            onClick={() => onReuploadPayment(item.booking_reference || "")}
                          >
                            <Receipt className="h-3 w-3 mr-1" />
                            Reupload
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
