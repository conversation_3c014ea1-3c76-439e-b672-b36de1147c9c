-- Truly non-destructive RLS policy updates for super_admin role access
-- Only ADDS new policies, never drops or modifies existing ones
-- Completely safe - no destructive operations

BEGIN;

-- ========================================
-- CARS TABLE - Add super_admin policies if they don't exist
-- ========================================

-- Add super_admin INSERT policy if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'cars' 
      AND policyname = 'Super admins can insert cars'
  ) THEN
    CREATE POLICY "Super admins can insert cars" 
      ON public.cars FOR INSERT 
      TO authenticated
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'super_admin'
        )
      );
  END IF;
END $$;

-- Add super_admin UPDATE policy if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'cars' 
      AND policyname = 'Super admins can update cars'
  ) THEN
    CREATE POLICY "Super admins can update cars" 
      ON public.cars FOR UPDATE 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'super_admin'
        )
      )
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'super_admin'
        )
      );
  END IF;
END $$;

-- Add super_admin DELETE policy if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'cars' 
      AND policyname = 'Super admins can delete cars'
  ) THEN
    CREATE POLICY "Super admins can delete cars" 
      ON public.cars FOR DELETE 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'super_admin'
        )
      );
  END IF;
END $$;

-- ========================================
-- BOOKINGS TABLE - Add super_admin policies if they don't exist
-- ========================================

-- Add super_admin SELECT policy if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'bookings' 
      AND policyname = 'Super admins can view all bookings'
  ) THEN
    CREATE POLICY "Super admins can view all bookings" 
      ON public.bookings FOR SELECT 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'super_admin'
        )
      );
  END IF;
END $$;

-- Add super_admin UPDATE policy if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'bookings' 
      AND policyname = 'Super admins can update bookings'
  ) THEN
    CREATE POLICY "Super admins can update bookings" 
      ON public.bookings FOR UPDATE 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'super_admin'
        )
      );
  END IF;
END $$;

-- Add super_admin DELETE policy if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'bookings' 
      AND policyname = 'Super admins can delete bookings'
  ) THEN
    CREATE POLICY "Super admins can delete bookings" 
      ON public.bookings FOR DELETE 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'super_admin'
        )
      );
  END IF;
END $$;

-- ========================================
-- PAYMENTS TABLE - Add super_admin policies if they don't exist
-- ========================================

-- Add super_admin SELECT policy if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'payments' 
      AND policyname = 'Super admins can view all payments'
  ) THEN
    CREATE POLICY "Super admins can view all payments" 
      ON public.payments FOR SELECT 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'super_admin'
        )
      );
  END IF;
END $$;

-- Add super_admin UPDATE policy if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'payments' 
      AND policyname = 'Super admins can verify payments'
  ) THEN
    CREATE POLICY "Super admins can verify payments" 
      ON public.payments FOR UPDATE 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'super_admin'
        )
      );
  END IF;
END $$;

-- ========================================
-- VEHICLE CATEGORIES - Add super_admin policy if it doesn't exist
-- ========================================

-- Add super_admin full access policy if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'vehicle_categories' 
      AND policyname = 'Super admins have full access to vehicle categories'
  ) THEN
    CREATE POLICY "Super admins have full access to vehicle categories" 
      ON public.vehicle_categories FOR ALL 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'super_admin'
        )
      );
  END IF;
END $$;

-- ========================================
-- CONDITIONAL POLICIES FOR OPTIONAL TABLES
-- ========================================

-- Add super_admin policies for renter management tables if they exist
DO $$ 
BEGIN
  -- Renter status table
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'renter_status') THEN
    IF NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE schemaname = 'public' 
        AND tablename = 'renter_status' 
        AND policyname = 'Super admins can manage renter status'
    ) THEN
      CREATE POLICY "Super admins can manage renter status" 
        ON public.renter_status FOR ALL 
        TO authenticated
        USING (
          EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role = 'super_admin'
          )
        );
    END IF;
  END IF;

  -- Issue categories table
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'issue_categories') THEN
    IF NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE schemaname = 'public' 
        AND tablename = 'issue_categories' 
        AND policyname = 'Super admins can manage issue categories'
    ) THEN
      CREATE POLICY "Super admins can manage issue categories" 
        ON public.issue_categories FOR ALL 
        TO authenticated
        USING (
          EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role = 'super_admin'
          )
        );
    END IF;
  END IF;

  -- Renter issues table
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'renter_issues') THEN
    IF NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE schemaname = 'public' 
        AND tablename = 'renter_issues' 
        AND policyname = 'Super admins can manage renter issues'
    ) THEN
      CREATE POLICY "Super admins can manage renter issues" 
        ON public.renter_issues FOR ALL 
        TO authenticated
        USING (
          EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role = 'super_admin'
          )
        );
    END IF;
  END IF;

  -- User legal documents table
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_legal_documents') THEN
    IF NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE schemaname = 'public' 
        AND tablename = 'user_legal_documents' 
        AND policyname = 'Super admins can manage user legal documents'
    ) THEN
      CREATE POLICY "Super admins can manage user legal documents" 
        ON public.user_legal_documents FOR ALL 
        TO authenticated
        USING (
          EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role = 'super_admin'
          )
        );
    END IF;
  END IF;

  -- Booking documents table
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'booking_documents') THEN
    IF NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE schemaname = 'public' 
        AND tablename = 'booking_documents' 
        AND policyname = 'Super admins can manage booking documents'
    ) THEN
      CREATE POLICY "Super admins can manage booking documents" 
        ON public.booking_documents FOR ALL 
        TO authenticated
        USING (
          EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role = 'super_admin'
          )
        );
    END IF;
  END IF;
END $$;

COMMIT;

-- ========================================
-- VERIFICATION - Show all policies including new ones
-- ========================================

SELECT 
  tablename, 
  policyname,
  cmd as operation,
  CASE 
    WHEN policyname LIKE '%Super admin%' THEN '🆕 New super_admin policy'
    WHEN cmd LIKE '%super_admin%' THEN '✅ Includes super_admin'
    ELSE '📋 Existing policy'
  END as status
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('cars', 'bookings', 'payments', 'vehicle_categories')
ORDER BY tablename, policyname;
