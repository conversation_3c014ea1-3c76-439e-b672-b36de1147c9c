-- Update profiles table to support super_admin role
-- Migration: Add super_admin role to profiles table constraint

-- Drop the existing role constraint
ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_role_check;

-- Add updated constraint that includes super_admin role
ALTER TABLE public.profiles ADD CONSTRAINT profiles_role_check 
CHECK (
  role = ANY (ARRAY['customer'::text, 'admin'::text, 'super_admin'::text])
);

-- Update the role default comment for documentation
COMMENT ON COLUMN public.profiles.role IS 'User role: customer, admin, or super_admin';

-- Optional: Create super admin account (uncomment and update password as needed)
-- INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at)
-- VALUES (
--   gen_random_uuid(),
--   '<EMAIL>',
--   crypt('your-secure-password', gen_salt('bf')),
--   now(),
--   now(),
--   now()
-- ) ON CONFLICT (email) DO NOTHING;

-- Optional: Create super admin profile (run after creating auth user)
-- INSERT INTO public.profiles (id, email, full_name, role, created_at, updated_at)
-- SELECT 
--   au.id,
--   '<EMAIL>',
--   'Super Admin',
--   'super_admin',
--   now(),
--   now()
-- FROM auth.users au 
-- WHERE au.email = '<EMAIL>'
-- ON CONFLICT (id) DO UPDATE SET 
--   role = 'super_admin',
--   updated_at = now();
