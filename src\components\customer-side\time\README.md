# TimePicker Component

A modern, accessible time picker component for the OllieTrack customer-side application.

## Features

- **Modern Design**: Matches the site's typography and design system with opaque popover and clean styling
- **Accessibility**: Full ARIA support with combobox/listbox pattern, keyboard navigation, and screen reader compatibility
- **Keyboard Navigation**: Arrow keys, Home/End, PageUp/PageDown, Enter/Escape support
- **Mobile-Friendly**: Touch targets ≥44px and responsive design
- **Configurable**: Customizable time range, step intervals, and quick actions
- **Consistent**: Single canonical component used across all customer-side instances

## Usage

```tsx
import { TimePicker } from "@/components/customer-side/time"

function MyComponent() {
  const [time, setTime] = useState("08:00")

  return (
    <TimePicker
      value={time}
      onChange={setTime}
      placeholder="Select time"
      minTime="06:00"
      maxTime="22:00"
      step={60}
      showQuickActions={true}
      showSecondaryFormat={true}
      aria-label="Pick-up time"
    />
  )
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `string` | `undefined` | Current time value in HH:mm format |
| `onChange` | `(value: string) => void` | `undefined` | Callback when time changes |
| `placeholder` | `string` | `"Select time"` | Placeholder text |
| `disabled` | `boolean` | `false` | Whether the picker is disabled |
| `className` | `string` | `undefined` | Additional CSS classes |
| `minTime` | `string` | `"06:00"` | Minimum time in HH:mm format |
| `maxTime` | `string` | `"22:00"` | Maximum time in HH:mm format |
| `step` | `number` | `60` | Step interval in minutes |
| `showQuickActions` | `boolean` | `true` | Show Now/+30m/Clear buttons |
| `showSecondaryFormat` | `boolean` | `true` | Show 24-hour format as secondary |
| `id` | `string` | `undefined` | HTML id attribute |
| `aria-label` | `string` | `undefined` | ARIA label for accessibility |
| `aria-describedby` | `string` | `undefined` | ARIA describedby for accessibility |

## Design Specifications

- **Trigger Height**: 48px (h-12) for good touch targets
- **Popover**: Opaque white background with 1px border and soft shadow
- **List Height**: max-height: 320px with scroll
- **Option Height**: 48px (h-12) for accessibility
- **Colors**: Consistent with site's blue theme (blue-600/700)
- **Typography**: Matches site fonts and weights
- **Animations**: Fade + scale 95→100 with reduced motion support

## Accessibility Features

- **ARIA Roles**: combobox, listbox, option
- **ARIA States**: aria-expanded, aria-selected, aria-activedescendant
- **Keyboard Support**: Full navigation with arrow keys, Enter, Escape
- **Focus Management**: Proper focus handling and visual indicators
- **Screen Readers**: Descriptive labels and state announcements

## Current Implementations

1. **Homepage** (`components/customer-side/home/<USER>
   - Quick booking widget time selection
   - Shows secondary 24-hour format

2. **Booking Process** (`components/customer-side/booking/booking-widget.tsx`)
   - Booking form time inputs
   - Streamlined interface without secondary format

3. **Booking Modal** (`components/ui/datetime-picker.tsx`)
   - Full booking form with date/time selection
   - Integrated with existing DateTimePicker

## Legacy Components

For backward compatibility, deprecated wrapper components exist:

- `components/customer-side/home/<USER>
- `components/customer-side/booking/BookingTimePicker.tsx` (DEPRECATED)

These re-export the canonical TimePicker with deprecation warnings.

## Browser Support

- Modern browsers with CSS Grid and Flexbox support
- Mobile Safari, Chrome Mobile, Firefox Mobile
- Desktop Chrome, Firefox, Safari, Edge

## Performance

- Lightweight with minimal DOM manipulation
- Virtualization not needed (under 200 options typically)
- Optimized re-renders with React.memo patterns
- No layout shift (CLS < 0.1)

## Testing Checklist

- [ ] Renders correctly at 320px, 375px, 414px, 768px, 1024px, 1280px
- [ ] Keyboard navigation works (arrows, home/end, page up/down, enter, escape)
- [ ] Screen reader announces states correctly
- [ ] Touch targets are ≥44px on mobile
- [ ] Popover is opaque and clearly visible
- [ ] No body scroll when popover is open
- [ ] Quick actions work correctly (Now, +30m, Clear)
- [ ] Value format preservation (HH:mm)
- [ ] WCAG AA contrast compliance
- [ ] No console errors or warnings
