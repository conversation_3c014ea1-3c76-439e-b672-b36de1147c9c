"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAdminAuth } from './admin-auth-context'

interface AdminProtectionProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function AdminProtection({ children, fallback }: AdminProtectionProps) {
  const { user, profile, loading } = useAdminAuth()
  const router = useRouter()

  // Add a delay before allowing redirects to give session restoration more time
  const [allowRedirect, setAllowRedirect] = useState(false)

  // Check if user is admin or super admin based on role or email if profile is not available
  const superAdminEmail = process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAIL || '<EMAIL>'
  const isAdmin = profile?.role === 'admin' || profile?.role === 'super_admin' || user?.email === superAdminEmail

  // Allow redirects after a delay to give session restoration time
  useEffect(() => {
    const timer = setTimeout(() => {
      setAllowRedirect(true)
    }, 2000) // 2 second delay before allowing redirects

    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // Only access window on client side
    if (typeof window === 'undefined') return

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [AdminProtection] Auth state check:', {
        loading,
        hasUser: !!user,
        userEmail: user?.email,
        hasProfile: !!profile,
        isAdmin,
        allowRedirect,
        currentPath: window.location.pathname,
        currentHref: window.location.href,
        timestamp: new Date().toISOString()
      })
    }

    if (!loading && !user && allowRedirect) {
      const currentPath = window.location.pathname
      const currentHref = window.location.href
      if (process.env.NODE_ENV === 'development') {
        console.log('❌ [AdminProtection] TRIGGERING REDIRECT - No user found after delay', {
          currentPath,
          currentHref,
          encodedPath: encodeURIComponent(currentPath),
          redirectUrl: `/admin-auth?redirect=${encodeURIComponent(currentPath)}`,
          timestamp: new Date().toISOString()
        })
      }
      // Redirect unauthenticated users to admin login
      router.replace(`/admin-auth?redirect=${encodeURIComponent(currentPath)}`)
    }
  }, [user, router, loading, profile, isAdmin, allowRedirect])

  // Show loading state while authentication is being resolved OR during initial delay
  if (loading || !allowRedirect) {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 [AdminProtection] Showing loading state', {
        loading,
        allowRedirect,
        currentPath: typeof window !== 'undefined' ? window.location.pathname : 'SSR',
        reason: loading ? 'auth loading' : 'waiting for redirect delay'
      })
    }
    return (
      <div className="min-h-screen grid place-items-center bg-muted/20">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">Loading...</p>
            <p className="text-xs text-muted-foreground">Please wait...</p>
          </div>
        </div>
      </div>
    )
  }

  // If user is not authenticated
  if (!user) {
    if (process.env.NODE_ENV === 'development') {
      console.log('❌ [AdminProtection] No user, showing auth required message')
    }
    return fallback || (
      <div className="min-h-screen grid place-items-center bg-muted/20">
        <div className="text-center">
          <p>Authentication required. Redirecting to admin login...</p>
        </div>
      </div>
    )
  }

  // If user is authenticated but not admin
  if (user && !isAdmin) {
    if (process.env.NODE_ENV === 'development') {
      console.log('❌ [AdminProtection] User is not admin, redirecting')
    }
    router.replace('/admin-auth?error=insufficient_permissions')
    return (
      <div className="min-h-screen grid place-items-center bg-muted/20">
        <div className="text-center">
          <p>Insufficient permissions. Redirecting to admin login...</p>
        </div>
      </div>
    )
  }

  // User is authenticated admin, render children
  if (process.env.NODE_ENV === 'development') {
    console.log('✅ [AdminProtection] User is authenticated admin, rendering children')
  }
  return <>{children}</>
}
