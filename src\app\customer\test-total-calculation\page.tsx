"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { calculateBookingTotal, getBookingCostBreakdown } from "@/utils/booking-total-calculation";
import type { BookingData } from "@/components/customer-side/booking/flow/booking-flow";
import type { Car } from "@/lib/types";

export default function TestTotalCalculationPage() {
  // Mock car data for testing
  const mockCar: Car = {
    id: "test-car-1",
    model: "Toyota Vios 2023",
    type: "Sedan",
    seats: 5,
    transmission: "Manual",
    fuel_type: "Gas/Premium",
    fuel_capacity: 40,
    condition: "Good",
    status: "Available",
    price_per_day: 2500,
    image_url: "/placeholder.svg",
    notes: "",
    is_archived: false,
    plate_number: "ABC-1001",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  };

  // Test scenarios
  const testScenarios = [
    {
      name: "3-Day Rental with Delivery Fees",
      bookingData: {
        selectedCar: mockCar,
        pickUpDate: "2024-01-15",
        dropOffDate: "2024-01-18",
        pickUpTime: "09:00",
        dropOffTime: "17:00",
        pickUpLocation: "Laoag City Airport",
        dropOffLocation: "Laoag City Airport",
        pickupTimePeriod: "day" as const,
        returnTimePeriod: "day" as const,
        deliveryFee: 500,
        returnFee: 500,
        totalDeliveryFees: 1000,
        isSpecialService: false,
      } as Partial<BookingData>,
      expectedDays: 3,
      expectedBaseRental: 7500, // 3 days × ₱2500
      expectedDeliveryFees: 1000,
      expectedTotal: 8500, // ₱7500 + ₱1000
    },
    {
      name: "1-Day Rental with Night Delivery",
      bookingData: {
        selectedCar: mockCar,
        pickUpDate: "2024-01-15",
        dropOffDate: "2024-01-15",
        pickUpTime: "20:00",
        dropOffTime: "21:00",
        pickUpLocation: "Vigan City Center",
        dropOffLocation: "Laoag City Airport",
        pickupTimePeriod: "night" as const,
        returnTimePeriod: "night" as const,
        deliveryFee: 750,
        returnFee: 500,
        totalDeliveryFees: 1250,
        isSpecialService: false,
      } as Partial<BookingData>,
      expectedDays: 1,
      expectedBaseRental: 2500, // 1 day × ₱2500
      expectedDeliveryFees: 1250,
      expectedTotal: 3750, // ₱2500 + ₱1250
    },
    {
      name: "Garage Pickup (Free Delivery)",
      bookingData: {
        selectedCar: mockCar,
        pickUpDate: "2024-01-15",
        dropOffDate: "2024-01-17",
        pickUpTime: "10:00",
        dropOffTime: "16:00",
        pickUpLocation: "#9 Lubnac, Vintar, Ilocos Norte",
        dropOffLocation: "#9 Lubnac, Vintar, Ilocos Norte",
        pickupTimePeriod: "day" as const,
        returnTimePeriod: "day" as const,
        deliveryFee: 0,
        returnFee: 0,
        totalDeliveryFees: 0,
        isSpecialService: false,
      } as Partial<BookingData>,
      expectedDays: 2,
      expectedBaseRental: 5000, // 2 days × ₱2500
      expectedDeliveryFees: 0,
      expectedTotal: 5000, // ₱5000 + ₱0
    },
    {
      name: "Special Service Booking",
      bookingData: {
        selectedCar: null,
        isSpecialService: true,
        pickUpDate: "2024-01-15",
        dropOffDate: "2024-01-15",
        deliveryFee: 0,
        returnFee: 0,
        totalDeliveryFees: 0,
      } as Partial<BookingData>,
      expectedDays: 1,
      expectedBaseRental: 8000, // Special service base price
      expectedDeliveryFees: 0,
      expectedTotal: 8000,
    },
  ];

  const [results, setResults] = React.useState<Array<{
    scenario: string;
    passed: boolean;
    details: string;
    totals: any;
    breakdown: any;
  }>>([]);

  const runTests = () => {
    const testResults = testScenarios.map((scenario) => {
      try {
        const totals = calculateBookingTotal(scenario.bookingData as BookingData, scenario.bookingData.selectedCar || null);
        const breakdown = getBookingCostBreakdown(scenario.bookingData as BookingData, scenario.bookingData.selectedCar || null);
        
        // Validate calculations
        const daysMatch = totals.days === scenario.expectedDays;
        const baseRentalMatch = totals.baseRentalCost === scenario.expectedBaseRental;
        const deliveryFeesMatch = totals.totalDeliveryFees === scenario.expectedDeliveryFees;
        const totalMatch = totals.totalAmount === scenario.expectedTotal;
        
        const passed = daysMatch && baseRentalMatch && deliveryFeesMatch && totalMatch;
        
        let details = "";
        if (!daysMatch) details += `Days: Expected ${scenario.expectedDays}, got ${totals.days}. `;
        if (!baseRentalMatch) details += `Base Rental: Expected ₱${scenario.expectedBaseRental}, got ₱${totals.baseRentalCost}. `;
        if (!deliveryFeesMatch) details += `Delivery Fees: Expected ₱${scenario.expectedDeliveryFees}, got ₱${totals.totalDeliveryFees}. `;
        if (!totalMatch) details += `Total: Expected ₱${scenario.expectedTotal}, got ₱${totals.totalAmount}. `;
        
        if (passed) details = "✅ All calculations correct!";

        return {
          scenario: scenario.name,
          passed,
          details,
          totals,
          breakdown,
        };
      } catch (error) {
        return {
          scenario: scenario.name,
          passed: false,
          details: `Error: ${error instanceof Error ? error.message : "Unknown error"}`,
          totals: null,
          breakdown: null,
        };
      }
    });
    
    setResults(testResults);
  };

  React.useEffect(() => {
    runTests();
  }, []);

  const allTestsPassed = results.length > 0 && results.every(r => r.passed);

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            📊 Booking Total Calculation Test Page
            {results.length > 0 && (
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                allTestsPassed 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {allTestsPassed ? '✅ All Tests Passed' : '❌ Some Tests Failed'}
              </span>
            )}
          </CardTitle>
          <p className="text-gray-600">
            This page tests the centralized booking total calculation utility to ensure accurate pricing across all booking steps.
          </p>
        </CardHeader>
        <CardContent>
          <Button onClick={runTests} className="mb-4">
            🔄 Run Tests Again
          </Button>
          
          {results.length === 0 && (
            <p className="text-gray-500">Running tests...</p>
          )}
        </CardContent>
      </Card>

      <div className="grid gap-6">
        {results.map((result, index) => (
          <Card key={index} className={`border-l-4 ${
            result.passed ? 'border-l-green-500' : 'border-l-red-500'
          }`}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{result.scenario}</span>
                <span className={`text-2xl ${result.passed ? 'text-green-600' : 'text-red-600'}`}>
                  {result.passed ? '✅' : '❌'}
                </span>
              </CardTitle>
              <p className={`text-sm ${result.passed ? 'text-green-700' : 'text-red-700'}`}>
                {result.details}
              </p>
            </CardHeader>
            
            {result.totals && result.breakdown && (
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Raw Totals */}
                  <div>
                    <h4 className="font-semibold mb-3 text-gray-900">Raw Calculation Results</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Days:</span>
                        <span className="font-medium">{result.totals.days}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Base Rental Cost:</span>
                        <span className="font-medium">₱{result.totals.baseRentalCost.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Pickup Delivery Fee:</span>
                        <span className="font-medium">₱{result.totals.pickupDeliveryFee.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Return Delivery Fee:</span>
                        <span className="font-medium">₱{result.totals.returnDeliveryFee.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Delivery Fees:</span>
                        <span className="font-medium">₱{result.totals.totalDeliveryFees.toFixed(2)}</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between font-semibold">
                        <span>Total Amount:</span>
                        <span className="text-blue-600">₱{result.totals.totalAmount.toFixed(2)}</span>
                      </div>
                      {result.totals.error && (
                        <div className="text-red-600 text-xs mt-2">
                          Error: {result.totals.error}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Formatted Breakdown */}
                  <div>
                    <h4 className="font-semibold mb-3 text-gray-900">Formatted Display Values</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Daily Rate:</span>
                        <span className="font-medium">{result.breakdown.dailyRate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Duration:</span>
                        <span className="font-medium">{result.breakdown.daysLabel}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Subtotal:</span>
                        <span className="font-medium">{result.breakdown.subtotal}</span>
                      </div>
                      {result.breakdown.hasDeliveryFees && (
                        <>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Pickup Fee:</span>
                            <span className="font-medium">{result.breakdown.pickupFee}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Return Fee:</span>
                            <span className="font-medium">{result.breakdown.returnFee}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Total Delivery:</span>
                            <span className="font-medium text-orange-600">{result.breakdown.totalDeliveryFees}</span>
                          </div>
                        </>
                      )}
                      <div className="border-t pt-2 flex justify-between font-semibold">
                        <span>Grand Total:</span>
                        <span className="text-blue-600">{result.breakdown.grandTotal}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* Test Summary */}
      <Card className="bg-gray-50">
        <CardHeader>
          <CardTitle>📋 Test Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">{results.length}</div>
              <div className="text-sm text-gray-600">Total Tests</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {results.filter(r => r.passed).length}
              </div>
              <div className="text-sm text-gray-600">Passed</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">
                {results.filter(r => !r.passed).length}
              </div>
              <div className="text-sm text-gray-600">Failed</div>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-white rounded-lg border">
            <h4 className="font-semibold mb-2">✅ What This Test Validates:</h4>
            <ul className="text-sm text-gray-700 space-y-1 list-disc list-inside">
              <li>Day-based rental calculations (pickup/return dates)</li>
              <li>Delivery fee inclusion in total amount</li>
              <li>Special service pricing</li>
              <li>Free delivery for garage/office locations</li>
              <li>Day vs Night delivery fee differences</li>
              <li>Consistency between calculation utility and display formatting</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
