"use client";

import * as React from "react";
import { Palette, Save } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import type { ThemeSettings } from "@/lib/types";

export function ThemeSection({
  settings,
  onUpdate,
  onSave,
  saving,
}: {
  settings: ThemeSettings | null;
  onUpdate: (updates: Partial<ThemeSettings>) => void;
  onSave: () => void;
  saving?: boolean;
}) {
  if (!settings) return null;

  return (
    <Card data-testid="admin-settings-card-theme">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="h-5 w-5" />
          Theme & Appearance
        </CardTitle>
        <p className="text-sm text-gray-600">
          Customize the look and feel of your application
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Color Scheme */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Color Scheme</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="primary_color">Primary Color</Label>
              <div className="relative">
                <Input
                  id="primary_color"
                  value={settings.primary_color}
                  onChange={(e) => onUpdate({ primary_color: e.target.value })}
                  className="pl-10"
                />
                <input
                  type="color"
                  value={settings.primary_color}
                  onChange={(e) => onUpdate({ primary_color: e.target.value })}
                  className="absolute left-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0 border-none cursor-pointer"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="secondary_color">Secondary Color</Label>
              <div className="relative">
                <Input
                  id="secondary_color"
                  value={settings.secondary_color}
                  onChange={(e) => onUpdate({ secondary_color: e.target.value })}
                  className="pl-10"
                />
                <input
                  type="color"
                  value={settings.secondary_color}
                  onChange={(e) =>
                    onUpdate({ secondary_color: e.target.value })
                  }
                  className="absolute left-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0 border-none cursor-pointer"
                />
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Dark Mode */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Display Options</h3>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="dark_mode">Dark Mode</Label>
              <p className="text-sm text-gray-500">Enable dark mode for the admin interface</p>
            </div>
            <input
              id="dark_mode"
              type="checkbox"
              checked={settings.dark_mode_enabled}
              onChange={(e) => onUpdate({ dark_mode_enabled: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
        </div>


        <div className="flex justify-end pt-4">
          <Button
            onClick={onSave}
            disabled={saving}
            className="px-6 bg-blue-600 hover:bg-blue-700"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
