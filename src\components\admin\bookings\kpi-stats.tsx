"use client"

import * as React from "react"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"
import { KPIStatsDrawer } from "./kpi-stats-drawer"

interface KPIStatsProps {
  stats: {
    today: number
    thisWeek: number
    pending: number
    active: number
    completed: number
    cancelled: number
  }
  className?: string
}

export function KPIStats({ stats, className }: KPIStatsProps) {
  const isMobile = useIsMobile()
  const kpiItems = [
    { 
      key: 'today', 
      label: 'Today', 
      value: stats.today, 
      bgColor: 'bg-gradient-to-br from-slate-50 to-slate-100', 
      textColor: 'text-slate-900',
      borderColor: 'border-slate-400',
      icon: '📅'
    },
    { 
      key: 'thisWeek', 
      label: 'This Week', 
      value: stats.thisWeek, 
      bgColor: 'bg-gradient-to-br from-slate-50 to-slate-100', 
      textColor: 'text-slate-900',
      borderColor: 'border-slate-400',
      icon: '📊'
    },
    { 
      key: 'pending', 
      label: 'Pending', 
      value: stats.pending, 
      bgColor: 'bg-gradient-to-br from-amber-50 to-amber-100', 
      textColor: 'text-amber-900',
      borderColor: 'border-amber-400',
      icon: '⏳'
    },
    { 
      key: 'active', 
      label: 'Active', 
      value: stats.active, 
      bgColor: 'bg-gradient-to-br from-green-50 to-green-100', 
      textColor: 'text-green-900',
      borderColor: 'border-green-400',
      icon: '🚗'
    },
    { 
      key: 'completed', 
      label: 'Completed', 
      value: stats.completed, 
      bgColor: 'bg-gradient-to-br from-blue-50 to-blue-100', 
      textColor: 'text-blue-900',
      borderColor: 'border-blue-400',
      icon: '✅'
    },
    { 
      key: 'cancelled', 
      label: 'Cancelled', 
      value: stats.cancelled, 
      bgColor: 'bg-gradient-to-br from-red-50 to-red-100', 
      textColor: 'text-red-900',
      borderColor: 'border-red-400',
      icon: '❌'
    },
  ]

  // KPI Stats are now purely informational - no filter actions

  // For mobile and tablet screens, show the unified button that opens a drawer
  if (isMobile) {
    return (
      <KPIStatsDrawer 
        stats={stats}
        className={className}
      />
    )
  }

  // For desktop screens, show the original grid layout
  return (
    <div className={cn("grid grid-cols-2 lg:grid-cols-6 gap-3", className)}>
      {kpiItems.map((item) => {
        return (
          <div
            key={item.key}
            className={cn(
              // Pure display card styling - no interactive states
              "px-4 py-3 rounded-xl border-2 font-semibold text-sm shadow-lg",
              "lg:px-4 lg:py-3 lg:rounded-xl lg:border-2 lg:font-semibold lg:text-sm lg:shadow-md",
              // Original themed colors for each metric
              item.bgColor,
              item.textColor, 
              item.borderColor
            )}
          >
            <div className="flex items-center gap-2">
              <span className="text-lg lg:text-base opacity-60">
                {item.icon}
              </span>
              <div className="text-left">
                <div className="text-xs font-medium opacity-80 lg:text-gray-600 lg:font-normal">
                  {item.label}
                </div>
                <div className="text-2xl font-bold lg:text-lg lg:font-semibold lg:text-gray-900">
                  {item.value}
                </div>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
