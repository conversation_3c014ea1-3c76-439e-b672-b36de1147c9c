"use client"

import React from 'react'
import { cn } from "@/lib/utils"
import { Button, ButtonProps } from "@/components/ui/button"
import { Loader2 } from "lucide-react"

interface CustomerLoadingButtonProps extends ButtonProps {
  isLoading?: boolean
  loadingText?: string
  icon?: React.ComponentType<{ className?: string }>
  preventDoubleClick?: boolean
  minimumLoadingDuration?: number
}

export function CustomerLoadingButton({
  children,
  isLoading = false,
  loadingText,
  icon: Icon,
  preventDoubleClick = true,
  minimumLoadingDuration = 300,
  disabled,
  onClick,
  className,
  ...props
}: CustomerLoadingButtonProps) {
  const [internalLoading, setInternalLoading] = React.useState(false)
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null)
  const startTimeRef = React.useRef<number | null>(null)

  const isButtonLoading = isLoading || internalLoading
  const isButtonDisabled = disabled || isButtonLoading

  const handleClick = React.useCallback(async (event: React.MouseEvent<HTMLButtonElement>) => {
    if (isButtonDisabled || !onClick) return

    if (preventDoubleClick) {
      setInternalLoading(true)
      startTimeRef.current = Date.now()
    }

    try {
      await onClick(event)
    } finally {
      if (preventDoubleClick) {
        const elapsed = startTimeRef.current ? Date.now() - startTimeRef.current : 0
        const remainingTime = Math.max(0, minimumLoadingDuration - elapsed)
        
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
        }
        
        timeoutRef.current = setTimeout(() => {
          setInternalLoading(false)
        }, remainingTime)
      }
    }
  }, [onClick, isButtonDisabled, preventDoubleClick, minimumLoadingDuration])

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  // Respect reduced motion preference
  const prefersReducedMotion = React.useMemo(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-reduced-motion: reduce)').matches
    }
    return false
  }, [])

  return (
    <Button
      {...props}
      disabled={isButtonDisabled}
      onClick={handleClick}
      className={cn(
        // Ensure button maintains width when loading
        "relative min-w-fit",
        className
      )}
      aria-busy={isButtonLoading}
      data-testid="customer-loading-button"
    >
      <span className={cn(
        "flex items-center gap-2",
        isButtonLoading && "opacity-0"
      )}>
        {Icon && <Icon className="w-4 h-4" />}
        {children}
      </span>
      
      {isButtonLoading && (
        <span className="absolute inset-0 flex items-center justify-center gap-2">
          <Loader2 
            className={cn(
              "w-4 h-4",
              prefersReducedMotion ? "" : "animate-spin"
            )} 
            aria-hidden="true"
          />
          <span className="sr-only">
            {isLoading ? "Processing request" : "Please wait"}. 
            Button is temporarily disabled.
          </span>
          {loadingText && <span>{loadingText}</span>}
        </span>
      )}
    </Button>
  )
}

// Enhanced input with loading state
interface CustomerLoadingInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  isLoading?: boolean
  error?: string
  success?: boolean
}

export function CustomerLoadingInput({
  isLoading = false,
  error,
  success,
  disabled,
  className,
  ...props
}: CustomerLoadingInputProps) {
  return (
    <div className="relative">
      <input
        {...props}
        disabled={disabled || isLoading}
        className={cn(
          "w-full px-3 py-2 border rounded-md",
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
          "disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed",
          error && "border-red-500 focus:ring-red-500",
          success && "border-green-500 focus:ring-green-500",
          isLoading && "pr-10",
          className
        )}
        aria-busy={isLoading}
        aria-invalid={!!error}
        aria-describedby={error ? `${props.id}-error` : undefined}
      />
      
      {isLoading && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <Loader2 className="w-4 h-4 animate-spin text-gray-400" aria-hidden="true" />
          <span className="sr-only">Loading</span>
        </div>
      )}
      
      {error && (
        <p id={`${props.id}-error`} className="mt-1 text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </div>
  )
}

// Progress indicator with timeout warning
interface CustomerProgressIndicatorProps {
  progress?: number
  isVisible: boolean
  showTimeout?: boolean
  timeoutDuration?: number
  onTimeout?: () => void
  className?: string
}

export function CustomerProgressIndicator({
  progress,
  isVisible,
  showTimeout = true,
  timeoutDuration = 10000, // 10 seconds
  onTimeout,
  className
}: CustomerProgressIndicatorProps) {
  const [timeRemaining, setTimeRemaining] = React.useState(timeoutDuration)
  const [showWarning, setShowWarning] = React.useState(false)
  
  React.useEffect(() => {
    if (!isVisible || !showTimeout) return

    const interval = setInterval(() => {
      setTimeRemaining(prev => {
        const newTime = prev - 100
        
        if (newTime <= 3000 && !showWarning) {
          setShowWarning(true)
        }
        
        if (newTime <= 0) {
          clearInterval(interval)
          onTimeout?.()
          return 0
        }
        
        return newTime
      })
    }, 100)

    return () => clearInterval(interval)
  }, [isVisible, showTimeout, onTimeout, showWarning])

  React.useEffect(() => {
    if (isVisible) {
      setTimeRemaining(timeoutDuration)
      setShowWarning(false)
    }
  }, [isVisible, timeoutDuration])

  if (!isVisible) return null

  return (
    <div 
      className={cn("space-y-2", className)}
      role="status"
      aria-live="polite"
      data-testid="customer-progress-indicator"
    >
      {progress !== undefined && (
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            role="progressbar"
            aria-valuenow={progress}
            aria-valuemin={0}
            aria-valuemax={100}
          />
        </div>
      )}
      
      {showWarning && timeRemaining > 0 && (
        <div 
          className="text-sm text-orange-600 bg-orange-50 p-2 rounded"
          role="alert"
          aria-live="assertive"
        >
          This is taking longer than expected... {Math.ceil(timeRemaining / 1000)}s remaining
        </div>
      )}
    </div>
  )
}
