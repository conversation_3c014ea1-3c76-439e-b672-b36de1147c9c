import { PublicAppShell } from "@/components/layout/public-app-shell"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"

export default function CustomerLoginLoading() {
  return (
    <PublicAppShell>
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-3 text-center">
            <div className="h-12 w-12 mx-auto bg-primary/10 rounded-full animate-pulse" />
            <div className="space-y-2">
              <div className="h-6 w-32 bg-gradient-to-r from-gray-200 to-gray-300 rounded mx-auto animate-pulse" />
              <div className="h-4 w-48 bg-gradient-to-r from-gray-200 to-gray-300 rounded mx-auto animate-pulse" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="pt-4">
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
            
            {/* Divider */}
            <div className="flex items-center gap-4 my-6">
              <div className="flex-1 h-px bg-gradient-to-r from-gray-200 to-gray-300 animate-pulse" />
              <div className="h-4 w-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse" />
              <div className="flex-1 h-px bg-gradient-to-r from-gray-200 to-gray-300 animate-pulse" />
            </div>
            
            {/* Additional links */}
            <div className="space-y-3">
              <div className="h-4 w-40 bg-gradient-to-r from-gray-200 to-gray-300 rounded mx-auto animate-pulse" />
              <div className="h-4 w-32 bg-gradient-to-r from-gray-200 to-gray-300 rounded mx-auto animate-pulse" />
            </div>
          </CardContent>
        </Card>
      </div>
    </PublicAppShell>
  )
}
