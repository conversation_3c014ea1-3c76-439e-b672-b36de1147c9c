"use server";

import { createContextClient } from "@/lib/supabase/server";

export interface AdminPayment {
  id: string;
  paymentRef: string;
  bookingId: string;
  bookingRef: string;
  renterName: string;
  renterEmail: string;
  renterPhone: string;
  amount: number;
  method: string;
  status: string;
  transactionDate: string;
  proofOfPaymentUrl?: string;
  verificationNotes?: string;
}

export async function getAdminPayments(): Promise<{
  data: AdminPayment[] | null;
  error: any;
}> {
  try {
    const supabase = await createContextClient('admin');

    // Check admin authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { data: null, error: { message: "Unauthorized" } };
    }

    // Get user profile to verify admin role
    const { data: profile } = await supabase
      .from("profiles")
      .select("role")
      .eq("id", user.id)
      .single();

    if (!profile || (profile.role !== "admin" && profile.role !== "super_admin")) {
      return { data: null, error: { message: "Admin access required" } };
    }

    // Fetch payments with booking and customer details
    const { data: paymentsData, error } = await supabase
      .from("payments")
      .select(`
        id,
        payment_ref,
        booking_id,
        amount,
        method,
        status,
        transaction_date,
        proof_of_payment_url,
        verification_notes,
        bookings (
          id,
          booking_ref,
          profiles (
            full_name,
            first_name,
            middle_initial,
            last_name,
            email,
            phone
          )
        )
      `)
      .order("transaction_date", { ascending: false });

    if (error) {
      console.error("Error fetching admin payments:", error);
      return { data: null, error };
    }

    if (!paymentsData) {
      return { data: [], error: null };
    }

    // Fetch proof of payment documents for bookings that have payments
    const bookingIds = paymentsData.map((payment: any) => payment.booking_id);
    const { data: proofDocuments } = await supabase
      .from("booking_documents")
      .select("booking_id, file_url, verification_status")
      .in("booking_id", bookingIds)
      .eq("document_type", "proof_of_payment");

    // Create a map of booking_id to proof document URL for quick lookup
    const proofDocumentMap = new Map<string, string>();
    if (proofDocuments) {
      proofDocuments.forEach((doc: any) => {
        proofDocumentMap.set(doc.booking_id, doc.file_url);
      });
    }

    // Transform data to match AdminPayment interface
    const adminPayments: AdminPayment[] = paymentsData.map((payment: any) => {
      const profile = payment.bookings?.profiles;
      
      // Build customer name from available fields
      let customerName = "Unknown Customer";
      if (profile) {
        if (profile.full_name && profile.full_name.trim()) {
          customerName = profile.full_name.trim();
        } else if (profile.first_name || profile.last_name) {
          // Construct name from separate fields
          const parts = [];
          if (profile.first_name) parts.push(profile.first_name.trim());
          if (profile.middle_initial) parts.push(profile.middle_initial.trim());
          if (profile.last_name) parts.push(profile.last_name.trim());
          customerName = parts.join(" ");
        } else if (profile.email) {
          customerName = profile.email;
        }
      }

      // Get proof of payment URL from either payments table or booking_documents table
      const proofUrl = payment.proof_of_payment_url || proofDocumentMap.get(payment.booking_id) || null;

      return {
        id: payment.id,
        paymentRef: payment.payment_ref || `PMT-${new Date(payment.transaction_date).toISOString().slice(0, 10).replace(/-/g, '')}-${payment.id.slice(-4)}`,
        bookingId: payment.booking_id,
        bookingRef: payment.bookings?.booking_ref || `BKG-${new Date(payment.transaction_date).toISOString().slice(0, 10).replace(/-/g, '')}-${payment.booking_id.slice(-4)}`,
        renterName: customerName,
        renterEmail: profile?.email || "N/A",
        renterPhone: profile?.phone || "N/A",
        amount: payment.amount || 0,
        method: payment.method || "Unknown",
        status: payment.status || "Unknown",
        transactionDate: payment.transaction_date,
        proofOfPaymentUrl: proofUrl,
        verificationNotes: payment.verification_notes,
      };
    });

    return { data: adminPayments, error: null };

  } catch (error) {
    console.error("Unexpected error in getAdminPayments:", error);
    return { data: null, error: { message: "Failed to fetch payments" } };
  }
}
