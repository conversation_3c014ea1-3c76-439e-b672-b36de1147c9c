-- Verify the exact data that should be returned to frontend
-- This mimics what the customer dashboard queries should return

-- Check what customer payment data looks like
SELECT 
    p.id as payment_id,
    p.payment_ref,
    p.booking_id,
    p.amount,
    p.method,
    p.status,
    p.transaction_date,
    b.id as booking_id_from_join,
    b.booking_ref,
    b.customer_id
FROM payments p
LEFT JOIN bookings b ON p.booking_id = b.id
ORDER BY p.created_at DESC
LIMIT 5;

-- Check what admin payments query should return (mimics getAdminPayments)
SELECT 
    p.id,
    p.payment_ref,
    p.booking_id,
    p.amount,
    p.method,
    p.status,
    p.transaction_date,
    b.booking_ref,
    prof.full_name,
    prof.email
FROM payments p
LEFT JOIN bookings b ON p.booking_id = b.id  
LEFT JOIN profiles prof ON b.customer_id = prof.id
ORDER BY p.transaction_date DESC;
