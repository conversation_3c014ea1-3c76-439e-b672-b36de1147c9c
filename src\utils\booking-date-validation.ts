/**
 * Booking Date Validation Utilities
 * 
 * This module provides utilities for enforcing day-only rental calculations
 * and proper date validation in customer booking forms.
 * 
 * Key Rules:
 * - Rentals are charged per day only (not hourly)
 * - Time picker is for pickup/delivery scheduling only
 * - Drop-off date must be >= pickup date
 * - Minimum rental period is 1 day
 */

export interface RentalPeriod {
  days: number;
  isValid: boolean;
  error?: string;
}

export interface DateTimeValidation {
  isValid: boolean;
  error?: string;
}

/**
 * Calculate rental period in full days only
 * Time is ignored for rental duration calculation
 */
export function calculateDayOnlyRental(
  pickupDate: string,
  dropoffDate: string
): RentalPeriod {
  if (!pickupDate || !dropoffDate) {
    return {
      days: 1,
      isValid: false,
      error: "Both pickup and drop-off dates are required"
    };
  }

  try {
    // Parse dates without time to focus on date-only calculation
    const pickup = new Date(pickupDate);
    const dropoff = new Date(dropoffDate);
    
    // Reset time to start of day for accurate date comparison
    pickup.setHours(0, 0, 0, 0);
    dropoff.setHours(0, 0, 0, 0);
    
    const diffTime = dropoff.getTime() - pickup.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return {
        days: 1,
        isValid: false,
        error: "Drop-off date cannot be before pickup date"
      };
    }
    
    // Minimum rental period is 1 day
    const rentalDays = Math.max(1, diffDays + 1); // +1 because same day = 1 day rental
    
    return {
      days: rentalDays,
      isValid: true
    };
  } catch (error) {
    return {
      days: 1,
      isValid: false,
      error: "Invalid date format"
    };
  }
}

/**
 * Format rental duration for display (day-only)
 */
export function formatRentalDuration(days: number): string {
  if (days === 1) {
    return "1 day";
  }
  return `${days} days`;
}

/**
 * Validate date and time inputs
 */
export function validateBookingDateTime(
  pickupDate: string,
  pickupTime: string,
  dropoffDate: string,
  dropoffTime: string
): DateTimeValidation {
  // Validate required fields
  if (!pickupDate) {
    return { isValid: false, error: "Pickup date is required" };
  }
  
  if (!dropoffDate) {
    return { isValid: false, error: "Drop-off date is required" };
  }
  
  if (!pickupTime) {
    return { isValid: false, error: "Pickup time is required for scheduling" };
  }
  
  if (!dropoffTime) {
    return { isValid: false, error: "Drop-off time is required for scheduling" };
  }
  
  // Validate date order
  try {
    const pickup = new Date(pickupDate);
    const dropoff = new Date(dropoffDate);
    
    pickup.setHours(0, 0, 0, 0);
    dropoff.setHours(0, 0, 0, 0);
    
    if (dropoff < pickup) {
      return { 
        isValid: false, 
        error: "Drop-off date cannot be before pickup date" 
      };
    }
    
    // Validate dates are not in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (pickup < today) {
      return { 
        isValid: false, 
        error: "Pickup date cannot be in the past" 
      };
    }
    
    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: "Invalid date format" };
  }
}

/**
 * Get minimum date for date pickers (today)
 */
export function getMinimumDate(): string {
  const today = new Date();
  return today.toISOString().split("T")[0];
}

/**
 * Get minimum drop-off date based on pickup date
 */
export function getMinimumDropoffDate(pickupDate: string): string {
  if (!pickupDate) {
    return getMinimumDate();
  }
  
  try {
    const pickup = new Date(pickupDate);
    return pickup.toISOString().split("T")[0];
  } catch {
    return getMinimumDate();
  }
}

/**
 * Calculate total rental cost for day-based billing
 */
export function calculateDayBasedCost(
  pickupDate: string,
  dropoffDate: string,
  dailyRate: number
): { days: number; totalCost: number; isValid: boolean; error?: string } {
  const rental = calculateDayOnlyRental(pickupDate, dropoffDate);
  
  if (!rental.isValid) {
    return {
      days: 1,
      totalCost: dailyRate,
      isValid: false,
      error: rental.error
    };
  }
  
  return {
    days: rental.days,
    totalCost: rental.days * dailyRate,
    isValid: true
  };
}

/**
 * Format date range for display (e.g., "Sept 10–12, 2025")
 */
export function formatDateRange(
  pickupDate: string,
  dropoffDate: string
): string {
  if (!pickupDate || !dropoffDate) {
    return "";
  }

  try {
    const pickup = new Date(pickupDate);
    const dropoff = new Date(dropoffDate);
    
    const options: Intl.DateTimeFormatOptions = {
      month: "short",
      day: "numeric",
      year: "numeric",
    };
    
    // If same year and month, show abbreviated format
    if (pickup.getFullYear() === dropoff.getFullYear() && 
        pickup.getMonth() === dropoff.getMonth()) {
      const monthYear = pickup.toLocaleDateString("en-US", {
        month: "short",
        year: "numeric",
      });
      
      // If same day, just show single date
      if (pickup.getDate() === dropoff.getDate()) {
        return pickup.toLocaleDateString("en-US", options);
      }
      
      // Same month, different days: "Sept 10–12, 2025"
      return `${pickup.toLocaleDateString("en-US", { month: "short", day: "numeric" })}–${dropoff.getDate()}, ${pickup.getFullYear()}`;
    }
    
    // Different months or years: "Sept 30 – Oct 2, 2025"
    const pickupFormatted = pickup.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    
    const dropoffFormatted = dropoff.toLocaleDateString("en-US", options);
    
    return `${pickupFormatted} – ${dropoffFormatted}`;
  } catch (error) {
    return "";
  }
}

/**
 * Format enhanced rental duration with date range
 * Example: "3 Days (Sept 10–12, 2025)"
 */
export function formatRentalDurationWithDateRange(
  pickupDate: string,
  dropoffDate: string
): string {
  const rental = calculateDayOnlyRental(pickupDate, dropoffDate);
  
  if (!rental.isValid || !pickupDate || !dropoffDate) {
    return formatRentalDuration(rental.days);
  }
  
  const dateRange = formatDateRange(pickupDate, dropoffDate);
  const duration = formatRentalDuration(rental.days);
  
  if (dateRange) {
    return `${duration} (${dateRange})`;
  }
  
  return duration;
}

/**
 * Generate user-friendly display text for day-only rentals
 */
export function generateRentalDisplayText(
  pickupDate: string,
  dropoffDate: string
): string {
  const rental = calculateDayOnlyRental(pickupDate, dropoffDate);
  
  if (!rental.isValid) {
    return "Please select valid dates";
  }
  
  return `${formatRentalDuration(rental.days)} rental period`;
}
