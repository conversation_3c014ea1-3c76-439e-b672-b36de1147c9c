"use client"

import { useState, useEffect } from 'react'
import { useAdminAuth } from '@/components/auth/admin-auth-context'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useToast } from '@/hooks/use-toast'
import { Trash2, UserPlus, Shield, User, <PERSON>, Eye, EyeOff, Plus } from 'lucide-react'
import { redirect } from 'next/navigation'

type AccountProfile = {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: 'customer' | 'admin' | 'super_admin'
  avatar_url: string | null
  created_at: string
  updated_at: string
}

export default function AccountsPage() {
  const { isSuperAdmin, loading } = useAdminAuth()
  const [adminAccounts, setAdminAccounts] = useState<AccountProfile[]>([])
  const [customerAccounts, setCustomerAccounts] = useState<AccountProfile[]>([])
  const [loadingAccounts, setLoadingAccounts] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [newAccount, setNewAccount] = useState({
    firstName: '',
    middleInitial: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    role: 'admin' as 'admin',
    pageAccess: {
      allAccess: true,
      dashboard: true,
      fleetManagement: true,
      bookings: true,
      payments: true,
      salesTracking: true,
      accounts: false // Only super admin has accounts access
    }
  })
  const { toast } = useToast()
  const supabase = createClient()

  // Redirect if not super admin
  useEffect(() => {
    if (!loading && !isSuperAdmin) {
      redirect('/admin')
    }
  }, [isSuperAdmin, loading])

  // Fetch all accounts
  const fetchAccounts = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error

      const admins = data.filter(profile => profile.role === 'admin' || profile.role === 'super_admin')
      const customers = data.filter(profile => profile.role === 'customer')

      setAdminAccounts(admins)
      setCustomerAccounts(customers)
    } catch (error) {
      console.error('Error fetching accounts:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch accounts"
      })
    } finally {
      setLoadingAccounts(false)
    }
  }

  useEffect(() => {
    if (isSuperAdmin) {
      fetchAccounts()
    }
  }, [isSuperAdmin])

  const createAccount = async () => {
    try {
      // Build full name from parts
      const fullName = [
        newAccount.firstName.trim(),
        newAccount.middleInitial.trim(),
        newAccount.lastName.trim()
      ].filter(Boolean).join(' ')

      // Call API route to create user (uses service role key)
      const response = await fetch('/api/admin/create-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: newAccount.email,
          password: newAccount.password,
          full_name: fullName || null,
          phone: newAccount.phone || null,
          role: newAccount.role
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create account')
      }

      toast({
        title: "Success",
        description: `Admin account created successfully. Welcome email sent to ${newAccount.email}`
      })

      setShowCreateDialog(false)
      setNewAccount({
        firstName: '',
        middleInitial: '',
        lastName: '',
        email: '',
        phone: '',
        password: '',
        role: 'admin',
        pageAccess: {
          allAccess: true,
          dashboard: true,
          fleetManagement: true,
          bookings: true,
          payments: true,
          salesTracking: true,
          accounts: false
        }
      })
      fetchAccounts()
    } catch (error: any) {
      console.error('Error creating account:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to create account"
      })
    }
  }

  const deleteAccount = async (accountId: string, email: string) => {
    if (email === '<EMAIL>') {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Cannot delete super admin account"
      })
      return
    }

    if (!confirm('Are you sure you want to delete this account? This action cannot be undone.')) {
      return
    }

    try {
      // Delete from profiles table (auth user will be cascade deleted)
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', accountId)

      if (error) throw error

      toast({
        title: "Success",
        description: "Account deleted successfully"
      })
      fetchAccounts()
    } catch (error: any) {
      console.error('Error deleting account:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to delete account"
      })
    }
  }


  if (loading || loadingAccounts) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isSuperAdmin) {
    return null
  }

  return (
    <div className="space-y-3 xs:space-y-4 sm:space-y-6 p-1 xs:p-2 sm:p-4 lg:p-6 max-w-full overflow-hidden">
      <div className="flex flex-col xs:flex-row items-start xs:items-center justify-between gap-4 xs:gap-2">
        <div>
          <h1 className="text-lg xs:text-xl sm:text-2xl lg:text-3xl font-bold tracking-tight text-foreground">Account Management</h1>
          <p className="text-sm xs:text-sm sm:text-base lg:text-lg text-muted-foreground mt-1 xs:mt-2">Manage admin accounts and view customer accounts</p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="text-sm xs:text-sm sm:text-base lg:text-lg py-2 xs:py-2 sm:py-2.5 h-9 xs:h-9 sm:h-10 lg:h-11 px-4 xs:px-4 sm:px-5">
              <Plus className="h-4 w-4 xs:h-4 xs:w-4 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-2 xs:mr-2 sm:mr-2" />
              Create Account
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Admin Account</DialogTitle>
              <DialogDescription>
                Create a new admin account with access to all admin dashboard features
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-3 xs:space-y-4">
              <div className="grid grid-cols-1 xs:grid-cols-3 gap-2 xs:gap-3 sm:gap-4">
                <div>
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    value={newAccount.firstName}
                    onChange={(e) => setNewAccount(prev => ({ ...prev, firstName: e.target.value }))}
                    placeholder="First name"
                  />
                </div>
                <div>
                  <Label htmlFor="middleInitial">Middle Initial</Label>
                  <Input
                    id="middleInitial"
                    value={newAccount.middleInitial}
                    onChange={(e) => setNewAccount(prev => ({ ...prev, middleInitial: e.target.value }))}
                    placeholder="M.I. (Optional)"
                    maxLength={2}
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    value={newAccount.lastName}
                    onChange={(e) => setNewAccount(prev => ({ ...prev, lastName: e.target.value }))}
                    placeholder="Last name"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={newAccount.email}
                  onChange={(e) => setNewAccount(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="Enter email address"
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={newAccount.phone}
                  onChange={(e) => setNewAccount(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="09XX XXX XXXX (optional)"
                  maxLength={11}
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={newAccount.password}
                    onChange={(e) => setNewAccount(prev => ({ ...prev, password: e.target.value }))}
                    placeholder="Enter your password"
                    className="pr-10"
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground cursor-pointer"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>
              <div>
                <Label htmlFor="role">Role</Label>
                <div className="p-2 xs:p-3 bg-muted rounded-md">
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4 text-blue-600" />
                    <span className="font-medium">Admin</span>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    Configure specific page access permissions below
                  </p>
                </div>
              </div>
              <div>
                <Label>Sidebar Page Access</Label>
                <div className="space-y-2 xs:space-y-3 mt-1 xs:mt-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="allAccess"
                      checked={newAccount.pageAccess.allAccess}
                      onCheckedChange={(checked) => {
                        const allChecked = checked === true
                        setNewAccount(prev => ({
                          ...prev,
                          pageAccess: {
                            allAccess: allChecked,
                            dashboard: allChecked,
                            fleetManagement: allChecked,
                            bookings: allChecked,
                            payments: allChecked,
                            salesTracking: allChecked,
                            accounts: false // Only super admin
                          }
                        }))
                      }}
                    />
                    <Label htmlFor="allAccess" className="font-medium">All Access</Label>
                  </div>
                  <div className="grid grid-cols-1 xs:grid-cols-2 gap-2 ml-6">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="dashboard"
                        checked={newAccount.pageAccess.dashboard}
                        onCheckedChange={(checked) => {
                          setNewAccount(prev => ({
                            ...prev,
                            pageAccess: {
                              ...prev.pageAccess,
                              dashboard: checked === true,
                              allAccess: false
                            }
                          }))
                        }}
                      />
                      <Label htmlFor="dashboard" className="text-sm">Dashboard</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="fleetManagement"
                        checked={newAccount.pageAccess.fleetManagement}
                        onCheckedChange={(checked) => {
                          setNewAccount(prev => ({
                            ...prev,
                            pageAccess: {
                              ...prev.pageAccess,
                              fleetManagement: checked === true,
                              allAccess: false
                            }
                          }))
                        }}
                      />
                      <Label htmlFor="fleetManagement" className="text-sm">Fleet Management</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="bookings"
                        checked={newAccount.pageAccess.bookings}
                        onCheckedChange={(checked) => {
                          setNewAccount(prev => ({
                            ...prev,
                            pageAccess: {
                              ...prev.pageAccess,
                              bookings: checked === true,
                              allAccess: false
                            }
                          }))
                        }}
                      />
                      <Label htmlFor="bookings" className="text-sm">Bookings</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="payments"
                        checked={newAccount.pageAccess.payments}
                        onCheckedChange={(checked) => {
                          setNewAccount(prev => ({
                            ...prev,
                            pageAccess: {
                              ...prev.pageAccess,
                              payments: checked === true,
                              allAccess: false
                            }
                          }))
                        }}
                      />
                      <Label htmlFor="payments" className="text-sm">Payments</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="salesTracking"
                        checked={newAccount.pageAccess.salesTracking}
                        onCheckedChange={(checked) => {
                          setNewAccount(prev => ({
                            ...prev,
                            pageAccess: {
                              ...prev.pageAccess,
                              salesTracking: checked === true,
                              allAccess: false
                            }
                          }))
                        }}
                      />
                      <Label htmlFor="salesTracking" className="text-sm">Sales Tracking</Label>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-end">
                <Button
                  onClick={createAccount}
                  className="w-full text-xs xs:text-sm py-1.5 xs:py-2 h-8 xs:h-9"
                  disabled={!newAccount.email || !newAccount.password}
                >
                  Create Account
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="admins" className="space-y-3 xs:space-y-4">
        <TabsList className="w-full sm:w-auto px-1 py-1 xs:px-1 xs:py-1 sm:px-1.5 sm:py-1.5">
          <TabsTrigger 
            value="admins" 
            className="text-sm xs:text-sm sm:text-base lg:text-lg py-2 xs:py-2 sm:py-2.5 lg:py-3 px-3 xs:px-3 sm:px-4 lg:px-5 data-[state=active]:bg-blue-700 data-[state=active]:text-white data-[state=active]:border-blue-600 data-[state=active]:shadow-lg dark:data-[state=active]:bg-blue-600 dark:data-[state=active]:text-white dark:data-[state=active]:border-blue-500 font-semibold transition-all duration-200 hover:bg-blue-100 dark:hover:bg-blue-900/20"
          >
            <Shield className="h-4 w-4 xs:h-4 xs:w-4 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-2 xs:mr-2 sm:mr-2" />
            Admin ({adminAccounts.length})
          </TabsTrigger>
          <TabsTrigger 
            value="customers" 
            className="text-sm xs:text-sm sm:text-base lg:text-lg py-2 xs:py-2 sm:py-2.5 lg:py-3 px-3 xs:px-3 sm:px-4 lg:px-5 data-[state=active]:bg-green-700 data-[state=active]:text-white data-[state=active]:border-green-600 data-[state=active]:shadow-lg dark:data-[state=active]:bg-green-600 dark:data-[state=active]:text-white dark:data-[state=active]:border-green-500 font-semibold transition-all duration-200 hover:bg-green-100 dark:hover:bg-green-900/20"
          >
            <Users className="h-4 w-4 xs:h-4 xs:w-4 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-2 xs:mr-2 sm:mr-2" />
            Customer ({customerAccounts.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="admins" className="space-y-2 xs:space-y-3 sm:space-y-4">
          <div className="grid gap-2 xs:gap-3 sm:gap-4">
            {adminAccounts.map((account) => (
              <Card key={account.id}>
                <CardHeader className="pb-2 xs:pb-2 sm:pb-3 lg:pb-3 p-3 xs:p-3 sm:p-4 lg:p-4">
                  <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between gap-2 xs:gap-3">
                    <div className="flex items-center space-x-2 xs:space-x-3 sm:space-x-3">
                      <div className="w-8 h-8 xs:w-9 xs:h-9 sm:w-10 sm:h-10 lg:w-12 lg:h-12 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold text-xs xs:text-sm sm:text-base lg:text-lg">
                        {account.email.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <CardTitle className="text-sm xs:text-sm sm:text-base lg:text-lg">{account.full_name || account.email}</CardTitle>
                        <CardDescription className="text-xs xs:text-xs sm:text-sm lg:text-base truncate max-w-[150px] xs:max-w-none mt-1">{account.email}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 xs:space-x-2 self-end xs:self-auto">
                      <Badge variant={account.role === 'super_admin' ? 'default' : 'secondary'} className="text-xs xs:text-xs sm:text-sm lg:text-base whitespace-nowrap px-2 xs:px-2 sm:px-3 py-1 xs:py-1">
                        {account.role === 'super_admin' ? 'Super Admin' : 'Admin'}
                      </Badge>
                      {account.email !== '<EMAIL>' && (
                        <Button
                          variant="secondary"
                          size="icon"
                          className="h-7 w-7 xs:h-8 xs:w-8 sm:h-8 sm:w-8 lg:h-9 lg:w-9"
                          onClick={() => deleteAccount(account.id, account.email)}
                        >
                          <Trash2 className="h-3 w-3 xs:h-4 xs:w-4 sm:h-4 sm:w-4 lg:h-4 lg:w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-3 xs:p-3 sm:p-4 lg:p-4 pt-0">
                  <div className="text-xs xs:text-xs sm:text-sm lg:text-base text-muted-foreground space-y-1 xs:space-y-1">
                    <p>Phone: {account.phone || 'N/A'}</p>
                    <p>Created: {new Date(account.created_at).toLocaleDateString()}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="customers" className="space-y-2 xs:space-y-3 sm:space-y-4">
          <div className="grid gap-2 xs:gap-3 sm:gap-4">
            {customerAccounts.map((account) => (
              <Card key={account.id}>
                <CardHeader className="pb-2 xs:pb-2 sm:pb-3 lg:pb-3 p-3 xs:p-3 sm:p-4 lg:p-4">
                  <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between gap-2 xs:gap-3">
                    <div className="flex items-center space-x-2 xs:space-x-3 sm:space-x-3">
                      <div className="w-8 h-8 xs:w-9 xs:h-9 sm:w-10 sm:h-10 lg:w-12 lg:h-12 rounded-full bg-green-600 flex items-center justify-center text-white font-semibold text-xs xs:text-sm sm:text-base lg:text-lg">
                        {account.email.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <CardTitle className="text-sm xs:text-sm sm:text-base lg:text-lg">{account.full_name || account.email}</CardTitle>
                        <CardDescription className="text-xs xs:text-xs sm:text-sm lg:text-base truncate max-w-[150px] xs:max-w-none mt-1">{account.email}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 xs:space-x-2 self-end xs:self-auto">
                      <Badge variant="secondary" className="text-xs xs:text-xs sm:text-sm lg:text-base whitespace-nowrap px-2 xs:px-2 sm:px-3 py-1 xs:py-1">Customer</Badge>
                      <Button
                        variant="secondary"
                        size="icon"
                        className="h-7 w-7 xs:h-8 xs:w-8 sm:h-8 sm:w-8 lg:h-9 lg:w-9"
                        onClick={() => deleteAccount(account.id, account.email)}
                      >
                        <Trash2 className="h-3 w-3 xs:h-4 xs:w-4 sm:h-4 sm:w-4 lg:h-4 lg:w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-3 xs:p-3 sm:p-4 lg:p-4 pt-0">
                  <div className="text-xs xs:text-xs sm:text-sm lg:text-base text-muted-foreground space-y-1 xs:space-y-1">
                    <p>Phone: {account.phone || 'N/A'}</p>
                    <p>Created: {new Date(account.created_at).toLocaleDateString()}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
