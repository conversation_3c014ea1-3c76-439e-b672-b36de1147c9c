"use client"

import dynamic from 'next/dynamic'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { MapPin } from 'lucide-react'
import { GPSLocation } from '@/lib/gps-data'

// Dynamically import the GPS tracker map to prevent SSR issues
const GPSTrackerMap = dynamic(
  () => import('./gps-tracker-map').then(mod => ({ default: mod.GPSTrackerMap })),
  {
    ssr: false,
    loading: () => (
      <Card>
        <CardContent className="p-0">
          <div className="h-[500px] w-full relative bg-muted rounded-b-lg flex items-center justify-center">
            <div className="text-center space-y-4">
              <MapPin className="w-8 h-8 mx-auto text-muted-foreground animate-pulse" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-32 mx-auto" />
                <Skeleton className="h-3 w-24 mx-auto" />
              </div>
              <div className="text-sm text-muted-foreground">Loading GPS tracker...</div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }
)

interface DynamicGPSMapProps {
  selectedCarId?: string
  followingCarId?: string
  onCarSelect?: (carId: string) => void
  showClusters?: boolean
  showTrails?: boolean
  showGeofences?: boolean
  timeWindow?: 'live' | '15m' | '1h' | '24h'
  className?: string
  // New props for ESP32 URL parameter support
  locations?: GPSLocation[]
  initialCenter?: [number, number]
  initialZoom?: number
}

export function DynamicGPSMap(props: DynamicGPSMapProps) {
  return <GPSTrackerMap {...props} />
}
