// MQTT GPS Consumer Service
// This service subscribes to HiveMQ Cloud and persists GPS data to database
import * as dotenv from 'dotenv';
import * as path from 'path';
import mqtt from 'mqtt';
import { createClient } from '@supabase/supabase-js';

// Load environment variables from .env.local
const envPath = path.join(process.cwd(), '.env.local');
dotenv.config({ path: envPath });
console.log('📁 Loading .env from:', envPath);

interface GPSPayload {
  deviceId: string;
  lat: number;
  lng: number;
  speed?: number;
  heading?: number;
  timestamp?: number;
  accuracy?: number;  // GPS accuracy in meters (from ESP32)
  satellites?: number; // Number of satellites (optional)
  fixMode?: number;    // GPS fix mode: 2=2D, 3=3D (optional)
}

interface DeviceStablePosition {
  deviceId: string;
  stableLatitude: number;
  stableLongitude: number;
  stableTimestamp: number;
  stationaryDuration: number; // How long device has been stationary (ms)
  lastRawLatitude: number;
  lastRawLongitude: number;
  consecutiveStationaryReadings: number;
}


class MQTTGPSConsumer {
  private client: mqtt.MqttClient | null = null;
  private supabase: any;
  private isConnected = false;
  private deviceHeartbeats: Map<string, number> = new Map(); // Track last seen timestamps
  private deviceStablePositions: Map<string, DeviceStablePosition> = new Map(); // Track stable positions for drift filtering
  private offlineCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  // ==========================================
  // ENHANCED GPS DRIFT FILTERING UTILITIES
  // ==========================================

  /**
   * Calculate distance between two GPS points using Haversine formula
   * @param lat1 Latitude of point 1
   * @param lon1 Longitude of point 1  
   * @param lat2 Latitude of point 2
   * @param lon2 Longitude of point 2
   * @returns Distance in meters
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371000; // Earth's radius in meters
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  /**
   * Enhanced GPS filtering with position-based drift detection
   * This prevents GPS "drift" on the map when devices are stationary
   */
  private filterGPSNoise(deviceId: string, gpsData: GPSPayload): {
    filteredLatitude: number;
    filteredLongitude: number;
    filteredSpeed: number;
    isStationary: boolean;
    movementConfidence: number;
    reasonCode: string;
    distanceFromStable: number;
    positionChanged: boolean;
  } {
    const rawSpeed = gpsData.speed || 0;
    const rawLatitude = gpsData.lat;
    const rawLongitude = gpsData.lng;
    const gpsAccuracy = gpsData.accuracy || 10; // Default to 10m if not provided
    const now = Date.now();
    
    // Constants for filtering logic
    const SPEED_THRESHOLD = 5.0; // km/h - speeds below this are treated as GPS drift
    const MIN_MOVEMENT_DISTANCE = Math.max(gpsAccuracy * 1.5, 8); // Minimum distance for significant movement (at least 1.5x GPS accuracy or 8m)
    const STATIONARY_CONFIRMATION_READINGS = 3; // Number of consecutive stationary readings before position is "locked"
    const MAX_STATIONARY_DURATION = 5 * 60 * 1000; // 5 minutes - max time to keep using stable position
    
    // Get or create stable position for this device
    let stablePos = this.deviceStablePositions.get(deviceId);
    
    if (!stablePos) {
      // First reading for this device - establish initial stable position
      stablePos = {
        deviceId,
        stableLatitude: rawLatitude,
        stableLongitude: rawLongitude,
        stableTimestamp: now,
        stationaryDuration: 0,
        lastRawLatitude: rawLatitude,
        lastRawLongitude: rawLongitude,
        consecutiveStationaryReadings: 1
      };
      this.deviceStablePositions.set(deviceId, stablePos);
      
      console.log(`🆕 NEW DEVICE STABLE POSITION for ${deviceId}: ${rawLatitude.toFixed(6)}, ${rawLongitude.toFixed(6)}`);
      
      return {
        filteredLatitude: rawLatitude,
        filteredLongitude: rawLongitude,
        filteredSpeed: rawSpeed >= SPEED_THRESHOLD ? rawSpeed : 0,
        isStationary: rawSpeed < SPEED_THRESHOLD,
        movementConfidence: rawSpeed >= SPEED_THRESHOLD ? 1.0 : 0.0,
        reasonCode: 'initial_position',
        distanceFromStable: 0,
        positionChanged: true
      };
    }

    // Calculate distance from current stable position
    const distanceFromStable = this.calculateDistance(
      stablePos.stableLatitude, stablePos.stableLongitude,
      rawLatitude, rawLongitude
    );
    
    // Calculate time since position was established
    const timeSinceStable = now - stablePos.stableTimestamp;
    stablePos.stationaryDuration = timeSinceStable;
    
    // Update raw position tracking
    stablePos.lastRawLatitude = rawLatitude;
    stablePos.lastRawLongitude = rawLongitude;
    
    console.log(`📍 GPS FILTER ANALYSIS for ${deviceId}:`);
    console.log(`   Raw Position: ${rawLatitude.toFixed(6)}, ${rawLongitude.toFixed(6)}`);
    console.log(`   Stable Position: ${stablePos.stableLatitude.toFixed(6)}, ${stablePos.stableLongitude.toFixed(6)}`);
    console.log(`   Distance from Stable: ${distanceFromStable.toFixed(2)}m`);
    console.log(`   GPS Accuracy: ${gpsAccuracy.toFixed(1)}m`);
    console.log(`   Raw Speed: ${rawSpeed.toFixed(1)} km/h`);
    console.log(`   Min Movement Distance: ${MIN_MOVEMENT_DISTANCE.toFixed(1)}m`);
    console.log(`   Stationary Duration: ${Math.round(timeSinceStable / 1000)}s`);
    
    // Determine if this represents significant movement
    const hasSignificantSpeedMovement = rawSpeed >= SPEED_THRESHOLD;
    const hasSignificantPositionMovement = distanceFromStable >= MIN_MOVEMENT_DISTANCE;
    const isSignificantMovement = hasSignificantSpeedMovement || hasSignificantPositionMovement;
    
    // Check if we should reset stable position due to time limits
    const shouldResetStablePosition = timeSinceStable > MAX_STATIONARY_DURATION;
    
    if (isSignificantMovement || shouldResetStablePosition) {
      // REAL MOVEMENT DETECTED or stable position expired
      stablePos.stableLatitude = rawLatitude;
      stablePos.stableLongitude = rawLongitude;
      stablePos.stableTimestamp = now;
      stablePos.stationaryDuration = 0;
      stablePos.consecutiveStationaryReadings = 0;
      
      const reason = shouldResetStablePosition ? 'stable_position_expired' : 
                    hasSignificantSpeedMovement ? 'speed_movement' : 'position_movement';
      
      console.log(`✅ SIGNIFICANT MOVEMENT for ${deviceId}: ${reason}`);
      console.log(`   Distance: ${distanceFromStable.toFixed(2)}m (≥${MIN_MOVEMENT_DISTANCE.toFixed(1)}m threshold)`);
      console.log(`   Speed: ${rawSpeed.toFixed(1)}km/h (≥${SPEED_THRESHOLD}km/h threshold)`);
      console.log(`   → UPDATING position to ${rawLatitude.toFixed(6)}, ${rawLongitude.toFixed(6)}`);
      
      return {
        filteredLatitude: rawLatitude,
        filteredLongitude: rawLongitude,
        filteredSpeed: rawSpeed,
        isStationary: false,
        movementConfidence: 1.0,
        reasonCode: reason,
        distanceFromStable,
        positionChanged: true
      };
    } else {
      // GPS DRIFT DETECTED - use stable position
      stablePos.consecutiveStationaryReadings++;
      
      const driftFilterActive = stablePos.consecutiveStationaryReadings >= STATIONARY_CONFIRMATION_READINGS;
      
      console.log(`🔒 GPS DRIFT DETECTED for ${deviceId}:`);
      console.log(`   Distance: ${distanceFromStable.toFixed(2)}m (<${MIN_MOVEMENT_DISTANCE.toFixed(1)}m threshold)`);
      console.log(`   Speed: ${rawSpeed.toFixed(1)}km/h (<${SPEED_THRESHOLD}km/h threshold)`);
      console.log(`   Consecutive Stationary: ${stablePos.consecutiveStationaryReadings}/${STATIONARY_CONFIRMATION_READINGS}`);
      console.log(`   → ${driftFilterActive ? 'USING STABLE' : 'ALLOWING'} position ${stablePos.stableLatitude.toFixed(6)}, ${stablePos.stableLongitude.toFixed(6)}`);
      
      if (driftFilterActive) {
        // Use stable position to prevent drift
        return {
          filteredLatitude: stablePos.stableLatitude,
          filteredLongitude: stablePos.stableLongitude,
          filteredSpeed: 0, // Always 0 speed for drift-filtered positions
          isStationary: true,
          movementConfidence: 0.0,
          reasonCode: 'gps_drift_filtered',
          distanceFromStable,
          positionChanged: false
        };
      } else {
        // Still allowing small movements until we confirm it's stationary
        return {
          filteredLatitude: rawLatitude,
          filteredLongitude: rawLongitude,
          filteredSpeed: 0, // Speed filtered to 0 but position still updating
          isStationary: true,
          movementConfidence: 0.2,
          reasonCode: 'confirming_stationary',
          distanceFromStable,
          positionChanged: true
        };
      }
    }
  }

  async connect() {
    // Use EMQ Public Broker (Simple and Reliable)
    const brokerUrl = 'mqtt://broker.emqx.io:1883';
    const username = '';
    const password = '';
    
    const options: mqtt.IClientOptions = {
      clientId: `pathlink_consumer_${Math.random().toString(16).substr(2, 8)}`,
      reconnectPeriod: 5000,
      connectTimeout: 30000,
      clean: true,
      keepalive: 60
    };

    console.log(`🔌 Connecting to EMQ Public Broker...`);
    console.log('🔗 Broker URL:', brokerUrl);
    console.log('👤 Username:', username || 'none');
    console.log('🔑 Password:', password || 'none');
    
    try {
      this.client = mqtt.connect(brokerUrl, options);
    } catch (error) {
      console.error('❌ MQTT Connection Error:', error);
      throw error;
    }

    this.client.on('connect', () => {
      console.log(`✅ MQTT Consumer connected to EMQ Public Broker`);
      console.log(`🔍 Client ID: ${options.clientId}`);
      console.log(`🔍 Connection Options:`, JSON.stringify(options, null, 2));
      this.isConnected = true;
      
      // Subscribe to EMQ GPS topics with more debugging
      const topics = ['pathlink/gps/+']; // Use wildcard pattern only to avoid duplicates
      
      topics.forEach(topic => {
        this.client?.subscribe(topic, { qos: 1 }, (err) => {
          if (err) {
            console.error(`❌ MQTT Subscription Error for ${topic}:`, err);
          } else {
            console.log(`📡 ✅ Successfully subscribed to: ${topic}`);
          }
        });
      });
      
      // Additional connection info
      console.log(`🔍 MQTT Client State: ${this.client?.connected ? 'CONNECTED' : 'DISCONNECTED'}`);
    });

    this.client.on('message', async (topic, payload) => {
      console.log(`📨 Raw MQTT Message Received:`);
      console.log(`   Topic: ${topic}`);
      console.log(`   Payload: ${payload.toString()}`);
      console.log(`   Length: ${payload.length}`);
      
      try {
        await this.handleGPSMessage(topic, payload);
      } catch (error) {
        console.error('❌ Error processing GPS message:', error);
        console.error('   Topic:', topic);
        console.error('   Payload:', payload.toString());
      }
    });

    this.client.on('error', (err) => {
      console.error('❌ MQTT Client Error:', err);
      this.isConnected = false;
    });

    this.client.on('close', () => {
      console.log('🔌 MQTT Client disconnected');
      this.isConnected = false;
    });

    this.client.on('reconnect', () => {
      console.log('🔄 MQTT Client attempting to reconnect...');
    });

    this.client.on('offline', () => {
      console.log('📴 MQTT Client went offline');
      this.isConnected = false;
    });

    // Periodic connection status check
    setInterval(() => {
      if (this.client) {
        console.log(`🔍 MQTT Status Check: Connected=${this.client.connected}, Reconnecting=${this.client.reconnecting}`);
        console.log(`🔍 Client State: isConnected=${this.isConnected}`);
      }
    }, 30000); // Every 30 seconds
    
    // Set up device offline detection (check every 30 seconds)
    this.offlineCheckInterval = setInterval(() => {
      this.checkDeviceOfflineStatus();
    }, 30000);
  }

  private async handleGPSMessage(topic: string, payload: Buffer) {
    try {
      const topicParts = topic.split('/');
      let deviceId: string;
      
      // Extract device ID based on topic structure
      if (topic.startsWith('devices/')) {
        // Azure IoT Hub: devices/lilygo-a7670e-01/messages/events/
        deviceId = topicParts[1];
      } else {
        // HiveMQ/EMQ: pathlink/gps/DEVICE_ID
        deviceId = topicParts[2];
      }
      
      const message: GPSPayload = JSON.parse(payload.toString());
      
      // 🔍 ENHANCED DEBUGGING: Identify the source of GPS data
      console.log(`\n=== GPS MESSAGE DEBUG INFO ===`);
      console.log(`📡 Topic: ${topic}`);
      console.log(`🆔 Device ID: ${deviceId}`);
      console.log(`📦 Raw Payload: ${payload.toString()}`);
      console.log(`🕐 Server Time: ${new Date().toISOString()}`);
      console.log(`🕐 ESP32 Timestamp: ${message.timestamp} (millis)`);
      console.log(`🔍 Has New Fields: accuracy=${message.accuracy !== undefined}, satellites=${message.satellites !== undefined}, fixMode=${message.fixMode !== undefined}`);
      
      // 🕵️ SOURCE DETECTION: Identify if this is from real ESP32 or phantom source
      const isRealESP32 = (
        message.accuracy !== undefined &&
        message.satellites !== undefined &&
        message.fixMode !== undefined &&
        typeof message.timestamp === 'number' &&
        message.timestamp > 0
      );
      
      console.log(`🕵️ Source Analysis: ${isRealESP32 ? '🆕 REAL ESP32' : '🟡 PHANTOM/TEST'} data detected`);
      console.log(`==============================\n`);
      
      if (!isRealESP32) {
        console.error(`❌ PHANTOM GPS DATA BLOCKED from ${deviceId}`);
        console.error(`   Reason: Missing ESP32-specific fields (accuracy/satellites/fixMode)`);
        console.error(`   This is likely from test script, template firmware, or mock data`);
        return; // Block phantom GPS data
      }
      
      // 🔍 STRICT VALIDATION: Only accept GPS data from authorized ESP32 devices
      const authorizedDevices = [
        'lilygo-a7670e-01',
        'lilygo-esp32-01', 
        // Add other authorized ESP32 device IDs here
      ];
      
      if (!authorizedDevices.includes(deviceId)) {
        console.error(`⚠️ UNAUTHORIZED GPS DATA ATTEMPT from device: ${deviceId}`);
        console.error(`   Only authorized devices can send GPS data: ${authorizedDevices.join(', ')}`);
        console.error(`   This may be from test script, template firmware, or unauthorized device`);
        return; // Block unauthorized GPS data
      }
      
      // Update device heartbeat - track when this device was last seen
      const now = Date.now();
      const previousHeartbeat = this.deviceHeartbeats.get(deviceId);
      this.deviceHeartbeats.set(deviceId, now);
      
      if (previousHeartbeat) {
        const timeSinceLastSeen = now - previousHeartbeat;
        console.log(`💗 Device ${deviceId} heartbeat: ${Math.round(timeSinceLastSeen / 1000)}s since last message`);
      } else {
        console.log(`🆕 Device ${deviceId} first authorized heartbeat received`);
      }
      
      // Validate ESP32 data format - only accept messages with GPS quality metrics
      const hasRequiredFields = (
        message.accuracy !== undefined && 
        message.satellites !== undefined && 
        message.fixMode !== undefined
      );
      
      if (!hasRequiredFields) {
        console.warn(`⚠️ Rejecting incomplete GPS data from ${deviceId}:`);
        console.warn(`   Missing required fields: accuracy=${message.accuracy}, satellites=${message.satellites}, fixMode=${message.fixMode}`);
        console.warn(`   This is likely from test script or old ESP32 firmware - waiting for proper ESP32 data`);
        return; // Skip processing - wait for real ESP32 data
      }
      
      console.log(`📍 Valid ESP32 GPS data received from ${deviceId}:`, {
        lat: message.lat,
        lng: message.lng,
        speed: message.speed,
        accuracy: message.accuracy + 'm',
        satellites: message.satellites,
        fixMode: message.fixMode === 3 ? '3D' : message.fixMode === 2 ? '2D' : 'INVALID',
        esp32_timestamp: message.timestamp,
        server_timestamp: new Date().toISOString()
      });

      // ==========================================
      // GPS QUALITY VALIDATION AND STATUS LOGIC
      // ==========================================
      
      // Validate GPS coordinates are within reasonable bounds
      const isValidCoordinates = (
        typeof message.lat === 'number' && typeof message.lng === 'number' &&
        message.lat >= -90 && message.lat <= 90 &&
        message.lng >= -180 && message.lng <= 180 &&
        !(message.lat === 0 && message.lng === 0) // Exclude null island
      );
      
      if (!isValidCoordinates) {
        console.warn(`⚠️ Invalid GPS coordinates from ${deviceId}: lat=${message.lat}, lng=${message.lng}`);
        // Mark device as connected but GPS offline
        await this.updateDeviceStatus(deviceId, 'offline');
        return;
      }
      
      // ==========================================
      // 🚀 ENHANCED GPS FILTERING WITH DRIFT DETECTION
      // ==========================================
      
      // Apply intelligent GPS filtering and movement detection
      const filterResult = this.filterGPSNoise(deviceId, message);
      
      // Use filtered position and speed to prevent GPS drift
      const filteredLatitude = filterResult.filteredLatitude;
      const filteredLongitude = filterResult.filteredLongitude;
      const filteredSpeed = filterResult.filteredSpeed;
      const isStationaryDevice = filterResult.isStationary;
      const movementConfidence = filterResult.movementConfidence;
      const reasonCode = filterResult.reasonCode;
      const distanceFromStable = filterResult.distanceFromStable;
      const positionChanged = filterResult.positionChanged;
      
      // Determine GPS status based on filtered data and quality metrics
      let gpsStatus: 'active' | 'idle' | 'offline' = 'offline';
      let connectionStatus: 'online' | 'offline' = 'online'; // Device is sending data
      
      // GPS Quality Assessment (using validated ESP32 data and filtered speed)
      const accuracy = message.accuracy!; // Guaranteed to exist due to validation above
      const rawSpeed = message.speed || 0;
      const satellites = message.satellites!; // Guaranteed to exist due to validation above
      const fixMode = message.fixMode!; // Guaranteed to exist due to validation above
      
      // Re-validate coordinates using filtered position instead of raw position
      const isValidFilteredCoordinates = (
        typeof filteredLatitude === 'number' && typeof filteredLongitude === 'number' &&
        filteredLatitude >= -90 && filteredLatitude <= 90 &&
        filteredLongitude >= -180 && filteredLongitude <= 180 &&
        !(filteredLatitude === 0 && filteredLongitude === 0) // Exclude null island
      );
      
      if (accuracy <= 50 && isValidFilteredCoordinates) {
        // Good GPS accuracy (<=50m) - use filtered speed for status determination
        if (filteredSpeed >= 5.0) {
          gpsStatus = 'active'; // Real movement detected (speed >= 5 km/h)
        } else {
          gpsStatus = 'idle'; // Stationary or GPS drift (speed < 5 km/h)
        }
      } else {
        // Poor accuracy GPS (>50m) or invalid coordinates - mark as offline
        gpsStatus = 'offline';
        console.warn(`⚠️ Poor GPS quality from ${deviceId}: accuracy=${accuracy}m`);
      }
      
      console.log(`📊 Enhanced GPS Quality Assessment for ${deviceId}:`, {
        raw_position: `${message.lat.toFixed(6)}, ${message.lng.toFixed(6)}`,
        filtered_position: `${filteredLatitude.toFixed(6)}, ${filteredLongitude.toFixed(6)}`,
        position_changed: positionChanged,
        distance_from_stable: distanceFromStable.toFixed(2) + 'm',
        raw_speed: rawSpeed.toFixed(1) + 'km/h',
        filtered_speed: filteredSpeed.toFixed(1) + 'km/h',
        accuracy: accuracy.toFixed(1) + 'm',
        is_stationary: isStationaryDevice,
        movement_confidence: movementConfidence.toFixed(2),
        filter_reason: reasonCode,
        determined_status: gpsStatus,
        connection_status: connectionStatus
      });

      // Resolve device ID to car UUID
      const { data: mappingData } = await this.supabase
        .rpc('get_car_id_from_device', { device_id_param: deviceId });
      
      const actualCarId = mappingData || deviceId;

      // Generate encrypted token for security (using lat, lng, timestamp, deviceId)
      const currentTimestamp = new Date();
      const tokenData = `${message.lat}-${message.lng}-${currentTimestamp.getTime()}-${deviceId}`;
      const encryptedToken = Buffer.from(tokenData).toString('base64');
      
      console.log(`🔒 Generated encrypted token: ${encryptedToken.substring(0, 20)}...`);

      // ==========================================
      // DATABASE INSERT WITH FILTERED COORDINATES
      // ==========================================
      
      // Only insert if position actually changed or it's the first reading
      // This prevents unnecessary database writes when device is stationary
      if (positionChanged || reasonCode === 'initial_position') {
        console.log(`💾 STORING GPS DATA for ${deviceId} (reason: ${reasonCode})`);
        console.log(`   Position: ${filteredLatitude.toFixed(6)}, ${filteredLongitude.toFixed(6)}`);
        console.log(`   Speed: ${filteredSpeed.toFixed(1)} km/h`);
        
        // Insert GPS data into main table using FILTERED coordinates and speed
        const { data, error } = await this.supabase
          .from('gps_locations')
          .insert({
            car_id: actualCarId,
            latitude: filteredLatitude, // Use FILTERED latitude to prevent drift
            longitude: filteredLongitude, // Use FILTERED longitude to prevent drift
            speed: filteredSpeed, // Use filtered speed
            heading: message.heading || 0,
            status: gpsStatus, // Use calculated status based on GPS quality and filtering
            timestamp: currentTimestamp, // Use current server time instead of ESP32 millis()
            encrypted_token: encryptedToken
          })
          .select()
          .single();

        if (error) {
          console.error('❌ Database insert failed:', error);
          return;
        }
        
        console.log(`✅ GPS data stored successfully for ${deviceId}`);
      } else {
        console.log(`⏭️  SKIPPING database insert for ${deviceId} (no position change, drift filtered)`);
        console.log(`   Raw position would be: ${message.lat.toFixed(6)}, ${message.lng.toFixed(6)}`);
        console.log(`   Using stable position: ${filteredLatitude.toFixed(6)}, ${filteredLongitude.toFixed(6)}`);
      }

      // Get current message count first
      const { data: currentDevice } = await this.supabase
        .from('gps_device_mapping')
        .select('total_messages_sent')
        .eq('device_id', deviceId)
        .single();

      // Update device mapping with latest GPS data and quality-based status
      // Use filtered coordinates to maintain consistency with stored GPS data
      await this.supabase
        .from('gps_device_mapping')
        .update({
          last_gps_latitude: filteredLatitude, // Use filtered latitude
          last_gps_longitude: filteredLongitude, // Use filtered longitude
          last_gps_accuracy: accuracy || null,
          last_seen: new Date().toISOString(),
          connection_status: connectionStatus, // Use calculated connection status
          total_messages_sent: (currentDevice?.total_messages_sent || 0) + 1
        })
        .eq('device_id', deviceId);

      // Update car current position with filtered coordinates
      if (actualCarId !== deviceId) {
        await this.supabase
          .from('cars')
          .update({
            current_latitude: filteredLatitude, // Use filtered latitude
            current_longitude: filteredLongitude, // Use filtered longitude
            last_gps_update: new Date().toISOString()
          })
          .eq('id', actualCarId);
      }

      console.log(`✅ GPS data processed for device ${deviceId} -> car ${actualCarId}`);
      console.log(`📊 Final Status: GPS=${gpsStatus}, Connection=${connectionStatus}, Accuracy=${accuracy}m`);
      console.log(`🎯 Speed Processing: Raw=${rawSpeed.toFixed(1)}km/h → Filtered=${filteredSpeed.toFixed(1)}km/h`);
      console.log(`📍 Position Processing: Raw=(${message.lat.toFixed(6)}, ${message.lng.toFixed(6)}) → Filtered=(${filteredLatitude.toFixed(6)}, ${filteredLongitude.toFixed(6)})`);
      console.log(`🔄 Filter Result: ${reasonCode}, Position Changed: ${positionChanged}, Distance from Stable: ${distanceFromStable.toFixed(2)}m`);
      
      // Emit to WebSocket clients for real-time updates
      // TODO: Implement WebSocket/SSE broadcasting
      
    } catch (error) {
      console.error('❌ Failed to process GPS message:', error);
    }
  }

  async updateDeviceStatus(deviceId: string, status: 'online' | 'offline') {
    await this.supabase
      .from('gps_device_mapping')
      .update({
        connection_status: status,
        last_seen: new Date().toISOString()
      })
      .eq('device_id', deviceId);
  }
  
  // Check for devices that haven't sent data recently and mark them offline
  private async checkDeviceOfflineStatus() {
    const now = Date.now();
    const offlineThresholdMs = 2 * 60 * 1000; // 2 minutes without data = offline
    
    for (const [deviceId, lastSeen] of this.deviceHeartbeats.entries()) {
      const timeSinceLastSeen = now - lastSeen;
      
      if (timeSinceLastSeen > offlineThresholdMs) {
        console.log(`⚠️  Device ${deviceId} appears OFFLINE - no data for ${Math.round(timeSinceLastSeen / 1000)}s`);
        
        // Mark device as offline in database
        await this.updateDeviceStatus(deviceId, 'offline');
        
        // Remove from heartbeat tracking to avoid spam
        this.deviceHeartbeats.delete(deviceId);
        
        console.log(`🔴 Device ${deviceId} marked as OFFLINE due to no recent GPS data`);
      }
    }
    
    // Log current active devices
    const activeDevices = Array.from(this.deviceHeartbeats.keys());
    if (activeDevices.length > 0) {
      console.log(`🟢 Active devices: ${activeDevices.join(', ')}`);
    } else {
      console.log(`🔴 No active GPS devices detected`);
    }
  }

  disconnect() {
    if (this.client) {
      this.client.end();
      this.isConnected = false;
      console.log('🔌 MQTT Consumer disconnected');
    }
    
    // Clear offline check interval
    if (this.offlineCheckInterval) {
      clearInterval(this.offlineCheckInterval);
      this.offlineCheckInterval = null;
    }
    
    // Clear device heartbeats
    this.deviceHeartbeats.clear();
    
    // Clear stable positions
    this.deviceStablePositions.clear();
    console.log('🗑️ Cleared device stable positions');
  }

  getConnectionStatus() {
    return this.isConnected;
  }
}

// Singleton instance
let mqttConsumer: MQTTGPSConsumer | null = null;

export function getMQTTConsumer(): MQTTGPSConsumer {
  if (!mqttConsumer) {
    mqttConsumer = new MQTTGPSConsumer();
  }
  return mqttConsumer;
}

// Auto-start in development or when explicitly requested
console.log('🚀 Starting MQTT GPS Consumer...');
// Using EMQ Public Broker (Simple and Reliable)
const brokerUrl = 'mqtt://broker.emqx.io:1883';
const brokerType = 'EMQ Public Broker';

console.log('🔧 Environment check:', {
  NODE_ENV: process.env.NODE_ENV,
  START_MQTT_CONSUMER: process.env.START_MQTT_CONSUMER,
  BROKER_TYPE: brokerType,
  BROKER_URL: 'SET',
  USERNAME: 'NONE',
  PASSWORD: 'NONE'
});

if (process.env.NODE_ENV !== 'test') {
  const consumer = getMQTTConsumer();
  consumer.connect().catch(error => {
    console.error('❌ Failed to start MQTT consumer:', error);
    process.exit(1);
  });
  
  // Handle graceful shutdown
  process.on('SIGTERM', () => {
    console.log('🛑 Shutting down MQTT consumer...');
    consumer.disconnect();
    process.exit(0);
  });

  process.on('SIGINT', () => {
    console.log('🛑 Shutting down MQTT consumer (Ctrl+C)...');
    consumer.disconnect();
    process.exit(0);
  });
}

export default MQTTGPSConsumer;
