"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MapPin, Calendar, Clock } from "lucide-react"
import * as React from "react"

export function DetailsRental() {
  const [rentalStatus] = React.useState<"upcoming" | "active" | "completed">("upcoming")
  
  const statusConfig = {
    upcoming: {
      label: "Upcoming Rental",
      color: "bg-blue-100 text-blue-800"
    },
    active: {
      label: "Active Rental", 
      color: "bg-green-100 text-green-800"
    },
    completed: {
      label: "Past Rental",
      color: "bg-gray-100 text-gray-800"
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Rental Details</CardTitle>
          <Badge className={statusConfig[rentalStatus].color}>
            {statusConfig[rentalStatus].label}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Car Information */}
        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
          <div className="relative w-16 h-12 bg-white rounded-md overflow-hidden">
            <img
              src="/sport-car-thumb.png"
              alt="Rental car"
              className="w-full h-full object-contain"
              crossOrigin="anonymous"
            />
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900">Mercedes-Benz AMG GT</h4>
            <p className="text-sm text-gray-600">Sport Car • Automatic</p>
          </div>
        </div>

        {/* Rental Information */}
        <div className="space-y-3">
          {/* Pick-up */}
          <div className="flex items-start gap-3">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full mt-0.5">
              <MapPin className="h-4 w-4 text-blue-600" />
            </div>
            <div className="flex-1">
              <div className="font-medium text-gray-900">Pick-up Location</div>
              <div className="text-sm text-gray-600">Laoag City - Ollie's Garage</div>
              <div className="flex items-center gap-2 mt-1 text-sm text-gray-500">
                <Calendar className="h-3 w-3" />
                <span>July 20, 2025</span>
                <Clock className="h-3 w-3 ml-2" />
                <span>07:00 AM</span>
              </div>
            </div>
          </div>

          {/* Drop-off */}
          <div className="flex items-start gap-3">
            <div className="flex items-center justify-center w-8 h-8 bg-orange-50 rounded-full mt-0.5">
              <MapPin className="h-4 w-4 text-orange-600" />
            </div>
            <div className="flex-1">
              <div className="font-medium text-gray-900">Drop-off Location</div>
              <div className="text-sm text-gray-600">Manila - NAIA Terminal 3</div>
              <div className="flex items-center gap-2 mt-1 text-sm text-gray-500">
                <Calendar className="h-3 w-3" />
                <span>July 21, 2025</span>
                <Clock className="h-3 w-3 ml-2" />
                <span>01:00 PM</span>
              </div>
            </div>
          </div>
        </div>

        {/* Pricing Breakdown */}
        <div className="p-3 bg-gray-50 rounded-lg space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Rental Duration</span>
            <span className="font-medium text-gray-900">1 day, 6 hours</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Base Price</span>
            <span className="font-medium text-gray-900">₱90.00</span>
          </div>
          <div className="flex justify-between text-sm text-green-600">
            <span>Discount (10%)</span>
            <span>-₱10.00</span>
          </div>
          <div className="border-t border-gray-200 pt-2">
            <div className="flex justify-between">
              <span className="font-medium text-gray-900">Total Amount</span>
              <span className="text-lg font-bold text-blue-600">₱80.00</span>
            </div>
          </div>
        </div>
      </CardContent>
      

    </Card>
  )
}
