"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { useRouter, useSearchParams } from "next/navigation";
import { Eye, EyeOff, ArrowLeft, CheckCircle, XCircle, Shield, Check, X } from "lucide-react";
import Image from "next/image";
import { useToast } from "@/hooks/use-toast";
import { CustomerLoadingButton } from "@/components/customer-side/loading/enhanced-loading-components";
import Link from "next/link";

interface DesktopAuthPageProps {
  mode: "login" | "register";
}

export function DesktopAuthPage({ mode }: DesktopAuthPageProps) {
  const { signIn, signUp, user } = useCustomerAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  
  // Form states
  const [email, setEmail] = React.useState("");
  const [password, setPassword] = React.useState("");
  const [firstName, setFirstName] = React.useState("");
  const [middleInitial, setMiddleInitial] = React.useState("");
  const [lastName, setLastName] = React.useState("");
  const [phoneNumber, setPhoneNumber] = React.useState("");
  const [showPassword, setShowPassword] = React.useState(false);
  const [rememberMe, setRememberMe] = React.useState(false);
  const [error, setError] = React.useState("");
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [currentMode, setCurrentMode] = React.useState<"login" | "register">(mode);

  // Password strength calculation for register mode
  const getPasswordStrength = (password: string) => {
    let score = 0;
    const requirements = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };

    Object.values(requirements).forEach((met) => met && score++);

    return {
      score,
      requirements,
      strength: score < 3 ? "weak" : score < 5 ? "medium" : "strong",
    };
  };

  const passwordStrength = getPasswordStrength(password);

  // Redirect already authenticated users
  React.useEffect(() => {
    if (user) {
      if (user.user_metadata?.role === "admin") {
        router.replace("/admin");
      } else {
        router.replace("/");
      }
    }
  }, [user, router]);

  // Check for verification message or error in URL params
  React.useEffect(() => {
    const errorParam = searchParams.get("error");
    const messageParam = searchParams.get("message");

    if (errorParam) {
      setError(decodeURIComponent(errorParam));
    }

    if (messageParam) {
      toast({
        title: "Account Status",
        description: decodeURIComponent(messageParam),
      });
    }
  }, [searchParams, toast]);

  const handleModeSwitch = (newMode: "login" | "register") => {
    setCurrentMode(newMode);
    setError("");
    setEmail("");
    setPassword("");
    setFirstName("");
    setMiddleInitial("");
    setLastName("");
    setPhoneNumber("");
    setShowPassword(false);
    setRememberMe(false);
  };

  // Phone number validation
  const validatePhoneNumber = (phone: string): boolean => {
    // Basic Philippine phone number validation
    const phoneRegex = /^(\+63|63|0)?[9]\d{9}$/;
    return phoneRegex.test(phone.replace(/[\s-]/g, ''));
  };

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (isSubmitting) return;

    setError("");
    setIsSubmitting(true);

    // Add small delay to prevent rapid successive requests
    await new Promise(resolve => setTimeout(resolve, 500));

    try {
      if (currentMode === "login") {
        const { error } = await signIn(email, password);

        if (error) {
          setError(error.message);
          toast({
            variant: "destructive",
            title: "Login Failed",
            description: error.message,
            className: "bg-white border-red-200",
            action: <X className="h-5 w-5 text-red-500" />,
          });
          return;
        }

        const redirectTo = searchParams.get("redirect");
        if (redirectTo && !redirectTo.startsWith("/admin")) {
          router.replace(redirectTo);
        } else {
          router.replace("/");
        }
      } else {
        // Register mode
        if (passwordStrength.score < 3) {
          setError("Please ensure your password meets the minimum requirements.");
          return;
        }

        // Validate phone number
        if (!validatePhoneNumber(phoneNumber)) {
          setError("Please enter a valid Philippine phone number (e.g., 09123456789).");
          return;
        }

        const nameParts = [firstName.trim(), middleInitial.trim(), lastName.trim()].filter(Boolean);
        const fullName = nameParts.join(' ').trim();
        const { error } = await signUp(email, password, { 
          full_name: fullName,
          phone: phoneNumber.trim(),
          first_name: firstName.trim(),
          middle_initial: middleInitial.trim(),
          last_name: lastName.trim()
        });

        if (error) {
          setError(error.message);
          toast({
            variant: "destructive",
            title: "Registration Failed",
            description: error.message,
            className: "bg-white border-red-200",
            action: <X className="h-5 w-5 text-red-500" />,
          });
          return;
        }

        toast({
          title: "Registration Successful!",
          description: "Please check your email for a 6-digit verification code.",
          className: "bg-white border-green-200",
          action: <Check className="h-5 w-5 text-green-500" />,
        });

        // Store user data in localStorage for setup account page
        localStorage.setItem('pendingUserData', JSON.stringify({
          firstName: firstName.trim(),
          middleInitial: middleInitial.trim(),
          lastName: lastName.trim(),
          phone: phoneNumber.trim()
        }));

        router.replace(`/auth/verify-otp?email=${encodeURIComponent(email)}`);
      }
    } catch (err) {
      const errorMessage = "An unexpected error occurred. Please try again.";
      setError(errorMessage);
      toast({
        variant: "destructive",
        title: currentMode === "login" ? "Login Failed" : "Registration Failed",
        description: errorMessage,
        className: "bg-white border-red-200",
        action: <X className="h-5 w-5 text-red-500" />,
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="min-h-screen grid grid-cols-2">
      {/* Left Panel - Background Image with Text Overlay */}
      <div className="relative overflow-hidden bg-muted flex items-center justify-center">
        <div className="relative w-full h-full max-w-2xl max-h-screen">
          <Image
            src="/ollie_cover_photo.jpg"
            alt="Ollie's Rent A Car"
            fill
            className="object-contain"
            priority
          />
        </div>
        {/* Text Overlay - Bottom Left */}
        <div className="absolute bottom-0 left-0 p-12 text-white bg-gradient-to-t from-black/60 to-transparent w-full">
          <h1 className="text-2xl font-bold mb-2">
            Welcome to Ollie's Rent A Car
          </h1>
          <p className="text-base font-normal opacity-90">
            Your trusted partner for reliable vehicle rentals
          </p>
        </div>
      </div>

      {/* Right Panel - Form */}
      <div className="flex flex-col justify-center items-center p-12 bg-background">
        {/* Back to Homepage Button - Top Right */}
        <div className="absolute top-4 right-4">
          <Link href="/">
            <Button
              variant="secondary"
              size="sm"
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Homepage
            </Button>
          </Link>
        </div>

        {/* Logo - Centered */}
        <div className="w-full max-w-md mb-8">
          <div className="flex justify-center mb-6">
            <Link href="/" className="cursor-pointer">
              <div className="relative w-40 h-16">
                <Image
                  src="/ollie_logo1.jpg"
                  alt="Ollie's Logo"
                  fill
                  className="object-contain hover:opacity-80 transition-opacity"
                  priority
                />
              </div>
            </Link>
          </div>

          {/* Welcome Text - Centered */}
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold mb-2">
              {currentMode === "login" ? "Welcome Back" : "Create Your Account"}
            </h2>
            <p className="text-base text-muted-foreground">
              {currentMode === "login" 
                ? "Sign in to your customer account" 
                : "Join Ollie Track and start your journey"}
            </p>
          </div>

          {/* Tab Toggle - Pill Style */}
          <div className="flex rounded-full bg-muted p-1 mb-6 border border-border/50">
            <button
              type="button"
              onClick={() => handleModeSwitch("login")}
              className={`flex-1 py-3 px-6 rounded-full text-sm font-medium transition-all duration-200 ${
                currentMode === "login"
                  ? "bg-blue-600 text-white shadow-sm"
                  : "bg-transparent text-muted-foreground hover:text-foreground hover:bg-background/50"
              }`}
            >
              Login
            </button>
            <button
              type="button"
              onClick={() => handleModeSwitch("register")}
              className={`flex-1 py-3 px-6 rounded-full text-sm font-medium transition-all duration-200 ${
                currentMode === "register"
                  ? "bg-blue-600 text-white shadow-sm"
                  : "bg-transparent text-muted-foreground hover:text-foreground hover:bg-background/50"
              }`}
            >
              Register
            </button>
          </div>

          {/* Form */}
          <form onSubmit={onSubmit} className="space-y-4">

            {/* Name Fields - Register Only */}
            {currentMode === "register" && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName" className="text-sm font-medium">
                      First Name
                    </Label>
                    <Input
                      id="firstName"
                      type="text"
                      placeholder="First name"
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      required
                      className="h-12 px-4 rounded-full border border-border"
                      disabled={isSubmitting}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName" className="text-sm font-medium">
                      Last Name
                    </Label>
                    <Input
                      id="lastName"
                      type="text"
                      placeholder="Last name"
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      required
                      className="h-12 px-4 rounded-full border border-border"
                      disabled={isSubmitting}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="middleInitial" className="text-sm font-medium">
                    Middle Initial <span className="text-muted-foreground">(Optional)</span>
                  </Label>
                  <Input
                    id="middleInitial"
                    type="text"
                    placeholder="M"
                    value={middleInitial}
                    onChange={(e) => {
                      const value = e.target.value.toUpperCase().slice(0, 1);
                      setMiddleInitial(value);
                    }}
                    maxLength={1}
                    className="h-12 px-4 rounded-full border border-border"
                    disabled={isSubmitting}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber" className="text-sm font-medium">
                    Phone Number (max 11 digits)
                  </Label>
                  <Input
                    id="phoneNumber"
                    type="tel"
                    placeholder="Enter your phone number (e.g., 09123456789)"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    maxLength={11}
                    required
                    className="h-12 px-4 rounded-full border border-border"
                    disabled={isSubmitting}
                  />
                </div>
              </>
            )}

            {/* Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                {currentMode === "login" ? "Username" : "Email Address"}
              </Label>
              <Input
                id="email"
                type="email"
                placeholder={currentMode === "login" ? "Enter your username" : "Enter your email address"}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="h-12 px-4 rounded-full border border-border"
                disabled={isSubmitting}
              />
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium">
                Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder={currentMode === "login" ? "Enter your password" : "Create a strong password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="h-12 px-4 pr-12 rounded-full border border-border"
                  disabled={isSubmitting}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                  aria-label={showPassword ? "Hide password" : "Show password"}
                  disabled={isSubmitting}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>

              {/* Password Requirements - Register Only */}
              {currentMode === "register" && password && (
                <div className="space-y-2 mt-3">
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-muted-foreground" />
                    <span className="text-xs font-medium">
                      Password Strength:
                      <span
                        className={`ml-1 ${
                          passwordStrength.strength === "weak"
                            ? "text-destructive"
                            : passwordStrength.strength === "medium"
                            ? "text-yellow-600"
                            : "text-green-600"
                        }`}
                      >
                        {passwordStrength.strength === "weak"
                          ? "Weak"
                          : passwordStrength.strength === "medium"
                          ? "Medium"
                          : "Strong"}
                      </span>
                    </span>
                  </div>

                  <div className="w-full bg-muted rounded-full h-1.5">
                    <div
                      className={`h-1.5 rounded-full transition-all duration-300 ${
                        passwordStrength.strength === "weak"
                          ? "bg-destructive w-1/3"
                          : passwordStrength.strength === "medium"
                          ? "bg-yellow-500 w-2/3"
                          : "bg-green-500 w-full"
                      }`}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-1 text-xs">
                    <div className="flex items-center gap-1">
                      {passwordStrength.requirements.length ? (
                        <CheckCircle className="h-3 w-3 text-green-600" />
                      ) : (
                        <XCircle className="h-3 w-3 text-destructive" />
                      )}
                      <span
                        className={
                          passwordStrength.requirements.length
                            ? "text-green-600"
                            : "text-destructive"
                        }
                      >
                        8+ characters
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      {passwordStrength.requirements.lowercase ? (
                        <CheckCircle className="h-3 w-3 text-green-600" />
                      ) : (
                        <XCircle className="h-3 w-3 text-destructive" />
                      )}
                      <span
                        className={
                          passwordStrength.requirements.lowercase
                            ? "text-green-600"
                            : "text-destructive"
                        }
                      >
                        Lowercase
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      {passwordStrength.requirements.uppercase ? (
                        <CheckCircle className="h-3 w-3 text-green-600" />
                      ) : (
                        <XCircle className="h-3 w-3 text-destructive" />
                      )}
                      <span
                        className={
                          passwordStrength.requirements.uppercase
                            ? "text-green-600"
                            : "text-destructive"
                        }
                      >
                        Uppercase
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      {passwordStrength.requirements.number ? (
                        <CheckCircle className="h-3 w-3 text-green-600" />
                      ) : (
                        <XCircle className="h-3 w-3 text-destructive" />
                      )}
                      <span
                        className={
                          passwordStrength.requirements.number
                            ? "text-green-600"
                            : "text-destructive"
                        }
                      >
                        Number
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Remember Me & Forgot Password - Login Only */}
            {currentMode === "login" && (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="remember"
                    checked={rememberMe}
                    onCheckedChange={(checked) => setRememberMe(!!checked)}
                    className="h-4 w-4"
                  />
                  <Label htmlFor="remember" className="text-sm">
                    Remember Me
                  </Label>
                </div>
                <Link
                  href="/customer/forgot-password"
                  className="text-sm text-[#4285f4] hover:text-[#3367d6] hover:underline"
                >
                  Forgot Password?
                </Link>
              </div>
            )}

            {/* Submit Button */}
            <CustomerLoadingButton
              type="submit"
              isLoading={isSubmitting}
              loadingText={currentMode === "login" ? "Signing In..." : "Creating Account..."}
              disabled={currentMode === "register" && !!password && passwordStrength.score < 3}
              className="w-full h-12 rounded-full bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md transition-all duration-200 font-medium"
            >
              {currentMode === "login" ? "Sign In" : "Create Account"}
            </CustomerLoadingButton>
          </form>

          {/* Footer Link */}
          <div className="text-center mt-6">
            <p className="text-sm text-muted-foreground">
              {currentMode === "login" ? "Don't have an account? " : "Already have an account? "}
              <button
                type="button"
                onClick={() => handleModeSwitch(currentMode === "login" ? "register" : "login")}
                className="text-blue-600 hover:text-blue-700 hover:underline font-medium transition-colors duration-200"
              >
                {currentMode === "login" ? "Create one here" : "Sign in here"}
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
