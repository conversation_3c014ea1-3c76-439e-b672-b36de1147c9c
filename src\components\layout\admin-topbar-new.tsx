"use client"

import Image from "next/image"
import Link from "next/link"
import { useAdminAuth } from "../auth/admin-auth-context"
import { useEffect, useState } from "react"
import { cn } from "@/lib/utils"

export function AdminTopbar() {
  const { user } = useAdminAuth()
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  if (!isMounted) {
    return null
  }

  return (
    <header className={cn(
      "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-all duration-200",
      isScrolled && "shadow-sm"
    )}>
      <div className="flex h-14 items-center justify-between px-4 md:px-6">
        <div className="flex items-center gap-4">
          {/* Logo */}
          <Link href="/admin" className="flex items-center gap-2">
            <Image
              src="/ollie_logo.svg"
              alt="Ollie Track Logo"
              width={32}
              height={32}
              className="h-8 w-8"
            />
            <span className="font-semibold text-lg hidden sm:inline-block">
              Admin Portal
            </span>
          </Link>
        </div>

        <div className="flex items-center gap-4">
          {user && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>Welcome, {user.email?.split("@")[0]}</span>
            </div>
          )}
        </div>
      </div>
    </header>
  )
}
