import { PublicAppShell } from "@/components/layout/public-app-shell"
import { CustomerContactLoading as ContactLoadingIndicator } from "@/components/customer-side/loading/customer-loading"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function CustomerContactLoading() {
  return (
    <PublicAppShell>
      <div className="space-y-8 px-4 md:px-6 py-8" data-testid="customer-loading-page">
        {/* Page header skeleton */}
        <div className="text-center space-y-4">
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded w-64 mx-auto animate-pulse"></div>
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-96 mx-auto animate-pulse"></div>
        </div>

        {/* Loading indicator */}
        <div className="flex flex-col items-center justify-center py-8">
          <ContactLoadingIndicator />
        </div>

        {/* Contact cards skeleton */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {Array.from({ length: 3 }, (_, i) => (
            <Card key={i} className="text-center">
              <CardHeader>
                <div className="h-16 w-16 bg-gray-200 dark:bg-gray-700 rounded-full mx-auto mb-4 animate-pulse"></div>
                <CardTitle>
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-24 mx-auto animate-pulse"></div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32 mx-auto animate-pulse"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-28 mx-auto animate-pulse"></div>
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full mt-4 animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Contact form skeleton */}
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle>
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-40 animate-pulse"></div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {Array.from({ length: 4 }, (_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse"></div>
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full animate-pulse"></div>
              </div>
            ))}
            <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
          </CardContent>
        </Card>
      </div>
    </PublicAppShell>
  )
}
