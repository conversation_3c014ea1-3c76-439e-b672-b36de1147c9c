"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"
import { AlertCircle, CheckCircle } from "lucide-react"

interface EnhancedInputProps extends Omit<React.ComponentProps<"input">, "size"> {
  label?: string
  description?: string
  error?: string
  success?: string
  size?: "sm" | "md" | "lg"
  variant?: "default" | "filled" | "outlined"
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  showValidation?: boolean
}

const EnhancedInput = React.forwardRef<HTMLInputElement, EnhancedInputProps>(
  ({ 
    className, 
    type = "text", 
    label, 
    description, 
    error, 
    success, 
    size = "md", 
    variant = "default",
    leftIcon,
    rightIcon,
    showValidation = true,
    disabled,
    required,
    ...props 
  }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false)
    const [hasValue, setHasValue] = React.useState(!!props.value || !!props.defaultValue)
    
    const inputId = React.useId()
    const descriptionId = React.useId()
    const errorId = React.useId()

    // Size variants
    const sizeClasses = {
      sm: "h-8 px-2 py-1 text-sm",
      md: "h-10 px-3 py-2 text-base md:text-sm", 
      lg: "h-12 px-4 py-3 text-lg md:text-base"
    }

    // Variant styles
    const variantClasses = {
      default: "border-input bg-transparent",
      filled: "border-input bg-muted/30",
      outlined: "border-2 border-input bg-transparent"
    }

    // State classes
    const getStateClasses = () => {
      if (disabled) return "opacity-50 cursor-not-allowed pointer-events-none"
      if (error) return "border-destructive focus-visible:border-destructive focus-visible:ring-destructive/20"
      if (success) return "border-green-500 focus-visible:border-green-500 focus-visible:ring-green-500/20"
      return "focus-visible:border-ring focus-visible:ring-ring/20"
    }

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true)
      props.onFocus?.(e)
    }

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false)
      props.onBlur?.(e)
    }

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(!!e.target.value)
      props.onChange?.(e)
    }

    // Enhanced placeholder text based on input type
    const getEnhancedPlaceholder = () => {
      if (props.placeholder) return props.placeholder
      
      switch (type) {
        case "email":
          return "<EMAIL>"
        case "tel":
          return "+63 9XX XXX XXXX"
        case "password":
          return "Enter your password"
        case "number":
          return "0"
        case "url":
          return "https://example.com"
        case "date":
          return "MM/DD/YYYY"
        case "time":
          return "HH:MM"
        default:
          return `Enter ${label?.toLowerCase() || 'value'}`
      }
    }

    return (
      <div className="space-y-2">
        {/* Label */}
        {label && (
          <Label 
            htmlFor={inputId} 
            className={cn(
              "text-sm font-medium",
              error && "text-destructive",
              success && "text-green-700",
              disabled && "opacity-50"
            )}
          >
            {label}
            {required && <span className="text-destructive ml-1">*</span>}
          </Label>
        )}

        {/* Description */}
        {description && (
          <p 
            id={descriptionId}
            className={cn(
              "text-xs text-muted-foreground",
              disabled && "opacity-50"
            )}
          >
            {description}
          </p>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {leftIcon}
            </div>
          )}

          {/* Input Field */}
          <input
            id={inputId}
            ref={ref}
            type={type}
            disabled={disabled}
            required={required}
            placeholder={getEnhancedPlaceholder()}
            className={cn(
              // Base styles
              "flex w-full min-w-0 rounded-md border shadow-xs transition-all duration-200 outline-none",
              sizeClasses[size],
              variantClasses[variant],
              
              // State styles
              getStateClasses(),
              
              // Focus styles
              "focus-visible:ring-[3px]",
              
              // Placeholder styles
              "placeholder:text-muted-foreground/60",
              
              // Icon spacing
              leftIcon && "pl-10",
              (rightIcon || showValidation) && "pr-10",
              
              // Hover styles (when not disabled)
              !disabled && "hover:border-muted-foreground/30",
              
              // Custom styles
              className
            )}
            aria-describedby={cn(
              description && descriptionId,
              error && errorId
            )}
            aria-invalid={!!error}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            {...props}
          />

          {/* Right Icon or Validation Icon */}
          {(rightIcon || (showValidation && (error || success))) && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              {rightIcon || (
                showValidation && (
                  error ? (
                    <AlertCircle className="h-4 w-4 text-destructive" />
                  ) : success ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : null
                )
              )}
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <p 
            id={errorId}
            className="text-xs text-destructive flex items-center gap-1"
            role="alert"
          >
            <AlertCircle className="h-3 w-3 flex-shrink-0" />
            {error}
          </p>
        )}

        {/* Success Message */}
        {success && !error && (
          <p className="text-xs text-green-600 flex items-center gap-1">
            <CheckCircle className="h-3 w-3 flex-shrink-0" />
            {success}
          </p>
        )}
      </div>
    )
  }
)

EnhancedInput.displayName = "EnhancedInput"

export { EnhancedInput }
