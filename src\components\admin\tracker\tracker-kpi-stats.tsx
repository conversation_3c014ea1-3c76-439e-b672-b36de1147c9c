import { Card, CardContent } from '@/components/ui/card'
import { Car, Activity, Clock, AlertTriangle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TrackerKPIStatsProps {
  stats: {
    totalCars: number
    activeCars: number
    idleCars: number
    offlineCars: number
  }
}

export function TrackerKPIStats({ stats }: TrackerKPIStatsProps) {
  const kpiCards = [
    {
      icon: Car,
      label: 'Total Vehicles',
      value: stats.totalCars,
      iconColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      icon: Activity,
      label: 'Active',
      value: stats.activeCars,
      iconColor: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      textColor: 'text-green-700'
    },
    {
      icon: Clock,
      label: 'Idle',
      value: stats.idleCars,
      iconColor: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      textColor: 'text-yellow-700'
    },
    {
      icon: AlertTriangle,
      label: 'Offline',
      value: stats.offlineCars,
      iconColor: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      textColor: 'text-red-700'
    }
  ]

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
      {kpiCards.map((card, index) => {
        const Icon = card.icon
        return (
          <Card 
            key={index}
            className={cn(
              "transition-all duration-200 hover:shadow-md border",
              card.borderColor
            )}
          >
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className={cn(
                  "flex items-center justify-center w-10 h-10 rounded-lg",
                  card.bgColor
                )}>
                  <Icon className={cn("w-5 h-5", card.iconColor)} />
                </div>
                <div className="flex-1 min-w-0">
                  <div className={cn(
                    "text-2xl font-bold tabular-nums",
                    card.textColor || "text-foreground"
                  )}>
                    {card.value}
                  </div>
                  <div className="text-sm text-muted-foreground font-medium">
                    {card.label}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
