-- ============================================
-- BOOKING DOCUMENTS TABLE
-- ============================================
-- Stores document uploads required for bookings
-- Based on the 4 required document types in booking flow

CREATE TABLE public.booking_documents (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  booking_id UUID NOT NULL REFERENCES public.bookings(id) ON DELETE CASCADE,
  document_type TEXT NOT NULL CHECK (document_type IN (
    'drivers_license',
    'government_id', 
    'proof_of_age',
    'security_deposit_confirmation'
  )),
  file_url TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL, -- e.g., 'application/pdf', 'image/jpeg'
  verification_status TEXT NOT NULL DEFAULT 'pending' CHECK (verification_status IN (
    'pending',
    'approved',
    'rejected',
    'requires_resubmission'
  )),
  verification_notes TEXT, -- Admin notes for rejected documents
  verified_by UUID REFERENCES public.profiles(id), -- Admin who verified
  verified_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure only one document per type per booking
  UNIQUE(booking_id, document_type)
);

-- Enable RLS for booking_documents
ALTER TABLE public.booking_documents ENABLE ROW LEVEL SECURITY;

-- RLS Policies for booking_documents
CREATE POLICY "Users can view documents for their own bookings" 
  ON public.booking_documents FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.bookings 
      WHERE bookings.id = booking_id 
      AND bookings.customer_id = (SELECT auth.uid())
    ) OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Users can upload documents for their own bookings" 
  ON public.booking_documents FOR INSERT 
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.bookings 
      WHERE bookings.id = booking_id 
      AND bookings.customer_id = (SELECT auth.uid())
    )
  );

CREATE POLICY "Users can update documents for their own bookings or admins can update any" 
  ON public.booking_documents FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.bookings 
      WHERE bookings.id = booking_id 
      AND bookings.customer_id = (SELECT auth.uid())
    ) OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.bookings 
      WHERE bookings.id = booking_id 
      AND bookings.customer_id = (SELECT auth.uid())
    ) OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- Create indexes for better performance
CREATE INDEX idx_booking_documents_booking_id ON public.booking_documents(booking_id);
CREATE INDEX idx_booking_documents_type ON public.booking_documents(document_type);
CREATE INDEX idx_booking_documents_status ON public.booking_documents(verification_status);
CREATE INDEX idx_booking_documents_created ON public.booking_documents(created_at);

-- Create updated_at trigger
CREATE TRIGGER handle_updated_at_booking_documents 
  BEFORE UPDATE ON public.booking_documents 
  FOR EACH ROW 
  EXECUTE FUNCTION handle_updated_at();

-- Helper function to check if all required documents are uploaded for a booking
CREATE OR REPLACE FUNCTION check_booking_documents_complete(booking_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    required_docs TEXT[] := ARRAY['drivers_license', 'government_id', 'proof_of_age', 'security_deposit_confirmation'];
    uploaded_count INTEGER;
BEGIN
    SELECT COUNT(DISTINCT document_type) 
    INTO uploaded_count
    FROM public.booking_documents 
    WHERE booking_id = booking_uuid 
    AND document_type = ANY(required_docs);
    
    RETURN uploaded_count = array_length(required_docs, 1);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get booking document status summary
CREATE OR REPLACE FUNCTION get_booking_document_status(booking_uuid UUID)
RETURNS TABLE (
    total_required INTEGER,
    uploaded_count INTEGER,
    pending_count INTEGER,
    approved_count INTEGER,
    rejected_count INTEGER,
    is_complete BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        4 as total_required,
        COUNT(*)::INTEGER as uploaded_count,
        COUNT(*) FILTER (WHERE verification_status = 'pending')::INTEGER as pending_count,
        COUNT(*) FILTER (WHERE verification_status = 'approved')::INTEGER as approved_count,
        COUNT(*) FILTER (WHERE verification_status = 'rejected')::INTEGER as rejected_count,
        check_booking_documents_complete(booking_uuid) as is_complete
    FROM public.booking_documents 
    WHERE booking_id = booking_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
