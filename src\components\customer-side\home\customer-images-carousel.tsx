'use client'

import React, { useEffect, useCallback } from 'react'
import Image from 'next/image'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  CarouselApi,
} from "@/components/ui/carousel"
import { Card, CardContent } from "@/components/ui/card"

// Customer images data with testimonials
const customerImages = [
  {
    src: "/customer_1.jpg",
    alt: "Happy customer with rental car",
    name: "Maria Santos",
    location: "Vigan, Ilocos Sur",
    testimonial: "Excellent service and clean vehicles!"
  },
  {
    src: "/customer_2.jpg", 
    alt: "Family enjoying road trip",
    name: "The Rodriguez Family",
    location: "Laoag, Ilocos Norte",
    testimonial: "Perfect for our family vacation!"
  },
  {
    src: "/customer_3.jpg",
    alt: "Business traveler with premium vehicle", 
    name: "John Dela Cruz",
    location: "Batac, Ilocos Norte",
    testimonial: "Professional and reliable service."
  },
  {
    src: "/customer_4.jpg",
    alt: "Tourist exploring Ilocos",
    name: "<PERSON>",
    location: "Pagudpud, Ilocos Norte",
    testimonial: "Made our Ilocos trip unforgettable!"
  },
  {
    src: "/customer_5.jpg",
    alt: "Weekend getaway couple",
    name: "Mark & Lisa",
    location: "San Juan, La Union",
    testimonial: "Best car rental experience ever!"
  },
]

export function CustomerImagesCarousel() {
  const [api, setApi] = React.useState<CarouselApi>()
  const [current, setCurrent] = React.useState(0)

  const scrollNext = useCallback(() => {
    if (api) {
      api.scrollNext()
    }
  }, [api])

  // Auto-scroll functionality
  useEffect(() => {
    if (!api) return

    const interval = setInterval(() => {
      scrollNext()
    }, 5000) // Change slide every 5 seconds

    return () => clearInterval(interval)
  }, [api, scrollNext])

  // Update current slide index
  useEffect(() => {
    if (!api) return

    setCurrent(api.selectedScrollSnap() + 1)

    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1)
    })
  }, [api])

  return (
    <div className="relative">
      <Carousel 
        setApi={setApi}
        className="w-full"
        opts={{
          align: "start",
          loop: true,
        }}
      >
        <CarouselContent>
          {customerImages.map((customer, index) => (
            <CarouselItem key={index}>
              <div className="relative">
                <Card className="border-0 shadow-2xl overflow-hidden">
                  <CardContent className="p-0 relative">
                    <div className="relative aspect-[4/3] lg:aspect-[3/2]">
                      <Image
                        src={customer.src}
                        alt={customer.alt}
                        fill
                        className="object-cover transition-transform duration-700 hover:scale-105"
                        sizes="(max-width: 768px) 100vw, 600px"
                        priority={index === 0}
                      />
                      {/* Overlay gradient */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                      
                      {/* Customer info overlay */}
                      <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                        <div className="bg-black/40 backdrop-blur-sm rounded-xl p-6 border border-white/10">
                          <h3 className="font-bold text-xl mb-2">{customer.name}</h3>
                          <p className="text-blue-200 text-sm mb-3 flex items-center">
                            <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                            {customer.location}
                          </p>
                          <p className="text-gray-200 text-sm italic">"{customer.testimonial}"</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="left-4 bg-white/90 hover:bg-white text-gray-800 border-white/50 shadow-lg" />
        <CarouselNext className="right-4 bg-white/90 hover:bg-white text-gray-800 border-white/50 shadow-lg" />
      </Carousel>
      
      {/* Carousel indicators */}
      <div className="flex justify-center mt-6 space-x-2">
        {customerImages.map((_, index) => (
          <button
            key={index}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === current - 1 
                ? 'bg-blue-500 scale-110' 
                : 'bg-white/50 hover:bg-white/80'
            }`}
            onClick={() => api?.scrollTo(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  )
}
