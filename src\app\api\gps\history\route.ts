import { NextRequest, NextResponse } from 'next/server'
import { createClient as createServerClient } from '@/lib/supabase/server'
import { createClient as createSbDirect } from '@supabase/supabase-js'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const carId = searchParams.get('carId')
    const from = searchParams.get('from')
    const to = searchParams.get('to')

    if (!carId || !from || !to) {
      return NextResponse.json(
        { error: 'Missing required parameters: carId, from, to' }, 
        { status: 400 }
      )
    }

    const serviceUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    const supabase = serviceUrl && serviceKey
      ? createSbDirect(serviceUrl, serviceKey)
      : await createServerClient()

    // Fetch historical GPS locations for the specified car and time range
    const { data, error } = await supabase
      .from('gps_locations')
      .select(`
        id,
        car_id,
        latitude,
        longitude,
        speed,
        heading,
        status,
        timestamp,
        created_at
      `)
      .eq('car_id', carId)
      .gte('timestamp', from)
      .lte('timestamp', to)
      .order('timestamp', { ascending: true })
      .limit(500) // Limit to prevent excessive data transfer

    if (error) {
      console.error('Database error fetching GPS history:', error)
      throw error
    }

    const points = data || []
    
    console.log(`📍 GPS History API: Returning ${points.length} historical location(s) for car ${carId}`)
    
    return NextResponse.json({ 
      data: points,
      carId,
      timeRange: { from, to },
      count: points.length
    })
  } catch (error) {
    console.error('GPS history fetch error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', details: String(error) }, 
      { status: 500 }
    )
  }
}
