# LilyGO T-Call A7670E GPS Tracker Setup Guide

## Overview

This guide will help you set up your LilyGO T-Call A7670E ESP32 development board to send real-time GPS data to your OllieTrack website. The device will send GPS coordinates, speed, and heading data every 10 seconds via HTTPS.

## Prerequisites

### Hardware Required
- LilyGO T-Call A7670E ESP32 development board
- 4G nano-SIM card with data plan
- GNSS antenna (included with board)
- LTE antenna (included with board)
- USB-C cable for programming
- Computer with Arduino IDE or PlatformIO

### Software Required
- Arduino IDE 2.x or PlatformIO
- Required Libraries:
  - TinyGSM
  - ArduinoHttpClient
  - ArduinoJson

## Step 1: Hardware Setup

### 1.1 Insert SIM Card
1. Power off the board
2. Insert your nano-SIM card into the SIM slot
3. Ensure the SIM card has an active data plan

### 1.2 Connect Antennas
1. Connect the GNSS antenna to the IPEX1 connector (labeled GNSS)
2. Connect the LTE antenna to the IPEX2 connector (labeled LTE)
3. Place the GNSS antenna with clear sky view (outdoor setup preferred)

### 1.3 Check DIP Switches
1. Ensure the 4G switch is ON (powers the A7670E module)
2. Ensure the USB switch is OFF (routes UART to ESP32)

## Step 2: Software Setup

### 2.1 Install Required Libraries

In Arduino IDE, go to Library Manager and install:
```
TinyGSM by Volodymyr Shymanskyy
ArduinoHttpClient by Arduino
ArduinoJson by Benoit Blanchon
```

### 2.2 Configure the Code

1. Open the `ollietrack_gps_main.cpp` file from `iot/GPS/src/`
2. Update these configuration values:

```cpp
// ====== OllieTrack Configuration ======
const char* OLLIETRACK_HOST = "your-domain.com";  // Your OllieTrack domain
const char* DEVICE_TOKEN = "your-secure-token";   // Set in your .env file
const char* CAR_ID = "lilygo-esp32-01";          // Unique device ID

// ====== Cellular Configuration ======
const char* APN = "internet";  // Your carrier's APN
const char* APN_USER = "";     // Usually empty
const char* APN_PASS = "";     // Usually empty
```

### 2.3 Common APN Settings

| Carrier | APN |
|---------|-----|
| Globe (Philippines) | `internet` |
| Smart (Philippines) | `smartlte` |
| T-Mobile (US) | `fast.t-mobile.com` |
| Verizon (US) | `vzwinternet` |
| Vodafone (EU) | `internet` |

## Step 3: Backend Configuration

### 3.1 Environment Variables

Add these to your `.env.local` file:

```env
# GPS Data Source
NEXT_PUBLIC_GPS_SOURCE=api
NEXT_PUBLIC_GPS_STALE_SECONDS=60

# Device Authentication
INBOUND_DEVICE_TOKEN=your-secure-device-token-here

# Supabase Configuration (if not already set)
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 3.2 Device Token Security

Generate a secure device token:
```bash
# Use a strong random token
openssl rand -hex 32
```

Use this same token in both:
- Arduino code (`DEVICE_TOKEN`)
- Environment variable (`INBOUND_DEVICE_TOKEN`)

## Step 4: Upload and Test

### 4.1 Upload the Code

1. Select board: **ESP32 Dev Module**
2. Connect your LilyGO board via USB-C
3. Select the correct COM port
4. Upload the code

### 4.2 Monitor Serial Output

1. Open Serial Monitor at 115200 baud
2. Look for these status messages:
   ```
   🚀 OllieTrack GPS Tracker Starting...
   ✅ AT communication established
   ✅ A7670E detected
   ✅ Cellular connection established
   ✅ Successfully connected to OllieTrack server
   ✅ GPS hardware enabled
   ```

### 4.3 GPS Fix Acquisition

1. Move the device outdoors for best GPS reception
2. Wait 1-5 minutes for initial GPS fix
3. Look for GPS coordinates in Serial Monitor:
   ```
   📍 Location: 14.599512, 120.984222
   🏃 Speed: 0.0 km/h | 🧭 Heading: 0°
   🛰️ Satellites: 8 visible, 6 used
   ✅ Data sent to OllieTrack successfully!
   ```

## Step 5: Verify on Website

### 5.1 Check Admin Tracker

1. Open your OllieTrack website
2. Navigate to `/admin/tracker`
3. You should see your device on the map as a green marker
4. Device will be labeled with your `CAR_ID`

### 5.2 Real-time Updates

- Location updates every 10 seconds
- Map refreshes every 5 seconds
- Status shows as "active" when moving, "idle" when stationary

## Troubleshooting

### No AT Communication
- Check DIP switches (4G=ON, USB=OFF)
- Verify power connections
- Try different USB cable
- Check Serial Monitor for error messages

### No Cellular Connection
- Verify SIM card is active with data plan
- Check APN settings for your carrier
- Ensure antennas are properly connected
- Check signal strength in your area

### No GPS Fix
- Move to outdoor location with clear sky view
- Wait longer (first fix can take 5-15 minutes)
- Check GNSS antenna connection
- Restart the device

### Data Not Appearing on Website
- Verify `OLLIETRACK_HOST` matches your domain
- Check device token matches environment variable
- Look for HTTP error codes in Serial Monitor
- Verify your server is accessible from the internet

### Low Power/Battery Issues
- Use quality USB cable for programming
- Consider external 5V power supply for continuous operation
- Check power consumption if using battery

## Device Deployment

### Vehicle Installation
1. Mount device securely in vehicle
2. Connect to 12V power via USB adapter
3. Route GNSS antenna to roof or dashboard
4. Ensure cellular antenna has clear placement
5. Test connectivity before final installation

### Power Management
- Device draws ~200-500mA during operation
- GPS acquisition uses most power
- Consider sleep modes for battery operation
- Monitor voltage if using external battery

## API Endpoints

Your device sends data to these endpoints:

### GPS Data Ingestion
```
POST /api/gps/ingest
Headers:
  Content-Type: application/json
  X-Device-Key: your-device-token

Body:
{
  "carId": "lilygo-esp32-01",
  "latitude": 14.599512,
  "longitude": 120.984222,
  "speed": 0.0,
  "heading": 0,
  "status": "idle"
}
```

### Current GPS Data
```
GET /api/gps/current
Returns: Array of current GPS locations
```

## Advanced Configuration

### Adjusting Update Intervals
```cpp
// In ollietrack_gps_main.cpp
static const uint32_t GPS_UPDATE_INTERVAL = 10000;  // 10 seconds
static const uint32_t STATUS_REPORT_INTERVAL = 60000;  // 1 minute
```

### Custom Device Naming
```cpp
const char* CAR_ID = "fleet-vehicle-01";  // Use descriptive names
```

### Production Security
- Use HTTPS (already configured)
- Implement device certificates for production
- Use secure device tokens
- Consider VPN for sensitive deployments

## Support

### Hardware Documentation
- [LilyGO T-Call A7670E Wiki](https://www.waveshare.com/wiki/ESP32-S3-A7670E-4G)
- [A7670E AT Commands Manual](https://www.simcom.com/product/SIM7670G.html)

### Software Libraries
- [TinyGSM Library](https://github.com/vshymanskyy/TinyGSM)
- [ArduinoHttpClient](https://github.com/arduino-libraries/ArduinoHttpClient)

### Common Issues
- Ensure proper antenna connections
- Verify SIM card data plan is active
- Check firewall settings for HTTPS connectivity
- Monitor serial output for detailed error messages

## Next Steps

Once your device is successfully sending data:

1. **Fleet Management**: Configure multiple devices with unique Car IDs
2. **Geofencing**: Add boundary alerts in your application
3. **Historical Data**: Implement route history and analytics
4. **Alerts**: Set up speed limits and movement notifications
5. **Mobile App**: Consider mobile companion app for field monitoring

Your LilyGO T-Call A7670E is now successfully integrated with OllieTrack for real-time GPS tracking!
