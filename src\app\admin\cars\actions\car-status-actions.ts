"use server";

import { createClient } from "@/lib/supabase/server";
import { logWithContext, logError } from "@/lib/utils/logger";

export async function syncCarAvailability() {
  const supabase = await createClient();
  
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (!user) {
    return { error: { message: "You must be logged in to sync car availability." } };
  }

  // Check if user is admin
  const { data: profile } = await supabase
    .from("profiles")
    .select("role")
    .eq("id", user.id)
    .single();

  if (!profile || (profile.role !== "admin" && profile.role !== "super_admin")) {
    return { error: { message: "Only admins can sync car availability." } };
  }

  try {
    logWithContext("CarStatusActions", "Starting car availability sync", { userId: user.id });

    // Get all active bookings
    const { data: activeBookings } = await supabase
      .from('bookings')
      .select('car_id, id, status')
      .eq('status', 'Active');

    // Get all cars
    const { data: cars } = await supabase
      .from('cars')
      .select('id, model, status')
      .eq('is_archived', false);

    if (!cars) {
      return { error: { message: "Failed to fetch cars data." } };
    }

    const activeCarIds = new Set(activeBookings?.map(b => b.car_id) || []);
    const updatedCars: Array<{id: string, model: string, oldStatus: string, newStatus: string}> = [];

    // Check and fix inconsistencies
    for (const car of cars) {
      const shouldBeRented = activeCarIds.has(car.id);
      let newStatus = car.status;

      if (shouldBeRented && car.status !== 'Rented') {
        newStatus = 'Rented';
      } else if (!shouldBeRented && car.status === 'Rented') {
        newStatus = 'Available';
      }

      if (newStatus !== car.status) {
        const { error: updateError } = await supabase
          .from('cars')
          .update({ status: newStatus })
          .eq('id', car.id);

        if (updateError) {
          logError("CarStatusActions", "Error updating car status", { carId: car.id, updateError });
        } else {
          updatedCars.push({
            id: car.id,
            model: car.model,
            oldStatus: car.status,
            newStatus: newStatus
          });
          logWithContext("CarStatusActions", "Updated car status", {
            carId: car.id,
            model: car.model,
            oldStatus: car.status,
            newStatus: newStatus
          });
        }
      }
    }

    return { 
      success: true, 
      message: `Sync completed. Updated ${updatedCars.length} car(s).`,
      updatedCars
    };
  } catch (error) {
    logError("CarStatusActions", "Error syncing car availability", error);
    return { error: { message: "Failed to sync car availability." } };
  }
}
