"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { MoreHorizontal } from "lucide-react";
import { cn } from "@/lib/utils";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { getBottomTabItems, getMoreMenuItems, NavItem } from "./navItems";
import { MoreMenu } from "./MoreMenu";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

interface BottomNavProps {
  className?: string;
}

// Safe hook that doesn't throw if outside provider
function useSafeCustomerAuth() {
  try {
    return useCustomerAuth();
  } catch (error) {
    // If we're not within a CustomerAuthProvider, return null user
    console.debug('BottomNav: outside provider');
    return { user: null };
  }
}

export function BottomNav({ className }: BottomNavProps) {
  const { user } = useSafeCustomerAuth();
  const pathname = usePathname();
  const [isMoreSheetOpen, setIsMoreSheetOpen] = React.useState(false);

  const isAuthenticated = Boolean(user);
  const bottomTabItems = React.useMemo(
    () => getBottomTabItems(isAuthenticated),
    [isAuthenticated]
  );
  const moreItems = React.useMemo(
    () => getMoreMenuItems(isAuthenticated, bottomTabItems),
    [isAuthenticated, bottomTabItems]
  );

  const isActive = React.useCallback(
    (href: string) => {
      if (href === "/") {
        return pathname === "/";
      }
      return pathname.startsWith(href);
    },
    [pathname]
  );

  const handleNavClick = () => {
    setIsMoreSheetOpen(false);
  };

  return (
    <>
      {/* Bottom Tab Bar */}
      <nav
        className={cn(
          "fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200",
          "safe-area-bottom bottom-nav nav-optimized overflow-x-hidden",
          className
        )}
        role="navigation"
        aria-label="Main navigation"
      >
        <div className="flex items-center justify-around px-2 py-2 max-w-screen-sm mx-auto min-w-0">
          {/* Primary Navigation Items */}
          {bottomTabItems.map((item) => {
            const active = isActive(item.href);
            const IconComponent = item.icon;

            return (
              <Link
                key={item.key}
                href={item.href}
                onClick={handleNavClick}
                className={cn(
                  "flex flex-col items-center justify-center min-w-0 flex-1 px-1 py-2 rounded-lg",
                  "min-h-[44px] touch-manipulation nav-transition nav-focus-visible", // Accessibility
                  active
                    ? "text-blue-600 bg-blue-50"
                    : "text-gray-600 hover:text-blue-600 hover:bg-gray-50 active:bg-gray-100"
                )}
                aria-current={active ? "page" : undefined}
              >
                <IconComponent
                  className={cn(
                    "h-5 w-5 mb-1 flex-shrink-0",
                    item.isAction && "h-6 w-6" // Slightly larger for CTA
                  )}
                />
                <span
                  className={cn(
                    "text-xs font-medium truncate w-full text-center leading-tight max-w-full",
                    item.isAction && "text-[11px] font-semibold"
                  )}
                >
                  {item.label}
                </span>
              </Link>
            );
          })}

          {/* More Menu */}
          {moreItems.length > 0 && (
            <Sheet open={isMoreSheetOpen} onOpenChange={setIsMoreSheetOpen}>
              <SheetTrigger asChild>
                <Button
                  variant="tertiary"
                  className={cn(
                    "flex flex-col items-center justify-center min-w-0 flex-1 px-1 py-2 rounded-lg",
                    "min-h-[44px] touch-manipulation nav-transition nav-focus-visible text-gray-600 hover:text-blue-600 hover:bg-gray-50 active:bg-gray-100"
                  )}
                  aria-label="More navigation options"
                >
                  <MoreHorizontal className="h-5 w-5 mb-1 flex-shrink-0" />
                  <span className="text-xs font-medium truncate w-full text-center leading-tight">
                    More
                  </span>
                </Button>
              </SheetTrigger>

              <SheetContent
                side="bottom"
                className="h-auto max-h-[60vh] bg-white border-gray-200 rounded-t-xl shadow-xl"
              >
                <SheetHeader className="border-b border-gray-100 pb-3">
                  <SheetTitle className="text-gray-900 text-lg font-semibold">
                    More Options
                  </SheetTitle>
                </SheetHeader>

                <div className="mt-4 pb-4">
                  <MoreMenu
                    items={moreItems}
                    onItemClick={() => setIsMoreSheetOpen(false)}
                    showUserActions={true}
                  />
                </div>
              </SheetContent>
            </Sheet>
          )}
        </div>
      </nav>

      {/* Bottom padding spacer to prevent content from being hidden behind fixed nav */}
      <div
        className="sm:hidden h-16 w-full flex-shrink-0 safe-area-bottom"
        aria-hidden="true"
      />
    </>
  );
}
