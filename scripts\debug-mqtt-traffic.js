#!/usr/bin/env node
/**
 * MQTT Traffic Debug Script
 * Monitors all GPS MQTT traffic to identify phantom data sources
 */
import mqtt from 'mqtt';

const BROKER_URL = 'mqtt://broker.emqx.io:1883';
const GPS_TOPICS = ['pathlink/gps/+', '#']; // Monitor all GPS topics and everything

console.log('🔍 MQTT Traffic Monitor Started...');
console.log(`🔗 Broker: ${BROKER_URL}`);
console.log('🎯 This will show ALL MQTT messages to help identify phantom GPS sources\n');

const client = mqtt.connect(BROKER_URL, {
  clientId: `debug_monitor_${Math.random().toString(16).substr(2, 8)}`,
  clean: true,
  keepalive: 60,
  connectTimeout: 30000
});

client.on('connect', () => {
  console.log('✅ Connected to EMQ Public Broker');
  
  GPS_TOPICS.forEach(topic => {
    console.log(`👂 Listening to topic: ${topic}`);
    client.subscribe(topic, { qos: 1 }, (error) => {
      if (error) {
        console.error(`❌ Failed to subscribe to ${topic}:`, error);
      } else {
        console.log(`✅ Subscribed to ${topic}`);
      }
    });
  });
  
  console.log('\n🚨 Monitoring MQTT traffic - Press Ctrl+C to stop\n');
});

client.on('message', (topic, payload) => {
  try {
    console.log(`\n📨 === MQTT MESSAGE DETECTED ===`);
    console.log(`📡 Topic: ${topic}`);
    console.log(`🕐 Time: ${new Date().toISOString()}`);
    console.log(`📦 Payload Length: ${payload.length} bytes`);
    
    // Try to parse as JSON
    let parsedData;
    try {
      parsedData = JSON.parse(payload.toString());
      console.log(`📄 Parsed JSON:`, JSON.stringify(parsedData, null, 2));
      
      // Analyze GPS data structure
      if (parsedData.lat && parsedData.lng) {
        console.log(`🎯 GPS DATA DETECTED:`);
        console.log(`   Device ID: ${parsedData.deviceId || 'UNKNOWN'}`);
        console.log(`   Coordinates: ${parsedData.lat}, ${parsedData.lng}`);
        console.log(`   Has Accuracy: ${parsedData.accuracy !== undefined ? '✅' : '❌'}`);
        console.log(`   Has Satellites: ${parsedData.satellites !== undefined ? '✅' : '❌'}`);
        console.log(`   Has Fix Mode: ${parsedData.fixMode !== undefined ? '✅' : '❌'}`);
        
        // Identify data source
        const isRealESP32 = (
          parsedData.accuracy !== undefined &&
          parsedData.satellites !== undefined &&
          parsedData.fixMode !== undefined
        );
        
        console.log(`🔍 SOURCE: ${isRealESP32 ? '🟢 REAL ESP32' : '🔴 PHANTOM/TEST'}`);
        
        if (!isRealESP32) {
          console.log(`⚠️  PHANTOM GPS SOURCE DETECTED!`);
          console.log(`   This is the source of your phantom GPS data when ESP32 is off`);
        }
      }
      
    } catch (parseError) {
      console.log(`📄 Raw Text: ${payload.toString()}`);
    }
    
    console.log(`================================\n`);
    
  } catch (error) {
    console.error('❌ Error processing message:', error);
  }
});

client.on('error', (error) => {
  console.error('❌ MQTT Connection Error:', error);
});

client.on('close', () => {
  console.log('🔌 Connection closed');
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down MQTT traffic monitor...');
  client.end();
  console.log('✅ Monitor stopped');
  process.exit(0);
});

// Auto-stop after 5 minutes to avoid running forever
setTimeout(() => {
  console.log('\n⏰ Auto-stopping monitor after 5 minutes...');
  client.end();
  process.exit(0);
}, 5 * 60 * 1000);
