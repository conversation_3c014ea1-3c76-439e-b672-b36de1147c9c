"use client"

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useCustomerAuth } from './customer-auth-context'

interface CustomerProtectionProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  allowPublic?: boolean // Allow unauthenticated users
}

export function CustomerProtection({ children, fallback, allowPublic = false }: CustomerProtectionProps) {
  const { user, profile, loading } = useCustomerAuth()
  const router = useRouter()

  // Check if user is admin or super admin based on role or email if profile is not available
  const superAdminEmail = process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAIL || '<EMAIL>'
  const isAdmin = profile?.role === 'admin' || profile?.role === 'super_admin' || user?.email === superAdminEmail

  useEffect(() => {
    if (!user && !allowPublic) {
      // Redirect unauthenticated users to customer login
      const currentPath = window.location.pathname
      router.replace(`/customer/login?redirect=${encodeURIComponent(currentPath)}`)
    }
  }, [user, router, allowPublic])

  // Show loading state while authentication is being resolved
  if (loading) {
    return (
      <div className="min-h-screen grid place-items-center bg-muted/20">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">Loading...</p>
            <p className="text-xs text-muted-foreground">Please wait...</p>
          </div>
        </div>
      </div>
    )
  }

  // If user is not authenticated and public access is not allowed
  if (!user && !allowPublic) {
    return fallback || (
      <div className="min-h-screen grid place-items-center bg-muted/20">
        <div className="text-center">
          <p>Authentication required. Redirecting to login...</p>
        </div>
      </div>
    )
  }

  // If user is authenticated but is admin, redirect to admin dashboard
  if (user && isAdmin) {
    router.replace('/admin')
    return (
      <div className="min-h-screen grid place-items-center bg-muted/20">
        <div className="text-center">
          <p>Admin user detected. Redirecting to admin dashboard...</p>
        </div>
      </div>
    )
  }

  // User is authenticated customer or public access is allowed, render children
  return <>{children}</>
}
