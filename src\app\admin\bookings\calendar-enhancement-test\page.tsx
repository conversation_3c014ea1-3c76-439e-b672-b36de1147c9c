"use client"

import * as React from "react"
import { BookingCalendar } from "@/components/admin/bookings/booking-calendar"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { format, addDays, subDays } from "date-fns"

// Mock booking data for testing
const mockBookings = [
  {
    id: "1",
    customer_id: "user1",
    car_id: "car1", 
    pickup_location: "Manila Airport",
    dropoff_location: "Manila Airport",
    pickup_datetime: new Date().toISOString(),
    dropoff_datetime: addDays(new Date(), 2).toISOString(),
    special_requests: undefined,
    status: "Active" as const,
    total_amount: 5000,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    userName: "Juan Dela Cruz",
    carModel: "Toyota Vios",
    from: new Date(),
    to: addDays(new Date(), 2),
    days: 3,
    payStatus: "Paid" as const,
    totalAmount: 5000,
  },
  {
    id: "2",
    customer_id: "user2", 
    car_id: "car2",
    pickup_location: "Makati CBD",
    dropoff_location: "BGC",
    pickup_datetime: addDays(new Date(), 1).toISOString(),
    dropoff_datetime: addDays(new Date(), 3).toISOString(),
    special_requests: undefined,
    status: "Pending" as const,
    total_amount: 7500,
    created_at: addDays(new Date(), 1).toISOString(),
    updated_at: addDays(new Date(), 1).toISOString(),
    userName: "Maria Santos",
    carModel: "Honda Civic",
    from: addDays(new Date(), 1),
    to: addDays(new Date(), 3),
    days: 3,
    payStatus: "Unpaid" as const,
    totalAmount: 7500,
  },
  {
    id: "3",
    customer_id: "user3",
    car_id: "car3", 
    pickup_location: "Cebu Airport",
    dropoff_location: "Cebu City",
    pickup_datetime: subDays(new Date(), 1).toISOString(),
    dropoff_datetime: addDays(new Date(), 1).toISOString(),
    special_requests: "Baby seat required",
    status: "Completed" as const,
    total_amount: 4200,
    created_at: subDays(new Date(), 2).toISOString(),
    updated_at: new Date().toISOString(),
    userName: "Carlos Mendoza",
    carModel: "Mitsubishi Mirage",
    from: subDays(new Date(), 1),
    to: addDays(new Date(), 1),
    days: 3,
    payStatus: "Paid" as const,
    totalAmount: 4200,
  },
  {
    id: "4",
    customer_id: "user4",
    car_id: "car4",
    pickup_location: "Davao Airport", 
    dropoff_location: "SM Lanang",
    pickup_datetime: addDays(new Date(), 3).toISOString(),
    dropoff_datetime: addDays(new Date(), 5).toISOString(),
    special_requests: undefined,
    status: "Cancelled" as const,
    total_amount: 6000,
    created_at: addDays(new Date(), 2).toISOString(),
    updated_at: addDays(new Date(), 2).toISOString(),
    userName: "Ana Rodriguez",
    carModel: "Nissan Almera",
    from: addDays(new Date(), 3),
    to: addDays(new Date(), 5),
    days: 3,
    payStatus: "Refunded" as const,
    totalAmount: 6000,
  }
]

export default function CalendarEnhancementTestPage() {
  const [view, setView] = React.useState<"week" | "month">("week")
  const [currentDate, setCurrentDate] = React.useState<Date>(new Date())
  const [selectedBooking, setSelectedBooking] = React.useState<any>(null)
  const [currentViewport, setCurrentViewport] = React.useState<string>("")

  // Track viewport size for testing
  React.useEffect(() => {
    const updateViewport = () => {
      const width = window.innerWidth
      let size = ""
      if (width < 375) size = "Mobile S (< 375px)"
      else if (width < 425) size = "Mobile M (375px - 424px)"
      else if (width < 768) size = "Mobile L (425px - 767px)"
      else if (width < 1024) size = "Tablet (768px - 1023px)"
      else size = "Laptop+ (≥ 1024px)"
      
      setCurrentViewport(`${width}px - ${size}`)
    }

    updateViewport()
    window.addEventListener('resize', updateViewport)
    return () => window.removeEventListener('resize', updateViewport)
  }, [])

  const handleEventClick = (booking: any) => {
    setSelectedBooking(booking)
    console.log("Clicked booking:", booking)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-700 text-white">
        <div className="max-w-7xl mx-auto p-4 xs:p-6 lg:p-8">
          <div className="flex flex-col xs:flex-row items-start xs:items-center justify-between gap-4">
            <div>
              <h1 className="text-2xl xs:text-3xl lg:text-4xl font-bold">
                📅 Calendar Enhancement Test
              </h1>
              <p className="text-blue-100 mt-2 text-sm xs:text-base">
                Testing responsive design across all breakpoints
              </p>
            </div>
            
            {/* Viewport Indicator */}
            <div className="bg-white/15 backdrop-blur-sm rounded-xl px-4 py-3 border border-white/20">
              <div className="text-xs xs:text-sm font-semibold text-white/90 mb-1">
                Current Viewport
              </div>
              <div className="text-sm xs:text-base font-bold text-white">
                {currentViewport}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-4 xs:p-6 lg:p-8 space-y-6">
        
        {/* Test Information */}
        <Card className="bg-white/80 backdrop-blur-sm border-2 border-blue-200 shadow-xl">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b-2 border-blue-200">
            <CardTitle className="text-lg xs:text-xl text-blue-900 flex items-center gap-2">
              🧪 Enhancement Testing Checklist
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 xs:p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              
              {/* UI Improvements */}
              <div>
                <h3 className="font-bold text-slate-900 mb-3 flex items-center gap-2">
                  🎨 UI Improvements
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">✓</Badge>
                    <span className="text-sm">Enhanced header with gradient background</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">✓</Badge>
                    <span className="text-sm">Improved typography and spacing</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">✓</Badge>
                    <span className="text-sm">Better visual hierarchy with shadows</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">✓</Badge>
                    <span className="text-sm">Enhanced booking event styling</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">✓</Badge>
                    <span className="text-sm">Gradient status indicators</span>
                  </div>
                </div>
              </div>

              {/* Responsive Features */}
              <div>
                <h3 className="font-bold text-slate-900 mb-3 flex items-center gap-2">
                  📱 Responsive Features
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">✓</Badge>
                    <span className="text-sm">Mobile S (320px) - Optimized spacing</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">✓</Badge>
                    <span className="text-sm">Mobile M (375px) - Better touch targets</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">✓</Badge>
                    <span className="text-sm">Mobile L (425px) - Readable text sizes</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">✓</Badge>
                    <span className="text-sm">Tablet (768px) - Enhanced layouts</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">✓</Badge>
                    <span className="text-sm">Laptop (1024px+) - Full features</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Controls */}
        <Card className="bg-white/80 backdrop-blur-sm border-2 border-purple-200 shadow-xl">
          <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b-2 border-purple-200">
            <CardTitle className="text-lg xs:text-xl text-purple-900 flex items-center gap-2">
              🎮 Test Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 xs:p-6">
            <div className="flex flex-wrap gap-3">
              <Button
                onClick={() => setView("week")}
                variant={view === "week" ? "primary" : "secondary"}
                className="flex-1 xs:flex-none"
              >
                Switch to Week View
              </Button>
              <Button
                onClick={() => setView("month")}
                variant={view === "month" ? "primary" : "secondary"}
                className="flex-1 xs:flex-none"
              >
                Switch to Month View
              </Button>
              <Button
                onClick={() => setCurrentDate(new Date())}
                variant="secondary"
                className="flex-1 xs:flex-none"
              >
                Reset to Today
              </Button>
            </div>
            
            {selectedBooking && (
              <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-semibold text-blue-900 mb-2">Last Clicked Booking:</h4>
                <p className="text-sm text-blue-800">
                  {selectedBooking.userName} - {selectedBooking.carModel} 
                  ({selectedBooking.status})
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  {format(selectedBooking.from, "MMM d, yyyy")} - {format(selectedBooking.to, "MMM d, yyyy")}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Enhanced Calendar Component */}
        <div className="space-y-6">
          <BookingCalendar
            bookings={mockBookings}
            onEventClick={handleEventClick}
            view={view}
            onViewChange={setView}
            currentDate={currentDate}
            onDateChange={setCurrentDate}
          />
        </div>

        {/* Testing Instructions */}
        <Card className="bg-white/80 backdrop-blur-sm border-2 border-green-200 shadow-xl">
          <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b-2 border-green-200">
            <CardTitle className="text-lg xs:text-xl text-green-900 flex items-center gap-2">
              📋 Testing Instructions
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 xs:p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              
              <div>
                <h3 className="font-bold text-slate-900 mb-3">🔍 What to Test</h3>
                <ol className="list-decimal list-inside space-y-2 text-sm text-slate-700">
                  <li>Resize browser window to test all breakpoints (320px - 1024px+)</li>
                  <li>Switch between Week and Month views</li>
                  <li>Click on booking events to test interactivity</li>
                  <li>Navigate between dates using controls</li>
                  <li>Check text readability at all screen sizes</li>
                  <li>Verify hover effects and animations work smoothly</li>
                  <li>Test touch targets on mobile devices</li>
                  <li>Confirm no horizontal scrolling on any screen size</li>
                </ol>
              </div>

              <div>
                <h3 className="font-bold text-slate-900 mb-3">✅ Expected Results</h3>
                <ul className="list-disc list-inside space-y-2 text-sm text-slate-700">
                  <li>Calendar remains fully functional at all screen sizes</li>
                  <li>Text is readable without being too small or too large</li>
                  <li>Booking events have proper visual hierarchy</li>
                  <li>Hover effects provide good user feedback</li>
                  <li>Today's date is clearly highlighted</li>
                  <li>Legend matches the booking event colors</li>
                  <li>Navigation controls work smoothly</li>
                  <li>No visual regressions or layout breaks</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
