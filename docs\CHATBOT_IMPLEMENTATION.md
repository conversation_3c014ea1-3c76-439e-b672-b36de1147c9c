# PathLink Chatbot Implementation

## Overview
The PathLink chatbot system is now fully implemented with OpenRouter integration and RAG (Retrieval-Augmented Generation) capabilities for both customer and admin interfaces.

## Components Implemented

### 1. Database Schema
- **File**: `database/chatbot-knowledge-schema.sql`
- **Tables**: 
  - `chatbot_knowledge` - Stores embedded knowledge for RAG
  - `chatbot_conversations` - Tracks user sessions
  - `chatbot_messages` - Stores conversation history
- **Features**: Vector similarity search, RLS policies, proper indexing

### 2. API Routes
- **Customer Chat**: `src/app/api/chat/customer/route.ts`
- **Admin Chat**: `src/app/api/chat/admin/route.ts`
- **Integration**: OpenRouter API with `openai/gpt-oss-20b:free` model
- **Authentication**: User verification and role-based access

### 3. Frontend Components
- **Customer Chatbot**: `src/components/customer-side/chatbot/customer-chatbot.tsx`
  - Blue theme, positioned bottom-right
  - Session management with unique IDs
  - Streaming responses with loading indicators

- **Admin Chatbot**: `src/components/admin/chatbot/admin-chatbot.tsx`
  - Orange theme with admin badge
  - Enhanced UI for administrative tasks
  - Role verification and secure access

### 4. Core Libraries
- **OpenRouter Config**: `src/lib/openrouter.ts`
  - Model: `openai/gpt-oss-20b:free`
  - Optimized settings for chat interactions

- **RAG System**: `src/lib/rag.ts`
  - Vector embedding generation
  - Knowledge base search functionality
  - Context building for enhanced responses

### 5. Knowledge Base
- **Schema**: Vector-enabled PostgreSQL with pgvector
- **Seed Data**: `database/chatbot-seed-data.sql`
- **Categories**: 
  - Customer: booking, payments, vehicles, support, general
  - Admin: booking management, GPS tracking, system operations

## Environment Variables Required

Add to your `.env.local`:
```env
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENAI_API_KEY=your_openai_api_key_here  # Optional, for embeddings
```

## Setup Instructions

1. **Database Setup**:
   ```bash
   # Run the schema creation
   psql -f database/chatbot-knowledge-schema.sql

   # Seed the knowledge base
   psql -f database/chatbot-seed-data.sql
   ```

2. **Environment Configuration**:
   - Add OpenRouter API key to your environment
   - Optionally add OpenAI API key for embedding generation

3. **Dependencies**:
   - All required packages already installed (`@ai-sdk/openai`, `@ai-sdk/react`, `ai`, `openai`)

## Features

### Customer Chatbot
- ✅ Car rental inquiries and booking assistance
- ✅ Payment and document requirements help
- ✅ Location services integration
- ✅ Service area information
- ✅ Booking status support

### Admin Chatbot
- ✅ Booking management assistance
- ✅ Document verification guidance
- ✅ GPS tracking and fleet management help
- ✅ Payment verification support
- ✅ System troubleshooting assistance

### Technical Features
- ✅ Real-time streaming responses
- ✅ Session persistence
- ✅ Role-based access control
- ✅ RAG-enhanced contextual responses
- ✅ Vector similarity search
- ✅ Responsive UI design

## Integration Points

### With Existing Systems
- **Authentication**: Uses existing Supabase auth system
- **Database**: Integrates with current PathLink schema
- **Location Services**: References `philippines-locations.ts` for pickup/dropoff
- **User Roles**: Supports customer/admin/super_admin roles
- **GPS Data**: Admin chatbot can provide insights on `gps_locations` and `car_routes`

### API Endpoints Used
- `/api/chat/customer` - Customer support chatbot
- `/api/chat/admin` - Administrative assistant chatbot

## Security Features
- Row Level Security (RLS) on all chatbot tables
- User authentication verification
- Role-based access control
- Session-based conversation tracking
- Secure API key handling

## Performance Optimizations
- Vector indexing for fast similarity search
- Efficient embedding caching
- Streaming responses for better UX
- Optimized database queries
- Smart context limiting

## Monitoring & Analytics
- Conversation tracking in database
- User session management
- Error logging and handling
- Performance metrics available

## Next Steps
1. Test the chatbot functionality thoroughly
2. Monitor API usage and costs
3. Expand knowledge base as needed
4. Gather user feedback for improvements
5. Consider adding more advanced features like conversation memory

## Support
For issues or questions about the chatbot implementation, refer to the knowledge base or contact the development team.
