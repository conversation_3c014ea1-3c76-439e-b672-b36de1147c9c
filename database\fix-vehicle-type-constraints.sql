-- Fix vehicle type constraints to only use vehicle categories
-- This will restrict car types to only MPV, SUV, SEDAN

-- First, update any existing cars with invalid types
UPDATE cars 
SET type = CASE 
  WHEN type = 'Hatchback' THEN 'Sedan'
  WHEN type = 'Sport' THEN 'Sedan' 
  WHEN type = 'Coupe' THEN 'Sedan'
  ELSE type
END
WHERE type IN ('Hatchback', 'Sport', 'Coupe');

-- Drop the old constraint
ALTER TABLE cars DROP CONSTRAINT IF EXISTS cars_type_check;

-- Add new constraint with only the 3 vehicle category types
ALTER TABLE cars ADD CONSTRAINT cars_type_check 
CHECK (type = ANY (ARRAY['SUV'::text, 'MPV'::text, 'Sedan'::text]));

-- Update any cars that might have been missed
UPDATE cars SET type = 'Sedan' WHERE type NOT IN ('SUV', 'MPV', 'Sedan');
