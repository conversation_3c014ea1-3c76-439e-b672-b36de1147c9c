-- Fix RLS policies to allow super_admin role access
-- Updates all admin-only policies to include super_admin role

BEGIN;

-- ========================================
-- CARS TABLE - Allow super_admin to manage cars
-- ========================================

DROP POLICY IF EXISTS "Only admins can insert cars" ON public.cars;
CREATE POLICY "Only admins can insert cars" 
  ON public.cars FOR INSERT 
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Only admins can update cars" ON public.cars;
CREATE POLICY "Only admins can update cars" 
  ON public.cars FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Only admins can delete cars" ON public.cars;
CREATE POLICY "Only admins can delete cars" 
  ON public.cars FOR DELETE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- BOOKINGS TABLE - Allow super_admin to manage bookings
-- ========================================

DROP POLICY IF EXISTS "Admins can view all bookings" ON public.bookings;
CREATE POLICY "Admins can view all bookings" 
  ON public.bookings FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Only admins can update bookings" ON public.bookings;
CREATE POLICY "Only admins can update bookings" 
  ON public.bookings FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Only admins can delete bookings" ON public.bookings;
CREATE POLICY "Only admins can delete bookings" 
  ON public.bookings FOR DELETE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- PAYMENTS TABLE - Allow super_admin to manage payments
-- ========================================

DROP POLICY IF EXISTS "Admins can view all payments" ON public.payments;
CREATE POLICY "Admins can view all payments" 
  ON public.payments FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Admins can verify payments" ON public.payments;
CREATE POLICY "Admins can verify payments" 
  ON public.payments FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- VEHICLE CATEGORIES - Allow super_admin full access
-- ========================================

DROP POLICY IF EXISTS "Allow admin full access to vehicle categories" ON public.vehicle_categories;
CREATE POLICY "Allow admin full access to vehicle categories" 
  ON public.vehicle_categories FOR ALL 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- RENTER STATUS TABLE - Allow super_admin to manage
-- ========================================

DROP POLICY IF EXISTS "Only admins can view renter status" ON public.renter_status;
CREATE POLICY "Only admins can view renter status" 
  ON public.renter_status FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Only admins can manage renter status" ON public.renter_status;
CREATE POLICY "Only admins can manage renter status" 
  ON public.renter_status FOR ALL 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- ISSUE CATEGORIES - Allow super_admin to manage
-- ========================================

DROP POLICY IF EXISTS "Issue categories are viewable by admins" ON public.issue_categories;
CREATE POLICY "Issue categories are viewable by admins" 
  ON public.issue_categories FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Only admins can manage issue categories" ON public.issue_categories;
CREATE POLICY "Only admins can manage issue categories" 
  ON public.issue_categories FOR ALL 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- RENTER ISSUES - Allow super_admin to manage
-- ========================================

DROP POLICY IF EXISTS "Only admins can view renter issues" ON public.renter_issues;
CREATE POLICY "Only admins can view renter issues" 
  ON public.renter_issues FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Only admins can manage renter issues" ON public.renter_issues;
CREATE POLICY "Only admins can manage renter issues" 
  ON public.renter_issues FOR ALL 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- RENTER CATEGORY TAGS - Allow super_admin to manage
-- ========================================

DROP POLICY IF EXISTS "Only admins can view renter category tags" ON public.renter_category_tags;
CREATE POLICY "Only admins can view renter category tags" 
  ON public.renter_category_tags FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Only admins can manage renter category tags" ON public.renter_category_tags;
CREATE POLICY "Only admins can manage renter category tags" 
  ON public.renter_category_tags FOR ALL 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- USER LEGAL DOCUMENTS - Allow super_admin to manage
-- ========================================

DROP POLICY IF EXISTS "Users can update their own legal documents or admins can update any" ON public.user_legal_documents;
CREATE POLICY "Users can update their own legal documents or admins can update any" 
  ON public.user_legal_documents FOR UPDATE 
  TO authenticated
  USING (
    user_id = (SELECT auth.uid()) 
    OR EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Users can delete their own legal documents or admins can delete any" ON public.user_legal_documents;
CREATE POLICY "Users can delete their own legal documents or admins can delete any" 
  ON public.user_legal_documents FOR DELETE 
  TO authenticated
  USING (
    user_id = (SELECT auth.uid()) 
    OR EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- BOOKING DOCUMENTS - Allow super_admin to manage
-- ========================================

DROP POLICY IF EXISTS "Admins can view all booking documents" ON public.booking_documents;
CREATE POLICY "Admins can view all booking documents" 
  ON public.booking_documents FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Admins can update booking documents" ON public.booking_documents;
CREATE POLICY "Admins can update booking documents" 
  ON public.booking_documents FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

COMMIT;

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Verify policies are updated
SELECT 
  schemaname, 
  tablename, 
  policyname,
  cmd
FROM pg_policies 
WHERE schemaname = 'public' 
  AND (
    policyname ILIKE '%admin%' 
    OR policyname ILIKE '%manage%'
  )
ORDER BY tablename, policyname;
