"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { ChevronRight, Home } from "lucide-react"
import { cn } from "@/lib/utils"

interface BreadcrumbItem {
  label: string
  href?: string
  icon?: React.ComponentType<{ className?: string }>
}

const routeMap: Record<string, BreadcrumbItem[]> = {
  "/": [
    { label: "Home", href: "/", icon: Home }
  ],
  "/customer/catalog": [
    { label: "Home", href: "/", icon: Home },
    { label: "Vehicle Catalog", href: "/customer/catalog" }
  ],
  "/customer/dashboard": [
    { label: "Home", href: "/", icon: Home },
    { label: "Dashboard", href: "/customer/dashboard" }
  ],
  "/faq": [
    { label: "Home", href: "/", icon: Home },
    { label: "FAQ", href: "/faq" }
  ],
  "/contact": [
    { label: "Home", href: "/", icon: Home },
    { label: "Contact Us", href: "/contact" }
  ],
  "/terms": [
    { label: "Home", href: "/", icon: Home },
    { label: "Terms & Conditions", href: "/terms" }
  ],
  "/settings": [
    { label: "Home", href: "/", icon: Home },
    { label: "Settings", href: "/settings" }
  ],
  "/admin": [
    { label: "Admin", href: "/admin", icon: Home }
  ],
  "/admin/cars": [
    { label: "Admin", href: "/admin", icon: Home },
    { label: "Cars Management", href: "/admin/cars" }
  ],
  "/admin/car-availability": [
    { label: "Admin", href: "/admin", icon: Home },
    { label: "Car Availability", href: "/admin/car-availability" }
  ],
  "/admin/bookings": [
    { label: "Admin", href: "/admin", icon: Home },
    { label: "Bookings", href: "/admin/bookings" }
  ],
  "/admin/sales-tracking": [
    { label: "Admin", href: "/admin", icon: Home },
    { label: "Sales Tracking", href: "/admin/sales-tracking" }
  ],
  "/admin/tracker": [
    { label: "Admin", href: "/admin", icon: Home },
    { label: "GPS Tracker", href: "/admin/tracker" }
  ],
  "/admin/payments": [
    { label: "Admin", href: "/admin", icon: Home },
    { label: "Payments", href: "/admin/payments" }
  ],
  "/admin/settings": [
    { label: "Admin", href: "/admin", icon: Home },
    { label: "Settings", href: "/admin/settings" }
  ]
}

interface BreadcrumbNavProps {
  className?: string
  showOnHome?: boolean
}

export function BreadcrumbNav({ className, showOnHome = false }: BreadcrumbNavProps) {
  const pathname = usePathname()
  
  // Don't show breadcrumbs on auth pages
  if (pathname.startsWith('/auth/')) return null
  
  const breadcrumbs = routeMap[pathname] || []
  
  // Don't show on home unless explicitly requested
  if (pathname === "/" && !showOnHome) return null
  
  // Don't show if only one item
  if (breadcrumbs.length <= 1) return null

  return (
    <nav 
      aria-label="Breadcrumb" 
      className={cn("flex items-center space-x-1 text-sm text-gray-600", className)}
    >
      {breadcrumbs.map((item, index) => {
        const isLast = index === breadcrumbs.length - 1
        const Icon = item.icon
        
        return (
          <div key={item.href || item.label} className="flex items-center">
            {index > 0 && (
              <ChevronRight className="h-4 w-4 text-gray-400 mx-2" />
            )}
            
            {isLast ? (
              <span className="flex items-center gap-1.5 font-medium text-gray-900">
                {Icon && <Icon className="h-4 w-4" />}
                {item.label}
              </span>
            ) : (
              <Link
                href={item.href!}
                className="flex items-center gap-1.5 hover:text-blue-600 transition-colors"
              >
                {Icon && <Icon className="h-4 w-4" />}
                {item.label}
              </Link>
            )}
          </div>
        )
      })}
    </nav>
  )
}

// Simplified breadcrumb for page headers
export function PageBreadcrumb() {
  return (
    <div className="mb-6">
      <BreadcrumbNav className="mb-2" />
    </div>
  )
}
