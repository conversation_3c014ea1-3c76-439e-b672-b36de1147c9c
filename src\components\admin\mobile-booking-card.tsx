"use client"

import { useState } from "react"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogOverlay, DialogClose } from "@/components/ui/dialog"
import { formatCurrency } from "@/lib/utils/format-currency"
import { formatBookingIdForDisplay } from "@/lib/reference-ids"
import { CarIcon, Clock, CalendarCheck, MapPin, FileText, UserIcon, Mail, CreditCard } from "lucide-react"
import type { Booking, Car, User, Payment } from "@/lib/types"

interface MobileBookingCardProps {
  booking: Booking
  car: Car
  customer: User
  payment?: Payment | null
}

export function MobileBookingCard({ booking, car, customer, payment }: MobileBookingCardProps) {
  const [isOpen, setIsOpen] = useState(false)
  
  // Helper function to get status badge color
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
      case 'active':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300';
      case 'pending':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  // Format dates
  const formatDate = (isoString: string) => {
    const date = new Date(isoString)
    return date.toLocaleDateString("en-US", { 
      month: "short", 
      day: "numeric", 
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    })
  }

  // Get customer name
  const customerName = customer?.full_name || customer?.name || "Unknown Customer"
  
  // Get status badge classes
  const statusBadgeClasses = getStatusBadge(booking.status)
  
  // Handle actions
  const handleViewDetails = () => {
    setIsOpen(true)
  }
  
  const handleExtend = () => {
    // Open extend rental modal
    console.log(`Extend rental ID: ${booking.id}`)
  }
  
  const handleContact = () => {
    // Contact customer
    console.log(`Contact customer for rental ID: ${booking.id}`)
  }

  return (
    <>
      {/* Compact Card for Mobile Only */}
      <div className="md:hidden border rounded-lg shadow-sm bg-card hover:shadow-md transition-all duration-200 border-l-4 border-l-emerald-500">
        <div className="p-4 space-y-3">
          {/* Model Name, Renter Name, Status Badge */}
          <div className="space-y-2">
            <h3 className="font-bold text-base sm:text-lg line-clamp-1">{car?.model}</h3>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1.5">
                <div className="w-5 h-5 rounded-full bg-emerald-100 dark:bg-emerald-900 flex items-center justify-center flex-shrink-0">
                  <UserIcon className="h-2.5 w-2.5 text-emerald-600 dark:text-emerald-400" />
                </div>
                <p className="font-medium text-sm truncate">{customerName}</p>
              </div>
              <Badge className={`${statusBadgeClasses} text-xs px-2 py-1`}>
                {booking.status}
              </Badge>
            </div>
          </div>
          
          {/* View Details Button - Ensuring minimum 44px touch target */}
          <Button 
            onClick={handleViewDetails}
            className="w-full h-11 min-h-[44px] text-sm font-medium"
            aria-label={`View details for ${car?.model} booking by ${customerName}`}
          >
            View Details
          </Button>
        </div>
      </div>
      
      {/* Details Modal */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent 
          className="sm:max-w-md max-h-[90vh] overflow-y-auto p-4 sm:p-5 border-none shadow-lg w-[95%] sm:w-auto" 
          style={{ backgroundColor: 'white' }}
          aria-modal="true"
          onEscapeKeyDown={() => setIsOpen(false)}
        >
          <DialogHeader>
            <DialogTitle className="text-lg font-bold">Booking Details</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-3 w-full">
            {/* Header with ID and Status */}
            <div className="flex items-center justify-between gap-2 bg-muted/30 p-3 rounded-lg border border-muted">
              <h4 className="font-bold text-foreground text-sm">{formatBookingIdForDisplay(booking)}</h4>
              <Badge className={`${statusBadgeClasses} text-xs px-2 py-1`}>
                {booking.status}
              </Badge>
            </div>
            
            {/* Car Details Section */}
            <div className="bg-card border rounded-lg shadow-sm p-3 sm:p-4 space-y-2.5">
              <div className="flex items-center gap-2 mb-1">
                <div className="w-4 h-4 rounded-full bg-emerald-100 flex items-center justify-center dark:bg-emerald-900">
                  <CarIcon className="h-2.5 w-2.5 text-emerald-600 dark:text-emerald-400" />
                </div>
                <h3 className="font-semibold text-sm text-muted-foreground">VEHICLE DETAILS</h3>
              </div>
              <div className="flex gap-3">
                {/* Car Image */}
                <div className="flex-shrink-0">
                  <div className="relative w-20 aspect-[4/3] rounded-lg overflow-hidden bg-muted">
                    <Image
                      src={car?.image_url || '/placeholder.jpg'}
                      alt={`${car?.model} - ${car?.plate_number}`}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
                
                {/* Car Details */}
                <div className="flex-1 min-w-0 space-y-2">
                  <h3 className="font-semibold text-foreground text-base">{car?.model}</h3>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <CarIcon className="h-3.5 w-3.5 flex-shrink-0" />
                    <span className="truncate">{car?.plate_number}</span>
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="secondary" className="text-xs px-2 py-0.5">
                      {car?.type}
                    </Badge>
                    <Badge variant="outline" className="text-xs px-2 py-0.5">
                      {car?.seats} seats
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Customer Info Section */}
            <div className="bg-card border rounded-lg shadow-sm p-3 sm:p-4 space-y-2.5">
              <div className="flex items-center gap-2 mb-1">
                <div className="w-4 h-4 rounded-full bg-blue-100 flex items-center justify-center dark:bg-blue-900">
                  <UserIcon className="h-2.5 w-2.5 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="font-semibold text-sm text-muted-foreground">CUSTOMER INFORMATION</h3>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 rounded-full bg-emerald-100 dark:bg-emerald-900 flex items-center justify-center flex-shrink-0">
                    <UserIcon className="h-3 w-3 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <p className="font-medium text-foreground text-sm">{customerName}</p>
                </div>
                {customer?.email && (
                  <p className="text-sm text-muted-foreground flex items-center gap-1 ml-8">
                    <Mail className="h-3.5 w-3.5 flex-shrink-0" />
                    <span>{customer.email}</span>
                  </p>
                )}
              </div>
            </div>
            
            {/* Location Section */}
            <div className="bg-card border rounded-lg shadow-sm p-3 sm:p-4 space-y-2.5">
              <div className="flex items-center gap-2 mb-1">
                <div className="w-4 h-4 rounded-full bg-amber-100 flex items-center justify-center dark:bg-amber-900">
                  <MapPin className="h-2.5 w-2.5 text-amber-600 dark:text-amber-400" />
                </div>
                <h3 className="font-semibold text-sm text-muted-foreground">PICKUP & RETURN</h3>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground flex items-start gap-2">
                  <MapPin className="h-3.5 w-3.5 text-emerald-500 flex-shrink-0 mt-0.5" />
                  <span>
                    <span className="font-medium">Pickup:</span> {booking.pickup_location}
                  </span>
                </p>
                <p className="text-sm text-muted-foreground flex items-start gap-2">
                  <MapPin className="h-3.5 w-3.5 text-red-500 flex-shrink-0 mt-0.5" />
                  <span>
                    <span className="font-medium">Return:</span> {booking.dropoff_location}
                  </span>
                </p>
              </div>
            </div>
            
            {/* Schedule Section */}
            <div className="bg-card border rounded-lg shadow-sm p-3 sm:p-4 space-y-2.5">
              <div className="flex items-center gap-2 mb-1">
                <div className="w-4 h-4 rounded-full bg-purple-100 flex items-center justify-center dark:bg-purple-900">
                  <Clock className="h-2.5 w-2.5 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="font-semibold text-sm text-muted-foreground">SCHEDULE</h3>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground flex items-center gap-2">
                  <Clock className="h-3.5 w-3.5 text-emerald-500 flex-shrink-0" />
                  <span>
                    <span className="font-medium">Start:</span> {formatDate(booking.pickup_datetime)}
                  </span>
                </p>
                <p className="text-sm text-muted-foreground flex items-center gap-2">
                  <CalendarCheck className="h-3.5 w-3.5 text-red-500 flex-shrink-0" />
                  <span>
                    <span className="font-medium">End:</span> {formatDate(booking.dropoff_datetime)}
                  </span>
                </p>
              </div>
            </div>
            
            {/* Payment Section */}
            <div className="bg-card border rounded-lg shadow-sm p-3 sm:p-4 space-y-2.5">
              <div className="flex items-center gap-2 mb-1">
                <div className="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center dark:bg-green-900">
                  <CreditCard className="h-2.5 w-2.5 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="font-semibold text-sm text-muted-foreground">PAYMENT</h3>
              </div>
              <div className="space-y-2">
                <p className="text-lg font-bold text-emerald-600 dark:text-emerald-400 mt-1 mb-1">
                  {formatCurrency(booking.total_amount)}
                </p>
                {payment && (
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-3.5 w-3.5 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">{payment.method}</span>
                  </div>
                )}
              </div>
            </div>
            
            {/* Special Requests */}
            {booking.special_requests && (
              <div className="bg-card border rounded-lg shadow-sm p-3 sm:p-4 space-y-2.5">
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-4 h-4 rounded-full bg-indigo-100 flex items-center justify-center dark:bg-indigo-900">
                    <FileText className="h-2.5 w-2.5 text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <h3 className="font-semibold text-sm text-muted-foreground">SPECIAL REQUESTS</h3>
                </div>
                <p className="text-sm text-muted-foreground flex items-start gap-2">
                  <FileText className="h-3.5 w-3.5 mt-0.5 flex-shrink-0" />
                  <span>"{booking.special_requests}"</span>
                </p>
              </div>
            )}
            
            {/* Actions - Ensuring minimum 44px touch targets */}
            <div className="flex items-center justify-between gap-2 pt-3 mt-2 border-t border-muted">
              <Button 
                variant="secondary" 
                size="sm" 
                className="flex-1 text-xs sm:text-sm h-10 min-h-[40px]" 
                onClick={handleExtend}
                aria-label={`Extend rental for ${car?.model} by ${customerName}`}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    handleExtend();
                  }
                }}
              >
                Extend
              </Button>
              <Button 
                variant="secondary" 
                size="sm" 
                className="flex-1 text-xs sm:text-sm h-10 min-h-[40px]" 
                onClick={handleContact}
                aria-label={`Contact ${customerName} about their ${car?.model} booking`}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    handleContact();
                  }
                }}
              >
                Contact
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
