"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, ArrowRight, CheckCircle } from "lucide-react";
import { BookingSummaryStep } from "./booking-summary-step";
import { RequirementsUploadStep } from "./requirements-upload-step";
import { PersonalInfoStep } from "./personal-info-step";
import { PaymentProofStep } from "./payment-proof-step";
import { ConfirmationStep } from "./confirmation-step";
import { TermsConditionsBanner } from "./terms-conditions-banner";
import {
  CustomerLoadingOverlay,
  useCustomerLoadingState,
} from "@/components/customer-side/loading/customer-loading";
import type { Car } from "@/lib/types";
import type { DocumentFile } from "@/components/ui/document-upload";
import { FIXED_PICKUP_LOCATION } from "@/components/ui/booking-location-components";

interface BookingFlowProps {
  initialData: {
    pickUpLocation: string;
    dropOffLocation: string;
    pickUpDate: string;
    pickUpTime: string;
    dropOffDate: string;
    dropOffTime: string;
    carId: string | null;
    specialService?: boolean;
    serviceType?: string | null;
  };
}

export interface BookingData {
  // Car selection
  selectedCar: Car | null;

  // Booking details
  pickUpLocation: string;
  dropOffLocation: string;
  pickUpDate: string;
  pickUpTime: string;
  dropOffDate: string;
  dropOffTime: string;

  // Day/Night selection and delivery fees
  pickupTimePeriod: "day" | "night";
  returnTimePeriod: "day" | "night";
  deliveryFee: number;
  returnFee: number;
  totalDeliveryFees: number;

  // Location checkboxes
  pickupGarageChecked: boolean;
  dropoffSameAsPickupChecked: boolean;
  
  // Special service booking
  isSpecialService: boolean;
  serviceType: string | null;

  // Personal information
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  familyMemberPhone: string;
  familyMemberFacebook: string;
  notes: string;

  // Requirements upload
  driversLicense?: DocumentFile[];
  governmentId?: DocumentFile[];
  proofOfBilling?: DocumentFile[];

  // Proof of payment
  proofOfPayment?: DocumentFile[];
  paymentMethod?: "GCash" | "Bank Transfer" | "Remittance Center";
  gcashReferenceNumber?: string;
}

const STEPS = [
  {
    id: 1,
    title: "Booking Details",
    description: "Review and edit your booking",
  },
  {
    id: 2,
    title: "Requirements Upload",
    description: "Upload required documents",
  },
  { id: 3, title: "Personal Info", description: "Enter your contact details" },
  {
    id: 4,
    title: "Payment",
    description: "Downpayment and proof of payment",
  },
  {
    id: 5,
    title: "Review & Confirm",
    description: "Final review before submission",
  },
];

export function BookingFlow({ initialData }: BookingFlowProps) {
  const [currentStep, setCurrentStep] = React.useState(1);
  const [bookingData, setBookingData] = React.useState<BookingData>({
    selectedCar: null,
    pickUpLocation: initialData.pickUpLocation || "", // Use dynamic pickup location from URL params
    dropOffLocation: initialData.dropOffLocation,
    pickUpDate: initialData.pickUpDate,
    pickUpTime: initialData.pickUpTime,
    dropOffDate: initialData.dropOffDate,
    dropOffTime: initialData.dropOffTime,
    // Day/Night selection and delivery fees initialization
    pickupTimePeriod: "day",
    returnTimePeriod: "day",
    deliveryFee: 0,
    returnFee: 0,
    totalDeliveryFees: 0,
    customerName: "",
    customerEmail: "",
    customerPhone: "",
    familyMemberPhone: "",
    familyMemberFacebook: "",
    notes: "",
    // Location checkboxes initialization
    pickupGarageChecked: false,
    dropoffSameAsPickupChecked: false,
    // Special service initialization
    isSpecialService: initialData.specialService || false,
    serviceType: initialData.serviceType || null,
    // Requirements documents initialization
    driversLicense: [],
    governmentId: [],
    proofOfBilling: [],
    // Proof of payment initialization
    proofOfPayment: [],
    paymentMethod: undefined,
    gcashReferenceNumber: "",
  });

  // Load car if carId is provided (runs after localStorage data is loaded)
  React.useEffect(() => {
    const loadCarFromId = async () => {
      if (initialData.carId) {
        // Only load if no car is currently selected, or if the selected car ID doesn't match
        if (initialData.carId) {
          try {
            // Import createClient and fetch cars from Supabase (same as catalog)
            const { createClient } = await import("@/lib/supabase/client");
            const supabase = createClient();

            const { data: cars, error } = await supabase
              .from("cars")
              .select("*")
              .eq("is_archived", false)
              .eq("status", "Available");

            if (error) {
              console.error("Error fetching cars from Supabase:", error);
              return;
            }

            const car = cars?.find((c) => c.id === initialData.carId);
            if (car && car.status === "Available") {
              setBookingData((prev) => ({ ...prev, selectedCar: car }));
            }
          } catch (error) {
            console.error("Error loading car:", error);
          }
        }
      }
    };

    // Add a small delay to ensure localStorage has been processed first
    const timeoutId = setTimeout(loadCarFromId, 100);
    return () => clearTimeout(timeoutId);
  }, [initialData.carId, bookingData.selectedCar?.id]);

  // Store data in localStorage to persist across refreshes
  React.useEffect(() => {
    const savedData = localStorage.getItem("ollietrack-booking-data");
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        setBookingData((prev) => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error("Failed to parse saved booking data:", error);
      }
    }
  }, []);

  React.useEffect(() => {
    localStorage.setItem(
      "ollietrack-booking-data",
      JSON.stringify(bookingData)
    );
  }, [bookingData]);

  const updateBookingData = (updates: Partial<BookingData>) => {
    setBookingData((prev) => ({ ...prev, ...updates }));
  };

  const nextStep = () => {
    if (canProceedToNextStep()) {
      setCurrentStep((prev) => Math.min(prev + 1, STEPS.length));
    }
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 1:
        return (
          bookingData.selectedCar !== null &&
          bookingData.pickUpLocation &&
          bookingData.dropOffLocation &&
          bookingData.pickUpDate &&
          bookingData.pickUpTime &&
          bookingData.dropOffDate &&
          bookingData.dropOffTime
        );
      case 2:
        // Requirements upload validation - check if all required documents are uploaded and completed
        const requiredDocs = [
          "driversLicense",
          "governmentId",
          "proofOfBilling",
        ] as const;
        return requiredDocs.every((docType) => {
          const files = bookingData[docType];
          return (
            files &&
            files.length > 0 &&
            files.every((f) => f.status === "completed")
          );
        });
      case 3:
        return (
          bookingData.customerName &&
          bookingData.customerEmail &&
          bookingData.customerPhone &&
          bookingData.familyMemberPhone &&
          bookingData.familyMemberFacebook
        );
      case 4:
        // Proof of payment validation
        const hasValidPaymentMethod = bookingData.paymentMethod;
        const hasProofOfPayment = 
          bookingData.proofOfPayment &&
          bookingData.proofOfPayment.length > 0 &&
          bookingData.proofOfPayment.every((f) => f.status === "completed");
        
        // If GCash is selected, also require the reference number
        const hasGCashReferenceIfNeeded = 
          bookingData.paymentMethod !== "GCash" || 
          (bookingData.gcashReferenceNumber && bookingData.gcashReferenceNumber.trim().length > 0);
        
        return hasValidPaymentMethod && hasProofOfPayment && hasGCashReferenceIfNeeded;
      case 5:
        return true;
      default:
        return false;
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <BookingSummaryStep
            bookingData={bookingData}
            onUpdate={updateBookingData}
          />
        );
      case 2:
        return (
          <RequirementsUploadStep
            bookingData={bookingData}
            onUpdate={updateBookingData}
          />
        );
      case 3:
        return (
          <PersonalInfoStep
            bookingData={bookingData}
            onUpdate={updateBookingData}
          />
        );
      case 4:
        return (
          <PaymentProofStep
            bookingData={bookingData}
            onUpdate={updateBookingData}
          />
        );
      case 5:
        return <ConfirmationStep bookingData={bookingData} />;
      default:
        return null;
    }
  };

  const progressPercentage = (currentStep / STEPS.length) * 100;

  return (
    <div className="max-w-4xl mx-auto px-2 sm:px-3 lg:px-4 py-3 sm:py-4 lg:py-8 overflow-x-hidden">
      {/* Progress Header */}
      <Card className="mb-4 sm:mb-6 lg:mb-8 overflow-hidden">
        <CardHeader className="p-3 sm:p-4 lg:p-6 pb-3 sm:pb-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4 gap-1.5 sm:gap-2 min-w-0">
            <div className="min-w-0 flex-1">
              <CardTitle className="text-base sm:text-lg lg:text-xl xl:text-2xl font-bold text-gray-900 break-words">
                Book Your Vehicle
              </CardTitle>
              <p className="text-[10px] sm:text-xs lg:text-sm text-gray-600 mt-0.5 sm:mt-1 break-words leading-tight">
                Vehicle selected from catalog • Continue with booking details
              </p>
            </div>
            <div className="text-[10px] sm:text-xs lg:text-sm text-gray-600 flex-shrink-0 self-start sm:self-auto">
              Step {currentStep} of {STEPS.length}
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-3 sm:mb-4 lg:mb-6">
            <Progress value={progressPercentage} className="h-1.5 sm:h-2" />
          </div>

          {/* Step Indicators */}
          <div className="grid grid-cols-5 gap-0.5 sm:gap-1 lg:gap-2 max-w-full">
            {STEPS.map((step) => (
              <div
                key={step.id}
                className={`flex flex-col items-center text-center p-0.5 sm:p-1 lg:p-2 rounded-lg transition-colors min-w-0 ${
                  step.id === currentStep
                    ? "bg-blue-50 text-blue-600"
                    : step.id < currentStep
                    ? "bg-green-50 text-green-600"
                    : "bg-gray-50 text-gray-400"
                }`}
              >
                <div
                  className={`w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 xl:w-8 xl:h-8 rounded-full flex items-center justify-center mb-0.5 sm:mb-1 lg:mb-2 flex-shrink-0 ${
                    step.id === currentStep
                      ? "bg-blue-600 text-white"
                      : step.id < currentStep
                      ? "bg-green-600 text-white"
                      : "bg-gray-300 text-gray-600"
                  }`}
                >
                  {step.id < currentStep ? (
                    <CheckCircle className="w-2 h-2 sm:w-2.5 sm:h-2.5 lg:w-3 lg:h-3 xl:w-5 xl:h-5" />
                  ) : (
                    <span className="text-[8px] sm:text-[10px] lg:text-xs xl:text-sm font-semibold">
                      {step.id}
                    </span>
                  )}
                </div>
                <div className="text-[8px] sm:text-[10px] lg:text-xs font-medium truncate max-w-full leading-tight">
                  {step.title}
                </div>
                <div className="hidden sm:block text-[8px] sm:text-[10px] lg:text-xs text-gray-500 break-words text-center max-w-full leading-tight">
                  {step.description}
                </div>
              </div>
            ))}
          </div>
        </CardHeader>
      </Card>

      {/* Step Content */}
      <div className="mb-4 sm:mb-6 lg:mb-8 max-w-full overflow-x-hidden space-y-3 sm:space-y-4">
        {/* Terms & Conditions Banner - Show on all steps */}
        <TermsConditionsBanner />

        {/* Actual Step Content */}
        {renderStep()}
      </div>

      {/* Navigation Footer */}
      <Card className="overflow-hidden">
        <CardContent className="p-3 sm:p-4 lg:p-6">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-2 sm:gap-3 lg:gap-4">
            <Button
              variant="secondary"
              onClick={prevStep}
              disabled={currentStep === 1}
              className="flex items-center gap-1.5 sm:gap-2 min-h-[40px] sm:min-h-[44px] w-full sm:w-auto order-2 sm:order-1 text-xs sm:text-sm"
            >
              <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
              <span>Previous</span>
            </Button>

            <div className="text-[10px] sm:text-xs lg:text-sm text-gray-600 flex-1 min-w-0 px-1 sm:px-2 lg:px-4 order-1 sm:order-2 flex items-center justify-center">
              <span className="break-words leading-tight text-center">
                {STEPS.find((s) => s.id === currentStep)?.description}
              </span>
            </div>

            {currentStep < STEPS.length ? (
              <Button
                onClick={nextStep}
                disabled={!canProceedToNextStep()}
                className="flex items-center gap-1.5 sm:gap-2 bg-blue-600 hover:bg-blue-700 min-h-[40px] sm:min-h-[44px] w-full sm:w-auto order-3 text-xs sm:text-sm"
              >
                <span className="truncate">
                  {currentStep === 3 ? "Review & Confirm" : "Next"}
                </span>
                <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
              </Button>
            ) : (
              <div className="flex-shrink-0 w-full sm:w-auto order-3" />
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
