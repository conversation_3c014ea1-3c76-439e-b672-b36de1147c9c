"use server";

import { createContextClient } from "@/lib/supabase/server";
import { sendEmail, buildBookingConfirmedEmail, buildBookingDeniedEmail, buildBookingReceiptEmail } from "@/lib/email";
import type { SendEmailResult } from "@/lib/email";
import { logWithContext, logError } from "@/lib/utils/logger";
import { format } from "date-fns";

export async function adminDecideBooking(
  bookingId: string,
  action: "confirm" | "deny",
  notes?: string
) {
  const supabase = await createContextClient('admin');

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  logWithContext("DecisionActions", "Auth debug:", {
    user: user ? { id: user.id, email: user.email } : null,
    authError,
    bookingId,
    action,
  });

  if (!user) {
    return { error: { message: "You must be logged in to decide bookings." } };
  }

  // Check if user is admin
  const { data: profile } = await supabase
    .from("profiles")
    .select("role")
    .eq("id", user.id)
    .single();

  logWithContext("DecisionActions", "Admin role check:", {
    userId: user.id,
    role: (profile as any)?.role,
  });

  if (!profile || (profile.role !== "admin" && profile.role !== "super_admin")) {
    return { error: { message: "Only admins can decide bookings." } };
  }

  const newStatus = action === "confirm" ? "Active" : "Cancelled";

  const { data: updatedBooking, error: updateError } = await supabase
    .from("bookings")
    .update({
      status: newStatus,
      updated_at: new Date().toISOString(),
    })
    .eq("id", bookingId)
    .select("id, customer_id, booking_ref")
    .single();

  if (updateError) {
    logError("DecisionActions", "Error updating booking status", updateError);
    return { error: updateError };
  }

  // If confirming the booking, automatically mark the payment as paid
  if (action === "confirm") {
    try {
      logWithContext("DecisionActions", "Starting payment processing for confirmed booking", { bookingId });
      
      const { data: payment, error: paymentFetchError } = await supabase
        .from("payments")
        .select("id, status, booking_id, amount, method")
        .eq("booking_id", bookingId)
        .single();

      logWithContext("DecisionActions", "Payment fetch result", {
        bookingId,
        payment,
        paymentFetchError: paymentFetchError?.message || null,
        paymentFetchErrorCode: paymentFetchError?.code || null
      });

      if (!paymentFetchError && payment && payment.status !== "Paid") {
        logWithContext("DecisionActions", "Updating payment status for confirmed booking", {
          bookingId,
          paymentId: payment.id,
          currentStatus: payment.status,
          paymentAmount: payment.amount,
          paymentMethod: payment.method
        });

        const { data: updatedPayment, error: paymentUpdateError } = await supabase
          .from("payments")
          .update({
            status: "Paid",
            verified_by: user.id,
            verified_at: new Date().toISOString(),
            verification_notes: "Payment automatically verified upon booking confirmation",
            updated_at: new Date().toISOString()
          })
          .eq("id", payment.id)
          .select("id, status, amount, method")
          .single();

        if (paymentUpdateError) {
          logError("DecisionActions", "CRITICAL: Error updating payment status", {
            bookingId,
            paymentId: payment.id,
            error: paymentUpdateError,
            errorMessage: paymentUpdateError?.message,
            errorCode: paymentUpdateError?.code
          });
          // Don't fail the booking confirmation if payment update fails
        } else {
          logWithContext("DecisionActions", "SUCCESS: Payment automatically marked as paid for confirmed booking", {
            bookingId,
            paymentId: payment.id,
            updatedPayment,
            previousStatus: payment.status,
            newStatus: updatedPayment?.status
          });
          
          // Verify the update by fetching the payment again
          const { data: verifiedPayment, error: verifyError } = await supabase
            .from("payments")
            .select("id, status, amount, method, verified_by, verified_at")
            .eq("id", payment.id)
            .single();
            
          if (!verifyError && verifiedPayment) {
            logWithContext("DecisionActions", "Payment status verification - FINAL CHECK", {
              paymentId: payment.id,
              verifiedPayment,
              statusConfirmed: verifiedPayment.status === "Paid"
            });
          } else {
            logError("DecisionActions", "Failed to verify payment update", {
              paymentId: payment.id,
              verifyError: verifyError?.message
            });
          }
        }
      } else if (!paymentFetchError && payment && payment.status === "Paid") {
        logWithContext("DecisionActions", "Payment already paid, skipping update", {
          bookingId,
          paymentId: payment.id,
          currentStatus: payment.status,
          paymentAmount: payment.amount
        });
      } else {
        logError("DecisionActions", "No payment found for booking or fetch error", {
          bookingId,
          paymentFetchErrorMessage: paymentFetchError?.message,
          paymentFetchErrorCode: paymentFetchError?.code,
          paymentFetchError,
        });

        // If no payment exists, create one for the confirmed booking
        if (paymentFetchError && paymentFetchError.code === "PGRST116") {
          // PGRST116 = no rows returned, meaning no payment exists
          logWithContext("DecisionActions", "Creating payment record for confirmed booking", {
            bookingId,
          });

          // Get booking details to create payment
          const { data: bookingData, error: bookingError } = await supabase
            .from("bookings")
            .select("total_amount")
            .eq("id", bookingId)
            .single();

          if (!bookingError && bookingData) {
            const { data: newPayment, error: paymentCreateError } = await supabase
              .from("payments")
              .insert({
                booking_id: bookingId,
                amount: bookingData.total_amount,
                status: "Paid",
                method: "GCash",
                verified_by: user.id,
                verified_at: new Date().toISOString(),
                verification_notes: "Payment created and verified upon booking confirmation",
                transaction_date: new Date().toISOString(),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .select("id, status, amount, method")
              .single();

            if (paymentCreateError) {
              logError("DecisionActions", "CRITICAL: Error creating payment record", {
                bookingId,
                error: paymentCreateError,
                errorMessage: paymentCreateError?.message,
                errorCode: paymentCreateError?.code
              });
            } else {
              logWithContext("DecisionActions", "SUCCESS: Payment record created successfully for confirmed booking", {
                bookingId,
                amount: bookingData.total_amount,
                newPayment,
                paymentId: newPayment?.id
              });
            }
          } else {
            logError("DecisionActions", "Could not fetch booking data to create payment", {
              bookingId,
              bookingError: bookingError?.message
            });
          }
        }
      }
    } catch (paymentError: any) {
      logError("DecisionActions", "CRITICAL: Exception during payment processing for confirmed booking", {
        bookingId,
        error: paymentError,
        errorMessage: paymentError?.message || String(paymentError),
        errorStack: paymentError?.stack
      });
      // Don't fail the booking confirmation if payment processing fails
    }
  }

  // Send notification email
  try {
    const customerId = (updatedBooking as any)?.customer_id as string | undefined;
    logWithContext("DecisionActions", "Sending email notification for booking:", { bookingId, customerId, action });
    
    if (customerId) {
      const { data: customer, error: customerError } = await supabase
        .from("profiles")
        .select("email, full_name")
        .eq("id", customerId)
        .single();

      if (customerError) {
        logError("DecisionActions", "Error fetching customer for email", customerError);
        return { data: updatedBooking };
      }

      const to = (customer as any)?.email as string | undefined;
      const customerName = (customer as any)?.full_name as string | undefined;

      logWithContext("DecisionActions", "Customer email details:", { to, customerName, customerId });

      if (to) {
        let emailResult;
        if (action === "confirm") {
          // Get additional booking details for the receipt email
          const { data: bookingDetails, error: bookingDetailsError } = await supabase
            .from("bookings")
            .select(
              "id, created_at, pickup_datetime, dropoff_datetime, pickup_location, total_amount, car_id"
            )
            .eq("id", bookingId)
            .single();

          if (bookingDetailsError) {
            logError("DecisionActions", "Error fetching booking details for email", bookingDetailsError);
          } else {
            // Get car details
            const { data: carDetails, error: carDetailsError } = await supabase
              .from("cars")
              .select("id, model, plate_number, price_per_day")
              .eq("id", bookingDetails.car_id)
              .single();

            if (carDetailsError) {
              logError("DecisionActions", "Error fetching car details for email", carDetailsError);
            } else {
              const dashboardUrl = process.env.NEXT_PUBLIC_SITE_URL 
                ? `${process.env.NEXT_PUBLIC_SITE_URL}/customer/dashboard` 
                : "https://olliesrentalcar.pathlinkio.app/customer/dashboard";
              
              // Calculate duration in days
              const pickupDate = new Date(bookingDetails.pickup_datetime);
              const dropoffDate = new Date(bookingDetails.dropoff_datetime);
              const duration = Math.max(1, Math.ceil((dropoffDate.getTime() - pickupDate.getTime()) / (1000 * 60 * 60 * 24)));

              // Send the detailed booking confirmation receipt (single email)
              const bookingRef = (updatedBooking as any)?.booking_ref || bookingId;
              const receiptEmail = buildBookingReceiptEmail({
                customerName: customerName || null,
                bookingRef,
                bookingDate: format(new Date(bookingDetails.created_at), "MMM d, yyyy, hh:mm a"),
                vehicleModel: carDetails.model,
                plateNumber: carDetails.plate_number,
                pickupDate: format(pickupDate, "MMM d, yyyy"),
                returnDate: format(dropoffDate, "MMM d, yyyy"),
                duration: duration,
                pickupLocation: bookingDetails.pickup_location,
                baseRate: bookingDetails.total_amount,
                totalAmount: bookingDetails.total_amount,
                dailyRate: carDetails.price_per_day,
                vehicleId: carDetails.id,
                dashboardUrl,
              });
              
              logWithContext("DecisionActions", "Sending booking confirmation receipt:", { to, subject: receiptEmail.subject });
              const receiptResult = await sendEmail({ to, subject: receiptEmail.subject, html: receiptEmail.html });
              
              if (!receiptResult.ok) {
                logError("DecisionActions", "Receipt email sending failed", receiptResult.error);
              } else {
                logWithContext("DecisionActions", "Receipt email sent successfully to:", to);
              }
            }
          }
        } else {
          const bookingRef = (updatedBooking as any)?.booking_ref || bookingId;
          const { subject, html } = buildBookingDeniedEmail({
            customerName: customerName || null,
            bookingRef,
            notes: notes || null,
            dashboardUrl: process.env.NEXT_PUBLIC_SITE_URL ? `${process.env.NEXT_PUBLIC_SITE_URL}/customer/dashboard` : "https://olliesrentalcar.pathlinkio.app/customer/dashboard",
          });
          logWithContext("DecisionActions", "Sending denial email:", { to, subject });
          emailResult = await sendEmail({ to, subject, html });
        }
        
        logWithContext("DecisionActions", "Email send result:", emailResult);
        
        // For denial emails or confirmation emails without receipt details
        if (emailResult) {
          if (!emailResult.ok) {
            logError("DecisionActions", "Email sending failed", emailResult.error);
          } else {
            logWithContext("DecisionActions", "Email sent successfully to:", to);
          }
        }
      } else {
        logWithContext("DecisionActions", "No email address found for customer:", customerId);
      }
    } else {
      logWithContext("DecisionActions", "No customer ID found for booking:", bookingId);
    }
  } catch (notifyErr) {
    logError("DecisionActions", "Email notification error", notifyErr);
    // Don't fail the booking update if email fails
  }

  return { data: updatedBooking };
}
