"use client";

import * as React from "react";

interface LoadingBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  delay?: number;
}

export function LoadingBoundary({ 
  children, 
  fallback = <div className="animate-pulse">Loading...</div>,
  delay = 100
}: LoadingBoundaryProps) {
  const [isReady, setIsReady] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setIsReady(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  if (!isReady) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Higher-order component version
export function withLoadingBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ReactNode,
  delay?: number
) {
  return function WrappedComponent(props: P) {
    return (
      <LoadingBoundary fallback={fallback} delay={delay}>
        <Component {...props} />
      </LoadingBoundary>
    );
  };
}
