import { PublicAppShell } from "@/components/layout/public-app-shell"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  FileText, 
  Scale, 
  Shield, 
  AlertTriangle, 
  CreditCard, 
  Clock, 
  Car, 
  Gavel,
  Phone,
  Mail,
  MessageCircle,
  CheckCircle,
  XCircle,
  Info
} from "lucide-react"
import Link from "next/link"

export default function TermsPage() {
  return (
    <PublicAppShell>
      <div className="space-y-0 -mx-4 md:-mx-6">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-slate-50 via-white to-gray-50 px-4 md:px-6 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-slate-100 rounded-full mb-6">
              <Scale className="h-8 w-8 text-slate-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Terms & Conditions
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Legal agreement between you and Ollie's Rent A Car. 
              Please read these terms carefully before using our services.
            </p>
            
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 max-w-2xl mx-auto">
              <div className="flex items-start gap-3">
                <Info className="h-6 w-6 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-left">
                  <h3 className="font-semibold text-blue-900 mb-2">Important Notice</h3>
                  <p className="text-sm text-blue-800">
                    By using our rental services, you agree to be bound by these terms and conditions. 
                    This creates a legally binding agreement between you and Ollie's Rent A Car.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Quick Summary */}
        <section className="px-4 md:px-6 py-12 bg-white border-y">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Key Terms Summary</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="border-l-4 border-l-blue-500">
                <CardContent className="p-6">
                  <CreditCard className="h-8 w-8 text-blue-600 mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-2">Payment</h3>
                  <p className="text-sm text-gray-600">Full payment required before vehicle release</p>
                </CardContent>
              </Card>
              
              <Card className="border-l-4 border-l-green-500">
                <CardContent className="p-6">
                  <FileText className="h-8 w-8 text-green-600 mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-2">License Required</h3>
                  <p className="text-sm text-gray-600">Valid driver's license for all authorized drivers</p>
                </CardContent>
              </Card>
              
              <Card className="border-l-4 border-l-yellow-500">
                <CardContent className="p-6">
                  <Clock className="h-8 w-8 text-yellow-600 mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-2">Late Returns</h3>
                  <p className="text-sm text-gray-600">₱500 per hour penalty for excess time</p>
                </CardContent>
              </Card>
              
              <Card className="border-l-4 border-l-red-500">
                <CardContent className="p-6">
                  <Shield className="h-8 w-8 text-red-600 mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-2">Damage Liability</h3>
                  <p className="text-sm text-gray-600">Renter responsible for all vehicle damage</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Terms Content */}
        <section className="px-4 md:px-6 py-12">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Complete Terms & Conditions</h2>
            
            <Accordion type="single" collapsible className="space-y-4">
              {/* Rental Agreement */}
              <AccordionItem value="rental-agreement" className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                <AccordionTrigger className="px-6 py-5 hover:no-underline hover:bg-gray-50 group">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FileText className="h-5 w-5 text-blue-600" />
                    </div>
                    <span className="font-semibold text-left group-hover:text-blue-600 transition-colors">
                      Rental Agreement
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 pt-2">
                  <div className="ml-11 space-y-4">
                    <div className="bg-slate-50 p-4 rounded-lg">
                      <p className="text-sm text-slate-700">
                        <strong>KNOW ALL MEN BY THESE PRESENTS:</strong> This Agreement made and executed by and between the RENTER and OLLIE'S RENT A CAR WITNESSETH OLLIE'S RENT A CAR hereby rents to the RENTER who acknowledges and agrees to the following terms:
                      </p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Terms of Payment */}
              <AccordionItem value="payment-terms" className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                <AccordionTrigger className="px-6 py-5 hover:no-underline hover:bg-gray-50 group">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <CreditCard className="h-5 w-5 text-green-600" />
                    </div>
                    <span className="font-semibold text-left group-hover:text-blue-600 transition-colors">
                      Terms of Payment
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 pt-2">
                  <div className="ml-11 space-y-4">
                    <div className="flex items-start gap-3 p-4 bg-green-50 rounded-lg border border-green-200">
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                      <div>
                        <p className="font-medium text-green-900 mb-1">Payment Policy</p>
                        <p className="text-sm text-green-800">
                          For each rental regardless of rental period, full payment in cash is required prior to the release of the vehicle.
                        </p>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Authorized Drivers */}
              <AccordionItem value="authorized-drivers" className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                <AccordionTrigger className="px-6 py-5 hover:no-underline hover:bg-gray-50 group">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Car className="h-5 w-5 text-purple-600" />
                    </div>
                    <span className="font-semibold text-left group-hover:text-blue-600 transition-colors">
                      Authorized Drivers
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 pt-2">
                  <div className="ml-11 space-y-4">
                    <div className="flex items-start gap-3 p-4 bg-purple-50 rounded-lg border border-purple-200">
                      <Shield className="h-5 w-5 text-purple-600 mt-0.5" />
                      <div>
                        <p className="font-medium text-purple-900 mb-1">Driver Requirements</p>
                        <p className="text-sm text-purple-800">
                          All authorized drivers must have a valid driver's license to operate a motor vehicle.
                        </p>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Excess Hours */}
              <AccordionItem value="excess-hours" className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                <AccordionTrigger className="px-6 py-5 hover:no-underline hover:bg-gray-50 group">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <Clock className="h-5 w-5 text-yellow-600" />
                    </div>
                    <span className="font-semibold text-left group-hover:text-blue-600 transition-colors">
                      Excess Hours & Late Return
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 pt-2">
                  <div className="ml-11 space-y-4">
                    <div className="flex items-start gap-3 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                      <div>
                        <p className="font-medium text-yellow-900 mb-2">Late Return Penalties</p>
                        <ul className="text-sm text-yellow-800 space-y-1">
                          <li>• <strong>₱500 per hour</strong> penalty for exceeding agreed return time</li>
                          <li>• No fraction of time allowed for 24-hour rentals</li>
                          <li>• After first 24 hours: 10% of daily rate per hour or ₱300 (whichever is higher)</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Rental Extension */}
              <AccordionItem value="rental-extension" className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                <AccordionTrigger className="px-6 py-5 hover:no-underline hover:bg-gray-50 group">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-indigo-100 rounded-lg">
                      <Clock className="h-5 w-5 text-indigo-600" />
                    </div>
                    <span className="font-semibold text-left group-hover:text-blue-600 transition-colors">
                      Rental Extension Policy
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 pt-2">
                  <div className="ml-11 space-y-4">
                    <div className="space-y-3">
                      <div className="p-4 bg-indigo-50 rounded-lg border border-indigo-200">
                        <p className="text-sm text-indigo-800">
                          The renter can request an extension while the rental is in progress. Owner's approval is required. 
                          Extensions beyond 6 hours will be charged equivalent to 1 full day rental.
                        </p>
                      </div>
                      <div className="flex items-start gap-3 p-4 bg-red-50 rounded-lg border border-red-200">
                        <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                        <div>
                          <p className="font-medium text-red-900 mb-1">Important Warning</p>
                          <p className="text-sm text-red-800">
                            Any delay in vehicle return without prior notice will result in automatic reporting 
                            to the constabulary Anti-Carnapping Group.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Damage to Vehicle */}
              <AccordionItem value="vehicle-damage" className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                <AccordionTrigger className="px-6 py-5 hover:no-underline hover:bg-gray-50 group">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-red-100 rounded-lg">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                    </div>
                    <span className="font-semibold text-left group-hover:text-blue-600 transition-colors">
                      Vehicle Damage & Loss
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 pt-2">
                  <div className="ml-11 space-y-4">
                    <div className="space-y-3">
                      <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                        <p className="font-medium text-red-900 mb-2">In Case of Damage:</p>
                        <ul className="text-sm text-red-800 space-y-1">
                          <li>• Secure police report and driver's license copy</li>
                          <li>• Obtain dealer's job estimate</li>
                          <li>• Pay total costs: parts, repairs, labor</li>
                          <li>• Pay 50% daily rate until vehicle is restored</li>
                        </ul>
                      </div>
                      <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                        <p className="font-medium text-red-900 mb-2">Total Loss or Damage:</p>
                        <ul className="text-sm text-red-800 space-y-1">
                          <li>• Renter pays current market value within 7 days</li>
                          <li>• Exclusive of insurance liability</li>
                          <li>• Loss of accessories, tools, tires charged to renter</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Additional Charges */}
              <AccordionItem value="additional-charges" className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                <AccordionTrigger className="px-6 py-5 hover:no-underline hover:bg-gray-50 group">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <CreditCard className="h-5 w-5 text-orange-600" />
                    </div>
                    <span className="font-semibold text-left group-hover:text-blue-600 transition-colors">
                      Additional Charges & Fees
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 pt-2">
                  <div className="ml-11 space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                        <p className="font-medium text-orange-900 mb-2">Renter's Responsibility:</p>
                        <ul className="text-sm text-orange-800 space-y-1">
                          <li>• Parking fees</li>
                          <li>• Toll fees</li>
                          <li>• Traffic fines</li>
                          <li>• Tire vulcanizing charges</li>
                        </ul>
                      </div>
                      <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                        <p className="font-medium text-orange-900 mb-2">Key-Related Charges:</p>
                        <ul className="text-sm text-orange-800 space-y-1">
                          <li>• Lost key replacement</li>
                          <li>• Damaged key replacement</li>
                          <li>• Lockout rescue service</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Liability Release */}
              <AccordionItem value="liability-release" className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                <AccordionTrigger className="px-6 py-5 hover:no-underline hover:bg-gray-50 group">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-slate-100 rounded-lg">
                      <Shield className="h-5 w-5 text-slate-600" />
                    </div>
                    <span className="font-semibold text-left group-hover:text-blue-600 transition-colors">
                      Release of Liability
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 pt-2">
                  <div className="ml-11 space-y-4">
                    <div className="space-y-3">
                      <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                        <p className="text-sm text-slate-700">
                          <strong>Ollie's Rent A Car</strong> is not liable for losses of valuables left in the vehicle. 
                          The renter guarantees lawful use of the vehicle in strict conformity with all applicable laws, 
                          ordinances, and regulations.
                        </p>
                      </div>
                      <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                        <p className="text-sm text-slate-700">
                          <strong>Force Majeure:</strong> We are not responsible for unforeseen events beyond human control 
                          (broken bridges, impassable roads, natural calamities, etc.) that may damage or prevent the vehicle 
                          from reaching its destination on time.
                        </p>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Legal Action */}
              <AccordionItem value="legal-action" className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                <AccordionTrigger className="px-6 py-5 hover:no-underline hover:bg-gray-50 group">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      <Gavel className="h-5 w-5 text-gray-600" />
                    </div>
                    <span className="font-semibold text-left group-hover:text-blue-600 transition-colors">
                      Course of Action & Legal Terms
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 pt-2">
                  <div className="ml-11 space-y-4">
                    <div className="space-y-3">
                      <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                        <p className="font-medium text-gray-900 mb-2">Violation of Agreement:</p>
                        <p className="text-sm text-gray-700">
                          In case of agreement violation, Ollie's Rent A Car may demand immediate vehicle return 
                          and may institute legal proceedings in Laoag City.
                        </p>
                      </div>
                      <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                        <p className="font-medium text-gray-900 mb-2">Additional Costs:</p>
                        <ul className="text-sm text-gray-700 space-y-1">
                          <li>• Renter pays all court costs and attorney's fees</li>
                          <li>• 5% monthly interest on accounts over 30 days</li>
                          <li>• All legal costs and expenses</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </section>

        {/* Contact Section */}
        <section className="bg-gradient-to-r from-slate-600 to-gray-700 text-white px-4 md:px-6 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">Questions About Our Terms?</h2>
            <p className="text-xl text-slate-100 mb-8 max-w-2xl mx-auto">
              Our team is here to help clarify any terms or conditions. 
              Contact us for detailed explanations or legal questions.
            </p>
            
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <Link href="/customer/contact">
                <Card className="bg-white/10 border-white/20 hover:bg-white/20 transition-colors cursor-pointer">
                  <CardContent className="p-6 text-center">
                    <MessageCircle className="h-8 w-8 text-yellow-400 mx-auto mb-3" />
                    <h3 className="font-semibold mb-2">Messenger</h3>
                    <p className="text-sm text-slate-100">Quick legal questions</p>
                  </CardContent>
                </Card>
              </Link>
              
              <Link href="/customer/contact">
                <Card className="bg-white/10 border-white/20 hover:bg-white/20 transition-colors cursor-pointer">
                  <CardContent className="p-6 text-center">
                    <Phone className="h-8 w-8 text-yellow-400 mx-auto mb-3" />
                    <h3 className="font-semibold mb-2">Phone</h3>
                    <p className="text-sm text-slate-100">Speak with our team</p>
                  </CardContent>
                </Card>
              </Link>
              
              <Link href="/customer/contact">
                <Card className="bg-white/10 border-white/20 hover:bg-white/20 transition-colors cursor-pointer">
                  <CardContent className="p-6 text-center">
                    <Mail className="h-8 w-8 text-yellow-400 mx-auto mb-3" />
                    <h3 className="font-semibold mb-2">Email</h3>
                    <p className="text-sm text-slate-100">Written inquiries</p>
                  </CardContent>
                </Card>
              </Link>
            </div>
            
            <div className="text-center space-y-4">
              <Link href="/customer/contact">
                <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold mr-4">
                  Contact Us
                </Button>
              </Link>
              <Link href="/customer/faq">
                <Button size="lg" variant="secondary" className="bg-white/10 hover:bg-white/20 text-white border-white/20">
                  View FAQ
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </div>
    </PublicAppShell>
  )
}
