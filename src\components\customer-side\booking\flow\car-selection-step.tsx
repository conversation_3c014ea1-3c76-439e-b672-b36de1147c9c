"use client"

import * as React from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter, ArrowLeft, Car as CarIcon } from "lucide-react"
import { listCars } from "@/lib/store"
import type { Car } from "@/lib/types"
import { CategoryCard } from "@/components/customer-side/cars/category-card"
import { SimpleCarCard } from "@/components/customer-side/cars/simple-car-card"

interface CarSelectionStepProps {
  selectedCar: Car | null
  onCarSelect: (car: Car) => void
  initialCarId: string | null
}

export function CarSelectionStep({ selectedCar, onCarSelect, initialCarId }: CarSelectionStepProps) {
  const [selectedCategory, setSelectedCategory] = React.useState<string | null>(null)
  const [searchTerm, setSearchTerm] = React.useState("")
  
  const cars = listCars()
  const availableCars = cars.filter(car => car.status === "Available")
  
  // Group cars by category
  const carsByCategory = React.useMemo(() => {
    const grouped = availableCars.reduce((acc, car) => {
      if (!acc[car.type]) {
        acc[car.type] = []
      }
      acc[car.type].push(car)
      return acc
    }, {} as Record<string, Car[]>)
    
    return grouped
  }, [availableCars])

  // Auto-select car if initialCarId is provided
  React.useEffect(() => {
    if (initialCarId && !selectedCar) {
      const car = cars.find(c => c.id === initialCarId)
      if (car && car.status === "Available") {
        setSelectedCategory(car.type)
        onCarSelect(car)
      }
    }
  }, [initialCarId, selectedCar, cars, onCarSelect])

  // Filter cars in selected category by search term
  const filteredCars = React.useMemo(() => {
    if (!selectedCategory) return []
    
    const categoryCars = carsByCategory[selectedCategory] || []
    
    if (!searchTerm) return categoryCars
    
    return categoryCars.filter(car =>
      car.model.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [selectedCategory, carsByCategory, searchTerm])

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category)
    setSearchTerm("")
  }

  const handleBackToCategories = () => {
    setSelectedCategory(null)
    setSearchTerm("")
  }

  const categories = Object.keys(carsByCategory).sort()
  const totalAvailableCars = availableCars.length

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            {selectedCategory && (
              <Button
                variant="secondary"
                size="sm"
                onClick={handleBackToCategories}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Categories
              </Button>
            )}
            <div className="flex-1">
              <CardTitle className="text-xl">
                {selectedCategory ? `${selectedCategory} Vehicles` : "Select Vehicle Category"}
              </CardTitle>
              <p className="text-gray-600 mt-1">
                {selectedCategory 
                  ? `Choose from our available ${selectedCategory.toLowerCase()} vehicles` 
                  : "Browse our vehicle categories to find the perfect car for your journey"
                }
              </p>
            </div>
          </div>
        </CardHeader>
        
        {selectedCategory && (
          <CardContent className="pt-0">
            {/* Search within category */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={`Search ${selectedCategory} vehicles...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        )}
      </Card>

      {!selectedCategory ? (
        // Category Selection View
        <>
          {/* Stats */}
          <Card>
            <CardContent className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{totalAvailableCars}</div>
                  <div className="text-sm text-gray-600">Available Cars</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{categories.length}</div>
                  <div className="text-sm text-gray-600">Categories</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">₱{Math.min(...availableCars.map(c => c.price_per_day))}</div>
                  <div className="text-sm text-gray-600">Starting Price</div>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">24/7</div>
                  <div className="text-sm text-gray-600">Support</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Category Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category) => (
              <CategoryCard
                key={category}
                category={category}
                cars={carsByCategory[category]}
                onSelect={handleCategorySelect}
              />
            ))}
          </div>
        </>
      ) : (
        // Car Selection within Category
        <>
          {/* Category Stats */}
          <Card>
            <CardContent className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{filteredCars.length}</div>
                  <div className="text-sm text-gray-600">Available</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    ₱{Math.min(...carsByCategory[selectedCategory].map(c => c.price_per_day))}
                  </div>
                  <div className="text-sm text-gray-600">Min Price</div>
                </div>
                <div className="text-center p-3 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">
                    ₱{Math.max(...carsByCategory[selectedCategory].map(c => c.price_per_day))}
                  </div>
                  <div className="text-sm text-gray-600">Max Price</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {[...new Set(carsByCategory[selectedCategory].map(c => c.transmission))].length}
                  </div>
                  <div className="text-sm text-gray-600">Transmission Types</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Car Grid */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {filteredCars.map((car) => (
              <SimpleCarCard
                key={car.id}
                car={car}
                isSelected={selectedCar?.id === car.id}
                onSelect={onCarSelect}
              />
            ))}
          </div>

          {filteredCars.length === 0 && (
            <Card>
              <CardContent className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <CarIcon className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No vehicles found</h3>
                <p className="text-gray-600">
                  {searchTerm 
                    ? `No ${selectedCategory} vehicles match "${searchTerm}". Try adjusting your search.`
                    : `No ${selectedCategory} vehicles are currently available.`
                  }
                </p>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  )
}
