"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

/**
 * Typography System
 * 
 * Comprehensive typography component system with:
 * - Font pairings (<PERSON>eist Sans for headings, <PERSON>eist Sans for body)
 * - Proper scale and hierarchy
 * - Line length controls (50-60 chars desktop, 30-40 mobile)
 * - WCAG compliant contrast ratios
 * - Consistent spacing system
 */

// Typography variants using CVA for consistency
const typographyVariants = cva(
  // Base styles with optimal line height and spacing
  "text-balance leading-relaxed",
  {
    variants: {
      variant: {
        // Display headings - Large, attention-grabbing
        "display-1": "text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight text-gray-900 leading-none",
        "display-2": "text-3xl md:text-5xl lg:text-6xl font-bold tracking-tight text-gray-900 leading-tight",
        
        // Standard headings with proper hierarchy
        "h1": "text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight text-gray-900 leading-tight",
        "h2": "text-2xl md:text-3xl lg:text-4xl font-semibold tracking-tight text-gray-900 leading-tight",
        "h3": "text-xl md:text-2xl lg:text-3xl font-semibold tracking-tight text-gray-900 leading-snug",
        "h4": "text-lg md:text-xl lg:text-2xl font-semibold tracking-tight text-gray-900 leading-snug",
        "h5": "text-base md:text-lg lg:text-xl font-semibold tracking-tight text-gray-900 leading-snug",
        "h6": "text-sm md:text-base lg:text-lg font-semibold tracking-tight text-gray-900 leading-snug",
        
        // Body text with optimal reading experience
        "body-large": "text-lg md:text-xl leading-relaxed text-gray-700 max-w-prose",
        "body": "text-base md:text-lg leading-relaxed text-gray-700 max-w-prose",
        "body-small": "text-sm md:text-base leading-relaxed text-gray-600 max-w-prose",
        
        // UI text for interfaces
        "label": "text-sm font-medium text-gray-900 leading-none",
        "caption": "text-xs md:text-sm text-gray-500 leading-tight",
        "overline": "text-xs font-semibold uppercase tracking-wider text-gray-500 leading-none",
        
        // Special purpose
        "lead": "text-xl md:text-2xl leading-relaxed text-gray-600 font-light max-w-prose",
        "quote": "text-lg md:text-xl italic leading-relaxed text-gray-600 border-l-4 border-gray-300 pl-4 max-w-prose",
        "code": "text-sm font-mono bg-gray-100 text-gray-800 px-2 py-1 rounded border",
      },
      align: {
        left: "text-left",
        center: "text-center",
        right: "text-right",
        justify: "text-justify",
      },
      color: {
        default: "",
        muted: "text-gray-600",
        subtle: "text-gray-500",
        inverse: "text-white",
        primary: "text-blue-600",
        success: "text-green-600",
        warning: "text-amber-600",
        error: "text-red-600",
      },
      weight: {
        light: "font-light",
        normal: "font-normal",
        medium: "font-medium",
        semibold: "font-semibold",
        bold: "font-bold",
      },
    },
    defaultVariants: {
      variant: "body",
      align: "left",
      color: "default",
    },
  }
)

interface TypographyProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof typographyVariants> {
  as?: keyof JSX.IntrinsicElements
  maxWidth?: "none" | "xs" | "sm" | "md" | "lg" | "xl" | "prose"
}

function Typography({
  className,
  variant,
  align,
  color,
  weight,
  maxWidth,
  as,
  children,
  ...props
}: TypographyProps) {
  // Determine the semantic HTML element
  const getElement = () => {
    if (as) return as
    
    switch (variant) {
      case "display-1":
      case "display-2":
      case "h1":
        return "h1"
      case "h2":
        return "h2"
      case "h3":
        return "h3"
      case "h4":
        return "h4"
      case "h5":
        return "h5"
      case "h6":
        return "h6"
      case "lead":
      case "body-large":
      case "body":
      case "body-small":
        return "p"
      case "label":
        return "label"
      case "caption":
        return "span"
      case "overline":
        return "div"
      case "quote":
        return "blockquote"
      case "code":
        return "code"
      default:
        return "p"
    }
  }

  const Element = getElement()
  
  // Apply max-width classes for optimal reading length
  const maxWidthClass = {
    none: "",
    xs: "max-w-xs",
    sm: "max-w-sm", 
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    prose: "max-w-prose", // ~65ch optimal reading width
  }[maxWidth || "none"]

  return (
    <Element
      className={cn(
        typographyVariants({ variant, align, color, weight }),
        maxWidthClass,
        className
      )}
      {...props}
    >
      {children}
    </Element>
  )
}

// Convenience components for common use cases
const Heading = React.forwardRef<
  HTMLHeadingElement,
  Omit<TypographyProps, "variant"> & { level?: 1 | 2 | 3 | 4 | 5 | 6 }
>(({ level = 1, ...props }, ref) => {
  const variant = `h${level}` as const
  return <Typography ref={ref} variant={variant} {...props} />
})
Heading.displayName = "Heading"

const Text = React.forwardRef<
  HTMLParagraphElement,
  Omit<TypographyProps, "variant"> & { size?: "small" | "base" | "large" }
>(({ size = "base", ...props }, ref) => {
  const variant = size === "small" ? "body-small" : size === "large" ? "body-large" : "body"
  return <Typography ref={ref} variant={variant} maxWidth="prose" {...props} />
})
Text.displayName = "Text"

const Label = React.forwardRef<
  HTMLLabelElement,
  Omit<TypographyProps, "variant" | "as">
>(({ ...props }, ref) => {
  return <Typography ref={ref} variant="label" as="label" {...props} />
})
Label.displayName = "Label"

const Caption = React.forwardRef<
  HTMLSpanElement,
  Omit<TypographyProps, "variant" | "as">
>(({ ...props }, ref) => {
  return <Typography ref={ref} variant="caption" as="span" {...props} />
})
Caption.displayName = "Caption"

const Lead = React.forwardRef<
  HTMLParagraphElement,
  Omit<TypographyProps, "variant">
>(({ ...props }, ref) => {
  return <Typography ref={ref} variant="lead" maxWidth="prose" {...props} />
})
Lead.displayName = "Lead"

const Quote = React.forwardRef<
  HTMLQuoteElement,
  Omit<TypographyProps, "variant">
>(({ ...props }, ref) => {
  return <Typography ref={ref} variant="quote" maxWidth="prose" {...props} />
})
Quote.displayName = "Quote"

const Code = React.forwardRef<
  HTMLElement,
  Omit<TypographyProps, "variant">
>(({ ...props }, ref) => {
  return <Typography ref={ref} variant="code" {...props} />
})
Code.displayName = "Code"

export {
  Typography,
  Heading,
  Text,
  Label,
  Caption,
  Lead,
  Quote,
  Code,
  typographyVariants,
  type TypographyProps,
}
