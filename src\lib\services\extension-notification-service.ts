"use client";

import { createClient } from "@/lib/supabase/client";

export interface ExtensionNotification {
  id: string;
  extension_request_id: string;
  recipient_id: string;
  recipient_type: "customer" | "admin";
  notification_type: "extension_requested" | "extension_approved" | "extension_rejected" | "extension_expired";
  title: string;
  message: string;
  is_read: boolean;
  created_at: string;
  extension_requests?: {
    id: string;
    booking_id: string;
    status: string;
    bookings: {
      booking_ref: string;
      cars: {
        model: string;
        plate_number: string;
      };
    };
    profiles: {
      full_name: string;
    };
  };
}

class ExtensionNotificationService {
  private supabase = createClient();

  /**
   * Get notifications for a specific user
   */
  async getNotifications(
    userId: string,
    userType: "customer" | "admin" = "customer",
    limit: number = 20
  ): Promise<{ data: ExtensionNotification[] | null; error: any }> {
    try {
      const { data, error } = await this.supabase
        .from("booking_extension_notifications")
        .select(`
          *,
          extension_requests!inner(
            id,
            booking_id,
            status,
            bookings!inner(
              booking_ref,
              cars!inner(
                model,
                plate_number
              )
            ),
            profiles!inner(
              full_name
            )
          )
        `)
        .eq("recipient_id", userId)
        .eq("recipient_type", userType)
        .order("created_at", { ascending: false })
        .limit(limit);

      if (error) {
        console.error("Error fetching extension notifications:", error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error("Extension notification fetch error:", error);
      return { data: null, error };
    }
  }

  /**
   * Get unread notification count for a user
   */
  async getUnreadCount(
    userId: string,
    userType: "customer" | "admin" = "customer"
  ): Promise<{ count: number; error: any }> {
    try {
      const { count, error } = await this.supabase
        .from("booking_extension_notifications")
        .select("*", { count: "exact", head: true })
        .eq("recipient_id", userId)
        .eq("recipient_type", userType)
        .eq("is_read", false);

      if (error) {
        console.error("Error fetching unread count:", error);
        return { count: 0, error };
      }

      return { count: count || 0, error: null };
    } catch (error) {
      console.error("Unread count fetch error:", error);
      return { count: 0, error };
    }
  }

  /**
   * Mark a notification as read
   */
  async markAsRead(notificationId: string): Promise<{ success: boolean; error: any }> {
    try {
      const { error } = await this.supabase
        .from("booking_extension_notifications")
        .update({ is_read: true })
        .eq("id", notificationId);

      if (error) {
        console.error("Error marking notification as read:", error);
        return { success: false, error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error("Mark as read error:", error);
      return { success: false, error };
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(
    userId: string,
    userType: "customer" | "admin" = "customer"
  ): Promise<{ success: boolean; error: any }> {
    try {
      const { error } = await this.supabase
        .from("booking_extension_notifications")
        .update({ is_read: true })
        .eq("recipient_id", userId)
        .eq("recipient_type", userType)
        .eq("is_read", false);

      if (error) {
        console.error("Error marking all notifications as read:", error);
        return { success: false, error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error("Mark all as read error:", error);
      return { success: false, error };
    }
  }

  /**
   * Delete a notification
   */
  async deleteNotification(notificationId: string): Promise<{ success: boolean; error: any }> {
    try {
      const { error } = await this.supabase
        .from("booking_extension_notifications")
        .delete()
        .eq("id", notificationId);

      if (error) {
        console.error("Error deleting notification:", error);
        return { success: false, error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error("Delete notification error:", error);
      return { success: false, error };
    }
  }

  /**
   * Subscribe to real-time notification updates
   */
  subscribeToNotifications(
    userId: string,
    userType: "customer" | "admin",
    onNotification: (notification: ExtensionNotification) => void,
    onError?: (error: any) => void
  ) {
    const channel = this.supabase
      .channel(`extension_notifications_${userId}`)
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "booking_extension_notifications",
          filter: `recipient_id=eq.${userId}`,
        },
        async (payload: any) => {
          // Fetch the complete notification with relations
          const { data, error } = await this.getNotifications(userId, userType, 1);
          
          if (error) {
            onError?.(error);
            return;
          }

          if (data && data.length > 0) {
            onNotification(data[0]);
          }
        }
      )
      .subscribe();

    return () => {
      this.supabase.removeChannel(channel);
    };
  }

  /**
   * Send email notification (integrate with existing email service)
   */
  async sendEmailNotification(
    extensionRequestId: string,
    notificationType: "extension_approved" | "extension_rejected"
  ): Promise<{ success: boolean; error: any }> {
    try {
      // This would integrate with your existing email service
      // For now, we'll call the Supabase Edge Function for sending emails
      const { data, error } = await this.supabase.functions.invoke("send-email", {
        body: {
          type: "extension_notification",
          extensionRequestId,
          notificationType,
        },
      });

      if (error) {
        console.error("Error sending email notification:", error);
        return { success: false, error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error("Email notification error:", error);
      return { success: false, error };
    }
  }

  /**
   * Create a manual notification (for testing purposes)
   */
  async createTestNotification(
    extensionRequestId: string,
    recipientId: string,
    recipientType: "customer" | "admin",
    notificationType: ExtensionNotification["notification_type"],
    title: string,
    message: string
  ): Promise<{ success: boolean; error: any }> {
    try {
      const { error } = await this.supabase
        .from("booking_extension_notifications")
        .insert([{
          extension_request_id: extensionRequestId,
          recipient_id: recipientId,
          recipient_type: recipientType,
          notification_type: notificationType,
          title,
          message,
          is_read: false,
        }]);

      if (error) {
        console.error("Error creating test notification:", error);
        return { success: false, error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error("Create test notification error:", error);
      return { success: false, error };
    }
  }
}

// Export singleton instance
export const extensionNotificationService = new ExtensionNotificationService();

// Helper functions for notification formatting
export const formatNotificationTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffMinutes = Math.floor(diffTime / (1000 * 60));
  const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffMinutes < 1) {
    return "Just now";
  } else if (diffMinutes < 60) {
    return `${diffMinutes}m ago`;
  } else if (diffHours < 24) {
    return `${diffHours}h ago`;
  } else if (diffDays < 7) {
    return `${diffDays}d ago`;
  } else {
    return date.toLocaleDateString();
  }
};

export const getNotificationIcon = (notificationType: ExtensionNotification["notification_type"]) => {
  switch (notificationType) {
    case "extension_requested":
      return "🔔";
    case "extension_approved":
      return "✅";
    case "extension_rejected":
      return "❌";
    case "extension_expired":
      return "⏰";
    default:
      return "📋";
  }
};

export const getNotificationColor = (notificationType: ExtensionNotification["notification_type"]) => {
  switch (notificationType) {
    case "extension_requested":
      return "bg-blue-100 text-blue-700 border-blue-200";
    case "extension_approved":
      return "bg-green-100 text-green-700 border-green-200";
    case "extension_rejected":
      return "bg-red-100 text-red-700 border-red-200";
    case "extension_expired":
      return "bg-yellow-100 text-yellow-700 border-yellow-200";
    default:
      return "bg-gray-100 text-gray-700 border-gray-200";
  }
};
