/**
 * Central utility for calculating booking totals across the customer flow
 * This ensures consistency and accuracy in all total calculations
 */

import type { BookingData } from "@/components/customer-side/booking/flow/booking-flow";
import type { Car } from "@/lib/types";
import { SPECIAL_SERVICES } from "@/lib/pricing-constants";
import { calculateDayBasedCost, formatRentalDurationWithDateRange } from "@/utils/booking-date-validation";

export interface BookingTotalDetails {
  /** Number of rental days */
  days: number;
  /** Base rental cost (daily rate × days) */
  baseRentalCost: number;
  /** Pickup delivery fee */
  pickupDeliveryFee: number;
  /** Return delivery fee */
  returnDeliveryFee: number;
  /** Total of all delivery fees */
  totalDeliveryFees: number;
  /** Final total amount (base rental + all fees) */
  totalAmount: number;
  /** Whether this is a special service booking */
  isSpecialService: boolean;
  /** Any validation error */
  error?: string;
}

/**
 * Calculate complete booking total including all fees
 * This is the single source of truth for all booking calculations
 */
export function calculateBookingTotal(
  bookingData: BookingData,
  selectedCar: Car | null
): BookingTotalDetails {
  // Handle special service bookings
  if (bookingData.isSpecialService) {
    const basePrice = SPECIAL_SERVICES.VAN_WITH_DRIVER.BASE_PRICE;
    return {
      days: 1,
      baseRentalCost: basePrice,
      pickupDeliveryFee: 0,
      returnDeliveryFee: 0,
      totalDeliveryFees: 0,
      totalAmount: basePrice,
      isSpecialService: true,
    };
  }

  // Validate required data
  if (!selectedCar) {
    return {
      days: 1,
      baseRentalCost: 0,
      pickupDeliveryFee: 0,
      returnDeliveryFee: 0,
      totalDeliveryFees: 0,
      totalAmount: 0,
      isSpecialService: false,
      error: "No vehicle selected",
    };
  }

  if (!bookingData.pickUpDate || !bookingData.dropOffDate) {
    return {
      days: 1,
      baseRentalCost: selectedCar.price_per_day,
      pickupDeliveryFee: bookingData.deliveryFee || 0,
      returnDeliveryFee: bookingData.returnFee || 0,
      totalDeliveryFees: bookingData.totalDeliveryFees || 0,
      totalAmount: selectedCar.price_per_day + (bookingData.totalDeliveryFees || 0),
      isSpecialService: false,
      error: "Incomplete booking dates",
    };
  }

  // Calculate day-based rental cost
  const dayBasedCost = calculateDayBasedCost(
    bookingData.pickUpDate,
    bookingData.dropOffDate,
    selectedCar.price_per_day
  );

  if (!dayBasedCost.isValid) {
    return {
      days: 1,
      baseRentalCost: selectedCar.price_per_day,
      pickupDeliveryFee: bookingData.deliveryFee || 0,
      returnDeliveryFee: bookingData.returnFee || 0,
      totalDeliveryFees: bookingData.totalDeliveryFees || 0,
      totalAmount: selectedCar.price_per_day + (bookingData.totalDeliveryFees || 0),
      isSpecialService: false,
      error: dayBasedCost.error,
    };
  }

  // Extract delivery fee information
  const pickupDeliveryFee = bookingData.deliveryFee || 0;
  const returnDeliveryFee = bookingData.returnFee || 0;
  const totalDeliveryFees = bookingData.totalDeliveryFees || 0;

  // Calculate final total
  const baseRentalCost = dayBasedCost.totalCost;
  const totalAmount = baseRentalCost + totalDeliveryFees;

  return {
    days: dayBasedCost.days,
    baseRentalCost,
    pickupDeliveryFee,
    returnDeliveryFee,
    totalDeliveryFees,
    totalAmount,
    isSpecialService: false,
  };
}

/**
 * Format currency for display
 */
export function formatBookingCurrency(amount: number): string {
  return `₱${amount.toFixed(2)}`;
}

/**
 * Get a detailed breakdown of the booking cost for display purposes
 */
export function getBookingCostBreakdown(
  bookingData: BookingData,
  selectedCar: Car | null
): {
  dailyRate: string;
  days: number;
  daysLabel: string;
  subtotal: string;
  pickupFee: string;
  returnFee: string;
  totalDeliveryFees: string;
  grandTotal: string;
  hasDeliveryFees: boolean;
  isSpecialService: boolean;
} {
  const totals = calculateBookingTotal(bookingData, selectedCar);

  // Use enhanced duration format with date range
  const enhancedDuration = formatRentalDurationWithDateRange(
    bookingData.pickUpDate,
    bookingData.dropOffDate
  );

  return {
    dailyRate: formatBookingCurrency(selectedCar?.price_per_day || 0),
    days: totals.days,
    daysLabel: enhancedDuration,
    subtotal: formatBookingCurrency(totals.baseRentalCost),
    pickupFee: formatBookingCurrency(totals.pickupDeliveryFee),
    returnFee: formatBookingCurrency(totals.returnDeliveryFee),
    totalDeliveryFees: formatBookingCurrency(totals.totalDeliveryFees),
    grandTotal: formatBookingCurrency(totals.totalAmount),
    hasDeliveryFees: totals.totalDeliveryFees > 0,
    isSpecialService: totals.isSpecialService,
  };
}
