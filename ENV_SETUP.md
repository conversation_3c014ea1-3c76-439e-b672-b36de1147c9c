# Environment Variables Setup for PathLink

## Required Environment Variables

Add these to your production environment (Netlify, Vercel, etc.):

```env
# GPS Device Authentication Token
INBOUND_DEVICE_TOKEN=your-secure-device-token-here

# OpenAI API Key for Chatbot and RAG Embeddings
OPENAI_API_KEY=your-openai-api-key-here

# Site URL (should be set automatically by your hosting provider)
NEXT_PUBLIC_SITE_URL=https://olliesrentalcar.pathlinkio.app

# Supabase Configuration (you should already have these)
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

## For Local Development

Create a `.env.local` file in your project root:

```env
INBOUND_DEVICE_TOKEN=dev-token-12345
OPENAI_API_KEY=your-openai-api-key-here
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

## Security Notes

1. **INBOUND_DEVICE_TOKEN**: Use a strong, random token (32+ characters)
2. **Keep tokens secret**: Never commit tokens to version control
3. **Different tokens**: Use different tokens for development and production

## Generating a Secure Token

You can generate a secure token using:

```bash
# Using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Or online at: https://www.uuidgenerator.net/
```

## ESP32 Configuration

The ESP32 doesn't need the INBOUND_DEVICE_TOKEN - it sends encrypted GPS data that the server decrypts and then authenticates internally.
