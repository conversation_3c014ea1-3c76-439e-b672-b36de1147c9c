import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { 
  Search, 
  Filter, 
  RotateCcw, 
  X,
  SlidersHorizontal,
  ChevronDown
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface TrackerFilters {
  searchTerm: string
  categoryFilter: string
  branchFilter: string
  timeWindow: 'live' | '15m' | '1h' | '24h'
  sortBy: 'lastSeen' | 'speed' | 'distance'
  showClusters: boolean
  showTrails: boolean
  showGeofences: boolean
  denseList: boolean
}

interface TrackerToolbarProps {
  filters: TrackerFilters
  onFilterChange: (filters: Partial<TrackerFilters>) => void
  onResetFilters: () => void
  onRefreshData: () => void
  vehicleCount: number
  selectedCount: number
}

export function TrackerToolbar({
  filters,
  onFilterChange,
  onResetFilters,
  onRefreshData,
  vehicleCount,
  selectedCount
}: TrackerToolbarProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const timeWindowOptions = [
    { value: 'live', label: 'Live' },
    { value: '15m', label: 'Last 15m' },
    { value: '1h', label: 'Last 1h' },
    { value: '24h', label: 'Last 24h' }
  ]

  const sortOptions = [
    { value: 'lastSeen', label: 'Last Seen' },
    { value: 'speed', label: 'Speed' },
    { value: 'distance', label: 'Distance' }
  ]

  const hasActiveFilters = 
    filters.searchTerm !== '' ||
    filters.categoryFilter !== 'all' ||
    filters.branchFilter !== 'all' ||
    filters.timeWindow !== 'live'

  return (
    <div className="space-y-4">
      {/* Primary Controls */}
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1 max-w-md">
          <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search vehicles, plates, or drivers..."
            value={filters.searchTerm}
            onChange={(e) => onFilterChange({ searchTerm: e.target.value })}
            className="pl-9 h-10"
          />
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-10"
          >
            <SlidersHorizontal className="w-4 h-4 mr-2" />
            Filters
            <ChevronDown className={cn(
              "w-4 h-4 ml-1 transition-transform",
              isExpanded && "rotate-180"
            )} />
          </Button>

          <Separator orientation="vertical" className="h-6" />
          
          <Button
            variant="secondary"
            size="sm"
            onClick={onRefreshData}
            className="h-10"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Vehicle Count & Filter Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="text-sm text-muted-foreground">
            <span className="font-medium">{vehicleCount}</span> vehicles
            {selectedCount > 0 && (
              <span className="ml-2">
                • <span className="font-medium">{selectedCount}</span> selected
              </span>
            )}
          </div>
          
          {hasActiveFilters && (
            <Button
              variant="tertiary"
              size="sm"
              onClick={onResetFilters}
              className="h-7 px-2 text-xs"
            >
              <X className="w-3 h-3 mr-1" />
              Clear filters
            </Button>
          )}
        </div>
      </div>

      {/* Advanced Filters */}
      {isExpanded && (
        <div className="bg-muted/30 rounded-lg p-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Time Window */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Time Window</label>
              <Select 
                value={filters.timeWindow} 
                onValueChange={(value: any) => onFilterChange({ timeWindow: value })}
              >
                <SelectTrigger className="h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timeWindowOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sort By */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Sort By</label>
              <Select 
                value={filters.sortBy} 
                onValueChange={(value: any) => onFilterChange({ sortBy: value })}
              >
                <SelectTrigger className="h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Category Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Category</label>
              <Select 
                value={filters.categoryFilter} 
                onValueChange={(value) => onFilterChange({ categoryFilter: value })}
              >
                <SelectTrigger className="h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="sedan">Sedan</SelectItem>
                  <SelectItem value="suv">SUV</SelectItem>
                  <SelectItem value="hatchback">Hatchback</SelectItem>
                  <SelectItem value="motorcycle">Motorcycle</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Branch Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Branch</label>
              <Select 
                value={filters.branchFilter} 
                onValueChange={(value) => onFilterChange({ branchFilter: value })}
              >
                <SelectTrigger className="h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Branches</SelectItem>
                  <SelectItem value="manila">Manila</SelectItem>
                  <SelectItem value="cebu">Cebu</SelectItem>
                  <SelectItem value="davao">Davao</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Toggle Options */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Cluster markers</span>
              <Switch
                checked={filters.showClusters}
                onCheckedChange={(checked) => onFilterChange({ showClusters: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Show trails</span>
              <Switch
                checked={filters.showTrails}
                onCheckedChange={(checked) => onFilterChange({ showTrails: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Show geofences</span>
              <Switch
                checked={filters.showGeofences}
                onCheckedChange={(checked) => onFilterChange({ showGeofences: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Dense list</span>
              <Switch
                checked={filters.denseList}
                onCheckedChange={(checked) => onFilterChange({ denseList: checked })}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
