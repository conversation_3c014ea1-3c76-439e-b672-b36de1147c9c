-- Script to verify and fix document type consistency
-- This script checks the current database state and ensures it matches our requirements
-- for exactly 4 document types (drivers_license, government_id, proof_of_billing, proof_of_payment)

BEGIN;

-- First, check what document types are currently allowed in the constraint
SELECT pg_get_constraintdef(oid)
FROM pg_constraint
WHERE conname = 'booking_documents_document_type_check';

-- Check the current implementation of check_booking_documents_complete function
SELECT pg_get_functiondef('check_booking_documents_complete(uuid)'::regprocedure);

-- Check the current implementation of get_booking_document_status function
SELECT pg_get_functiondef('get_booking_document_status(uuid)'::regprocedure);

-- Find any documents with non-standard document types
SELECT document_type, count(*)
FROM booking_documents
GROUP BY document_type
ORDER BY document_type;

-- This will ensure the constraint is correct for our 4 required document types
ALTER TABLE public.booking_documents
  DROP CONSTRAINT IF EXISTS booking_documents_document_type_check;

ALTER TABLE public.booking_documents
  ADD CONSTRAINT booking_documents_document_type_check
  CHECK (
    document_type IN (
      'drivers_license',
      'government_id',
      'proof_of_billing',
      'proof_of_payment'
    )
  );

-- Update the check function to ensure exactly 4 required docs
CREATE OR REPLACE FUNCTION check_booking_documents_complete(booking_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    required_docs TEXT[] := ARRAY[
        'drivers_license',
        'government_id',
        'proof_of_billing',
        'proof_of_payment'
    ];
    uploaded_count INTEGER;
BEGIN
    SELECT COUNT(DISTINCT document_type)
    INTO uploaded_count
    FROM public.booking_documents 
    WHERE booking_id = booking_uuid 
    AND document_type = ANY(required_docs);
    
    RETURN uploaded_count = array_length(required_docs, 1);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update status function to reflect 4 required docs
CREATE OR REPLACE FUNCTION get_booking_document_status(booking_uuid UUID)
RETURNS TABLE (
    total_required INTEGER,
    uploaded_count INTEGER,
    pending_count INTEGER,
    approved_count INTEGER,
    rejected_count INTEGER,
    is_complete BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        4 as total_required,
        COUNT(*)::INTEGER as uploaded_count,
        COUNT(*) FILTER (WHERE verification_status = 'pending')::INTEGER as pending_count,
        COUNT(*) FILTER (WHERE verification_status = 'approved')::INTEGER as approved_count,
        COUNT(*) FILTER (WHERE verification_status = 'rejected')::INTEGER as rejected_count,
        check_booking_documents_complete(booking_uuid) as is_complete
    FROM public.booking_documents 
    WHERE booking_id = booking_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Map old document types to new ones if needed
-- This is commented out by default - review and uncomment if needed after checking results
/*
UPDATE booking_documents
SET document_type = 'proof_of_billing'
WHERE document_type = 'proof_of_age';

UPDATE booking_documents
SET document_type = 'proof_of_payment'
WHERE document_type = 'security_deposit_confirmation' 
AND NOT EXISTS (
    SELECT 1 FROM booking_documents bd2 
    WHERE bd2.booking_id = booking_documents.booking_id 
    AND bd2.document_type = 'proof_of_payment'
);
*/

COMMIT;
