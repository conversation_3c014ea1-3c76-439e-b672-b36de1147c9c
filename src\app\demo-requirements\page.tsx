"use client";

import * as React from "react";
import { RequirementsUploadStep } from "@/components/customer-side/booking/flow/requirements-upload-step";
import type { BookingData } from "@/components/customer-side/booking/flow/booking-flow";

export default function DemoRequirementsPage() {
  const [bookingData, setBookingData] = React.useState<BookingData>({
    selectedCar: null,
    pickUpLocation: "Manila",
    dropOffLocation: "Quezon City",
    pickUpDate: "2024-08-20",
    pickUpTime: "09:00",
    dropOffDate: "2024-08-22",
    dropOffTime: "18:00",
    customerName: "",
    customerEmail: "",
    customerPhone: "",
    notes: "",
    driversLicense: [],
    governmentId: [],
    proofOfAge: [],
    securityDeposit: [],
  });

  const handleUpdate = (updates: Partial<BookingData>) => {
    setBookingData((prev) => ({ ...prev, ...updates }));
  };

  return (
    <div className="max-w-6xl mx-auto p-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Requirements Upload Step Demo
        </h1>
        <p className="text-gray-600">
          Testing the requirements upload step for the booking flow
        </p>
      </div>

      <RequirementsUploadStep
        bookingData={bookingData}
        onUpdate={handleUpdate}
      />

      {/* Debug info */}
      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-medium mb-2">Debug Info:</h3>
        <pre className="text-xs text-gray-600">
          {JSON.stringify(
            {
              driversLicense: bookingData.driversLicense?.length || 0,
              governmentId: bookingData.governmentId?.length || 0,
              proofOfAge: bookingData.proofOfAge?.length || 0,
              securityDeposit: bookingData.securityDeposit?.length || 0,
            },
            null,
            2
          )}
        </pre>
      </div>
    </div>
  );
}
