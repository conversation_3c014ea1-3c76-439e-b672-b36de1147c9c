"use client";

import * as React from "react";
import {
  DocumentUpload,
  type DocumentFile,
} from "@/components/ui/document-upload";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function DemoUploadPage() {
  const [driversLicense, setDriversLicense] = React.useState<DocumentFile[]>(
    []
  );
  const [governmentId, setGovernmentId] = React.useState<DocumentFile[]>([]);

  return (
    <div className="max-w-4xl mx-auto p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Document Upload Demo
        </h1>
        <p className="text-gray-600">
          Testing the document upload component for the booking flow
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Driver's License</CardTitle>
          </CardHeader>
          <CardContent>
            <DocumentUpload
              label="Upload Driver's License"
              description="Upload a clear photo of your driver's license"
              files={driversLicense}
              onChange={setDriversLicense}
              accept="image/jpeg,image/jpg,image/png,application/pdf"
              maxFiles={2}
              required
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Government ID</CardTitle>
          </CardHeader>
          <CardContent>
            <DocumentUpload
              label="Upload Government ID"
              description="Upload your valid government-issued identification"
              files={governmentId}
              onChange={setGovernmentId}
              accept="image/jpeg,image/jpg,image/png,application/pdf"
              maxFiles={2}
              required
            />
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Upload Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Driver's License Files:</h4>
              {driversLicense.length === 0 ? (
                <p className="text-sm text-gray-500">No files uploaded</p>
              ) : (
                <ul className="space-y-1">
                  {driversLicense.map((file) => (
                    <li key={file.id} className="text-sm">
                      <span className="font-medium">{file.file.name}</span> -
                      <span
                        className={`ml-1 ${
                          file.status === "completed"
                            ? "text-green-600"
                            : file.status === "error"
                            ? "text-red-600"
                            : "text-blue-600"
                        }`}
                      >
                        {file.status}
                      </span>
                    </li>
                  ))}
                </ul>
              )}
            </div>

            <div>
              <h4 className="font-medium mb-2">Government ID Files:</h4>
              {governmentId.length === 0 ? (
                <p className="text-sm text-gray-500">No files uploaded</p>
              ) : (
                <ul className="space-y-1">
                  {governmentId.map((file) => (
                    <li key={file.id} className="text-sm">
                      <span className="font-medium">{file.file.name}</span> -
                      <span
                        className={`ml-1 ${
                          file.status === "completed"
                            ? "text-green-600"
                            : file.status === "error"
                            ? "text-red-600"
                            : "text-blue-600"
                        }`}
                      >
                        {file.status}
                      </span>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
