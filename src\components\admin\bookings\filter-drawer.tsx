"use client"

import * as React from "react"
import { Calendar, Filter, X, Search } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>er, Drawer<PERSON>ontent, <PERSON>er<PERSON>eader, <PERSON>er<PERSON><PERSON><PERSON>, DrawerTrigger } from "@/components/ui/drawer"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRange } from "react-day-picker"
import { format, addDays, startOfToday } from "date-fns"
import { cn } from "@/lib/utils"
import { getBookingStatusOptions } from "@/lib/utils/booking-status"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { BookingFilters } from "./booking-toolbar"

interface FilterDrawerProps {
  filters: BookingFilters
  onFiltersChange: (filters: BookingFilters) => void
  availableVehicles?: string[]
  className?: string
}

export function FilterDrawer({
  filters,
  onFiltersChange,
  availableVehicles = [],
  className
}: FilterDrawerProps) {
  const [open, setOpen] = React.useState(false)
  const [isCalendarOpen, setIsCalendarOpen] = React.useState(false)

  const updateFilter = (key: keyof BookingFilters, value: any) => {
    onFiltersChange({ ...filters, [key]: value })
  }

  const setQuickDateRange = (preset: "today" | "week" | "custom") => {
    const today = startOfToday()
    switch (preset) {
      case "today":
        updateFilter("dateRange", { from: today, to: today })
        break
      case "week":
        updateFilter("dateRange", { from: today, to: addDays(today, 7) })
        break
      case "custom":
        // Keep current range or clear it
        break
    }
  }

  const clearFilters = () => {
    onFiltersChange({
      search: "",
      status: "all",
      paymentStatus: "all",
      vehicle: "all",
      location: "all",
      extensionStatus: "all",
      dateRange: { from: null, to: null },
    })
  }

  const hasActiveFilters =
    filters.search ||
    filters.status !== "all" ||
    filters.paymentStatus !== "all" ||
    filters.vehicle !== "all" ||
    filters.location !== "all" ||
    filters.dateRange.from ||
    filters.dateRange.to

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button 
          variant={hasActiveFilters ? "primary" : "secondary"}
          size="lg"
          className={cn(
            "w-full min-h-[52px] xs:min-h-[56px] text-sm xs:text-base font-semibold shadow-lg flex items-center justify-center gap-2 mt-3 xs:mt-4 rounded-xl transition-all duration-200",
            hasActiveFilters && "bg-blue-600 hover:bg-blue-700 border-blue-500",
            !hasActiveFilters && "bg-white border-2 border-gray-300 text-gray-700 hover:border-blue-400 hover:bg-blue-50",
            className
          )}
        >
          <Filter className="h-4 w-4 xs:h-5 xs:w-5 flex-shrink-0" />
          <span className="truncate">{hasActiveFilters ? "Filters Applied" : "Filters"}</span>
          {hasActiveFilters && (
            <span className="inline-flex items-center justify-center w-5 h-5 xs:w-6 xs:h-6 rounded-full bg-white text-blue-700 text-xs font-bold ml-1 flex-shrink-0">
              {Object.values(filters).filter(val => 
                val && 
                (typeof val === 'string' ? val !== 'all' : 
                 val.from || val.to)
              ).length}
            </span>
          )}
        </Button>
      </DrawerTrigger>
      <DrawerContent className="h-[85vh] xs:h-[80vh] rounded-t-2xl xs:rounded-t-3xl p-4 xs:p-6 bg-white border border-gray-200 shadow-2xl">
        <DrawerHeader className="flex flex-row items-center justify-between mb-4 xs:mb-6">
          <DrawerTitle className="text-lg xs:text-xl sm:text-2xl font-bold text-gray-900">Booking Filters</DrawerTitle>
          {hasActiveFilters && (
            <Button
              variant="destructive-outline"
              size="sm"
              onClick={() => {
                clearFilters()
                setOpen(false)
              }}
              className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-500 hover:bg-red-50"
            >
              <X className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          )}
        </DrawerHeader>
        
        <div className="space-y-4 xs:space-y-6 overflow-y-auto max-h-[calc(85vh-120px)] xs:max-h-[calc(80vh-120px)] pb-4 xs:pb-6">
          {/* Search */}
          <div className="space-y-2">
            <label className="text-sm xs:text-base font-medium text-gray-700">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 xs:h-5 xs:w-5 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search by renter, booking ID, or car..."
                value={filters.search}
                onChange={(e) => updateFilter("search", e.target.value)}
                className={cn(
                  "pl-10 xs:pl-12 border-2 min-h-[52px] xs:min-h-[56px] text-sm xs:text-base transition-all duration-200 font-medium rounded-xl",
                  filters.search
                    ? "border-blue-300 bg-blue-50 text-blue-900"
                    : "hover:border-gray-400 focus:border-blue-500"
                )}
              />
            </div>
          </div>
          
          {/* Date Range */}
          <div className="space-y-2">
            <label className="text-sm xs:text-base font-medium text-gray-700">Date Range</label>
            <div className="flex flex-col space-y-3">
              <div className="grid grid-cols-2 gap-2 xs:gap-3">
                <Button
                  variant={
                    filters.dateRange.from &&
                    filters.dateRange.to &&
                    format(filters.dateRange.from, "yyyy-MM-dd") ===
                      format(startOfToday(), "yyyy-MM-dd") &&
                    format(filters.dateRange.to, "yyyy-MM-dd") ===
                      format(startOfToday(), "yyyy-MM-dd")
                      ? "primary"
                      : "secondary"
                  }
                  size="lg"
                  onClick={() => setQuickDateRange("today")}
                  className="min-h-[52px] xs:min-h-[56px] text-sm xs:text-base font-medium rounded-xl transition-all duration-200"
                >
                  Today
                </Button>
                <Button
                  variant={
                    filters.dateRange.from &&
                    filters.dateRange.to &&
                    format(filters.dateRange.from, "yyyy-MM-dd") ===
                      format(startOfToday(), "yyyy-MM-dd") &&
                    format(filters.dateRange.to, "yyyy-MM-dd") ===
                      format(addDays(startOfToday(), 7), "yyyy-MM-dd")
                      ? "primary"
                      : "secondary"
                  }
                  size="lg"
                  onClick={() => setQuickDateRange("week")}
                  className="min-h-[52px] xs:min-h-[56px] text-sm xs:text-base font-medium rounded-xl transition-all duration-200"
                >
                  Next 7 days
                </Button>
              </div>

              <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="secondary"
                    size="lg"
                    className={cn(
                      "justify-start text-left font-normal min-h-[52px] xs:min-h-[56px] text-sm xs:text-base border-2 transition-all duration-200 rounded-xl",
                      (filters.dateRange.from || filters.dateRange.to) &&
                        "text-foreground border-blue-300 bg-blue-50",
                      "hover:border-blue-400 hover:shadow-sm"
                    )}
                  >
                    <Calendar className="mr-2 h-4 w-4 xs:h-5 xs:w-5 flex-shrink-0" />
                    {filters.dateRange.from ? (
                      filters.dateRange.to ? (
                        <>
                          {format(filters.dateRange.from, "LLL dd")} -{" "}
                          {format(filters.dateRange.to, "LLL dd, y")}
                        </>
                      ) : (
                        format(filters.dateRange.from, "LLL dd, y")
                      )
                    ) : (
                      <span className="text-muted-foreground">Custom range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    mode="range"
                    defaultMonth={filters.dateRange.from || undefined}
                    selected={{
                      from: filters.dateRange.from || undefined,
                      to: filters.dateRange.to || undefined,
                    }}
                    onSelect={(range: any) => {
                      updateFilter("dateRange", {
                        from: range?.from || null,
                        to: range?.to || null,
                      });
                      if (range?.from && range?.to) {
                        setIsCalendarOpen(false);
                      }
                    }}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          
          {/* Status Filter */}
          <div className="space-y-2">
            <label className="text-sm xs:text-base font-medium text-gray-700">Booking Status</label>
            <Select
              value={filters.status}
              onValueChange={(value) => updateFilter("status", value)}
            >
              <SelectTrigger
                className={cn(
                  "w-full min-h-[52px] xs:min-h-[56px] text-sm xs:text-base border-2 transition-all duration-200 font-medium rounded-xl",
                  filters.status !== "all"
                    ? "border-blue-300 bg-blue-50 text-blue-800"
                    : "hover:border-gray-400"
                )}
              >
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                {getBookingStatusOptions().map(({ value, label }) => (
                  <SelectItem key={value} value={value}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {/* Payment Status Filter */}
          <div className="space-y-2">
            <label className="text-sm xs:text-base font-medium text-gray-700">Payment Status</label>
            <Select
              value={filters.paymentStatus}
              onValueChange={(value) => updateFilter("paymentStatus", value)}
            >
              <SelectTrigger
                className={cn(
                  "w-full min-h-[52px] xs:min-h-[56px] text-sm xs:text-base border-2 transition-all duration-200 font-medium rounded-xl",
                  filters.paymentStatus !== "all"
                    ? "border-green-300 bg-green-50 text-green-800"
                    : "hover:border-gray-400"
                )}
              >
                <SelectValue placeholder="All Payment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Payment</SelectItem>
                <SelectItem value="Paid">Paid</SelectItem>
                <SelectItem value="Pending">Pending</SelectItem>
                <SelectItem value="Partial">Partial</SelectItem>
                <SelectItem value="Refunded">Refunded</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Vehicle Filter */}
          {availableVehicles.length > 0 && (
            <div className="space-y-2">
              <label className="text-sm xs:text-base font-medium text-gray-700">Vehicle</label>
              <Select
                value={filters.vehicle}
                onValueChange={(value) => updateFilter("vehicle", value)}
              >
                <SelectTrigger
                  className={cn(
                    "w-full min-h-[52px] xs:min-h-[56px] text-sm xs:text-base border-2 transition-all duration-200 font-medium rounded-xl",
                    filters.vehicle !== "all"
                      ? "border-purple-300 bg-purple-50 text-purple-800"
                      : "hover:border-gray-400"
                  )}
                >
                  <SelectValue placeholder="All Vehicles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Vehicles</SelectItem>
                  {availableVehicles.map((vehicle) => (
                    <SelectItem key={vehicle} value={vehicle}>
                      {vehicle}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          
          <Button 
            className="w-full min-h-[52px] xs:min-h-[56px] text-sm xs:text-base font-semibold mt-4 xs:mt-6 rounded-xl bg-blue-600 hover:bg-blue-700 transition-all duration-200" 
            onClick={() => setOpen(false)}
          >
            Apply Filters
          </Button>
        </div>
      </DrawerContent>
    </Drawer>
  )
}
