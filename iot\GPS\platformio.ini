[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino

; Serial monitor
monitor_speed = 115200
; monitor_port = COM4           ; Let PlatformIO auto-detect
; upload_port  = COM4           ; Let PlatformIO auto-detect

; Libraries for modem + GNSS + HTTP + JSON + MQTT
lib_deps =
  ; USE THIS FORK (supports A7670/A7608/SIM7670G)
  https://github.com/lewisxhe/TinyGSM-fork.git
  bblanchon/ArduinoJson @ ^6.21.0
  arduino-libraries/ArduinoHttpClient @ ^0.4.0
  ; MQTT Client Library (required for TinyGSM MQTT)
  knolleary/PubSubClient @ ^2.8

; Use A7670 profile (NOT SIM7600) + LilyGO board define + debug & RX buffer
build_flags =
  -D TINY_GSM_RX_BUFFER=1024
