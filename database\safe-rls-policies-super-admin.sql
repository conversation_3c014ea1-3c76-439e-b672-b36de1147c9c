-- Safe RLS policy updates for super_admin role access
-- Uses DROP POLICY IF EXISTS + CREATE POLICY to safely update existing policies
-- Safe approach - won't fail if policies don't exist

BEGIN;

-- ========================================
-- CARS TABLE - Allow super_admin to manage cars
-- ========================================

DROP POLICY IF EXISTS "Only admins can insert cars" ON public.cars;
CREATE POLICY "Only admins can insert cars" 
  ON public.cars FOR INSERT 
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Only admins can update cars" ON public.cars;
CREATE POLICY "Only admins can update cars" 
  ON public.cars FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Only admins can delete cars" ON public.cars;
CREATE POLICY "Only admins can delete cars" 
  ON public.cars FOR DELETE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- BOOKINGS TABLE - Allow super_admin to manage bookings
-- ========================================

DROP POLICY IF EXISTS "Admins can view all bookings" ON public.bookings;
CREATE POLICY "Admins can view all bookings" 
  ON public.bookings FOR SELECT 
  TO authenticated
  USING (
    -- Customers can view their own bookings
    customer_id = auth.uid()
    OR
    -- Admins and super_admins can view all bookings
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Only admins can update bookings" ON public.bookings;
CREATE POLICY "Only admins can update bookings" 
  ON public.bookings FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Only admins can delete bookings" ON public.bookings;
CREATE POLICY "Only admins can delete bookings" 
  ON public.bookings FOR DELETE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- PAYMENTS TABLE - Allow super_admin to manage payments
-- ========================================

DROP POLICY IF EXISTS "Admins can view all payments" ON public.payments;
CREATE POLICY "Admins can view all payments" 
  ON public.payments FOR SELECT 
  TO authenticated
  USING (
    -- Customers can view payments for their own bookings
    EXISTS (
      SELECT 1 FROM public.bookings 
      WHERE bookings.id = payments.booking_id 
      AND bookings.customer_id = auth.uid()
    )
    OR
    -- Admins and super_admins can view all payments
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

DROP POLICY IF EXISTS "Admins can verify payments" ON public.payments;
CREATE POLICY "Admins can verify payments" 
  ON public.payments FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- VEHICLE CATEGORIES - Allow super_admin full access
-- ========================================

DROP POLICY IF EXISTS "Allow admin full access to vehicle categories" ON public.vehicle_categories;
CREATE POLICY "Allow admin full access to vehicle categories" 
  ON public.vehicle_categories FOR ALL 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- ========================================
-- CONDITIONAL UPDATES FOR RENTER MANAGEMENT TABLES
-- (Only if tables exist)
-- ========================================

-- Update renter_status policies if table exists
DO $$ 
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'renter_status') THEN
    EXECUTE 'CREATE OR REPLACE POLICY "Only admins can view renter status" 
      ON public.renter_status FOR SELECT 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = ''admin'' OR role = ''super_admin'')
        )
      )';
      
    EXECUTE 'CREATE OR REPLACE POLICY "Only admins can manage renter status" 
      ON public.renter_status FOR ALL 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = ''admin'' OR role = ''super_admin'')
        )
      )';
  END IF;
END $$;

-- Update issue_categories policies if table exists
DO $$ 
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'issue_categories') THEN
    EXECUTE 'CREATE OR REPLACE POLICY "Issue categories are viewable by admins" 
      ON public.issue_categories FOR SELECT 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = ''admin'' OR role = ''super_admin'')
        )
      )';
      
    EXECUTE 'CREATE OR REPLACE POLICY "Only admins can manage issue categories" 
      ON public.issue_categories FOR ALL 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = ''admin'' OR role = ''super_admin'')
        )
      )';
  END IF;
END $$;

-- Update renter_issues policies if table exists  
DO $$ 
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'renter_issues') THEN
    EXECUTE 'CREATE OR REPLACE POLICY "Only admins can view renter issues" 
      ON public.renter_issues FOR SELECT 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = ''admin'' OR role = ''super_admin'')
        )
      )';
      
    EXECUTE 'CREATE OR REPLACE POLICY "Only admins can manage renter issues" 
      ON public.renter_issues FOR ALL 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = ''admin'' OR role = ''super_admin'')
        )
      )';
  END IF;
END $$;

-- Update user_legal_documents policies if table exists
DO $$ 
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_legal_documents') THEN
    EXECUTE 'CREATE OR REPLACE POLICY "Users can update their own legal documents or admins can update any" 
      ON public.user_legal_documents FOR UPDATE 
      TO authenticated
      USING (
        user_id = (SELECT auth.uid()) 
        OR EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = ''admin'' OR role = ''super_admin'')
        )
      )';
      
    EXECUTE 'CREATE OR REPLACE POLICY "Users can delete their own legal documents or admins can delete any" 
      ON public.user_legal_documents FOR DELETE 
      TO authenticated
      USING (
        user_id = (SELECT auth.uid()) 
        OR EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = ''admin'' OR role = ''super_admin'')
        )
      )';
  END IF;
END $$;

-- Update booking_documents policies if table exists
DO $$ 
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'booking_documents') THEN
    EXECUTE 'CREATE OR REPLACE POLICY "Admins can view all booking documents" 
      ON public.booking_documents FOR SELECT 
      TO authenticated
      USING (
        -- Customers can view documents for their own bookings
        EXISTS (
          SELECT 1 FROM public.bookings 
          WHERE bookings.id = booking_documents.booking_id 
          AND bookings.customer_id = auth.uid()
        )
        OR
        -- Admins and super_admins can view all documents
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = ''admin'' OR role = ''super_admin'')
        )
      )';
      
    EXECUTE 'CREATE OR REPLACE POLICY "Admins can update booking documents" 
      ON public.booking_documents FOR UPDATE 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = ''admin'' OR role = ''super_admin'')
        )
      )';
  END IF;
END $$;

COMMIT;

-- ========================================
-- VERIFICATION - Show updated policies
-- ========================================

SELECT 
  tablename, 
  policyname,
  CASE 
    WHEN cmd LIKE '%super_admin%' THEN '✅ Updated for super_admin'
    ELSE '⚠️ May need update'
  END as status
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('cars', 'bookings', 'payments', 'vehicle_categories')
  AND (policyname ILIKE '%admin%' OR policyname ILIKE '%manage%')
ORDER BY tablename, policyname;
