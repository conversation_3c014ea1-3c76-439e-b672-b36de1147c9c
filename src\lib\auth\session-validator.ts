/**
 * Standardized Session Validation Utility
 * 
 * This module provides consistent session validation logic across
 * both admin and customer authentication contexts.
 */

import type { Session } from '@supabase/supabase-js'

export interface SessionValidationResult {
  isValid: boolean
  reason?: string
}

export class SessionValidator {
  /**
   * Validates a session with standardized rules
   */
  static validateSession(session: Session | null, contextType: 'admin' | 'customer'): SessionValidationResult {
    if (!session || !session.user) {
      return { isValid: true, reason: 'No session to validate' }
    }

    // Basic session structure validation
    if (!session.access_token || !session.user.id || !session.user.email) {
      return { isValid: false, reason: 'Incomplete session data' }
    }
    
    // Check session expiry
    if (session.expires_at && Date.now() / 1000 > session.expires_at) {
      return { isValid: false, reason: 'Session expired' }
    }

    // Context-specific validation
    const superAdminEmail = process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAIL || '<EMAIL>'
    
    if (contextType === 'admin') {
      // For admin context, allow super admin email or defer to profile validation
      if (session.user.email === superAdminEmail) {
        return { isValid: true, reason: 'Super admin email' }
      } else {
        // Allow session but require proper role validation in auth context
        return { isValid: true, reason: 'Pending role validation' }
      }
    } else if (contextType === 'customer') {
      // For customer context, reject super admin email
      if (session.user.email === superAdminEmail) {
        return { isValid: false, reason: 'Super admin email not allowed in customer context' }
      } else {
        // Allow session but require proper role validation in auth context
        return { isValid: true, reason: 'Valid customer session' }
      }
    }

    return { isValid: true, reason: 'Session structure valid' }
  }

  /**
   * Validates user role for the given context
   */
  static validateRole(userProfile: any, contextType: 'admin' | 'customer'): SessionValidationResult {
    if (!userProfile) {
      return { isValid: false, reason: 'No profile data available' }
    }

    if (contextType === 'admin') {
      if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
        return { isValid: true, reason: 'Valid admin role' }
      } else {
        return { isValid: false, reason: 'Insufficient admin privileges' }
      }
    } else if (contextType === 'customer') {
      if (userProfile.role === 'customer') {
        return { isValid: true, reason: 'Valid customer role' }
      } else if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
        return { isValid: false, reason: 'Admin user not allowed in customer context' }
      } else {
        return { isValid: false, reason: 'Invalid user role' }
      }
    }

    return { isValid: false, reason: 'Unknown context type' }
  }

  /**
   * Check if email should be treated as super admin
   */
  static isSuperAdminEmail(email: string): boolean {
    const superAdminEmail = process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAIL || '<EMAIL>'
    return email === superAdminEmail
  }
}
