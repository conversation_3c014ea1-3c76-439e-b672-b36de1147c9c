-- Migration: Add 'proof_of_payment' to booking_documents.document_type allowed values
-- Safe to run multiple times due to IF EXISTS on DROP

BEGIN;

ALTER TABLE public.booking_documents
  DROP CONSTRAINT IF EXISTS booking_documents_document_type_check;

ALTER TABLE public.booking_documents
  ADD CONSTRAINT booking_documents_document_type_check
  CHECK (
    document_type IN (
      'drivers_license',
      'government_id',
      'proof_of_age',
      'security_deposit_confirmation',
      'proof_of_payment'
    )
  );

COMMIT;
