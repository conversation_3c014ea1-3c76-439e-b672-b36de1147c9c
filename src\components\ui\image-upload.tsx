"use client"

import * as React from "react"
import { Upload, X, Image as ImageIcon } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { uploadFile, deleteFile } from "@/lib/file-upload"
import { cn } from "@/lib/utils"

interface ImageUploadProps {
  label: string
  value: string
  onChange: (url: string) => void
  required?: boolean
  className?: string
}

export function ImageUpload({
  label,
  value,
  onChange,
  required = false,
  className
}: ImageUploadProps) {
  const [uploading, setUploading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [dragActive, setDragActive] = React.useState(false)
  const fileInputRef = React.useRef<HTMLInputElement>(null)

  const handleFileSelect = async (file: File) => {
    setUploading(true)
    setError(null)

    try {
      const result = await uploadFile(file)
      
      if (result.error) {
        setError(result.error)
      } else if (result.url) {
        onChange(result.url)
      }
    } catch (err) {
      setError('Failed to upload file')
    } finally {
      setUploading(false)
    }
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
    
    const file = e.dataTransfer.files[0]
    if (file && file.type.startsWith('image/')) {
      handleFileSelect(file)
    } else {
      setError('Please drop a valid image file')
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
  }

  const handleRemove = async () => {
    if (value) {
      // Optionally delete from storage
      await deleteFile(value)
      onChange('')
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className={cn("space-y-2", className)}>
      <label className="text-sm font-medium text-foreground">
        {label}{required ? " *" : ""}
      </label>
      
      {value ? (
        // Show uploaded image
        <div className="relative">
          <div className="relative w-full h-48 bg-gray-50 rounded-lg border-2 border-gray-200 overflow-hidden">
            <img
              src={value}
              alt="Car image"
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = "/placeholder.svg";
              }}
            />
            <div className="absolute inset-0 bg-black/40 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={openFileDialog}
                  className="bg-white/90 text-gray-900 hover:bg-white"
                >
                  <Upload className="h-4 w-4 mr-1" />
                  Replace
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  onClick={handleRemove}
                  className="bg-red-600/90 text-white hover:bg-red-600"
                >
                  <X className="h-4 w-4 mr-1" />
                  Remove
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        // Show upload area
        <div
          className={cn(
            "relative w-full h-48 border-2 border-dashed rounded-lg transition-colors cursor-pointer",
            dragActive ? "border-blue-500 bg-blue-50" : "border-gray-300 bg-gray-50",
            uploading && "opacity-50 pointer-events-none"
          )}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={openFileDialog}
        >
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            {uploading ? (
              <>
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
                <p className="text-sm">Uploading...</p>
              </>
            ) : (
              <>
                <ImageIcon className="h-12 w-12 mb-3 text-gray-400" />
                <p className="text-sm font-medium text-gray-600 mb-1">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-500">
                  PNG, JPG, WebP or GIF (max 5MB)
                </p>
              </>
            )}
          </div>
        </div>
      )}

      {error && (
        <p className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2">
          {error}
        </p>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  )
}
