// Supabase Database Types for Ollie Track

export type CarType = "SUV" | "Sport" | "Coupe" | "Hatchback" | "MPV" | "Sedan";
export type CarStatus = "Available" | "Rented" | "In Maintenance";
export type CarCondition = "Good" | "Needs Repair";
export type TransmissionType = "Manual" | "Automatic";
export type UserRole = "customer" | "admin";
export type BookingStatus = "Pending" | "Active" | "Completed" | "Cancelled";
export type PaymentStatus =
  | "Pending"
  | "Paid"
  | "Failed"
  | "Refunded"
  | "Pending Verification"
  | "Rejected";
export type PaymentMethod = "Card" | "Wallet" | "Cash" | "GCash" | "PayMaya";
export type GPSStatus = "active" | "idle" | "offline";

// Database table interfaces matching Supabase schema
export interface Profile {
  id: string; // UUID from auth.users
  email: string;
  full_name?: string;
  first_name?: string;
  middle_initial?: string;
  last_name?: string;
  phone?: string;
  role: UserRole;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Car {
  id: string; // UUID
  model: string;
  type: CarType;
  plate_number: string;
  status: CarStatus;
  condition: CarCondition;
  fuel_capacity: number;
  fuel_type: string;
  transmission: TransmissionType;
  seats: number;
  price_per_day: number;
  image_url?: string;
  notes?: string;
  is_archived: boolean;
  created_at: string;
  updated_at: string;
  // GPS tracking fields
  current_latitude?: number;
  current_longitude?: number;
  last_gps_update?: string;
}

export interface Booking {
  id: string; // UUID
  customer_id: string; // UUID
  car_id: string; // UUID
  pickup_location: string;
  dropoff_location: string;
  pickup_datetime: string; // ISO timestamp
  dropoff_datetime: string; // ISO timestamp
  special_requests?: string;
  status: BookingStatus;
  total_amount: number;
  created_at: string;
  updated_at: string;
}

export interface Payment {
  id: string; // UUID
  booking_id: string; // UUID
  amount: number;
  status: PaymentStatus;
  method: PaymentMethod;
  transaction_id?: string;
  transaction_date: string;
  proof_of_payment_url?: string; // URL to uploaded proof of payment file
  verification_notes?: string; // Admin notes for verification
  verified_by?: string; // Admin user ID who verified the payment
  verified_at?: string; // Timestamp when payment was verified
  created_at: string;
  updated_at: string;
}

export interface GPSLocation {
  id: string; // UUID
  car_id: string; // UUID
  latitude: number;
  longitude: number;
  speed: number; // km/h
  heading: number; // degrees
  status: GPSStatus;
  driver_id?: string; // UUID
  timestamp: string;
  created_at: string;
}

export interface CarRoute {
  id: string; // UUID
  car_id: string; // UUID
  booking_id?: string; // UUID
  route_data: RoutePoint[]; // JSONB array
  start_time: string;
  end_time?: string;
  total_distance?: number; // km
  created_at: string;
}

export interface RoutePoint {
  lat: number;
  lng: number;
  timestamp: string;
}

// Legacy interfaces for backward compatibility with existing frontend code
// These map the new database fields to the old interface structure
export interface User extends Profile {
  name: string; // maps to full_name
}

// Helper types for API responses
export interface BookingWithDetails extends Booking {
  car?: Car;
  customer?: Profile;
  payment?: Payment;
}

export interface CarWithLocation extends Car {
  gps_location?: GPSLocation;
}

// API request/response types
export interface CreateBookingRequest {
  carId: string;
  pickUpLocation: string;
  dropOffLocation: string;
  pickUpDateTime: string;
  dropOffDateTime: string;
  specialRequests?: string;
  paymentMethod: PaymentMethod;
}

export interface UpdateCarRequest {
  model?: string;
  type?: CarType;
  plate_number?: string;
  status?: CarStatus;
  condition?: CarCondition;
  fuel_capacity?: number;
  fuel_type?: string;
  transmission?: TransmissionType;
  seats?: number;
  price_per_day?: number;
  image_url?: string;
  notes?: string;
  is_archived?: boolean;
}

export interface CreatePaymentRequest {
  booking_id: string;
  amount: number;
  method: PaymentMethod;
  transaction_id?: string;
}

// Search and filter types
export interface CarSearchFilters {
  type?: CarType;
  condition?: CarCondition;
  status?: CarStatus;
  priceRange?: {
    min: number;
    max: number;
  };
  seatsRange?: {
    min: number;
    max: number;
  };
  transmission?: TransmissionType;
  availability?: {
    from: string;
    to: string;
  };
}

export interface BookingSearchFilters {
  status?: BookingStatus;
  customer_id?: string;
  car_id?: string;
  dateRange?: {
    from: string;
    to: string;
  };
}

// Admin Settings Types
export type CurrencyCode = "PHP" | "USD" | "EUR" | "JPY";
export type NotificationChannel = "email" | "sms" | "messenger";
export type SettingsSection =
  | "organization"
  | "branches"
  | "pricing"
  | "policies"
  | "data"
  | "theme"
  | "audit";

export interface OrganizationSettings {
  id?: string;
  company_name: string;
  legal_name: string;
  contact_email: string;
  contact_phone: string;
  business_hours: BusinessHours;
  logo_url?: string;
  favicon_url?: string;
  default_timezone: string;
  default_currency: CurrencyCode;
  created_at?: string;
  updated_at?: string;
}

export interface BusinessHours {
  monday: DayHours;
  tuesday: DayHours;
  wednesday: DayHours;
  thursday: DayHours;
  friday: DayHours;
  saturday: DayHours;
  sunday: DayHours;
}

export interface DayHours {
  is_open: boolean;
  open_time?: string; // "09:00"
  close_time?: string; // "18:00"
}

export interface Branch {
  id: string;
  name: string;
  address: string;
  city: string;
  province: string;
  latitude?: number;
  longitude?: number;
  contact_numbers: string[];
  business_hours: BusinessHours;
  pickup_available: boolean;
  dropoff_available: boolean;
  blackout_dates: string[]; // ISO date strings
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface PricingSettings {
  id?: string;
  base_rates: { [category: string]: number };
  weekend_multiplier: number;
  holiday_multiplier: number;
  seasonal_ranges: SeasonalRange[];
  tax_rate: number; // percentage
  security_deposit: number;
  late_return_fee_per_hour: number;
  extension_hourly_rate: number;
  extension_price_per_day: number;
  pickup_surcharge_per_km: number;
  created_at?: string;
  updated_at?: string;
}

export interface SeasonalRange {
  name: string;
  start_date: string; // "MM-DD"
  end_date: string; // "MM-DD"
  multiplier: number;
}

export interface BookingPolicySettings {
  id?: string;
  min_rental_duration_hours: number;
  max_rental_duration_days: number;
  advance_booking_cutoff_hours: number;
  grace_period_minutes: number;
  cancellation_policy: CancellationPolicy;
  required_documents: string[];
  created_at?: string;
  updated_at?: string;
}

export interface CancellationPolicy {
  free_cancellation_hours: number;
  partial_refund_hours: number;
  no_refund_hours: number;
  cancellation_fee: number;
}

export interface PaymentSettings {
  id?: string;
  enabled_gateways: PaymentGateway[];
  accepted_methods: PaymentMethod[];
  default_currency: CurrencyCode;
  invoice_prefix: string;
  invoice_sequence: number;
  tax_registration_number?: string;
  invoice_footer_notes: string;
  refund_policy_notes: string;
  created_at?: string;
  updated_at?: string;
}

export interface PaymentGateway {
  id: string;
  name: string;
  is_enabled: boolean;
  api_key?: string;
  secret_key?: string;
  webhook_url?: string;
  settings: { [key: string]: any };
}

export interface VehicleSettings {
  id?: string;
  categories: VehicleCategory[];
  default_transmission_options: TransmissionType[];
  default_seat_capacities: number[];
  addon_services: AddonService[];
  created_at?: string;
  updated_at?: string;
}

export interface VehicleCategory {
  id: string;
  name: string;
  description: string;
  default_price_per_day: number;
  icon?: string;
}

export interface AddonService {
  id: string;
  name: string;
  description: string;
  price: number;
  is_available: boolean;
}

export interface MaintenanceSettings {
  id?: string;
  maintenance_intervals: MaintenanceInterval[];
  alert_rules: MaintenanceAlert[];
  created_at?: string;
  updated_at?: string;
}

export interface MaintenanceInterval {
  type: "km" | "time";
  interval_value: number; // km or days
  description: string;
}

export interface MaintenanceAlert {
  type: "low_fuel" | "overdue_maintenance" | "gps_offline" | "custom";
  threshold_value?: number;
  message: string;
  is_enabled: boolean;
}

export interface AdminUserRole {
  id: string;
  name: string;
  permissions: Permission[];
}

export interface Permission {
  resource: string; // "cars" | "bookings" | "payments" | "settings" etc.
  actions: string[]; // ["read", "create", "update", "delete"]
}

export interface NotificationSettings {
  id?: string;
  email_enabled: boolean;
  sms_enabled: boolean;
  messenger_enabled: boolean;
  event_templates: NotificationTemplate[];
  created_at?: string;
  updated_at?: string;
}

export interface NotificationTemplate {
  event: string; // "new_booking" | "pickup_reminder" | "overdue_return" etc.
  channels: NotificationChannel[];
  subject?: string;
  template: string;
  variables: string[]; // ["{{renter}}", "{{car}}", "{{pickup_time}}"]
  is_enabled: boolean;
}

export interface IntegrationSettings {
  id?: string;
  google_calendar_enabled: boolean;
  google_calendar_api_key?: string;
  webhooks: Webhook[];
  created_at?: string;
  updated_at?: string;
}

export interface Webhook {
  id: string;
  name: string;
  url: string;
  events: string[];
  secret?: string;
  is_enabled: boolean;
}

export interface SecuritySettings {
  id?: string;
  session_timeout_minutes: number;
  password_policy: PasswordPolicy;
  ip_allowlist?: string[];
  two_factor_enabled: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface PasswordPolicy {
  min_length: number;
  require_uppercase: boolean;
  require_lowercase: boolean;
  require_numbers: boolean;
  require_symbols: boolean;
}

export interface ThemeSettings {
  id?: string;
  primary_color: string;
  secondary_color: string;
  neutral_color: string;
  dark_mode_enabled: boolean;
  custom_css?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AuditLogEntry {
  id: string;
  user_id: string;
  user_email: string;
  action: string;
  section: SettingsSection;
  field_name?: string;
  old_value?: any;
  new_value?: any;
  timestamp: string;
  ip_address?: string;
}

export interface SettingsDirtyState {
  [section: string]: boolean;
}

// Settings form validation types
export interface SettingsValidationError {
  field: string;
  message: string;
}

export interface SettingsFormState {
  isDirty: boolean;
  isValid: boolean;
  errors: SettingsValidationError[];
}

// ============================================
// Renter Issue Tracking Types
// ============================================

export type IssueSeverity = "Low" | "Medium" | "High" | "Critical";

export interface RenterStatus {
  id: string;
  customer_id: string;
  status_tagline: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface IssueCategory {
  id: string;
  name: string;
  description?: string;
  color: string;
  created_at: string;
  is_active: boolean;
}

export interface RenterIssue {
  id: string;
  customer_id: string;
  category_id: string;
  booking_id?: string;
  description: string;
  severity: IssueSeverity;
  resolved: boolean;
  resolved_at?: string;
  resolved_by?: string;
  resolution_notes?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  // Joined data
  category?: IssueCategory;
  booking?: Booking;
  created_by_admin?: Profile;
  resolved_by_admin?: Profile;
}

export interface RenterCategoryTag {
  id: string;
  customer_id: string;
  category_id: string;
  tagged_by: string;
  tagged_at: string;
  // Joined data
  category?: IssueCategory;
  tagged_by_admin?: Profile;
}

export interface RenterBehaviorSummary {
  total_bookings: number;
  completed_bookings: number;
  cancelled_bookings: number;
  overdue_bookings: number;
  total_issues: number;
  unresolved_issues: number;
  issue_categories: Array<{
    category: string;
    count: number;
    color: string;
  }>;
}

export interface RenterProfile extends Profile {
  // Extended profile with renter-specific data
  status?: RenterStatus;
  category_tags?: RenterCategoryTag[];
  behavior_summary?: RenterBehaviorSummary;
  recent_issues?: RenterIssue[];
}
