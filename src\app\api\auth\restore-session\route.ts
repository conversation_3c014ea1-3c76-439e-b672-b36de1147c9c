"use server";

import { createContextClient } from "@/lib/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { context } = await request.json();
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`[RestoreSession] Attempting to restore session for context: ${context}`);
    }
    
    // Validate context
    const authContext = context as 'admin' | 'customer' | undefined;
    if (authContext && !['admin', 'customer'].includes(authContext)) {
      return NextResponse.json({ error: "Invalid context provided" }, { status: 400 });
    }

    // Use context-specific client to check for server-side session
    const supabase = authContext 
      ? await createContextClient(authContext)
      : await createContextClient('admin'); // Default to admin for this endpoint
    
    // Try to get the current session from server-side cookies
    const { data: { session }, error } = await supabase.auth.getSession();

    if (process.env.NODE_ENV === 'development') {
      console.log(`[RestoreSession] Server session check: hasSession=${!!session}, error=${error?.message || 'none'}`);
    }

    if (error) {
      console.error(`[RestoreSession] Error getting server session:`, error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (session) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[RestoreSession] Found server session for user: ${session.user?.email}`);
        console.log(`[RestoreSession] User metadata:`, session.user?.user_metadata);
        console.log(`[RestoreSession] Session expires at:`, session.expires_at);
      }

      return NextResponse.json({
        session: {
          access_token: session.access_token,
          refresh_token: session.refresh_token,
          user: session.user,
          expires_at: session.expires_at
        }
      });
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[RestoreSession] No server session found`);
      }
      
      return NextResponse.json({ session: null });
    }
  } catch (error) {
    console.error("[RestoreSession] Session restoration error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
