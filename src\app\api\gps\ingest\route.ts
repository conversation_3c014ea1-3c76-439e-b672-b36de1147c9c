import { NextRequest, NextResponse } from 'next/server'
import { createClient as createServerClient } from '@/lib/supabase/server'
import { createClient as createSbDirect } from '@supabase/supabase-js'
import { saveDevPoint } from '@/app/api/gps/dev-store'

// Simple header-based auth for device ingestion
// Set INBOUND_DEVICE_TOKEN in your environment and send it in X-Device-Key
function isAuthorized(req: NextRequest): boolean {
  const configuredToken = process.env.INBOUND_DEVICE_TOKEN
  if (!configuredToken) return true // allow in dev if not configured
  const headerToken = req.headers.get('x-device-key') || req.headers.get('X-Device-Key')
  return headerToken === configuredToken
}

// GPS Data Encryption/Decryption (moved from tracker endpoint)
const ENCRYPT_KEY = "OllieGPS2024";

function decryptGPSToken(encryptedToken: string): { lat: number; lon: number; acc: number; deviceId: string } | null {
  try {
    if (!/^[0-9a-f]+$/i.test(encryptedToken)) return null;
    const hexPairs = encryptedToken.match(/.{1,2}/g) || [];
    let decrypted = "";
    
    for (let i = 0; i < hexPairs.length; i++) {
      const hexChar = parseInt(hexPairs[i], 16);
      const keyChar = ENCRYPT_KEY.charCodeAt(i % ENCRYPT_KEY.length);
      decrypted += String.fromCharCode(hexChar ^ keyChar);
    }
    
    const parts = decrypted.split(',');
    if (parts.length >= 4) {
      const lat = parseFloat(parts[0]);
      const lon = parseFloat(parts[1]);
      const acc = parseFloat(parts[2]);
      const deviceId = parts[3];
      
      if (!isNaN(lat) && !isNaN(lon)) {
        return { lat, lon, acc, deviceId };
      }
    }
    return null;
  } catch (error) {
    return null;
  }
}

export async function GET(req: NextRequest) {
  // Handle legacy tracker GET requests with encrypted tokens
  try {
    const { searchParams } = new URL(req.url);
    const gpsToken = searchParams.get('gps');
    
    if (gpsToken) {
      const gpsData = decryptGPSToken(gpsToken);
      if (gpsData) {
        // Convert to POST format and process
        const postData = {
          carId: gpsData.deviceId,
          latitude: gpsData.lat,
          longitude: gpsData.lon,
          speed: 0,
          heading: 0,
          status: 'active'
        };
        
        // Process the same way as POST (reuse logic)
        const mockReq = { 
          headers: req.headers,
          json: async () => postData 
        } as any;
        return POST(mockReq);
      }
    }
    
    return NextResponse.json({ error: 'Invalid GPS token' }, { status: 400 });
  } catch (error) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    if (!isAuthorized(req)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await req.json()
    const {
      carId,
      latitude,
      longitude,
      speed,
      heading,
      status,
      driver_id
    } = body || {}

    if (!carId || typeof latitude !== 'number' || typeof longitude !== 'number') {
      return NextResponse.json({ error: 'Missing or invalid fields' }, { status: 400 })
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('📥 GPS ingest received:', {
        carId, latitude, longitude, speed, heading, status
      });
    }

    // Prefer service-role key if available to avoid RLS issues on inserts
    const serviceUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    const supabase = serviceUrl && serviceKey
      ? createSbDirect(serviceUrl, serviceKey)
      : await createServerClient()

    // Resolve device ID to car UUID if needed
    let actualCarId = carId;
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(carId);
    
    if (!isUUID) {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Non-UUID carId detected, looking up device mapping:', carId);
      }
      
      // Look up the device mapping
      const { data: mappingData, error: mappingError } = await supabase
        .rpc('get_car_id_from_device', { device_id_param: carId })
        
      if (mappingError) {
        console.error('❌ Device mapping lookup failed:', mappingError);
      } else if (mappingData) {
        actualCarId = mappingData;
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ Device mapped:', carId, '->', actualCarId);
        }
      } else {
        if (process.env.NODE_ENV === 'development') {
          console.log('⚠️ No mapping found for device:', carId);
        }
      }
    }

    let data: any = null
    let insertSucceeded = false;
    
    // Try to insert with resolved car ID
    if (isUUID || actualCarId !== carId) {
      try {
        const { data: ins, error } = await supabase
          .from('gps_locations')
          .insert({
            car_id: actualCarId,
            latitude,
            longitude,
            speed,
            heading,
            status,
            driver_id
          })
          .select()
          .single()
        if (error) throw error
        data = ins
        insertSucceeded = true;
        
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ GPS data stored in database successfully');
        }
      } catch (e: any) {
        if (process.env.NODE_ENV === 'development') {
          console.log('🚨 Database insert failed:', e.message);
        }
      }
    }
    
    // Fallback to dev store if database insert failed
    if (!insertSucceeded) {
      if (process.env.NODE_ENV === 'development') {
        console.log('💾 Using dev store for device:', carId);
      }
      
      // Check if this is an ESP32 device (lilygo-esp32-01 or similar)
      const isESP32 = carId.includes('esp32') || carId.includes('lilygo');
      const deviceName = isESP32 ? 'ESP32 Tracker' : carId;
      
      // For development: if carId is not a UUID or insert fails, store in volatile dev store
      saveDevPoint(carId, {
        car_id: actualCarId !== carId ? actualCarId : null,
        latitude,
        longitude,
        speed,
        heading,
        status,
        car: { 
          model: deviceName, 
          plate_number: isESP32 ? 'ESP32-GPS' : 'DEV' 
        },
      })
      data = { 
        id: `dev_${carId}_${Date.now()}`, 
        car_id: actualCarId !== carId ? actualCarId : null, 
        latitude, 
        longitude, 
        speed, 
        heading, 
        status 
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log('💾 Saved point in dev-store for', carId, 'at', new Date().toISOString());
        console.log('✅ Stored in dev store:', data.id);
      }
    }

    // Update current car position snapshot as convenience
    if (actualCarId && isUUID) {
      try {
        await supabase
        .from('cars')
        .update({
          current_latitude: latitude,
          current_longitude: longitude,
          last_gps_update: new Date().toISOString()
        })
        .eq('id', actualCarId)
        
        if (process.env.NODE_ENV === 'development') {
          console.log('📍 Updated car position for:', actualCarId);
        }
      } catch (e) {
        console.warn('Failed to update car position:', e);
      }
    }

    return NextResponse.json({ ok: true, data })
  } catch (error) {
    console.error('GPS ingest error:', error)
    return NextResponse.json({ error: 'Internal Server Error', details: String(error) }, { status: 500 })
  }
}


