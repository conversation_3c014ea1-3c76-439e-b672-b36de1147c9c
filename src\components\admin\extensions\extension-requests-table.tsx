"use client";

import React from "react";
import { format } from "date-fns";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Clock, 
  Calendar, 
  User, 
  Car, 
  MapPin, 
  CheckCircle, 
  XCircle,
  Eye,
  AlertTriangle,
  Timer
} from "lucide-react";
import { cn } from "@/lib/utils";
import { ExtensionRequestWithDetails } from "@/lib/services/admin-extension-service";

interface ExtensionRequestsTableProps {
  extensionRequests: ExtensionRequestWithDetails[];
  loading?: boolean;
  onViewDetails: (request: ExtensionRequestWithDetails) => void;
  onApprove: (requestId: string) => void;
  onReject: (requestId: string) => void;
}

export function ExtensionRequestsTable({
  extensionRequests,
  loading = false,
  onViewDetails,
  onApprove,
  onReject
}: ExtensionRequestsTableProps) {
  const formatDateTime = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM dd, yyyy • h:mm a");
    } catch {
      return dateString;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', { 
      style: 'currency', 
      currency: 'PHP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { 
        variant: "secondary" as const, 
        icon: <Timer className="h-3 w-3" />,
        className: "bg-yellow-100 text-yellow-800 border-yellow-200"
      },
      approved: { 
        variant: "default" as const, 
        icon: <CheckCircle className="h-3 w-3" />,
        className: "bg-green-100 text-green-800 border-green-200"
      },
      rejected: { 
        variant: "destructive" as const, 
        icon: <XCircle className="h-3 w-3" />,
        className: "bg-red-100 text-red-800 border-red-200"
      },
      expired: { 
        variant: "secondary" as const, 
        icon: <AlertTriangle className="h-3 w-3" />,
        className: "bg-gray-100 text-gray-800 border-gray-200"
      }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <Badge variant={config.variant} className={cn("flex items-center gap-1 capitalize", config.className)}>
        {config.icon}
        {status}
      </Badge>
    );
  };

  const getPriorityColor = (request: ExtensionRequestWithDetails) => {
    const now = new Date();
    const expiresAt = new Date(request.expires_at);
    const hoursUntilExpiry = (expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (request.status === 'pending' && hoursUntilExpiry <= 2) {
      return "border-l-red-500"; // Urgent - expires soon
    } else if (request.status === 'pending' && hoursUntilExpiry <= 6) {
      return "border-l-yellow-500"; // Medium priority
    } else if (request.status === 'pending') {
      return "border-l-blue-500"; // Normal priority
    }
    return "border-l-gray-300";
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 animate-spin" />
            Loading Extension Requests...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-100 rounded-lg animate-pulse" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (extensionRequests.length === 0) {
    return (
      <Card>
        <CardContent className="py-12 text-center">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Extension Requests</h3>
          <p className="text-gray-500">No extension requests found for the selected criteria.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Mobile Card View - Simplified */}
      <div className="block lg:hidden space-y-3">
        {extensionRequests.map((request) => (
          <Card key={request.id} className={cn("border-l-4", getPriorityColor(request))}>
            <CardContent className="p-3 xs:p-4">
              {/* Header with customer name and status */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <User className="h-4 w-4 text-gray-500 flex-shrink-0" />
                  <span className="font-medium text-sm text-gray-900 truncate">
                    {request.customers?.full_name || "Unknown"}
                  </span>
                </div>
                {getStatusBadge(request.status)}
              </div>
              
              {/* Booking reference */}
              <div className="text-xs text-gray-500 font-mono mb-3">
                #{request.bookings?.booking_ref || "N/A"}
              </div>
              
              {/* Extension info - simplified */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 mb-3">
                <div className="flex items-center justify-between">
                  <div className="text-sm">
                    <div className="font-medium text-gray-900">+{request.extension_duration_hours} hours</div>
                    <div className="text-xs text-gray-600">{request.bookings?.cars?.model || "Unknown Car"}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-green-600 text-sm">{formatCurrency(request.additional_amount)}</div>
                    <div className="text-xs text-gray-500">Additional cost</div>
                  </div>
                </div>
              </div>
              
              {/* Action button - mobile only shows View Details */}
              <Button
                variant="secondary"
                size="sm"
                onClick={() => onViewDetails(request)}
                className="w-full min-h-[48px] text-sm font-medium"
              >
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Desktop Table View */}
      <div className="hidden lg:block border border-gray-200 rounded-lg overflow-hidden shadow-sm bg-white">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader className="bg-gray-50 sticky top-0 z-10">
              <TableRow className="border-b border-gray-200">
                <TableHead className="min-w-[160px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Customer & Booking</TableHead>
                <TableHead className="min-w-[140px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Vehicle</TableHead>
                <TableHead className="min-w-[140px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Original Return</TableHead>
                <TableHead className="min-w-[140px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Requested Return</TableHead>
                <TableHead className="w-[100px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Extension</TableHead>
                <TableHead className="w-[100px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Cost</TableHead>
                <TableHead className="w-[100px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Status</TableHead>
                <TableHead className="w-[100px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Created</TableHead>
                <TableHead className="w-[120px] font-semibold text-gray-900 text-xs text-left">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {extensionRequests.map((request, index) => (
                <TableRow 
                  key={request.id} 
                  className={cn(
                    "cursor-pointer hover:bg-gray-50 transition-colors border-b border-gray-200 border-l-4",
                    getPriorityColor(request),
                    index % 2 === 0 ? "bg-white" : "bg-gray-50/30"
                  )}
                  onClick={() => onViewDetails(request)}
                >
                  <TableCell className="py-3 border-r border-gray-200">
                    <div>
                      <p className="font-medium text-gray-900 text-sm">{request.customers?.full_name || "Unknown"}</p>
                      <p className="text-xs text-gray-500 font-mono">#{request.bookings?.booking_ref || "N/A"}</p>
                    </div>
                  </TableCell>
                  <TableCell className="py-3 border-r border-gray-200">
                    <div>
                      <p className="font-medium text-gray-900 text-sm">{request.bookings?.cars?.model || "Unknown"}</p>
                      <p className="text-xs text-gray-500">{request.bookings?.cars?.plate_number || "N/A"}</p>
                    </div>
                  </TableCell>
                  <TableCell className="py-3 border-r border-gray-200">
                    <div>
                      <p className="font-medium text-gray-900 text-sm">{format(new Date(request.original_dropoff_datetime), "MMM d • h:mm a")}</p>
                      <p className="text-xs text-gray-500">{format(new Date(request.original_dropoff_datetime), "EEE")}</p>
                    </div>
                  </TableCell>
                  <TableCell className="py-3 border-r border-gray-200">
                    <div>
                      <p className="font-medium text-green-700 text-sm">{format(new Date(request.requested_dropoff_datetime), "MMM d • h:mm a")}</p>
                      <p className="text-xs text-gray-500">{format(new Date(request.requested_dropoff_datetime), "EEE")}</p>
                    </div>
                  </TableCell>
                  <TableCell className="py-3 border-r border-gray-200">
                    <div className="text-sm">
                      <p className="font-medium text-gray-900">+{request.extension_duration_hours}h</p>
                    </div>
                  </TableCell>
                  <TableCell className="py-3 border-r border-gray-200">
                    <p className="font-semibold text-base text-green-600">{formatCurrency(request.additional_amount)}</p>
                  </TableCell>
                  <TableCell className="py-3 border-r border-gray-200">
                    {getStatusBadge(request.status)}
                  </TableCell>
                  <TableCell className="py-3 border-r border-gray-200">
                    <p className="text-sm text-gray-500">{format(new Date(request.created_at), "MMM dd, yy")}</p>
                  </TableCell>
                  <TableCell className="py-3">
                    <div className="flex items-center gap-1 justify-end">
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onViewDetails(request);
                        }}
                        className="h-8 w-8 p-0 border border-gray-300 bg-white hover:border-gray-400 hover:bg-gray-50 transition-colors shadow-none hover:shadow-sm"
                      >
                        <Eye className="h-4 w-4 text-gray-600" />
                      </Button>
                      {request.status === 'pending' && (
                        <>
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              onApprove(request.id);
                            }}
                            className="h-8 w-8 p-0 border border-green-300 bg-green-50 text-green-700 hover:bg-green-100 hover:border-green-400 transition-colors shadow-none hover:shadow-sm"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              onReject(request.id);
                            }}
                            className="h-8 w-8 p-0 border border-red-300 bg-red-50 text-red-700 hover:bg-red-100 hover:border-red-400 transition-colors shadow-none hover:shadow-sm"
                          >
                            <XCircle className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
