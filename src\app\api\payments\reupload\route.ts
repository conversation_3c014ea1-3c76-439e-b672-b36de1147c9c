import { NextRequest, NextResponse } from "next/server";
import { createContextClient } from "@/lib/supabase/server";

export async function POST(request: NextRequest) {
  try {
    const { paymentId, proofOfPaymentUrl } = await request.json();

    if (!paymentId || !proofOfPaymentUrl) {
      return NextResponse.json(
        { error: "Payment ID and proof of payment URL are required" },
        { status: 400 }
      );
    }

    const supabase = await createContextClient('customer');

    // Get the current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Verify the payment belongs to the current user
    const { data: payment, error: paymentError } = await supabase
      .from("payments")
      .select(
        `
        id,
        booking_id,
        bookings!inner(customer_id)
      `
      )
      .eq("id", paymentId)
      .single();

    if (paymentError || !payment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    // Check if the booking belongs to the current user
    const booking = payment.bookings as any;
    if (booking.customer_id !== user.id) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Update the payment with new proof and reset status to "Pending Verification"
    const { data: updatedPayment, error: updateError } = await supabase
      .from("payments")
      .update({
        proof_of_payment_url: proofOfPaymentUrl,
        status: "Pending Verification",
        verification_notes: null, // Clear previous notes
        verified_by: null,
        verified_at: null,
        updated_at: new Date().toISOString(),
      })
      .eq("id", paymentId)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating payment:", updateError);
      return NextResponse.json(
        { error: "Failed to update payment" },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        message: "Payment proof re-uploaded successfully",
        payment: updatedPayment,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error in payment reupload API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
