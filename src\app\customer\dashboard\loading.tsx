import { PublicAppShell } from "@/components/layout/public-app-shell"
import { CustomerDashboardLoading as DashboardLoadingIndicator } from "@/components/customer-side/loading/customer-loading"
import { CustomerTableSkeleton, CustomerCardSkeleton } from "@/components/customer-side/loading/customer-loading"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function CustomerDashboardLoading() {
  return (
    <PublicAppShell>
      <div className="space-y-8 px-4 md:px-6 py-8" data-testid="customer-loading-page">
        {/* Page header skeleton */}
        <div className="space-y-4">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 animate-pulse"></div>
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-96 animate-pulse"></div>
        </div>

        {/* Quick stats skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {Array.from({ length: 3 }, (_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse"></div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse mb-2"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-20 animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Loading indicator */}
        <div className="flex flex-col items-center justify-center py-8">
          <DashboardLoadingIndicator />
        </div>

        {/* Recent bookings skeleton */}
        <Card>
          <CardHeader>
            <CardTitle>
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CustomerTableSkeleton rows={3} />
          </CardContent>
        </Card>

        {/* Favorite vehicles skeleton */}
        <Card>
          <CardHeader>
            <CardTitle>
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-40 animate-pulse"></div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              {Array.from({ length: 3 }, (_, i) => (
                <CustomerCardSkeleton key={i} />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </PublicAppShell>
  )
}
