"use client"

import * as React from "react"

interface ValidationRule {
  required?: boolean | string
  minLength?: number | { value: number; message: string }
  maxLength?: number | { value: number; message: string }
  pattern?: RegExp | { value: RegExp; message: string }
  validate?: (value: any) => string | boolean
  email?: boolean | string
  phone?: boolean | string
  url?: boolean | string
  number?: boolean | string
}

interface FieldState {
  value: any
  error: string
  touched: boolean
  dirty: boolean
}

interface FormState {
  [key: string]: FieldState
}

interface UseFormValidationOptions {
  initialValues?: Record<string, any>
  validationRules?: Record<string, ValidationRule>
  validateOnChange?: boolean
  validateOnBlur?: boolean
}

export function useFormValidation({
  initialValues = {},
  validationRules = {},
  validateOnChange = true,
  validateOnBlur = true
}: UseFormValidationOptions = {}) {
  
  const [formState, setFormState] = React.useState<FormState>(() => {
    const state: FormState = {}
    Object.keys(initialValues).forEach(key => {
      state[key] = {
        value: initialValues[key] || '',
        error: '',
        touched: false,
        dirty: false
      }
    })
    return state
  })

  // Validation functions
  const validateField = React.useCallback((fieldName: string, value: any): string => {
    const rules = validationRules[fieldName]
    if (!rules) return ''

    // Required validation
    if (rules.required) {
      const isEmpty = value === '' || value === null || value === undefined || 
                     (Array.isArray(value) && value.length === 0)
      if (isEmpty) {
        return typeof rules.required === 'string' ? rules.required : 'This field is required'
      }
    }

    // Skip other validations if field is empty and not required
    if (!value && !rules.required) return ''

    // Email validation
    if (rules.email && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        return typeof rules.email === 'string' ? rules.email : 'Please enter a valid email address'
      }
    }

    // Phone validation (Philippine format)
    if (rules.phone && value) {
      const phoneRegex = /^(\+63|0)?[0-9]{10}$/
      if (!phoneRegex.test(value.replace(/\s+/g, ''))) {
        return typeof rules.phone === 'string' ? rules.phone : 'Please enter a valid phone number'
      }
    }

    // URL validation
    if (rules.url && value) {
      try {
        new URL(value)
      } catch {
        return typeof rules.url === 'string' ? rules.url : 'Please enter a valid URL'
      }
    }

    // Number validation
    if (rules.number && value) {
      if (isNaN(Number(value))) {
        return typeof rules.number === 'string' ? rules.number : 'Please enter a valid number'
      }
    }

    // MinLength validation
    if (rules.minLength && value) {
      const minLength = typeof rules.minLength === 'number' ? rules.minLength : rules.minLength.value
      if (value.length < minLength) {
        return typeof rules.minLength === 'object' 
          ? rules.minLength.message 
          : `Must be at least ${minLength} characters`
      }
    }

    // MaxLength validation
    if (rules.maxLength && value) {
      const maxLength = typeof rules.maxLength === 'number' ? rules.maxLength : rules.maxLength.value
      if (value.length > maxLength) {
        return typeof rules.maxLength === 'object' 
          ? rules.maxLength.message 
          : `Must be no more than ${maxLength} characters`
      }
    }

    // Pattern validation
    if (rules.pattern && value) {
      const pattern = rules.pattern instanceof RegExp ? rules.pattern : rules.pattern.value
      if (!pattern.test(value)) {
        return typeof rules.pattern === 'object' 
          ? rules.pattern.message 
          : 'Invalid format'
      }
    }

    // Custom validation
    if (rules.validate && value) {
      const result = rules.validate(value)
      if (typeof result === 'string') return result
      if (result === false) return 'Validation failed'
    }

    return ''
  }, [validationRules])

  // Set field value
  const setValue = React.useCallback((fieldName: string, value: any) => {
    setFormState(prev => {
      const newState = {
        ...prev,
        [fieldName]: {
          ...prev[fieldName],
          value,
          dirty: true,
          error: validateOnChange ? validateField(fieldName, value) : prev[fieldName]?.error || ''
        }
      }
      return newState
    })
  }, [validateField, validateOnChange])

  // Set field error
  const setError = React.useCallback((fieldName: string, error: string) => {
    setFormState(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        error
      }
    }))
  }, [])

  // Set field touched
  const setTouched = React.useCallback((fieldName: string, touched: boolean = true) => {
    setFormState(prev => {
      const newState = {
        ...prev,
        [fieldName]: {
          ...prev[fieldName],
          touched,
          error: validateOnBlur && touched ? validateField(fieldName, prev[fieldName]?.value) : prev[fieldName]?.error || ''
        }
      }
      return newState
    })
  }, [validateField, validateOnBlur])

  // Validate all fields
  const validateAll = React.useCallback((): boolean => {
    let hasErrors = false
    const newState = { ...formState }

    Object.keys(validationRules).forEach(fieldName => {
      const currentValue = formState[fieldName]?.value || ''
      const error = validateField(fieldName, currentValue)
      
      newState[fieldName] = {
        ...newState[fieldName],
        error,
        touched: true
      }

      if (error) hasErrors = true
    })

    setFormState(newState)
    return !hasErrors
  }, [formState, validationRules, validateField])

  // Reset form
  const reset = React.useCallback(() => {
    const state: FormState = {}
    Object.keys(initialValues).forEach(key => {
      state[key] = {
        value: initialValues[key] || '',
        error: '',
        touched: false,
        dirty: false
      }
    })
    setFormState(state)
  }, [initialValues])

  // Get field props for input components
  const getFieldProps = React.useCallback((fieldName: string) => {
    const field = formState[fieldName] || { value: '', error: '', touched: false, dirty: false }
    
    return {
      value: field.value,
      error: field.touched ? field.error : '',
      success: field.touched && !field.error && field.dirty ? 'Valid' : '',
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setValue(fieldName, e.target.value)
      },
      onBlur: () => {
        setTouched(fieldName, true)
      }
    }
  }, [formState, setValue, setTouched])

  // Get all values
  const values = React.useMemo(() => {
    const vals: Record<string, any> = {}
    Object.keys(formState).forEach(key => {
      vals[key] = formState[key].value
    })
    return vals
  }, [formState])

  // Get all errors
  const errors = React.useMemo(() => {
    const errs: Record<string, string> = {}
    Object.keys(formState).forEach(key => {
      if (formState[key].touched && formState[key].error) {
        errs[key] = formState[key].error
      }
    })
    return errs
  }, [formState])

  // Check if form is valid
  const isValid = React.useMemo(() => {
    return Object.keys(errors).length === 0 && 
           Object.keys(formState).some(key => formState[key].dirty)
  }, [errors, formState])

  // Check if form is dirty
  const isDirty = React.useMemo(() => {
    return Object.keys(formState).some(key => formState[key].dirty)
  }, [formState])

  return {
    values,
    errors,
    isValid,
    isDirty,
    formState,
    setValue,
    setError,
    setTouched,
    validateAll,
    reset,
    getFieldProps
  }
}
