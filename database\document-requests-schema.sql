-- ============================================
-- DOCUMENT REQUESTS TABLE
-- ============================================
-- Stores admin requests for customer document uploads/revisions

CREATE TABLE public.document_requests (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  booking_id UUID NOT NULL REFERENCES public.bookings(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  document_type TEXT NOT NULL CHECK (document_type IN (
    'drivers_license',
    'government_id', 
    'proof_of_billing'
  )),
  request_type TEXT NOT NULL DEFAULT 'missing' CHECK (request_type IN (
    'missing',     -- Document not uploaded yet
    'revision'     -- Document needs to be re-uploaded/revised
  )),
  admin_notes TEXT, -- Admin explanation for the request
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN (
    'pending',     -- Request sent, awaiting customer action
    'fulfilled',   -- Customer uploaded/revised document
    'expired'      -- Request expired without action
  )),
  requested_by UUID NOT NULL REFERENCES public.profiles(id), -- Admin who made the request
  requested_at TIMESTAMPTZ DEFAULT NOW(),
  fulfilled_at TIMESTAMPTZ, -- When customer completed the request
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'), -- Request expires after 7 days
  email_sent BOOLEAN DEFAULT FALSE, -- Track if email notification was sent
  email_sent_at TIMESTAMPTZ, -- When email notification was sent
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure only one active request per document type per booking
  UNIQUE(booking_id, document_type, status) DEFERRABLE INITIALLY DEFERRED
);

-- Enable RLS for document_requests
ALTER TABLE public.document_requests ENABLE ROW LEVEL SECURITY;

-- RLS Policies for document_requests
CREATE POLICY "Customers can view their own document requests" 
  ON public.document_requests FOR SELECT 
  TO authenticated
  USING (customer_id = (SELECT auth.uid()));

CREATE POLICY "Admins can view all document requests" 
  ON public.document_requests FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

CREATE POLICY "Admins can create document requests" 
  ON public.document_requests FOR INSERT 
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

CREATE POLICY "Admins can update document requests" 
  ON public.document_requests FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) 
      AND (role = 'admin' OR role = 'super_admin')
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) 
      AND (role = 'admin' OR role = 'super_admin')
    )
  );

-- Create indexes for better performance
CREATE INDEX idx_document_requests_customer_id ON public.document_requests(customer_id);
CREATE INDEX idx_document_requests_booking_id ON public.document_requests(booking_id);
CREATE INDEX idx_document_requests_status ON public.document_requests(status);
CREATE INDEX idx_document_requests_document_type ON public.document_requests(document_type);
CREATE INDEX idx_document_requests_expires_at ON public.document_requests(expires_at);
CREATE INDEX idx_document_requests_created_at ON public.document_requests(created_at);

-- Create updated_at trigger
CREATE TRIGGER handle_updated_at_document_requests 
  BEFORE UPDATE ON public.document_requests 
  FOR EACH ROW 
  EXECUTE FUNCTION handle_updated_at();

-- Function to check pending document requests for a customer
CREATE OR REPLACE FUNCTION get_customer_pending_document_requests(customer_uuid UUID)
RETURNS TABLE (
    id UUID,
    booking_id UUID,
    document_type TEXT,
    request_type TEXT,
    admin_notes TEXT,
    requested_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    booking_details JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        dr.id,
        dr.booking_id,
        dr.document_type,
        dr.request_type,
        dr.admin_notes,
        dr.requested_at,
        dr.expires_at,
        jsonb_build_object(
            'id', b.id,
            'status', b.status,
            'pickup_datetime', b.pickup_datetime,
            'dropoff_datetime', b.dropoff_datetime,
            'car_model', c.model,
            'car_plate', c.plate_number
        ) as booking_details
    FROM public.document_requests dr
    JOIN public.bookings b ON dr.booking_id = b.id
    LEFT JOIN public.cars c ON b.car_id = c.id
    WHERE dr.customer_id = customer_uuid 
    AND dr.status = 'pending'
    AND dr.expires_at > NOW()
    ORDER BY dr.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check missing documents for a booking
CREATE OR REPLACE FUNCTION check_missing_documents_for_booking(booking_uuid UUID)
RETURNS TABLE (
    document_type TEXT,
    is_missing BOOLEAN,
    current_status TEXT
) AS $$
DECLARE
    required_docs TEXT[] := ARRAY['drivers_license', 'government_id', 'proof_of_billing'];
    doc_type TEXT;
BEGIN
    FOREACH doc_type IN ARRAY required_docs
    LOOP
        RETURN QUERY
        SELECT 
            doc_type as document_type,
            (bd.id IS NULL OR bd.verification_status IN ('rejected', 'requires_resubmission')) as is_missing,
            COALESCE(bd.verification_status, 'not_uploaded') as current_status
        FROM (SELECT doc_type) dt
        LEFT JOIN public.booking_documents bd 
            ON bd.booking_id = booking_uuid 
            AND bd.document_type = doc_type;
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to automatically fulfill document request when document is uploaded/approved
CREATE OR REPLACE FUNCTION fulfill_document_request_on_upload()
RETURNS TRIGGER AS $$
BEGIN
    -- When a document is uploaded or approved, fulfill any pending requests for that document type
    IF (NEW.verification_status = 'approved' OR 
        (TG_OP = 'INSERT' AND NEW.verification_status = 'pending')) THEN
        
        UPDATE public.document_requests 
        SET 
            status = 'fulfilled',
            fulfilled_at = NOW(),
            updated_at = NOW()
        WHERE booking_id = NEW.booking_id 
        AND document_type = NEW.document_type 
        AND status = 'pending';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to auto-fulfill requests when documents are uploaded
CREATE TRIGGER trigger_fulfill_document_request_on_upload
    AFTER INSERT OR UPDATE ON public.booking_documents
    FOR EACH ROW
    EXECUTE FUNCTION fulfill_document_request_on_upload();

-- Function to expire old document requests
CREATE OR REPLACE FUNCTION expire_old_document_requests()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER;
BEGIN
    UPDATE public.document_requests 
    SET 
        status = 'expired',
        updated_at = NOW()
    WHERE status = 'pending' 
    AND expires_at <= NOW();
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create notification preferences table for customers
CREATE TABLE public.customer_notification_preferences (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  customer_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  email_notifications BOOLEAN DEFAULT TRUE,
  document_request_emails BOOLEAN DEFAULT TRUE,
  booking_update_emails BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(customer_id)
);

-- Enable RLS for notification preferences
ALTER TABLE public.customer_notification_preferences ENABLE ROW LEVEL SECURITY;

-- RLS Policy for notification preferences
CREATE POLICY "Users can manage their own notification preferences" 
  ON public.customer_notification_preferences 
  FOR ALL 
  TO authenticated
  USING (customer_id = (SELECT auth.uid()))
  WITH CHECK (customer_id = (SELECT auth.uid()));

-- Create updated_at trigger for notification preferences
CREATE TRIGGER handle_updated_at_customer_notification_preferences 
  BEFORE UPDATE ON public.customer_notification_preferences 
  FOR EACH ROW 
  EXECUTE FUNCTION handle_updated_at();
