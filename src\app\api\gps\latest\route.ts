import { NextResponse } from 'next/server'
import { createClient as createServerClient } from '@/lib/supabase/server'
import { createClient as createSbDirect } from '@supabase/supabase-js'

// GPS Data Encryption (same key as ESP32)
const ENCRYPT_KEY = "OllieGPS2024";

function encryptGPSData(data: string): string {
  let encrypted = "";
  const hexDigits = "0123456789abcdef";
  
  for (let i = 0; i < data.length; i++) {
    const charCode = data.charCodeAt(i);
    const keyCharCode = ENCRYPT_KEY.charCodeAt(i % ENCRYPT_KEY.length);
    const encryptedByte = charCode ^ keyCharCode;
    encrypted += hexDigits[(encryptedByte >> 4) & 0x0F];
    encrypted += hexDigits[encryptedByte & 0x0F];
  }
  
  return encrypted;
}

function createGPSToken(lat: number, lon: number, acc: number, deviceId: string = 'lilygo-esp32-01'): string {
  const payload = `${lat.toFixed(6)},${lon.toFixed(6)},${acc.toFixed(1)},${deviceId}`;
  return encryptGPSData(payload);
}

export async function GET() {
  try {
    const serviceUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    const supabase = serviceUrl && serviceKey
      ? createSbDirect(serviceUrl, serviceKey)
      : await createServerClient()

    // Get the latest GPS location
    const { data, error } = await supabase
      .from('gps_locations')
      .select(`
        *,
        car:cars(model, plate_number)
      `)
      .order('timestamp', { ascending: false })
      .limit(1)

    if (error) throw error

    if (!data || data.length === 0) {
      return NextResponse.json({ 
        success: false, 
        error: 'No GPS data available' 
      }, { status: 404 });
    }

    const latestLocation = data[0];
    
    // Generate encrypted token for the latest location
    const token = createGPSToken(
      latestLocation.latitude,
      latestLocation.longitude,
      latestLocation.accuracy || 10.0,
      latestLocation.car_id || 'lilygo-esp32-01'
    );

    if (process.env.NODE_ENV === 'development') {
      console.log('🔑 Generated latest GPS token:', {
        carId: latestLocation.car_id,
        latitude: latestLocation.latitude,
        longitude: latestLocation.longitude,
        accuracy: latestLocation.accuracy,
        tokenPreview: token.substring(0, 20) + '...',
        timestamp: latestLocation.timestamp
      });
    }

    return NextResponse.json({
      success: true,
      token,
      location: {
        carId: latestLocation.car_id,
        latitude: latestLocation.latitude,
        longitude: latestLocation.longitude,
        accuracy: latestLocation.accuracy,
        timestamp: latestLocation.timestamp,
        car: latestLocation.car
      },
      urls: {
        dev: `http://192.168.100.239:3000/admin/tracker/?gps=${token}`,
        prod: `https://olliesrentalcar.pathlinkio.app/admin/tracker/?gps=${token}`
      }
    });
    
  } catch (error) {
    console.error('❌ Error fetching latest GPS token:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: String(error)
    }, { status: 500 });
  }
}
