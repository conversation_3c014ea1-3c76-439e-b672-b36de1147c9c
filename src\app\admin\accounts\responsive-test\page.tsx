"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Shield, Users, Eye } from 'lucide-react'

export default function AccountsResponsiveTestPage() {
  const [viewportWidth, setViewportWidth] = useState<number>(0)
  const [viewportHeight, setViewportHeight] = useState<number>(0)

  useEffect(() => {
    const updateViewport = () => {
      setViewportWidth(window.innerWidth)
      setViewportHeight(window.innerHeight)
    }

    updateViewport()
    window.addEventListener('resize', updateViewport)
    return () => window.removeEventListener('resize', updateViewport)
  }, [])

  const getBreakpointInfo = (width: number) => {
    if (width < 375) return { name: 'Mobile S', color: 'bg-red-100 text-red-800' }
    if (width < 425) return { name: 'Mobile M', color: 'bg-orange-100 text-orange-800' }
    if (width < 768) return { name: 'Mobile L', color: 'bg-yellow-100 text-yellow-800' }
    if (width < 1024) return { name: 'Tablet', color: 'bg-blue-100 text-blue-800' }
    if (width < 1280) return { name: 'Laptop', color: 'bg-green-100 text-green-800' }
    if (width < 1440) return { name: 'Desktop', color: 'bg-purple-100 text-purple-800' }
    return { name: 'Large Desktop', color: 'bg-indigo-100 text-indigo-800' }
  }

  const breakpointInfo = getBreakpointInfo(viewportWidth)

  // Mock data for testing
  const mockAdmins = [
    {
      id: '1',
      email: '<EMAIL>',
      full_name: 'Test Admin',
      phone: '09123456789',
      role: 'admin' as const,
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      email: '<EMAIL>',
      full_name: 'Super Admin',
      phone: '09987654321',
      role: 'super_admin' as const,
      created_at: '2024-01-01T00:00:00Z'
    }
  ]

  const mockCustomers = [
    {
      id: '3',
      email: '<EMAIL>',
      full_name: 'John Doe',
      phone: '09111111111',
      role: 'customer' as const,
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: '4',
      email: '<EMAIL>',
      full_name: 'Jane Smith',
      phone: '***********',
      role: 'customer' as const,
      created_at: '2024-01-01T00:00:00Z'
    }
  ]

  return (
    <div className="space-y-3 xs:space-y-4 sm:space-y-6 p-1 xs:p-2 sm:p-4 lg:p-6 max-w-full overflow-hidden">
      {/* Viewport Information */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg xs:text-xl">Tab Highlighting Test - Account Management</CardTitle>
          <CardDescription>Testing tab color highlighting across all breakpoints</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2 xs:gap-3 items-center">
            <Badge className={breakpointInfo.color}>
              {breakpointInfo.name}
            </Badge>
            <span className="text-xs xs:text-sm text-muted-foreground">
              {viewportWidth}×{viewportHeight}px
            </span>
          </div>
          <div className="mt-4 space-y-2 text-xs xs:text-sm">
            <p><strong>Test Instructions:</strong></p>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground">
              <li>Click between Admin and Customer tabs to see color highlighting</li>
              <li>Admin tab should highlight in <strong className="text-blue-600">blue</strong> when active</li>
              <li>Customer tab should highlight in <strong className="text-green-600">green</strong> when active</li>
              <li>Verify hover effects work on both tabs</li>
              <li>Test across different screen sizes by resizing browser</li>
              <li>Check dark mode compatibility if available</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Header */}
      <div className="flex flex-col xs:flex-row items-start xs:items-center justify-between gap-4 xs:gap-2">
        <div>
          <h1 className="text-lg xs:text-xl sm:text-2xl lg:text-3xl font-bold tracking-tight text-foreground">Account Management Test</h1>
          <p className="text-sm xs:text-sm sm:text-base lg:text-lg text-muted-foreground mt-1 xs:mt-2">Testing tab highlighting functionality</p>
        </div>
        <Button className="text-sm xs:text-sm sm:text-base lg:text-lg py-2 xs:py-2 sm:py-2.5 h-9 xs:h-9 sm:h-10 lg:h-11 px-4 xs:px-4 sm:px-5">
          <Eye className="h-4 w-4 xs:h-4 xs:w-4 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-2 xs:mr-2 sm:mr-2" />
          Test Mode
        </Button>
      </div>

      {/* Tabs with Enhanced Highlighting */}
      <Tabs defaultValue="admins" className="space-y-3 xs:space-y-4">
        <TabsList className="w-full sm:w-auto px-1 py-1 xs:px-1 xs:py-1 sm:px-1.5 sm:py-1.5">
          <TabsTrigger 
            value="admins" 
            className="text-sm xs:text-sm sm:text-base lg:text-lg py-2 xs:py-2 sm:py-2.5 lg:py-3 px-3 xs:px-3 sm:px-4 lg:px-5 data-[state=active]:bg-blue-700 data-[state=active]:text-white data-[state=active]:border-blue-600 data-[state=active]:shadow-lg dark:data-[state=active]:bg-blue-600 dark:data-[state=active]:text-white dark:data-[state=active]:border-blue-500 font-semibold transition-all duration-200 hover:bg-blue-100 dark:hover:bg-blue-900/20"
          >
            <Shield className="h-4 w-4 xs:h-4 xs:w-4 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-2 xs:mr-2 sm:mr-2" />
            Admin ({mockAdmins.length})
          </TabsTrigger>
          <TabsTrigger 
            value="customers" 
            className="text-sm xs:text-sm sm:text-base lg:text-lg py-2 xs:py-2 sm:py-2.5 lg:py-3 px-3 xs:px-3 sm:px-4 lg:px-5 data-[state=active]:bg-green-700 data-[state=active]:text-white data-[state=active]:border-green-600 data-[state=active]:shadow-lg dark:data-[state=active]:bg-green-600 dark:data-[state=active]:text-white dark:data-[state=active]:border-green-500 font-semibold transition-all duration-200 hover:bg-green-100 dark:hover:bg-green-900/20"
          >
            <Users className="h-4 w-4 xs:h-4 xs:w-4 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-2 xs:mr-2 sm:mr-2" />
            Customer ({mockCustomers.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="admins" className="space-y-2 xs:space-y-3 sm:space-y-4">
          <div className="grid gap-2 xs:gap-3 sm:gap-4">
            {mockAdmins.map((account) => (
              <Card key={account.id}>
                <CardHeader className="pb-2 xs:pb-2 sm:pb-3 lg:pb-3 p-3 xs:p-3 sm:p-4 lg:p-4">
                  <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between gap-2 xs:gap-3">
                    <div className="flex items-center space-x-2 xs:space-x-3 sm:space-x-3">
                      <div className="w-8 h-8 xs:w-9 xs:h-9 sm:w-10 sm:h-10 lg:w-12 lg:h-12 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold text-xs xs:text-sm sm:text-base lg:text-lg">
                        {account.email.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <CardTitle className="text-sm xs:text-sm sm:text-base lg:text-lg">{account.full_name || account.email}</CardTitle>
                        <CardDescription className="text-xs xs:text-xs sm:text-sm lg:text-base truncate max-w-[150px] xs:max-w-none mt-1">{account.email}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 xs:space-x-2 self-end xs:self-auto">
                      <Badge variant={account.role === 'super_admin' ? 'default' : 'secondary'} className="text-xs xs:text-xs sm:text-sm lg:text-base whitespace-nowrap px-2 xs:px-2 sm:px-3 py-1 xs:py-1">
                        {account.role === 'super_admin' ? 'Super Admin' : 'Admin'}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-3 xs:p-3 sm:p-4 lg:p-4 pt-0">
                  <div className="text-xs xs:text-xs sm:text-sm lg:text-base text-muted-foreground space-y-1 xs:space-y-1">
                    <p>Phone: {account.phone || 'N/A'}</p>
                    <p>Created: {new Date(account.created_at).toLocaleDateString()}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="customers" className="space-y-2 xs:space-y-3 sm:space-y-4">
          <div className="grid gap-2 xs:gap-3 sm:gap-4">
            {mockCustomers.map((account) => (
              <Card key={account.id}>
                <CardHeader className="pb-2 xs:pb-2 sm:pb-3 lg:pb-3 p-3 xs:p-3 sm:p-4 lg:p-4">
                  <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between gap-2 xs:gap-3">
                    <div className="flex items-center space-x-2 xs:space-x-3 sm:space-x-3">
                      <div className="w-8 h-8 xs:w-9 xs:h-9 sm:w-10 sm:h-10 lg:w-12 lg:h-12 rounded-full bg-green-600 flex items-center justify-center text-white font-semibold text-xs xs:text-sm sm:text-base lg:text-lg">
                        {account.email.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <CardTitle className="text-sm xs:text-sm sm:text-base lg:text-lg">{account.full_name || account.email}</CardTitle>
                        <CardDescription className="text-xs xs:text-xs sm:text-sm lg:text-base truncate max-w-[150px] xs:max-w-none mt-1">{account.email}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 xs:space-x-2 self-end xs:self-auto">
                      <Badge variant="secondary" className="text-xs xs:text-xs sm:text-sm lg:text-base whitespace-nowrap px-2 xs:px-2 sm:px-3 py-1 xs:py-1">Customer</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-3 xs:p-3 sm:p-4 lg:p-4 pt-0">
                  <div className="text-xs xs:text-xs sm:text-sm lg:text-base text-muted-foreground space-y-1 xs:space-y-1">
                    <p>Phone: {account.phone || 'N/A'}</p>
                    <p>Created: {new Date(account.created_at).toLocaleDateString()}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Testing Notes */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-sm xs:text-base">Testing Checklist</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-xs xs:text-sm">
            <div className="grid grid-cols-1 xs:grid-cols-2 gap-2 xs:gap-4">
              <div>
                <h4 className="font-medium text-blue-600">Admin Tab (Blue Theme):</h4>
                <ul className="list-disc list-inside text-muted-foreground space-y-1 mt-1">
                  <li>Active: Blue background, dark blue text</li>
                  <li>Hover: Light blue background</li>
                  <li>Transitions: Smooth color changes</li>
                  <li>Dark mode: Blue/20 opacity background</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-green-600">Customer Tab (Green Theme):</h4>
                <ul className="list-disc list-inside text-muted-foreground space-y-1 mt-1">
                  <li>Active: Green background, dark green text</li>
                  <li>Hover: Light green background</li>
                  <li>Transitions: Smooth color changes</li>
                  <li>Dark mode: Green/20 opacity background</li>
                </ul>
              </div>
            </div>
            <div className="mt-4 p-2 xs:p-3 bg-muted rounded-md">
              <p className="font-medium">Accessibility Check:</p>
              <ul className="list-disc list-inside text-muted-foreground space-y-1 mt-1">
                <li>Color contrast meets WCAG standards</li>
                <li>Tab focus indicators visible</li>
                <li>Keyboard navigation functional</li>
                <li>Screen reader compatibility maintained</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
