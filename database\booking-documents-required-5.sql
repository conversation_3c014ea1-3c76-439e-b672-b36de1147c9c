-- Migration: Update booking document requirements to 5 (add proof_of_payment)
-- This updates helper functions to include 'proof_of_payment' as a required document
-- and sets total_required to 5.

BEGIN;

-- Update check function to include 5 required docs
CREATE OR REPLACE FUNCTION check_booking_documents_complete(booking_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    required_docs TEXT[] := ARRAY[
        'drivers_license',
        'government_id',
        'proof_of_age',
        'security_deposit_confirmation',
        'proof_of_payment'
    ];
    uploaded_count INTEGER;
BEGIN
    SELECT COUNT(DISTINCT document_type)
    INTO uploaded_count
    FROM public.booking_documents 
    WHERE booking_id = booking_uuid 
    AND document_type = ANY(required_docs);
    
    RETURN uploaded_count = array_length(required_docs, 1);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update status function to reflect 5 required docs
CREATE OR REPLACE FUNCTION get_booking_document_status(booking_uuid UUID)
RETURNS TABLE (
    total_required INTEGER,
    uploaded_count INTEGER,
    pending_count INTEGER,
    approved_count INTEGER,
    rejected_count INTEGER,
    is_complete BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        5 as total_required,
        COUNT(*)::INTEGER as uploaded_count,
        COUNT(*) FILTER (WHERE verification_status = 'pending')::INTEGER as pending_count,
        COUNT(*) FILTER (WHERE verification_status = 'approved')::INTEGER as approved_count,
        COUNT(*) FILTER (WHERE verification_status = 'rejected')::INTEGER as rejected_count,
        check_booking_documents_complete(booking_uuid) as is_complete
    FROM public.booking_documents 
    WHERE booking_id = booking_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMIT;
