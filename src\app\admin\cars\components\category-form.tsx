"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ImageUpload } from "@/components/ui/image-upload"
import { Edit, Plus } from "lucide-react"
import { CategoryData } from "../actions/category-actions"
import { TransmissionType } from "@/lib/types"

interface InputLProps {
  label: string
  value: string
  onChange: (v: string) => void
  type?: string
  placeholder?: string
  required?: boolean
  min?: string
  max?: string
  step?: string
}

function InputL({
  label,
  value,
  onChange,
  type = "text",
  placeholder,
  required,
  min,
  max,
  step,
}: InputLProps) {
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-foreground">
        {label}{required ? " *" : ""}
      </label>
      <input
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-3 py-2 border border-input rounded-md bg-background text-sm focus:ring-2 focus:ring-ring focus:border-transparent transition-colors"
        placeholder={placeholder}
        required={required}
        min={min}
        max={max}
        step={step}
      />
    </div>
  )
}

// Note: Vehicle Type is intentionally omitted in this form.
// New categories define a new vehicle type; the type is derived server-side from the name.

interface CheckboxGroupProps {
  label: string
  options: string[]
  selectedOptions: string[]
  onChange: (selected: string[]) => void
}

function CheckboxGroup({
  label,
  options,
  selectedOptions,
  onChange,
}: CheckboxGroupProps) {
  const handleToggle = (option: string) => {
    if (selectedOptions.includes(option)) {
      onChange(selectedOptions.filter(o => o !== option))
    } else {
      onChange([...selectedOptions, option])
    }
  }

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-foreground">{label}</label>
      <div className="flex flex-wrap gap-3">
        {options.map((option) => (
          <label key={option} className="flex items-center gap-2">
            <input 
              type="checkbox" 
              checked={selectedOptions.includes(option)}
              onChange={() => handleToggle(option)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm">{option}</span>
          </label>
        ))}
      </div>
    </div>
  )
}

interface CategoryFormProps {
  id?: string
  onClose: () => void
  categoryData?: CategoryData | null
}

export function CategoryForm({ id, onClose, categoryData }: CategoryFormProps) {
  const [form, setForm] = React.useState({
    name: categoryData?.name || "",
    imageUrl: categoryData?.imageUrl || "/placeholder.svg",
    priceRangeFrom: categoryData?.priceRangeFrom || 1000,
    priceRangeTo: categoryData?.priceRangeTo || 3000,
    transmissionTypes: categoryData?.transmissionTypes || ["Automatic"] as TransmissionType[]
  })
  
  function onChange<K extends keyof typeof form>(key: K, value: any) {
    setForm((f) => ({ ...f, [key]: value }))
  }

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault()
    
    // Import dynamically to avoid circular dependency
    const { addCategoryAction, updateCategoryAction } = await import("../actions/category-actions")
    
    // Prepare category data
    const categoryData = {
      name: form.name,
      imageUrl: form.imageUrl,
      priceRangeFrom: form.priceRangeFrom,
      priceRangeTo: form.priceRangeTo,
      transmissionTypes: form.transmissionTypes,
      // cars property is handled by the actions automatically
    }
    
    // Call the appropriate action
    if (id) {
      await updateCategoryAction(id, categoryData)
    } else {
      await addCategoryAction(categoryData)
    }
    
    onClose()
  }

  return (
    <div className="bg-white">
      <form onSubmit={onSubmit} className="space-y-6 pt-4 bg-white">
        {/* Basic Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-foreground border-b pb-2">Category Information</h3>
          
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-800 mb-4">
            <p className="font-medium">Note: Categories are automatically generated based on your fleet inventory.</p>
            <p className="mt-1">Customizations here will affect how categories appear to customers, but the actual vehicle data is determined by the cars in your fleet.</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputL 
              label="Category Name" 
              value={form.name} 
              onChange={(v) => onChange("name", v)} 
              required 
            />
          </div>
        </div>

        {/* Price Range Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-foreground border-b pb-2">Price Range</h3>
          
          {categoryData && categoryData.carCount === 0 ? (
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-md">
              <p className="text-amber-800 font-medium">No vehicles in this category</p>
              <p className="text-amber-700 text-sm mt-1">
                Add vehicles to your fleet to automatically populate this category.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <InputL 
                label="Price From (₱)" 
                type="number" 
                value={String(form.priceRangeFrom)} 
                onChange={(v) => onChange("priceRangeFrom", Number(v))}
                min="500"
                step="50"
                required
              />
              <InputL 
                label="Price To (₱)" 
                type="number" 
                value={String(form.priceRangeTo)} 
                onChange={(v) => onChange("priceRangeTo", Number(v))}
                min="500"
                step="50"
                required
              />
              {categoryData && categoryData.cars && categoryData.cars.length > 0 && (
                <div className="md:col-span-2 mt-1">
                  <p className="text-xs text-muted-foreground">
                    Price range is calculated from your fleet: {categoryData.cars.length} vehicle(s) with prices 
                    from ₱{Math.min(...categoryData.cars.map(car => car.price_per_day)).toLocaleString()} 
                    to ₱{Math.max(...categoryData.cars.map(car => car.price_per_day)).toLocaleString()} per day
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Transmission Types */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-foreground border-b pb-2">Transmission Types</h3>
          <CheckboxGroup
            label="Available Transmission Types"
            options={["Manual", "Automatic"]}
            selectedOptions={form.transmissionTypes}
            onChange={(selected) => onChange("transmissionTypes", selected as TransmissionType[])}
          />
        </div>

        {/* Image Upload Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-foreground border-b pb-2">Category Image</h3>
          <ImageUpload 
            label="Category Image" 
            value={form.imageUrl} 
            onChange={(url) => onChange("imageUrl", url)} 
          />
          <p className="text-sm text-muted-foreground mt-2">
            Upload a representative image for this vehicle category.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button type="button" variant="secondary" onClick={onClose} className="px-6">
            Cancel
          </Button>
          <Button type="submit" variant="primary" className="px-6">
            {id ? (
              <>
                <Edit className="h-4 w-4 mr-2" />
                Save Changes
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Add Category
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
