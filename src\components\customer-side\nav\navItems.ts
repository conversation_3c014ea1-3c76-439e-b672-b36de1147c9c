// Navigation items configuration for customer-side navigation
// Priority levels: 1 = highest (always visible), 5 = lowest (first to be moved to "More")

import {
  Home,
  Car,
  User,
  CalendarDays,
  HelpCircle,
  Phone,
  Scale,
  Settings,
  LogIn,
  UserPlus,
  LucideIcon,
} from "lucide-react";

export interface NavItem {
  key: string;
  label: string;
  href: string;
  icon: LucideIcon;
  priority: number; // 1 = highest priority, 5 = lowest
  requiresAuth?: boolean;
  hideWhenAuth?: boolean;
  isAction?: boolean; // Primary CTA
}

export const customerNavItems: NavItem[] = [
  {
    key: "home",
    label: "Home",
    href: "/",
    icon: Home,
    priority: 1,
  },
  {
    key: "catalog",
    label: "Fleet",
    href: "/customer/catalog",
    icon: Car,
    priority: 1,
  },
  {
    key: "dashboard",
    label: "Account",
    href: "/customer/dashboard",
    icon: User,
    priority: 2,
    requiresAuth: true,
  },
  {
    key: "faq",
    label: "FAQ",
    href: "/customer/faq",
    icon: HelpCircle,
    priority: 3,
  },
  {
    key: "contact",
    label: "Contact",
    href: "/customer/contact",
    icon: Phone,
    priority: 5,
  },
  {
    key: "terms",
    label: "Terms",
    href: "/customer/terms",
    icon: Scale,
    priority: 4,
  },
  {
    key: "settings",
    label: "Settings",
    href: "/customer/settings",
    icon: Settings,
    priority: 4,
    requiresAuth: true,
  },
];

// Auth-only items that appear in "More" menu or replace nav items when not authenticated
export const authNavItems: NavItem[] = [];

// Get filtered nav items based on auth status
export function getFilteredNavItems(isAuthenticated: boolean): NavItem[] {
  const baseItems = customerNavItems.filter((item) => {
    if (item.requiresAuth && !isAuthenticated) return false;
    return true;
  });

  if (!isAuthenticated) {
    // Add auth items when not authenticated
    return [...baseItems, ...authNavItems].sort(
      (a, b) => a.priority - b.priority
    );
  }

  return baseItems.sort((a, b) => a.priority - b.priority);
}

// Get items for bottom tab bar (mobile) - max 5 items
export function getBottomTabItems(isAuthenticated: boolean): NavItem[] {
  const allItems = getFilteredNavItems(isAuthenticated);

  // For mobile bottom tabs, we want the most important items
  const primaryItems = allItems.filter((item) => item.priority <= 2);

  // Ensure we have exactly 4-5 items for bottom nav
  if (primaryItems.length > 5) {
    return primaryItems.slice(0, 5);
  }

  // If we need more items, add from priority 3
  if (primaryItems.length < 4) {
    const additionalItems = allItems.filter((item) => item.priority === 3);
    return [...primaryItems, ...additionalItems].slice(0, 5);
  }

  return primaryItems;
}

// Get overflow items for "More" menu
export function getMoreMenuItems(
  isAuthenticated: boolean,
  visibleItems: NavItem[]
): NavItem[] {
  const allItems = getFilteredNavItems(isAuthenticated);
  const visibleKeys = new Set(visibleItems.map((item) => item.key));

  return allItems.filter((item) => !visibleKeys.has(item.key));
}
