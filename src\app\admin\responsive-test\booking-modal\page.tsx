"use client"

import * as React from "react"
import { BookingDetailsModal } from "@/components/admin/bookings/booking-details-modal"
import { Button } from "@/components/ui/button"
import { addDays, subDays } from "date-fns"

export default function BookingModalResponsiveTest() {
  const [isModalOpen, setIsModalOpen] = React.useState(false)
  const [currentViewport, setCurrentViewport] = React.useState("")

  // Update viewport info on resize
  React.useEffect(() => {
    const updateViewport = () => {
      const width = window.innerWidth
      if (width < 375) {
        setCurrentViewport(`Mobile S: ${width}px`)
      } else if (width < 425) {
        setCurrentViewport(`Mobile M: ${width}px`) 
      } else if (width < 768) {
        setCurrentViewport(`Mobile L: ${width}px`)
      } else if (width < 1024) {
        setCurrentViewport(`Tablet: ${width}px`)
      } else {
        setCurrentViewport(`Desktop: ${width}px`)
      }
    }

    updateViewport()
    window.addEventListener('resize', updateViewport)
    return () => window.removeEventListener('resize', updateViewport)
  }, [])

  // Mock booking data with comprehensive information
  const mockBooking = {
    id: "B001-2024-RESPONSIVE-TEST",
    userName: "Maria Elena Santos-Rodriguez",
    carModel: "Toyota Camry 2024 Hybrid XLE Premium",
    from: subDays(new Date(), 1),
    to: addDays(new Date(), 6),
    days: 7,
    status: "Pending" as const,
    payStatus: "Partial" as const,
    totalAmount: 35000,
    pickup_location: "Ninoy Aquino International Airport Terminal 3 - Long Location Name That Might Wrap",
    dropoff_location: "Cebu Mactan International Airport Terminal 2",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }

  const mockCustomer = {
    name: "Maria Elena Santos-Rodriguez",
    email: "<EMAIL>", 
    phone: "+63 ************"
  }

  const mockCar = {
    model: "Toyota Camry 2024 Hybrid XLE Premium",
    plate_number: "ABC-1234",
    transmission: "CVT Automatic",
    type: "Sedan Premium",
    seats: 5
  }

  const mockPayment = {
    id: "PAY001-2024",
    amount: 35000,
    method: "GCash Online Payment",
    status: "Pending Verification",
    transaction_id: "GCASH-TXN-2024-1234567890-ABCDEF",
    proof_of_payment_url: "https://example.com/payment-proof.jpg"
  }

  const handleOpenModal = () => {
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
  }

  const handleAddToCalendar = () => {
    console.log("Add to calendar clicked")
    // Mock calendar functionality
  }

  const handleAddToOnPageCalendar = () => {
    console.log("Add to on-page calendar clicked")
    // Mock on-page calendar functionality
  }

  return (
    <div className="container mx-auto p-3 xs:p-4 sm:p-6 space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h1 className="text-xl sm:text-2xl font-bold text-blue-800 mb-2">
          Admin Booking Details Modal - Responsive Test
        </h1>
        <p className="text-blue-700 text-sm sm:text-base">
          Testing modal responsiveness and duplicate close button fix across all mobile breakpoints.
        </p>
      </div>

      {/* Current Viewport Indicator */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3">Current Viewport: {currentViewport}</h2>
        <div className="grid grid-cols-2 xs:grid-cols-3 sm:grid-cols-5 gap-2 text-xs sm:text-sm">
          <div className="bg-red-100 border border-red-300 p-2 rounded text-center block xs:hidden">
            Mobile S<br/>320px
          </div>
          <div className="bg-orange-100 border border-orange-300 p-2 rounded text-center hidden xs:block sm:hidden">
            Mobile M<br/>375px
          </div>
          <div className="bg-yellow-100 border border-yellow-300 p-2 rounded text-center hidden sm:block md:hidden">
            Mobile L<br/>425px
          </div>
          <div className="bg-green-100 border border-green-300 p-2 rounded text-center hidden md:block lg:hidden">
            Tablet<br/>768px
          </div>
          <div className="bg-blue-100 border border-blue-300 p-2 rounded text-center hidden lg:block">
            Desktop<br/>1024px+
          </div>
        </div>
      </div>

      {/* Test Controls */}
      <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
        <h2 className="text-lg font-semibold">Modal Test Controls</h2>
        
        <div className="grid grid-cols-1 xs:grid-cols-2 gap-3">
          <Button
            onClick={handleOpenModal}
            className="w-full h-12 text-sm sm:text-base"
            size="lg"
          >
            Open Booking Details Modal
          </Button>
          
          <Button
            variant="secondary"
            onClick={() => window.location.reload()}
            className="w-full h-12 text-sm sm:text-base"
            size="lg"
          >
            Refresh Test Page
          </Button>
        </div>
      </div>

      {/* Testing Checklist */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">Responsive Testing Checklist</h2>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-gray-800 mb-2">✅ Duplicate Close Button Fix</h3>
            <ul className="text-sm text-gray-600 space-y-1 pl-4">
              <li>• Only one close button should be visible (in footer)</li>
              <li>• No absolute-positioned close button in top-right</li>
              <li>• Close button should be modernized with proper styling</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-gray-800 mb-2">📱 Mobile Breakpoints (320px - 425px)</h3>
            <ul className="text-sm text-gray-600 space-y-1 pl-4">
              <li>• Modal content should not overflow horizontally</li>
              <li>• Touch targets should be ≥44px height</li>
              <li>• Text should remain readable at all sizes</li>
              <li>• Buttons should stack vertically on narrow screens</li>
              <li>• Proper padding and margins for small screens</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-gray-800 mb-2">💻 Desktop Experience</h3>
            <ul className="text-sm text-gray-600 space-y-1 pl-4">
              <li>• Modal should maintain proper width constraints</li>
              <li>• Button layout should be horizontal on larger screens</li>
              <li>• No regressions in desktop functionality</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-gray-800 mb-2">🎯 Accessibility</h3>
            <ul className="text-sm text-gray-600 space-y-1 pl-4">
              <li>• Close button should have proper aria-labels</li>
              <li>• Modal should trap focus correctly</li>
              <li>• Keyboard navigation should work properly</li>
              <li>• Screen reader announcements should be clear</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Test Data Overview */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-3">Mock Test Data</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">Booking Details:</h4>
            <ul className="text-gray-600 space-y-1">
              <li>• Long customer name to test overflow</li>
              <li>• Long car model name to test wrapping</li>
              <li>• Long location names to test truncation</li>
              <li>• Multiple day booking period</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">Content Features:</h4>
            <ul className="text-gray-600 space-y-1">
              <li>• Customer contact information</li>
              <li>• Payment verification buttons</li>
              <li>• Document verification section</li>
              <li>• Booking management controls</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-amber-800 mb-3">Testing Instructions</h2>
        <ol className="text-amber-700 text-sm space-y-2 pl-4">
          <li>1. <strong>Open the modal</strong> using the button above</li>
          <li>2. <strong>Test mobile breakpoints:</strong> Resize browser to 320px, 375px, 425px</li>
          <li>3. <strong>Verify close button:</strong> Only one close button should be visible in footer</li>
          <li>4. <strong>Check responsiveness:</strong> All content should fit without horizontal scrolling</li>
          <li>5. <strong>Test interactions:</strong> Click all buttons and verify they work properly</li>
          <li>6. <strong>Test keyboard navigation:</strong> Use Tab key to navigate through modal</li>
          <li>7. <strong>Verify touch targets:</strong> All buttons should be easy to tap on mobile</li>
        </ol>
      </div>

      {/* Booking Details Modal */}
      <BookingDetailsModal
        booking={mockBooking}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onAddToCalendar={handleAddToCalendar}
        onAddToOnPageCalendar={handleAddToOnPageCalendar}
        useOnPageCalendar={true}
        customer={mockCustomer}
        car={mockCar}
        payment={mockPayment}
        onPaymentVerification={(paymentId, action, reason) => {
          console.log("Payment verification:", { paymentId, action, reason })
        }}
        onStatusChange={(bookingId, status) => {
          console.log("Status change:", { bookingId, status })
        }}
        currentAdminId="admin-test-001"
      />
    </div>
  )
}
