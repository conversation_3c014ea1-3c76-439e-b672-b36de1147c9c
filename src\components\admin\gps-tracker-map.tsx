"use client"

import React, { useEffect, useRef, useState } from 'react'
import { useIsMobile } from '@/hooks/use-mobile'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, CircleMarker } from 'react-leaflet'
import L from 'leaflet'
import { GPSLocation, getCurrentGPSLocations, simulateGPSUpdate, getCarRoute, fetchCurrentGPSLocations, CarRoute, generateSimulatedTrack } from '@/lib/gps-data'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MapPin, Navigation, Clock, User } from 'lucide-react'

// Fix for Leaflet default markers in Next.js
delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
})

// Custom car icons
const createCarIcon = (heading: number = 0, color: string = '#3b82f6', isMobile: boolean = false) => {
  // Use larger size for mobile to ensure touch-friendly targets (≥44px)
  const size = isMobile ? 44 : 24;
  const iconSize = isMobile ? 24 : 14;
  const borderWidth = isMobile ? 3 : 2;
  
  return L.divIcon({
    className: 'custom-car-icon',
    html: `
      <div style="
        width: ${size}px; 
        height: ${size}px; 
        background: ${color}; 
        border: ${borderWidth}px solid white; 
        border-radius: 50%; 
        display: flex; 
        align-items: center; 
        justify-content: center;
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        transform: rotate(${heading}deg);
      ">
        <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="white">
          <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/>
        </svg>
      </div>
    `,
    iconSize: [size, size],
    iconAnchor: [size/2, size/2],
  })
}

interface GPSTrackerMapProps {
  selectedCarId?: string
  followingCarId?: string
  onCarSelect?: (carId: string) => void
  showClusters?: boolean
  showTrails?: boolean
  showGeofences?: boolean
  timeWindow?: 'live' | '15m' | '1h' | '24h'
  className?: string
  // New props for ESP32 URL parameter support
  locations?: GPSLocation[]
  initialCenter?: [number, number]
  initialZoom?: number
}

export function GPSTrackerMap({ 
  selectedCarId, 
  followingCarId,
  onCarSelect,
  showClusters = true,
  showTrails: propShowTrails = false,
  showGeofences = false,
  timeWindow = 'live',
  className,
  // New props for ESP32 URL parameter support
  locations: propLocations,
  initialCenter,
  initialZoom
}: GPSTrackerMapProps) {
  const isMobile = useIsMobile();
  const [locations, setLocations] = useState<GPSLocation[]>([])
  const [selectedCar, setSelectedCar] = useState<GPSLocation | null>(null)
  const [showRoutes, setShowRoutes] = useState(false)
  const [routes, setRoutes] = useState<{ [carId: string]: Array<[number, number]> }>({})
  const [trackRoutes, setTrackRoutes] = useState<{ [carId: string]: CarRoute }>({})
  const [loadingTracks, setLoadingTracks] = useState(false)
  const mapRef = useRef<L.Map | null>(null)

  // Initialize GPS locations from API or use passed locations
  useEffect(() => {
    if (propLocations) {
      // Use locations passed from parent (includes ESP32 URL data)
      setLocations(propLocations)
    } else {
      // Fallback to loading from API
      const loadInitialData = async () => {
        try {
          const initialLocations = await fetchCurrentGPSLocations()
          setLocations(initialLocations)
        } catch (error) {
          console.error('Failed to load initial GPS data:', error)
          const devLocation = getCurrentGPSLocations()
          setLocations(devLocation)
        }
      }
      loadInitialData()
    }
  }, [propLocations])

  // Set up real-time updates from API only if not using passed locations
  useEffect(() => {
    if (propLocations) return // Skip if using passed locations from parent
    
    const interval = setInterval(async () => {
      try {
        const freshLocations = await fetchCurrentGPSLocations()
        setLocations(freshLocations)
      } catch (error) {
        console.error('Failed to fetch updated GPS data:', error)
      }
    }, 5000)

    return () => clearInterval(interval)
  }, [propLocations])

  // Handle selected car change
  useEffect(() => {
    if (selectedCarId) {
      const car = locations.find(l => l.carId === selectedCarId)
      setSelectedCar(car || null)
      
      // Only center on first selection, preserve zoom level
      if (car && mapRef.current && !selectedCar) {
        const currentZoom = mapRef.current.getZoom()
        mapRef.current.setView([car.latitude, car.longitude], currentZoom)
      }
    }
  }, [selectedCarId, locations])

  // Keep selected car reference fresh when locations update
  useEffect(() => {
    if (!selectedCarId) return
    const refreshed = locations.find(l => l.carId === selectedCarId) || null
    setSelectedCar(refreshed)
  }, [locations, selectedCarId])

  // Load routes when needed
  useEffect(() => {
    if (showRoutes) {
      const routeData: { [carId: string]: Array<[number, number]> } = {}
      locations.forEach(location => {
        const route = getCarRoute(location.carId)
        if (route) {
          routeData[location.carId] = route.route.map(point => [point.lat, point.lng])
        }
      })
      setRoutes(routeData)
    }
  }, [showRoutes, locations])

  // Load track logs when showTrails is enabled
  useEffect(() => {
    const loadTracks = async () => {
      if (!propShowTrails) {
        setTrackRoutes({})
        return
      }

      setLoadingTracks(true)
      try {
        const trackData: { [carId: string]: CarRoute } = {}
        
        // Fetch tracks for all visible cars
        const trackPromises = locations.map(async (location) => {
          try {
            const route = await getCarRoute(location.carId, timeWindow)
            if (route && route.route.length > 1) {
              trackData[location.carId] = route
            }
          } catch (error) {
            console.warn(`Failed to load track for car ${location.carId}:`, error)
          }
        })

        await Promise.allSettled(trackPromises)
        setTrackRoutes(trackData)
      } catch (error) {
        console.error('Failed to load tracks:', error)
      } finally {
        setLoadingTracks(false)
      }
    }

    loadTracks()
  }, [propShowTrails, timeWindow, locations])

  // Determine initial map settings
  const mapCenter: [number, number] = initialCenter || [14.5995, 120.9842]; // Manila center as fallback
  const mapZoom = initialZoom || 12;

  return (
    <div className="h-full flex flex-col">
      <Card className="h-full">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MapPin className="w-5 h-5" />
              Live GPS Tracking
            </div>
            {loadingTracks && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="w-3 h-3 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                Loading tracks...
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0 h-[calc(100%-5rem)]">
          <div className="h-full w-full relative">
            <MapContainer
              center={mapCenter}
              zoom={mapZoom}
              className="h-full w-full rounded-b-lg"
              ref={mapRef}
            >
              <TileLayer
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              />
                  
              {/* Car Markers */}
              {locations
                .filter((l) => {
                  const staleSeconds = Number(process.env.NEXT_PUBLIC_GPS_STALE_SECONDS || '60')
                  const maxAgeMs = Math.max(0, staleSeconds) * 1000
                  const isFresh = Date.now() - l.timestamp.getTime() <= maxAgeMs
                  return isFresh
                })
                .map((location) => (
                <Marker
                  key={location.id}
                  position={[location.latitude, location.longitude]}
                  icon={createCarIcon(location.heading, location.carId === 'esp32_dev' ? '#10b981' : '#3b82f6', isMobile)}
                  eventHandlers={{
                    click: () => {
                      setSelectedCar(location)
                      onCarSelect?.(location.carId)
                    },
                  }}
                >
                  <Popup>
                    <div className="w-[200px] xs:w-[220px] sm:w-[250px]">
                      <div className="font-semibold truncate">{location.carModel}</div>
                      <div className="text-sm text-muted-foreground mb-2 truncate">{location.carPlate}</div>
                      
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span>Speed:</span>
                          <span className="font-medium">{location.speed} km/h</span>
                        </div>
                        
                        {location.driverName && (
                          <div className="flex justify-between">
                            <span>Driver:</span>
                            <span className="font-medium truncate max-w-[120px]">{location.driverName}</span>
                          </div>
                        )}
                        
                        <div className="text-xs text-muted-foreground pt-1">
                          Updated: {location.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  </Popup>
                </Marker>
              ))}
                  
              {/* Routes */}
              {showRoutes && Object.entries(routes).map(([carId, route]) => {
                const car = locations.find(l => l.carId === carId)
                if (!car || route.length < 2) return null
                
                const color = '#3b82f6' // Use neutral blue for all routes
                
                return (
                  <Polyline
                    key={carId}
                    positions={route}
                    color={color}
                    weight={3}
                    opacity={0.7}
                  />
                )
              })}

              {/* Track Logs */}
              {propShowTrails && Object.entries(trackRoutes).map(([carId, trackRoute]) => {
                const car = locations.find(l => l.carId === carId)
                if (!car || trackRoute.route.length < 2) return null
                
                // Use different colors for different cars
                const colorOptions = ['#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16']
                const colorIndex = locations.findIndex(l => l.carId === carId) % colorOptions.length
                const trackColor = colorOptions[colorIndex]
                
                // Convert route to polyline positions
                const positions = trackRoute.route.map(point => [point.lat, point.lng] as [number, number])
                
                return (
                  <React.Fragment key={`track-${carId}`}>
                    {/* Track line */}
                    <Polyline
                      positions={positions}
                      color={trackColor}
                      weight={2}
                      opacity={0.6}
                      dashArray="5, 5"
                    />
                    
                    {/* Track dots/markers */}
                    {trackRoute.route.map((point, index) => (
                      <CircleMarker
                        key={`track-dot-${carId}-${index}`}
                        center={[point.lat, point.lng]}
                        radius={3}
                        fillColor={trackColor}
                        color="white"
                        weight={1}
                        opacity={0.8}
                        fillOpacity={0.7}
                      >
                        <Popup>
                          <div className="text-sm">
                            <div className="font-semibold">{car.carModel}</div>
                            <div className="text-muted-foreground">Track Point</div>
                            <div className="mt-1">
                              <div>Time: {point.timestamp.toLocaleTimeString()}</div>
                              <div>Position: {point.lat.toFixed(6)}, {point.lng.toFixed(6)}</div>
                            </div>
                          </div>
                        </Popup>
                      </CircleMarker>
                    ))}
                  </React.Fragment>
                )
              })}
            </MapContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
