"use client";

import * as React from "react";
import * as TooltipPrimitive from "@radix-ui/react-tooltip";

import { cn } from "@/lib/utils";

// Enhanced Tooltip Provider with improved defaults for admin UI
function TooltipProvider({
  delayDuration = 200, // Reduced delay for better UX
  skipDelayDuration = 100,
  disableHoverableContent = false,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {
  return (
    <TooltipPrimitive.Provider
      data-slot="tooltip-provider"
      delayDuration={delayDuration}
      skipDelayDuration={skipDelayDuration}
      disableHoverableContent={disableHoverableContent}
      {...props}
    />
  );
}

// Enhanced Tooltip Root with better defaults
function Tooltip({
  delayDuration,
  disableHoverableContent,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Root>) {
  return (
    <TooltipProvider
      delayDuration={delayDuration}
      disableHoverableContent={disableHoverableContent}
    >
      <TooltipPrimitive.Root data-slot="tooltip" {...props} />
    </TooltipProvider>
  );
}

function TooltipTrigger({
  className,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {
  return (
    <TooltipPrimitive.Trigger
      data-slot="tooltip-trigger"
      className={cn(
        // Ensure focus visibility for accessibility
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",
        className
      )}
      {...props}
    />
  );
}

interface TooltipContentProps
  extends React.ComponentProps<typeof TooltipPrimitive.Content> {
  variant?: "default" | "inverse" | "warning" | "error" | "info" | "success";
  size?: "sm" | "md" | "lg";
  showArrow?: boolean;
  dismissible?: boolean;
}

function TooltipContent({
  className,
  sideOffset = 8,
  children,
  variant = "default",
  size = "md",
  showArrow = true,
  dismissible = false,
  ...props
}: TooltipContentProps) {
  const [isManuallyDismissed, setIsManuallyDismissed] = React.useState(false);

  const variantClasses = {
    default: "bg-gray-900 text-white border-gray-800",
    inverse: "bg-white text-gray-900 border-gray-200 shadow-lg",
    warning: "bg-amber-600 text-white border-amber-700",
    error: "bg-red-600 text-white border-red-700",
    info: "bg-blue-600 text-white border-blue-700",
    success: "bg-green-600 text-white border-green-700",
  };

  const sizeClasses = {
    sm: "px-2 py-1 text-xs max-w-xs",
    md: "px-3 py-1.5 text-sm max-w-sm",
    lg: "px-4 py-2 text-base max-w-md",
  };

  const arrowClasses = {
    default: "fill-gray-900",
    inverse: "fill-white",
    warning: "fill-amber-600",
    error: "fill-red-600",
    info: "fill-blue-600",
    success: "fill-green-600",
  };

  if (isManuallyDismissed) {
    return null;
  }

  return (
    <TooltipPrimitive.Portal>
      <TooltipPrimitive.Content
        data-slot="tooltip-content"
        sideOffset={sideOffset}
        className={cn(
          // Base styles with improved animations and accessibility
          "relative z-50 rounded-md border text-balance font-medium leading-tight",
          "animate-in fade-in-0 zoom-in-95 duration-200",
          "data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[state=closed]:duration-150",
          "data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2",
          "data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
          // Transform origin for smooth scaling
          "origin-[var(--radix-tooltip-content-transform-origin)]",
          // High contrast for accessibility
          variantClasses[variant],
          sizeClasses[size],
          // Ensure tooltip doesn't break layout
          "break-words hyphens-auto",
          className
        )}
        onEscapeKeyDown={(e) => {
          if (dismissible) {
            setIsManuallyDismissed(true);
          }
        }}
        {...props}
      >
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1">{children}</div>
          {dismissible && (
            <button
              type="button"
              onClick={() => setIsManuallyDismissed(true)}
              className={cn(
                "flex-shrink-0 ml-2 rounded-sm p-0.5 transition-colors",
                "hover:bg-black/10 focus-visible:bg-black/10 focus-visible:outline-none",
                variant === "inverse" &&
                  "hover:bg-gray-100 focus-visible:bg-gray-100"
              )}
              aria-label="Dismiss tooltip"
            >
              <svg
                className="h-3 w-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          )}
        </div>
        {showArrow && (
          <TooltipPrimitive.Arrow
            className={cn(
              "z-50 size-2.5 rotate-45 rounded-[2px]",
              arrowClasses[variant]
            )}
          />
        )}
      </TooltipPrimitive.Content>
    </TooltipPrimitive.Portal>
  );
}

// Specialized tooltip variants for common admin use cases
interface AdminTooltipProps {
  children: React.ReactNode;
  content: React.ReactNode;
  shortcut?: string;
  status?: "online" | "offline" | "pending" | "error";
  preview?: React.ReactNode;
  side?: "top" | "right" | "bottom" | "left";
  align?: "start" | "center" | "end";
  delayDuration?: number;
  className?: string;
}

function AdminTooltip({
  children,
  content,
  shortcut,
  status,
  preview,
  side = "top",
  align = "center",
  delayDuration = 200,
  className,
  ...props
}: AdminTooltipProps) {
  const statusVariants = {
    online: "success",
    offline: "default",
    pending: "warning",
    error: "error",
  } as const;

  return (
    <Tooltip delayDuration={delayDuration} {...props}>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent
        side={side}
        align={align}
        variant={status ? statusVariants[status] : "inverse"}
        size={preview ? "lg" : "md"}
        dismissible={Boolean(preview)}
        className={className}
      >
        <div className="space-y-1">
          {typeof content === "string" ? <div>{content}</div> : content}

          {shortcut && (
            <div className="flex items-center gap-1 text-xs opacity-75 mt-1">
              <kbd className="inline-flex items-center rounded border border-current px-1 py-0.5 font-mono text-[10px]">
                {shortcut}
              </kbd>
            </div>
          )}

          {status && (
            <div className="flex items-center gap-1 text-xs mt-1">
              <div
                className={cn(
                  "h-1.5 w-1.5 rounded-full",
                  status === "online" && "bg-green-400",
                  status === "offline" && "bg-gray-400",
                  status === "pending" && "bg-yellow-400",
                  status === "error" && "bg-red-400"
                )}
              />
              <span className="capitalize">{status}</span>
            </div>
          )}

          {preview && (
            <div className="border-t border-current/20 pt-2 mt-2">
              {preview}
            </div>
          )}
        </div>
      </TooltipContent>
    </Tooltip>
  );
}

export {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
  AdminTooltip,
};
