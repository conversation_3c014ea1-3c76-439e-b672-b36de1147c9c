"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Mail } from "lucide-react"
import Image from "next/image"

export default function ForgotPasswordPage() {
  const [email, setEmail] = React.useState("")
  const [isSubmitted, setIsSubmitted] = React.useState(false)

  function onSubmit(e: React.FormEvent) {
    e.preventDefault()
    // For demo purposes, just show success message
    setIsSubmitted(true)
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen grid place-items-center bg-muted/20 px-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full">
                <Mail className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <CardTitle className="text-xl font-semibold">Check Your Email</CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              We've sent password reset instructions to {email}
            </p>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-center text-muted-foreground mb-4">
              Didn't receive the email? Check your spam folder or try again.
            </p>
          </CardContent>
          <CardFooter className="text-center">
            <a 
              href="/customer/login"
              className="inline-flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 hover:underline"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to sign in
            </a>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen grid place-items-center bg-muted/20 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <Image
              src="/ollie_logo.svg"
              alt="Ollie Track"
              width={120}
              height={40}
              className="h-10 w-auto"
            />
          </div>
          <CardTitle className="text-xl font-semibold">Reset Your Password</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Enter your email address and we'll send you instructions to reset your password
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={onSubmit} className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="email" className="text-sm font-medium">
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="h-10"
              />
            </div>
            <Button type="submit" className="w-full h-10 bg-blue-600 hover:bg-blue-700">
              Send Reset Instructions
            </Button>
          </form>
        </CardContent>
        <CardFooter className="text-center">
          <a 
            href="/customer/login"
            className="inline-flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 hover:underline"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to sign in
          </a>
        </CardFooter>
      </Card>
    </div>
  )
}
