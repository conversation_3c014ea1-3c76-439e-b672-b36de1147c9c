-- Fix the final 2 function security warnings
-- Targets: update_category_pricing and sync_car_archive_status

-- ========================================
-- DISCOVER EXACT FUNCTION SIGNATURES FIRST
-- ========================================

-- Show the exact signatures of the remaining problematic functions
SELECT 
  p.proname as function_name,
  pg_get_function_identity_arguments(p.oid) as exact_arguments,
  p.prosecdef as security_definer,
  p.proconfig as current_config,
  CASE WHEN p.prosecdef THEN '✅ SECURE' ELSE '❌ NEEDS SECURITY DEFINER' END as security_status,
  CASE WHEN 'search_path=public' = ANY(p.proconfig) THEN '✅ FIXED' ELSE '❌ NEEDS SEARCH_PATH' END as search_path_status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
  AND p.proname IN ('update_category_pricing', 'sync_car_archive_status')
ORDER BY p.proname;

-- ========================================
-- FIX THE 2 REMAINING FUNCTION WARNINGS
-- ========================================

-- Fix update_category_pricing (discover and fix any signature)
DO $$
DECLARE
  func_record RECORD;
  fix_sql TEXT;
BEGIN
  -- Find the exact function signature
  FOR func_record IN 
    SELECT 
      p.proname,
      pg_get_function_identity_arguments(p.oid) as args,
      p.oid
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public' AND p.proname = 'update_category_pricing'
  LOOP
    -- Build the ALTER FUNCTION statement with exact signature
    fix_sql := 'ALTER FUNCTION public.' || func_record.proname || '(' || func_record.args || ') SET search_path = public';
    EXECUTE fix_sql;
    
    fix_sql := 'ALTER FUNCTION public.' || func_record.proname || '(' || func_record.args || ') SECURITY DEFINER';
    EXECUTE fix_sql;
    
    RAISE NOTICE '✅ Fixed update_category_pricing with signature: %', func_record.args;
  END LOOP;
  
  IF NOT FOUND THEN
    RAISE NOTICE '❌ Function update_category_pricing not found';
  END IF;
END $$;

-- Fix sync_car_archive_status (discover and fix any signature)
DO $$
DECLARE
  func_record RECORD;
  fix_sql TEXT;
BEGIN
  -- Find the exact function signature
  FOR func_record IN 
    SELECT 
      p.proname,
      pg_get_function_identity_arguments(p.oid) as args,
      p.oid
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public' AND p.proname = 'sync_car_archive_status'
  LOOP
    -- Build the ALTER FUNCTION statement with exact signature
    fix_sql := 'ALTER FUNCTION public.' || func_record.proname || '(' || func_record.args || ') SET search_path = public';
    EXECUTE fix_sql;
    
    fix_sql := 'ALTER FUNCTION public.' || func_record.proname || '(' || func_record.args || ') SECURITY DEFINER';
    EXECUTE fix_sql;
    
    RAISE NOTICE '✅ Fixed sync_car_archive_status with signature: %', func_record.args;
  END LOOP;
  
  IF NOT FOUND THEN
    RAISE NOTICE '❌ Function sync_car_archive_status not found';
  END IF;
END $$;

-- ========================================
-- FINAL VERIFICATION
-- ========================================

-- Verify the fixes worked
SELECT 
  p.proname as function_name,
  pg_get_function_identity_arguments(p.oid) as arguments,
  p.prosecdef as security_definer,
  p.proconfig as search_path_config,
  CASE WHEN p.prosecdef THEN '✅ SECURE' ELSE '❌ INSECURE' END as security_status,
  CASE WHEN 'search_path=public' = ANY(p.proconfig) THEN '✅ FIXED' ELSE '❌ MUTABLE' END as search_path_status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
  AND p.proname IN ('update_category_pricing', 'sync_car_archive_status')
ORDER BY p.proname;

-- Show all functions with their security status (for complete overview)
SELECT 
  p.proname as function_name,
  CASE WHEN p.prosecdef THEN '✅' ELSE '❌' END as secure,
  CASE WHEN 'search_path=public' = ANY(p.proconfig) THEN '✅' ELSE '❌' END as fixed_path
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
ORDER BY p.proname;
