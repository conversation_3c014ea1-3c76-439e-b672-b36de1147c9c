"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Mail, 
  User,
  Calendar,
  AlertTriangle
} from "lucide-react";
import { getDocumentRequests, createDocumentRequests } from "@/lib/services/document-request-service";

interface DocumentRequestsListProps {
  bookingId: string;
  customerId: string;
  customerEmail: string;
  customerName?: string;
}

const DOCUMENT_DISPLAY_NAMES: { [key: string]: string } = {
  'drivers_license': "Driver's License",
  'government_id': "Government ID",
  'proof_of_billing': "Proof of Billing"
};

export function DocumentRequestsList({ 
  bookingId, 
  customerId, 
  customerEmail, 
  customerName 
}: DocumentRequestsListProps) {
  const [requests, setRequests] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showNewRequestModal, setShowNewRequestModal] = useState(false);
  const [newRequestNotes, setNewRequestNotes] = useState("");
  const [selectedDocTypes, setSelectedDocTypes] = useState<string[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    fetchDocumentRequests();
  }, [bookingId]);

  const fetchDocumentRequests = async () => {
    setIsLoading(true);
    try {
      const result = await getDocumentRequests(bookingId);
      if (result.success && result.data) {
        setRequests(result.data);
      } else {
        console.error("Error fetching document requests:", result.error);
      }
    } catch (error) {
      console.error("Error fetching document requests:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateNewRequest = async () => {
    if (selectedDocTypes.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one document type",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const result = await createDocumentRequests({
        bookingId,
        customerId,
        customerEmail,
        customerName,
        documentTypes: selectedDocTypes,
        requestType: 'missing', // Default to missing
        adminNotes: newRequestNotes
      });

      if (result.success) {
        toast({
          title: "Success",
          description: `Document request sent successfully. Email notification sent to ${customerEmail}`,
        });
        setShowNewRequestModal(false);
        setNewRequestNotes("");
        setSelectedDocTypes([]);
        fetchDocumentRequests(); // Refresh the list
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to send document request",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
          <Clock className="w-3 h-3 mr-1" />
          Pending
        </Badge>;
      case 'fulfilled':
        return <Badge className="bg-green-100 text-green-800 border-green-200">
          <CheckCircle className="w-3 h-3 mr-1" />
          Fulfilled
        </Badge>;
      case 'expired':
        return <Badge className="bg-red-100 text-red-800 border-red-200">
          <XCircle className="w-3 h-3 mr-1" />
          Expired
        </Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const pendingRequests = requests.filter(req => req.status === 'pending');
  const completedRequests = requests.filter(req => req.status !== 'pending');

  if (requests.length === 0 && !isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Document Requests
            </CardTitle>
            <Dialog open={showNewRequestModal} onOpenChange={setShowNewRequestModal}>
              <DialogTrigger asChild>
                <Button size="sm" className="flex items-center gap-1">
                  <Mail className="w-4 h-4" />
                  New Request
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Send Document Request</DialogTitle>
                  <DialogDescription>
                    Request specific documents from {customerName || customerEmail}.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Document Types:</Label>
                    <div className="space-y-2 mt-2">
                      {Object.entries(DOCUMENT_DISPLAY_NAMES).map(([key, name]) => (
                        <div key={key} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={key}
                            checked={selectedDocTypes.includes(key)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedDocTypes([...selectedDocTypes, key]);
                              } else {
                                setSelectedDocTypes(selectedDocTypes.filter(d => d !== key));
                              }
                            }}
                            className="rounded border-gray-300"
                          />
                          <Label htmlFor={key} className="text-sm">{name}</Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="request-notes">Additional Notes (Optional)</Label>
                    <Textarea
                      id="request-notes"
                      placeholder="Add any specific instructions for the customer..."
                      value={newRequestNotes}
                      onChange={(e) => setNewRequestNotes(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowNewRequestModal(false)}>
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleCreateNewRequest} 
                    disabled={isLoading || selectedDocTypes.length === 0}
                  >
                    {isLoading ? "Sending..." : "Send Request"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No document requests found for this booking.</p>
            <p className="text-sm mt-1">Click "New Request" to send a document request to the customer.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Document Requests
            {pendingRequests.length > 0 && (
              <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 ml-2">
                {pendingRequests.length} Pending
              </Badge>
            )}
          </CardTitle>
          <Dialog open={showNewRequestModal} onOpenChange={setShowNewRequestModal}>
            <DialogTrigger asChild>
              <Button size="sm" className="flex items-center gap-1">
                <Mail className="w-4 h-4" />
                New Request
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Send Document Request</DialogTitle>
                <DialogDescription>
                  Request specific documents from {customerName || customerEmail}.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Document Types:</Label>
                  <div className="space-y-2 mt-2">
                    {Object.entries(DOCUMENT_DISPLAY_NAMES).map(([key, name]) => (
                      <div key={key} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={key}
                          checked={selectedDocTypes.includes(key)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedDocTypes([...selectedDocTypes, key]);
                            } else {
                              setSelectedDocTypes(selectedDocTypes.filter(d => d !== key));
                            }
                          }}
                          className="rounded border-gray-300"
                        />
                        <Label htmlFor={key} className="text-sm">{name}</Label>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="request-notes">Additional Notes (Optional)</Label>
                  <Textarea
                    id="request-notes"
                    placeholder="Add any specific instructions for the customer..."
                    value={newRequestNotes}
                    onChange={(e) => setNewRequestNotes(e.target.value)}
                    className="mt-1"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowNewRequestModal(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleCreateNewRequest} 
                  disabled={isLoading || selectedDocTypes.length === 0}
                >
                  {isLoading ? "Sending..." : "Send Request"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="text-sm text-gray-500 mt-2">Loading requests...</p>
          </div>
        ) : (
          <>
            {/* Pending Requests */}
            {pendingRequests.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900 flex items-center gap-2">
                  <Clock className="w-4 h-4 text-yellow-600" />
                  Active Requests ({pendingRequests.length})
                </h4>
                {pendingRequests.map((request) => (
                  <div key={request.id} className="border rounded-lg p-4 bg-yellow-50 border-yellow-200">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h5 className="font-medium">
                            {DOCUMENT_DISPLAY_NAMES[request.document_type]}
                          </h5>
                          {getStatusBadge(request.status)}
                        </div>
                        <div className="text-sm text-gray-600 space-y-1">
                          <p className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            Requested: {formatDate(request.requested_at)}
                          </p>
                          <p className="flex items-center gap-1">
                            <AlertTriangle className="w-3 h-3" />
                            Expires: {formatDate(request.expires_at)}
                          </p>
                          {request.admin_notes && (
                            <p><strong>Notes:</strong> {request.admin_notes}</p>
                          )}
                          <p className="flex items-center gap-1">
                            <Mail className="w-3 h-3" />
                            Email sent: {request.email_sent ? 'Yes' : 'No'}
                            {request.email_sent_at && ` (${formatDate(request.email_sent_at)})`}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Completed Requests */}
            {completedRequests.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900 flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  Request History ({completedRequests.length})
                </h4>
                {completedRequests.map((request) => (
                  <div key={request.id} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h5 className="font-medium">
                            {DOCUMENT_DISPLAY_NAMES[request.document_type]}
                          </h5>
                          {getStatusBadge(request.status)}
                        </div>
                        <div className="text-sm text-gray-600 space-y-1">
                          <p className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            Requested: {formatDate(request.requested_at)}
                          </p>
                          {request.fulfilled_at && (
                            <p className="flex items-center gap-1">
                              <CheckCircle className="w-3 h-3" />
                              Fulfilled: {formatDate(request.fulfilled_at)}
                            </p>
                          )}
                          {request.admin_notes && (
                            <p><strong>Notes:</strong> {request.admin_notes}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
