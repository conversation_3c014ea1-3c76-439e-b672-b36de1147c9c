#!/bin/bash

# PathLink GPS Tracker GUI - Unix Launcher
# This shell script launches the PathLink GUI application

echo "========================================"
echo "PathLink GPS Tracker - Control Panel"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "ERROR: Python is not installed or not in PATH"
        echo "Please install Python 3.7+ from https://python.org"
        echo
        read -p "Press Enter to continue..."
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "Python found. Checking dependencies..."
echo

# Check if required packages are installed
if ! $PYTHON_CMD -c "import requests, websocket" &> /dev/null; then
    echo "Installing required dependencies..."
    if ! $PYTHON_CMD -m pip install -r requirements.txt; then
        echo "ERROR: Failed to install dependencies"
        echo "Please run: $PYTHON_CMD -m pip install -r requirements.txt"
        echo
        read -p "Press Enter to continue..."
        exit 1
    fi
    echo "Dependencies installed successfully!"
    echo
fi

echo "Launching PathLink GUI..."
echo

# Launch the application
$PYTHON_CMD pathlink_gui.py

# Check if the application exited with an error
if [ $? -ne 0 ]; then
    echo
    echo "ERROR: Application exited with an error"
    echo "Check the console output above for details"
    echo
    read -p "Press Enter to continue..."
fi

echo
echo "Application closed."
read -p "Press Enter to continue..."
