// Email utility using Supabase Edge Functions for email notifications
import { createClient } from "@/lib/supabase/server";
import { buildAdminAccountCreationEmail } from "./email-templates/admin-account-creation-template";

export type SendEmailOptions = {
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
};

export type SendEmailResult = {
  ok: boolean;
  skipped?: boolean;
  error?: string;
};

export async function sendEmail(options: SendEmailOptions): Promise<SendEmailResult> {
  try {
    const supabase = await createClient();
    
    // Use Supabase Edge Function for email sending
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseAnonKey) {
      console.warn("Email sending skipped: Missing Supabase configuration.");
      return { ok: false, skipped: true, error: "Missing Supabase config" };
    }

    // Get current user for authentication (optional for server-side operations)
    const { data: { user } } = await supabase.auth.getUser();
    // Continue with email sending even if no authenticated user for server-side operations

    const toArray = Array.isArray(options.to) ? options.to : [options.to];

    // Call Supabase Edge Function for email sending
    const { data, error } = await supabase.functions.invoke('send-email', {
      body: {
        to: toArray,
        subject: options.subject,
        html: options.html,
        text: options.text,
      }
    });

    if (error) {
      console.error("Supabase Edge Function error:", error);
      // Fallback: Log email content for debugging
      console.log("Email that would have been sent:", {
        to: toArray,
        subject: options.subject,
        html: options.html,
      });
      return { ok: false, error: error.message };
    }

    console.log("Email sent successfully via Supabase:", data);
    return { ok: true };
  } catch (err: any) {
    console.error("Error sending email:", err?.message || err);
    // Log email details for debugging
    console.log("Email details for debugging:", {
      to: Array.isArray(options.to) ? options.to : [options.to],
      subject: options.subject,
    });
    return { ok: false, error: err?.message || String(err) };
  }
}

// Convenience templates
export function buildPaymentApprovedEmail(params: {
  customerName?: string | null;
  bookingRef: string;
  paymentRef?: string;
  dashboardUrl?: string;
}): { subject: string; html: string } {
  const { customerName, bookingRef, paymentRef, dashboardUrl = "/customer/dashboard" } = params;
  const subject = "Payment Approved - Ollie's Rent A Car";
  const greeting = customerName ? `Hi ${customerName},` : "Hello,";
  const html = `
    <div style="font-family: Arial, sans-serif; line-height:1.6; max-width: 600px; margin: 0 auto;">
      <!-- Header with Logo -->
      <div style="text-align: center; padding: 20px; background-color: #f8fafc; border-bottom: 2px solid #e5e7eb;">
        <img src="https://pathlinkio.app/ollie_logo.jpg" alt="Ollie's Rent A Car" style="height: 60px; width: auto; margin-bottom: 10px;">
        <h1 style="color: #1f2937; margin: 0; font-size: 24px;">Ollie's Rent A Car</h1>
      </div>
      
      <!-- Content -->
      <div style="padding: 20px;">
        <p>${greeting}</p>
        <p>Your payment${paymentRef ? ` (#${paymentRef})` : ''} for booking <strong>#${bookingRef}</strong> has been <strong>approved</strong>.</p>
        <p>You can view the details in your dashboard.</p>
        <p><a href="${dashboardUrl}" style="background:#1d4ed8;color:#fff;padding:10px 16px;border-radius:6px;text-decoration:none;">Open Dashboard</a></p>
        <p>Thank you for choosing Ollie's Rent A Car!</p>
      </div>
    </div>
  `;
  return { subject, html };
}

export function buildPaymentRejectedEmail(params: {
  customerName?: string | null;
  bookingRef: string;
  paymentRef?: string;
  notes?: string | null;
  dashboardUrl?: string;
}): { subject: string; html: string } {
  const { customerName, bookingRef, paymentRef, notes, dashboardUrl = "/customer/dashboard" } = params;
  const subject = "Payment Proof Requires Attention - Ollie's Rent A Car";
  const greeting = customerName ? `Hi ${customerName},` : "Hello,";
  const notesBlock = notes
    ? `<p><strong>Reason:</strong> ${escapeHtml(notes)}</p>`
    : "";
  const html = `
    <div style="font-family: Arial, sans-serif; line-height:1.6; max-width: 600px; margin: 0 auto;">
      <!-- Header with Logo -->
      <div style="text-align: center; padding: 20px; background-color: #f8fafc; border-bottom: 2px solid #e5e7eb;">
        <img src="https://pathlinkio.app/ollie_logo.jpg" alt="Ollie's Rent A Car" style="height: 60px; width: auto; margin-bottom: 10px;">
        <h1 style="color: #1f2937; margin: 0; font-size: 24px;">Ollie's Rent A Car</h1>
      </div>
      
      <!-- Content -->
      <div style="padding: 20px;">
        <p>${greeting}</p>
        <p>We reviewed your payment proof${paymentRef ? ` (#${paymentRef})` : ''} for booking <strong>#${bookingRef}</strong> and it was <strong>rejected</strong>.</p>
        ${notesBlock}
        <p>Please re-upload a valid payment proof from your dashboard.</p>
        <p><a href="${dashboardUrl}" style="background:#b91c1c;color:#fff;padding:10px 16px;border-radius:6px;text-decoration:none;">Re-upload Proof</a></p>
        <p>If you have any questions, reply to this email.</p>
      </div>
    </div>
  `;
  return { subject, html };
}

function escapeHtml(input: string) {
  return input
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/\"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

// Booking decision emails
export function buildBookingConfirmedEmail(params: {
  customerName?: string | null;
  bookingRef: string;
  dashboardUrl?: string;
}): { subject: string; html: string } {
  const { customerName, bookingRef, dashboardUrl = "/customer/dashboard" } = params;
  const subject = "Booking Confirmed - Ollie's Rent A Car";
  const greeting = customerName ? `Hi ${customerName},` : "Hello,";
  const html = `
    <div style="font-family: Arial, sans-serif; line-height:1.6; max-width: 600px; margin: 0 auto;">
      <!-- Header with Logo -->
      <div style="text-align: center; padding: 20px; background-color: #f8fafc; border-bottom: 2px solid #e5e7eb;">
        <img src="https://pathlinkio.app/ollie_logo.jpg" alt="Ollie's Rent A Car" style="height: 60px; width: auto; margin-bottom: 10px;">
        <h1 style="color: #1f2937; margin: 0; font-size: 24px;">Ollie's Rent A Car</h1>
      </div>
      
      <!-- Content -->
      <div style="padding: 20px;">
        <p>${greeting}</p>
        <p>Your booking <strong>#${bookingRef}</strong> has been <strong>confirmed</strong>.</p>
        <p>You can view the booking details and next steps in your dashboard.</p>
        <p><a href="${dashboardUrl}" style="background:#16a34a;color:#fff;padding:10px 16px;border-radius:6px;text-decoration:none;">View Booking</a></p>
        <p>We look forward to serving you!</p>
      </div>
    </div>
  `;
  return { subject, html };
}

// Trip completion email
export function buildTripCompletedEmail(params: {
  customerName?: string | null;
  bookingRef: string;
  dashboardUrl?: string;
}): { subject: string; html: string } {
  const { customerName, bookingRef, dashboardUrl = "/customer/dashboard" } = params;
  const subject = "Trip Completed - Thank You for Choosing Ollie's Rent A Car";
  const greeting = customerName ? `Hi ${customerName},` : "Hello,";
  const html = `
    <div style="font-family: Arial, sans-serif; line-height:1.6; max-width: 600px; margin: 0 auto;">
      <!-- Header with Logo -->
      <div style="text-align: center; padding: 20px; background-color: #f8fafc; border-bottom: 2px solid #e5e7eb;">
        <img src="https://pathlinkio.app/ollie_logo.jpg" alt="Ollie's Rent A Car" style="height: 60px; width: auto; margin-bottom: 10px;">
        <h1 style="color: #1f2937; margin: 0; font-size: 24px;">Ollie's Rent A Car</h1>
      </div>
      
      <!-- Content -->
      <div style="padding: 20px;">
        <p>${greeting}</p>
        <p>Thank you for completing your trip with booking <strong>#${bookingRef}</strong>!</p>
        <p>We hope you had a wonderful experience with our service. Your feedback is valuable to us and helps us continue to improve.</p>
        <p>You can view your completed booking details and history in your dashboard.</p>
        <p><a href="${dashboardUrl}" style="background:#16a34a;color:#fff;padding:10px 16px;border-radius:6px;text-decoration:none;">View Dashboard</a></p>
        <p>Thank you for choosing Ollie's Rent A Car. We look forward to serving you again!</p>
      </div>
    </div>
  `;
  return { subject, html };
}

export function buildBookingDeniedEmail(params: {
  customerName?: string | null;
  bookingRef: string;
  notes?: string | null;
  dashboardUrl?: string;
}): { subject: string; html: string } {
  const { customerName, bookingRef, notes, dashboardUrl = "/customer/dashboard" } = params;
  const subject = "Booking Request Update - Ollie's Rent A Car";
  const greeting = customerName ? `Hi ${customerName},` : "Hello,";
  const notesBlock = notes
    ? `<p><strong>Reason:</strong> ${escapeHtml(notes)}</p>`
    : "";
  const html = `
    <div style="font-family: Arial, sans-serif; line-height:1.6; max-width: 600px; margin: 0 auto;">
      <!-- Header with Logo -->
      <div style="text-align: center; padding: 20px; background-color: #f8fafc; border-bottom: 2px solid #e5e7eb;">
        <img src="https://pathlinkio.app/ollie_logo.jpg" alt="Ollie's Rent A Car" style="height: 60px; width: auto; margin-bottom: 10px;">
        <h1 style="color: #1f2937; margin: 0; font-size: 24px;">Ollie's Rent A Car</h1>
      </div>
      
      <!-- Content -->
      <div style="padding: 20px;">
        <p>${greeting}</p>
        <p>Your booking <strong>#${bookingRef}</strong> was <strong>not approved</strong> at this time.</p>
        ${notesBlock}
        <p>You can review details or create a new booking from your dashboard.</p>
        <p><a href="${dashboardUrl}" style="background:#374151;color:#fff;padding:10px 16px;border-radius:6px;text-decoration:none;">Open Dashboard</a></p>
      </div>
    </div>
  `;
  return { subject, html };
}

// Booking submission confirmation email
export function buildBookingSubmissionEmail(params: {
  customerName?: string | null;
  bookingRef: string;
  vehicleModel: string;
  pickupDate: string;
  returnDate: string;
  pickupLocation: string;
  totalAmount: number;
  dashboardUrl?: string;
}): { subject: string; html: string } {
  const { 
    customerName, 
    bookingRef, 
    vehicleModel,
    pickupDate,
    returnDate,
    pickupLocation,
    totalAmount,
    dashboardUrl = "https://olliesrentalcar.pathlinkio.app/customer/dashboard/" 
  } = params;
  
  const subject = `Booking Request Submitted #${bookingRef} - Ollie's Rent A Car`;
  const greeting = customerName ? `Dear ${customerName},` : "Hello,";
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', { style: 'currency', currency: 'PHP' }).format(amount);
  };
  
  const formattedAmount = formatCurrency(totalAmount);
  
  const html = `
    <div style="font-family: Arial, sans-serif; line-height:1.6; max-width: 600px; margin: 0 auto; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
      <!-- Header with Logo -->
      <div style="text-align: center; padding: 20px; background-color: #f8fafc; border-bottom: 2px solid #e5e7eb;">
        <img src="https://pathlinkio.app/ollie_logo.jpg" alt="Ollie's Rent A Car" style="height: 60px; width: auto; margin-bottom: 10px;">
        <h1 style="color: #1f2937; margin: 0; font-size: 24px;">Ollie's Rent A Car</h1>
      </div>
      
      <!-- Booking Status Header -->
      <div style="background-color: #dbeafe; padding: 16px; border-bottom: 1px solid #e5e7eb;">
        <h2 style="margin: 0; color: #1e3a8a; font-size: 18px;">Booking Request Submitted</h2>
        <div style="margin-top: 8px;">
          <div style="font-size: 14px;"><strong>Booking ID:</strong> #${bookingRef}</div>
        </div>
        <div style="display: inline-block; background-color: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin-top: 8px;">Pending Review</div>
      </div>
      
      <!-- Message -->
      <div style="padding: 16px; border-bottom: 1px solid #e5e7eb;">
        <p>${greeting}</p>
        <p>Thank you for submitting your booking request! We have received your request and it is now being reviewed by our team.</p>
        <p><strong>What happens next:</strong></p>
        <ul style="margin: 12px 0; padding-left: 20px;">
          <li>Our team will review your documents and payment proof</li>
          <li>You will receive an email confirmation once your booking is approved</li>
          <li>If any documents need resubmission, we'll notify you with specific instructions</li>
        </ul>
      </div>
      
      <!-- Booking Summary -->
      <div style="padding: 16px; background-color: #f0f9ff; border-bottom: 1px solid #e5e7eb;">
        <h3 style="margin: 0 0 12px 0; color: #1e3a8a; font-size: 16px;">Booking Summary</h3>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <div style="font-size: 14px; color: #4b5563;">Vehicle:</div>
          <div style="font-size: 14px; font-weight: bold;">${vehicleModel}</div>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <div style="font-size: 14px; color: #4b5563;">Pickup Date:</div>
          <div style="font-size: 14px;">${pickupDate}</div>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <div style="font-size: 14px; color: #4b5563;">Return Date:</div>
          <div style="font-size: 14px;">${returnDate}</div>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <div style="font-size: 14px; color: #4b5563;">Pickup Location:</div>
          <div style="font-size: 14px;">${pickupLocation}</div>
        </div>
        <div style="border-top: 1px solid #e5e7eb; margin: 12px 0; padding-top: 12px;">
          <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 16px;">
            <div>Total Amount:</div>
            <div>${formattedAmount}</div>
          </div>
        </div>
      </div>
      
      <!-- Footer -->
      <div style="padding: 16px; background-color: #f3f4f6; text-align: center; font-size: 12px; color: #6b7280;">
        <p>You can track your booking status and manage your reservation from your dashboard.</p>
        <p><a href="${dashboardUrl}" style="background:#1d4ed8;color:#fff;padding:8px 12px;border-radius:4px;text-decoration:none;display:inline-block;margin-top:8px;">View My Bookings</a></p>
        <p style="margin-top: 12px;">If you have any questions, please contact our support team.</p>
      </div>
    </div>
  `;
  
  return { subject, html };
}

// Admin account creation notification email
export async function sendAdminAccountCreationEmail(params: {
  adminEmail: string;
  adminName?: string | null;
  temporaryPassword: string;
  createdBy?: string;
}): Promise<SendEmailResult> {
  const { adminEmail, adminName, temporaryPassword, createdBy } = params;
  
  const { subject, html } = buildAdminAccountCreationEmail({
    adminName,
    adminEmail,
    temporaryPassword,
    createdBy
  });

  return sendEmail({
    to: adminEmail,
    subject,
    html
  });
}

// Booking receipt email with detailed booking information
export function buildBookingReceiptEmail(params: {
  customerName?: string | null;
  bookingRef: string;
  bookingDate: string;
  vehicleModel: string;
  plateNumber: string;
  pickupDate: string;
  returnDate: string;
  duration: number;
  pickupLocation: string;
  baseRate: number;
  totalAmount: number;
  dailyRate: number;
  vehicleId?: string;
  bookingChannel?: string;
  dashboardUrl?: string;
}): { subject: string; html: string } {
  const { 
    customerName, 
    bookingRef, 
    bookingDate,
    vehicleModel,
    plateNumber,
    pickupDate,
    returnDate,
    duration,
    pickupLocation,
    baseRate,
    totalAmount,
    dailyRate,
    vehicleId = "",
    bookingChannel = "Web",
    dashboardUrl = "https://olliesrentalcar.pathlinkio.app/customer/dashboard" 
  } = params;
  
  const subject = `Booking Receipt #${bookingRef} - Ollie's Rent A Car`;
  const greeting = customerName ? `Dear ${customerName},` : "Hello,";
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', { style: 'currency', currency: 'PHP' }).format(amount);
  };
  
  const formattedBaseRate = formatCurrency(baseRate);
  const formattedTotalAmount = formatCurrency(totalAmount);
  const formattedDailyRate = formatCurrency(dailyRate);
  
  const html = `
    <div style="font-family: Arial, sans-serif; line-height:1.6; max-width: 600px; margin: 0 auto;">
      <!-- Header with Logo -->
      <div style="text-align: center; padding: 20px; background-color: #ffffff;">
        <img src="https://pathlinkio.app/ollie_logo.jpg" alt="Ollie's Rent A Car" style="height: 60px; width: auto; margin-bottom: 10px;">
        <h1 style="color: #1f2937; margin: 0; font-size: 24px;">Ollie's Rent A Car</h1>
      </div>
      
      <!-- Booking Confirmed Header (matching booking request design) -->
      <div style="background-color: #dbeafe; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="margin: 0 0 12px 0; color: #1e40af; font-size: 20px; font-weight: bold;">Booking Confirmed</h2>
        <div style="font-size: 16px; color: #1f2937; margin-bottom: 12px;">
          <strong>Booking ID:</strong> #${bookingRef}
        </div>
        <div style="display: inline-block;">
          <span style="background-color: #16a34a; color: white; padding: 6px 12px; border-radius: 4px; font-size: 14px; font-weight: bold;">Confirmed</span>
        </div>
      </div>
      
      <!-- Main Content -->
      <div style="padding: 0 20px;">
        <p style="font-size: 16px; color: #1f2937; margin-bottom: 20px;">
          ${greeting}
        </p>
        <p style="font-size: 16px; color: #1f2937; margin-bottom: 8px;">
          Your booking has been confirmed! Here are the details:
        </p>
        
        <!-- Booking Summary (matching booking request design) -->
        <div style="background-color: #dbeafe; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin: 0 0 16px 0; color: #1e40af; font-size: 18px; font-weight: bold;">Booking Summary</h3>
          
          <div style="margin-bottom: 12px;">
            <strong style="color: #1f2937;">Vehicle:</strong> ${vehicleModel}
          </div>
          
          <div style="margin-bottom: 12px;">
            <strong style="color: #1f2937;">Pickup Date:</strong> ${pickupDate}
          </div>
          
          <div style="margin-bottom: 12px;">
            <strong style="color: #1f2937;">Return Date:</strong> ${returnDate}
          </div>
          
          <div style="margin-bottom: 12px;">
            <strong style="color: #1f2937;">Pickup Location:</strong> ${pickupLocation}
          </div>
          
          <div style="border-top: 2px solid #3b82f6; padding-top: 12px; margin-top: 16px;">
            <div style="font-size: 18px; font-weight: bold; color: #1f2937;">
              Total Amount: ${formattedTotalAmount}
            </div>
          </div>
        </div>
        
        <p style="font-size: 16px; color: #6b7280; text-align: center; margin: 30px 0;">
          You can track your booking status and manage your reservation from your dashboard.
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${dashboardUrl}" style="background-color: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-size: 16px; font-weight: bold; display: inline-block;">View Booking Details</a>
        </div>
      </div>
    </div>
  `;
  
  return { subject, html };
}
