"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { DayNightSelector, CompactDayNightSelector } from "@/components/ui/day-night-selector";
import { TimePicker } from "@/components/customer-side/time";
import { DatePicker } from "@/components/customer-side/date";
import { PublicAppShell } from "@/components/layout/public-app-shell";
import { getTimePeriod, type TimePeriod } from "@/lib/delivery-fee-constants";
import { CheckCircle, XCircle, Clock, Calendar } from "lucide-react";

interface TestState {
  pickupTime: string;
  dropoffTime: string;
  pickupTimePeriod: TimePeriod;
  returnTimePeriod: TimePeriod;
  pickupDate: string;
  dropoffDate: string;
}

export default function TestDayNightAutoFillPage() {
  const [testState, setTestState] = React.useState<TestState>({
    pickupTime: "",
    dropoffTime: "",
    pickupTimePeriod: "day",
    returnTimePeriod: "day",
    pickupDate: "",
    dropoffDate: "",
  });

  const [testResults, setTestResults] = React.useState<Record<string, boolean>>({});

  // Test scenarios
  const runTest = (testName: string, condition: boolean) => {
    setTestResults(prev => ({ ...prev, [testName]: condition }));
  };

  // Auto-fill handlers
  const handlePickupTimeAutoFill = (time: string) => {
    setTestState(prev => ({ ...prev, pickupTime: time }));
  };

  const handleDropoffTimeAutoFill = (time: string) => {
    setTestState(prev => ({ ...prev, dropoffTime: time }));
  };

  // Period change handlers
  const handlePickupPeriodChange = (period: TimePeriod) => {
    setTestState(prev => ({ ...prev, pickupTimePeriod: period }));
  };

  const handleReturnPeriodChange = (period: TimePeriod) => {
    setTestState(prev => ({ ...prev, returnTimePeriod: period }));
  };

  // Manual time change handlers
  const handlePickupTimeChange = (time: string) => {
    setTestState(prev => ({ ...prev, pickupTime: time }));
  };

  const handleDropoffTimeChange = (time: string) => {
    setTestState(prev => ({ ...prev, dropoffTime: time }));
  };

  // Run verification tests
  const runVerificationTests = () => {
    // Test 1: Day button auto-fills 10:00 AM
    runTest("day_autofill_pickup", testState.pickupTime === "10:00" && testState.pickupTimePeriod === "day");
    
    // Test 2: Night button auto-fills 6:00 PM  
    runTest("night_autofill_dropoff", testState.dropoffTime === "18:00" && testState.returnTimePeriod === "night");
    
    // Test 3: Manual override works
    const manualPickupTime = "14:30";
    const manualDetectedPeriod = getTimePeriod(manualPickupTime);
    runTest("manual_override", testState.pickupTime !== "10:00" && testState.pickupTime !== "18:00");
    
    // Test 4: Time period detection works
    runTest("time_period_detection", getTimePeriod(testState.pickupTime) === testState.pickupTimePeriod);
    
    // Test 5: UI shows auto-fill indicators
    const isPickupAutoFilled = testState.pickupTime === "10:00" || testState.pickupTime === "18:00";
    const isDropoffAutoFilled = testState.dropoffTime === "10:00" || testState.dropoffTime === "18:00";
    runTest("autofill_indicators", isPickupAutoFilled || isDropoffAutoFilled);
  };

  const resetTest = () => {
    setTestState({
      pickupTime: "",
      dropoffTime: "",
      pickupTimePeriod: "day",
      returnTimePeriod: "day",
      pickupDate: "",
      dropoffDate: "",
    });
    setTestResults({});
  };

  const TestResultIcon = ({ passed }: { passed: boolean | undefined }) => {
    if (passed === undefined) return <Clock className="h-4 w-4 text-gray-400" />;
    return passed ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <XCircle className="h-4 w-4 text-red-600" />
    );
  };

  return (
    <PublicAppShell>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 space-y-8">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Day/Night Auto-Fill Test Page
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              Test the enhanced Day/Night button functionality with auto-fill capabilities
            </p>
            <div className="flex justify-center gap-4">
              <Button onClick={runVerificationTests} className="bg-blue-600 hover:bg-blue-700">
                Run Verification Tests
              </Button>
              <Button onClick={resetTest} variant="secondary">
                Reset Test
              </Button>
            </div>
          </div>

          {/* Test Results */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-blue-600" />
                Verification Checklist
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <TestResultIcon passed={testResults.day_autofill_pickup} />
                  <span>Day button auto-fills 10:00 AM for pickup time</span>
                </div>
                <div className="flex items-center gap-3">
                  <TestResultIcon passed={testResults.night_autofill_dropoff} />
                  <span>Night button auto-fills 6:00 PM for dropoff time</span>
                </div>
                <div className="flex items-center gap-3">
                  <TestResultIcon passed={testResults.manual_override} />
                  <span>Manual time selection overrides auto-fill</span>
                </div>
                <div className="flex items-center gap-3">
                  <TestResultIcon passed={testResults.time_period_detection} />
                  <span>Time period detection works correctly</span>
                </div>
                <div className="flex items-center gap-3">
                  <TestResultIcon passed={testResults.autofill_indicators} />
                  <span>UI shows auto-fill indicators when appropriate</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Full Day/Night Selector Test */}
          <Card>
            <CardHeader>
              <CardTitle>Full Day/Night Selector (Step 1 Style)</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Pickup */}
                <div className="space-y-4">
                  <Label className="text-sm font-medium">Pickup Time</Label>
                  <TimePicker
                    value={testState.pickupTime}
                    onChange={handlePickupTimeChange}
                    placeholder="Select time"
                    className="w-full"
                  />
                  <DayNightSelector
                    value={testState.pickupTimePeriod}
                    onChange={handlePickupPeriodChange}
                    onTimeAutoFill={handlePickupTimeAutoFill}
                    currentTime={testState.pickupTime}
                    enableAutoFill={true}
                    size="sm"
                    label="Pickup Time Category"
                  />
                </div>

                {/* Dropoff */}
                <div className="space-y-4">
                  <Label className="text-sm font-medium">Dropoff Time</Label>
                  <TimePicker
                    value={testState.dropoffTime}
                    onChange={handleDropoffTimeChange}
                    placeholder="Select time"
                    className="w-full"
                  />
                  <DayNightSelector
                    value={testState.returnTimePeriod}
                    onChange={handleReturnPeriodChange}
                    onTimeAutoFill={handleDropoffTimeAutoFill}
                    currentTime={testState.dropoffTime}
                    enableAutoFill={true}
                    size="sm"
                    label="Return Time Category"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Compact Day/Night Selector Test */}
          <Card>
            <CardHeader>
              <CardTitle>Compact Day/Night Selector (Quick Booking Style)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Pickup Date & Time */}
                <div className="space-y-4">
                  <Label>Pick-up Date & Time</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <DatePicker
                      value={testState.pickupDate}
                      onChange={(date) => setTestState(prev => ({ ...prev, pickupDate: date }))}
                      placeholder="Select date"
                    />
                    <TimePicker
                      value={testState.pickupTime}
                      onChange={handlePickupTimeChange}
                      placeholder="Select time"
                    />
                  </div>
                  <CompactDayNightSelector
                    value={testState.pickupTimePeriod}
                    onChange={handlePickupPeriodChange}
                    onTimeAutoFill={handlePickupTimeAutoFill}
                    currentTime={testState.pickupTime}
                    enableAutoFill={true}
                    className="justify-start"
                  />
                </div>

                {/* Dropoff Date & Time */}
                <div className="space-y-4">
                  <Label>Drop-off Date & Time</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <DatePicker
                      value={testState.dropoffDate}
                      onChange={(date) => setTestState(prev => ({ ...prev, dropoffDate: date }))}
                      placeholder="Select date"
                    />
                    <TimePicker
                      value={testState.dropoffTime}
                      onChange={handleDropoffTimeChange}
                      placeholder="Select time"
                    />
                  </div>
                  <CompactDayNightSelector
                    value={testState.returnTimePeriod}
                    onChange={handleReturnPeriodChange}
                    onTimeAutoFill={handleDropoffTimeAutoFill}
                    currentTime={testState.dropoffTime}
                    enableAutoFill={true}
                    className="justify-start"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current State Display */}
          <Card>
            <CardHeader>
              <CardTitle>Current Test State</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Pickup Time:</strong> {testState.pickupTime || "Not set"}
                </div>
                <div>
                  <strong>Dropoff Time:</strong> {testState.dropoffTime || "Not set"}
                </div>
                <div>
                  <strong>Pickup Period:</strong> {testState.pickupTimePeriod}
                </div>
                <div>
                  <strong>Return Period:</strong> {testState.returnTimePeriod}
                </div>
                <div>
                  <strong>Auto-filled Pickup:</strong> {testState.pickupTime === "10:00" || testState.pickupTime === "18:00" ? "Yes" : "No"}
                </div>
                <div>
                  <strong>Auto-filled Dropoff:</strong> {testState.dropoffTime === "10:00" || testState.dropoffTime === "18:00" ? "Yes" : "No"}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Testing Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Testing Instructions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm">
                <div>
                  <h4 className="font-semibold mb-2">Test Scenarios:</h4>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>Click the "Day" button and verify it auto-fills time to 10:00 AM</li>
                    <li>Click the "Night" button and verify it auto-fills time to 6:00 PM</li>
                    <li>Manually select a different time and verify it overrides the auto-fill</li>
                    <li>Check that the time period detection updates correctly</li>
                    <li>Verify that auto-fill indicators appear when appropriate</li>
                    <li>Test both full and compact selector styles</li>
                    <li>Verify consistency between Step 1 and Quick Booking styles</li>
                  </ol>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Expected Behavior:</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Day button should set time to 10:00 (10:00 AM)</li>
                    <li>Night button should set time to 18:00 (6:00 PM)</li>
                    <li>Manual time selection should work normally after auto-fill</li>
                    <li>Time period should auto-detect based on selected time</li>
                    <li>UI should show "Auto-filled" indicators when times are auto-set</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PublicAppShell>
  );
}
