-- ============================================
-- CREATE MISSING GPS LOCATIONS TABLE
-- ============================================
-- This table is required by the GPS tracker and admin GPS devices functionality
-- Error: relation "gps_locations" does not exist

-- Create the GPS locations table
CREATE TABLE IF NOT EXISTS public.gps_locations (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  car_id UUID NOT NULL REFERENCES public.cars(id) ON DELETE CASCADE,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  speed DECIMAL(5,2) DEFAULT 0, -- km/h
  heading DECIMAL(5,2) DEFAULT 0, -- degrees
  status TEXT NOT NULL DEFAULT 'offline' CHECK (status IN ('active', 'idle', 'offline')),
  driver_id UUID REFERENCES public.profiles(id),
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_gps_car ON public.gps_locations(car_id);
CREATE INDEX IF NOT EXISTS idx_gps_timestamp ON public.gps_locations(timestamp);
CREATE INDEX IF NOT EXISTS idx_gps_status ON public.gps_locations(status);

-- Enable RLS for GPS locations
ALTER TABLE public.gps_locations ENABLE ROW LEVEL SECURITY;

-- GPS Locations RLS Policies
DO $$
BEGIN
  -- Policy for SELECT (viewing GPS locations)
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'gps_locations' 
    AND policyname = 'Only admins can view GPS locations'
  ) THEN
    CREATE POLICY "Only admins can view GPS locations" 
      ON public.gps_locations FOR SELECT 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM profiles 
          WHERE profiles.id = auth.uid() 
          AND profiles.role IN ('admin', 'super_admin')
        )
      );
  END IF;

  -- Policy for INSERT (adding GPS locations)
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'gps_locations' 
    AND policyname = 'Only admins can insert GPS locations'
  ) THEN
    CREATE POLICY "Only admins can insert GPS locations" 
      ON public.gps_locations FOR INSERT 
      TO authenticated
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM profiles 
          WHERE profiles.id = auth.uid() 
          AND profiles.role IN ('admin', 'super_admin')
        )
      );
  END IF;

  -- Policy for UPDATE (updating GPS locations)
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'gps_locations' 
    AND policyname = 'Only admins can update GPS locations'
  ) THEN
    CREATE POLICY "Only admins can update GPS locations" 
      ON public.gps_locations FOR UPDATE 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM profiles 
          WHERE profiles.id = auth.uid() 
          AND profiles.role IN ('admin', 'super_admin')
        )
      );
  END IF;
END $$;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.gps_locations TO service_role;
GRANT SELECT ON public.gps_locations TO authenticated;

-- Enable realtime for GPS locations (if not already enabled)
DO $$
BEGIN
  -- Add table to realtime publication if not already added
  IF NOT EXISTS (
    SELECT 1 FROM pg_publication_tables 
    WHERE pubname = 'supabase_realtime' 
    AND schemaname = 'public' 
    AND tablename = 'gps_locations'
  ) THEN
    ALTER PUBLICATION supabase_realtime ADD TABLE public.gps_locations;
  END IF;
EXCEPTION 
  WHEN duplicate_object THEN 
    NULL; -- Table already in publication
END $$;

-- Verify the table was created successfully
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'gps_locations'
  ) THEN
    RAISE NOTICE '✅ gps_locations table created successfully';
    RAISE NOTICE 'ℹ️  GPS tracker and admin GPS devices pages should now work';
  ELSE
    RAISE NOTICE '❌ Failed to create gps_locations table';
  END IF;
END $$;

-- Add the encrypted_token column to store ESP32 encrypted tokens
ALTER TABLE public.gps_locations 
ADD COLUMN IF NOT EXISTS encrypted_token text;

-- Add an index for efficient lookups
CREATE INDEX IF NOT EXISTS idx_gps_encrypted_token 
ON public.gps_locations USING btree (encrypted_token);

-- Comment for documentation
COMMENT ON TABLE public.gps_locations IS 'Stores real-time GPS location data from tracking devices';
COMMENT ON COLUMN public.gps_locations.car_id IS 'Reference to the car being tracked';
COMMENT ON COLUMN public.gps_locations.latitude IS 'GPS latitude coordinate (decimal degrees)';
COMMENT ON COLUMN public.gps_locations.longitude IS 'GPS longitude coordinate (decimal degrees)';
COMMENT ON COLUMN public.gps_locations.speed IS 'Vehicle speed in km/h';
COMMENT ON COLUMN public.gps_locations.heading IS 'Vehicle heading in degrees (0-360)';
COMMENT ON COLUMN public.gps_locations.status IS 'Vehicle status: active, idle, or offline';
COMMENT ON COLUMN public.gps_locations.driver_id IS 'Optional reference to the driver profile';
COMMENT ON COLUMN public.gps_locations.timestamp IS 'When the GPS reading was taken';
COMMENT ON COLUMN public.gps_locations.created_at IS 'When the record was inserted into database';
COMMENT ON COLUMN public.gps_locations.encrypted_token IS 'ESP32 encrypted token containing coordinates and device ID for this GPS reading';
