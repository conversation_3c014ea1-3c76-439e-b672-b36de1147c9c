"use client"

import * as React from "react"
import { createClient } from "@/lib/supabase/client"
import { PublicAppShell } from "@/components/layout/public-app-shell"
import { useCustomerAuth } from "@/components/auth/customer-auth-context"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { User, Mail, Phone, Bell, Shield, Save, Check } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { LegalDocumentsSection } from "@/components/customer-side/dashboard/legal-documents-section"
import { DocumentRequestsSection } from "@/components/customer-side/dashboard/document-requests-section"
import { CustomerSettingsSkeleton } from "@/components/customer-side/loading/skeleton-components"

export default function SettingsPage() {
  const { user, fetchUserProfile } = useCustomerAuth()
  const [firstName, setFirstName] = React.useState("")
  const [middleInitial, setMiddleInitial] = React.useState("")
  const [lastName, setLastName] = React.useState("")
  const [email, setEmail] = React.useState("")
  const [phone, setPhone] = React.useState("")
  const [notifBooking, setNotifBooking] = React.useState(true)
  const [notifPayment, setNotifPayment] = React.useState(true)
  const [notifMarketing, setNotifMarketing] = React.useState(false)
  const [saving, setSaving] = React.useState(false)
  const [saved, setSaved] = React.useState(false)
  const [loading, setLoading] = React.useState(true)

  // Helper function to parse full name into components
  const parseFullName = (fullName: string) => {
    if (!fullName) return { firstName: "", middleInitial: "", lastName: "" };
    
    const parts = fullName.trim().split(/\s+/);
    if (parts.length === 1) {
      return { firstName: parts[0], middleInitial: "", lastName: "" };
    } else if (parts.length === 2) {
      return { firstName: parts[0], middleInitial: "", lastName: parts[1] };
    } else if (parts.length >= 3) {
      // If middle part is single character or has a dot, treat as middle initial
      const middlePart = parts[1];
      if (middlePart.length === 1 || middlePart.endsWith('.')) {
        return { 
          firstName: parts[0], 
          middleInitial: middlePart.replace('.', ''), 
          lastName: parts.slice(2).join(' ') 
        };
      } else {
        // Treat as multiple first names or compound last name
        return { 
          firstName: parts[0], 
          middleInitial: "", 
          lastName: parts.slice(1).join(' ') 
        };
      }
    }
    return { firstName: "", middleInitial: "", lastName: "" };
  };

  React.useEffect(() => {
    async function loadSettings() {
      setLoading(true)
      
      if (user) {
        setEmail(user.email ?? "")
        // Load profile data from Supabase
        const supabase = createClient()
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single()
          
        if (data) {
          // Parse full name into separate fields
          const { firstName: fName, middleInitial: mInitial, lastName: lName } = parseFullName(data.full_name || "");
          setFirstName(fName);
          setMiddleInitial(mInitial);
          setLastName(lName);
          setPhone(data.phone || "")
        }
        if(error) {
          console.warn('Error fetching profile:', error)
        }
      }

      // Load notification preferences from localStorage
      if (typeof window !== "undefined") {
        const prefs = localStorage.getItem("ollie_prefs")
        if (prefs) {
          try {
            const parsed = JSON.parse(prefs)
            setNotifBooking(parsed.notifBooking ?? true)
            setNotifPayment(parsed.notifPayment ?? true)
            setNotifMarketing(parsed.notifMarketing ?? false)
          } catch (e) {
            console.warn("Failed to parse preferences from localStorage")
          }
        }
      }
      
      setLoading(false)
    }

    loadSettings()
  }, [user])

  // Helper function to construct full name from components
  const constructFullName = (first: string, middle: string, last: string) => {
    const parts = [first.trim()];
    if (middle.trim()) {
      parts.push(middle.trim());
    }
    if (last.trim()) {
      parts.push(last.trim());
    }
    return parts.join(' ');
  };

  async function onSave(e: React.FormEvent) {
    e.preventDefault()
    if (!user) return

    setSaving(true)
    const supabase = createClient()

    // 1. Update Supabase Auth user (if email has changed)
    if (email !== user.email) {
      const { error: authError } = await supabase.auth.updateUser({ email })
      if (authError) {
        console.error('Error updating auth email:', authError)
        // Handle error display to user
      }
    }

    // 2. Construct full name from separate fields
    const fullName = constructFullName(firstName, middleInitial, lastName);

    // 3. Update public.profiles table
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log('Updating profile for user:', user.id);
    }
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log('Update data:', {
        full_name: fullName,
        first_name: firstName.trim() || null,
        middle_initial: middleInitial.trim() || null,
        last_name: lastName.trim() || null,
        phone: phone
      });
    }

    const { data, error: profileError } = await supabase
      .from('profiles')
      .update({ 
        full_name: fullName, 
        first_name: firstName.trim() || null,
        middle_initial: middleInitial.trim() || null,
        last_name: lastName.trim() || null,
        phone: phone, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', user.id)
      .select()

    if (profileError) {
      console.error('Error updating profile:', profileError)
      alert(`Error updating profile: ${profileError.message}`)
    } else {
      if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
        console.log('Profile updated successfully:', data);
      }
    }

    // 3. Save notification preferences to localStorage
    if (typeof window !== "undefined") {
      localStorage.setItem(
        "ollie_prefs",
        JSON.stringify({ notifBooking, notifPayment, notifMarketing })
      )
    }

    // 4. Refresh the user profile in the auth context
    if (fetchUserProfile) {
        await fetchUserProfile();
    }

    setSaving(false)
    if (!profileError) {
      setSaved(true)
      setTimeout(() => setSaved(false), 3000)
    }
  }

  if (!user) {
    return (
      <PublicAppShell>
        <div className="max-w-4xl mx-auto px-4 py-12">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600 mb-6">Please sign in to access your settings.</p>
            <Link href="/customer/login">
              <Button>Sign In</Button>
            </Link>
          </div>
        </div>
      </PublicAppShell>
    )
  }

  if (loading) {
    return (
      <PublicAppShell>
        <CustomerSettingsSkeleton />
      </PublicAppShell>
    )
  }

  return (
    <PublicAppShell>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Account Settings</h1>
          <p className="text-gray-600">Manage your account information and preferences.</p>
        </div>

        {/* Success Message */}
        {saved && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-3">
            <Check className="h-5 w-5 text-green-600" />
            <span className="text-green-700 font-medium">Settings saved successfully!</span>
          </div>
        )}

        <div className="grid gap-8">
          {/* Document Requests Section */}
          <DocumentRequestsSection onNavigateToDocuments={() => {
            const legalDocsSection = document.getElementById('legal-documents-section');
            if (legalDocsSection) {
              legalDocsSection.scrollIntoView({ behavior: 'smooth' });
            }
          }} />

          {/* Profile Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                Profile Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={onSave} className="space-y-6">
                {/* Profile Picture Section */}
                <div className="flex items-center gap-6 pb-6 border-b border-gray-200">
                  <div className="relative">
                    <Image
                      src="/avatar.svg"
                      alt="Profile picture"
                      width={80}
                      height={80}
                      className="rounded-full bg-gray-100 p-2 border-2 border-gray-200"
                    />
                    <Badge className="absolute -bottom-1 -right-1 bg-blue-100 text-blue-700 border-blue-200 text-xs">
                      {user.role}
                    </Badge>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-2">Profile Picture</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      Your profile uses our standard avatar design for consistency across the platform.
                    </p>
                    <div className="inline-flex items-center gap-2 text-xs text-blue-600 bg-blue-50 px-3 py-2 rounded-full">
                      <Shield className="h-3 w-3" />
                      Standard avatar applied automatically
                    </div>
                  </div>
                </div>

                {/* Form Fields */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="firstName" className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      First Name
                    </Label>
                    <Input 
                      id="firstName" 
                      value={firstName} 
                      onChange={(e) => setFirstName(e.target.value)}
                      className="h-11"
                      placeholder="Enter your first name"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="middleInitial" className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      Middle Initial <span className="text-gray-400 text-sm">(Optional)</span>
                    </Label>
                    <Input 
                      id="middleInitial" 
                      value={middleInitial} 
                      onChange={(e) => setMiddleInitial(e.target.value.toUpperCase().slice(0, 1))}
                      className="h-11"
                      placeholder="M"
                      maxLength={1}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName" className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      Last Name
                    </Label>
                    <Input 
                      id="lastName" 
                      value={lastName} 
                      onChange={(e) => setLastName(e.target.value)}
                      className="h-11"
                      placeholder="Enter your last name"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="email" className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-gray-500" />
                      Email Address
                    </Label>
                    <Input 
                      id="email" 
                      type="email" 
                      value={email} 
                      onChange={(e) => setEmail(e.target.value)}
                      className="h-11"
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="phone" className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      Phone Number (max 11 digits)
                    </Label>
                    <Input 
                      id="phone" 
                      type="tel"
                      value={phone} 
                      onChange={(e) => setPhone(e.target.value)} 
                      placeholder="Enter your phone number (e.g., 09123456789)"
                      maxLength={11}
                      className="h-11"
                    />
                  </div>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button 
                    type="submit" 
                    className="bg-blue-600 hover:bg-blue-700" 
                    disabled={saving}
                  >
                    {saving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </Button>
                  <Link href="/customer/dashboard">
                    <Button variant="secondary">Cancel</Button>
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Notification Preferences */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-green-600" />
                Notification Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">Booking Updates</h4>
                  <p className="text-sm text-gray-600">Get notified about booking confirmations, changes, and reminders</p>
                </div>
                <Switch 
                  checked={notifBooking} 
                  onCheckedChange={setNotifBooking}
                  className="ml-4"
                />
              </div>
              
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">Payment Notifications</h4>
                  <p className="text-sm text-gray-600">Receive alerts about payment status, receipts, and billing</p>
                </div>
                <Switch 
                  checked={notifPayment} 
                  onCheckedChange={setNotifPayment}
                  className="ml-4"
                />
              </div>
              
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">Marketing & Promotions</h4>
                  <p className="text-sm text-gray-600">Stay updated on special offers, new vehicles, and exclusive deals</p>
                </div>
                <Switch 
                  checked={notifMarketing} 
                  onCheckedChange={setNotifMarketing}
                  className="ml-4"
                />
              </div>
            </CardContent>
          </Card>

          {/* Legal Documents Section */}
          <LegalDocumentsSection />

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Link href="/customer/dashboard">
                  <Button className="w-full justify-start" variant="secondary">
                    <User className="h-4 w-4 mr-2" />
                    Back to Dashboard
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button className="w-full justify-start" variant="secondary">
                    <Mail className="h-4 w-4 mr-2" />
                    Contact Support
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PublicAppShell>
  )
}
