'use server'

import { createContextClient } from '@/lib/supabase/server'
import { revalidatePath } from 'next/cache'
import type {
  OrganizationSettings,
  Branch,
  PricingSettings,
  BookingPolicySettings,
  PaymentSettings,
  VehicleSettings,
  MaintenanceSettings,
  NotificationSettings,
  IntegrationSettings,
  SecuritySettings,
  ThemeSettings,
  AuditLogEntry,
  SettingsSection
} from '@/lib/types'

const createAuditLog = async (
  section: SettingsSection,
  action: string,
  fieldName?: string,
  oldValue?: any,
  newValue?: any
) => {
  try {
    const supabase = await createContextClient('admin')
    const { data: { user } } = await supabase.auth.getUser()
    
    if (user) {
      await supabase
        .from('audit_logs')
        .insert({
          user_id: user.id,
          user_email: user.email,
          action,
          section,
          field_name: fieldName,
          old_value: oldValue,
          new_value: newValue,
          timestamp: new Date().toISOString()
        })
    }
  } catch (error) {
    console.error('Failed to create audit log:', error)
  }
}

// Organization Settings
export async function getOrganizationSettings(): Promise<OrganizationSettings | null> {
  try {
    const supabase = await createContextClient('admin')
    const { data, error } = await supabase
      .from('organization_settings')
      .select('*')
      .limit(1)
      .maybeSingle()

    if (error) throw error
    return data || getDefaultOrganizationSettings()
  } catch (error) {
    console.error('Failed to fetch organization settings:', error)
    return getDefaultOrganizationSettings()
  }
}

export async function updateOrganizationSettings(settings: Partial<OrganizationSettings>): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createContextClient('admin')
    const existing = await getOrganizationSettings()
    
    const { data, error } = await supabase
      .from('organization_settings')
      .upsert({
        id: existing?.id || '1',
        ...settings,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error

    // Log changes
    if (existing) {
      for (const [key, value] of Object.entries(settings)) {
        if (existing[key as keyof OrganizationSettings] !== value) {
          await createAuditLog(
            'organization',
            'update',
            key,
            existing[key as keyof OrganizationSettings],
            value
          )
        }
      }
    } else {
      await createAuditLog('organization', 'create')
    }

    revalidatePath('/admin/settings')
    return { success: true }
  } catch (error) {
    console.error('Failed to update organization settings:', error)
    return { success: false, error: (error as Error).message }
  }
}

// Branch Management
export async function getBranches(): Promise<Branch[]> {
  try {
    const supabase = await createContextClient('admin')
    const { data, error } = await supabase
      .from('branches')
      .select('*')
      .order('created_at')

    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Failed to fetch branches:', error)
    return []
  }
}

export async function createBranch(branch: Omit<Branch, 'id' | 'created_at' | 'updated_at'>): Promise<{ success: boolean; error?: string; data?: Branch }> {
  try {
    const supabase = await createContextClient('admin')
    const { data, error } = await supabase
      .from('branches')
      .insert(branch)
      .select()
      .single()

    if (error) throw error

    await createAuditLog('branches', 'create', 'branch', null, data.name)
    revalidatePath('/admin/settings')
    return { success: true, data }
  } catch (error) {
    console.error('Failed to create branch:', error)
    return { success: false, error: (error as Error).message }
  }
}

export async function updateBranch(id: string, updates: Partial<Branch>): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createContextClient('admin')
    const { data: existing } = await supabase
      .from('branches')
      .select('*')
      .eq('id', id)
      .single()

    const { data, error } = await supabase
      .from('branches')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error

    // Log changes
    if (existing) {
      for (const [key, value] of Object.entries(updates)) {
        if (existing[key as keyof Branch] !== value) {
          await createAuditLog(
            'branches',
            'update',
            key,
            existing[key as keyof Branch],
            value
          )
        }
      }
    }

    revalidatePath('/admin/settings')
    return { success: true }
  } catch (error) {
    console.error('Failed to update branch:', error)
    return { success: false, error: (error as Error).message }
  }
}

export async function deleteBranch(id: string): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createContextClient('admin')
    const { data: existing } = await supabase
      .from('branches')
      .select('name')
      .eq('id', id)
      .single()

    const { error } = await supabase
      .from('branches')
      .delete()
      .eq('id', id)

    if (error) throw error

    await createAuditLog('branches', 'delete', 'branch', existing?.name, null)
    revalidatePath('/admin/settings')
    return { success: true }
  } catch (error) {
    console.error('Failed to delete branch:', error)
    return { success: false, error: (error as Error).message }
  }
}

// Pricing Settings
export async function getPricingSettings(): Promise<PricingSettings | null> {
  try {
    const supabase = await createContextClient('admin')
    const { data, error } = await supabase
      .from('pricing_settings')
      .select('*')
      .limit(1)
      .maybeSingle()

    if (error) throw error
    return data || getDefaultPricingSettings()
  } catch (error) {
    console.error('Failed to fetch pricing settings:', error)
    return getDefaultPricingSettings()
  }
}

export async function updatePricingSettings(settings: Partial<PricingSettings>): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createContextClient('admin')
    const existing = await getPricingSettings()
    
    const { data, error } = await supabase
      .from('pricing_settings')
      .upsert({
        id: existing?.id || '1',
        ...settings,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error

    // Log changes
    if (existing) {
      for (const [key, value] of Object.entries(settings)) {
        if (JSON.stringify(existing[key as keyof PricingSettings]) !== JSON.stringify(value)) {
          await createAuditLog(
            'pricing',
            'update',
            key,
            existing[key as keyof PricingSettings],
            value
          )
        }
      }
    } else {
      await createAuditLog('pricing', 'create')
    }

    revalidatePath('/admin/settings')
    return { success: true }
  } catch (error) {
    console.error('Failed to update pricing settings:', error)
    return { success: false, error: (error as Error).message }
  }
}

// Booking Policy Settings
export async function getBookingPolicySettings(): Promise<BookingPolicySettings | null> {
  try {
    const supabase = await createContextClient('admin')
    const { data, error } = await supabase
      .from('booking_policy_settings')
      .select('*')
      .limit(1)
      .maybeSingle()

    if (error) throw error
    return data || getDefaultBookingPolicySettings()
  } catch (error) {
    console.error('Failed to fetch booking policy settings:', error)
    return getDefaultBookingPolicySettings()
  }
}

export async function updateBookingPolicySettings(settings: Partial<BookingPolicySettings>): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createContextClient('admin')
    const existing = await getBookingPolicySettings()
    
    const { data, error } = await supabase
      .from('booking_policy_settings')
      .upsert({
        id: existing?.id || '1',
        ...settings,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error

    // Log changes
    if (existing) {
      for (const [key, value] of Object.entries(settings)) {
        if (JSON.stringify(existing[key as keyof BookingPolicySettings]) !== JSON.stringify(value)) {
          await createAuditLog(
            'policies',
            'update',
            key,
            existing[key as keyof BookingPolicySettings],
            value
          )
        }
      }
    } else {
      await createAuditLog('policies', 'create')
    }

    revalidatePath('/admin/settings')
    return { success: true }
  } catch (error) {
    console.error('Failed to update booking policy settings:', error)
    return { success: false, error: (error as Error).message }
  }
}

// Theme Settings
export async function getThemeSettings(): Promise<ThemeSettings | null> {
  try {
    const supabase = await createContextClient('admin')
    const { data, error } = await supabase
      .from('theme_settings')
      .select('*')
      .limit(1)
      .maybeSingle()

    if (error) throw error
    return data || getDefaultThemeSettings()
  } catch (error) {
    console.error('Failed to fetch theme settings:', error)
    return getDefaultThemeSettings()
  }
}

export async function updateThemeSettings(settings: Partial<ThemeSettings>): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createContextClient('admin')
    const existing = await getThemeSettings()
    
    const { data, error } = await supabase
      .from('theme_settings')
      .upsert({
        id: existing?.id || '1',
        ...settings,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error

    // Log changes
    if (existing) {
      for (const [key, value] of Object.entries(settings)) {
        if (existing[key as keyof ThemeSettings] !== value) {
          await createAuditLog(
            'theme',
            'update',
            key,
            existing[key as keyof ThemeSettings],
            value
          )
        }
      }
    } else {
      await createAuditLog('theme', 'create')
    }

    revalidatePath('/admin/settings')
    return { success: true }
  } catch (error) {
    console.error('Failed to update theme settings:', error)
    return { success: false, error: (error as Error).message }
  }
}

// Audit Log
export async function getAuditLogs(filters?: {
  section?: SettingsSection
  user_id?: string
  from_date?: string
  to_date?: string
  limit?: number
}): Promise<AuditLogEntry[]> {
  try {
    const supabase = await createContextClient('admin')
    let query = supabase
      .from('audit_logs')
      .select('*')
      .order('timestamp', { ascending: false })

    if (filters?.section) {
      query = query.eq('section', filters.section)
    }
    if (filters?.user_id) {
      query = query.eq('user_id', filters.user_id)
    }
    if (filters?.from_date) {
      query = query.gte('timestamp', filters.from_date)
    }
    if (filters?.to_date) {
      query = query.lte('timestamp', filters.to_date)
    }
    if (filters?.limit) {
      query = query.limit(filters.limit)
    }

    const { data, error } = await query

    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Failed to fetch audit logs:', error)
    return []
  }
}

// Default Settings
function getDefaultOrganizationSettings(): OrganizationSettings {
  return {
    company_name: "Ollie's Rent A Car",
    legal_name: "Ollie's Rent A Car Corporation",
    contact_email: "<EMAIL>",
    contact_phone: "+63 ************",
    business_hours: {
      monday: { is_open: true, open_time: "08:00", close_time: "18:00" },
      tuesday: { is_open: true, open_time: "08:00", close_time: "18:00" },
      wednesday: { is_open: true, open_time: "08:00", close_time: "18:00" },
      thursday: { is_open: true, open_time: "08:00", close_time: "18:00" },
      friday: { is_open: true, open_time: "08:00", close_time: "18:00" },
      saturday: { is_open: true, open_time: "08:00", close_time: "17:00" },
      sunday: { is_open: false }
    },
    default_timezone: "Asia/Manila",
    default_currency: "PHP"
  }
}

function getDefaultPricingSettings(): PricingSettings {
  return {
    base_rates: {
      "SUV": 2500,
      "Sport": 3500,
      "Coupe": 2800,
      "Hatchback": 1800,
      "MPV": 2200
    },
    weekend_multiplier: 1.2,
    holiday_multiplier: 1.5,
    seasonal_ranges: [],
    tax_rate: 12,
    security_deposit: 5000,
    late_return_fee_per_hour: 200,
    extension_hourly_rate: 150,
    extension_price_per_day: 1000,
    pickup_surcharge_per_km: 15
  }
}

function getDefaultBookingPolicySettings(): BookingPolicySettings {
  return {
    min_rental_duration_hours: 4,
    max_rental_duration_days: 30,
    advance_booking_cutoff_hours: 2,
    grace_period_minutes: 15,
    cancellation_policy: {
      free_cancellation_hours: 24,
      partial_refund_hours: 4,
      no_refund_hours: 1,
      cancellation_fee: 500
    },
    required_documents: ["Valid Driver's License", "Government ID", "Proof of Billing"]
  }
}

function getDefaultThemeSettings(): ThemeSettings {
  return {
    primary_color: "#2563eb",
    secondary_color: "#dc2626", 
    neutral_color: "#6b7280",
    dark_mode_enabled: false
  }
}
