{"designSystem": {"viewport": "desktop", "layout": {"grid": "2-column split", "leftPanel": {"type": "image", "backgroundImage": "full-height photo", "textOverlay": {"alignment": "bottom-left", "headingStyle": {"fontWeight": "bold", "fontSize": "24px"}, "subheadingStyle": {"fontWeight": "normal", "fontSize": "16px"}}}, "rightPanel": {"formAlignment": "centered vertically and horizontally", "padding": "48px"}}, "branding": {"logo": "top-right text placeholder", "welcomeText": "Centered greeting above form"}, "colors": {"background": "hsl(var(--background))", "foreground": "hsl(var(--foreground))", "primary": {"DEFAULT": "hsl(var(--primary))", "foreground": "hsl(var(--primary-foreground))"}, "secondary": {"DEFAULT": "hsl(var(--secondary))", "foreground": "hsl(var(--secondary-foreground))"}, "accent": {"DEFAULT": "hsl(var(--accent))", "foreground": "hsl(var(--accent-foreground))"}, "muted": {"DEFAULT": "hsl(var(--muted))", "foreground": "hsl(var(--muted-foreground))"}, "input": "hsl(var(--input))", "border": "hsl(var(--border))", "card": {"DEFAULT": "hsl(var(--card))", "foreground": "hsl(var(--card-foreground))"}, "error": {"DEFAULT": "hsl(var(--destructive))", "foreground": "hsl(var(--destructive-foreground))"}, "hardcoded": ["#fff", "#ddd", "rgba(0,0,0,0.05)", "rgba(0,0,0,0.1)"]}, "typography": {"fontFamily": "Sans-serif", "heading": {"weight": "bold", "size": "24px"}, "body": {"weight": "normal", "size": "16px"}}, "components": {"tabs": {"style": "pill toggle", "active": {"background": "hsl(var(--primary))", "textColor": "hsl(var(--primary-foreground))"}, "inactive": {"background": "hsl(var(--muted))", "textColor": "hsl(var(--muted-foreground))"}}, "inputs": {"shape": "rounded-pill", "border": "1px solid hsl(var(--border))", "padding": "12px 16px", "icons": {"passwordToggle": "eye icon, right-aligned"}}, "buttons": {"primary": {"shape": "pill", "background": "hsl(var(--primary))", "textColor": "hsl(var(--primary-foreground))", "hover": "opacity: 0.9"}}, "checkbox": {"style": "minimal square", "labelPlacement": "right"}, "links": {"forgotPassword": {"alignment": "right", "textColor": "hsl(var(--muted-foreground))"}}}, "states": {"login": {"fields": ["username", "password"], "extras": ["rememberMe", "forgotPassword"]}, "register": {"fields": ["email", "username", "password"]}}}}