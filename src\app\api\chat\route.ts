import { streamText } from 'ai';
import { NextRequest } from 'next/server';
import { openai, MODEL_SETTINGS } from '@/lib/openrouter';
import { searchKnowledge, buildRagContext } from '@/lib/rag';
import { createClient } from '@/lib/supabase/server';

// System prompts
const ADMIN_SYSTEM_PROMPT = `You are PathLink's admin AI assistant. You help administrators manage the car rental platform, bookings, customers, and operations.

Key Guidelines:
- Be professional, efficient, and detail-oriented
- Focus on administrative tasks, system management, and operational support
- Provide technical guidance for platform operations
- Help with booking management, customer issues, and system troubleshooting
- Always prioritize security and data protection
- Provide clear, actionable advice

Admin Capabilities:
- Booking management and finalization
- Customer account management
- Vehicle fleet management
- Reports and analytics

Security Notes:
- Never provide sensitive customer data in chat
- Always reference proper admin procedures
- Escalate security concerns appropriately
- Maintain audit trails for admin actions`;

const CUSTOMER_SYSTEM_PROMPT = `You are PathLink's customer service AI assistant for <PERSON><PERSON>'s Rent A Car. You help customers with their car rental needs, bookings, and general inquiries.

Key Guidelines:
- Be friendly, helpful, and professional
- Focus on customer service and support
- Provide clear information about services, policies, and procedures
- Help with booking assistance and general inquiries
- Always prioritize customer satisfaction
- Be empathetic and understanding

Customer Service Areas:
- Vehicle availability and booking assistance
- Rental policies and procedures
- Payment and pricing information
- Location and pickup/drop-off details
- General car rental questions
- Troubleshooting booking issues

Communication Style:
- Use a warm, conversational tone
- Be patient and thorough in explanations
- Provide step-by-step guidance when needed
- Offer alternative solutions when possible`;

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Get user profile to determine admin vs customer
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    const isAdmin = profile && ['admin', 'super_admin'].includes(profile.role);
    const systemPrompt = isAdmin ? ADMIN_SYSTEM_PROMPT : CUSTOMER_SYSTEM_PROMPT;
    const userType = isAdmin ? 'admin' : 'customer';

    const requestBody = await request.json();
    if (process.env.NODE_ENV === 'development') {
      console.log('Received request body:', JSON.stringify(requestBody, null, 2));
    }
    
    const { messages, sessionId } = requestBody;
    
    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid messages format', { status: 400 });
    }

    // Get the latest user message for RAG search
    const userMessage = messages[messages.length - 1];
    if (process.env.NODE_ENV === 'development') {
      console.log('Latest user message:', JSON.stringify(userMessage, null, 2));
    }
    
    if (!userMessage || userMessage.role !== 'user') {
      return new Response('No valid user message found', { status: 400 });
    }

    // Handle multiple possible content formats from AI SDK
    let messageContent = '';
    
    // First try the parts array format (AI SDK v2 format)
    if (userMessage.parts && Array.isArray(userMessage.parts)) {
      const textParts = userMessage.parts.filter((part: any) => part.type === 'text');
      messageContent = textParts.map((part: any) => part.text).join(' ').trim();
    }
    // Then try content as string
    else if (typeof userMessage.content === 'string') {
      messageContent = userMessage.content.trim();
    } 
    // Try content as array format
    else if (Array.isArray(userMessage.content)) {
      const textParts = userMessage.content.filter((part: any) => part.type === 'text');
      messageContent = textParts.map((part: any) => part.text).join(' ').trim();
    } 
    // Try text property directly
    else if (userMessage.text) {
      messageContent = userMessage.text.trim();
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('Extracted message content:', messageContent);
    }

    if (!messageContent) {
      return new Response('Empty message content', { status: 400 });
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`${userType.toUpperCase()} chat - Processing message:`, messageContent);
    }

    // Perform RAG search for relevant context
    const knowledgeResults = await searchKnowledge(messageContent, userType);
    const ragContext = buildRagContext(messageContent, knowledgeResults, userType);

    // Build conversation messages for the AI model
    const conversationMessages = [
      {
        role: 'system' as const,
        content: `${systemPrompt}\n\nAdditional Context:\n${ragContext}`
      },
      ...messages.map((msg: any) => ({
        role: msg.role,
        content: typeof msg.content === 'string' ? msg.content : 
                Array.isArray(msg.content) ? msg.content.filter((p: any) => p.type === 'text').map((p: any) => p.text).join(' ') :
                msg.text || ''
      }))
    ];

    // Store conversation
    const finalSessionId = sessionId || `${userType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Create a truncated version of the user message for title (max 50 chars)
    const conversationTitle = messageContent.length > 50 
      ? messageContent.substring(0, 47) + '...' 
      : messageContent;

    await supabase
      .from('conversations')
      .insert({
        user_id: user.id,
        session_id: finalSessionId,
        user_message: messageContent,
        conversation_title: conversationTitle,
        user_type: userType
      });

    // Generate streaming response
    const result = await streamText({
      model: openai(MODEL_SETTINGS.model),
      messages: conversationMessages,
      temperature: MODEL_SETTINGS.temperature,
      frequencyPenalty: MODEL_SETTINGS.frequencyPenalty,
      presencePenalty: MODEL_SETTINGS.presencePenalty,
    });

    return result.toTextStreamResponse();
  } catch (error) {
    console.error('Chat error:', error);
    return new Response('Internal server error', { status: 500 });
  }
}
