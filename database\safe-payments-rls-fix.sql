-- Safe RLS policy fix for payments table
-- Only adds missing INSERT policy without dropping anything

-- Add policy to allow customers to insert payment records for their own bookings
DO $$
BEGIN
    -- Check if the policy already exists
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'payments' 
        AND policyname = 'Users can insert payments for their own bookings'
    ) THEN
        -- Create the INSERT policy
        CREATE POLICY "Users can insert payments for their own bookings" 
        ON public.payments FOR INSERT 
        TO authenticated
        WITH CHECK (
            EXISTS (
                SELECT 1 FROM public.bookings 
                WHERE bookings.id = booking_id 
                AND bookings.customer_id = (SELECT auth.uid())
            )
        );
        
        RAISE NOTICE 'Created INSERT policy for payments table';
    ELSE
        RAISE NOTICE 'INSERT policy already exists for payments table';
    END IF;
END
$$;
