"use client"

import React from "react"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"

// Admin Dashboard Overview Skeleton with modern design
export function AdminDashboardSkeleton({ 
  className, 
  variant = "default" 
}: { 
  className?: string
  variant?: "default" | "compact"
}) {
  const isCompact = variant === "compact"
  
  return (
    <div className={cn(
      "space-y-6",
      isCompact ? "p-4" : "p-6",
      className
    )} role="status" aria-busy="true" aria-label="Loading admin dashboard">
      {/* Header */}
      <div className="space-y-3">
        <Skeleton 
          className={isCompact ? "h-7 w-56" : "h-8 w-64"} 
          
          aria-label="Loading dashboard title"
        />
        <Skeleton 
          className={isCompact ? "h-3 w-40" : "h-4 w-48"} 
          
          aria-label="Loading dashboard subtitle"
        />
      </div>

      {/* KPI Cards */}
      <div className={cn(
        "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",
        isCompact && "gap-4"
      )}>
        {Array.from({ length: 4 }, (_, i) => {
          const metrics = ['Total Revenue', 'Active Bookings', 'Available Cars', 'Customer Growth']
          return (
            <div key={i} className={cn(
              "bg-card rounded-lg border transition-all duration-200 hover:shadow-sm",
              isCompact ? "p-4" : "p-6"
            )}>
              <div className="flex items-center justify-between">
                <div className="space-y-2 flex-1">
                  <Skeleton 
                    className={isCompact ? "h-3 w-16" : "h-4 w-20"} 
                    
                    aria-label={`Loading ${metrics[i]} label`}
                  />
                  <Skeleton 
                    className={isCompact ? "h-6 w-12" : "h-8 w-16"} 
                    
                    aria-label={`Loading ${metrics[i]} value`}
                  />
                  <Skeleton 
                    className={isCompact ? "h-2 w-20" : "h-3 w-24"} 
                    
                    aria-label={`Loading ${metrics[i]} change`}
                  />
                </div>
                <Skeleton 
                  className={cn(isCompact ? "h-6 w-6" : "h-8 w-8", "rounded-full")} 
                  aria-label={`Loading ${metrics[i]} icon`}
                />
              </div>
            </div>
          )
        })}
      </div>

      {/* Charts Section */}
      <div className={cn(
        "grid grid-cols-1 xl:grid-cols-2 gap-6",
        isCompact && "gap-4"
      )}>
        <div className={cn(
          "bg-card rounded-lg border",
          isCompact ? "p-4" : "p-6"
        )}>
          <div className="flex items-center justify-between mb-4">
            <Skeleton 
              className={isCompact ? "h-5 w-28" : "h-6 w-32"} 
              
              aria-label="Loading chart title"
            />
            <Skeleton 
              className="h-8 w-24" 
              
              aria-label="Loading chart filter"
            />
          </div>
          <Skeleton 
            className={isCompact ? "h-48 w-full" : "h-64 w-full"} 
            
            aria-label="Loading revenue chart"
          />
        </div>
        
        <div className={cn(
          "bg-card rounded-lg border",
          isCompact ? "p-4" : "p-6"
        )}>
          <div className="flex items-center justify-between mb-4">
            <Skeleton 
              className={isCompact ? "h-5 w-32" : "h-6 w-40"} 
              
              aria-label="Loading analytics chart title"
            />
            <Skeleton 
              className="h-8 w-20" 
              
              aria-label="Loading analytics filter"
            />
          </div>
          <Skeleton 
            className={isCompact ? "h-48 w-full" : "h-64 w-full"} 
            
            aria-label="Loading analytics chart"
          />
        </div>
      </div>

      {/* Recent Activity */}
      <div className={cn(
        "grid grid-cols-1 xl:grid-cols-3 gap-6",
        isCompact && "gap-4"
      )}>
        {/* Activity Feed */}
        <div className={cn(
          "xl:col-span-2 bg-card rounded-lg border",
          isCompact ? "p-4" : "p-6"
        )}>
          <div className="flex items-center justify-between mb-4">
            <Skeleton 
              className={isCompact ? "h-5 w-28" : "h-6 w-32"} 
              
              aria-label="Loading recent activity title"
            />
            <Skeleton 
              className="h-8 w-20" 
              
              aria-label="Loading view all button"
            />
          </div>
          
          <div className="space-y-4">
            {Array.from({ length: isCompact ? 3 : 5 }, (_, i) => (
              <div key={i} className={cn(
                "flex items-center gap-4 rounded-lg transition-colors hover:bg-muted/50",
                isCompact ? "p-2" : "p-3"
              )}>
                <Skeleton className={cn(isCompact ? "h-8 w-8" : "h-10 w-10", "rounded-full")} />
                <div className="flex-1 space-y-1">
                  <Skeleton 
                    className={isCompact ? "h-3 w-full" : "h-4 w-full"} 
                    
                    aria-label={`Loading activity ${i + 1} description`}
                  />
                  <Skeleton 
                    className={isCompact ? "h-2 w-2/3" : "h-3 w-3/4"} 
                    
                    aria-label={`Loading activity ${i + 1} details`}
                  />
                </div>
                <Skeleton 
                  className="h-6 w-16" 
                  
                  aria-label={`Loading activity ${i + 1} status`}
                />
              </div>
            ))}
          </div>
        </div>
        
        {/* Quick Stats */}
        <div className={cn(
          "bg-card rounded-lg border",
          isCompact ? "p-4" : "p-6"
        )}>
          <Skeleton 
            className={isCompact ? "h-5 w-24" : "h-6 w-28"} 
            aria-label="Loading quick stats title"
          />
          <div className={cn("space-y-3", isCompact ? "mt-3" : "mt-4")}>
            {Array.from({ length: 6 }, (_, i) => {
              const stats = ['Today', 'This Week', 'This Month', 'Pending', 'Approved', 'Total']
              return (
                <div key={i} className="flex items-center justify-between">
                  <Skeleton 
                    className={isCompact ? "h-3 w-16" : "h-4 w-20"} 
                    
                    aria-label={`Loading ${stats[i]} label`}
                  />
                  <Skeleton 
                    className={isCompact ? "h-3 w-10" : "h-4 w-12"} 
                    
                    aria-label={`Loading ${stats[i]} value`}
                  />
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}

// Admin Bookings Page Skeleton
export function AdminBookingsSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-6 p-6", className)} role="status" aria-busy="true" aria-label="Loading bookings">
      {/* Header with filters */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-32" />
          <div className="flex gap-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>

        {/* KPI Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {Array.from({ length: 6 }, (_, i) => (
            <div key={i} className="bg-white border rounded-lg p-4 text-center">
              <Skeleton className="h-4 w-16 mx-auto mb-2" />
              <Skeleton className="h-6 w-8 mx-auto mb-1" />
              <Skeleton className="h-3 w-12 mx-auto" />
            </div>
          ))}
        </div>

        {/* Filters toolbar */}
        <div className="bg-white border rounded-lg p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <Skeleton className="h-10 w-64" />
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-48" />
          </div>
        </div>
      </div>

      {/* Mobile cards (visible on small screens) */}
      <div className="block md:hidden space-y-4">
        {Array.from({ length: 5 }, (_, i) => (
          <div key={i} className="bg-white border rounded-lg p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="space-y-1">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-24" />
              </div>
              <Skeleton className="h-6 w-16 rounded-full" />
            </div>
            <div className="space-y-2 mb-3">
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
            <div className="flex gap-2">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-20" />
            </div>
          </div>
        ))}
      </div>

      {/* Desktop table (visible on larger screens) */}
      <div className="hidden md:block">
        <div className="space-y-3">
          {/* Header */}
          <div className="grid grid-cols-8 gap-4 p-4 bg-muted/50 rounded-lg font-medium">
            {Array.from({ length: 8 }, (_, i) => (
              <Skeleton key={i} className="h-4" />
            ))}
          </div>
          {/* Rows */}
          <div className="space-y-1">
            {Array.from({ length: 10 }, (_, i) => (
              <div key={i} className="grid grid-cols-8 gap-4 p-4 border rounded-lg transition-colors hover:bg-muted/20">
                {Array.from({ length: 8 }, (_, j) => (
                  <Skeleton key={j} className="h-4" />
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Admin Cars Page Skeleton
export function AdminCarsSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-6 p-6", className)} role="status" aria-busy="true" aria-label="Loading cars">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-8 w-40" />
          <Skeleton className="h-4 w-64" />
        </div>
        <Skeleton className="h-10 w-24" />
      </div>

      {/* Tabs */}
      <div className="flex gap-4 border-b">
        {Array.from({ length: 4 }, (_, i) => (
          <div key={i} className="flex items-center gap-2 pb-2">
            <Skeleton className="h-2 w-2 rounded-full" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-6 rounded-full" />
          </div>
        ))}
      </div>

      {/* Mobile cards (visible on small screens) */}
      <div className="block lg:hidden">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {Array.from({ length: 8 }, (_, i) => (
            <div key={i} className="bg-white border rounded-lg overflow-hidden">
              <Skeleton className="h-48 w-full" />
              <div className="p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <Skeleton className="h-6 w-16 rounded-full" />
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                </div>
                <div className="flex items-center justify-between pt-2">
                  <Skeleton className="h-6 w-20" />
                  <div className="flex gap-1">
                    <Skeleton className="h-8 w-8 rounded" />
                    <Skeleton className="h-8 w-8 rounded" />
                    <Skeleton className="h-8 w-8 rounded" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Desktop table (visible on larger screens) */}
      <div className="hidden lg:block">
        <div className="space-y-3">
          {/* Header */}
          <div className="grid grid-cols-9 gap-4 p-4 bg-muted/50 rounded-lg font-medium">
            {Array.from({ length: 9 }, (_, i) => (
              <Skeleton key={i} className="h-4" />
            ))}
          </div>
          {/* Rows */}
          <div className="space-y-1">
            {Array.from({ length: 12 }, (_, i) => (
              <div key={i} className="grid grid-cols-9 gap-4 p-4 border rounded-lg transition-colors hover:bg-muted/20">
                {Array.from({ length: 9 }, (_, j) => (
                  <Skeleton key={j} className="h-4" />
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Admin Payments Page Skeleton
export function AdminPaymentsSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-6 p-6", className)} role="status" aria-busy="true" aria-label="Loading payments">
      {/* Header with filters */}
      <div className="space-y-4">
        <Skeleton className="h-8 w-40" />
        
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-48" />
        </div>
      </div>

      {/* Mobile cards (visible on small screens) */}
      <div className="block lg:hidden space-y-4">
        {Array.from({ length: 6 }, (_, i) => (
          <div key={i} className="bg-white border rounded-lg p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="space-y-1">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-24" />
              </div>
              <Skeleton className="h-6 w-16 rounded-full" />
            </div>
            <div className="space-y-2 mb-3">
              <div className="flex justify-between">
                <Skeleton className="h-3 w-16" />
                <Skeleton className="h-3 w-20" />
              </div>
              <div className="flex justify-between">
                <Skeleton className="h-3 w-20" />
                <Skeleton className="h-3 w-16" />
              </div>
              <div className="flex justify-between">
                <Skeleton className="h-3 w-12" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
            <Skeleton className="h-8 w-full" />
          </div>
        ))}
      </div>

      {/* Desktop table (visible on larger screens) */}
      <div className="hidden lg:block">
        <div className="space-y-3">
          {/* Header */}
          <div className="grid grid-cols-7 gap-4 p-4 bg-muted/50 rounded-lg font-medium">
            {Array.from({ length: 7 }, (_, i) => (
              <Skeleton key={i} className="h-4" />
            ))}
          </div>
          {/* Rows */}
          <div className="space-y-1">
            {Array.from({ length: 8 }, (_, i) => (
              <div key={i} className="grid grid-cols-7 gap-4 p-4 border rounded-lg transition-colors hover:bg-muted/20">
                {Array.from({ length: 7 }, (_, j) => (
                  <Skeleton key={j} className="h-4" />
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-4 w-32" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-20" />
          {Array.from({ length: 5 }, (_, i) => (
            <Skeleton key={i} className="h-8 w-8" />
          ))}
          <Skeleton className="h-8 w-20" />
        </div>
      </div>
    </div>
  )
}

// Admin Settings Page Skeleton
export function AdminSettingsSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("flex h-screen", className)} role="status" aria-busy="true" aria-label="Loading settings">
      {/* Desktop sidebar (visible on larger screens) */}
      <div className="hidden lg:block w-64 border-r bg-white p-6">
        <Skeleton className="h-6 w-32 mb-6" />
        <div className="space-y-2">
          {Array.from({ length: 6 }, (_, i) => (
            <div key={i} className="flex items-center gap-3 p-3 rounded">
              <Skeleton className="h-5 w-5" />
              <Skeleton className="h-4 w-24" />
            </div>
          ))}
        </div>
      </div>

      {/* Mobile header (visible on smaller screens) */}
      <div className="lg:hidden flex items-center justify-between p-4 border-b">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-8 w-8" />
      </div>

      {/* Main content */}
      <div className="flex-1 p-6 space-y-8">
        {/* Section 1 */}
        <div className="bg-white rounded-lg border p-6">
          <Skeleton className="h-6 w-40 mb-6" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Array.from({ length: 4 }, (_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
          </div>
        </div>

        {/* Section 2 */}
        <div className="bg-white rounded-lg border p-6">
          <Skeleton className="h-6 w-48 mb-6" />
          <div className="space-y-4">
            {Array.from({ length: 5 }, (_, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded">
                <div className="space-y-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-48" />
                </div>
                <Skeleton className="h-6 w-12 rounded-full" />
              </div>
            ))}
          </div>
        </div>

        {/* Section 3 */}
        <div className="bg-white rounded-lg border p-6">
          <Skeleton className="h-6 w-36 mb-6" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Array.from({ length: 6 }, (_, i) => (
              <div key={i} className="border rounded-lg p-4 text-center">
                <Skeleton className="h-12 w-12 mx-auto mb-2" />
                <Skeleton className="h-4 w-20 mx-auto mb-1" />
                <Skeleton className="h-3 w-16 mx-auto" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Admin GPS Tracker Skeleton
export function AdminTrackerSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("h-screen flex flex-col", className)} role="status" aria-busy="true" aria-label="Loading GPS tracker">
      {/* Header */}
      <div className="border-b bg-white p-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-32" />
          <div className="flex gap-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-20" />
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Desktop sidebar (visible on larger screens) */}
        <div className="hidden lg:block w-80 border-r bg-white p-4 space-y-4">
          {/* KPI Stats */}
          <div className="grid grid-cols-2 gap-4">
            {Array.from({ length: 4 }, (_, i) => (
              <div key={i} className="border rounded-lg p-3 text-center">
                <Skeleton className="h-3 w-16 mx-auto mb-1" />
                <Skeleton className="h-6 w-8 mx-auto mb-1" />
                <Skeleton className="h-2 w-12 mx-auto" />
              </div>
            ))}
          </div>

          {/* Vehicle details */}
          <div className="border rounded-lg p-4">
            <Skeleton className="h-5 w-28 mb-3" />
            <div className="space-y-2">
              <div className="flex justify-between">
                <Skeleton className="h-3 w-16" />
                <Skeleton className="h-3 w-20" />
              </div>
              <div className="flex justify-between">
                <Skeleton className="h-3 w-12" />
                <Skeleton className="h-3 w-16" />
              </div>
              <div className="flex justify-between">
                <Skeleton className="h-3 w-20" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
          </div>

          {/* Fleet overview */}
          <div className="border rounded-lg p-4">
            <Skeleton className="h-5 w-24 mb-3" />
            <div className="space-y-2">
              {Array.from({ length: 5 }, (_, i) => (
                <div key={i} className="flex items-center gap-2 p-2 border rounded">
                  <Skeleton className="h-2 w-2 rounded-full" />
                  <Skeleton className="h-3 w-20" />
                  <Skeleton className="h-3 w-16 ml-auto" />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main map area */}
        <div className="flex-1 relative">
          <Skeleton className="h-full w-full" />
          
          {/* Mobile floating buttons */}
          <div className="lg:hidden absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <Skeleton className="h-10 w-24 rounded-full" />
          </div>
          
          <div className="lg:hidden absolute top-4 right-4 flex flex-col gap-2">
            <Skeleton className="h-10 w-10 rounded-full" />
            <Skeleton className="h-10 w-10 rounded-full" />
          </div>
        </div>
      </div>
    </div>
  )
}

// Admin Modal Skeleton
export function AdminModalSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("p-6 space-y-6", className)} role="status" aria-busy="true" aria-label="Loading modal content">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-6 w-6 rounded" />
      </div>

      {/* Tabs or sections */}
      <div className="flex gap-4 border-b">
        {Array.from({ length: 3 }, (_, i) => (
          <Skeleton key={i} className="h-8 w-20 rounded-t" />
        ))}
      </div>

      {/* Content */}
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
        
        <Skeleton className="h-32 w-full rounded-md" />
        
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-3 justify-end pt-4 border-t">
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-24" />
      </div>
    </div>
  )
}

// Admin Drawer Skeleton
export function AdminDrawerSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("w-full max-w-2xl p-6 space-y-6", className)} role="status" aria-busy="true" aria-label="Loading drawer content">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-32" />
        </div>
        <Skeleton className="h-6 w-6 rounded" />
      </div>

      {/* Content sections */}
      {Array.from({ length: 3 }, (_, sectionIndex) => (
        <div key={sectionIndex} className="space-y-4">
          <Skeleton className="h-5 w-32" />
          <div className="border rounded-lg p-4 space-y-3">
            {Array.from({ length: 4 }, (_, i) => (
              <div key={i} className="flex justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-32" />
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* Action buttons */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
        <Skeleton className="h-10 flex-1" />
        <Skeleton className="h-10 flex-1" />
        <Skeleton className="h-10 flex-1" />
      </div>
    </div>
  )
}
