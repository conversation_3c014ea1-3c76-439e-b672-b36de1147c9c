"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { useRouter, useSearchParams } from "next/navigation";
import { Eye, EyeOff, ArrowLeft, CheckCircle, XCircle, Shield, Check, X } from "lucide-react";
import Image from "next/image";
import { useToast } from "@/hooks/use-toast";
import { CustomerLoadingButton } from "@/components/customer-side/loading/enhanced-loading-components";
import Link from "next/link";

interface MobileAuthPageProps {
  mode: "login" | "register";
}

export function MobileAuthPage({ mode }: MobileAuthPageProps) {
  const { signIn, signUp, user } = useCustomerAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  
  // Form states
  const [email, setEmail] = React.useState("");
  const [password, setPassword] = React.useState("");
  const [firstName, setFirstName] = React.useState("");
  const [middleInitial, setMiddleInitial] = React.useState("");
  const [lastName, setLastName] = React.useState("");
  const [phoneNumber, setPhoneNumber] = React.useState("");
  const [showPassword, setShowPassword] = React.useState(false);
  const [rememberMe, setRememberMe] = React.useState(false);
  const [error, setError] = React.useState("");
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [currentMode, setCurrentMode] = React.useState<"login" | "register">(mode);

  // Password strength calculation for register mode
  const getPasswordStrength = (password: string) => {
    let score = 0;
    const requirements = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };

    Object.values(requirements).forEach((met) => met && score++);

    return {
      score,
      requirements,
      strength: score < 3 ? "weak" : score < 5 ? "medium" : "strong",
    };
  };

  const passwordStrength = getPasswordStrength(password);

  // Redirect already authenticated users
  React.useEffect(() => {
    if (user) {
      if (user.user_metadata?.role === "admin") {
        router.replace("/admin");
      } else {
        router.replace("/");
      }
    }
  }, [user, router]);

  // Check for verification message or error in URL params
  React.useEffect(() => {
    const errorParam = searchParams.get("error");
    const messageParam = searchParams.get("message");

    if (errorParam) {
      setError(decodeURIComponent(errorParam));
    }

    if (messageParam) {
      toast({
        title: "Account Status",
        description: decodeURIComponent(messageParam),
      });
    }
  }, [searchParams, toast]);

  const handleModeSwitch = (newMode: "login" | "register") => {
    setCurrentMode(newMode);
    setError("");
    setEmail("");
    setPassword("");
    setFirstName("");
    setMiddleInitial("");
    setLastName("");
    setPhoneNumber("");
    setShowPassword(false);
    setRememberMe(false);
  };

  // Phone number validation
  const validatePhoneNumber = (phone: string): boolean => {
    // Basic Philippine phone number validation
    const phoneRegex = /^(\+63|63|0)?[9]\d{9}$/;
    return phoneRegex.test(phone.replace(/[\s-]/g, ''));
  };

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (isSubmitting) return;

    setError("");
    setIsSubmitting(true);

    // Add small delay to prevent rapid successive requests
    await new Promise(resolve => setTimeout(resolve, 500));

    try {
      if (currentMode === "login") {
        const { error } = await signIn(email, password);

        if (error) {
          setError(error.message);
          toast({
            variant: "destructive",
            title: "Login Failed",
            description: error.message,
            className: "bg-white border-red-200",
            action: <X className="h-5 w-5 text-red-500" />,
          });
          return;
        }

        const redirectTo = searchParams.get("redirect");
        if (redirectTo && !redirectTo.startsWith("/admin")) {
          router.replace(redirectTo);
        } else {
          router.replace("/");
        }
      } else {
        // Register mode
        if (passwordStrength.score < 3) {
          setError("Please ensure your password meets the minimum requirements.");
          return;
        }

        // Validate phone number
        if (!validatePhoneNumber(phoneNumber)) {
          setError("Please enter a valid Philippine phone number (e.g., 09123456789).");
          return;
        }

        const nameParts = [firstName.trim(), middleInitial.trim(), lastName.trim()].filter(Boolean);
        const fullName = nameParts.join(' ').trim();
        const { error } = await signUp(email, password, { 
          full_name: fullName,
          phone: phoneNumber.trim(),
          first_name: firstName.trim(),
          middle_initial: middleInitial.trim(),
          last_name: lastName.trim()
        });

        if (error) {
          setError(error.message);
          toast({
            variant: "destructive",
            title: "Registration Failed",
            description: error.message,
            className: "bg-white border-red-200",
            action: <X className="h-5 w-5 text-red-500" />,
          });
          return;
        }

        toast({
          title: "Registration Successful!",
          description: "Please check your email for a 6-digit verification code.",
          className: "bg-white border-green-200",
          action: <Check className="h-5 w-5 text-green-500" />,
        });

        // Store user data in localStorage for setup account page
        localStorage.setItem('pendingUserData', JSON.stringify({
          firstName: firstName.trim(),
          middleInitial: middleInitial.trim(),
          lastName: lastName.trim(),
          phone: phoneNumber.trim()
        }));

        router.replace(`/auth/verify-otp?email=${encodeURIComponent(email)}`);
      }
    } catch (err) {
      const errorMessage = "An unexpected error occurred. Please try again.";
      setError(errorMessage);
      toast({
        variant: "destructive",
        title: currentMode === "login" ? "Login Failed" : "Registration Failed",
        description: errorMessage,
        className: "bg-white border-red-200",
        action: <X className="h-5 w-5 text-red-500" />,
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Back to Homepage Button - Fixed Position */}
      <div className="fixed top-4 left-4 z-20">
        <Link href="/">
          <Button
            variant="secondary"
            size="sm"
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground shadow-sm"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
        </Link>
      </div>

      {/* Cover Image Section - Top Background */}
      <div className="relative h-48 w-full overflow-hidden bg-muted">
        <Image
          src="/ollie_cover_photo.jpg"
          alt="Ollie's Rent A Car"
          fill
          className="object-cover"
          priority
        />
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-black/10 to-black/40" />
        
        {/* Welcome Text Overlay - Bottom of Cover */}
        <div className="absolute bottom-4 left-6 right-6 text-white">
          <h1 className="text-xl font-bold leading-tight">
            Welcome to Ollie's Rent A Car
          </h1>
          <p className="text-sm font-normal opacity-90 mt-1">
            Your trusted partner for reliable vehicle rentals
          </p>
        </div>
      </div>

      {/* Main Content - Single Column Layout */}
      <div className="px-6 py-6 space-y-6">
        {/* Logo - Centered */}
        <div className="flex justify-center">
          <Link href="/" className="cursor-pointer">
            <div className="relative w-32 h-12">
              <Image
                src="/ollie_logo1.jpg"
                alt="Ollie's Logo"
                fill
                className="object-contain hover:opacity-80 transition-opacity"
                priority
              />
            </div>
          </Link>
        </div>

        {/* Welcome Text - Centered greeting above tabs */}
        <div className="text-center space-y-2">
          <h2 className="text-xl font-bold text-foreground">
            {currentMode === "login" ? "Welcome Back" : "Create Your Account"}
          </h2>
          <p className="text-sm text-muted-foreground">
            {currentMode === "login" 
              ? "Sign in to your customer account" 
              : "Join Ollie Track and start your journey"}
          </p>
        </div>

        {/* Tab Toggle - Pill Style */}
        <div className="flex rounded-full bg-muted p-1 border border-border/50">
          <button
            type="button"
            onClick={() => handleModeSwitch("login")}
            className={`flex-1 py-3 px-4 rounded-full text-sm font-medium transition-all duration-200 ${
              currentMode === "login"
                ? "bg-[#4285f4] text-white shadow-sm"
                : "bg-transparent text-muted-foreground hover:text-foreground"
            }`}
          >
            Login
          </button>
          <button
            type="button"
            onClick={() => handleModeSwitch("register")}
            className={`flex-1 py-3 px-4 rounded-full text-sm font-medium transition-all duration-200 ${
              currentMode === "register"
                ? "bg-[#4285f4] text-white shadow-sm"
                : "bg-transparent text-muted-foreground hover:text-foreground"
            }`}
          >
            Register
          </button>
        </div>

        {/* Form */}
        <form onSubmit={onSubmit} className="space-y-4">

          {/* Name Fields - Register Only */}
          {currentMode === "register" && (
            <>
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-sm font-medium">
                    First Name
                  </Label>
                  <Input
                    id="firstName"
                    type="text"
                    placeholder="First name"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    required
                    className="h-11 px-4 rounded-full border border-border"
                    disabled={isSubmitting}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-sm font-medium">
                    Last Name
                  </Label>
                  <Input
                    id="lastName"
                    type="text"
                    placeholder="Last name"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    required
                    className="h-11 px-4 rounded-full border border-border"
                    disabled={isSubmitting}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="middleInitial" className="text-sm font-medium">
                  Middle Initial <span className="text-muted-foreground">(Optional)</span>
                </Label>
                <Input
                  id="middleInitial"
                  type="text"
                  placeholder="M"
                  value={middleInitial}
                  onChange={(e) => {
                    const value = e.target.value.toUpperCase().slice(0, 1);
                    setMiddleInitial(value);
                  }}
                  maxLength={1}
                  className="h-11 px-4 rounded-full border border-border"
                  disabled={isSubmitting}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phoneNumber" className="text-sm font-medium">
                  Phone Number (max 11 digits)
                </Label>
                <Input
                  id="phoneNumber"
                  type="tel"
                  placeholder="Enter your phone number (e.g., 09123456789)"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  maxLength={11}
                  required
                  className="h-11 px-4 rounded-full border border-border"
                  disabled={isSubmitting}
                />
              </div>
            </>
          )}

          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium">
              {currentMode === "login" ? "Username" : "Email Address"}
            </Label>
            <Input
              id="email"
              type="email"
              placeholder={currentMode === "login" ? "Enter your username" : "Enter your email address"}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="h-11 px-4 rounded-full border border-border"
              disabled={isSubmitting}
            />
          </div>

          {/* Password Field */}
          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm font-medium">
              Password
            </Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder={currentMode === "login" ? "Enter your password" : "Create a strong password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="h-11 px-4 pr-12 rounded-full border border-border"
                disabled={isSubmitting}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                aria-label={showPassword ? "Hide password" : "Show password"}
                disabled={isSubmitting}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>

            {/* Password Requirements - Register Only */}
            {currentMode === "register" && password && (
              <div className="space-y-3 mt-3">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-muted-foreground" />
                  <span className="text-xs font-medium">
                    Password Strength:
                    <span
                      className={`ml-1 ${
                        passwordStrength.strength === "weak"
                          ? "text-destructive"
                          : passwordStrength.strength === "medium"
                          ? "text-yellow-600"
                          : "text-green-600"
                      }`}
                    >
                      {passwordStrength.strength === "weak"
                        ? "Weak"
                        : passwordStrength.strength === "medium"
                        ? "Medium"
                        : "Strong"}
                    </span>
                  </span>
                </div>

                <div className="w-full bg-muted rounded-full h-1.5">
                  <div
                    className={`h-1.5 rounded-full transition-all duration-300 ${
                      passwordStrength.strength === "weak"
                        ? "bg-destructive w-1/3"
                        : passwordStrength.strength === "medium"
                        ? "bg-yellow-500 w-2/3"
                        : "bg-green-500 w-full"
                    }`}
                  />
                </div>

                <div className="space-y-1 text-xs">
                  <div className="flex items-center gap-1">
                    {passwordStrength.requirements.length ? (
                      <CheckCircle className="h-3 w-3 text-green-600" />
                    ) : (
                      <XCircle className="h-3 w-3 text-destructive" />
                    )}
                    <span
                      className={
                        passwordStrength.requirements.length
                          ? "text-green-600"
                          : "text-destructive"
                      }
                    >
                      8+ characters
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    {passwordStrength.requirements.lowercase ? (
                      <CheckCircle className="h-3 w-3 text-green-600" />
                    ) : (
                      <XCircle className="h-3 w-3 text-destructive" />
                    )}
                    <span
                      className={
                        passwordStrength.requirements.lowercase
                          ? "text-green-600"
                          : "text-destructive"
                      }
                    >
                      Lowercase letter
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    {passwordStrength.requirements.uppercase ? (
                      <CheckCircle className="h-3 w-3 text-green-600" />
                    ) : (
                      <XCircle className="h-3 w-3 text-destructive" />
                    )}
                    <span
                      className={
                        passwordStrength.requirements.uppercase
                          ? "text-green-600"
                          : "text-destructive"
                      }
                    >
                      Uppercase letter
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    {passwordStrength.requirements.number ? (
                      <CheckCircle className="h-3 w-3 text-green-600" />
                    ) : (
                      <XCircle className="h-3 w-3 text-destructive" />
                    )}
                    <span
                      className={
                        passwordStrength.requirements.number
                          ? "text-green-600"
                          : "text-destructive"
                      }
                    >
                      Number
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Remember Me & Forgot Password - Login Only */}
          {currentMode === "login" && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={rememberMe}
                  onCheckedChange={(checked) => setRememberMe(!!checked)}
                  className="h-4 w-4"
                />
                <Label htmlFor="remember" className="text-sm">
                  Remember Me
                </Label>
              </div>
              <Link
                href="/customer/forgot-password"
                className="text-sm text-[#4285f4] hover:text-[#3367d6] hover:underline"
              >
                Forgot Password?
              </Link>
            </div>
          )}

          {/* Submit Button - Touch-friendly height */}
          <CustomerLoadingButton
            type="submit"
            isLoading={isSubmitting}
            loadingText={currentMode === "login" ? "Signing In..." : "Creating Account..."}
            disabled={currentMode === "register" && !!password && passwordStrength.score < 3}
            className="w-full h-12 rounded-full bg-[#4285f4] text-white hover:bg-[#3367d6] transition-all duration-200 font-medium"
          >
            {currentMode === "login" ? "Sign In" : "Create Account"}
          </CustomerLoadingButton>
        </form>

        {/* Footer Link */}
        <div className="text-center pb-6">
          <p className="text-sm text-muted-foreground">
            {currentMode === "login" ? "Don't have an account? " : "Already have an account? "}
            <button
              type="button"
              onClick={() => handleModeSwitch(currentMode === "login" ? "register" : "login")}
              className="text-[#4285f4] hover:text-[#3367d6] hover:underline font-medium transition-colors duration-200"
            >
              {currentMode === "login" ? "Create one here" : "Sign in here"}
            </button>
          </p>
        </div>
      </div>
    </div>
  );
}
