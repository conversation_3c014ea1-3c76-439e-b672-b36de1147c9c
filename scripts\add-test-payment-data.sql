-- Add test payment and document data for admin booking modal testing
-- This script assumes there are existing bookings in the database

-- First, let's get an existing booking ID (replace with actual booking ID from your database)
-- SELECT id FROM bookings LIMIT 1;

-- Example test data - replace 'YOUR_BOOKING_ID_HERE' with actual booking ID
DO $$
DECLARE
    test_booking_id UUID;
    test_customer_id UUID;
BEGIN
    -- Get the first booking from the database
    SELECT id, customer_id INTO test_booking_id, test_customer_id
    FROM bookings 
    WHERE status = 'Pending'
    LIMIT 1;
    
    IF test_booking_id IS NOT NULL THEN
        -- Insert test payment data
        INSERT INTO payments (
            id,
            booking_id,
            customer_id,
            amount,
            status,
            method,
            transaction_id,
            transaction_date,
            proof_of_payment_url,
            created_at,
            updated_at
        ) VALUES (
            uuid_generate_v4(),
            test_booking_id,
            test_customer_id,
            2500.00,
            'Pending Verification',
            'GCash',
            'GCASH123456789',
            NOW(),
            'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop', -- Sample receipt image
            NOW(),
            NOW()
        ) ON CONFLICT (booking_id) DO UPDATE SET
            proof_of_payment_url = EXCLUDED.proof_of_payment_url,
            status = 'Pending Verification',
            updated_at = NOW();
        
        -- Insert test document data
        INSERT INTO booking_documents (
            id,
            booking_id,
            document_type,
            file_url,
            file_name,
            file_size,
            file_type,
            verification_status,
            created_at,
            updated_at
        ) VALUES 
        (
            uuid_generate_v4(),
            test_booking_id,
            'drivers_license',
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop', -- Sample driver's license
            'drivers_license.jpg',
            245760,
            'image/jpeg',
            'pending',
            NOW(),
            NOW()
        ),
        (
            uuid_generate_v4(),
            test_booking_id,
            'government_id',
            'https://images.unsplash.com/photo-*************-e1a4e481c6c8?w=800&h=600&fit=crop', -- Sample ID
            'government_id.jpg',
            189432,
            'image/jpeg',
            'pending',
            NOW(),
            NOW()
        ),
        (
            uuid_generate_v4(),
            test_booking_id,
            'proof_of_age',
            'https://images.unsplash.com/photo-*************-e1a4e481c6c8?w=800&h=600&fit=crop', -- Sample document
            'proof_of_age.pdf',
            512000,
            'application/pdf',
            'approved',
            NOW(),
            NOW()
        ),
        (
            uuid_generate_v4(),
            test_booking_id,
            'security_deposit_confirmation',
            'https://images.unsplash.com/photo-**********-6726b3ff858f?w=800&h=600&fit=crop', -- Sample bank receipt
            'security_deposit.jpg',
            320450,
            'image/jpeg',
            'pending',
            NOW(),
            NOW()
        )
        ON CONFLICT (booking_id, document_type) DO UPDATE SET
            file_url = EXCLUDED.file_url,
            verification_status = 'pending',
            updated_at = NOW();
            
        RAISE NOTICE 'Test data added for booking ID: %', test_booking_id;
    ELSE
        RAISE NOTICE 'No pending bookings found to add test data to';
    END IF;
END $$;
