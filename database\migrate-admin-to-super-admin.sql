-- Migrate existing admin account to super admin
-- Updates manuelram858@gmail.<NAME_EMAIL> with super_admin role

BEGIN;

-- Update email in auth.users table
UPDATE auth.users 
SET 
  email = '<EMAIL>',
  updated_at = now()
WHERE email = '<EMAIL>';

-- Update email and role in profiles table
UPDATE public.profiles 
SET 
  email = '<EMAIL>',
  role = 'super_admin',
  full_name = 'Super Admin',
  updated_at = now()
WHERE email = '<EMAIL>';

-- Verify the update
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE email = '<EMAIL>' AND role = 'super_admin'
  ) THEN
    RAISE EXCEPTION 'Migration failed: Super admin account not found after update';
  END IF;
  
  RAISE NOTICE 'Successfully migrated admin account to super admin: <EMAIL>';
END $$;

COMMIT;
