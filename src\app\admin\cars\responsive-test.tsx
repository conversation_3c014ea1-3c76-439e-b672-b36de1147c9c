"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

export default function ResponsiveTest() {
  const breakpoints = [
    { name: "Mobile S", width: "320px" },
    { name: "Mobile M", width: "375px" },
    { name: "Mobile L", width: "425px" },
    { name: "Tablet", width: "768px" },
    { name: "Laptop", width: "1024px" },
    { name: "Desktop", width: "1440px" },
  ];

  return (
    <div className="container mx-auto p-4 space-y-6">
      <h1 className="text-2xl font-bold">Responsive Design Test</h1>
      <p className="text-muted-foreground">
        This page helps verify that the Cars page is responsive across all required breakpoints.
      </p>

      <Card>
        <CardHeader>
          <CardTitle>Current Breakpoint Detection</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="xs:bg-red-100 sm:bg-orange-100 md:bg-yellow-100 lg:bg-green-100 xl:bg-blue-100 2xl:bg-purple-100 p-4 rounded-md border">
                <p className="font-medium">Current breakpoint:</p>
                <p className="xs:block sm:hidden">Mobile S (320px)</p>
                <p className="hidden sm:block md:hidden">Mobile M (375px)</p>
                <p className="hidden md:block lg:hidden">Tablet (768px)</p>
                <p className="hidden lg:block xl:hidden">Laptop (1024px)</p>
                <p className="hidden xl:block 2xl:hidden">Desktop (1280px)</p>
                <p className="hidden 2xl:block">Large Desktop (1440px+)</p>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="font-semibold">Breakpoint Reference:</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
                {breakpoints.map((bp) => (
                  <div key={bp.name} className="border rounded-md p-3 text-center">
                    <p className="font-medium">{bp.name}</p>
                    <p className="text-sm text-muted-foreground">{bp.width}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Responsive Features Implemented</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="list-disc pl-5 space-y-2">
            <li className="line-through text-muted-foreground">Horizontally scrollable tab navigation on small screens</li>
            <li className="font-medium text-emerald-700">Vertical tab navigation for mobile/tablet (320px-768px)</li>
            <li>Floating action button (FAB) for "Add Car" on mobile screens</li>
            <li>Card-style layout for cars on mobile/tablet views (320px-768px)</li>
            <li>Touch-friendly action buttons with minimum 44px height</li>
            <li>Prominent price display on mobile cards</li>
            <li>Status indicators as colored badges top-right of each card</li>
            <li>Table layout preserved for desktop/laptop (1024px+)</li>
            <li>Horizontal tab navigation preserved for desktop/laptop (768px+)</li>
          </ul>
        </CardContent>
      </Card>

      <div className="text-center p-4 text-sm text-muted-foreground">
        <p>Resize your browser window to test different breakpoints</p>
      </div>
    </div>
  );
}
