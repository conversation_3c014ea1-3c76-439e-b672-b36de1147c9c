"use client"

import * as React from "react"
import { 
  EyeIcon, 
  EditIcon, 
  XCircleIcon, 
  ReceiptIcon, 
  ChevronRightIcon,
  CalendarIcon
} from "lucide-react"
import { <PERSON><PERSON>, <PERSON>etContent, SheetHeader, SheetTit<PERSON> } from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface BookingActionDrawerProps {
  booking: {
    id: string
    userName: string
    carModel: string
    status: string
  }
  isOpen: boolean
  onClose: () => void
  onView?: () => void
  onEdit?: () => void
  onCancel?: () => void
  onViewReceipt?: () => void
  onAddToCalendar?: () => void
}

export function BookingActionDrawer({
  booking,
  isOpen,
  onClose,
  onView,
  onEdit,
  onCancel,
  onViewReceipt,
  onAddToCalendar
}: BookingActionDrawerProps) {
  const handleAction = (action: () => void) => {
    action()
    onClose()
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="bottom" className="h-auto max-h-[80vh] rounded-t-xl p-4 sm:p-6">
        <SheetHeader className="mb-4">
          <SheetTitle className="text-center text-xl sm:text-2xl">
            Booking Actions
            <div className="text-xs sm:text-sm font-normal text-gray-500 mt-1 truncate max-w-[280px] sm:max-w-none mx-auto">
              {booking.carModel} • {booking.userName}
            </div>
          </SheetTitle>
        </SheetHeader>
        
        <div className="flex flex-col gap-2">
          {onView && (
            <ActionButton 
              icon={<EyeIcon className="h-5 w-5" />}
              label="View Details"
              onClick={() => handleAction(onView)}
              className="bg-blue-50 text-blue-700 hover:bg-blue-100"
            />
          )}
          
          {onEdit && (
            <ActionButton 
              icon={<EditIcon className="h-5 w-5" />}
              label="Edit Booking"
              onClick={() => handleAction(onEdit)}
              className="bg-amber-50 text-amber-700 hover:bg-amber-100"
            />
          )}
          
          {onCancel && (
            <ActionButton 
              icon={<XCircleIcon className="h-5 w-5" />}
              label="Cancel Booking"
              onClick={() => handleAction(onCancel)}
              className="bg-red-50 text-red-700 hover:bg-red-100"
            />
          )}
          
          {onViewReceipt && (
            <ActionButton 
              icon={<ReceiptIcon className="h-5 w-5" />}
              label="View Receipt"
              onClick={() => handleAction(onViewReceipt)}
              className="bg-green-50 text-green-700 hover:bg-green-100"
            />
          )}
          
          {onAddToCalendar && (
            <ActionButton 
              icon={<CalendarIcon className="h-5 w-5" />}
              label="Add to Calendar"
              onClick={() => handleAction(onAddToCalendar)}
              className="bg-purple-50 text-purple-700 hover:bg-purple-100"
            />
          )}
        </div>
        
        <Button 
          variant="secondary" 
          className="w-full mt-4 h-12 sm:h-14 text-gray-500 font-medium text-base"
          onClick={onClose}
        >
          Close
        </Button>
      </SheetContent>
    </Sheet>
  )
}

interface ActionButtonProps {
  icon: React.ReactNode
  label: string
  onClick: () => void
  className?: string
}

function ActionButton({ icon, label, onClick, className }: ActionButtonProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "flex items-center justify-between w-full p-3 sm:p-4 rounded-lg text-left transition-all",
        "min-h-[50px] sm:min-h-[56px] text-sm sm:text-base font-medium",
        className
      )}
    >
      <div className="flex items-center gap-3">
        {icon}
        <span>{label}</span>
      </div>
      <ChevronRightIcon className="h-5 w-5 opacity-70" />
    </button>
  )
}
