/**
 * Browser Integration Test Suite
 * 
 * Simulates real browser behavior and complex scenarios involving
 * tab management, localStorage corruption, and network errors.
 */

import { render, screen, waitFor, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import React from 'react'
import { createBrowserClient } from '@supabase/ssr'
import { CustomerAuthProvider } from '../../src/components/auth/customer-auth-context'
import { AdminAuthProvider } from '../../src/components/auth/admin-auth-context'

// Mock Supabase client
jest.mock('@supabase/ssr', () => ({
  createBrowserClient: jest.fn()
}))

const mockCreateBrowserClient = createBrowserClient as jest.MockedFunction<typeof createBrowserClient>

// Mock component to test session state
const SessionStatusComponent = ({ type }: { type: 'customer' | 'admin' }) => {
  const [status, setStatus] = React.useState('initializing')
  
  React.useEffect(() => {
    try {
      if (type === 'customer') {
        const { useCustomerAuth } = require('../../src/components/auth/customer-auth-context')
        const auth = useCustomerAuth()
        if (auth.session) {
          setStatus(`Logged in as ${auth.user?.email}`)
        } else {
          setStatus('Not logged in')
        }
      } else {
        const { useAdminAuth } = require('../../src/components/auth/admin-auth-context')
        const auth = useAdminAuth()
        if (auth.session) {
          setStatus(`Logged in as ${auth.user?.email}`)
        } else {
          setStatus('Not logged in')
        }
      }
    } catch (error) {
      setStatus('Context error')
    }
  }, [type])

  return <div data-testid={`${type}-status`}>{status}</div>
}

describe('Browser Integration Tests', () => {
  let mockSupabaseCustomer: any
  let mockSupabaseAdmin: any

  beforeEach(() => {
    // Reset all mocks and clear storage
    jest.clearAllMocks()
    window.localStorage.clear()
    window.sessionStorage.clear()
    
    // Create separate mock clients
    mockSupabaseCustomer = {
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        onAuthStateChange: jest.fn().mockReturnValue({
          data: { subscription: { unsubscribe: jest.fn() } }
        }),
        signInWithPassword: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn().mockResolvedValue({ error: null }),
        resetPasswordForEmail: jest.fn(),
        verifyOtp: jest.fn(),
        resend: jest.fn()
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } })
          })
        })
      })
    }

    mockSupabaseAdmin = {
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        onAuthStateChange: jest.fn().mockReturnValue({
          data: { subscription: { unsubscribe: jest.fn() } }
        }),
        signInWithPassword: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn().mockResolvedValue({ error: null }),
        resetPasswordForEmail: jest.fn(),
        verifyOtp: jest.fn(),
        resend: jest.fn()
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } })
          })
        })
      })
    }

    // Setup createBrowserClient mock
    mockCreateBrowserClient.mockImplementation((url, key, options) => {
      if (options?.auth?.storageKey === 'sb-customer-auth-token') {
        return mockSupabaseCustomer
      } else if (options?.auth?.storageKey === 'sb-admin-auth-token') {
        return mockSupabaseAdmin
      }
      return mockSupabaseCustomer
    })
  })

  test('should handle complete browser session lifecycle', async () => {
    // Phase 1: Fresh browser start (no session)
    await act(async () => {
      render(
        <CustomerAuthProvider>
          <SessionStatusComponent type="customer" />
        </CustomerAuthProvider>
      )
    })

    await waitFor(() => {
      expect(screen.getByTestId('customer-status')).toBeInTheDocument()
    })

    // Phase 2: User signs in
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    // Trigger auth state change
    const onAuthStateChange = mockSupabaseCustomer.auth.onAuthStateChange.mock.calls[0][0]
    await act(async () => {
      onAuthStateChange('SIGNED_IN', customerSession)
    })

    // Should show logged in status
    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
    })

    // Phase 3: User signs out
    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null
    })

    await act(async () => {
      onAuthStateChange('SIGNED_OUT', null)
    })

    // Should clear session
    expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
  })

  test('should handle multiple tabs with same context', async () => {
    // Setup session
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    // Simulate multiple tabs
    await act(async () => {
      render(
        <div>
          <div data-testid="tab1">
            <CustomerAuthProvider>
              <SessionStatusComponent type="customer" />
            </CustomerAuthProvider>
          </div>
          <div data-testid="tab2">
            <CustomerAuthProvider>
              <SessionStatusComponent type="customer" />
            </CustomerAuthProvider>
          </div>
        </div>
      )
    })

    // Both tabs should handle the session independently
    await waitFor(() => {
      expect(screen.getByTestId('tab1')).toBeInTheDocument()
      expect(screen.getByTestId('tab2')).toBeInTheDocument()
    })

    expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
  })

  test('should handle cross-context isolation in multiple tabs', async () => {
    // Setup different sessions
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    const adminSession = {
      access_token: 'admin-token',
      user: { id: 'admin-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseAdmin.auth.getSession.mockResolvedValue({
      data: { session: adminSession },
      error: null
    })

    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    mockSupabaseAdmin.from().select().eq().single.mockResolvedValue({
      data: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
      error: null
    })

    await act(async () => {
      render(
        <div>
          <div data-testid="customer-tab">
            <CustomerAuthProvider>
              <SessionStatusComponent type="customer" />
            </CustomerAuthProvider>
          </div>
          <div data-testid="admin-tab">
            <AdminAuthProvider>
              <SessionStatusComponent type="admin" />
            </AdminAuthProvider>
          </div>
        </div>
      )
    })

    // Both contexts should work independently
    await waitFor(() => {
      expect(screen.getByTestId('customer-tab')).toBeInTheDocument()
      expect(screen.getByTestId('admin-tab')).toBeInTheDocument()
    })
  })

  test('should handle signout in one tab affecting all tabs', async () => {
    // Setup session
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    // Create multiple tabs
    await act(async () => {
      render(
        <div>
          <div data-testid="tab1">
            <CustomerAuthProvider>
              <SessionStatusComponent type="customer" />
            </CustomerAuthProvider>
          </div>
          <div data-testid="tab2">
            <CustomerAuthProvider>
              <SessionStatusComponent type="customer" />
            </CustomerAuthProvider>
          </div>
        </div>
      )
    })

    // Simulate signout in one tab
    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null
    })

    // All auth state change listeners should be notified
    const onAuthStateChangeCallbacks = mockSupabaseCustomer.auth.onAuthStateChange.mock.calls.map(call => call[0])
    
    await act(async () => {
      onAuthStateChangeCallbacks.forEach(callback => {
        callback('SIGNED_OUT', null)
      })
    })

    // All tabs should be affected
    expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
  })

  test('should handle corrupted localStorage data', async () => {
    // Setup corrupted data
    window.localStorage.setItem('sb-customer-auth-token', 'corrupted-json-data')
    window.localStorage.setItem('sb-admin-auth-token', '{"incomplete": json')

    // Should handle gracefully without crashing
    await act(async () => {
      render(
        <div>
          <CustomerAuthProvider>
            <SessionStatusComponent type="customer" />
          </CustomerAuthProvider>
          <AdminAuthProvider>
            <SessionStatusComponent type="admin" />
          </AdminAuthProvider>
        </div>
      )
    })

    // Components should render despite corrupted data
    await waitFor(() => {
      expect(screen.getByTestId('customer-status')).toBeInTheDocument()
      expect(screen.getByTestId('admin-status')).toBeInTheDocument()
    })
  })

  test('should handle network errors during session restoration', async () => {
    // Setup network error
    mockSupabaseCustomer.auth.getSession.mockRejectedValue(new Error('Network error'))
    mockSupabaseAdmin.auth.getSession.mockRejectedValue(new Error('Network error'))

    // Should handle gracefully
    await act(async () => {
      render(
        <div>
          <CustomerAuthProvider>
            <SessionStatusComponent type="customer" />
          </CustomerAuthProvider>
          <AdminAuthProvider>
            <SessionStatusComponent type="admin" />
          </AdminAuthProvider>
        </div>
      )
    })

    // Should not crash despite network errors
    await waitFor(() => {
      expect(screen.getByTestId('customer-status')).toBeInTheDocument()
      expect(screen.getByTestId('admin-status')).toBeInTheDocument()
    })
  })

  test('should handle rapid tab switching', async () => {
    // Setup session
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    // Rapidly mount and unmount (simulating tab switching)
    for (let i = 0; i < 3; i++) {
      const { unmount } = await act(async () => {
        return render(
          <CustomerAuthProvider>
            <SessionStatusComponent type="customer" />
          </CustomerAuthProvider>
        )
      })
      
      // Quick unmount
      unmount()
    }

    // Final mount should work
    await act(async () => {
      render(
        <CustomerAuthProvider>
          <SessionStatusComponent type="customer" />
        </CustomerAuthProvider>
      )
    })

    await waitFor(() => {
      expect(screen.getByTestId('customer-status')).toBeInTheDocument()
    })
  })

  test('should handle browser refresh simulation', async () => {
    // Setup existing session in localStorage
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    window.localStorage.setItem('sb-customer-auth-token', JSON.stringify({
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }))

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    // Simulate browser refresh by creating new component instance
    await act(async () => {
      render(
        <CustomerAuthProvider>
          <SessionStatusComponent type="customer" />
        </CustomerAuthProvider>
      )
    })

    // Should restore session from storage
    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
    })

    await waitFor(() => {
      expect(screen.getByTestId('customer-status')).toBeInTheDocument()
    })
  })
})
