"use client"

import * as React from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Home, Phone, Mail, ArrowLeft, ExternalLink } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"

export default function NotFoundPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = React.useState("")
  const [isMounted, setIsMounted] = React.useState(false)

  React.useEffect(() => {
    setIsMounted(true)
  }, [])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // Redirect to catalog with search query
      router.push(`/customer/catalog?search=${encodeURIComponent(searchQuery.trim())}`)
    }
  }

  const handleGoBack = () => {
    if (isMounted && window.history.length > 1) {
      router.back()
    } else {
      router.push('/')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-gray-50 flex items-center justify-center px-4">
      <Card className="w-full max-w-2xl shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="text-center pb-6">
          {/* Logo */}
          <div className="flex justify-center mb-6">
            <Image
              src="/ollie_logo.svg"
              alt="Ollie Track"
              width={150}
              height={50}
              className="h-12 w-auto"
            />
          </div>

          {/* 404 Visual */}
          <div className="mb-6">
            <div className="text-8xl font-bold text-blue-600 mb-2 tracking-tight">
              404
            </div>
            <div className="text-2xl font-semibold text-gray-900 mb-3">
              Page Not Found
            </div>
            <div className="text-base text-gray-600 max-w-md mx-auto leading-relaxed">
              Oops! The page you're looking for seems to have taken a detour. 
              It might have been moved, deleted, or you may have entered an incorrect URL.
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Search Bar */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-blue-900 mb-3 flex items-center gap-2">
              <Search className="h-4 w-4" />
              Looking for something specific?
            </h3>
            <form onSubmit={handleSearch} className="flex gap-2">
              <Input
                type="text"
                placeholder="Search for vehicles, pages, or features..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 h-10 bg-white border-blue-200 focus:border-blue-400 focus:ring-blue-400"
              />
              <Button 
                type="submit" 
                className="h-10 bg-blue-600 hover:bg-blue-700 px-4"
                disabled={!searchQuery.trim()}
              >
                <Search className="h-4 w-4" />
              </Button>
            </form>
          </div>

          {/* Navigation Links */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Homepage Link */}
            <Link href="/" className="group">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg p-4 hover:from-blue-600 hover:to-blue-700 transition-all duration-200 transform hover:scale-105 shadow-md">
                <div className="flex items-center gap-3">
                  <div className="bg-white/20 rounded-full p-2">
                    <Home className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="font-semibold">Go to Homepage</div>
                    <div className="text-sm text-blue-100">
                      Browse our vehicle catalog
                    </div>
                  </div>
                  <ExternalLink className="h-4 w-4 ml-auto opacity-70 group-hover:opacity-100" />
                </div>
              </div>
            </Link>

            {/* Contact Link */}
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg p-4 hover:from-green-600 hover:to-green-700 transition-all duration-200 transform hover:scale-105 shadow-md cursor-pointer group">
              <div className="flex items-center gap-3">
                <div className="bg-white/20 rounded-full p-2">
                  <Phone className="h-5 w-5" />
                </div>
                <div>
                  <div className="font-semibold">Need Help?</div>
                  <div className="text-sm text-green-100">
                    Contact our support team
                  </div>
                </div>
                <ExternalLink className="h-4 w-4 ml-auto opacity-70 group-hover:opacity-100" />
              </div>
            </div>
          </div>

          {/* Additional Quick Links */}
          <div className="border-t border-gray-200 pt-6">
            <h4 className="text-sm font-semibold text-gray-700 mb-4 text-center">
              Or try these popular pages:
            </h4>
            <div className="flex flex-wrap justify-center gap-2">
              <Link href="/customer/catalog">
                <Button variant="secondary" size="sm" className="h-8 text-xs">
                  Vehicle Catalog
                </Button>
              </Link>
              <Link href="/customer/dashboard">
                <Button variant="secondary" size="sm" className="h-8 text-xs">
                  Dashboard
                </Button>
              </Link>
              <Link href="/customer/faq">
                <Button variant="secondary" size="sm" className="h-8 text-xs">
                  FAQ
                </Button>
              </Link>
              <Link href="/customer/terms">
                <Button variant="secondary" size="sm" className="h-8 text-xs">
                  Terms & Conditions
                </Button>
              </Link>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-gray-50 rounded-lg p-4 text-center">
            <h4 className="text-sm font-semibold text-gray-700 mb-2">
              Still need assistance?
            </h4>
            <div className="flex items-center justify-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Phone className="h-4 w-4" />
                <span>+639998810866</span>
              </div>
              <div className="flex items-center gap-1">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Go Back Button */}
          <div className="flex justify-center pt-2">
            <Button 
              onClick={handleGoBack}
              variant="tertiary" 
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
