// Database operations for renter issue tracking
import { createClient } from "./client";
import type {
  RenterStatus,
  IssueCategory,
  RenterIssue,
  RenterCategoryTag,
  RenterBehaviorSummary,
  IssueSeverity,
} from "@/lib/types";

const supabase = createClient();

// ============================================
// Renter Status Operations
// ============================================

export async function getRenterStatus(
  customerId: string
): Promise<RenterStatus | null> {
  const { data, error } = await supabase
    .from("renter_status")
    .select("*")
    .eq("customer_id", customerId)
    .single();

  if (error && error.code !== "PGRST116") {
    console.warn("getRenterStatus: returning null due to error:", error);
    return null;
  }

  return data;
}

export async function setRenterStatus(
  customerId: string,
  statusTagline: string,
  adminId: string
): Promise<RenterStatus> {
  const { data, error } = await supabase
    .from("renter_status")
    .upsert(
      {
        customer_id: customerId,
        status_tagline: statusTagline,
        created_by: adminId,
        updated_at: new Date().toISOString(),
      },
      {
        onConflict: "customer_id",
      }
    )
    .select()
    .single();

  if (error) {
    console.error("Error setting renter status:", error);
    throw error;
  }

  return data;
}

export async function removeRenterStatus(customerId: string): Promise<void> {
  const { error } = await supabase
    .from("renter_status")
    .delete()
    .eq("customer_id", customerId);

  if (error) {
    console.error("Error removing renter status:", error);
    throw error;
  }
}

// ============================================
// Issue Categories Operations
// ============================================

export async function getIssueCategories(): Promise<IssueCategory[]> {
  const { data, error } = await supabase
    .from("issue_categories")
    .select("*")
    .eq("is_active", true)
    .order("name");

  if (error) {
    console.warn("getIssueCategories: returning [] due to error:", error);
    return [];
  }

  return data || [];
}

export async function createIssueCategory(
  name: string,
  description?: string,
  color: string = "#6B7280"
): Promise<IssueCategory> {
  const { data, error } = await supabase
    .from("issue_categories")
    .insert({
      name,
      description,
      color,
    })
    .select()
    .single();

  if (error) {
    console.error("Error creating issue category:", error);
    throw error;
  }

  return data;
}

// ============================================
// Renter Issues Operations
// ============================================

export async function getRenterIssues(
  customerId: string
): Promise<RenterIssue[]> {
  const { data, error } = await supabase
    .from("renter_issues")
    .select(
      `
      *,
      category:issue_categories(*),
      booking:bookings(*),
      created_by_admin:profiles!renter_issues_created_by_fkey(*),
      resolved_by_admin:profiles!renter_issues_resolved_by_fkey(*)
    `
    )
    .eq("customer_id", customerId)
    .order("created_at", { ascending: false });

  if (error) {
    console.warn("getRenterIssues: returning [] due to error:", error);
    return [];
  }

  return data || [];
}

export async function createRenterIssue({
  customerId,
  categoryId,
  bookingId,
  description,
  severity = "Medium",
  adminId,
}: {
  customerId: string;
  categoryId: string;
  bookingId?: string;
  description: string;
  severity?: IssueSeverity;
  adminId: string;
}): Promise<RenterIssue> {
  const { data, error } = await supabase
    .from("renter_issues")
    .insert({
      customer_id: customerId,
      category_id: categoryId,
      booking_id: bookingId,
      description,
      severity,
      created_by: adminId,
    })
    .select(
      `
      *,
      category:issue_categories(*),
      booking:bookings(*),
      created_by_admin:profiles!renter_issues_created_by_fkey(*)
    `
    )
    .single();

  if (error) {
    console.error("Error creating renter issue:", error);
    throw error;
  }

  return data;
}

export async function updateRenterIssue(
  issueId: string,
  updates: Partial<{
    description: string;
    severity: IssueSeverity;
    resolved: boolean;
    resolution_notes: string;
    resolved_by: string;
  }>
): Promise<RenterIssue> {
  const updateData: any = { ...updates };

  // If marking as resolved, add timestamp
  if (updates.resolved === true) {
    updateData.resolved_at = new Date().toISOString();
  } else if (updates.resolved === false) {
    updateData.resolved_at = null;
    updateData.resolution_notes = null;
    updateData.resolved_by = null;
  }

  const { data, error } = await supabase
    .from("renter_issues")
    .update(updateData)
    .eq("id", issueId)
    .select(
      `
      *,
      category:issue_categories(*),
      booking:bookings(*),
      created_by_admin:profiles!renter_issues_created_by_fkey(*),
      resolved_by_admin:profiles!renter_issues_resolved_by_fkey(*)
    `
    )
    .single();

  if (error) {
    console.error("Error updating renter issue:", error);
    throw error;
  }

  return data;
}

export async function deleteRenterIssue(issueId: string): Promise<void> {
  const { error } = await supabase
    .from("renter_issues")
    .delete()
    .eq("id", issueId);

  if (error) {
    console.error("Error deleting renter issue:", error);
    throw error;
  }
}

// ============================================
// Renter Category Tags Operations
// ============================================

export async function getRenterCategoryTags(
  customerId: string
): Promise<RenterCategoryTag[]> {
  const { data, error } = await supabase
    .from("renter_category_tags")
    .select(
      `
      *,
      category:issue_categories(*),
      tagged_by_admin:profiles!renter_category_tags_tagged_by_fkey(*)
    `
    )
    .eq("customer_id", customerId)
    .order("tagged_at", { ascending: false });

  if (error) {
    console.warn("getRenterCategoryTags: returning [] due to error:", error);
    return [];
  }

  return data || [];
}

export async function addRenterCategoryTag(
  customerId: string,
  categoryId: string,
  adminId: string
): Promise<RenterCategoryTag> {
  const { data, error } = await supabase
    .from("renter_category_tags")
    .insert({
      customer_id: customerId,
      category_id: categoryId,
      tagged_by: adminId,
    })
    .select(
      `
      *,
      category:issue_categories(*),
      tagged_by_admin:profiles!renter_category_tags_tagged_by_fkey(*)
    `
    )
    .single();

  if (error) {
    console.error("Error adding renter category tag:", error);
    throw error;
  }

  return data;
}

export async function removeRenterCategoryTag(
  customerId: string,
  categoryId: string
): Promise<void> {
  const { error } = await supabase
    .from("renter_category_tags")
    .delete()
    .eq("customer_id", customerId)
    .eq("category_id", categoryId);

  if (error) {
    console.error("Error removing renter category tag:", error);
    throw error;
  }
}

// ============================================
// Behavior Summary Operations
// ============================================

export async function getRenterBehaviorSummary(
  customerId: string
): Promise<RenterBehaviorSummary> {
  const { data, error } = await supabase.rpc("get_renter_behavior_summary", {
    renter_id: customerId,
  });

  if (error) {
    console.warn(
      "getRenterBehaviorSummary: returning default summary due to error:",
      error
    );
    return {
      total_bookings: 0,
      completed_bookings: 0,
      cancelled_bookings: 0,
      overdue_bookings: 0,
      total_issues: 0,
      unresolved_issues: 0,
      issue_categories: [],
    };
  }

  return (
    data || {
      total_bookings: 0,
      completed_bookings: 0,
      cancelled_bookings: 0,
      overdue_bookings: 0,
      total_issues: 0,
      unresolved_issues: 0,
      issue_categories: [],
    }
  );
}

// ============================================
// Bulk Operations
// ============================================

export async function getRenterProfileWithIssues(customerId: string) {
  const [status, categoryTags, issues, behaviorSummary] = await Promise.all([
    getRenterStatus(customerId),
    getRenterCategoryTags(customerId),
    getRenterIssues(customerId),
    getRenterBehaviorSummary(customerId),
  ]);

  return {
    status,
    category_tags: categoryTags,
    recent_issues: issues.slice(0, 5), // Last 5 issues
    behavior_summary: behaviorSummary,
  };
}

// ============================================
// Utility Functions
// ============================================

export function getIssueSeverityColor(severity: IssueSeverity): string {
  switch (severity) {
    case "Low":
      return "#10B981"; // green
    case "Medium":
      return "#F59E0B"; // yellow
    case "High":
      return "#EF4444"; // red
    case "Critical":
      return "#DC2626"; // dark red
    default:
      return "#6B7280"; // gray
  }
}

export function getIssueSeverityBadgeVariant(
  severity: IssueSeverity
): "default" | "secondary" | "destructive" | "outline" {
  switch (severity) {
    case "Low":
      return "secondary";
    case "Medium":
      return "default";
    case "High":
      return "destructive";
    case "Critical":
      return "destructive";
    default:
      return "outline";
  }
}
