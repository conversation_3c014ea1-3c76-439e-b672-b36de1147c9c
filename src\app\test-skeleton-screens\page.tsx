"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import {
  CustomerDashboardSkeleton,
  CustomerBookingFlowSkeleton,
  CustomerVehicleCatalogSkeleton,
  CustomerSettingsSkeleton,
  CustomerUnifiedTableSkeleton
} from "@/components/customer-side/loading/skeleton-components"
import {
  AdminDashboardSkeleton,
  AdminBookingsSkeleton,
  AdminCarsSkeleton,
  AdminPaymentsSkeleton,
  AdminSettingsSkeleton,
  AdminTrackerSkeleton,
  AdminModalSkeleton,
  AdminDrawerSkeleton
} from "@/components/admin/loading/skeleton-components"
import { Monitor, Tablet, Smartphone, Check, X } from "lucide-react"

export default function SkeletonScreensTestPage() {
  const [currentView, setCurrentView] = React.useState<"base" | "customer" | "admin">("base")
  const [showShimmer, setShowShimmer] = React.useState(true)
  const [currentBreakpoint, setCurrentBreakpoint] = React.useState<string>("lg")

  // Detect current breakpoint
  React.useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth
      if (width < 320) setCurrentBreakpoint("xs")
      else if (width < 640) setCurrentBreakpoint("sm") 
      else if (width < 768) setCurrentBreakpoint("md")
      else if (width < 1024) setCurrentBreakpoint("lg")
      else if (width < 1280) setCurrentBreakpoint("xl")
      else setCurrentBreakpoint("2xl")
    }

    updateBreakpoint()
    window.addEventListener("resize", updateBreakpoint)
    return () => window.removeEventListener("resize", updateBreakpoint)
  }, [])

  const breakpoints = [
    { name: "xs", label: "Mobile S", width: "320px", icon: Smartphone },
    { name: "sm", label: "Mobile M", width: "640px", icon: Smartphone },
    { name: "md", label: "Mobile L", width: "768px", icon: Tablet },
    { name: "lg", label: "Tablet", width: "1024px", icon: Tablet },
    { name: "xl", label: "Laptop", width: "1280px", icon: Monitor },
    { name: "2xl", label: "Desktop", width: "1440px+", icon: Monitor },
  ]

  const customerSkeletons = [
    { name: "Dashboard", component: <CustomerDashboardSkeleton /> },
    { name: "Booking Flow", component: <CustomerBookingFlowSkeleton /> },
    { name: "Vehicle Catalog", component: <CustomerVehicleCatalogSkeleton /> },
    { name: "Settings", component: <CustomerSettingsSkeleton /> },
    { name: "Unified Table", component: <CustomerUnifiedTableSkeleton /> },
  ]

  const adminSkeletons = [
    { name: "Dashboard", component: <AdminDashboardSkeleton /> },
    { name: "Bookings", component: <AdminBookingsSkeleton /> },
    { name: "Cars", component: <AdminCarsSkeleton /> },
    { name: "Payments", component: <AdminPaymentsSkeleton /> },
    { name: "Settings", component: <AdminSettingsSkeleton /> },
    { name: "GPS Tracker", component: <AdminTrackerSkeleton /> },
    { name: "Modal", component: <AdminModalSkeleton /> },
    { name: "Drawer", component: <AdminDrawerSkeleton /> },
  ]

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Header */}
      <div className="max-w-7xl mx-auto mb-8">
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <CardTitle className="text-2xl font-bold">Skeleton Screens Test Suite</CardTitle>
                <p className="text-muted-foreground mt-2">
                  Comprehensive testing of animated skeleton components across all breakpoints and pages
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  variant={showShimmer ? "primary" : "secondary"}
                  onClick={() => setShowShimmer(!showShimmer)}
                  size="sm"
                >
                  {showShimmer ? "Shimmer On" : "Pulse Only"}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Current Breakpoint Indicator */}
            <div className="mb-6">
              <h3 className="font-semibold mb-3">Current Breakpoint</h3>
              <div className="flex flex-wrap gap-2">
                {breakpoints.map((bp) => {
                  const Icon = bp.icon
                  const isCurrent = bp.name === currentBreakpoint
                  return (
                    <div
                      key={bp.name}
                      className={`flex items-center gap-2 px-3 py-2 rounded-lg border ${
                        isCurrent 
                          ? "bg-blue-100 border-blue-300 text-blue-800" 
                          : "bg-gray-100 border-gray-300 text-gray-600"
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      <span className="font-medium">{bp.label}</span>
                      <span className="text-sm">({bp.width})</span>
                      {isCurrent && <Check className="h-4 w-4" />}
                    </div>
                  )
                })}
              </div>
            </div>

            {/* View Selector */}
            <div className="mb-6">
              <h3 className="font-semibold mb-3">Test Categories</h3>
              <div className="flex gap-2">
                <Button
                  variant={currentView === "base" ? "primary" : "secondary"}
                  onClick={() => setCurrentView("base")}
                  size="sm"
                >
                  Base Components
                </Button>
                <Button
                  variant={currentView === "customer" ? "primary" : "secondary"}
                  onClick={() => setCurrentView("customer")}
                  size="sm"
                >
                  Customer Pages
                </Button>
                <Button
                  variant={currentView === "admin" ? "primary" : "secondary"}
                  onClick={() => setCurrentView("admin")}
                  size="sm"
                >
                  Admin Pages
                </Button>
              </div>
            </div>

            {/* Accessibility Checklist */}
            <div className="mb-6">
              <h3 className="font-semibold mb-3">Accessibility Checklist</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  "ARIA role attributes present",
                  "Screen reader labels provided", 
                  "Loading state announced",
                  "Focus management maintained",
                  "Reduced motion supported",
                  "Color contrast adequate"
                ].map((item, i) => (
                  <div key={i} className="flex items-center gap-2 text-sm">
                    <Check className="h-4 w-4 text-green-600" />
                    <span>{item}</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Content */}
      <div className="max-w-7xl mx-auto">
        {currentView === "base" && (
          <div className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Base Skeleton Components</CardTitle>
              </CardHeader>
              <CardContent className="space-y-8">
                {/* Basic Skeleton */}
                <div>
                  <h4 className="font-semibold mb-4">Basic Skeleton</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Skeleton className="h-4" />
                    <Skeleton className="h-6" />
                    <Skeleton className="h-8 w-32" />
                  </div>
                </div>

                {/* Skeleton Text */}
                <div>
                  <h4 className="font-semibold mb-4">Skeleton Text</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                    </div>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-4/5" />
                      <Skeleton className="h-4 w-3/5" />
                    </div>
                  </div>
                </div>

                {/* Skeleton Buttons */}
                <div>
                  <h4 className="font-semibold mb-4">Skeleton Buttons</h4>
                  <div className="flex flex-wrap gap-4">
                    <Skeleton className="h-8 w-20" />
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-12 w-28" />
                  </div>
                </div>

                {/* Skeleton Avatars */}
                <div>
                  <h4 className="font-semibold mb-4">Skeleton Avatars</h4>
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <Skeleton className="h-12 w-12 rounded-full" />
                  </div>
                </div>

                {/* Skeleton Cards */}
                <div>
                  <h4 className="font-semibold mb-4">Skeleton Cards</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="rounded-lg border bg-card p-6 space-y-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2 flex-1">
                          <Skeleton className="h-5 w-3/4" />
                          <Skeleton className="h-4 w-1/2" />
                        </div>
                        <Skeleton className="h-6 w-16" />
                      </div>
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-4/5" />
                        <Skeleton className="h-4 w-3/5" />
                      </div>
                    </div>
                    <div className="rounded-lg border bg-card p-6 space-y-4">
                      <Skeleton className="h-48 w-full rounded-md" />
                      <div className="space-y-2">
                        <Skeleton className="h-5 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                      <div className="flex gap-2 pt-2 border-t">
                        <Skeleton className="h-10 flex-1" />
                        <Skeleton className="h-10 flex-1" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Skeleton Form */}
                <div>
                  <h4 className="font-semibold mb-4">Skeleton Form</h4>
                  <div className="max-w-md space-y-6">
                    {Array.from({ length: 4 }, (_, i) => (
                      <div key={i} className="space-y-2">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-10 w-full" />
                      </div>
                    ))}
                    <div className="pt-4">
                      <Skeleton className="h-10 w-full" />
                    </div>
                  </div>
                </div>

                {/* Skeleton Table */}
                <div>
                  <h4 className="font-semibold mb-4">Skeleton Table</h4>
                  <div className="space-y-3">
                    {/* Header */}
                    <div className="grid grid-cols-5 gap-4 p-4 bg-muted/50 rounded-lg">
                      {Array.from({ length: 5 }, (_, i) => (
                        <Skeleton key={i} className="h-4" />
                      ))}
                    </div>
                    {/* Rows */}
                    <div className="space-y-1">
                      {Array.from({ length: 6 }, (_, i) => (
                        <div key={i} className="grid grid-cols-5 gap-4 p-4 border rounded-lg">
                          {Array.from({ length: 5 }, (_, j) => (
                            <Skeleton key={j} className="h-4" />
                          ))}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {currentView === "customer" && (
          <div className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Customer Page Skeletons</CardTitle>
                <p className="text-muted-foreground">
                  Testing skeleton screens for all customer-facing pages
                </p>
              </CardHeader>
            </Card>
            {customerSkeletons.map((skeleton, i) => (
              <Card key={i}>
                <CardHeader>
                  <CardTitle className="text-lg">{skeleton.name} Skeleton</CardTitle>
                </CardHeader>
                <CardContent className="p-0 overflow-hidden">
                  {skeleton.component}
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {currentView === "admin" && (
          <div className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Admin Page Skeletons</CardTitle>
                <p className="text-muted-foreground">
                  Testing skeleton screens for all admin dashboard pages
                </p>
              </CardHeader>
            </Card>
            {adminSkeletons.map((skeleton, i) => (
              <Card key={i}>
                <CardHeader>
                  <CardTitle className="text-lg">{skeleton.name} Skeleton</CardTitle>
                </CardHeader>
                <CardContent className="p-0 overflow-hidden">
                  {skeleton.component}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="max-w-7xl mx-auto mt-12">
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">
              <p className="mb-2">
                <strong>Testing Instructions:</strong>
              </p>
              <div className="text-left max-w-2xl mx-auto space-y-2">
                <p>1. Resize browser window to test all breakpoints (320px to 1440px+)</p>
                <p>2. Verify skeleton animations work smoothly on all screen sizes</p>
                <p>3. Test with screen readers to ensure accessibility</p>
                <p>4. Check for layout shifts or visual inconsistencies</p>
                <p>5. Validate color contrast meets accessibility standards</p>
                <p>6. Ensure skeleton components match the visual hierarchy of real content</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
