# Booking Extension Feature Documentation

## Overview

The Booking Extension feature allows customers to request extensions for their active bookings and enables admins to review, approve, or deny these requests. The system includes availability checking, conflict detection, alternative car suggestions, and comprehensive notification management.

## Feature Components

### 1. Database Schema

#### Tables Created:
- `booking_extensions` - Stores extension requests with status, conflicts, and alternatives
- `booking_extension_notifications` - Manages real-time notifications for extension events

#### Key Fields:
- Extension duration, additional costs, conflict detection
- Alternative car suggestions when conflicts exist
- Admin review notes and decision tracking
- Automatic expiration after 24 hours

### 2. Backend Services

#### `booking-extension-service.ts`
- **checkExtensionAvailability()** - Validates extension periods and detects conflicts
- **findAlternativeCars()** - Suggests available alternatives during conflict periods
- **createExtensionRequest()** - Creates new extension requests with proper validation
- **reviewExtensionRequest()** - Admin approval/denial with automatic booking updates
- **getPendingExtensionRequests()** - Retrieves requests for admin review

#### `extension-notification-service.ts`
- Real-time notification management with Supabase subscriptions
- Email integration for approval/denial notifications
- Unread count tracking and bulk operations
- User-specific notification filtering (customer/admin)

### 3. Customer-Side Components

#### `ExtensionRequestModal`
Multi-step modal workflow:
1. **Request Step** - Date/time selection with optional reason
2. **Availability Check** - Real-time conflict detection
3. **Alternatives** - Alternative car suggestions if conflicts exist
4. **Confirmation** - Final submission with admin approval notice

Features:
- Responsive design with mobile support
- Real-time availability validation
- Clear conflict visualization
- Alternative car comparison with pricing

#### Dashboard Integration
- Extension request button in booking details modal (Active bookings only)
- Extension request option in unified dashboard table
- Seamless integration with existing booking workflows

### 4. Admin-Side Components

#### `ExtensionApprovalDrawer`
Comprehensive review interface:
- Complete request details with customer information
- Original vs requested booking periods comparison
- Conflict visualization with customer details
- Alternative car suggestions display
- Admin notes and rejection reason fields
- One-click approval/denial with confirmation

#### `ExtensionRequestsSection`
Admin dashboard integration:
- Filterable list of all extension requests
- Real-time status updates with auto-refresh
- Mobile-responsive card layout
- Statistics dashboard with pending/conflict counts
- Bulk operations and search functionality

### 5. Notification System

#### Real-time Notifications
- WebSocket-based real-time updates via Supabase
- Customer notifications for approval/denial decisions
- Admin notifications for new extension requests
- Automatic email notifications for status changes

#### Notification Types:
- `extension_requested` - New request notification for admins
- `extension_approved` - Approval notification for customers
- `extension_rejected` - Rejection notification with reason
- `extension_expired` - Auto-expiration notification

## Business Logic

### Extension Request Flow

1. **Customer Request**
   - Customer selects new return date/time for active booking
   - System validates extension period (minimum 1 hour extension)
   - Real-time availability check against existing bookings
   - Conflict detection for the same vehicle during extension period

2. **Availability Check**
   - Query overlapping bookings for the same car
   - Calculate additional cost based on hourly/daily rates
   - If conflicts exist, suggest alternative vehicles
   - Present clear options to customer

3. **Request Submission**
   - Create extension record with "pending" status
   - Generate admin notification
   - Set 24-hour expiration timer
   - Send confirmation to customer

4. **Admin Review**
   - Admin views request details and conflict information
   - Review alternative suggestions if applicable
   - Add admin notes for context
   - Approve or reject with optional reason

5. **Status Resolution**
   - **Approved**: Update original booking dropoff time and total amount
   - **Rejected**: Send notification with admin reason
   - **Expired**: Auto-reject after 24 hours if not reviewed

### Conflict Resolution

When conflicts are detected:
1. System identifies overlapping bookings for the same vehicle
2. Finds alternative vehicles available during extension period
3. Calculates pricing for alternative options
4. Presents alternatives with clear comparison
5. Customer can choose alternative or cancel request
6. Admin can review alternative selections during approval

### Cost Calculation

Extension costs calculated as:
- **Hourly Rate**: `(car.price_per_day / 24) * extension_hours`
- **Daily Rate**: If extension > 12 hours, use daily rate calculations
- **Location Fees**: Apply pickup/return location fees if applicable
- **Total**: Original booking amount + extension amount

## Security & Permissions

### Row Level Security (RLS)
- Customers can only view/create their own extension requests
- Admins have full access to all extension requests
- Notifications are user-scoped with proper access control

### Data Validation
- Extension period must be at least 1 hour in the future
- Cannot extend completed or cancelled bookings
- Maximum extension period of 30 days
- Rate limiting on extension requests (max 3 per booking)

## Integration Points

### Email System
- Integrates with existing Supabase Edge Functions for email notifications
- Template-based emails for approval/rejection notifications
- Includes booking details and next steps for customers

### Payment System
- Automatic payment adjustment for approved extensions
- Integration with existing payment processing
- Clear cost breakdown in notifications and receipts

### Calendar System
- Updates existing booking calendars
- Prevents double-booking conflicts
- Maintains scheduling integrity

## Configuration

### Environment Variables
```bash
# Supabase configuration (existing)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key

# Email service configuration (existing)
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Feature Flags
- Extension requests can be enabled/disabled per vehicle type
- Maximum extension duration configurable per car category
- Notification preferences per user type

## Monitoring & Analytics

### Key Metrics
- Extension request volume and approval rates
- Average extension duration and costs
- Conflict rates and alternative acceptance
- Customer satisfaction with extension process

### Error Monitoring
- Failed availability checks
- Notification delivery failures
- Payment processing errors for extensions
- Database constraint violations

## Testing Strategy

### Unit Tests
- Extension availability logic
- Cost calculation accuracy
- Conflict detection algorithms
- Notification service methods

### Integration Tests
- End-to-end extension request flow
- Admin approval/rejection workflow
- Real-time notification delivery
- Database integrity and RLS policies

### User Acceptance Tests
- Customer extension request journey
- Admin review and decision process
- Mobile responsiveness and accessibility
- Email notification delivery and content

## Deployment

### Database Migration
1. Run the booking-extensions-schema.sql migration
2. Verify RLS policies are active
3. Test notification triggers and functions
4. Validate data integrity constraints

### Feature Rollout
1. Deploy backend services and database changes
2. Release admin interface for staff training
3. Enable customer-facing extension requests
4. Monitor system performance and error rates

### Rollback Plan
- Feature flags to disable extension requests
- Database rollback scripts for schema changes
- Component-level rollbacks for UI updates
- Notification service bypass options

## Future Enhancements

### Planned Features
- Bulk extension operations for fleet management
- Automated extension approvals based on rules
- Integration with third-party calendar systems
- Advanced analytics and reporting dashboard

### Technical Improvements
- Caching layer for availability checks
- Background jobs for notification processing
- Advanced conflict resolution algorithms
- Mobile app push notifications

## Support & Troubleshooting

### Common Issues
1. **Extension not showing for active booking**
   - Verify booking status is exactly "Active"
   - Check user permissions and RLS policies
   - Ensure booking is not already extended

2. **Availability check failures**
   - Verify date/time format and timezone handling
   - Check car availability data integrity
   - Validate booking period calculations

3. **Notification delivery problems**
   - Check Supabase real-time connection
   - Verify email service configuration
   - Review notification trigger functions

### Debug Commands
```sql
-- Check extension request status
SELECT * FROM booking_extensions WHERE booking_id = 'booking_id';

-- Verify notification delivery
SELECT * FROM booking_extension_notifications 
WHERE recipient_id = 'user_id' 
ORDER BY created_at DESC;

-- Check booking conflicts
SELECT * FROM bookings 
WHERE car_id = 'car_id' 
AND pickup_datetime <= 'end_time' 
AND dropoff_datetime >= 'start_time';
```

## API Reference

### Extension Service Methods

```typescript
// Check extension availability
await checkExtensionAvailability(bookingId, requestedDropoffDatetime);

// Create extension request
await createExtensionRequest(bookingId, requestedDropoffDatetime, reason, notes);

// Review extension request (admin)
await reviewExtensionRequest(extensionId, action, adminId, adminNotes, rejectionReason);

// Get pending requests (admin)
await getPendingExtensionRequests();
```

### Notification Service Methods

```typescript
// Get user notifications
await extensionNotificationService.getNotifications(userId, userType);

// Mark as read
await extensionNotificationService.markAsRead(notificationId);

// Subscribe to real-time updates
const unsubscribe = extensionNotificationService.subscribeToNotifications(
  userId, userType, onNotification, onError
);
```

## Conclusion

The Booking Extension feature provides a comprehensive solution for managing booking extensions with proper conflict detection, administrative oversight, and user-friendly interfaces. The implementation follows best practices for security, scalability, and maintainability while integrating seamlessly with the existing OllieTrack system.

For additional support or feature requests, refer to the development team or create an issue in the project repository.
