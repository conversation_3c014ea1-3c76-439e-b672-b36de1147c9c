"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MobileNavDrawer } from "@/components/admin/settings/mobile-nav-drawer";
import { SettingsModal } from "@/components/admin/settings/settings-modal";
import { Building2, Brush, Clock, CreditCard, FileText, Home, Settings, Users } from "lucide-react";
import { cn } from "@/lib/utils";
import { useMediaQuery } from "@/hooks/use-mobile";
import type { SettingsSection } from "@/lib/types";

export default function ResponsiveTestPage() {
  const [activeSection, setActiveSection] = React.useState<SettingsSection>("organization");
  const [modalOpen, setModalOpen] = React.useState(false);
  const [dirtyState, setDirtyState] = React.useState<Record<string, boolean>>({});
  const [viewportSize, setViewportSize] = React.useState({ width: 0, height: 0 });
  const [breakpoint, setBreakpoint] = React.useState("");
  const isMobile = useMediaQuery("(max-width: 1023px)");

  const sidebarItems = [
    {
      id: "organization",
      label: "Organization & Branding",
      icon: Building2,
    },
    {
      id: "branches",
      label: "Branches Management",
      icon: Home,
    },
    {
      id: "pricing",
      label: "Pricing & Fees",
      icon: CreditCard,
    },
    {
      id: "policies",
      label: "Rental Policies",
      icon: FileText,
    },
    {
      id: "theme",
      label: "Theme & Appearance",
      icon: Brush,
    },
    {
      id: "audit",
      label: "Audit Log",
      icon: Clock,
    },
    {
      id: "users",
      label: "User Management",
      icon: Users,
    },
  ] as const;

  const handleOpenSettingsModal = React.useCallback(() => {
    if (isMobile) {
      setModalOpen(true);
    }
  }, [isMobile]);

  const toggleDirtyState = (sectionId: string) => {
    setDirtyState((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId],
    }));
  };

  React.useEffect(() => {
    const updateSize = () => {
      setViewportSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });

      if (window.innerWidth < 375) {
        setBreakpoint("xs (< 375px)");
      } else if (window.innerWidth < 640) {
        setBreakpoint("sm (375px - 639px)");
      } else if (window.innerWidth < 768) {
        setBreakpoint("md (640px - 767px)");
      } else if (window.innerWidth < 1024) {
        setBreakpoint("lg (768px - 1023px)");
      } else if (window.innerWidth < 1280) {
        setBreakpoint("xl (1024px - 1279px)");
      } else {
        setBreakpoint("2xl (≥ 1280px)");
      }
    };

    updateSize();
    window.addEventListener("resize", updateSize);
    return () => window.removeEventListener("resize", updateSize);
  }, []);

  return (
    <div className="space-y-4 p-4">
      <header className="flex justify-between items-center">
        <div className="space-y-2">
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-foreground">
            Responsive Test
          </h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            Test the responsive behavior of the settings page
          </p>
        </div>
        <MobileNavDrawer
          sidebarItems={sidebarItems}
          activeSection={activeSection}
          setActiveSection={setActiveSection}
          dirtyState={dirtyState}
          onOpenSettingsModal={handleOpenSettingsModal}
        />
      </header>

      {/* Viewport Size Indicator */}
      <Card className="border-2 border-blue-500">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Viewport Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium">Current Size:</p>
              <p className="text-2xl font-bold">{viewportSize.width} × {viewportSize.height}px</p>
            </div>
            <div>
              <p className="text-sm font-medium">Breakpoint:</p>
              <p className="text-2xl font-bold">{breakpoint}</p>
            </div>
          </div>
          <div className="mt-4">
            <p className="text-sm font-medium">Breakpoint Indicators:</p>
            <div className="flex flex-wrap gap-2 mt-2">
              <div className="bg-red-500 text-white px-3 py-1 rounded text-xs xs:hidden">xs hidden</div>
              <div className="bg-red-500 text-white px-3 py-1 rounded text-xs hidden xs:block sm:hidden">xs visible</div>
              <div className="bg-orange-500 text-white px-3 py-1 rounded text-xs hidden sm:block md:hidden">sm visible</div>
              <div className="bg-yellow-500 text-white px-3 py-1 rounded text-xs hidden md:block lg:hidden">md visible</div>
              <div className="bg-green-500 text-white px-3 py-1 rounded text-xs hidden lg:block xl:hidden">lg visible</div>
              <div className="bg-blue-500 text-white px-3 py-1 rounded text-xs hidden xl:block 2xl:hidden">xl visible</div>
              <div className="bg-purple-500 text-white px-3 py-1 rounded text-xs hidden 2xl:block">2xl visible</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="h-full bg-gray-50/50" data-testid="admin-settings-page">
        <div className="flex h-full">
          {/* Settings Sidebar - Only visible on desktop */}
          <aside
            className="w-80 bg-white border-r border-gray-200 flex-shrink-0 hidden lg:block"
            data-testid="admin-settings-sidebar"
          >
            <div className="p-6">
              <h2 className="text-lg font-semibold">Settings</h2>
              <p className="text-sm text-gray-600 mt-1">
                Configure your car rental business settings
              </p>
            </div>
            <nav className="px-3 py-2 space-y-1">
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveSection(item.id as SettingsSection)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all",
                    activeSection === item.id
                      ? "bg-blue-50 text-blue-700 border border-blue-200 font-medium"
                      : "text-gray-700 hover:bg-gray-100 hover:text-gray-900",
                    dirtyState[item.id] && "relative"
                  )}
                >
                  <item.icon className="h-4 w-4 flex-shrink-0" />
                  <span className="flex-1 text-left">{item.label}</span>
                  {dirtyState[item.id] && (
                    <div className="w-2 h-2 rounded-full bg-orange-500 flex-shrink-0" />
                  )}
                </button>
              ))}
            </nav>
          </aside>

          {/* Main Content */}
          <main className="flex-1 overflow-auto">
            <div className="max-w-5xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
              <div className="space-y-6">
                <Card className="p-4">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {React.createElement(
                        sidebarItems.find((item) => item.id === activeSection)?.icon || Settings,
                        { className: "h-5 w-5" }
                      )}
                      {sidebarItems.find((item) => item.id === activeSection)?.label}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p>This is a test page for the {activeSection} section.</p>
                      <Button 
                        onClick={() => toggleDirtyState(activeSection)}
                        variant="secondary"
                      >
                        Toggle Dirty State
                      </Button>
                      <div className="p-4 border rounded-md bg-gray-50">
                        <p className="text-sm">
                          Current section: <span className="font-bold">{activeSection}</span>
                        </p>
                        <p className="text-sm">
                          Dirty state: <span className="font-bold">{dirtyState[activeSection] ? "Yes" : "No"}</span>
                        </p>
                        <p className="text-sm">
                          View: <span className="font-bold">{isMobile ? "Mobile/Tablet" : "Desktop"}</span>
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </main>
        </div>
      </div>

      {/* Fixed Save Button for Mobile - Only visible when there are changes and no modal is open */}
      {isMobile && Object.values(dirtyState).some(Boolean) && !modalOpen && (
        <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200 flex justify-end z-10">
          <Button>Save Changes</Button>
        </div>
      )}

      {/* Settings Modal for Mobile/Tablet */}
      <SettingsModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        activeSection={activeSection}
        sectionTitle={sidebarItems.find((item) => item.id === activeSection)?.label || ""}
        sectionIcon={sidebarItems.find((item) => item.id === activeSection)?.icon || Settings}
        isDirty={!!dirtyState[activeSection]}
        onSave={() => {
          setDirtyState((prev) => ({
            ...prev,
            [activeSection]: false,
          }));
          setModalOpen(false);
        }}
        onDiscard={() => {
          setDirtyState((prev) => ({
            ...prev,
            [activeSection]: false,
          }));
          setModalOpen(false);
        }}
        saving={false}
      >
        <div className="space-y-4">
          <p>This is the modal content for the {activeSection} section.</p>
          <Button 
            onClick={() => toggleDirtyState(activeSection)}
            variant="secondary"
          >
            Toggle Dirty State
          </Button>
          <div className="p-4 border rounded-md bg-gray-50">
            <p className="text-sm">
              Current section: <span className="font-bold">{activeSection}</span>
            </p>
            <p className="text-sm">
              Dirty state: <span className="font-bold">{dirtyState[activeSection] ? "Yes" : "No"}</span>
            </p>
          </div>
        </div>
      </SettingsModal>

      {/* Testing Instructions */}
      <Card className="border-2 border-green-500 mt-8">
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold">Mobile View (320px - 1023px)</h3>
            <ul className="list-disc pl-5 space-y-2 mt-2">
              <li>Hamburger menu should appear in the header</li>
              <li>Clicking on a section in the drawer should open the modal</li>
              <li>Modal should show section content with save/discard buttons</li>
              <li>When dirty state is active, a fixed save button should appear at bottom</li>
              <li>Desktop sidebar should be hidden</li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold">Desktop View (≥1024px)</h3>
            <ul className="list-disc pl-5 space-y-2 mt-2">
              <li>Sidebar should be visible on the left</li>
              <li>Content should appear in the main area</li>
              <li>Hamburger menu should be hidden</li>
              <li>Modal should not open when clicking sidebar items</li>
              <li>Mobile fixed save button should be hidden</li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold">Breakpoints to Test</h3>
            <ul className="list-disc pl-5 space-y-2 mt-2">
              <li>Mobile S: 320px</li>
              <li>Mobile M: 375px</li>
              <li>Mobile L: 425px</li>
              <li>Tablet: 768px</li>
              <li>Laptop: 1024px</li>
              <li>Desktop: 1440px+</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
