"use client"

import * as React from "react"
import { format } from "date-fns"
import { 
  CalendarIcon, 
  XIcon, 
  FileTextIcon, 
  EyeIcon, 
  CopyIcon, 
  CheckCircleIcon, 
  XCircleIcon, 
  AlertCircleIcon, 
  ShieldCheckIcon, 
  ClockIcon,
  PhoneIcon,
  MailIcon,
  UserIcon,
  CarIcon,
  MapPinIcon,
  CreditCardIcon,
  ReceiptIcon
} from "lucide-react"
import { StatusBadge, PaymentBadge } from "./status-badges"
import { cn } from "@/lib/utils"
import { logWithContext } from "@/lib/utils/logger"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
  DialogClose
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { useIsMobile } from "@/hooks/use-mobile"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { 
  getBookingDocuments, 
  verifyBookingDocument, 
  requireDocumentResubmission,
  getBookingDocumentStatus 
} from "@/app/admin/bookings/actions/document-actions"

interface BookingDocument {
  id: string;
  booking_id: string;
  document_type: string;
  file_url: string;
  file_name: string;
  file_size: number;
  file_type: string;
  verification_status: "pending" | "approved" | "rejected" | "requires_resubmission";
  verification_notes?: string;
  verified_by?: string;
  verified_at?: string;
  created_at: string;
  updated_at: string;
}

export type BookingDetailsModalProps = {
  booking: {
    id: string
    userName: string
    carModel: string
    from: Date
    to: Date
    days: number
    status: "Pending" | "Active" | "Completed" | "Cancelled"
    payStatus: "Paid" | "Unpaid" | "Partial" | "Refunded"
    totalAmount: number
    pickup_location: string
    dropoff_location: string
    created_at: string
    updated_at: string
  }
  isOpen: boolean
  onClose: () => void
  onAddToCalendar: () => void
  onAddToOnPageCalendar?: () => void
  useOnPageCalendar?: boolean
  onPaymentVerification?: (paymentId: string, action: "verify" | "reject", reason?: string) => void
  onStatusChange?: (bookingId: string, status: "Active" | "Cancelled") => void
  customer?: {
    name: string
    email?: string
    phone?: string
  }
  car?: {
    model: string
    plate_number: string
    transmission: string
    type: string
    seats: number
  }
  payment?: {
    id: string
    amount: number
    method: string
    status: string
    transaction_id?: string
    proof_of_payment_url?: string
    gcash_reference_number?: string
  } | null
  currentAdminId?: string
}

export function BookingDetailsModal(props: BookingDetailsModalProps) {
  const {
    booking,
    isOpen,
    onClose,
    onAddToCalendar,
    onAddToOnPageCalendar,
    useOnPageCalendar = false,
    onPaymentVerification,
    onStatusChange,
    customer,
    car,
    payment,
  } = props;
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const [documents, setDocuments] = React.useState<BookingDocument[]>([]);
  const [loadingDocuments, setLoadingDocuments] = React.useState(false);
  const [viewerOpen, setViewerOpen] = React.useState(false);
  const [viewerNotes, setViewerNotes] = React.useState("");
  const [viewerItem, setViewerItem] = React.useState<
    | { type: "document"; url: string; title: string; mime?: string; document: BookingDocument }
    | null
  >(null);
  const formatDateRange = (date: Date) => {
    const dateFormatted = format(date, "MMM d, yyyy")
    const timeFormatted = format(date, "h:mm a")
    const dayFormatted = format(date, "EEE")
    
    return {
      date: dateFormatted,
      time: timeFormatted,
      day: dayFormatted
    }
  }

  const pickupInfo = formatDateRange(booking.from)
  const returnInfo = formatDateRange(booking.to)
  
  // Document type label helper
  const documentTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      drivers_license: "Driver's License",
      government_id: "Government ID",
      proof_of_billing: "Proof of Billing",
      proof_of_payment: "Proof of Downpayment",
    };
    return labels[type] || type;
  };

  // Format file size helper
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Document viewer functions
  const openDocumentViewer = (doc: BookingDocument) => {
    setViewerItem({
      type: "document",
      url: doc.file_url,
      title: `${documentTypeLabel(doc.document_type)} • ${doc.file_name}`,
      mime: doc.file_type,
      document: doc,
    });
    setViewerNotes("");
    setViewerOpen(true);
  };

  // Fetch documents function
  const fetchBookingDocuments = async () => {
    if (!booking?.id) return;
    
    setLoadingDocuments(true);
    try {
      logWithContext("BookingDetailsModal", "Starting document fetch for booking:", booking.id);
      const documentsResult = await getBookingDocuments(booking.id);

      if (documentsResult.data) {
        setDocuments(documentsResult.data);
      } else {
        logWithContext("BookingDetailsModal", "No documents data received:", documentsResult.error);
      }
    } catch (error) {
      logWithContext("BookingDetailsModal", "Error fetching booking documents:", error);
    } finally {
      setLoadingDocuments(false);
    }
  };

  // Document verification handler
  const handleDocumentVerification = async (
    documentId: string,
    action: "approve" | "reject",
    notes?: string
  ) => {
    try {
      const result = await verifyBookingDocument(documentId, action, notes);
      if (result.error) {
        toast({
          variant: "destructive",
          title: "Document verification failed",
          description: result.error.message,
        });
        return;
      }

      // Refresh documents
      await fetchBookingDocuments();
      toast({
        title: `Document ${action === "approve" ? "approved" : "rejected"}`,
        description: notes ? `Notes: ${notes}` : undefined,
      });
    } catch (error) {
      console.error("Error verifying document:", error);
      toast({
        variant: "destructive",
        title: "Failed to verify document",
        description: "Please try again.",
      });
    }
  };

  // Require resubmission handler
  const handleRequireResubmission = async (documentId: string, notes: string) => {
    try {
      const result = await requireDocumentResubmission(documentId, notes);
      if (result.error) {
        toast({
          variant: "destructive",
          title: "Resubmission request failed",
          description: result.error.message,
        });
        return;
      }

      // Refresh documents
      await fetchBookingDocuments();
      toast({
        title: "Resubmission requested",
        description: `Notes: ${notes}`,
      });
    } catch (error) {
      console.error("Error requesting resubmission:", error);
      toast({
        variant: "destructive",
        title: "Failed to request resubmission",
        description: "Please try again.",
      });
    }
  };

  // Copy to clipboard helper
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({ title: "Copied to clipboard", description: text });
  };

  // Fetch documents when modal opens
  React.useEffect(() => {
    if (isOpen && booking?.id) {
      fetchBookingDocuments();
    }
  }, [isOpen, booking?.id]);

  const handleAddToCalendar = () => {
    if (onAddToCalendar) {
      onAddToCalendar();
      window.open(
        `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(`${booking.userName} – ${booking.carModel}`)}&dates=${booking.from.toISOString().replace(/[-:]/g, '').split('.')[0]}Z/${booking.to.toISOString().replace(/[-:]/g, '').split('.')[0]}Z`,
        '_blank'
      )
      onClose()
    }
  }

  // Document viewer modal
  const renderDocumentViewer = () => {
    if (!viewerItem) return null;

    return (
      <Dialog open={viewerOpen} onOpenChange={(open) => !open && setViewerOpen(false)}>
        <DialogContent className="w-full max-w-[calc(100%-2rem)] xs:max-w-[calc(100%-3rem)] sm:max-w-lg md:max-w-xl lg:max-w-2xl overflow-y-auto max-h-[90vh] p-4 xs:p-5 sm:p-6" showCloseButton={false}>
          <DialogHeader className="space-y-2 px-0 sm:px-0 flex flex-row items-start justify-between">
            <div>
              <DialogTitle className="text-lg md:text-xl font-bold flex items-center gap-2">
                <FileTextIcon className="h-5 w-5 text-blue-600" />
                {viewerItem.title}
              </DialogTitle>
              <p className="text-sm text-gray-500">Review document details below</p>
            </div>
            <DialogClose className="rounded-full h-8 w-8 flex items-center justify-center hover:bg-gray-100">
              <XIcon className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </DialogClose>
          </DialogHeader>
          
          <div className="my-4 flex flex-col items-center justify-center">
            {viewerItem.type === "document" && (
              <div className="w-full max-h-[50vh] overflow-auto border rounded-md shadow-sm">
                {viewerItem.mime?.startsWith("image/") ? (
                  <img 
                    src={viewerItem.url} 
                    alt="Document preview" 
                    className="w-full h-auto object-contain"
                  />
                ) : viewerItem.mime?.startsWith("application/pdf") ? (
                  <iframe 
                    src={viewerItem.url} 
                    className="w-full h-[50vh]" 
                    title="PDF Document"
                  />
                ) : (
                  <div className="p-4 text-center">
                    <FileTextIcon className="h-12 w-12 mx-auto text-gray-400" />
                    <p className="mt-2 text-sm text-gray-500">
                      Preview not available. <a href={viewerItem.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Download file</a>
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {viewerItem.type === "document" && viewerItem.document.verification_status === "pending" && (
            <>
              <Separator className="my-4" />
              
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-gray-700">Verification Notes (optional)</h3>
                <Textarea 
                  placeholder="Add notes about this document..." 
                  value={viewerNotes} 
                  onChange={(e) => setViewerNotes(e.target.value)} 
                  className="min-h-[80px]"
                />
                
                <div className="flex flex-col xs:flex-row gap-2 justify-end">
                  <Button 
                    variant="destructive-outline" 
                    className={cn(
                      "flex items-center",
                      isMobile ? "justify-center h-10 w-full" : "gap-1"
                    )}
                    onClick={() => {
                      handleDocumentVerification(viewerItem.document.id, "reject", viewerNotes);
                      setViewerOpen(false);
                    }}
                  >
                    <XCircleIcon className="h-4 w-4" />
                    <span className="ml-1">Reject</span>
                  </Button>
                  
                  <Button 
                    variant="warning" 
                    className={cn(
                      "flex items-center",
                      isMobile ? "justify-center h-10 w-full" : "gap-1"
                    )}
                    onClick={() => {
                      handleRequireResubmission(viewerItem.document.id, viewerNotes || "Please resubmit this document");
                      setViewerOpen(false);
                    }}
                  >
                    <AlertCircleIcon className="h-4 w-4" />
                    <span className="ml-1">Request Resubmission</span>
                  </Button>
                  
                  <Button 
                    variant="success" 
                    className={cn(
                      "flex items-center",
                      isMobile ? "justify-center h-10 w-full" : "gap-1"
                    )}
                    onClick={() => {
                      handleDocumentVerification(viewerItem.document.id, "approve", viewerNotes);
                      setViewerOpen(false);
                    }}
                  >
                    <CheckCircleIcon className="h-4 w-4" />
                    <span className="ml-1">Approve</span>
                  </Button>
                </div>
              </div>
            </>
          )}
          
          <DialogFooter className="flex justify-end mt-5 pt-4 border-t">
            <DialogClose asChild>
              <Button 
                variant="secondary"
                className={cn(
                  "flex items-center",
                  isMobile ? "justify-center h-10 w-full" : "gap-1"
                )}
              >
                <XIcon className="h-4 w-4" />
                <span className="ml-1">Close</span>
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  const documentStatus = {
    pending: documents.filter((doc: BookingDocument) => doc.verification_status === "pending").length,
    approved: documents.filter((doc: BookingDocument) => doc.verification_status === "approved").length,
    rejected: documents.filter((doc: BookingDocument) => doc.verification_status === "rejected").length,
    resubmission: documents.filter((doc: BookingDocument) => doc.verification_status === "requires_resubmission").length,
  };

  return (
    <>
      {renderDocumentViewer()}
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent 
          className="w-full max-w-[calc(100%-1rem)] xs:max-w-[calc(100%-1.5rem)] sm:max-w-lg md:max-w-xl lg:max-w-2xl overflow-y-auto max-h-[90vh] p-3 xs:p-4 sm:p-6"
          showCloseButton={false}
        >
          <DialogHeader className="space-y-2 px-0 sm:px-0">
            <DialogTitle className="text-lg sm:text-xl md:text-2xl font-bold">
              <span>Booking Details</span>
            </DialogTitle>
          </DialogHeader>
        
        <div className="space-y-4 sm:space-y-6 my-4">
          {/* Booking ID and Status Section */}
          <div className="bg-gray-50 p-3 sm:p-4 rounded-lg shadow-sm border border-gray-100">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
              <div>
                <h3 className="font-semibold text-gray-700 mb-1 text-sm sm:text-base">Booking ID</h3>
                <p className="font-mono font-bold text-blue-700 break-all">#{booking.id}</p>
              </div>
              <div className="flex flex-col sm:items-end gap-1">
                <h3 className="font-semibold text-gray-700 text-sm sm:text-base">Status</h3>
                <div className="flex gap-2 items-center">
                  <StatusBadge status={booking.status} />
                  <PaymentBadge status={booking.payStatus} />
                </div>
              </div>
            </div>
          </div>
          
          {/* Section Header */}
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-base sm:text-lg font-bold text-gray-800">Booking Information</h2>
          </div>

          {/* Customer Information */}
          <div className="bg-gray-50 p-3 sm:p-4 rounded-lg shadow-sm border border-gray-100">
            <h3 className="font-semibold text-gray-700 mb-1 sm:mb-2 text-sm sm:text-base flex items-center gap-2">
              <UserIcon className="h-4 w-4" />
              Customer
            </h3>
            <p className="text-base sm:text-lg font-bold text-gray-900 break-words mb-2">{booking.userName}</p>
            
            {customer && (
              <div className="space-y-2 mt-3">
                {customer.phone && (
                  <div className="flex items-center gap-3 text-sm text-gray-700 bg-gray-100 p-2 rounded-lg border">
                    <PhoneIcon className="h-4 w-4 text-green-600 flex-shrink-0" />
                    <span className="font-medium">{customer.phone}</span>
                    <Button
                      variant="tertiary"
                      size="xs"
                      onClick={() => copyToClipboard(customer.phone!)}
                      className="ml-auto hover:bg-blue-100 h-7 w-7 p-0"
                      title="Copy phone number"
                    >
                      <CopyIcon className="h-3 w-3" />
                    </Button>
                  </div>
                )}
                {customer.email && (
                  <div className="flex items-center gap-3 text-sm text-gray-700 bg-gray-100 p-2 rounded-lg border">
                    <MailIcon className="h-4 w-4 text-blue-600 flex-shrink-0" />
                    <span className="font-medium overflow-hidden text-ellipsis">{customer.email}</span>
                    <Button
                      variant="tertiary"
                      size="xs"
                      onClick={() => copyToClipboard(customer.email || "")}
                      className="ml-auto hover:bg-blue-100 h-7 w-7 p-0"
                      title="Copy email address"
                    >
                      <CopyIcon className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Vehicle Information */}
          <div className="bg-gray-50 p-3 sm:p-4 rounded-lg shadow-sm border border-gray-100">
            <h3 className="font-semibold text-gray-700 mb-1 sm:mb-2 text-sm sm:text-base flex items-center gap-2">
              <CarIcon className="h-4 w-4" />
              Vehicle
            </h3>
            <div className="flex items-center mb-3">
              <div className="h-8 w-8 sm:h-10 sm:w-10 bg-blue-100 rounded-full flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0">
                <span className="text-blue-700 text-lg sm:text-xl">🚗</span>
              </div>
              <p className="text-base sm:text-lg font-bold text-gray-900 break-words">{booking.carModel}</p>
            </div>
            
            {car && (
              <div className="grid grid-cols-2 gap-2 mt-3 text-sm">
                <div className="bg-gray-100 p-2 rounded-lg border">
                  <span className="font-medium text-gray-700">Plate:</span>
                  <p className="font-semibold">{car.plate_number}</p>
                </div>
                <div className="bg-gray-100 p-2 rounded-lg border">
                  <span className="font-medium text-gray-700">Transmission:</span>
                  <p className="font-semibold">{car.transmission}</p>
                </div>
                <div className="bg-gray-100 p-2 rounded-lg border">
                  <span className="font-medium text-gray-700">Category:</span>
                  <p className="font-semibold">{car.type}</p>
                </div>
                <div className="bg-gray-100 p-2 rounded-lg border">
                  <span className="font-medium text-gray-700">Seats:</span>
                  <p className="font-semibold">{car.seats}</p>
                </div>
              </div>
            )}
          </div>
          
          {/* Booking Schedule */}
          <div className="space-y-3 sm:space-y-4">
            <h3 className="font-semibold text-gray-700 text-sm sm:text-base flex items-center gap-2">
              <ClockIcon className="h-4 w-4" />
              Booking Period
            </h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg shadow-sm border border-gray-100">
                <h4 className="font-semibold text-gray-700 mb-1 sm:mb-2 text-xs sm:text-sm">Pickup</h4>
                <p className="font-bold text-gray-900 text-sm sm:text-base">{pickupInfo.date}</p>
                <p className="text-gray-700 text-xs sm:text-sm">{pickupInfo.time} • {pickupInfo.day}</p>
                <p className="text-gray-600 mt-1 sm:mt-2 text-xs sm:text-sm break-words flex items-start gap-1">
                  <MapPinIcon className="h-3 w-3 flex-shrink-0 mt-0.5 text-gray-500" />
                  <span>{booking.pickup_location}</span>
                </p>
              </div>
              
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg shadow-sm border border-gray-100">
                <h4 className="font-semibold text-gray-700 mb-1 sm:mb-2 text-xs sm:text-sm">Return</h4>
                <p className="font-bold text-gray-900 text-sm sm:text-base">{returnInfo.date}</p>
                <p className="text-gray-700 text-xs sm:text-sm">{returnInfo.time} • {returnInfo.day}</p>
                <p className="text-gray-600 mt-1 sm:mt-2 text-xs sm:text-sm break-words flex items-start gap-1">
                  <MapPinIcon className="h-3 w-3 flex-shrink-0 mt-0.5 text-gray-500" />
                  <span>{booking.dropoff_location || booking.pickup_location}</span>
                </p>
              </div>
              
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg border border-gray-100 sm:col-span-2">
                <h4 className="font-semibold text-gray-700 mb-1 text-xs sm:text-sm">Duration</h4>
                <p className="font-semibold text-gray-900">
                  {booking.days} day{booking.days > 1 ? "s" : ""} {Math.floor((booking.to.getTime() - booking.from.getTime()) / (1000 * 60 * 60)) % 24} hour{Math.floor((booking.to.getTime() - booking.from.getTime()) / (1000 * 60 * 60)) % 24 !== 1 ? "s" : ""}
                </p>
              </div>
            </div>
          </div>
          
          {/* Payment Information */}
          <div className="bg-gray-50 p-3 sm:p-4 rounded-lg shadow-sm border border-gray-100">
            <h3 className="font-semibold text-gray-700 mb-2 sm:mb-3 text-sm sm:text-base flex items-center gap-2">
              <CreditCardIcon className="h-4 w-4" />
              Payment
            </h3>
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-700 text-sm sm:text-base">Total Amount</span>
              <span className="font-bold text-lg sm:text-xl text-green-700">₱{booking.totalAmount.toFixed(2)}</span>
            </div>
            
            {payment && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">Method:</span>
                    <p className="font-semibold">{payment.method}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Status:</span>
                    <p className="font-semibold">{payment.status}</p>
                  </div>
                  {payment.transaction_id && (
                    <div className="col-span-2">
                      <span className="font-medium text-gray-600">Reference:</span>
                      <p className="font-mono text-xs bg-gray-100 p-1 rounded border break-all">
                        {payment.transaction_id}
                      </p>
                    </div>
                  )}
                  {payment.method === "GCash" && payment.gcash_reference_number && (
                    <div className="col-span-2">
                      <span className="font-medium text-cyan-800">GCash Reference:</span>
                      <div className="flex items-center gap-2 mt-1">
                        <p className="font-mono text-xs bg-cyan-50 p-2 rounded border-2 border-cyan-300 break-all flex-1 text-cyan-900">
                          {payment.gcash_reference_number}
                        </p>
                        <Button
                          variant="tertiary"
                          size="xs"
                          onClick={() => copyToClipboard(payment.gcash_reference_number!)}
                          className="hover:bg-cyan-100 h-7 w-7 p-0 flex-shrink-0 border-cyan-300"
                          title="Copy GCash reference number"
                        >
                          <CopyIcon className="h-3 w-3 text-cyan-700" />
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
                
                {payment.proof_of_payment_url && (
                  <div className="mt-2">
                    <p className="text-xs font-medium text-gray-600 mb-1">Proof of Payment</p>
                    <Button
                      variant="secondary"
                      size="sm"
                      className={cn(
                        "flex items-center",
                        isMobile ? "justify-center h-8 w-8 p-0 rounded-full" : "gap-1 text-xs"
                      )}
                      title="View Payment Proof"
                      onClick={() => {
                        setViewerItem({
                          type: "document",
                          url: payment.proof_of_payment_url!,
                          title: "Proof of Payment",
                          document: {
                            id: payment.id,
                            booking_id: booking.id,
                            document_type: "proof_of_payment",
                            file_url: payment.proof_of_payment_url!,
                            file_name: "payment-proof.jpg",
                            file_size: 0,
                            file_type: "image/jpeg",
                            verification_status: payment.status === "Pending Verification" ? "pending" : 
                                                payment.status === "Paid" ? "approved" : "rejected",
                            created_at: "",
                            updated_at: ""
                          }
                        });
                        setViewerOpen(true);
                      }}
                    >
                      <EyeIcon className="h-4 w-4" />
                      {!isMobile && <span>View Proof</span>}
                    </Button>
                  </div>
                )}
                
                {payment.status === "Pending Verification" && onPaymentVerification && (
                  <div className="mt-3 pt-2 border-t border-gray-200">
                    <p className="text-xs text-gray-600 mb-2">Verify or reject this payment</p>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="success"
                        className={cn(
                          "flex items-center",
                          isMobile ? "justify-center h-8 w-8 p-0 rounded-full" : "gap-1 text-xs"
                        )}
                        title="Verify Payment"
                        onClick={() => {
                          if (payment.proof_of_payment_url) {
                            setViewerItem({
                              type: "document",
                              url: payment.proof_of_payment_url,
                              title: "Verify Payment",
                              document: {
                                id: payment.id,
                                booking_id: booking.id,
                                document_type: "proof_of_payment",
                                file_url: payment.proof_of_payment_url,
                                file_name: "payment-proof.jpg",
                                file_size: 0,
                                file_type: "image/jpeg",
                                verification_status: "pending",
                                created_at: "",
                                updated_at: ""
                              }
                            });
                            setViewerOpen(true);
                          } else {
                            onPaymentVerification(payment.id, "verify");
                          }
                        }}
                      >
                        <CheckCircleIcon className="h-4 w-4" />
                        {!isMobile && <span>Verify</span>}
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        className={cn(
                          "flex items-center",
                          isMobile ? "justify-center h-8 w-8 p-0 rounded-full" : "gap-1 text-xs"
                        )}
                        title="Reject Payment"
                        onClick={() => {
                          if (payment.proof_of_payment_url) {
                            setViewerItem({
                              type: "document",
                              url: payment.proof_of_payment_url,
                              title: "Reject Payment",
                              document: {
                                id: payment.id,
                                booking_id: booking.id,
                                document_type: "proof_of_payment",
                                file_url: payment.proof_of_payment_url,
                                file_name: "payment-proof.jpg",
                                file_size: 0,
                                file_type: "image/jpeg",
                                verification_status: "pending",
                                created_at: "",
                                updated_at: ""
                              }
                            });
                            setViewerOpen(true);
                          } else {
                            onPaymentVerification(payment.id, "reject", "Payment proof rejected");
                          }
                        }}
                      >
                        <XCircleIcon className="h-4 w-4" />
                        {!isMobile && <span>Reject</span>}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Section Header */}
          <div className="flex items-center justify-between mt-6 mb-2">
            <h2 className="text-base sm:text-lg font-bold text-gray-800">Legal Documents</h2>
          </div>

          {/* Document Verification Section */}
          <div className="bg-gray-50 p-3 sm:p-4 rounded-lg shadow-sm border border-orange-200">
            <div className="flex items-center justify-between mb-2 sm:mb-3">
              <h3 className="font-semibold text-gray-700 text-sm sm:text-base flex items-center gap-2">
                <FileTextIcon className="h-4 w-4" />
                Required Documents
              </h3>
              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 text-xs">
                {documentStatus.pending} pending
              </Badge>
            </div>
            
            {loadingDocuments ? (
              <div className="flex justify-center items-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
                <span className="ml-2 text-sm text-gray-500">Loading documents...</span>
              </div>
            ) : documents.length === 0 ? (
              <div className="text-center py-4 text-gray-500 text-sm">
                <FileTextIcon className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <p>No documents uploaded yet</p>
              </div>
            ) : (
              <div className="space-y-3">
                {documents.map((doc) => (
                  <div 
                    key={doc.id} 
                    className={cn(
                      "border rounded-md p-2 sm:p-3 flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3",
                      doc.verification_status === "approved" ? "bg-green-50 border-green-200" :
                      doc.verification_status === "rejected" ? "bg-red-50 border-red-200" :
                      doc.verification_status === "requires_resubmission" ? "bg-amber-50 border-amber-200" :
                      "bg-white border-gray-200"
                    )}
                  >
                    {/* Document Info */}
                    <div className="flex-1">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          <FileTextIcon className="h-4 w-4 text-gray-500 flex-shrink-0" />
                          <span className="font-medium text-sm">{documentTypeLabel(doc.document_type)}</span>
                        </div>
                        <div>
                          {doc.verification_status === "approved" && (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                              <CheckCircleIcon className="h-3 w-3 mr-1" /> Approved
                            </Badge>
                          )}
                          {doc.verification_status === "rejected" && (
                            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                              <XCircleIcon className="h-3 w-3 mr-1" /> Rejected
                            </Badge>
                          )}
                          {doc.verification_status === "requires_resubmission" && (
                            <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                              <AlertCircleIcon className="h-3 w-3 mr-1" /> Resubmission
                            </Badge>
                          )}
                          {doc.verification_status === "pending" && (
                            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                              <ClockIcon className="h-3 w-3 mr-1" /> Pending
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        <span className="mr-2">{doc.file_name}</span>
                        <span>{formatFileSize(doc.file_size)}</span>
                      </div>
                      {doc.verification_notes && (
                        <div className="mt-1 text-xs italic text-gray-600 bg-gray-100 p-1 rounded">
                          Note: {doc.verification_notes}
                        </div>
                      )}
                    </div>
                    
                    {/* Actions */}
                    <div className="flex flex-row sm:flex-col gap-2 justify-end">
                      <Button 
                        size="sm" 
                        variant="secondary" 
                        className={cn(
                          "flex items-center justify-center",
                          isMobile ? "h-9 w-9 rounded-full p-0" : "h-8 px-2 text-blue-600"
                        )}
                        onClick={() => openDocumentViewer(doc)}
                        title="View Document"
                      >
                        <EyeIcon className="h-4 w-4" />
                        {!isMobile && <span className="text-xs ml-1">View</span>}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* Audit Trail Section */}
        <div className="space-y-4 sm:space-y-6 my-4">
          <div className="flex items-center justify-between mt-6 mb-2">
            <h2 className="text-base sm:text-lg font-bold text-gray-800">Booking Management</h2>
          </div>
          
          {/* Booking Status and Finalization */}
          <div className="bg-gray-50 p-3 sm:p-4 rounded-lg shadow-sm border border-gray-100">
            <h3 className="font-semibold text-gray-700 mb-2 sm:mb-3 text-sm sm:text-base flex items-center gap-2">
              <ShieldCheckIcon className="h-4 w-4" />
              Booking Management
            </h3>
            
            {booking.status === "Pending" && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <p className="text-xs text-gray-600 mb-2">Finalize this booking</p>
                <div className="flex flex-wrap gap-2">
                  <Button
                    size="sm"
                    variant="success"
                    className={cn(
                      "flex items-center",
                      isMobile ? "justify-center h-8 w-8 p-0 rounded-full" : "gap-1 text-xs"
                    )}
                    title="Approve Booking"
                    onClick={() => {
                      // Handle booking approval
                      if (onStatusChange) {
                        onStatusChange(booking.id, "Active");
                      }
                    }}
                  >
                    <CheckCircleIcon className="h-4 w-4" />
                    {!isMobile && <span>Approve</span>}
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    className={cn(
                      "flex items-center",
                      isMobile ? "justify-center h-8 w-8 p-0 rounded-full" : "gap-1 text-xs"
                    )}
                    title="Reject Booking"
                    onClick={() => {
                      // Handle booking rejection
                      if (onStatusChange) {
                        onStatusChange(booking.id, "Cancelled");
                      }
                    }}
                  >
                    <XCircleIcon className="h-4 w-4" />
                    {!isMobile && <span>Reject</span>}
                  </Button>
                </div>
              </div>
            )}
          </div>
          
          {/* Audit Trail */}
          <div className="bg-gray-50 p-3 sm:p-4 rounded-lg shadow-sm border border-gray-100">
            <h3 className="font-semibold text-gray-700 mb-2 sm:mb-3 text-sm sm:text-base flex items-center gap-2">
              <ClockIcon className="h-4 w-4" />
              Audit Trail
            </h3>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-start gap-2">
                <div className="h-5 w-5 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-700 text-xs">📝</span>
                </div>
                <div>
                  <p className="font-medium">Booking Created</p>
                  <p className="text-xs text-gray-600">{new Date(booking.created_at).toLocaleString()}</p>
                </div>
              </div>
              
              {booking.updated_at !== booking.created_at && (
                <div className="flex items-start gap-2">
                  <div className="h-5 w-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-green-700 text-xs">🔄</span>
                  </div>
                  <div>
                    <p className="font-medium">Booking Updated</p>
                    <p className="text-xs text-gray-600">{new Date(booking.updated_at).toLocaleString()}</p>
                  </div>
                </div>
              )}
              
              {payment && payment.status === "Paid" && (
                <div className="flex items-start gap-2">
                  <div className="h-5 w-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-green-700 text-xs">💰</span>
                  </div>
                  <div>
                    <p className="font-medium">Payment Verified</p>
                    <p className="text-xs text-gray-600">{new Date().toLocaleString()}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <DialogFooter className="border-t border-gray-200 pt-3 sm:pt-4 mt-4 sm:mt-6">
          <div className="flex flex-col xs:flex-row w-full gap-2 xs:gap-3">
            {useOnPageCalendar && onAddToOnPageCalendar ? (
              <Button
                variant="secondary"
                size="sm"
                className={cn(
                  "flex items-center justify-center gap-2",
                  isMobile ? "h-11 w-full text-sm" : "h-10 px-4 text-sm"
                )}
                onClick={onAddToOnPageCalendar}
                title="Add to On-Page Calendar"
              >
                <CalendarIcon className="h-4 w-4" />
                <span>Add to On-Page Calendar</span>
              </Button>
            ) : (
              <Button
                variant="secondary"
                size="sm"
                className={cn(
                  "flex items-center justify-center gap-2",
                  isMobile ? "h-11 w-full text-sm" : "h-10 px-4 text-sm"
                )}
                onClick={onAddToCalendar}
                title="Add to Calendar"
              >
                <CalendarIcon className="h-4 w-4" />
                <span>Add to Calendar</span>
              </Button>
            )}
            
            <Button
              variant="primary"
              size="sm"
              className={cn(
                "flex items-center justify-center gap-2",
                isMobile ? "h-11 w-full text-sm" : "h-10 px-4 text-sm"
              )}
              onClick={onClose}
              title="Close Modal"
            >
              <XIcon className="h-4 w-4" />
              <span>Close</span>
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    </>
  );
}
