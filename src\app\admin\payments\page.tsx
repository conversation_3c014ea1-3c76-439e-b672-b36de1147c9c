"use client"

import * as React from "react"

import { getAdminPayments, type AdminPayment } from "./actions/payment-actions"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import { PaymentCard } from "@/components/admin/payment-card"
import { AdminPaymentsSkeleton } from "@/components/admin/loading/skeleton-components"
import { db } from "@/lib/supabase/database"
import { useAdminAuth } from "@/components/auth/admin-auth-context"

export default function AdminPaymentsPage() {
  const { loading: authLoading } = useAdminAuth()
  const [payments, setPayments] = React.useState<AdminPayment[]>([])
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)

  const [query, setQuery] = React.useState("")
  const [method, setMethod] = React.useState<string>("all")
  const [sortBy, setSortBy] = React.useState<string>("date-desc")
  const [page, setPage] = React.useState(1)
  const pageSize = 10
  
  // Check if we're on mobile/tablet (< 1024px)
  const [isMobile, setIsMobile] = React.useState(false)
  
  React.useEffect(() => {
    // Set initial value
    setIsMobile(window.innerWidth < 1024)
    
    // Add resize listener
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1024)
    }
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Fetch payments data
  const fetchPayments = React.useCallback(async () => {
    setLoading(true)
    try {
      const { data, error: fetchError } = await getAdminPayments()
      
      if (fetchError) {
        setError(fetchError.message || "Failed to fetch payments")
      } else if (data) {
        setPayments(data)
        setError(null)
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error("Error fetching payments:", err)
    } finally {
      setLoading(false)
    }
  }, [])

  // Fetch initial data and subscribe to real-time updates
  React.useEffect(() => {
    fetchPayments()

    // Subscribe to payments table changes
    const paymentsSubscription = db.subscribeToPayments((payload) => {
      console.log("Payment change received!", payload)
      // Re-fetch payments to ensure data consistency
      fetchPayments()
    })

    // Subscribe to bookings table changes since payment status can be affected by booking updates
    const bookingsSubscription = db.subscribeToBookings((payload) => {
      console.log("Booking change received, refreshing payments!", payload)
      // Re-fetch payments when bookings change as payment status might be affected
      fetchPayments()
    })

    // Cleanup subscriptions on component unmount
    return () => {
      paymentsSubscription.unsubscribe()
      bookingsSubscription.unsubscribe()
    }
  }, [fetchPayments])

  const filtered = React.useMemo(() => {
    const q = query.trim().toLowerCase()
    return payments
      .filter((p) => (method === "all" ? true : p.method === method))
      .filter((p) =>
        q === ""
          ? true
          : (p.paymentRef + " " + p.bookingRef + " " + p.renterName + " " + p.renterEmail + " " + p.renterPhone + " " + p.amount + " " + p.method + " " + p.status)
              .toLowerCase()
              .includes(q)
      )
  }, [payments, query, method])

  const sorted = React.useMemo(() => {
    const res = [...filtered]
    switch (sortBy) {
      case "amount-desc":
        res.sort((a, b) => b.amount - a.amount)
        break
      case "amount-asc":
        res.sort((a, b) => a.amount - b.amount)
        break
      case "date-asc":
        res.sort((a, b) => +new Date(a.transactionDate) - +new Date(b.transactionDate))
        break
      default:
        res.sort((a, b) => +new Date(b.transactionDate) - +new Date(a.transactionDate))
    }
    return res
  }, [filtered, sortBy])

  const totalPages = Math.max(1, Math.ceil(sorted.length / pageSize))
  const pageItems = sorted.slice((page - 1) * pageSize, page * pageSize)

  React.useEffect(() => setPage(1), [query, method, sortBy])

  // Wait for auth loading to complete before rendering
  if (authLoading) {
    return <AdminPaymentsSkeleton />
  }

  if (loading) {
    return <AdminPaymentsSkeleton />
  }

  return (
    <div className="space-y-4 xs:space-y-6 p-3 xs:p-4 lg:p-6">
      <header className="space-y-2 xs:space-y-3">
        <div>
          <h1 className="text-2xl xs:text-3xl lg:text-4xl font-bold tracking-tight text-foreground">Payments</h1>
          <p className="text-sm xs:text-base lg:text-lg text-muted-foreground mt-1 xs:mt-2">View and manage all payment transactions</p>
        </div>
      </header>
      
      <Card className="shadow-sm">
        <CardHeader className="pb-3 xs:pb-4 lg:pb-6 px-3 xs:px-6">
          <div className="flex flex-col gap-3 xs:gap-4 lg:gap-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3 xs:gap-4">
              <CardTitle className="text-lg xs:text-xl lg:text-2xl">Payment Management</CardTitle>
              <div className="flex flex-col sm:flex-row gap-3 lg:w-auto">
                <Input 
                  placeholder="Search payments..." 
                  value={query} 
                  onChange={(e) => setQuery(e.target.value)} 
                  className="w-full text-sm xs:text-base h-10 xs:h-11 lg:w-80 xl:w-96" 
                />
              </div>
            </div>
            
            {/* Filter Controls */}
            <div className="flex flex-col gap-3 lg:gap-4">
              <div className="grid grid-cols-1 xs:grid-cols-2 gap-3 flex-1">
                <Select value={method} onValueChange={setMethod}>
                  <SelectTrigger className="w-full h-10 xs:h-11 text-sm xs:text-base">
                    <SelectValue placeholder="Payment Method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Methods</SelectItem>
                    <SelectItem value="GCash">GCash</SelectItem>
                    <SelectItem value="Bank Transfer">Bank Transfer</SelectItem>
                    <SelectItem value="Remittance Center">Remittance Center</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full h-10 xs:h-11 text-sm xs:text-base">
                    <SelectValue placeholder="Sort Options" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">Newest first</SelectItem>
                    <SelectItem value="date-asc">Oldest first</SelectItem>
                    <SelectItem value="amount-desc">Amount: high to low</SelectItem>
                    <SelectItem value="amount-asc">Amount: low to high</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardHeader>
      <CardContent className="p-0">
        {/* Mobile/Tablet Card View */}
        {isMobile ? (
          <div className="space-y-2 xs:space-y-3 p-2 xs:p-3">
            {loading ? (
              // Loading skeleton for mobile
              Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="overflow-hidden border rounded-lg shadow-sm">
                  <div className="p-4 space-y-3">
                    <div className="flex justify-between items-start">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-6 w-24" />
                    </div>
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-5 w-20" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                    <div className="pt-2 border-t border-muted">
                      <Skeleton className="h-10 w-full" />
                    </div>
                  </div>
                </Card>
              ))
            ) : error ? (
              <div className="p-8 text-center text-red-500">
                Error: {error}
              </div>
            ) : pageItems.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                No payments found
              </div>
            ) : (
              pageItems.map((payment) => (
                <PaymentCard key={payment.id} payment={payment} />
              ))
            )}
          </div>
        ) : (
          // Desktop Table View
          <div className="max-h-[75vh] overflow-auto lg:rounded-lg lg:border">
            <Table>
              <TableHeader className="sticky top-0 z-10 bg-gradient-to-r from-slate-50 to-slate-100 shadow-md">
                <TableRow className="border-b-2 border-slate-200">
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide">Payment ID</TableHead>
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide">Renter Information</TableHead>
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide">Booking Ref.</TableHead>
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide text-right">Amount Paid</TableHead>
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide">Payment Date</TableHead>
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide">Method</TableHead>
                  <TableHead className="px-4 py-4 lg:px-6 lg:py-5 text-sm font-semibold text-slate-700 tracking-wide text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  // Loading skeleton
                  Array.from({ length: 5 }).map((_, i) => (
                    <TableRow key={i} className="hover:bg-slate-50/50">
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5"><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5"><Skeleton className="h-4 w-40" /></TableCell>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5"><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5"><Skeleton className="h-4 w-28" /></TableCell>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5"><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5"><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell className="px-4 py-4 lg:px-6 lg:py-5"><Skeleton className="h-4 w-24" /></TableCell>
                    </TableRow>
                  ))
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={7} className="px-4 py-12 lg:px-6 lg:py-16 text-center">
                      <div className="text-red-500 font-medium">Error: {error}</div>
                    </TableCell>
                  </TableRow>
                ) : pageItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="px-4 py-12 lg:px-6 lg:py-16 text-center">
                      <div className="text-slate-500 font-medium">No payments found</div>
                      <p className="text-sm text-slate-400 mt-1">Try adjusting your search or filter criteria</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  pageItems.map((p, index) => {
                    // Get payment method badge styling
                    const getMethodBadgeClass = (method: string) => {
                      switch (method.toLowerCase()) {
                        case 'gcash':
                          return 'bg-blue-100 text-blue-800 border-blue-200'
                        case 'bank transfer':
                          return 'bg-green-100 text-green-800 border-green-200'
                        case 'remittance center':
                          return 'bg-purple-100 text-purple-800 border-purple-200'
                        default:
                          return 'bg-slate-100 text-slate-800 border-slate-200'
                      }
                    }
                    
                    return (
                      <TableRow key={p.id} className={`transition-colors hover:bg-slate-50/70 ${
                        index % 2 === 0 ? 'bg-white' : 'bg-slate-25'
                      } border-b border-slate-100`}>
                        <TableCell className="px-4 py-4 lg:px-6 lg:py-5">
                          <div className="font-mono text-sm font-medium text-slate-700">#{p.paymentRef}</div>
                        </TableCell>
                        <TableCell className="px-4 py-4 lg:px-6 lg:py-5">
                          <div className="space-y-1">
                            <div className="font-semibold text-slate-900 text-base">{p.renterName}</div>
                            <div className="text-sm text-slate-600">{p.renterEmail}</div>
                            {p.renterPhone !== "N/A" && (
                              <div className="text-sm text-slate-500">{p.renterPhone}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="px-4 py-4 lg:px-6 lg:py-5">
                          <a 
                            className="inline-flex items-center font-mono text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline transition-colors" 
                            href={`/admin/bookings?ref=${p.bookingRef}`}
                          >
                            #{p.bookingRef}
                          </a>
                        </TableCell>
                        <TableCell className="px-4 py-4 lg:px-6 lg:py-5 text-right">
                          <div className="font-bold text-lg text-slate-900">₱{p.amount.toLocaleString('en-PH', { minimumFractionDigits: 2 })}</div>
                        </TableCell>
                        <TableCell className="px-4 py-4 lg:px-6 lg:py-5">
                          <div className="text-sm font-medium text-slate-700">
                            {new Date(p.transactionDate).toLocaleDateString('en-PH', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric'
                            })}
                          </div>
                          <div className="text-xs text-slate-500">
                            {new Date(p.transactionDate).toLocaleTimeString('en-PH', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </div>
                        </TableCell>
                        <TableCell className="px-4 py-4 lg:px-6 lg:py-5">
                          <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${
                            getMethodBadgeClass(p.method)
                          }`}>
                            {p.method}
                          </span>
                        </TableCell>
                        <TableCell className="px-4 py-4 lg:px-6 lg:py-5 text-right">
                          {p.proofOfPaymentUrl ? (
                            <a 
                              href={p.proofOfPaymentUrl} 
                              target="_blank" 
                              rel="noopener noreferrer" 
                              className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                            >
                              View Receipt
                            </a>
                          ) : (
                            <span className="text-sm text-slate-400 font-medium">No receipt</span>
                          )}
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </div>
        )}
        <div className={`${isMobile ? 'flex flex-col gap-3 p-3 xs:p-4' : 'flex justify-between items-center gap-4 p-4 lg:p-6'} border-t border-slate-200 bg-slate-50/50`}>
          {!isMobile && (
            <div className="text-sm text-slate-600">
              Showing {(page - 1) * pageSize + 1} to {Math.min(page * pageSize, sorted.length)} of {sorted.length} payments
            </div>
          )}
          
          {/* Mobile pagination layout */}
          {isMobile ? (
            <>
              <div className="text-center">
                <span className="text-sm font-medium text-slate-700">Page {page} of {totalPages}</span>
                <div className="text-xs text-slate-500 mt-1">
                  {sorted.length} total payments
                </div>
              </div>
              <div className="flex items-center gap-2">
                <button 
                  className="flex-1 px-3 py-3 text-sm font-medium rounded-lg border border-slate-300 bg-white hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed min-h-[48px] transition-colors" 
                  onClick={() => setPage((p) => Math.max(1, p - 1))} 
                  disabled={page === 1}
                >
                  Previous
                </button>
                <button 
                  className="flex-1 px-3 py-3 text-sm font-medium rounded-lg border border-slate-300 bg-white hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed min-h-[48px] transition-colors" 
                  onClick={() => setPage((p) => Math.min(totalPages, p + 1))} 
                  disabled={page === totalPages}
                >
                  Next
                </button>
              </div>
            </>
          ) : (
            /* Desktop pagination layout */
            <div className="flex items-center gap-3">
              <button 
                className="px-4 py-2 text-sm font-medium rounded-lg border border-slate-300 bg-white hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px] transition-colors" 
                onClick={() => setPage((p) => Math.max(1, p - 1))} 
                disabled={page === 1}
              >
                Previous
              </button>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-slate-700">Page {page} of {totalPages}</span>
              </div>
              <button 
                className="px-4 py-2 text-sm font-medium rounded-lg border border-slate-300 bg-white hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px] transition-colors" 
                onClick={() => setPage((p) => Math.min(totalPages, p + 1))} 
                disabled={page === totalPages}
              >
                Next
              </button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
    </div>
  )
}
