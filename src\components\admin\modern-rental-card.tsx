import React from 'react';
import Image from 'next/image';
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { formatCurrency } from '@/lib/utils/format-currency';
import { CarIcon, MapPin, Clock, CalendarCheck } from 'lucide-react';

interface ModernRentalCardProps {
  booking: {
    id: string;
    status: string;
    total_amount: number;
    pickup_location: string;
    dropoff_location: string;
    pickup_datetime: string;
    dropoff_datetime: string;
  };
  car: {
    model: string;
    plate_number: string;
    image_url?: string;
    type?: string;
    seats?: number;
  };
}

export function ModernRentalCard({ booking, car }: ModernRentalCardProps) {
  // Format dates for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }) + ', ' + date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  return (
    <Card className="overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm hover:shadow-lg transition-all duration-300">
      {/* Status indicator */}
      <div className="h-1 w-full bg-emerald-500" />
      
      <div className="p-4 sm:p-5">
        {/* Header with status badge */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Badge className="bg-emerald-500 text-white border-0 px-2 py-0.5 text-xs font-medium uppercase">
              {booking.status.toUpperCase()}
            </Badge>
            <span className="text-xs text-gray-500 font-medium">#{booking.id}</span>
          </div>
        </div>
        
        {/* Main content */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Car image */}
          <div className="relative w-full sm:w-32 aspect-[4/3] rounded-lg overflow-hidden bg-gray-100">
            <Image
              src={car.image_url || '/placeholder.jpg'}
              alt={car.model}
              fill
              className="object-cover"
            />
          </div>
          
          {/* Car details */}
          <div className="flex-1">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
              <div>
                <h3 className="font-bold text-gray-900 text-lg">{car.model}</h3>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-xs text-gray-500">{car.type || 'Sedan'} • {car.seats || 5} seats</span>
                </div>
                
                <p className="text-sm text-gray-500 flex items-center gap-1 mt-2">
                  <CarIcon className="h-3.5 w-3.5" />
                  <span>{car.plate_number}</span>
                </p>
              </div>
              
              {/* Price */}
              <div className="mt-4 sm:mt-0 sm:text-right">
                <p className="text-2xl font-bold text-emerald-600">
                  {formatCurrency(booking.total_amount)}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Total Revenue
                </p>
              </div>
            </div>
            
            {/* Location and dates */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4 pt-4 border-t border-gray-200">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                  <p className="text-base font-medium text-gray-900">Pickup</p>
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-500 ml-4">
                  <MapPin className="h-3.5 w-3.5 flex-shrink-0" />
                  <span>{booking.pickup_location}</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-500 ml-4 mt-1">
                  <Clock className="h-3.5 w-3.5 flex-shrink-0" />
                  <span>{formatDate(booking.pickup_datetime)}</span>
                </div>
              </div>
              
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-2 h-2 rounded-full bg-red-500"></div>
                  <p className="text-base font-medium text-gray-900">Return</p>
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-500 ml-4">
                  <MapPin className="h-3.5 w-3.5 flex-shrink-0" />
                  <span>{booking.dropoff_location}</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-500 ml-4 mt-1">
                  <CalendarCheck className="h-3.5 w-3.5 flex-shrink-0" />
                  <span>{formatDate(booking.dropoff_datetime)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
