"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, X, FileText, CheckCircle, AlertCircle } from "lucide-react";
import { DocumentUpload, DocumentFile } from "@/components/ui/document-upload";
import { useToast } from "@/hooks/use-toast";

interface DocumentUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  bookingId: string;
  documentType: string;
  documentLabel: string;
  onSuccess?: () => void;
}

export function DocumentUploadModal({
  isOpen,
  onClose,
  bookingId,
  documentType,
  documentLabel,
  onSuccess,
}: DocumentUploadModalProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [files, setFiles] = useState<DocumentFile[]>([]);
  const { toast } = useToast();

  const handleClose = () => {
    setFiles([]);
    onClose();
  };

  const handleSubmitUpload = async () => {
    const uploadedFile = files.find((f) => f.status === "completed");
    if (!uploadedFile?.url) {
      toast({
        variant: "destructive",
        title: "Upload Required",
        description: "Please upload a document before submitting.",
      });
      return;
    }

    setIsUploading(true);

    try {
      // Submit the document to the server
      const response = await fetch("/api/bookings/documents/upload", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          bookingId,
          documentType,
          fileUrl: uploadedFile.url,
          fileName: uploadedFile.fileName || uploadedFile.file.name,
          fileSize: uploadedFile.fileSize || uploadedFile.file.size,
          fileType: uploadedFile.fileType || uploadedFile.file.type,
        }),
      });

      if (response.ok) {
        toast({
          title: "Document Uploaded Successfully",
          description: `Your ${documentLabel} has been uploaded and is pending verification.`,
          className: "bg-white border-green-200",
        });
        handleClose();
        onSuccess?.();
      } else {
        const errorData = await response.json();
        toast({
          variant: "destructive",
          title: "Upload Failed",
          description: errorData.message || "Failed to upload document.",
        });
      }
    } catch (error) {
      console.error("Error uploading document:", error);
      toast({
        variant: "destructive",
        title: "Upload Failed",
        description: "An unexpected error occurred while uploading your document.",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const uploadedFile = files.find((f) => f.status === "completed");

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Upload {documentLabel}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-800 mb-1">
                  Document Requirements
                </h4>
                <p className="text-sm text-blue-700">
                  Please upload a clear, high-quality image or PDF of your {documentLabel.toLowerCase()}. 
                  The document should be readable and all information should be visible.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <DocumentUpload
              label={`Upload ${documentLabel}`}
              description="Upload a clear image or PDF of your document"
              files={files}
              onChange={setFiles}
              accept="image/jpeg,image/jpg,image/png,application/pdf"
              maxFiles={1}
              maxSize={10 * 1024 * 1024} // 10MB
              required
            />
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              variant="secondary"
              onClick={handleClose}
              className="flex-1"
              disabled={isUploading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitUpload}
              disabled={!uploadedFile || isUploading}
              className="flex-1"
            >
              {isUploading ? (
                <>
                  <Upload className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Submit
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
