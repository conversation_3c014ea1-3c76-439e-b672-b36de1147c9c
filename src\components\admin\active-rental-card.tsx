import React from 'react';
import Image from 'next/image';
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { formatCurrency } from '@/lib/utils/format-currency';
import { formatBookingIdForDisplay } from '@/lib/reference-ids';
import { CarIcon } from 'lucide-react';

interface ActiveRentalCardProps {
  booking: {
    id: string;
    status: string;
    total_amount: number;
    pickup_location: string;
    dropoff_location: string;
    pickup_datetime: string;
    dropoff_datetime: string;
  };
  car: {
    model: string;
    plate_number: string;
    image_url?: string;
    type?: string;
    seats?: number;
  };
  customer?: {
    full_name?: string;
    name?: string;
    email?: string;
  };
}

export function ActiveRentalCard({ booking, car, customer }: ActiveRentalCardProps) {
  // Format dates for display
  const formatDateDisplay = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }) + ', ' + date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card className="overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm hover:shadow-lg transition-all duration-300">
      
      <div className="flex flex-col md:flex-row">
        {/* Left side - Car image */}
        <div className="relative w-full md:w-48 h-40 md:h-auto">
          <div className="absolute top-0 left-0 w-full h-full z-10">
            <Image
              src={car?.image_url || '/placeholder.jpg'}
              alt={car?.model || 'Vehicle'}
              fill
              className="object-cover"
            />
          </div>
          <div className="absolute top-2 left-2 z-20">
            <Badge className="bg-emerald-500 text-white border-0 px-2 py-0.5 text-xs font-medium uppercase">
              {booking.status.toUpperCase()}
            </Badge>
          </div>
        </div>
        
        {/* Right side - Details */}
        <div className="flex-1 p-4 md:p-5">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            {/* Car details */}
            <div>
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-bold text-gray-900 text-lg">{car?.model}</h3>
                <span className="text-xs text-gray-500 font-medium">{formatBookingIdForDisplay(booking)}</span>
              </div>
              
              <div className="flex items-center gap-2 mb-3">
                <span className="text-xs text-gray-500">{car?.type || 'Sedan'} • {car?.seats || 5} seats</span>
              </div>
              
              <p className="text-sm text-gray-500 flex items-center gap-1 mb-1">
                <CarIcon className="h-3.5 w-3.5" />
                <span>{car?.plate_number}</span>
              </p>
            </div>
            
            {/* Price */}
            <div className="text-right">
              <p className="text-3xl font-bold text-emerald-600">
                {formatCurrency(booking.total_amount)}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Total Revenue
              </p>
            </div>
          </div>
          
          {/* Pickup and Return Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 pt-4 border-t border-gray-200">
            <div>
              <div className="flex items-center gap-2 mb-1">
                <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                <p className="text-base font-medium text-gray-900">Pickup</p>
              </div>
              <p className="text-sm text-gray-500 ml-4 mb-1">{booking.pickup_location}</p>
              <p className="text-sm text-gray-500 ml-4">{formatDateDisplay(booking.pickup_datetime)}</p>
            </div>
            
            <div>
              <div className="flex items-center gap-2 mb-1">
                <div className="w-2 h-2 rounded-full bg-red-500"></div>
                <p className="text-base font-medium text-gray-900">Return</p>
              </div>
              <p className="text-sm text-gray-500 ml-4 mb-1">{booking.dropoff_location}</p>
              <p className="text-sm text-gray-500 ml-4">{formatDateDisplay(booking.dropoff_datetime)}</p>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
