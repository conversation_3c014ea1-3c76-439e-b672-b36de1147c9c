-- ============================================
-- Booking Extensions Schema
-- ============================================
-- This schema creates the booking_extensions table for managing
-- customer requests to extend their active bookings

-- Create booking_extensions table
CREATE TABLE IF NOT EXISTS public.booking_extensions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  booking_id UUID NOT NULL REFERENCES public.bookings(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  
  -- Original booking details
  original_dropoff_datetime TIMESTAMPTZ NOT NULL,
  
  -- Extension request details
  requested_dropoff_datetime TIMESTAMPTZ NOT NULL,
  extension_duration_hours INTEGER NOT NULL, -- Duration in hours
  additional_amount DECIMAL(10,2) NOT NULL DEFAULT 0, -- Extra cost for extension
  
  -- Request details
  request_reason TEXT,
  request_notes TEXT,
  
  -- Status management
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
  
  -- Admin review details
  reviewed_by UUID REFERENCES public.profiles(id),
  reviewed_at TIMESTAMPTZ,
  admin_notes TEXT,
  rejection_reason TEXT,
  
  -- Alternative suggestions (if original request conflicts)
  has_conflicts BOOLEAN DEFAULT FALSE,
  alternative_cars TEXT[], -- Array of alternative car IDs if conflicts exist
  alternative_suggestions JSONB, -- Detailed alternative suggestions
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '24 hours'), -- Auto-expire after 24 hours
  
  -- Constraints
  CONSTRAINT valid_extension_time CHECK (requested_dropoff_datetime > original_dropoff_datetime),
  CONSTRAINT valid_duration CHECK (extension_duration_hours > 0),
  CONSTRAINT valid_amount CHECK (additional_amount >= 0)
);

-- Enable RLS for booking_extensions
ALTER TABLE public.booking_extensions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for booking_extensions
CREATE POLICY "Customers can view their own extension requests"
  ON public.booking_extensions FOR SELECT
  USING (customer_id = (SELECT auth.uid()));

CREATE POLICY "Customers can create extension requests for their bookings"
  ON public.booking_extensions FOR INSERT
  WITH CHECK (
    customer_id = (SELECT auth.uid()) AND
    EXISTS (
      SELECT 1 FROM public.bookings 
      WHERE id = booking_id AND customer_id = (SELECT auth.uid())
    )
  );

CREATE POLICY "Customers can update their pending extension requests"
  ON public.booking_extensions FOR UPDATE
  USING (customer_id = (SELECT auth.uid()) AND status = 'pending')
  WITH CHECK (customer_id = (SELECT auth.uid()));

CREATE POLICY "Admins can view all extension requests"
  ON public.booking_extensions FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role IN ('admin', 'super_admin')
    )
  );

CREATE POLICY "Admins can update extension requests"
  ON public.booking_extensions FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role IN ('admin', 'super_admin')
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role IN ('admin', 'super_admin')
    )
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_booking_extensions_booking_id ON public.booking_extensions(booking_id);
CREATE INDEX IF NOT EXISTS idx_booking_extensions_customer_id ON public.booking_extensions(customer_id);
CREATE INDEX IF NOT EXISTS idx_booking_extensions_status ON public.booking_extensions(status);
CREATE INDEX IF NOT EXISTS idx_booking_extensions_created_at ON public.booking_extensions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_booking_extensions_expires_at ON public.booking_extensions(expires_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_booking_extension_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_booking_extensions_updated_at
  BEFORE UPDATE ON public.booking_extensions
  FOR EACH ROW
  EXECUTE FUNCTION update_booking_extension_updated_at();

-- Create function to auto-expire old extension requests
CREATE OR REPLACE FUNCTION expire_old_extension_requests()
RETURNS void AS $$
BEGIN
  UPDATE public.booking_extensions
  SET status = 'expired', updated_at = NOW()
  WHERE status = 'pending' AND expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Add notification triggers table for real-time updates
CREATE TABLE IF NOT EXISTS public.booking_extension_notifications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  extension_id UUID NOT NULL REFERENCES public.booking_extensions(id) ON DELETE CASCADE,
  recipient_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  notification_type TEXT NOT NULL CHECK (notification_type IN ('request_created', 'request_approved', 'request_rejected', 'request_expired')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS for notifications
ALTER TABLE public.booking_extension_notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own extension notifications"
  ON public.booking_extension_notifications FOR SELECT
  USING (recipient_id = (SELECT auth.uid()));

CREATE POLICY "System can insert extension notifications"
  ON public.booking_extension_notifications FOR INSERT
  WITH CHECK (true);

-- Create indexes for notifications
CREATE INDEX IF NOT EXISTS idx_extension_notifications_recipient ON public.booking_extension_notifications(recipient_id);
CREATE INDEX IF NOT EXISTS idx_extension_notifications_created_at ON public.booking_extension_notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_extension_notifications_is_read ON public.booking_extension_notifications(is_read);

-- Comments for documentation
COMMENT ON TABLE public.booking_extensions IS 'Stores customer requests to extend their active booking periods';
COMMENT ON COLUMN public.booking_extensions.extension_duration_hours IS 'Duration of extension requested in hours';
COMMENT ON COLUMN public.booking_extensions.additional_amount IS 'Additional cost calculated for the extension period';
COMMENT ON COLUMN public.booking_extensions.has_conflicts IS 'Whether the extension request conflicts with other bookings';
COMMENT ON COLUMN public.booking_extensions.alternative_cars IS 'Array of alternative car IDs if conflicts exist';
COMMENT ON COLUMN public.booking_extensions.expires_at IS 'Timestamp when pending request automatically expires';
