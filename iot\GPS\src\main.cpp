// ============================================================
// LILYGO T-Call A7670E GPS Tracker with EMQ MQTT
// Sends GPS coordinates via MQTT to pathlink/gps/lilygo-a7670e-01
// Data flows: ESP32 → EMQ Broker → MQTT Consumer → Database → Admin UI
// ============================================================

#define TINY_GSM_MODEM_SIM7600    
#define TINY_GSM_USE_GPRS true
#define TINY_GSM_USE_WIFI false
#define TINY_GSM_RX_BUFFER 1024

#define LILYGO_T_CALL_A7670_V1_0

#include "utilities.h"
#include <ArduinoJson.h>
#include <PubSubClient.h>

#define SerialMon Serial
#define TINY_GSM_DEBUG SerialMon

#include <TinyGsmClient.h>
TinyGsm modem(SerialAT);
TinyGsmClient client(modem);

// MQTT client using TinyGSM (recommended approach for A7670E)
PubSubClient mqtt(client);

// ====== Network settings ======
#define GPRS_APN  "internet"
#define GPRS_USER ""
#define GPRS_PASS ""

// ====== MQTT Settings (EMQ Public Broker - Reliable) ======
#define MQTT_BROKER   "broker.emqx.io"
#define MQTT_PORT     1883
#define MQTT_USERNAME ""
#define MQTT_PASSWORD ""
#define MQTT_CLIENT_ID "lilygo-a7670e-01"
#define MQTT_TOPIC    "pathlink/gps/lilygo-a7670e-01"

// ====== GPS settings ======
#define DEVICE_ID     "lilygo-a7670e-01"
#define GPS_INTERVAL_MS 10000  // Send GPS every 10 seconds

// ====== Tracking cadence (from old code) ======
static const uint32_t FIX_INTERVAL_MS    = 5000;
static const uint32_t NOFIX_INTERVAL_MS  = 15000;
static const uint8_t  KICK_AFTER_N_NOFIX = 10;

// ----- GPS helpers (from old code) -----
static bool looksLikeA7670E(const String &s) {
  String u = s; u.toUpperCase();
  return u.indexOf("A7670") >= 0 || u.indexOf("SIMCOM") >= 0;
}

// ---- GNSS diagnostics + QoL helpers (from old code) ----
static void gnssDiagnostics() {
  String r;
  SerialMon.println("=== GNSS DIAGNOSTICS (A7670E) ===");
  modem.sendAT("+CGNSSPWR?");  modem.waitResponse(2000, r); SerialMon.println("CGNSSPWR?: " + r);
  modem.sendAT("+CGNSSMODE?"); modem.waitResponse(2000, r); SerialMon.println("CGNSSMODE?: " + r);
  modem.sendAT("+CGNSSINFO");  modem.waitResponse(3000, r); SerialMon.println("CGNSSINFO: " + r);
  SerialMon.println("==================================");
}

// Optional: minor QoL for faster TTFF (time sync, no slow clock)
static void gnssQoL() {
  String r;
  modem.sendAT("+CTZU=1");  modem.waitResponse(2000, r);  // auto time update
  modem.sendAT("+CTZR=1");  modem.waitResponse(2000, r);  // time zone reporting
  modem.sendAT("+CSCLK=0"); modem.waitResponse(2000, r);  // no slow clock
}

// ---- GNSS kick (A7670E-friendly) ----
static void gnssKick() {
  String r;
  SerialMon.println("🔧 GNSS reconfigure (A7670E)...");
  modem.sendAT("+CGNSSPWR=1");     modem.waitResponse(2000, r); // GNSS rail on
  modem.sendAT("+CGNSSMODE=3");    modem.waitResponse(2000, r); // 3 = GPS+GLONASS on this FW
  // No +CGNSSNMEA on this firmware; we read via TinyGSM & +CGNSSINFO.
}

// --- CGNSSINFO fallback parser (from old code) ---
// Parses: +CGNSSINFO: <fix>,<vsat>,,<usat>,<mode>,<lat>,<N/S>,<lon>,<E/W>,<date>,<utc>,<alt>,<speed>,<course>,<hdop>,<pdop>,<vdop>
static bool parseCGNSSINFO(const String& s, double& lat, double& lon, double& acc, uint8_t& fixMode) {
  int p = s.indexOf("+CGNSSINFO:");
  if (p < 0) return false;

  String line = s.substring(p + 11);
  line.trim();
  if (line.startsWith(":")) line.remove(0, 1);
  line.trim();

  // Split by commas
  String f[24];
  int idx = 0, start = 0;
  for (; idx < 24; idx++) {
    int c = line.indexOf(',', start);
    if (c < 0) { f[idx] = line.substring(start); idx++; break; }
    f[idx] = line.substring(start, c);
    start = c + 1;
  }
  int n = idx;
  if (n < 15) return false;

  fixMode = (uint8_t) f[0].toInt();   // 2 = 2D, 3 = 3D
  if (fixMode < 2) return false;

  String slat = f[5]; slat.trim();
  String ns   = (n > 6 ? f[6] : ""); ns.trim();
  String slon = (n > 7 ? f[7] : ""); slon.trim();
  String ew   = (n > 8 ? f[8] : ""); ew.trim();
  if (slat.length() == 0 || slon.length() == 0) return false;

  lat = slat.toFloat();
  lon = slon.toFloat();
  if (ns == "S") lat = -lat;
  if (ew == "W") lon = -lon;

  double hdop = (n > 14 ? f[14].toFloat() : 0.0);
  acc = (hdop > 0.0) ? (hdop * 6.0) : 10.0; // rough estimate

  return (lat != 0.0 || lon != 0.0);
}

// MQTT callback for incoming messages
void mqttCallback(char* topic, byte* payload, unsigned int length) {
  SerialMon.print("📨 MQTT message arrived [");
  SerialMon.print(topic);
  SerialMon.print("]: ");
  for (int i = 0; i < length; i++) {
    SerialMon.print((char)payload[i]);
  }
  SerialMon.println();
}

// TinyGSM + PubSubClient MQTT connection (recommended for A7670E)
bool mqttConnect() {
  SerialMon.println("🔗 Connecting to MQTT broker using TinyGSM + PubSubClient...");
  
  // 🔍 DEBUG: Check network status first
  SerialMon.print("🔍 Network status: "); SerialMon.println(modem.isNetworkConnected() ? "CONNECTED" : "DISCONNECTED");
  SerialMon.print("🔍 GPRS status: "); SerialMon.println(modem.isGprsConnected() ? "CONNECTED" : "DISCONNECTED");
  
  // Set MQTT server and callback
  mqtt.setServer(MQTT_BROKER, MQTT_PORT);
  mqtt.setCallback(mqttCallback);
  
  SerialMon.println("🔍 Attempting MQTT connection to: " + String(MQTT_BROKER) + ":" + String(MQTT_PORT));
  
  // Connect with client ID (no username/password for EMQ public broker)
  bool connected = mqtt.connect(MQTT_CLIENT_ID);
  
  if (connected) {
    SerialMon.println("✅ MQTT connected successfully with TinyGSM!");
    SerialMon.println("🔍 Client ID: " + String(MQTT_CLIENT_ID));
    return true;
  } else {
    SerialMon.print("❌ MQTT connection failed, state: ");
    SerialMon.println(mqtt.state());
    
    // MQTT state codes:
    // -4 : MQTT_CONNECTION_TIMEOUT
    // -3 : MQTT_CONNECTION_LOST  
    // -2 : MQTT_CONNECT_FAILED
    // -1 : MQTT_DISCONNECTED
    //  0 : MQTT_CONNECTED
    //  1 : MQTT_CONNECT_UNAUTHORIZED
    //  2 : MQTT_CONNECT_ID_REJECTED
    //  3 : MQTT_CONNECT_SERVER_UNAVAILABLE
    //  4 : MQTT_CONNECT_BAD_CREDENTIALS
    //  5 : MQTT_CONNECT_UNAUTHORIZED
    
    switch (mqtt.state()) {
      case -4:
        SerialMon.println("🔍 Error: Connection timeout");
        break;
      case -3:
        SerialMon.println("🔍 Error: Connection lost");
        break;
      case -2:
        SerialMon.println("🔍 Error: Connect failed");
        break;
      case -1:
        SerialMon.println("🔍 Error: Disconnected");
        break;
      case 1:
        SerialMon.println("🔍 Error: Bad protocol version");
        break;
      case 2:
        SerialMon.println("🔍 Error: Client ID rejected");
        break;
      case 3:
        SerialMon.println("🔍 Error: Server unavailable");
        break;
      case 4:
        SerialMon.println("🔍 Error: Bad credentials");
        break;
      case 5:
        SerialMon.println("🔍 Error: Not authorized");
        break;
      default:
        SerialMon.println("🔍 Error: Unknown error");
    }
    return false;
  }
}

bool publishGPSData(double lat, double lon, double speed, double heading, double accuracy, int satellites, int fixMode) {
  // 🔍 DEBUG: Track function entry
  SerialMon.println("🔍 DEBUG: publishGPSData() function called (TinyGSM method)");
  SerialMon.print("🔍 Input: lat="); SerialMon.print(lat, 6);
  SerialMon.print(", lon="); SerialMon.print(lon, 6);
  SerialMon.print(", speed="); SerialMon.print(speed);
  SerialMon.print(", heading="); SerialMon.print(heading);
  SerialMon.print(", accuracy="); SerialMon.print(accuracy);
  SerialMon.print(", satellites="); SerialMon.print(satellites);
  SerialMon.print(", fixMode="); SerialMon.println(fixMode);
  
  // 🔍 DEBUG: Check network status before creating payload
  SerialMon.print("🔍 Network connected: "); SerialMon.println(modem.isNetworkConnected() ? "YES" : "NO");
  SerialMon.print("🔍 GPRS connected: "); SerialMon.println(modem.isGprsConnected() ? "YES" : "NO");
  
  // Check MQTT connection status
  if (!mqtt.connected()) {
    SerialMon.println("🔍 MQTT not connected, attempting to reconnect...");
    if (!mqttConnect()) {
      SerialMon.println("❌ Failed to reconnect to MQTT broker");
      return false;
    }
  }
  
  // Create JSON payload with GPS quality data
  DynamicJsonDocument doc(300);  // Increased size for additional fields
  doc["deviceId"] = DEVICE_ID;
  doc["lat"] = lat;
  doc["lng"] = lon;
  doc["speed"] = speed;
  doc["heading"] = heading;
  doc["accuracy"] = accuracy;      // GPS accuracy in meters
  doc["satellites"] = satellites;  // Number of satellites
  doc["fixMode"] = fixMode;       // GPS fix mode (2=2D, 3=3D)
  doc["timestamp"] = millis();
  
  String payload;
  serializeJson(doc, payload);
  
  SerialMon.println("📡 Publishing GPS data using TinyGSM + PubSubClient:");
  SerialMon.println("Topic: " + String(MQTT_TOPIC));
  SerialMon.println("Payload: " + payload);
  
  // Publish using PubSubClient (much more reliable than AT commands)
  bool published = mqtt.publish(MQTT_TOPIC, payload.c_str());
  
  if (published) {
    SerialMon.println("✅ GPS data published successfully with TinyGSM!");
    
    // Process any pending MQTT messages
    mqtt.loop();
    
    return true;
  } else {
    SerialMon.println("❌ Failed to publish GPS data");
    SerialMon.print("🔍 MQTT state: "); SerialMon.println(mqtt.state());
    
    // Try to reconnect if publish failed
    if (!mqtt.connected()) {
      SerialMon.println("🔍 MQTT disconnected during publish, will retry on next attempt");
    }
    
    return false;
  }
}

void setup() {
  SerialMon.begin(115200);
  SerialMon.println();
  SerialMon.println("🛰️ EMQ MQTT GPS Tracker Starting...");

  // Hardware initialization (same as HTTP version)
#ifdef BOARD_POWERON_PIN
  pinMode(BOARD_POWERON_PIN, OUTPUT);
  digitalWrite(BOARD_POWERON_PIN, HIGH);
#endif

#ifdef MODEM_RESET_PIN
  pinMode(MODEM_RESET_PIN, OUTPUT);
  digitalWrite(MODEM_RESET_PIN, !MODEM_RESET_LEVEL); delay(100);
  digitalWrite(MODEM_RESET_PIN,  MODEM_RESET_LEVEL);  delay(2600);
  digitalWrite(MODEM_RESET_PIN, !MODEM_RESET_LEVEL);
#endif

  pinMode(MODEM_DTR_PIN, OUTPUT);
  digitalWrite(MODEM_DTR_PIN, LOW);

  pinMode(BOARD_PWRKEY_PIN, OUTPUT);
  digitalWrite(BOARD_PWRKEY_PIN, LOW); delay(100);
  digitalWrite(BOARD_PWRKEY_PIN, HIGH); delay(MODEM_POWERON_PULSE_WIDTH_MS);
  digitalWrite(BOARD_PWRKEY_PIN, LOW);

  SerialAT.begin(115200, SERIAL_8N1, MODEM_RX_PIN, MODEM_TX_PIN);
  delay(6000);

  // Initialize modem with better identification (from old code)
  SerialMon.println("🔗 Testing AT...");
  modem.sendAT("E0"); modem.waitResponse(1000);
  for (int i = 0; i < 10 && !modem.testAT(1000); i++) {
    SerialMon.print(".");
    delay(500);
  }
  SerialMon.println("\n✅ AT ready (or continuing)");

  // Identify modem (from old code)
  String name = modem.getModemName();
  SerialMon.print("📱 Modem: "); SerialMon.println(name);
  String id;
  modem.sendAT("I");          modem.waitResponse(2000, id);
  modem.sendAT("+CGMM");      modem.waitResponse(2000, id);
  modem.sendAT("+SIMCOMATI"); modem.waitResponse(4000, id);
  if (id.length()) SerialMon.println("📋 Modem ID: " + id);
  SerialMon.println(looksLikeA7670E(name) ? "✅ A7670E series detected" : "⚠️ Unknown modem");

  // Enable GPS with enhanced logic (from old code)
  SerialMon.println("🛰️ Enabling GPS...");
  int g = 0;
  while (!modem.enableGPS() && g < 20) {
    SerialMon.print(".");
    g++;
    delay(500);
  }
  SerialMon.println(g>=20 ? "\n⚠️ TinyGSM enableGPS() didn't confirm — proceeding with direct AT" : "\n✅ GPS enabled");

  gnssQoL();     // time sync + no slow clock (from old code)
  gnssKick();    // prime GNSS (from old code)

  // Connect PDP (from old code)
  SerialMon.println("📶 Connecting PDP (4G/LTE)...");
  SerialMon.print("APN: "); SerialMon.println(GPRS_APN);
  String reg;
  modem.sendAT("+CEREG?");                // LTE/EPS registration
  modem.waitResponse(2000, reg);
  SerialMon.println("LTE reg (CEREG): " + reg);

  int n = 0;
  while (!modem.gprsConnect(GPRS_APN, GPRS_USER, GPRS_PASS) && n < 10) {
    SerialMon.print(".");
    n++;
    delay(1000);
  }
  
  if (n >= 10) {
    SerialMon.println("\n❌ PDP failed (check APN/coverage/SIM)");
    return;
  }
  
  SerialMon.println("\n✅ PDP connected");
  SerialMon.print("📍 IP: ");
  SerialMon.println(modem.localIP());
  gnssDiagnostics(); // snapshot GNSS state at boot (from old code)
  SerialMon.print("📶 RSSI: "); SerialMon.println(modem.getSignalQuality());

  // Connect to MQTT broker with retry logic
  int mqttRetries = 0;
  while (!mqttConnect() && mqttRetries < 5) {
    mqttRetries++;
    SerialMon.println("❌ MQTT connection failed, retry " + String(mqttRetries) + "/5");
    delay(5000);  // Wait before retry
  }
  
  if (mqttRetries >= 5) {
    SerialMon.println("❌ Failed to connect to MQTT broker after 5 attempts");
    SerialMon.println("⚠️ Continuing with GPS tracking, will retry MQTT on each publish attempt");
  } else {
    SerialMon.println("✅ MQTT broker connected successfully");
  }

  SerialMon.println("🎯 EMQ MQTT GPS tracking started");
}

void loop() {
  static uint32_t nextPollMs = 0;
  static uint8_t  nofixCount = 0;

  uint32_t now = millis();
  if ((int32_t)(now - nextPollMs) < 0) { delay(5); return; }

  // --- GNSS read (enhanced logic from old code) ---
  float lat=0, lon=0, speed=0, alt=0, acc=0;
  int vsat=0, usat=0, year=0, month=0, day=0, hour=0, minute=0, sec=0;
  uint8_t fixMode = 0, gpsStatus = 0;

  SerialMon.println("📍 Requesting GPS...");

  // TinyGSM getGPS signature (14 args including status parameter)
  bool ok = modem.getGPS(&gpsStatus, &lat, &lon, &speed, &alt, &vsat, &usat, &acc,
                         &year, &month, &day, &hour, &minute, &sec);

  bool haveFix = ok && lat != 0 && lon != 0;

  // If we have a fix via TinyGSM, fetch fixMode via CGNSSINFO (to preserve your UI readout)
  if (haveFix) {
    String ginfo;
    modem.sendAT("+CGNSSINFO"); modem.waitResponse(2000, ginfo);
    double dummyLat=0, dummyLon=0, dummyAcc=0; uint8_t fm=0;
    if (parseCGNSSINFO(ginfo, dummyLat, dummyLon, dummyAcc, fm)) {
      fixMode = fm; // 2 = 2D, 3 = 3D
    }
  }

  // Fallback: parse +CGNSSINFO if TinyGSM path didn't return a fix
  if (!haveFix) {
    String resp;
    modem.sendAT("+CGNSSINFO"); modem.waitResponse(3000, resp);

    double flat=0, flon=0, facc=0; uint8_t fmode=0;
    if (parseCGNSSINFO(resp, flat, flon, facc, fmode)) {
      haveFix = true;
      fixMode = fmode;
      lat = flat; lon = flon; acc = facc;
    }
  }

  if (haveFix) {
    nofixCount = 0;

    SerialMon.println("🎯 GPS FIX ACQUIRED!");
    SerialMon.print("📍 Fix Mode: "); SerialMon.println(fixMode);
    SerialMon.print("🌍 Latitude: "); SerialMon.print(lat, 6);
    SerialMon.print(" | Longitude: "); SerialMon.println(lon, 6);
    SerialMon.print("🎯 Accuracy (est): "); SerialMon.print(acc, 1); SerialMon.println(" m");

    // 🔍 DEBUG: Show MQTT publish attempt with GPS quality
    SerialMon.println("🔍 DEBUG: About to publish GPS data via MQTT...");
    SerialMon.print("🔍 GPS Data: lat="); SerialMon.print(lat, 6);
    SerialMon.print(", lon="); SerialMon.print(lon, 6);
    SerialMon.print(", speed="); SerialMon.print(speed);
    SerialMon.print(", accuracy="); SerialMon.print(acc, 1);
    SerialMon.print("m, satellites="); SerialMon.print(vsat);
    SerialMon.print(", fixMode="); SerialMon.print(fixMode);
    SerialMon.print(", timestamp="); SerialMon.println(millis());

    // Publish to MQTT with GPS quality data
    bool publishResult = publishGPSData(lat, lon, speed, 0, acc, vsat, fixMode);
    
    // 🔍 DEBUG: Show detailed publish result
    if (publishResult) {
      SerialMon.println("✅ GPS data sent via MQTT - SUCCESS!");
      SerialMon.println("🔍 DEBUG: MQTT publish returned TRUE");
    } else {
      SerialMon.println("❌ Failed to send GPS data via MQTT - FAILED!");
      SerialMon.println("🔍 DEBUG: MQTT publish returned FALSE - Check MQTT connection!");
    }

    // 🔍 DEBUG: Show next poll timing
    SerialMon.print("🔍 Next GPS poll in "); SerialMon.print(FIX_INTERVAL_MS);
    SerialMon.println(" ms (FIX_INTERVAL_MS)");
    nextPollMs = now + FIX_INTERVAL_MS;
  } else {
    // No fix yet → show status and maybe re-kick
    String r;
    modem.sendAT("+CGNSSINFO"); modem.waitResponse(2000, r);
    if (r.length()) SerialMon.println("📡 CGNSSINFO: " + r);

    nofixCount++;
    if (nofixCount >= KICK_AFTER_N_NOFIX) {
      SerialMon.println("🔄 No fix for a while — diagnostics + reconfigure");
      gnssDiagnostics();
      gnssKick();
      nofixCount = 0;
    }
    SerialMon.printf("⏳ Waiting for GPS fix... (%u/%u)\n", nofixCount, KICK_AFTER_N_NOFIX);
    nextPollMs = now + NOFIX_INTERVAL_MS;
  }
}