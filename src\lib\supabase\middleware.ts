import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  // Route protection logic
  const path = request.nextUrl.pathname
  const isAdminRoute = path.startsWith('/admin') && !path.startsWith('/admin-auth')

  // Create context-specific client based on route
  const createContextSpecificClient = (context?: 'admin' | 'customer') => {
    const cookiePrefix = context ? `sb-${context}-auth-token` : 'sb-'

    return createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        auth: context ? {
          storageKey: cookiePrefix, // Context-specific storage key
        } : undefined,
        cookies: {
          getAll() {
            const allCookies = request.cookies.getAll()
            // Filter cookies to only return context-specific ones if context is specified
            if (context) {
              return allCookies.filter(cookie =>
                cookie.name.startsWith(cookiePrefix) ||
                cookie.name.startsWith('sb-') && cookie.name.includes(context)
              )
            }
            return allCookies
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              // Ensure cookie names are context-specific if context is provided
              const contextName = context && name.startsWith('sb-') && !name.includes(context)
                ? name.replace('sb-', `sb-${context}-`)
                : name
              request.cookies.set(contextName, value)
            })
            supabaseResponse = NextResponse.next({
              request,
            })
            cookiesToSet.forEach(({ name, value, options }) => {
              const contextName = context && name.startsWith('sb-') && !name.includes(context)
                ? name.replace('sb-', `sb-${context}-`)
                : name
              supabaseResponse.cookies.set(contextName, value, options)
            })
          },
        },
      }
    )
  }

  // Use context-specific client for admin routes, generic for others
  const supabase = isAdminRoute
    ? createContextSpecificClient('admin')
    : createContextSpecificClient()

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.
  const {
    data: { user },
  } = await supabase.auth.getUser()
  
  const isAuthPage = path.startsWith('/auth') ||
                     path.startsWith('/customer/login') ||
                     path.startsWith('/customer/signup') ||
                     path.startsWith('/customer/forgot-password') ||
                     path.startsWith('/admin-auth')
  const isCustomerProtectedRoute = path.startsWith('/customer') && 
                                   !path.startsWith('/customer/login') &&
                                   !path.startsWith('/customer/signup') &&
                                   !path.startsWith('/customer/forgot-password') &&
                                   !path.startsWith('/customer/catalog') &&
                                   !path.startsWith('/customer/contact') &&
                                   !path.startsWith('/customer/faq') &&
                                   !path.startsWith('/customer/terms')

  // If no user and trying to access protected routes, redirect to appropriate login
  if (!user && !isAuthPage) {
    if (isAdminRoute) {
      const url = request.nextUrl.clone()
      url.pathname = '/admin-auth'
      url.searchParams.set('redirect', path) // Save original path for redirect after login
      return NextResponse.redirect(url)
    }
    if (isCustomerProtectedRoute) {
      const url = request.nextUrl.clone()
      url.pathname = '/customer/login'
      url.searchParams.set('redirect', path) // Save original path for redirect after login
      return NextResponse.redirect(url)
    }
  }

  // If user exists, check role-based access
  if (user) {
    // Get user profile to check role (more reliable than user metadata)
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    const userRole = profile?.role || user.user_metadata?.role || 'customer'
    const isAdminUser = userRole === 'admin' || userRole === 'super_admin'

    // Check for super admin email as fallback
    const superAdminEmail = process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAIL || '<EMAIL>'
    const isSuperAdminByEmail = user.email === superAdminEmail

    // Admin trying to access customer routes (redirect to admin dashboard)
    if (isCustomerProtectedRoute && (isAdminUser || isSuperAdminByEmail)) {
      const url = request.nextUrl.clone()
      url.pathname = '/admin'
      return NextResponse.redirect(url)
    }

    // Customer trying to access admin routes (redirect to customer login with error)
    if (isAdminRoute && !isAdminUser && !isSuperAdminByEmail) {
      const url = request.nextUrl.clone()
      url.pathname = '/admin-auth'
      url.searchParams.set('error', 'Access denied. Admin privileges required.')
      return NextResponse.redirect(url)
    }

    // Redirect authenticated users away from auth pages to their appropriate dashboard
    if (isAuthPage && !path.includes('forgot-password')) {
      const redirectParam = request.nextUrl.searchParams.get('redirect')
      const url = request.nextUrl.clone()
      
      if (redirectParam) {
        // If there's a redirect parameter, go there (but verify it's allowed for the user role)
        const redirectPath = redirectParam
        const isRedirectToAdmin = redirectPath.startsWith('/admin')
        const isRedirectToCustomer = redirectPath.startsWith('/customer')
        
        if ((isAdminUser || isSuperAdminByEmail) && (isRedirectToAdmin || !isRedirectToCustomer)) {
          url.pathname = redirectPath
        } else if (!(isAdminUser || isSuperAdminByEmail) && !isRedirectToAdmin) {
          url.pathname = redirectPath
        } else {
          // Fallback to default dashboard for the user role
          url.pathname = (isAdminUser || isSuperAdminByEmail) ? '/admin' : '/'
        }
      } else {
        // No redirect parameter, go to default dashboard
        url.pathname = (isAdminUser || isSuperAdminByEmail) ? '/admin' : '/'
      }
      
      url.searchParams.delete('redirect')
      url.searchParams.delete('error')
      return NextResponse.redirect(url)
    }
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is.
  return supabaseResponse
}
