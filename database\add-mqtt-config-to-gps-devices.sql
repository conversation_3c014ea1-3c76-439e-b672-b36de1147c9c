-- ============================================
-- NON-DESTRUCTIVE: ADD MQTT CONFIGURATION TO GPS DEVICE MAPPING
-- ============================================
-- Add MQTT configuration fields for ESP32 GPS devices
-- This script is completely safe and non-destructive

-- Add MQTT configuration columns to gps_device_mapping table (safe)
ALTER TABLE public.gps_device_mapping 
ADD COLUMN IF NOT EXISTS mqtt_broker_host text,
ADD COLUMN IF NOT EXISTS mqtt_broker_port integer DEFAULT 1883,
ADD COLUMN IF NOT EXISTS mqtt_client_id text,
ADD COLUMN IF NOT EXISTS mqtt_pub_topic text,
ADD COLUMN IF NOT EXISTS mqtt_sub_topic text,
ADD COLUMN IF NOT EXISTS mqtt_username text,
ADD COLUMN IF NOT EXISTS mqtt_password text;

-- Add constraints for MQTT fields (safe)
DO $$
BEGIN
  -- Add check constraint for mqtt_broker_port if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints 
    WHERE constraint_name = 'gps_device_mapping_mqtt_port_check'
  ) THEN
    ALTER TABLE public.gps_device_mapping 
    ADD CONSTRAINT gps_device_mapping_mqtt_port_check 
    CHECK (mqtt_broker_port IS NULL OR (mqtt_broker_port > 0 AND mqtt_broker_port <= 65535));
  END IF;
END $$;

-- Create indexes for MQTT fields (safe)
CREATE INDEX IF NOT EXISTS idx_gps_device_mapping_mqtt_broker on public.gps_device_mapping using btree (mqtt_broker_host);
CREATE INDEX IF NOT EXISTS idx_gps_device_mapping_mqtt_client on public.gps_device_mapping using btree (mqtt_client_id);

-- Create NEW function with MQTT fields (without dropping existing one)
-- This creates a new function name so it doesn't conflict
CREATE OR REPLACE FUNCTION public.get_gps_devices_with_mqtt_config()
RETURNS TABLE (
  id uuid,
  device_id text,
  car_id uuid,
  device_name text,
  device_type text,
  is_active boolean,
  mqtt_broker_host text,
  mqtt_broker_port integer,
  mqtt_client_id text,
  mqtt_pub_topic text,
  mqtt_sub_topic text,
  mqtt_username text,
  mqtt_password text,
  car_model text,
  car_plate text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    gdm.id,
    gdm.device_id,
    gdm.car_id,
    gdm.device_name,
    gdm.device_type,
    gdm.is_active,
    gdm.mqtt_broker_host,
    gdm.mqtt_broker_port,
    gdm.mqtt_client_id,
    gdm.mqtt_pub_topic,
    gdm.mqtt_sub_topic,
    gdm.mqtt_username,
    gdm.mqtt_password,
    c.model as car_model,
    c.plate_number as car_plate,
    gdm.created_at,
    gdm.updated_at
  FROM public.gps_device_mapping gdm
  LEFT JOIN public.cars c ON gdm.car_id = c.id
  ORDER BY gdm.created_at DESC;
END;
$$;

-- Grant permissions to the new function
GRANT EXECUTE ON FUNCTION public.get_gps_devices_with_mqtt_config() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_gps_devices_with_mqtt_config() TO service_role;

-- Verify the schema changes
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'gps_device_mapping' 
AND column_name LIKE '%mqtt%'
ORDER BY ordinal_position;

-- Add comments for documentation
COMMENT ON COLUMN public.gps_device_mapping.mqtt_broker_host IS 'MQTT broker hostname (e.g., mqtt.waveshare.cloud)';
COMMENT ON COLUMN public.gps_device_mapping.mqtt_broker_port IS 'MQTT broker port (default: 1883)';
COMMENT ON COLUMN public.gps_device_mapping.mqtt_client_id IS 'Unique MQTT client identifier';
COMMENT ON COLUMN public.gps_device_mapping.mqtt_pub_topic IS 'MQTT topic for publishing GPS data';
COMMENT ON COLUMN public.gps_device_mapping.mqtt_sub_topic IS 'MQTT topic for receiving commands';
COMMENT ON COLUMN public.gps_device_mapping.mqtt_username IS 'MQTT authentication username (optional)';
COMMENT ON COLUMN public.gps_device_mapping.mqtt_password IS 'MQTT authentication password (optional)';
