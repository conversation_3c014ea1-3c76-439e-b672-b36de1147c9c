"use client";

import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { ArrowLeft, Mail } from "lucide-react";
import Image from "next/image";
import { CustomerLoadingButton } from "@/components/customer-side/loading/enhanced-loading-components";
import Link from "next/link";

export default function CustomerForgotPasswordPage() {
  const { resetPassword } = useCustomerAuth();
  const [email, setEmail] = React.useState("");
  const [isSubmitted, setIsSubmitted] = React.useState(false);
  const [error, setError] = React.useState("");
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (isSubmitting) return;

    setError("");
    setIsSubmitting(true);

    try {
      const { error } = await resetPassword(email);

      if (error) {
        setError(
          error.message || "An error occurred while sending the reset email."
        );
        return;
      }

      setIsSubmitted(true);
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen grid place-items-center bg-muted/20 px-4">
        {/* Back to Homepage Button */}
        <div className="fixed top-4 left-4 z-10">
          <Link href="/">
            <Button
              variant="secondary"
              size="sm"
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Homepage
            </Button>
          </Link>
        </div>

        <Card className="w-full max-w-md">
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full">
                <Mail className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <CardTitle className="text-xl font-semibold">
              Check Your Email
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              We&apos;ve sent password reset instructions to {email}
            </p>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-center text-muted-foreground mb-4">
              Didn&apos;t receive the email? Check your spam folder or try
              again.
            </p>
          </CardContent>
          <CardFooter className="text-center">
            <a
              href="/customer/login"
              className="inline-flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 hover:underline"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to sign in
            </a>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen grid place-items-center bg-muted/20 px-4">
      {/* Back to Homepage Button */}
      <div className="fixed top-4 left-4 z-10">
        <Link href="/">
          <Button
            variant="secondary"
            size="sm"
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Homepage
          </Button>
        </Link>
      </div>

      <Card className="w-full max-w-md">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <Link href="/" className="cursor-pointer">
              <Image
                src="/ollie_logo.jpg"
                alt="Ollie's Rent A Car"
                width={120}
                height={40}
                className="h-10 w-auto hover:opacity-80 transition-opacity"
                priority
              />
            </Link>
          </div>
          <CardTitle className="text-xl font-semibold">
            Reset Your Password
          </CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Enter your email address and we&apos;ll send you instructions to
            reset your password
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={onSubmit} className="grid gap-4">
            {/* Error Message */}
            {error && (
              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                {error}
              </div>
            )}

            <div className="grid gap-2">
              <Label htmlFor="email" className="text-sm font-medium">
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="h-10"
                disabled={isSubmitting}
              />
            </div>

            <CustomerLoadingButton
              type="submit"
              isLoading={isSubmitting}
              loadingText="Sending Reset Email..."
              className="w-full h-10 bg-blue-600 hover:bg-blue-700"
            >
              Send Reset Instructions
            </CustomerLoadingButton>
          </form>
        </CardContent>
        <CardFooter className="text-center">
          <a
            href="/customer/login"
            className="inline-flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 hover:underline"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to sign in
          </a>
        </CardFooter>
      </Card>
    </div>
  );
}
