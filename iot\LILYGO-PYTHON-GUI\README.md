# PathLink GPS Tracker - GUI Control Panel

A comprehensive desktop GUI application for configuring, monitoring, and testing the PathLink GPS tracking system. This application provides an intuitive interface for managing ESP32 GPS tracker configurations, testing communication protocols, and monitoring system responses.

## 🚀 Features

### Core Functionality
- **Environment Management**: Switch between Development and Production environments
- **Protocol Support**: Multiple communication protocols including HTTP POST, WebSocket, CoAP, and AMQP
- **Configuration Management**: Save and load configuration settings
- **Real-time Monitoring**: Monitor ESP32 responses and system status
- **Test Data Sending**: Send test GPS coordinates to verify system functionality

### Configuration Options
- **HTTP Settings**: Configure host, port, and path for both environments
- **WebSocket Settings**: Full WebSocket configuration for real-time communication
- **Car ID Management**: Customize vehicle identifiers for tracking
- **Protocol Switching**: Easy switching between different communication methods

### User Interface
- **Modern Tkinter GUI**: Clean, responsive interface with proper styling
- **Real-time Status**: Live connection status and response monitoring
- **Log Management**: Comprehensive logging with clear timestamps
- **Responsive Design**: Adapts to different screen sizes and resolutions

## 📋 Requirements

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux
- **Python Version**: Python 3.7 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 100MB available disk space

### Python Dependencies
- `tkinter` - Built into Python standard library
- `requests` - HTTP library for API communication
- `websocket-client` - WebSocket client implementation
- `urllib3` - Enhanced HTTP handling

## 🛠️ Installation

### Option 1: Quick Install (Recommended)

1. **Clone or Download** the repository:
   ```bash
   git clone <repository-url>
   cd LILYGO-PYTHON-GUI
   ```

2. **Install Dependencies**:
   
   **Windows:**
   ```cmd
   install_dependencies.bat
   ```
   
   **macOS/Linux:**
   ```bash
   chmod +x install_dependencies.sh
   ./install_dependencies.sh
   ```
   
   **Manual:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the Application**:
    ```bash
    python pathlink_gui.py
    ```

### Option 2: Manual Installation

1. **Install Python Dependencies**:
   ```bash
   pip install requests websocket-client urllib3
   ```

2. **Run the Application**:
    ```bash
    python pathlink_gui.py
    ```

### Option 3: Virtual Environment (Recommended for Development)

1. **Create Virtual Environment**:
   ```bash
   python -m venv ollietrack_env
   
   # Windows
   ollietrack_env\Scripts\activate
   
   # macOS/Linux
   source ollietrack_env/bin/activate
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run Application**:
    ```bash
    python pathlink_gui.py
    ```

## 🎯 Usage Guide

### First Launch
1. **Launch the Application**: Run `python pathlink_gui.py`
2. **Review Default Settings**: The application loads with default configuration
3. **Configure Environment**: Select DEV or PROD environment
4. **Save Configuration**: Click "Save Configuration" to persist settings

### Configuration Section
- **Environment Selection**: Choose between Development and Production
- **HTTP Settings**: Configure host, port, and API path for each environment
- **WebSocket Settings**: Set up WebSocket connections for real-time communication
- **Save Settings**: Persist configuration changes to disk

### Protocol Selection
- **HTTP POST**: Default protocol for sending GPS data
- **HTTP GET**: Alternative HTTP method for data transmission
- **WebSocket**: Real-time bidirectional communication
- **CoAP**: Lightweight IoT protocol (future implementation)
- **AMQP**: Message broker protocol (future implementation)

### Testing and Monitoring
1. **Set Test Parameters**: Enter Car ID, Latitude, and Longitude
2. **Select Protocol**: Choose communication method
3. **Connect**: Establish connection to selected environment
4. **Send Test Data**: Transmit GPS coordinates for testing
5. **Monitor Responses**: View real-time system responses in the log

### Connection Management
- **Connect Button**: Establishes connection based on selected protocol
- **Disconnect Button**: Terminates active connections
- **Status Display**: Real-time connection status indicator
- **Auto-refresh**: Automatic status updates (configurable)

## 🔧 Configuration

### Configuration File
The application automatically creates `pathlink_config.ini` in the application directory:

```ini
[DEV]
http_host = ***************
http_port = 3000
http_path = /api/gps/ingest
ws_host = ***************
ws_port = 3000
ws_path = /api/ws/tracker

[PROD]
http_host = olliesrentalcar.pathlinkio.app
http_port = 443
http_path = /api/gps/ingest
ws_host = olliesrentalcar.pathlinkio.app
ws_port = 443
ws_path = /api/ws/tracker

[GENERAL]
car_id = lilygo-esp32-01
default_protocol = HTTP_POST
default_environment = DEV
```

### Environment Variables
- **DEV Environment**: Local development server configuration
- **PROD Environment**: Production server configuration
- **Automatic Switching**: Fields are enabled/disabled based on selected environment

## 📡 Communication Protocols

### HTTP POST (Default)
- **Endpoint**: `/api/gps/ingest`
- **Method**: POST
- **Content-Type**: application/json
- **Payload**: GPS data in JSON format
- **Fallback**: Automatic retry with different environments

### WebSocket
- **Endpoint**: `/api/ws/tracker`
- **Protocol**: ws:// (DEV) or wss:// (PROD)
- **Features**: Real-time bidirectional communication
- **Reconnection**: Automatic reconnection on connection loss

### Future Protocols
- **CoAP**: Lightweight UDP-based protocol for IoT
- **AMQP**: Advanced message queuing for enterprise use
- **MQTT**: Message queuing telemetry transport

## 🚗 Test Data Format

### GPS Data Structure
```json
{
  "carId": "lilygo-esp32-01",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "speed_kmh": 0.0,
  "altitude": 0.0,
  "accuracy_m": 5.0,
  "sat_visible": 8,
  "sat_used": 6,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Test Coordinates
- **New York City**: 40.7128, -74.0060
- **London**: 51.5074, -0.1278
- **Tokyo**: 35.6762, 139.6503
- **Custom**: Enter your own coordinates

## 🐛 Troubleshooting

### Common Issues

#### Configuration Save Errors
- **"Object has no attribute 'environment'"**: This error has been fixed in the latest version. If you encounter this, ensure you're running the updated code.
- **Permission Denied**: Ensure the application has write access to its directory for configuration files
- **Invalid Configuration**: Delete `pathlink_config.ini` to reset to default settings

#### Connection Errors
- **Check Network**: Verify internet connectivity
- **Verify URLs**: Ensure correct host, port, and path
- **Firewall**: Check if firewall is blocking connections
- **SSL/TLS**: Verify HTTPS certificate validity

#### WebSocket Issues
- **Port Configuration**: Ensure correct WebSocket port
- **Protocol**: Use ws:// for HTTP and wss:// for HTTPS
- **Server Support**: Verify server supports WebSocket connections

#### Configuration Problems
- **File Permissions**: Ensure write access to configuration directory
- **File Corruption**: Delete config file to reset to defaults
- **Invalid Values**: Check for proper host/IP address format

### Debug Information
- **Response Monitor**: View detailed communication logs
- **Status Bar**: Monitor connection and system status
- **Error Messages**: Clear error reporting for troubleshooting

## 🔮 Future Enhancements

### Planned Features
- **MQTT Support**: Full MQTT client implementation
- **Data Visualization**: GPS data plotting and mapping
- **Batch Operations**: Multiple device management
- **Advanced Logging**: Log file export and analysis
- **Plugin System**: Extensible architecture for custom protocols

### Protocol Expansion
- **CoAP Implementation**: Lightweight IoT protocol support
- **AMQP Integration**: Enterprise message queuing
- **LoRaWAN Support**: Long-range wireless communication
- **Custom Protocols**: User-defined communication methods

## 📚 API Reference

### HTTP Endpoints
- `GET /api/gps/ingest` - Test endpoint availability
- `POST /api/gps/ingest` - Submit GPS data
- `GET /api/ws/tracker` - WebSocket upgrade endpoint

### WebSocket Events
- `open` - Connection established
- `message` - Data received from server
- `error` - Connection error occurred
- `close` - Connection terminated

## 🤝 Contributing

### Development Setup
1. **Fork the Repository**
2. **Create Feature Branch**: `git checkout -b feature/new-feature`
3. **Make Changes**: Implement your feature or fix
4. **Test Thoroughly**: Ensure all functionality works
5. **Submit Pull Request**: Include detailed description

### Code Standards
- **Python Style**: Follow PEP 8 guidelines
- **Documentation**: Include docstrings for all functions
- **Error Handling**: Proper exception handling and user feedback
- **Testing**: Include tests for new functionality

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

### Getting Help
- **Documentation**: Review this README and code comments
- **Issues**: Report bugs via GitHub Issues
- **Discussions**: Use GitHub Discussions for questions
- **Community**: Join the OllieTrack community

### Contact Information
- **Project Maintainer**: OllieTrack Development Team
- **Repository**: [GitHub Repository URL]
- **Documentation**: [Documentation URL]

## 🔄 Version History

### v1.0.1 (Latest)
- Fixed configuration save error ("Object has no attribute 'environment'")
- Added dependency availability checks for better error handling
- Improved WebSocket and HTTP protocol handling
- Added installation scripts for easier setup
- Enhanced error messages and user feedback

### v1.0.0 (Initial)
- Initial release with core functionality
- HTTP POST/GET support
- WebSocket communication
- Configuration management
- Real-time monitoring
- Test data functionality

### Upcoming Versions
- **v1.1.0**: Enhanced protocol support
- **v1.2.0**: Data visualization features
- **v2.0.0**: Plugin architecture and extensibility

---

**Note**: This application is designed to work with the PathLink GPS tracking system. Ensure your ESP32 devices are properly configured and your server endpoints are accessible before testing.
