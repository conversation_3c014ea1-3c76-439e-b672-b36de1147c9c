/**
 * Centralized Icon System Configuration
 * 
 * This file provides a standardized approach to icon usage across the application
 * following UI/UX best practices for responsiveness, consistency, and accessibility.
 */

import React from 'react'
import { 
  // Navigation & Layout
  LayoutDashboard,
  Package,
  Users,
  Cog,
  Car,
  LogOut,
  
  // Actions & States
  CheckCircle,
  XCircle,
  AlertTriangle,
  Search,
  Bell,
  
  // Vehicle & Booking
  BadgeDollarSign,
  Fuel,
  Calendar,
  MapPin,
  User,
  Phone,
  Mail,
  CreditCard,
  
  // Communication
  CircleHelp,
  Scale,
  MessageCircle,
  
  // Admin
  CarFront,
  CalendarRange,
  Settings,
  
  // UI Elements
  Eye,
  EyeOff,
  ArrowLeft,
  Home,
  ExternalLink,
  
  type LucideIcon
} from 'lucide-react'

// Icon size variants for responsive design
export const iconSizes = {
  xs: 'h-3 w-3',      // 12px - for very small contexts
  sm: 'h-4 w-4',      // 16px - for compact UI elements
  md: 'h-5 w-5',      // 20px - for standard UI elements
  lg: 'h-6 w-6',      // 24px - for prominent elements
  xl: 'h-8 w-8',      // 32px - for large interactive elements
} as const

// Brand color variants for consistent theming
export const iconColors = {
  primary: 'text-blue-600',
  secondary: 'text-gray-600',
  success: 'text-green-600',
  warning: 'text-yellow-600',
  error: 'text-red-600',
  muted: 'text-gray-400',
  white: 'text-white',
} as const

// Semantic icon mapping (not action-based naming)
export const semanticIcons = {
  // Navigation
  dashboard: LayoutDashboard,
  catalog: Package,
  people: Users,
  settings: Cog,
  vehicle: Car,
  exit: LogOut,
  
  // Status indicators
  success: CheckCircle,
  error: XCircle,
  warning: AlertTriangle,
  
  // Search & Discovery
  magnifyingGlass: Search,
  notification: Bell,
  
  // Vehicle features
  currency: BadgeDollarSign,
  fuelTank: Fuel,
  person: User,
  
  // Communication
  calendar: Calendar,
  location: MapPin,
  telephone: Phone,
  envelope: Mail,
  creditCard: CreditCard,
  help: CircleHelp,
  legal: Scale,
  chat: MessageCircle,
  
  // Admin specific
  carManagement: CarFront,
  bookingCalendar: CalendarRange,
  adminSettings: Settings,
  
  // Form & Input
  eyeOpen: Eye,
  eyeClosed: EyeOff,
  arrowBack: ArrowLeft,
  house: Home,
  externalLink: ExternalLink,
} as const

// Icon component props interface
export interface IconProps {
  icon: LucideIcon
  size?: keyof typeof iconSizes
  color?: keyof typeof iconColors
  className?: string
  'aria-label'?: string
  'aria-hidden'?: boolean
}

// Standardized icon component with accessibility and responsiveness built-in
export function Icon({ 
  icon: IconComponent, 
  size = 'sm', 
  color = 'secondary',
  className = '',
  'aria-label': ariaLabel,
  'aria-hidden': ariaHidden = false,
  ...props 
}: IconProps & React.ComponentProps<LucideIcon>) {
  const sizeClass = iconSizes[size]
  const colorClass = iconColors[color]
  
  return (
    <IconComponent 
      className={`${sizeClass} ${colorClass} ${className}`.trim()}
      aria-label={ariaLabel}
      aria-hidden={ariaHidden}
      {...props}
    />
  )
}

// Responsive icon sizing based on screen size
export const responsiveIconClasses = {
  // Mobile first approach
  mobile: 'h-4 w-4',
  tablet: 'sm:h-5 sm:w-5',
  desktop: 'lg:h-6 lg:w-6',
  combined: 'h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6'
} as const

// Context-specific icon configurations
export const contextualIconSizes = {
  // Navigation elements
  sidebarNav: 'h-4 w-4',
  topbarNav: 'h-5 w-5',
  
  // Cards and content
  cardFeature: 'h-4 w-4',
  cardAction: 'h-4 w-4',
  cardStatus: 'h-3 w-3',
  
  // Forms
  inputIcon: 'h-4 w-4',
  buttonIcon: 'h-4 w-4',
  
  // Modals and dialogs
  modalIcon: 'h-5 w-5',
  alertIcon: 'h-5 w-5',
  
  // Admin interfaces
  tableIcon: 'h-4 w-4',
  dashboardIcon: 'h-6 w-6',
} as const

// Accessibility helpers
export const getIconAccessibility = (iconName: string, isDecorative: boolean = false) => {
  if (isDecorative) {
    return { 'aria-hidden': true }
  }
  
  return {
    'aria-label': `${iconName} icon`,
    'role': 'img'
  }
}

export type IconSize = keyof typeof iconSizes
export type IconColor = keyof typeof iconColors
export type SemanticIconName = keyof typeof semanticIcons
