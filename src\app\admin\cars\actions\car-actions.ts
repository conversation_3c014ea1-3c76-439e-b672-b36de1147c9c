"use server"

import { revalidatePath } from "next/cache"
import { createContextClient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"

export type Car = {
  id?: string
  model: string
  type: string
  plate_number: string
  status: string
  condition: string
  fuel_capacity: number
  fuel_type: string
  transmission: string
  seats: number
  price_per_day: number
  image_url: string
  notes?: string
  is_archived?: boolean
  color?: string
}

/**
 * Fetches all active (non-archived) cars from the database
 */
export async function listCars(forClient = true) {
  const supabase = await createContextClient('admin')
  
  const { data, error } = await supabase
    .from('cars')
    .select('*')
    .eq('is_archived', false)
    .order('model')
  
  if (error) {
    console.error('Error fetching cars:', error)
    return []
  }
  
  return data || []
}

/**
 * Fetches all archived cars from the database
 */
export async function listArchivedCars() {
  const supabase = await createContextClient('admin')
  
  const { data, error } = await supabase
    .from('cars')
    .select('*')
    .eq('is_archived', true)
    .order('model')
  
  if (error) {
    console.error('Error fetching archived cars:', error)
    return []
  }
  
  return data || []
}

/**
 * Adds a new car to the database
 */
export async function addCarAction(car: Car) {
  const supabase = await createContextClient('admin')
  
  const { data, error } = await supabase
    .from('cars')
    .insert({
      model: car.model,
      type: car.type,
      plate_number: car.plate_number,
      status: car.status,
      condition: car.condition,
      fuel_capacity: car.fuel_capacity,
      fuel_type: car.fuel_type,
      transmission: car.transmission,
      seats: car.seats,
      price_per_day: car.price_per_day,
      image_url: car.image_url,
      notes: car.notes,
      color: car.color,
      is_archived: false
    })
    .select()
  
  if (error) {
    console.error('Error adding car:', error)
    throw new Error(`Failed to add car: ${error.message}`)
  }
  
  revalidatePath('/admin/cars')
  return data[0]
}

/**
 * Updates an existing car in the database
 */
export async function updateCarAction(id: string, car: Car) {
  const supabase = await createContextClient('admin')
  
  const { data, error } = await supabase
    .from('cars')
    .update({
      model: car.model,
      type: car.type,
      plate_number: car.plate_number,
      status: car.status,
      condition: car.condition,
      fuel_capacity: car.fuel_capacity,
      fuel_type: car.fuel_type,
      transmission: car.transmission,
      seats: car.seats,
      price_per_day: car.price_per_day,
      image_url: car.image_url,
      notes: car.notes,
      color: car.color,
    })
    .eq('id', id)
    .select()
  
  if (error) {
    console.error('Error updating car:', error)
    throw new Error(`Failed to update car: ${error.message}`)
  }
  
  revalidatePath('/admin/cars')
  return data[0]
}

/**
 * Archives a car (soft delete)
 */
export async function archiveCarAction(id: string) {
  const supabase = await createContextClient('admin')
  
  const { error } = await supabase
    .from('cars')
    .update({ is_archived: true })
    .eq('id', id)
  
  if (error) {
    console.error('Error archiving car:', error)
    throw new Error(`Failed to archive car: ${error.message}`)
  }
  
  revalidatePath('/admin/cars')
  return { success: true }
}

/**
 * Restores an archived car
 */
export async function unarchiveCarAction(id: string) {
  const supabase = await createContextClient('admin')
  
  const { error } = await supabase
    .from('cars')
    .update({ is_archived: false })
    .eq('id', id)
  
  if (error) {
    console.error('Error restoring car:', error)
    throw new Error(`Failed to restore car: ${error.message}`)
  }
  
  revalidatePath('/admin/cars')
  return { success: true }
}

/**
 * Permanently deletes a car from the database
 */
export async function deleteCarAction(id: string) {
  const supabase = await createContextClient('admin')
  
  const { error } = await supabase
    .from('cars')
    .delete()
    .eq('id', id)
  
  if (error) {
    console.error('Error deleting car:', error)
    throw new Error(`Failed to delete car: ${error.message}`)
  }
  
  revalidatePath('/admin/cars')
  return { success: true }
}

/**
 * Updates the status of a car in the database
 */
// Ensure errors are serializable and descriptive when returned to the client
function normalizeError(err: unknown) {
  if (!err) return { message: "Unknown error" };
  if (typeof err === "string") return { message: err };
  if (err instanceof Error) return { message: err.message, name: err.name };
  if (typeof err === "object" && err !== null && "message" in err) {
    return { message: String(err.message) };
  }
  return { message: "An unexpected error occurred" };
}

export async function updateCarStatus(carId: string, status: string) {
  const supabase = await createContextClient('admin');
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`Updating car ${carId} status to: ${status}`);
  }
  
  const { error } = await supabase
    .from("cars")
    .update({ status })
    .eq("id", carId);

  if (error) {
    console.error("Error updating car status:", error);
    return { error: normalizeError(error) };
  }

  if (process.env.NODE_ENV === 'development') {
    console.log(`Successfully updated car ${carId} status to: ${status}`);
  }
  revalidatePath('/admin/cars')
  return { success: true };
}
