-- Diagnostic queries to check reference ID implementation status

-- 1. Check if columns exist
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name IN ('payments', 'bookings') 
    AND column_name IN ('payment_ref', 'booking_ref')
ORDER BY table_name, column_name;

-- 2. Check sample data from payments table
SELECT 
    id,
    payment_ref,
    booking_id,
    amount,
    status,
    created_at
FROM public.payments 
ORDER BY created_at DESC 
LIMIT 5;

-- 3. Check sample data from bookings table  
SELECT 
    id,
    booking_ref,
    customer_id,
    status,
    created_at
FROM public.bookings 
ORDER BY created_at DESC 
LIMIT 5;

-- 4. Check if triggers exist
SELECT 
    trigger_name,
    event_object_table,
    action_timing,
    event_manipulation
FROM information_schema.triggers 
WHERE trigger_name IN ('trigger_set_payment_ref', 'trigger_set_booking_ref');

-- 5. Check if functions exist
SELECT 
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_name IN ('generate_payment_ref', 'generate_booking_ref', 'set_payment_ref', 'set_booking_ref');

-- 6. Count records with/without reference IDs
SELECT 
    'payments' as table_name,
    COUNT(*) as total_records,
    COUNT(payment_ref) as with_ref_id,
    COUNT(*) - COUNT(payment_ref) as missing_ref_id
FROM public.payments
UNION ALL
SELECT 
    'bookings' as table_name,
    COUNT(*) as total_records,
    COUNT(booking_ref) as with_ref_id,
    COUNT(*) - COUNT(booking_ref) as missing_ref_id
FROM public.bookings;
