"use client";

import * as React from "react";
import { AdminSidebarNav } from "../nav/admin-sidebar-nav";
import { BreadcrumbNav } from "../nav/breadcrumb-nav";
import { AdminMobileNav } from "../nav/admin-mobile-nav";
import { SidebarProvider, useSidebar } from "../nav/sidebar-context";

function AdminShellInner({ children }: { children: React.ReactNode }) {
  const { isCollapsed } = useSidebar();
  return (
    <div className="min-h-screen bg-muted/20 flex flex-col md:flex-row">
      {/* Desktop Sidebar - Hidden on mobile */}
      <aside
        className="admin-sidebar hidden md:block shrink-0 border-r bg-white transition-[width] duration-300 ease-in-out overflow-hidden fixed h-screen z-20"
        style={
          { width: isCollapsed ? "5rem" : "18rem" } as React.CSSProperties
        }
      >
        <AdminSidebarNav />
      </aside>
      
      {/* Main Content Area */}
      <main 
        className="flex-1 flex flex-col min-w-0 overflow-auto w-full"
        style={{
          marginLeft: 0,
          ...(typeof window !== 'undefined' && window.innerWidth >= 768 
            ? { marginLeft: isCollapsed ? "5rem" : "18rem" } 
            : {})
        } as React.CSSProperties}
      >
        <div className="px-2 xs:px-3 sm:px-4 md:px-6 py-2 xs:py-3 sm:py-4">
          <div className="flex items-center justify-between mb-2 xs:mb-3 sm:mb-4">
            <div className="flex-1 min-w-0 overflow-hidden">
              <BreadcrumbNav />
            </div>
            <div className="md:hidden flex-shrink-0 ml-2">
              <AdminMobileNav />
            </div>
          </div>
          {children}
        </div>
      </main>
    </div>
  );
}

export function AdminShell({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider>
      <AdminShellInner>{children}</AdminShellInner>
    </SidebarProvider>
  );
}
