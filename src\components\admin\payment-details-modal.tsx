"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ExternalLink, Eye, ImageIcon } from "lucide-react"
import type { AdminPayment } from "@/app/admin/payments/actions/payment-actions"

interface PaymentDetailsModalProps {
  payment: AdminPayment
  isOpen: boolean
  onClose: () => void
}

export function PaymentDetailsModal({ payment, isOpen, onClose }: PaymentDetailsModalProps) {
  const [showImageViewer, setShowImageViewer] = React.useState(false)
  const [imageLoading, setImageLoading] = React.useState(true)
  const [imageError, setImageError] = React.useState(false)

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "numeric"
    })
  }

  // Reset image states when modal opens/closes
  React.useEffect(() => {
    if (isOpen) {
      setShowImageViewer(false)
      setImageLoading(true)
      setImageError(false)
    }
  }, [isOpen])

  const handleImageLoad = () => {
    setImageLoading(false)
    setImageError(false)
  }

  const handleImageError = () => {
    setImageLoading(false)
    setImageError(true)
  }

  // Get payment method badge color
  const getMethodBadgeClass = (method: string) => {
    switch (method.toLowerCase()) {
      case 'gcash':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'bank transfer':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'remittance center':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`${showImageViewer ? 'w-full max-w-full xs:max-w-[95vw] sm:max-w-4xl' : 'w-full max-w-full xs:max-w-[90vw] sm:max-w-md'} max-h-[90vh] overflow-y-auto p-3 xs:p-6`}>
        <DialogHeader className="pb-3 xs:pb-4">
          <DialogTitle className="text-base xs:text-lg lg:text-xl">
            {showImageViewer ? "Payment Receipt & Details" : "Payment Details"}
          </DialogTitle>
        </DialogHeader>
        
        <div className={`${showImageViewer ? 'grid grid-cols-1 lg:grid-cols-2 gap-4 xs:gap-6' : 'space-y-3 xs:space-y-4'}`}>
          {/* Receipt Image Viewer */}
          {showImageViewer && payment.proofOfPaymentUrl && (
            <div className="space-y-3 xs:space-y-4">
              <Card>
                <CardHeader className="pb-2 xs:pb-3">
                  <CardTitle className="flex items-center gap-2 text-sm xs:text-base">
                    <ImageIcon className="h-4 w-4 xs:h-5 xs:w-5" />
                    Payment Receipt
                  </CardTitle>
                </CardHeader>
                <CardContent className="px-3 xs:px-6">
                  <div className="relative bg-gray-50 rounded-lg overflow-hidden min-h-[200px] xs:min-h-[300px]">
                    {imageLoading && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                          <div className="animate-spin rounded-full h-6 w-6 xs:h-8 xs:w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                          <p className="text-xs xs:text-sm text-gray-600">Loading image...</p>
                        </div>
                      </div>
                    )}
                    {imageError ? (
                      <div className="flex flex-col items-center justify-center p-4 xs:p-8 text-center">
                        <ImageIcon className="h-8 w-8 xs:h-12 xs:w-12 text-gray-400 mb-2" />
                        <p className="text-xs xs:text-sm text-gray-600 mb-2">Unable to load image</p>
                        <Button 
                          variant="secondary" 
                          size="sm"
                          className="h-10 xs:h-11 min-h-[40px] xs:min-h-[44px] text-xs xs:text-sm"
                          asChild
                        >
                          <a 
                            href={payment.proofOfPaymentUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="flex items-center gap-2"
                          >
                            <ExternalLink className="h-3 w-3 xs:h-4 xs:w-4" />
                            <span className="hidden xs:inline">Open in new tab</span>
                            <span className="xs:hidden">Open</span>
                          </a>
                        </Button>
                      </div>
                    ) : (
                      <img 
                        src={payment.proofOfPaymentUrl}
                        alt="Payment Receipt"
                        className="w-full h-auto max-h-[250px] xs:max-h-96 object-contain"
                        onLoad={handleImageLoad}
                        onError={handleImageError}
                        style={{ display: imageLoading ? 'none' : 'block' }}
                      />
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Payment Details */}
          <div className="space-y-3 xs:space-y-4">
            {/* Payment amount and method */}
            <div className="flex flex-col space-y-2 xs:space-y-3">
              <div className="flex justify-between items-center">
                <h3 className="text-xs xs:text-sm font-medium text-muted-foreground">Amount</h3>
                <p className="text-lg xs:text-xl font-bold">₱{payment.amount.toLocaleString('en-PH', { minimumFractionDigits: 2 })}</p>
              </div>
              <div className="flex justify-between items-center">
                <h3 className="text-xs xs:text-sm font-medium text-muted-foreground">Method</h3>
                <Badge className={`${getMethodBadgeClass(payment.method)} px-2 py-1 text-xs`}>
                  {payment.method}
                </Badge>
              </div>
            </div>
          
            <div className="border-t pt-2 xs:pt-3">
              {/* Renter information */}
              <div className="space-y-2">
                <h3 className="text-xs xs:text-sm font-medium">Renter Information</h3>
                <div className="grid grid-cols-1 gap-2">
                  <div className="flex flex-col">
                    <span className="text-xs text-muted-foreground">Name</span>
                    <span className="font-medium text-sm xs:text-base truncate">{payment.renterName}</span>
                  </div>
                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground">Email</span>
                  <span className="font-medium text-sm xs:text-base truncate">{payment.renterEmail}</span>
                </div>
                {payment.renterPhone && payment.renterPhone !== "N/A" && (
                  <div className="flex flex-col">
                    <span className="text-xs text-muted-foreground">Phone</span>
                    <span className="font-medium text-sm xs:text-base">{payment.renterPhone}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="border-t pt-2 xs:pt-3">
            {/* Payment details */}
            <div className="space-y-2">
              <h3 className="text-xs xs:text-sm font-medium">Payment Details</h3>
              <div className="grid grid-cols-1 gap-2">
                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground">Payment ID</span>
                  <span className="font-medium text-sm xs:text-base">#{payment.id}</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground">Booking Reference</span>
                  <a 
                    href={`/admin/bookings?ref=${payment.bookingId}`}
                    className="font-medium text-sm xs:text-base text-blue-600 hover:underline truncate"
                  >
                    #{payment.bookingId}
                  </a>
                </div>
                <div className="flex flex-col">
                  <span className="text-xs text-muted-foreground">Transaction Date</span>
                  <span className="font-medium text-sm xs:text-base">{formatDate(payment.transactionDate)}</span>
                </div>
                {payment.status && (
                  <div className="flex flex-col">
                    <span className="text-xs text-muted-foreground">Status</span>
                    <span className="font-medium text-sm xs:text-base">{payment.status}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
            {/* Receipt buttons */}
            {payment.proofOfPaymentUrl && (
              <div className="border-t pt-2 xs:pt-3 space-y-2">
                <div className="flex flex-col xs:flex-row gap-2">
                  <Button 
                    variant="primary" 
                    className="flex-1 h-12 min-h-[48px] text-sm xs:text-base"
                    onClick={() => setShowImageViewer(!showImageViewer)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    {showImageViewer ? "Hide Receipt" : "View Receipt"}
                  </Button>
                  <Button 
                    variant="secondary" 
                    className="h-12 min-h-[48px] px-4 text-sm xs:text-base"
                    asChild
                  >
                    <a 
                      href={payment.proofOfPaymentUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center justify-center gap-2"
                    >
                      <ExternalLink className="h-4 w-4" />
                      <span className="hidden xs:inline">Open External</span>
                      <span className="xs:hidden">External</span>
                    </a>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
