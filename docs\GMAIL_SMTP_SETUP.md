# Setting Up Gmail SMTP with Supa<PERSON>

This guide explains how to configure Gmail SMTP for your PathLink application's email notifications.

## Step 1: Create Gmail App Password

1. Go to your Google Account → [Security Settings](https://myaccount.google.com/security)
2. If not already enabled, turn on 2-Step Verification
3. Search for "App passwords" or go to [App passwords](https://myaccount.google.com/apppasswords)
4. Select "Mail" and "Other (Custom name)"
5. Name it "Supabase SMTP" and click "Generate"
6. **Important**: Copy the 16-character password that appears - you'll only see it once

## Step 2: Configure Supabase SMTP Settings

1. Go to [Supabase Dashboard](https://app.supabase.com) → Your Project → Settings → Email
2. Enable "Custom SMTP" and enter:
   - **Host**: `smtp.gmail.com`
   - **Port**: `587`
   - **Username**: Your complete Gmail address
   - **Password**: The app password generated in Step 1
   - **Sender Name**: "<PERSON><PERSON>'s Rent A Car" (or your preferred name)
   - **Sender Email**: Your Gmail address

3. Click "Save Changes" and send a test email

## Step 3: Deploy the SQL Function

1. Navigate to the Supabase SQL Editor
2. Paste and execute the contents of `database/send-email-smtp-function.sql`

## Step 4: Deploy the Edge Function

Run the deployment script:
```bash
# From project root directory
cd scripts
./deploy-email-function.bat
```

## Limitations with Gmail

- Gmail limits sending to 500 emails per day
- May experience occasional throttling
- Not recommended for high-volume production use

## Troubleshooting

- **Authentication Failed**: Verify you're using the app password, not your regular password
- **Connection Timeout**: Check if your network allows outbound SMTP connections
- **Email Not Received**: Check spam folders and verify recipient addresses

## Testing

Test the email function by finalizing a booking in the admin panel. Check Edge Function logs in Supabase Dashboard if issues occur.
