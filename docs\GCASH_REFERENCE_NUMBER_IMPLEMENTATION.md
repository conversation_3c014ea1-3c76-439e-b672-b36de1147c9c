# GCash Reference Number Implementation

## Overview
This document describes the implementation of the GCash reference number input field for the customer booking flow in OllieTrack. The feature adds a required reference number field that appears conditionally when GCash is selected as the payment method.

## Implementation Summary

### 1. Customer Side Changes

#### Booking Data Structure (`booking-flow.tsx`)
- Added `gcashReferenceNumber?: string` to the `BookingData` interface
- Updated initialization to include empty string for `gcashReferenceNumber`
- Enhanced step validation to require GCash reference number when GCash payment method is selected

#### Payment Proof Step (`payment-proof-step.tsx`)
- Added conditional GCash reference number input field that only appears when GCash is selected
- Implemented real-time validation with the following rules:
  - Required when GCash is selected
  - Minimum 10 digits (after removing non-numeric characters)
  - Maximum 20 digits
  - Shows helpful error messages
- Added informational alert with instructions on where to find the reference number
- Enhanced completion status validation to include GCash reference number check

### 2. Admin Panel Changes

#### Booking Details Modal (`booking-details-modal.tsx`)
- Added `gcash_reference_number?: string` to payment interface
- Added conditional display of GCash reference number in payment section
- Includes copy-to-clipboard functionality for easy reference number copying
- Styled with blue accent colors to distinguish from regular transaction IDs

### 3. Validation Logic

#### GCash Reference Number Validation
```typescript
const validateGCashReference = (value: string): string => {
  if (!value || value.trim().length === 0) {
    return "GCash reference number is required";
  }
  
  // Remove spaces and special characters for validation
  const cleanValue = value.replace(/[^0-9]/g, "");
  
  // GCash reference numbers are typically 13 digits
  if (cleanValue.length < 10) {
    return "Reference number is too short (minimum 10 digits)";
  }
  
  if (cleanValue.length > 20) {
    return "Reference number is too long (maximum 20 digits)";
  }
  
  return "";
};
```

### 4. User Experience Features

#### Customer Side
- **Conditional Visibility**: Field only appears when GCash is selected
- **Real-time Validation**: Immediate feedback on input validity
- **Helper Information**: Clear instructions on where to find the reference number
- **Responsive Design**: Works across all breakpoints (320px to 1440px+)
- **Required Field**: Prevents booking progression without valid reference number

#### Admin Side
- **Clear Display**: GCash reference number shown in dedicated section
- **Copy Functionality**: One-click copy to clipboard
- **Visual Distinction**: Blue styling to differentiate from transaction IDs
- **Conditional Display**: Only shows when payment method is GCash

## File Changes

### Modified Files
1. `src/components/customer-side/booking/flow/booking-flow.tsx`
   - Updated BookingData interface
   - Enhanced validation logic
   - Added gcashReferenceNumber field initialization

2. `src/components/customer-side/booking/flow/payment-proof-step.tsx`
   - Added conditional GCash reference input field
   - Implemented validation logic
   - Enhanced UI with proper styling and responsiveness

3. `src/components/admin/bookings/booking-details-modal.tsx`
   - Updated payment interface
   - Added GCash reference number display
   - Implemented copy-to-clipboard functionality

### New Files
1. `src/app/customer/test-gcash-reference/page.tsx`
   - Comprehensive test page for GCash reference functionality
   - Validation testing with multiple scenarios
   - Responsive design verification
   - User instructions and testing guidelines

2. `docs/GCASH_REFERENCE_NUMBER_IMPLEMENTATION.md`
   - This documentation file

## Testing

### Test Page
A comprehensive test page is available at `/customer/test-gcash-reference` that includes:
- Payment method selection testing
- GCash reference number input validation
- Responsive design verification across all breakpoints
- Validation test cases with expected outcomes
- Real-time viewport width display

### Test Cases
1. **Valid Short Reference**: 10-digit number ✅
2. **Valid Long Reference**: 20-digit number ✅
3. **Too Short**: Less than 10 digits ❌
4. **Too Long**: More than 20 digits ❌
5. **Empty**: No input ❌
6. **With Spaces**: Numbers with spaces (cleaned) ✅

### Responsive Breakpoints Tested
- Mobile S (320px)
- Mobile M (375px)
- Mobile L (425px)
- Tablet (768px)
- Desktop (1024px)
- Large Desktop (1440px+)

## Integration Points

### Database Requirements
The booking record should be updated to store the `gcash_reference_number` field when GCash is selected as the payment method.

### API Integration
When the booking is submitted, the `gcashReferenceNumber` from the booking data should be included in the API payload for storage and later retrieval in the admin panel.

## Security Considerations
- Input validation prevents injection attacks
- Reference numbers are displayed as read-only in admin panel
- No sensitive information is exposed beyond the reference number itself

## Future Enhancements
1. **Reference Number Verification**: Integrate with GCash API to verify reference numbers
2. **Transaction Matching**: Match reference numbers with actual GCash transactions
3. **Bulk Import**: Allow admins to import GCash transaction data for verification
4. **Duplicate Detection**: Prevent duplicate reference numbers across bookings

## Support Information
- GCash reference numbers are typically 10-20 digit numeric strings
- Users can find reference numbers in their GCash transaction history
- Reference numbers may include spaces or dashes which are automatically cleaned during validation
- The field is only required and visible when GCash is selected as the payment method
