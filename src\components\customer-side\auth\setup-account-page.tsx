"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DocumentUpload,
  type DocumentFile,
} from "@/components/ui/document-upload";
import {
  Shield,
  FileText,
  Calendar,
  AlertTriangle,
  ArrowLeft,
  CheckCircle2,
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import Image from "next/image";
import Link from "next/link";
import {
  checkUserLegalDocuments,
  saveUserLegalDocument,
  getStandardDocumentType,
  type UserLegalDocument,
  type DocumentCheckResult,
} from "@/lib/services/document-service";
import { createClient } from "@/lib/supabase/client";

interface RequirementConfig {
  key: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  required: boolean;
  accept: string;
  details: string[];
}

const REQUIREMENTS: RequirementConfig[] = [
  {
    key: "driversLicense",
    title: "Driver's License",
    description: "Valid Philippine driver's license",
    icon: Shield,
    required: true,
    accept: "image/jpeg,image/jpg,image/png,application/pdf",
    details: [
      "Must be a valid Philippine driver's license",
      "License should have at least 1 year validity remaining",
      "Clear photo showing all details",
      "Both front and back if applicable",
    ],
  },
  {
    key: "governmentId",
    title: "Government ID",
    description: "Valid government-issued identification",
    icon: FileText,
    required: true,
    accept: "image/jpeg,image/jpg,image/png,application/pdf",
    details: [
      "Passport, National ID, SSS ID, or other government-issued ID",
      "Must be currently valid and unexpired",
      "Clear and readable photo",
      "Name must match driver's license",
    ],
  },
  {
    key: "proofOfBilling",
    title: "Proof of Billing",
    description: "Recent utility bill or billing statement",
    icon: Calendar,
    required: true,
    accept: "image/jpeg,image/jpg,image/png,application/pdf",
    details: [
      "Recent utility bill (electricity, water, internet, etc.)",
      "Credit card or bank statement",
      "Must be dated within the last 3 months",
      "Address must be clearly visible and readable",
    ],
  },
];

function SetupAccountPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();

  // Get user data from URL params and localStorage
  const email = searchParams.get("email") || "";
  
  // Try to get user data from localStorage first (from registration form)
  const [userData, setUserData] = React.useState<{
    firstName: string;
    middleInitial: string;
    lastName: string;
    phone: string;
  } | null>(null);

  React.useEffect(() => {
    const pendingUserData = localStorage.getItem('pendingUserData');
    if (pendingUserData) {
      try {
        const parsedData = JSON.parse(pendingUserData);
        setUserData({
          firstName: parsedData.firstName || "",
          middleInitial: parsedData.middleInitial || "",
          lastName: parsedData.lastName || "",
          phone: parsedData.phone || ""
        });
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    }
  }, []);

  // Fallback to URL params if localStorage data is not available
  const firstName = userData?.firstName || searchParams.get("firstName") || "";
  const middleInitial = userData?.middleInitial || "";
  const lastName = userData?.lastName || searchParams.get("lastName") || "";
  const phone = userData?.phone || searchParams.get("phone") || "";

  // Document upload states
  const [documents, setDocuments] = React.useState<Record<string, DocumentFile[]>>({
    driversLicense: [],
    governmentId: [],
    proofOfBilling: [],
  });

  const [pendingUploads, setPendingUploads] = React.useState<Record<string, DocumentFile[]>>({});
  const [documentCheck, setDocumentCheck] = React.useState<DocumentCheckResult | null>(null);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [saving, setSaving] = React.useState(false);

  // Load existing documents on mount
  React.useEffect(() => {
    async function loadExistingDocuments() {
      setLoading(true);
      try {
        const result = await checkUserLegalDocuments();
        setDocumentCheck(result);

        // Auto-fill documents if they exist
        const updatedDocuments: Record<string, DocumentFile[]> = {
          driversLicense: [],
          governmentId: [],
          proofOfBilling: [],
        };

        for (const req of REQUIREMENTS) {
          const docType = getStandardDocumentType(req.key);
          const existingDoc = result.existingDocuments.find((doc: UserLegalDocument) => doc.document_type === docType);
          
          if (existingDoc) {
            updatedDocuments[req.key] = [{
              id: existingDoc.id,
              file: new File([], existingDoc.file_name || 'Document', { 
                type: existingDoc.file_type || 'application/pdf' 
              }),
              fileName: existingDoc.file_name,
              fileSize: existingDoc.file_size,
              fileType: existingDoc.file_type,
              url: existingDoc.file_url,
              status: existingDoc.verification_status === 'approved' ? 'completed' : 
                      existingDoc.verification_status === 'rejected' ? 'error' : 'uploading',
              progress: 100,
            }];
          }
        }

        setDocuments(updatedDocuments);
      } catch (error) {
        console.error('Error loading documents:', error);
        toast({
          variant: "destructive",
          title: "Error Loading Documents",
          description: "Failed to load existing documents. Please try refreshing the page.",
        });
      } finally {
        setLoading(false);
      }
    }

    loadExistingDocuments();
  }, [toast]);

  const handleFileChange = (requirementKey: string, files: DocumentFile[]) => {
    setDocuments(prev => ({
      ...prev,
      [requirementKey]: files
    }));

    // Track pending uploads
    setPendingUploads(prev => ({
      ...prev,
      [requirementKey]: files
    }));
  };

  const getCompletedCount = () => {
    return REQUIREMENTS.filter((req) => {
      const files = documents[req.key] || [];
      return (
        files.length > 0 &&
        files.every((f) => f.status === "completed")
      );
    }).length;
  };

  const getRequiredCount = () => {
    return REQUIREMENTS.filter((req) => req.required).length;
  };

  const completedCount = getCompletedCount();
  const requiredCount = getRequiredCount();
  const allRequiredComplete = completedCount >= requiredCount;

  const handleConfirmUploads = async () => {
    setSaving(true);
    try {
      let uploadErrors = 0;
      let uploadsCompleted = 0;

      for (const [key, files] of Object.entries(pendingUploads)) {
        if (files.length === 0) continue;

        try {
          const docType = getStandardDocumentType(key);
          const file = files[0];
          
          if (file.file && file.file.size > 0) {
            await saveUserLegalDocument(docType, file.file);
            uploadsCompleted++;
          }
        } catch (error) {
          console.error(`Error uploading ${key}:`, error);
          uploadErrors++;
        }
      }

      if (uploadErrors === 0 && uploadsCompleted > 0) {
        toast({
          title: "Documents Saved",
          description: `Successfully uploaded ${uploadsCompleted} document(s).`,
        });
      } else if (uploadErrors > 0 && uploadsCompleted > 0) {
        toast({
          variant: "destructive",
          title: "Partial Upload",
          description: `${uploadsCompleted} document(s) uploaded successfully, but ${uploadErrors} failed.`,
        });
      } else if (uploadErrors > 0) {
        toast({
          variant: "destructive",
          title: "Upload Failed",
          description: "Failed to upload documents. Please try again.",
        });
      }

      // Reset pending uploads and reload documents
      setPendingUploads({});
      
      // Reload documents to reflect changes
      const result = await checkUserLegalDocuments();
      setDocumentCheck(result);

      // Update document display
      const updatedDocuments: Record<string, DocumentFile[]> = {
        driversLicense: [],
        governmentId: [],
        proofOfBilling: [],
      };

      for (const req of REQUIREMENTS) {
        const docType = getStandardDocumentType(req.key);
        const existingDoc = result.existingDocuments.find((doc: UserLegalDocument) => doc.document_type === docType);
        
        if (existingDoc) {
          updatedDocuments[req.key] = [{
            id: existingDoc.id,
            file: new File([], existingDoc.file_name || 'Document', { 
              type: existingDoc.file_type || 'application/pdf' 
            }),
            fileName: existingDoc.file_name,
            fileSize: existingDoc.file_size,
            fileType: existingDoc.file_type,
            url: existingDoc.file_url,
            status: existingDoc.verification_status === 'approved' ? 'completed' : 
                    existingDoc.verification_status === 'rejected' ? 'error' : 'uploading',
            progress: 100,
          }];
        }
      }

      setDocuments(updatedDocuments);
    } catch (error) {
      console.error('Error in bulk upload:', error);
      toast({
        variant: "destructive",
        title: "Upload Error",
        description: "An error occurred while uploading documents.",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleDiscardChanges = () => {
    // Reset pending uploads and revert to original documents
    setPendingUploads({});
    
    // Reload original document state
    if (documentCheck) {
      const updatedDocuments: Record<string, DocumentFile[]> = {
        driversLicense: [],
        governmentId: [],
        proofOfBilling: [],
      };

      for (const req of REQUIREMENTS) {
        const docType = getStandardDocumentType(req.key);
        const existingDoc = documentCheck.existingDocuments.find((doc: UserLegalDocument) => doc.document_type === docType);
        
        if (existingDoc) {
          updatedDocuments[req.key] = [{
            id: existingDoc.id,
            file: new File([], existingDoc.file_name || 'Document', { 
              type: existingDoc.file_type || 'application/pdf' 
            }),
            fileName: existingDoc.file_name,
            fileSize: existingDoc.file_size,
            fileType: existingDoc.file_type,
            url: existingDoc.file_url,
            status: existingDoc.verification_status === 'approved' ? 'completed' : 
                    existingDoc.verification_status === 'rejected' ? 'error' : 'uploading',
            progress: 100,
          }];
        }
      }

      setDocuments(updatedDocuments);
    }
  };

  const handleSubmit = async () => {
    if (!allRequiredComplete) {
      toast({
        variant: "destructive",
        title: "Documents Required",
        description: "Please upload all required documents before proceeding.",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      toast({
        title: "Account Setup Complete!",
        description: "Your documents have been uploaded successfully. You can now proceed with booking.",
      });

      // Redirect to the customer dashboard or booking page
      router.replace("/customer/booking/flow");
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Setup Failed",
        description: "There was an error setting up your account. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkipForNow = () => {
    // Allow users to skip document upload for now
    router.replace("/");
  };

  return (
    <div className="min-h-screen bg-background">


      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-6 space-y-6">
        {/* Header with Back Button and Logo */}
        <div className="flex items-center justify-between">
          {/* Back to Homepage Button */}
          <Button
            variant="secondary"
            onClick={() => router.replace("/")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Homepage
          </Button>

          {/* Logo - Centered */}
          <div className="flex justify-center flex-1">
            <Link href="/" className="cursor-pointer">
              <div className="relative w-32 h-12">
                <Image
                  src="/ollie_logo1.jpg"
                  alt="Ollie's Logo"
                  fill
                  className="object-contain hover:opacity-80 transition-opacity"
                  priority
                />
              </div>
            </Link>
          </div>

          {/* Spacer to balance the layout */}
          <div className="w-32"></div>
        </div>

        {/* Welcome Message */}
        <div className="text-center space-y-2">
          <h2 className="text-xl sm:text-2xl font-bold text-foreground">
            Welcome, {firstName}{middleInitial ? ` ${middleInitial}` : ''} {lastName}!
          </h2>
          <p className="text-sm sm:text-base text-muted-foreground">
            To complete your account setup and unlock full booking features, please upload the required documents below.
          </p>
        </div>


        {/* Document Cards */}
        <div className="space-y-4">
          {REQUIREMENTS.map((requirement) => {
            const files = documents[requirement.key] || [];
            const isComplete = files.length > 0 && files.every((f) => f.status === "completed");

            return (
              <div
                key={requirement.key}
                className="border rounded-lg p-6 bg-white"
                style={{
                  borderColor: '#E0E0E0',
                  borderRadius: '12px',
                  backgroundColor: '#FFFFFF'
                }}
              >
                {/* Document Header */}
                <div className="mb-4">
                  <h3 
                    className="font-semibold mb-2"
                    style={{
                      fontSize: '20px',
                      fontWeight: 600,
                      color: '#212121',
                      lineHeight: '28px'
                    }}
                  >
                    {requirement.title}
                  </h3>
                  <p 
                    style={{
                      fontSize: '14px',
                      color: '#616161',
                      lineHeight: '20px'
                    }}
                  >
                    {requirement.details.join('; ')}
                  </p>
                </div>

                {/* Upload Area */}
                <DocumentUpload
                  label=""
                  files={files}
                  onChange={(newFiles) => handleFileChange(requirement.key, newFiles)}
                  accept={requirement.accept}
                  maxFiles={1}
                  required={false}
                  className="w-full"
                  uploadFolder="setup-account"
                />
              </div>
            );
          })}

        </div>

        {/* Pending Uploads Notification */}
        {Object.keys(pendingUploads).length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                <span className="text-sm font-medium text-yellow-800">
                  You have pending document changes
                </span>
              </div>
            </div>
            <div className="flex gap-3">
              <Button
                onClick={handleConfirmUploads}
                disabled={saving}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {saving ? "Saving..." : "Save All Documents"}
              </Button>
              <Button
                onClick={handleDiscardChanges}
                variant="secondary"
                disabled={saving}
              >
                Discard Changes
              </Button>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <span className="ml-2 text-sm text-muted-foreground">Loading documents...</span>
          </div>
        )}

        {/* Skip Option */}
        <div className="text-center">
          <button
            onClick={handleSkipForNow}
            className="text-sm underline transition-colors"
            style={{
              color: '#616161',
              fontSize: '14px'
            }}
          >
            Skip for now
          </button>
        </div>

        {/* Footer Note */}
        <div className="text-center">
          <p className="text-xs sm:text-sm text-muted-foreground">
            You can always upload these documents later from your account settings.
          </p>
        </div>
      </div>
    </div>
  );
}

export { SetupAccountPage };
