/**
 * Session Persistence Test Suite
 * 
 * Tests session restoration and persistence across browser sessions,
 * page reloads, and tab management scenarios.
 */

import { render, screen, waitFor, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import React from 'react'
import { createBrowserClient } from '@supabase/ssr'
import { CustomerAuthProvider } from '../../src/components/auth/customer-auth-context'
import { AdminAuthProvider } from '../../src/components/auth/admin-auth-context'

// Mock Supabase client
jest.mock('@supabase/ssr', () => ({
  createBrowserClient: jest.fn()
}))

const mockCreateBrowserClient = createBrowserClient as jest.MockedFunction<typeof createBrowserClient>

// Test components to verify auth state
const CustomerTestComponent = () => {
  const [customerAuth, setCustomerAuth] = React.useState<any>(null)
  
  React.useEffect(() => {
    // Try to get customer auth context safely
    try {
      const { useCustomerAuth } = require('../../src/components/auth/customer-auth-context')
      const auth = useCustomerAuth()
      setCustomerAuth(auth)
    } catch (error) {
      // Context not available
      setCustomerAuth({ session: null, user: null, profile: null, isCustomer: false })
    }
  }, [])

  if (!customerAuth) return <div>Loading...</div>

  return (
    <div>
      <div data-testid="customer-session">
        {customerAuth.session ? 'Has session' : 'No session'}
      </div>
      <div data-testid="customer-user">
        {customerAuth.user?.email || 'No user'}
      </div>
      <div data-testid="customer-is-customer">
        {customerAuth.isCustomer ? 'true' : 'false'}
      </div>
    </div>
  )
}

const AdminTestComponent = () => {
  const [adminAuth, setAdminAuth] = React.useState<any>(null)
  
  React.useEffect(() => {
    // Try to get admin auth context safely
    try {
      const { useAdminAuth } = require('../../src/components/auth/admin-auth-context')
      const auth = useAdminAuth()
      setAdminAuth(auth)
    } catch (error) {
      // Context not available
      setAdminAuth({ session: null, user: null, profile: null, loading: false, isAdmin: false })
    }
  }, [])

  if (!adminAuth) return <div>Loading...</div>

  return (
    <div>
      <div data-testid="admin-session">
        {adminAuth.session ? 'Has session' : 'No session'}
      </div>
      <div data-testid="admin-user">
        {adminAuth.user?.email || 'No user'}
      </div>
      <div data-testid="admin-is-admin">
        {adminAuth.isAdmin ? 'true' : 'false'}
      </div>
      <div data-testid="admin-loading">
        {adminAuth.loading ? 'Loading' : 'Loaded'}
      </div>
    </div>
  )
}

describe('Session Persistence Tests', () => {
  let mockSupabaseCustomer: any
  let mockSupabaseAdmin: any

  beforeEach(() => {
    // Reset all mocks and clear storage
    jest.clearAllMocks()
    window.localStorage.clear()
    window.sessionStorage.clear()
    
    // Create separate mock clients
    mockSupabaseCustomer = {
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        onAuthStateChange: jest.fn().mockReturnValue({
          data: { subscription: { unsubscribe: jest.fn() } }
        }),
        signInWithPassword: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn().mockResolvedValue({ error: null }),
        resetPasswordForEmail: jest.fn(),
        verifyOtp: jest.fn(),
        resend: jest.fn()
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } })
          })
        })
      })
    }

    mockSupabaseAdmin = {
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        onAuthStateChange: jest.fn().mockReturnValue({
          data: { subscription: { unsubscribe: jest.fn() } }
        }),
        signInWithPassword: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn().mockResolvedValue({ error: null }),
        resetPasswordForEmail: jest.fn(),
        verifyOtp: jest.fn(),
        resend: jest.fn()
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } })
          })
        })
      })
    }

    // Setup createBrowserClient mock
    mockCreateBrowserClient.mockImplementation((url, key, options) => {
      if (options?.auth?.storageKey === 'sb-customer-auth-token') {
        return mockSupabaseCustomer
      } else if (options?.auth?.storageKey === 'sb-admin-auth-token') {
        return mockSupabaseAdmin
      }
      return mockSupabaseCustomer
    })
  })

  test('should restore customer session on page reload', async () => {
    // Setup existing customer session
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    await act(async () => {
      render(
        <CustomerAuthProvider>
          <CustomerTestComponent />
        </CustomerAuthProvider>
      )
    })

    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
    })

    // Should restore the session
    await waitFor(() => {
      expect(screen.getByTestId('customer-session')).toBeInTheDocument()
    })
  })

  test('should restore admin session on page reload', async () => {
    // Setup existing admin session
    const adminSession = {
      access_token: 'admin-token',
      user: { id: 'admin-1', email: '<EMAIL>' }
    }

    mockSupabaseAdmin.auth.getSession.mockResolvedValue({
      data: { session: adminSession },
      error: null
    })

    mockSupabaseAdmin.from().select().eq().single.mockResolvedValue({
      data: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
      error: null
    })

    await act(async () => {
      render(
        <AdminAuthProvider>
          <AdminTestComponent />
        </AdminAuthProvider>
      )
    })

    await waitFor(() => {
      expect(mockSupabaseAdmin.auth.getSession).toHaveBeenCalled()
    })

    // Should restore the session
    await waitFor(() => {
      expect(screen.getByTestId('admin-session')).toBeInTheDocument()
    })
  })

  test('should handle concurrent sessions without interference', async () => {
    // Setup different sessions for each context
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    const adminSession = {
      access_token: 'admin-token',
      user: { id: 'admin-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseAdmin.auth.getSession.mockResolvedValue({
      data: { session: adminSession },
      error: null
    })

    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    mockSupabaseAdmin.from().select().eq().single.mockResolvedValue({
      data: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
      error: null
    })

    await act(async () => {
      render(
        <div>
          <CustomerAuthProvider>
            <CustomerTestComponent />
          </CustomerAuthProvider>
          <AdminAuthProvider>
            <AdminTestComponent />
          </AdminAuthProvider>
        </div>
      )
    })

    // Both sessions should be handled independently
    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
      expect(mockSupabaseAdmin.auth.getSession).toHaveBeenCalled()
    })
  })

  test('should handle cross-context role validation', async () => {
    // Setup admin user session
    const adminSession = {
      access_token: 'admin-token',
      user: { id: 'admin-1', email: '<EMAIL>' }
    }

    // Both contexts get admin session
    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: adminSession },
      error: null
    })

    mockSupabaseAdmin.auth.getSession.mockResolvedValue({
      data: { session: adminSession },
      error: null
    })

    // Both get admin profile
    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
      error: null
    })

    mockSupabaseAdmin.from().select().eq().single.mockResolvedValue({
      data: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
      error: null
    })

    await act(async () => {
      render(
        <div>
          <CustomerAuthProvider>
            <CustomerTestComponent />
          </CustomerAuthProvider>
          <AdminAuthProvider>
            <AdminTestComponent />
          </AdminAuthProvider>
        </div>
      )
    })

    // Customer context should reject admin user
    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.signOut).toHaveBeenCalled()
    })
  })

  test('should handle session expiration gracefully', async () => {
    // Setup expired session error
    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: { message: 'Session expired' }
    })

    await act(async () => {
      render(
        <CustomerAuthProvider>
          <CustomerTestComponent />
        </CustomerAuthProvider>
      )
    })

    // Should handle gracefully without crashing
    await waitFor(() => {
      expect(screen.getByTestId('customer-session')).toHaveTextContent('No session')
      expect(screen.getByTestId('customer-is-customer')).toHaveTextContent('false')
    })
  })

  test('should clear session data on signout', async () => {
    // Setup existing session first
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    await act(async () => {
      render(
        <CustomerAuthProvider>
          <CustomerTestComponent />
        </CustomerAuthProvider>
      )
    })

    // Then simulate signout by updating the session to null
    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null
    })

    // Trigger auth state change
    const onAuthStateChange = mockSupabaseCustomer.auth.onAuthStateChange.mock.calls[0][0]
    await act(async () => {
      onAuthStateChange('SIGNED_OUT', null)
    })

    // Session should be cleared
    await waitFor(() => {
      expect(screen.queryByText('Has session')).not.toBeInTheDocument()
    })
  })

  test('should handle multiple tab simulation', async () => {
    // Setup session
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }

    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    // Simulate multiple tabs by rendering multiple instances
    await act(async () => {
      render(
        <div>
          <div data-testid="tab1">
            <CustomerAuthProvider>
              <CustomerTestComponent />
            </CustomerAuthProvider>
          </div>
          <div data-testid="tab2">
            <CustomerAuthProvider>
              <CustomerTestComponent />
            </CustomerAuthProvider>
          </div>
        </div>
      )
    })

    // Both tabs should work independently
    await waitFor(() => {
      expect(screen.getByTestId('tab1')).toBeInTheDocument()
      expect(screen.getByTestId('tab2')).toBeInTheDocument()
    })

    // getSession should have been called for each tab
    expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
  })
})
