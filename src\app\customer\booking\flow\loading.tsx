import { PublicAppShell } from "@/components/layout/public-app-shell"
import { CustomerBookingLoading } from "@/components/customer-side/loading/customer-loading"
import { CustomerCardSkeleton } from "@/components/customer-side/loading/customer-loading"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

export default function BookingFlowLoading() {
  return (
    <PublicAppShell>
      <div className="space-y-8 px-4 md:px-6 py-8" data-testid="customer-loading-page">
        {/* Progress bar skeleton */}
        <Card className="max-w-4xl mx-auto">
          <CardHeader className="pb-4">
            <div className="space-y-4">
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48 animate-pulse"></div>
              <Progress value={0} className="h-2" />
              <div className="flex justify-between text-sm">
                {Array.from({ length: 4 }, (_, i) => (
                  <div key={i} className="text-center">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse mb-1"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-20 animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Loading indicator */}
        <div className="flex flex-col items-center justify-center py-8">
          <CustomerBookingLoading />
        </div>

        {/* Step content skeleton */}
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 animate-pulse"></div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Form fields skeleton */}
            <div className="grid md:grid-cols-2 gap-6">
              {Array.from({ length: 4 }, (_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse"></div>
                  <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full animate-pulse"></div>
                </div>
              ))}
            </div>

            {/* Car selection skeleton if applicable */}
            <div className="space-y-4">
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-40 animate-pulse"></div>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {Array.from({ length: 3 }, (_, i) => (
                  <CustomerCardSkeleton key={i} />
                ))}
              </div>
            </div>

            {/* Action buttons skeleton */}
            <div className="flex justify-between pt-6">
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse"></div>
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-28 animate-pulse"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    </PublicAppShell>
  )
}
