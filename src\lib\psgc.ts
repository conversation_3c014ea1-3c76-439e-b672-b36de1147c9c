export interface Location {
  code: string;
  name: string;
  isCity: boolean;
}

const API_BASE_URL = 'https://psgc.gitlab.io/api';

async function fetchFromApi<T>(endpoint: string): Promise<T[]> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch from ${endpoint}: ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`Error fetching data from ${endpoint}:`, error);
    return [];
  }
}

export async function searchLocations(query: string): Promise<Location[]> {
  if (!query || query.trim().length < 2) {
    return [];
  }

  const citiesPromise = fetchFromApi<{ code: string; name: string; }>('/cities');
  const municipalitiesPromise = fetchFromApi<{ code: string; name:string; }>('/municipalities');

  const [cities, municipalities] = await Promise.all([citiesPromise, municipalitiesPromise]);

  const allLocations: Location[] = [
    ...cities.map(c => ({ ...c, isCity: true })),
    ...municipalities.map(m => ({ ...m, isCity: false }))
  ];

  const lowercasedQuery = query.toLowerCase();
  const filtered = allLocations.filter(location =>
    location.name.toLowerCase().includes(lowercasedQuery)
  );

  return filtered.sort((a, b) => a.name.localeCompare(b.name)).slice(0, 100); // Limit results
}
