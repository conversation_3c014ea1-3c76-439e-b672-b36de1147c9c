"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircleIcon, XCircleIcon } from "lucide-react";

interface RejectionReasonModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  onConfirm: (reason: string) => void;
  onCancel: () => void;
  confirmButtonText?: string;
  confirmButtonIcon?: React.ReactNode;
  placeholder?: string;
}

export function RejectionReasonModal({
  open,
  onOpenChange,
  title,
  description,
  onConfirm,
  onCancel,
  confirmButtonText = "Confirm",
  confirmButtonIcon = <XCircleIcon className="h-4 w-4" />,
  placeholder = "Please provide a reason (required)...",
}: RejectionReasonModalProps) {
  const [reason, setReason] = React.useState("");
  const [error, setError] = React.useState("");

  // Reset state when modal opens/closes
  React.useEffect(() => {
    if (!open) {
      setReason("");
      setError("");
    }
  }, [open]);

  const handleConfirm = () => {
    const trimmedReason = reason.trim();
    
    if (!trimmedReason) {
      setError("Please provide a reason before proceeding.");
      return;
    }

    onConfirm(trimmedReason);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onCancel();
    onOpenChange(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && e.ctrlKey) {
      e.preventDefault();
      handleConfirm();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-700">
            <AlertCircleIcon className="h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            {description}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-3">
          <div>
            <Textarea
              value={reason}
              onChange={(e) => {
                setReason(e.target.value);
                if (error) setError(""); // Clear error when user starts typing
              }}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className={`min-h-[100px] resize-none ${
                error ? "border-red-300 focus:border-red-500" : ""
              }`}
              autoFocus
            />
            {error && (
              <p className="text-red-600 text-sm mt-1 flex items-center gap-1">
                <AlertCircleIcon className="h-3 w-3" />
                {error}
              </p>
            )}
          </div>
          
          <div className="text-xs text-gray-500">
            Tip: Press Ctrl+Enter to submit quickly
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button 
            variant="secondary" 
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            Cancel
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleConfirm}
            className="flex items-center gap-2"
            disabled={!reason.trim()}
          >
            {confirmButtonIcon}
            {confirmButtonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
