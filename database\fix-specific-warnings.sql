-- Fix specific security warnings from database linter
-- Only targets the exact functions mentioned in the warning list

-- ========================================
-- 1. RLS FIX - Enable RLS on vehicle_categories
-- ========================================

-- Enable RLS on vehicle_categories table (if not already enabled)
DO $$ 
BEGIN
  IF NOT (SELECT rowsecurity FROM pg_tables WHERE tablename = 'vehicle_categories' AND schemaname = 'public') THEN
    ALTER TABLE public.vehicle_categories ENABLE ROW LEVEL SECURITY;
    RAISE NOTICE '✅ Enabled RLS on vehicle_categories';
  ELSE
    RAISE NOTICE '✅ RLS already enabled on vehicle_categories';
  END IF;
END $$;

-- Create policy to allow public read access
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'vehicle_categories' 
    AND policyname = 'Allow public read access to vehicle categories'
  ) THEN
    CREATE POLICY "Allow public read access to vehicle categories" ON public.vehicle_categories
    FOR SELECT 
    USING (is_active = true);
    RAISE NOTICE '✅ Created public read policy for vehicle_categories';
  ELSE
    RAISE NOTICE '✅ Public read policy already exists for vehicle_categories';
  END IF;
END $$;

-- Create policy to allow admin full access
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'vehicle_categories' 
    AND policyname = 'Allow admin full access to vehicle categories'
  ) THEN
    CREATE POLICY "Allow admin full access to vehicle categories" ON public.vehicle_categories
    FOR ALL 
    USING (
      EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
      )
    );
    RAISE NOTICE '✅ Created admin access policy for vehicle_categories';
  ELSE
    RAISE NOTICE '✅ Admin access policy already exists for vehicle_categories';
  END IF;
END $$;

-- Grant necessary permissions
GRANT SELECT ON public.vehicle_categories TO anon, authenticated;
GRANT ALL ON public.vehicle_categories TO authenticated;

-- ========================================
-- 2. FIX FUNCTION SEARCH_PATH WARNINGS
-- Only fix functions from the exact warning list
-- ========================================

-- Fix get_vehicle_categories_with_stats
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_vehicle_categories_with_stats' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.get_vehicle_categories_with_stats() SET search_path = public;
    ALTER FUNCTION public.get_vehicle_categories_with_stats() SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed get_vehicle_categories_with_stats()';
  ELSE
    RAISE NOTICE '❌ Function get_vehicle_categories_with_stats() does not exist';
  END IF;
END $$;

-- Fix get_cars_by_category
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_cars_by_category' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    -- Try different parameter signatures that might exist
    BEGIN
      ALTER FUNCTION public.get_cars_by_category(text) SET search_path = public;
      ALTER FUNCTION public.get_cars_by_category(text) SECURITY DEFINER;
      RAISE NOTICE '✅ Fixed get_cars_by_category(text)';
    EXCEPTION WHEN others THEN
      RAISE NOTICE '❌ Could not fix get_cars_by_category - check parameters';
    END;
  ELSE
    RAISE NOTICE '❌ Function get_cars_by_category does not exist';
  END IF;
END $$;

-- Fix update_category_pricing
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_category_pricing' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    BEGIN
      ALTER FUNCTION public.update_category_pricing(uuid, numeric, numeric) SET search_path = public;
      ALTER FUNCTION public.update_category_pricing(uuid, numeric, numeric) SECURITY DEFINER;
      RAISE NOTICE '✅ Fixed update_category_pricing(uuid, numeric, numeric)';
    EXCEPTION WHEN others THEN
      RAISE NOTICE '❌ Could not fix update_category_pricing - check parameters';
    END;
  ELSE
    RAISE NOTICE '❌ Function update_category_pricing does not exist';
  END IF;
END $$;

-- Fix check_booking_documents_complete
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'check_booking_documents_complete' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    BEGIN
      ALTER FUNCTION public.check_booking_documents_complete(uuid) SET search_path = public;
      ALTER FUNCTION public.check_booking_documents_complete(uuid) SECURITY DEFINER;
      RAISE NOTICE '✅ Fixed check_booking_documents_complete(uuid)';
    EXCEPTION WHEN others THEN
      RAISE NOTICE '❌ Could not fix check_booking_documents_complete - check parameters';
    END;
  ELSE
    RAISE NOTICE '❌ Function check_booking_documents_complete does not exist';
  END IF;
END $$;

-- Fix get_booking_document_status
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_booking_document_status' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    BEGIN
      ALTER FUNCTION public.get_booking_document_status(uuid) SET search_path = public;
      ALTER FUNCTION public.get_booking_document_status(uuid) SECURITY DEFINER;
      RAISE NOTICE '✅ Fixed get_booking_document_status(uuid)';
    EXCEPTION WHEN others THEN
      RAISE NOTICE '❌ Could not fix get_booking_document_status - check parameters';
    END;
  ELSE
    RAISE NOTICE '❌ Function get_booking_document_status does not exist';
  END IF;
END $$;

-- Fix get_archived_cars
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_archived_cars' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.get_archived_cars() SET search_path = public;
    ALTER FUNCTION public.get_archived_cars() SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed get_archived_cars()';
  ELSE
    RAISE NOTICE '❌ Function get_archived_cars() does not exist';
  END IF;
END $$;

-- Fix can_archive_car
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'can_archive_car' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    BEGIN
      ALTER FUNCTION public.can_archive_car(uuid) SET search_path = public;
      ALTER FUNCTION public.can_archive_car(uuid) SECURITY DEFINER;
      RAISE NOTICE '✅ Fixed can_archive_car(uuid)';
    EXCEPTION WHEN others THEN
      RAISE NOTICE '❌ Could not fix can_archive_car - check parameters';
    END;
  ELSE
    RAISE NOTICE '❌ Function can_archive_car does not exist';
  END IF;
END $$;

-- Fix car_has_archive_history
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'car_has_archive_history' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    BEGIN
      ALTER FUNCTION public.car_has_archive_history(uuid) SET search_path = public;
      ALTER FUNCTION public.car_has_archive_history(uuid) SECURITY DEFINER;
      RAISE NOTICE '✅ Fixed car_has_archive_history(uuid)';
    EXCEPTION WHEN others THEN
      RAISE NOTICE '❌ Could not fix car_has_archive_history - check parameters';
    END;
  ELSE
    RAISE NOTICE '❌ Function car_has_archive_history does not exist';
  END IF;
END $$;

-- Fix get_car_archive_history
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_car_archive_history' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    BEGIN
      ALTER FUNCTION public.get_car_archive_history(uuid) SET search_path = public;
      ALTER FUNCTION public.get_car_archive_history(uuid) SECURITY DEFINER;
      RAISE NOTICE '✅ Fixed get_car_archive_history(uuid)';
    EXCEPTION WHEN others THEN
      RAISE NOTICE '❌ Could not fix get_car_archive_history - check parameters';
    END;
  ELSE
    RAISE NOTICE '❌ Function get_car_archive_history does not exist';
  END IF;
END $$;

-- Fix sync_car_archive_status
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'sync_car_archive_status' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    BEGIN
      ALTER FUNCTION public.sync_car_archive_status(uuid, boolean) SET search_path = public;
      ALTER FUNCTION public.sync_car_archive_status(uuid, boolean) SECURITY DEFINER;
      RAISE NOTICE '✅ Fixed sync_car_archive_status(uuid, boolean)';
    EXCEPTION WHEN others THEN
      RAISE NOTICE '❌ Could not fix sync_car_archive_status - check parameters';
    END;
  ELSE
    RAISE NOTICE '❌ Function sync_car_archive_status does not exist';
  END IF;
END $$;

-- Fix handle_new_user
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'handle_new_user' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.handle_new_user() SET search_path = public;
    ALTER FUNCTION public.handle_new_user() SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed handle_new_user()';
  ELSE
    RAISE NOTICE '❌ Function handle_new_user() does not exist';
  END IF;
END $$;

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Show RLS status
SELECT 
  schemaname, 
  tablename, 
  rowsecurity,
  CASE WHEN rowsecurity THEN '✅ ENABLED' ELSE '❌ DISABLED' END as rls_status
FROM pg_tables 
WHERE tablename = 'vehicle_categories';

-- Show all existing functions and their security status
SELECT 
  p.proname as function_name,
  pg_get_function_identity_arguments(p.oid) as arguments,
  p.prosecdef as security_definer,
  p.proconfig as search_path_config,
  CASE WHEN p.prosecdef THEN '✅ SECURE' ELSE '❌ INSECURE' END as security_status,
  CASE WHEN 'search_path=public' = ANY(p.proconfig) THEN '✅ FIXED' ELSE '❌ MUTABLE' END as search_path_status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
  AND p.proname IN (
    'get_vehicle_categories_with_stats',
    'get_cars_by_category', 
    'update_category_pricing',
    'check_booking_documents_complete',
    'get_booking_document_status',
    'get_archived_cars',
    'can_archive_car',
    'car_has_archive_history',
    'get_car_archive_history',
    'sync_car_archive_status',
    'handle_new_user'
  )
ORDER BY p.proname;
