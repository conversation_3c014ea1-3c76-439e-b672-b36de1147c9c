/**
 * Responsive Design System
 * 
 * Comprehensive breakpoint and responsive utilities for the application.
 * Follows mobile-first approach with focus on ultra-wide, desktop, tablet, and mobile.
 */

// Breakpoint definitions (mobile-first approach)
export const breakpoints = {
  // Mobile (default)
  xs: '0px',      // Extra small devices (portrait phones)
  sm: '640px',    // Small devices (landscape phones)
  
  // Tablet
  md: '768px',    // Medium devices (tablets)
  lg: '1024px',   // Large devices (landscape tablets, small laptops)
  
  // Desktop
  xl: '1280px',   // Extra large devices (laptops, desktops)
  '2xl': '1536px', // 2X large devices (large desktops)
  
  // Ultra-wide
  '3xl': '1920px', // Ultra-wide monitors
  '4xl': '2560px', // 4K and ultra-wide displays
} as const

// Breakpoint values for JavaScript usage
export const breakpointValues = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
  '3xl': 1920,
  '4xl': 2560,
} as const

// Container max-widths for each breakpoint
export const containerSizes = {
  sm: '640px',
  md: '768px', 
  lg: '1024px',
  xl: '1280px',
  '2xl': '1400px', // Slightly smaller than breakpoint for better readability
  '3xl': '1600px',
  '4xl': '1800px',
} as const

// Grid column configurations for different breakpoints
export const gridColumns = {
  mobile: {
    xs: 1,  // Single column on extra small
    sm: 2,  // Two columns on small
  },
  tablet: {
    md: 2,  // Two columns on medium
    lg: 3,  // Three columns on large
  },
  desktop: {
    xl: 3,   // Three columns on extra large
    '2xl': 4, // Four columns on 2xl
  },
  ultrawide: {
    '3xl': 4, // Four columns on ultra-wide
    '4xl': 5, // Five columns on 4K
  }
} as const

// Typography scale for different screen sizes
export const typographyScale = {
  mobile: {
    'display-1': 'text-3xl',   // 48px
    'display-2': 'text-2xl',   // 36px
    h1: 'text-2xl',            // 36px
    h2: 'text-xl',             // 32px
    h3: 'text-lg',             // 28px
    h4: 'text-base',           // 24px
    h5: 'text-sm',             // 20px
    h6: 'text-xs',             // 18px
    body: 'text-sm',           // 16px
    caption: 'text-xs',        // 14px
  },
  tablet: {
    'display-1': 'md:text-4xl', // 56px
    'display-2': 'md:text-3xl', // 48px
    h1: 'md:text-3xl',          // 48px
    h2: 'md:text-2xl',          // 40px
    h3: 'md:text-xl',           // 32px
    h4: 'md:text-lg',           // 28px
    h5: 'md:text-base',         // 24px
    h6: 'md:text-sm',           // 20px
    body: 'md:text-base',       // 18px
    caption: 'md:text-sm',      // 16px
  },
  desktop: {
    'display-1': 'xl:text-6xl', // 72px
    'display-2': 'xl:text-5xl', // 64px
    h1: 'xl:text-5xl',          // 64px
    h2: 'xl:text-4xl',          // 56px
    h3: 'xl:text-3xl',          // 48px
    h4: 'xl:text-2xl',          // 40px
    h5: 'xl:text-xl',           // 32px
    h6: 'xl:text-lg',           // 28px
    body: 'xl:text-lg',         // 20px
    caption: 'xl:text-base',    // 18px
  },
  ultrawide: {
    'display-1': '3xl:text-7xl', // 80px
    'display-2': '3xl:text-6xl', // 72px
    h1: '3xl:text-6xl',          // 72px
    h2: '3xl:text-5xl',          // 64px
    h3: '3xl:text-4xl',          // 56px
    h4: '3xl:text-3xl',          // 48px
    h5: '3xl:text-2xl',          // 40px
    h6: '3xl:text-xl',           // 32px
    body: '3xl:text-xl',         // 22px
    caption: '3xl:text-lg',      // 20px
  }
} as const

// Touch target sizes for different devices
export const touchTargets = {
  mobile: {
    minimum: '44px',    // iOS/Android minimum
    comfortable: '48px', // Comfortable touch target
    large: '56px',      // Large touch target
  },
  tablet: {
    minimum: '40px',    // Smaller targets acceptable with stylus
    comfortable: '44px',
    large: '48px',
  },
  desktop: {
    minimum: '32px',    // Mouse precision allows smaller targets
    comfortable: '36px',
    large: '40px',
  }
} as const

// Spacing scale for different screen sizes
export const spacingScale = {
  mobile: {
    xs: '0.25rem', // 4px
    sm: '0.5rem',  // 8px
    md: '0.75rem', // 12px
    lg: '1rem',    // 16px
    xl: '1.25rem', // 20px
    '2xl': '1.5rem', // 24px
  },
  tablet: {
    xs: '0.5rem',  // 8px
    sm: '0.75rem', // 12px
    md: '1rem',    // 16px
    lg: '1.25rem', // 20px
    xl: '1.5rem',  // 24px
    '2xl': '2rem', // 32px
  },
  desktop: {
    xs: '0.75rem', // 12px
    sm: '1rem',    // 16px
    md: '1.25rem', // 20px
    lg: '1.5rem',  // 24px
    xl: '2rem',    // 32px
    '2xl': '2.5rem', // 40px
  }
} as const

// Content visibility rules for different screen sizes
export const contentVisibility = {
  // Show only on mobile
  mobileOnly: 'block sm:hidden',
  
  // Show only on tablet and up
  tabletUp: 'hidden md:block',
  
  // Show only on desktop and up
  desktopUp: 'hidden xl:block',
  
  // Show only on ultra-wide
  ultrawideOnly: 'hidden 3xl:block',
  
  // Hide on mobile
  hideMobile: 'hidden sm:block',
  
  // Hide on desktop
  hideDesktop: 'block xl:hidden',
} as const

// Utility functions
export function getBreakpointValue(breakpoint: keyof typeof breakpointValues): number {
  return breakpointValues[breakpoint]
}

export function isBreakpoint(breakpoint: keyof typeof breakpointValues): boolean {
  if (typeof window === 'undefined') return false
  return window.innerWidth >= getBreakpointValue(breakpoint)
}

export function getCurrentBreakpoint(): keyof typeof breakpointValues {
  if (typeof window === 'undefined') return 'xs'
  
  const width = window.innerWidth
  
  if (width >= breakpointValues['4xl']) return '4xl'
  if (width >= breakpointValues['3xl']) return '3xl'
  if (width >= breakpointValues['2xl']) return '2xl'
  if (width >= breakpointValues.xl) return 'xl'
  if (width >= breakpointValues.lg) return 'lg'
  if (width >= breakpointValues.md) return 'md'
  if (width >= breakpointValues.sm) return 'sm'
  
  return 'xs'
}

// Hook for responsive behavior
export function useResponsive() {
  const [breakpoint, setBreakpoint] = React.useState<keyof typeof breakpointValues>('xs')
  
  React.useEffect(() => {
    const updateBreakpoint = () => {
      setBreakpoint(getCurrentBreakpoint())
    }
    
    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
    
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])
  
  return {
    breakpoint,
    isMobile: breakpoint === 'xs' || breakpoint === 'sm',
    isTablet: breakpoint === 'md' || breakpoint === 'lg',
    isDesktop: breakpoint === 'xl' || breakpoint === '2xl',
    isUltrawide: breakpoint === '3xl' || breakpoint === '4xl',
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
  }
}

// Import React for the hook
import * as React from 'react'

export type BreakpointKey = keyof typeof breakpointValues
export type ResponsiveValue<T> = T | Partial<Record<BreakpointKey, T>>
