import { createClient } from '@/lib/supabase/server';
import { addKnowledge, KN<PERSON>LEDGE_CATEGORIES, KNOWLEDGE_SOURCES } from '@/lib/rag';

export interface DataExtractionConfig {
  includeCars?: boolean;
  includeBookingInfo?: boolean;
  includePolicies?: boolean;
  includeAdminProcedures?: boolean;
  includeCustomerInfo?: boolean;
  includeVehicleCategories?: boolean;
  includeDocumentManagement?: boolean;
  includeGPSTracking?: boolean;
  includeIssueTracking?: boolean;
  includePaymentDetails?: boolean;
  includeUIComponents?: boolean;
}

export class WebsiteDataExtractor {
  private async getSupabase() {
    return await createClient();
  }

  // Extract car information and specifications
  async extractCarData(): Promise<void> {
    try {
      console.log('Extracting car data...');
      const supabase = await this.getSupabase();
      
      const { data: cars, error } = await supabase
        .from('cars')
        .select('*');

      if (error) throw error;

      for (const car of cars || []) {
        const carInfo = `
Car: ${car.model}
Type: ${car.type}
Status: ${car.status}
Daily Rate: $${car.price_per_day}
Seats: ${car.seats}
Transmission: ${car.transmission}
Fuel Type: ${car.fuel_type}
Color: ${car.color}
Plate Number: ${car.plate_number}
Condition: ${car.condition}
Availability: ${car.status === 'Available' ? 'Currently available for booking' : car.status === 'Rented' ? 'Currently rented' : 'Under maintenance'}

Booking Information:
- This vehicle can be booked through the customer portal
- Daily rate is $${car.price_per_day} per day
- Vehicle fuel capacity: ${car.fuel_capacity}L
- Contact admin for special arrangements or extended rentals
        `.trim();

        await addKnowledge(
          `${car.model} (${car.type}) - Vehicle Information`,
          carInfo,
          KNOWLEDGE_SOURCES.CAR_INFO,
          KNOWLEDGE_CATEGORIES.CARS,
          { car_id: car.id, type: car.type, status: car.status }
        );
      }

      // Add general car categories information
      const categoriesInfo = await this.extractCarCategories();
      await addKnowledge(
        'Car Categories and Fleet Information',
        categoriesInfo,
        KNOWLEDGE_SOURCES.CAR_INFO,
        KNOWLEDGE_CATEGORIES.CARS
      );

      console.log(`Successfully extracted data for ${cars?.length || 0} cars`);
    } catch (error) {
      console.error('Error extracting car data:', error);
    }
  }

  // Extract booking policies and procedures
  async extractBookingInformation(): Promise<void> {
    try {
      console.log('Extracting booking information...');

      const bookingPolicies = `
BOOKING PROCESS AND POLICIES

1. BOOKING REQUIREMENTS:
- Valid driver's license (minimum 2 years)
- Government-issued ID
- Proof of age (21+ years required)
- Security deposit confirmation
- Proof of payment

2. BOOKING STEPS:
- Browse available cars in your preferred category
- Select dates and review pricing
- Complete booking form with personal details
- Upload required documents
- Submit booking for admin approval
- Receive confirmation via email

3. DOCUMENT VERIFICATION:
- All documents must be clear and legible
- Documents are reviewed by admin staff
- Booking confirmation sent after approval
- Admin may request additional documents if needed

4. PAYMENT PROCESS:
- Security deposit required for all bookings
- Payment methods: Bank transfer, credit card
- Full payment due before vehicle pickup
- Refunds processed within 5-7 business days for cancellations

5. PICKUP AND RETURN:
- Vehicle pickup at designated location
- Thorough vehicle inspection required
- Return vehicle in same condition
- Late return fees may apply

6. BOOKING STATUS TYPES:
- Pending: Awaiting admin approval
- Active: Approved and confirmed
- Completed: Rental finished successfully
- Cancelled: Booking cancelled by customer or admin

7. CANCELLATION POLICY:
- Free cancellation up to 24 hours before pickup
- Partial refund for cancellations within 24 hours
- No-show bookings forfeit full payment
- Emergency cancellations reviewed case-by-case
      `.trim();

      await addKnowledge(
        'Booking Process and Policies',
        bookingPolicies,
        KNOWLEDGE_SOURCES.BOOKING_INFO,
        KNOWLEDGE_CATEGORIES.BOOKING
      );

      // Extract payment information
      const paymentInfo = `
PAYMENT INFORMATION AND POLICIES

1. ACCEPTED PAYMENT METHODS:
- Bank transfer (preferred)
- Credit/debit cards (Visa, MasterCard)
- Cash payments (at office only)

2. SECURITY DEPOSIT:
- Required for all vehicle rentals
- Amount varies by vehicle category
- Held during rental period
- Refunded after successful return and inspection

3. PRICING STRUCTURE:
- Daily rates vary by vehicle category
- Extended rental discounts available
- Seasonal pricing may apply
- Additional fees for extra services

4. BILLING AND RECEIPTS:
- Digital receipts sent via email
- Detailed breakdown of all charges
- Tax information included
- Receipt available in customer account

5. REFUND PROCESS:
- Processed within 5-7 business days
- Refunded to original payment method
- Partial refunds for early returns (case-by-case)
- Security deposit refunded after inspection
      `.trim();

      await addKnowledge(
        'Payment Methods and Policies',
        paymentInfo,
        KNOWLEDGE_SOURCES.PAYMENT_INFO,
        KNOWLEDGE_CATEGORIES.PAYMENT
      );

      console.log('Successfully extracted booking and payment information');
    } catch (error) {
      console.error('Error extracting booking information:', error);
    }
  }

  // Extract admin procedures and operational guidelines
  async extractAdminProcedures(): Promise<void> {
    try {
      console.log('Extracting admin procedures...');

      const adminProcedures = `
ADMIN OPERATIONS AND PROCEDURES

1. BOOKING MANAGEMENT:
- Review and approve customer bookings
- Verify uploaded documents thoroughly
- Update booking status as appropriate
- Handle customer inquiries and modifications
- Process cancellations and refunds

2. DOCUMENT VERIFICATION CHECKLIST:
- Driver's License: Check validity, expiration date, photo match
- Government ID: Verify authenticity, clear photo, readable text
- Proof of Age: Confirm customer meets minimum age requirement (21+)
- Security Deposit: Verify payment confirmation and amount
- Proof of Payment: Confirm full payment received

3. VEHICLE MANAGEMENT:
- Update car status (Available/Rented/In Maintenance)
- Schedule maintenance and inspections
- Track vehicle locations and assignments
- Handle damage reports and insurance claims
- Maintain vehicle documentation

4. CUSTOMER COMMUNICATION:
- Respond to inquiries within 2 hours during business hours
- Send confirmation emails for approved bookings
- Notify customers of any changes or issues
- Handle complaints and resolve disputes
- Provide pickup/return instructions

5. SYSTEM ADMINISTRATION:
- Monitor booking system performance
- Update pricing and availability
- Generate reports and analytics
- Manage user accounts and permissions
- Backup important data regularly

6. EMERGENCY PROCEDURES:
- 24/7 roadside assistance contact
- Insurance claim procedures
- Vehicle breakdown protocols
- Customer emergency support
- Escalation procedures for serious issues

7. SUPER ADMIN CAPABILITIES:
- Create and manage admin accounts
- Access all system functions
- Override booking restrictions when necessary
- Generate comprehensive system reports
- Manage system-wide settings and configurations
      `.trim();

      await addKnowledge(
        'Admin Operations and Procedures',
        adminProcedures,
        KNOWLEDGE_SOURCES.ADMIN_DOCS,
        KNOWLEDGE_CATEGORIES.ADMIN
      );

      console.log('Successfully extracted admin procedures');
    } catch (error) {
      console.error('Error extracting admin procedures:', error);
    }
  }

  // Extract customer support information
  async extractCustomerSupport(): Promise<void> {
    try {
      console.log('Extracting customer support information...');

      const supportInfo = `
CUSTOMER SUPPORT AND FAQ

1. FREQUENTLY ASKED QUESTIONS:

Q: What are the minimum requirements to rent a car?
A: You must be at least 21 years old with a valid driver's license (held for minimum 2 years), government-issued ID, and able to provide a security deposit.

Q: How long does booking approval take?
A: Most bookings are approved within 2-4 hours during business hours. Document verification may take additional time if clarification is needed.

Q: Can I modify my booking after confirmation?
A: Yes, contact our admin team to modify dates, vehicle selection, or other details. Changes are subject to availability and may incur additional fees.

Q: What happens if I return the vehicle late?
A: Late return fees apply based on the delay. Contact us immediately if you anticipate being late to discuss options.

Q: Is insurance included in the rental?
A: Basic coverage is included. Additional insurance options are available. Customers are responsible for any damage beyond normal wear and tear.

2. CONTACT INFORMATION:
- Email: <EMAIL>
- Phone: [Contact admin for phone support]
- Office Hours: [Standard business hours]
- Emergency Support: 24/7 roadside assistance available

3. COMMON ISSUES AND SOLUTIONS:
- Document Upload Problems: Ensure files are clear, properly formatted (JPG/PNG/PDF)
- Payment Issues: Verify payment method and contact bank if needed
- Booking Modifications: Contact admin team with request details
- Vehicle Issues: Report immediately for roadside assistance
- Account Access: Password reset available on login page

4. PICKUP AND RETURN LOCATIONS:
- Main Office: [Primary location]
- Alternative locations available by arrangement
- GPS coordinates provided in booking confirmation
- Detailed pickup instructions sent after approval

5. VEHICLE CONDITION REPORTING:
- Thorough inspection required at pickup and return
- Report any pre-existing damage immediately
- Take photos of vehicle condition for records
- Admin staff available to assist with inspection
      `.trim();

      await addKnowledge(
        'Customer Support and FAQ',
        supportInfo,
        KNOWLEDGE_SOURCES.CUSTOMER_FAQ,
        KNOWLEDGE_CATEGORIES.SUPPORT
      );

      console.log('Successfully extracted customer support information');
    } catch (error) {
      console.error('Error extracting customer support information:', error);
    }
  }

  // Extract car categories and fleet information
  private async extractCarCategories(): Promise<string> {
    try {
      const supabase = await this.getSupabase();
      const { data: cars } = await supabase
        .from('cars')
        .select('type, model, price_per_day')
        .order('type');

      const categoryMap = new Map<string, any[]>();
      
      cars?.forEach((car: any) => {
        if (!categoryMap.has(car.type)) {
          categoryMap.set(car.type, []);
        }
        categoryMap.get(car.type)?.push(car);
      });

      let categoriesInfo = 'PATHLINK CAR CATEGORIES AND FLEET\n\n';
      
      for (const [type, vehicles] of categoryMap) {
        categoriesInfo += `${type.toUpperCase()} CATEGORY:\n`;
        categoriesInfo += `Available vehicles: ${vehicles.length}\n`;
        
        const rates = vehicles.map(v => v.price_per_day).filter(r => r);
        if (rates.length > 0) {
          const minRate = Math.min(...rates);
          const maxRate = Math.max(...rates);
          categoriesInfo += `Daily rates: $${minRate} - $${maxRate}\n`;
        }
        
        categoriesInfo += `Vehicles in this type:\n`;
        vehicles.forEach(vehicle => {
          categoriesInfo += `- ${vehicle.model} ($${vehicle.price_per_day}/day)\n`;
        });
        categoriesInfo += '\n';
      }

      return categoriesInfo.trim();
    } catch (error) {
      console.error('Error extracting car categories:', error);
      return 'Unable to retrieve car category information.';
    }
  }

  // Extract general policies and terms
  async extractPoliciesAndTerms(): Promise<void> {
    try {
      console.log('Extracting policies and terms...');

      const policiesInfo = `
PATHLINK RENTAL POLICIES AND TERMS

1. GENERAL TERMS AND CONDITIONS:
- All rentals subject to availability
- Prices subject to change without notice
- Customer responsible for traffic violations and fines
- Smoking prohibited in all vehicles
- Pets allowed with prior approval and additional cleaning fee

2. DRIVER REQUIREMENTS:
- Minimum age: 21 years
- Valid driver's license required (minimum 2 years held)
- International drivers must have valid international permit
- Additional drivers must be approved and added to rental agreement

3. VEHICLE USAGE POLICIES:
- Vehicles must be used for legal purposes only
- No off-road driving unless specifically approved
- Maximum occupancy as per vehicle specifications
- No unauthorized modifications to vehicle

4. DAMAGE AND LIABILITY:
- Customer liable for damage beyond normal wear and tear
- Comprehensive vehicle inspection at pickup and return
- Damage assessment by certified mechanics when necessary
- Insurance claims processed according to policy terms

5. FUEL POLICY:
- Vehicles provided with full tank
- Return with full tank to avoid refueling charges
- Fuel receipts may be required for reimbursement
- Alternative fuel arrangements available on request

6. MILEAGE POLICY:
- Unlimited mileage for rentals up to 7 days
- Extended rentals may have mileage restrictions
- Excess mileage charges apply when applicable
- Odometer readings recorded at pickup and return

7. CANCELLATION AND NO-SHOW POLICY:
- Free cancellation 24+ hours before pickup
- Partial refund for cancellations within 24 hours
- No-show bookings forfeit full payment
- Emergency cancellations reviewed individually

8. PRIVACY AND DATA PROTECTION:
- Customer information kept strictly confidential
- Data used only for rental purposes and communication
- Information shared with insurance and authorities only when required by law
- Customer has right to request data deletion after rental completion
      `.trim();

      await addKnowledge(
        'Rental Policies and Terms of Service',
        policiesInfo,
        KNOWLEDGE_SOURCES.POLICY_DOCS,
        KNOWLEDGE_CATEGORIES.POLICIES
      );

      console.log('Successfully extracted policies and terms');
    } catch (error) {
      console.error('Error extracting policies and terms:', error);
    }
  }

  // Extract vehicle categories and pricing information
  async extractVehicleCategories(): Promise<void> {
    try {
      console.log('Extracting vehicle categories...');
      const supabase = await this.getSupabase();
      
      const { data: categories, error } = await supabase
        .from('vehicle_categories')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      if (error) throw error;

      for (const category of categories || []) {
        const categoryInfo = `
Vehicle Category: ${category.name}
Type: ${category.type}
Description: ${category.description}
Price Range: $${category.min_price} - $${category.max_price} per day
Available Transmissions: ${category.available_transmissions?.join(', ') || 'Various'}
Display Order: ${category.display_order}

Category Features:
- ${category.description}
- Competitive pricing starting from $${category.min_price} per day
- Multiple transmission options available
- Professional maintenance and inspection
- 24/7 customer support included

Booking Information:
- Browse vehicles in this category through the customer portal
- Real-time availability checking
- Instant booking confirmation
- Flexible pickup and return options
        `.trim();

        await addKnowledge(
          `${category.name} - Vehicle Category`,
          categoryInfo,
          KNOWLEDGE_SOURCES.CAR_INFO,
          KNOWLEDGE_CATEGORIES.CARS,
          { category_id: category.id, type: category.type }
        );
      }

      console.log(`Successfully extracted ${categories?.length || 0} vehicle categories`);
    } catch (error) {
      console.error('Error extracting vehicle categories:', error);
    }
  }

  // Extract document management and verification processes
  async extractDocumentManagement(): Promise<void> {
    try {
      console.log('Extracting document management information...');

      const documentInfo = `
DOCUMENT MANAGEMENT AND VERIFICATION SYSTEM

1. REQUIRED DOCUMENTS FOR BOOKING:
- Driver's License: Valid license held for minimum 2 years
- Government ID: Current government-issued identification
- Proof of Age: Documentation confirming age 21+ requirement
- Security Deposit Confirmation: Bank receipt or payment proof
- Proof of Payment: Transaction confirmation for rental fees

2. DOCUMENT UPLOAD PROCESS:
- Upload clear, high-resolution images or PDFs
- Supported formats: JPG, PNG, PDF
- Maximum file size limits apply
- All text must be clearly readable
- Photos should be well-lit and focused

3. VERIFICATION WORKFLOW:
- Documents reviewed by admin staff within 2-4 hours
- Status updates: Pending → Approved/Rejected/Requires Resubmission
- Email notifications sent for status changes
- Admin notes provided for rejected documents
- Resubmission allowed for corrected documents

4. DOCUMENT STATUS TYPES:
- Pending: Under admin review
- Approved: Document accepted and verified
- Rejected: Document does not meet requirements
- Requires Resubmission: Document needs correction/replacement

5. ADMIN DOCUMENT MANAGEMENT:
- Comprehensive document review checklist
- Photo ID verification procedures
- License validity checking protocols
- Payment verification requirements
- Document retention policies

6. CUSTOMER DOCUMENT REQUESTS:
- Admin can request missing documents
- Automatic email notifications to customers
- 7-day expiration period for requests
- Request types: Missing documents or revision requests
- Tracking system for pending document submissions

7. PRIVACY AND SECURITY:
- All documents encrypted and securely stored
- Access restricted to authorized admin staff only
- Retention policy complies with data protection laws
- Customer consent required for document processing
- Right to request document deletion after rental completion
      `.trim();

      await addKnowledge(
        'Document Management and Verification System',
        documentInfo,
        KNOWLEDGE_SOURCES.BOOKING_INFO,
        KNOWLEDGE_CATEGORIES.BOOKING
      );

      console.log('Successfully extracted document management information');
    } catch (error) {
      console.error('Error extracting document management information:', error);
    }
  }

  // Extract GPS tracking and route management information
  async extractGPSTracking(): Promise<void> {
    try {
      console.log('Extracting GPS tracking information...');

      const gpsInfo = `
GPS TRACKING AND FLEET MANAGEMENT SYSTEM

1. REAL-TIME VEHICLE TRACKING:
- GPS coordinates updated in real-time
- Vehicle status monitoring (Active/Idle/Offline)
- Speed and heading information tracking
- Driver assignment tracking
- Location history maintenance

2. ROUTE MANAGEMENT:
- Historical route data storage
- Distance calculation and tracking
- Journey start and end time logging
- Route optimization for efficiency
- Geofencing capabilities for designated areas

3. ADMIN GPS DASHBOARD:
- Live vehicle location display
- Fleet overview with status indicators
- Individual vehicle tracking details
- Route history and analytics
- Performance monitoring tools

4. SAFETY AND SECURITY FEATURES:
- 24/7 vehicle monitoring
- Emergency location services
- Anti-theft tracking capabilities
- Roadside assistance coordination
- Insurance claim location verification

5. CUSTOMER BENEFITS:
- Enhanced security during rental period
- Roadside assistance location services
- Vehicle recovery in case of theft
- Accurate billing based on actual usage
- Peace of mind with monitored vehicles

6. DATA PRIVACY AND USAGE:
- GPS data used exclusively for fleet management
- Customer location privacy respected
- Data retention according to legal requirements
- Access restricted to authorized personnel only
- Compliance with data protection regulations

7. TECHNICAL SPECIFICATIONS:
- High-precision GPS tracking devices
- Real-time data transmission
- Backup power systems
- Weather-resistant equipment
- Integration with fleet management software
      `.trim();

      await addKnowledge(
        'GPS Tracking and Fleet Management',
        gpsInfo,
        KNOWLEDGE_SOURCES.CAR_INFO,
        KNOWLEDGE_CATEGORIES.CARS
      );

      console.log('Successfully extracted GPS tracking information');
    } catch (error) {
      console.error('Error extracting GPS tracking information:', error);
    }
  }

  // Extract issue tracking and customer behavior management
  async extractIssueTracking(): Promise<void> {
    try {
      console.log('Extracting issue tracking information...');

      const issueInfo = `
ISSUE TRACKING AND CUSTOMER BEHAVIOR MANAGEMENT

1. ISSUE CATEGORIES:
- Payment Issues: Payment processing problems or delays
- Late Returns: Vehicle returned after scheduled time
- Vehicle Damage: Damage caused to rental vehicle
- Communication Problems: Poor communication or unresponsive behavior
- No-Show: Failed to pick up reserved vehicle
- Cancellation Issues: Frequent cancellations or last-minute changes
- Documentation Issues: Problems with required documents
- Driving Violations: Traffic violations or reckless driving
- Contract Violations: Breach of rental agreement terms
- Positive Feedback: Exemplary renter behavior

2. CUSTOMER STATUS TRACKING:
- Behavioral monitoring and assessment
- Status taglines for customer identification
- Historical issue tracking per customer
- Pattern recognition for problematic behaviors
- Positive reinforcement for good customers

3. ISSUE SEVERITY LEVELS:
- Low: Minor issues with minimal impact
- Medium: Moderate issues requiring attention
- High: Serious issues affecting operations
- Critical: Severe issues requiring immediate action

4. ISSUE RESOLUTION PROCESS:
- Issue logging by admin staff
- Category assignment and severity rating
- Investigation and evidence gathering
- Resolution planning and implementation
- Follow-up and closure documentation

5. CUSTOMER BEHAVIOR ANALYTICS:
- Booking completion rates
- On-time return statistics
- Damage incident tracking
- Payment reliability scores
- Communication responsiveness ratings

6. ADMIN TOOLS AND FEATURES:
- Issue creation and management interface
- Customer behavior dashboards
- Reporting and analytics tools
- Category management system
- Resolution tracking workflows

7. PREVENTIVE MEASURES:
- Early warning systems for high-risk customers
- Proactive communication strategies
- Enhanced verification for flagged accounts
- Customized rental terms when necessary
- Training programs for problem prevention
      `.trim();

      await addKnowledge(
        'Issue Tracking and Customer Behavior Management',
        issueInfo,
        KNOWLEDGE_SOURCES.ADMIN_DOCS,
        KNOWLEDGE_CATEGORIES.ADMIN
      );

      console.log('Successfully extracted issue tracking information');
    } catch (error) {
      console.error('Error extracting issue tracking information:', error);
    }
  }

  // Extract detailed payment processing information
  async extractPaymentDetails(): Promise<void> {
    try {
      console.log('Extracting payment processing details...');
      const supabase = await this.getSupabase();

      const paymentDetails = `
COMPREHENSIVE PAYMENT PROCESSING SYSTEM

1. PAYMENT METHODS AND PROCESSING:
- Card Payments: Visa, MasterCard, American Express accepted
- Wallet Payments: Digital wallet integration available
- Cash Payments: Accepted at office locations only
- Bank Transfers: Preferred method for large transactions
- Installment Plans: Available for extended rentals

2. PAYMENT STATUS MANAGEMENT:
- Pending: Payment initiated but not yet confirmed
- Paid: Payment successfully processed and confirmed
- Failed: Payment processing failed (retry required)
- Refunded: Payment returned to customer account
- Partial: Partial payment received (balance due)

3. SECURITY DEPOSIT HANDLING:
- Required for all vehicle categories
- Amount varies by vehicle type and rental duration
- Held during entire rental period
- Automatic refund after successful return
- Damage assessment deductions when applicable

4. TRANSACTION PROCESSING:
- Secure payment gateway integration
- Real-time payment verification
- Automatic receipt generation
- Transaction ID tracking and reference
- Payment confirmation notifications

5. REFUND AND CANCELLATION PROCESSING:
- Automated refund processing system
- Processing time: 5-7 business days
- Refund to original payment method
- Partial refunds for early cancellations
- Emergency refund procedures available

6. BILLING AND INVOICING:
- Detailed transaction breakdowns
- Tax calculations and compliance
- Digital receipt delivery via email
- Invoice generation for corporate clients
- Payment history tracking in customer accounts

7. ADMIN PAYMENT MANAGEMENT:
- Payment verification and approval workflows
- Manual payment processing capabilities
- Dispute resolution procedures
- Chargeback handling protocols
- Financial reporting and analytics tools

8. COMPLIANCE AND REGULATIONS:
- PCI DSS compliance for card payments
- Anti-money laundering procedures
- Tax reporting and documentation
- International payment processing rules
- Data protection for financial information
      `.trim();

      await addKnowledge(
        'Payment Processing and Financial Management',
        paymentDetails,
        KNOWLEDGE_SOURCES.PAYMENT_INFO,
        KNOWLEDGE_CATEGORIES.PAYMENT
      );

      console.log('Successfully extracted payment processing details');
    } catch (error) {
      console.error('Error extracting payment details:', error);
    }
  }

  // Extract UI components and features documentation
  async extractUIComponentsAndFeatures(): Promise<void> {
    try {
      console.log('Extracting UI components and features documentation...');

      const uiFeatures = `
PATHLINK USER INTERFACE COMPONENTS AND FEATURES

1. CUSTOMER HOMEPAGE AND NAVIGATION:
- Responsive homepage with hero section and quick booking widget
- Vehicle category showcase with real-time availability
- Customer testimonials and image carousel
- Mobile-first design with progressive enhancement
- Fully responsive quick booking form (4-column desktop, stacked mobile)
- Date/time pickers with enhanced touch targets
- Location selection with fixed pickup and flexible dropoff

2. CUSTOMER BOOKING FLOW:
- Multi-step booking process with progress indicators
- Step 1: Car Selection - Browse available vehicles by category
- Step 2: Personal Information - Customer details and preferences
- Step 3: Requirements Upload - Document submission interface
- Step 4: Payment Proof - Payment confirmation upload
- Step 5: Booking Summary - Review and final confirmation
- Step 6: Confirmation - Booking completion with next steps

3. CUSTOMER DASHBOARD FEATURES:
- Active trips section with real-time booking status
- Booking history with detailed trip information
- Document management with upload and verification status
- Document request notifications from admin
- Payment reupload functionality for failed transactions
- Legal documents section for profile documents
- Unified dashboard table with booking management

4. CUSTOMER AUTHENTICATION SYSTEM:
- Mobile and desktop optimized login/signup flows
- Account setup wizard for new customers
- Password reset and recovery options
- Auth-required modal for protected actions
- Session management with auto-logout
- Customer protection middleware

5. CUSTOMER CHATBOT INTERFACE:
- Real-time AI assistant for customer support
- Context-aware responses based on booking history
- Integration with knowledge base and live data
- Responsive chat interface with typing indicators
- Message formatting with rich text support

6. ADMIN DASHBOARD COMPONENTS:
- Comprehensive booking management system
- Real-time booking calendar with drag-and-drop
- KPI statistics with visual analytics
- Advanced filtering and search capabilities
- Booking action drawer with status updates
- Document verification workflow

7. ADMIN BOOKING MANAGEMENT:
- Booking table with sortable columns
- Status badges with color-coded indicators
- Booking details modal with full information
- Action buttons for approve/reject/modify
- Calendar integration for scheduling
- Mobile-responsive booking cards

8. ADMIN GPS TRACKING SYSTEM:
- Real-time vehicle location mapping
- Dynamic GPS map with live updates
- Vehicle status indicators (active/idle/offline)
- Route history and analytics
- Geofencing and boundary alerts
- Fleet overview dashboard

9. ADMIN DOCUMENT MANAGEMENT:
- Document verification interface with approve/reject
- Document request system for missing files
- Bulk document processing capabilities
- Document status tracking and notifications
- Secure document viewing and downloading
- Audit trail for document actions

10. ADMIN PAYMENT MANAGEMENT:
- Payment verification and processing interface
- Transaction status tracking and updates
- Payment details modal with full transaction history
- Refund processing and management
- Financial reporting and analytics
- Payment method verification

11. ADMIN ISSUE TRACKING SYSTEM:
- Customer behavior monitoring dashboard
- Issue category management and assignment
- Renter status tracking with customizable taglines
- Issue severity levels and resolution workflow
- Customer analytics and behavioral patterns
- Preventive measures and early warning systems

12. ADMIN SETTINGS AND CONFIGURATION:
- Organization settings and branding customization
- Pricing management with dynamic rate updates
- Policy configuration and terms management
- Branch management with location settings
- Theme customization and UI preferences
- Audit log section with activity tracking

13. ADMIN CHATBOT INTERFACE:
- Advanced AI assistant for admin operations
- Integration with all admin data and workflows
- Context-aware responses for operational queries
- Real-time data fetching and analysis
- Administrative procedure guidance
- System status and analytics reporting

14. RESPONSIVE DESIGN FEATURES:
- Mobile-first approach with progressive enhancement
- Breakpoint-specific layouts (xs, sm, md, lg, xl)
- Touch-optimized interfaces with ≥44px targets
- Accessible navigation with keyboard support
- Consistent design system with shadcn/ui components
- Dark/light theme support throughout

15. AUTHENTICATION AND SECURITY:
- Role-based access control (customer/admin/super_admin)
- Secure session management with encryption
- Password strength validation
- Multi-device login support
- Activity logging and audit trails
- Data encryption for sensitive information

16. INTEGRATION FEATURES:
- Real-time data synchronization with Supabase
- GPS device integration for vehicle tracking
- Email notification system integration
- Payment gateway integration
- Document storage and retrieval system
- Calendar integration for scheduling
      `.trim();

      await addKnowledge(
        'UI Components and Platform Features',
        uiFeatures,
        KNOWLEDGE_SOURCES.ADMIN_DOCS,
        KNOWLEDGE_CATEGORIES.ADMIN
      );

      console.log('Successfully extracted UI components and features documentation');
    } catch (error) {
      console.error('Error extracting UI components and features:', error);
    }
  }

  // Main extraction method
  async extractAllWebsiteData(config: DataExtractionConfig = {}): Promise<void> {
    const {
      includeCars = true,
      includeBookingInfo = true,
      includePolicies = true,
      includeAdminProcedures = true,
      includeCustomerInfo = true,
      includeVehicleCategories = true,
      includeDocumentManagement = true,
      includeGPSTracking = true,
      includeIssueTracking = true,
      includePaymentDetails = true,
      includeUIComponents = true
    } = config;

    console.log('Starting complete website data extraction...');

    try {
      if (includeCars) {
        await this.extractCarData();
      }

      if (includeVehicleCategories) {
        await this.extractVehicleCategories();
      }

      if (includeBookingInfo) {
        await this.extractBookingInformation();
      }

      if (includeDocumentManagement) {
        await this.extractDocumentManagement();
      }

      if (includePaymentDetails) {
        await this.extractPaymentDetails();
      }

      if (includeGPSTracking) {
        await this.extractGPSTracking();
      }

      if (includeIssueTracking) {
        await this.extractIssueTracking();
      }

      if (includeUIComponents) {
        await this.extractUIComponentsAndFeatures();
      }

      if (includeAdminProcedures) {
        await this.extractAdminProcedures();
      }

      if (includeCustomerInfo) {
        await this.extractCustomerSupport();
      }

      if (includePolicies) {
        await this.extractPoliciesAndTerms();
      }

      console.log('Website data extraction completed successfully!');
    } catch (error) {
      console.error('Error during website data extraction:', error);
      throw error;
    }
  }
}

// Utility function to refresh knowledge base
export async function refreshWebsiteKnowledge(config?: DataExtractionConfig): Promise<void> {
  const extractor = new WebsiteDataExtractor();
  await extractor.extractAllWebsiteData(config);
}
