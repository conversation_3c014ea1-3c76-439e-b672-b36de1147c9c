"use client";

import * as React from "react"
import { Search, Filter, Calendar, Download, Plus, X, ChevronDown } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRange } from "react-day-picker"
import { format, addDays, startOfToday } from "date-fns"
import { cn } from "@/lib/utils"
import { getBookingStatusOptions } from "@/lib/utils/booking-status"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar as CalendarView } from "@/components/ui/calendar"
import { AdminTooltip } from "@/components/ui/tooltip"
import { useIsMobile } from "@/hooks/use-mobile"
import { FilterDrawer } from "./filter-drawer"

export interface BookingFilters {
  search: string;
  status: string;
  paymentStatus: string;
  vehicle: string;
  location: string;
  extensionStatus: string; // For extension requests filtering
  dateRange: {
    from: Date | null;
    to: Date | null;
  };
}

interface BookingToolbarProps {
  filters: BookingFilters;
  onFiltersChange: (filters: BookingFilters) => void;
  availableVehicles: string[];
  currentView?: "table" | "calendar" | "extensions";
  className?: string;
}

export function BookingToolbar({
  filters,
  onFiltersChange,
  availableVehicles,
  currentView = "table",
  className,
}: BookingToolbarProps) {
  const [isCalendarOpen, setIsCalendarOpen] = React.useState(false);
  const isMobile = useIsMobile();

  const updateFilter = (key: keyof BookingFilters, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const setQuickDateRange = (preset: "today" | "week" | "custom") => {
    const today = startOfToday();
    switch (preset) {
      case "today":
        updateFilter("dateRange", { from: today, to: today });
        break;
      case "week":
        updateFilter("dateRange", { from: today, to: addDays(today, 7) });
        break;
      case "custom":
        // Keep current range or clear it
        break;
    }
  };

  const clearFilters = () => {
    onFiltersChange({
      search: "",
      status: "all",
      paymentStatus: "all",
      vehicle: "all",
      location: "all",
      extensionStatus: "all",
      dateRange: { from: null, to: null },
    });
  };

  const hasActiveFilters =
    filters.search ||
    filters.status !== "all" ||
    filters.paymentStatus !== "all" ||
    filters.vehicle !== "all" ||
    filters.location !== "all" ||
    filters.dateRange.from ||
    filters.dateRange.to;

  // For mobile and tablet screens, show the unified filter button that opens a drawer
  if (isMobile) {
    return (
      <div className={cn("space-y-3", className)}>
        {/* Unified Filter Button */}
        <FilterDrawer
          filters={filters}
          onFiltersChange={onFiltersChange}
          availableVehicles={availableVehicles}
        />
      </div>
    );
  }

  // For desktop screens, show the original layout
  return (
    <div className="space-y-6">
      {/* Search and View Toggle Row */}
      <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between gap-4">
        {/* Enhanced Search Input */}
        <div className={cn("relative flex-1 max-w-sm", className)}>
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="Search by renter, booking ID, or car..."
            value={filters.search}
            onChange={(e) => onFiltersChange({ ...filters, search: e.target.value })}
            className="pl-10 pr-4 py-2.5 bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-gray-500 text-gray-900 font-medium transition-all duration-200 hover:shadow-md hover:border-gray-400"
          />
        </div>

        {/* Actions */}
        <div className="flex items-center lg:gap-3 gap-2">
          {hasActiveFilters && (
            <AdminTooltip
              content={
                <div>
                  <div className="font-medium">Clear All Filters</div>
                  <div className="text-xs opacity-75 mt-1">
                    Remove all active search and filter criteria
                  </div>
                </div>
              }
              shortcut="Ctrl+Shift+C"
            >
              <Button
                variant="destructive-outline"
                size="sm"
                onClick={clearFilters}
                className="bg-white text-red-600 border border-red-300 rounded-lg font-medium transition-all duration-200 transform hover:scale-102 hover:shadow-md hover:bg-red-50 hover:border-red-400 shadow-sm"
              >
                <X className="h-4 w-4 mr-1" />
                Clear All
              </Button>
            </AdminTooltip>
          )}

          <div className="flex items-center lg:rounded-lg lg:border lg:border-gray-200 lg:bg-white lg:shadow-sm rounded-lg border-2 border-gray-200 bg-white shadow-sm">
          </div>
        </div>
      </div>

      {/* Secondary Filters Row */}
      <div className="flex flex-wrap items-center lg:gap-2 gap-2">
        {/* Date Range */}
        <div className="flex items-center lg:gap-1 gap-1">
          <Button
            variant={
              filters.dateRange.from &&
              filters.dateRange.to &&
              format(filters.dateRange.from, "yyyy-MM-dd") ===
                format(startOfToday(), "yyyy-MM-dd") &&
              format(filters.dateRange.to, "yyyy-MM-dd") ===
                format(startOfToday(), "yyyy-MM-dd")
                ? "primary"
                : "secondary"
            }
            size="sm"
            onClick={() => setQuickDateRange("today")}
            className={cn(
              "font-medium transition-all duration-200 transform border rounded-lg",
              filters.dateRange.from &&
                filters.dateRange.to &&
                format(filters.dateRange.from, "yyyy-MM-dd") ===
                  format(startOfToday(), "yyyy-MM-dd") &&
                format(filters.dateRange.to, "yyyy-MM-dd") ===
                  format(startOfToday(), "yyyy-MM-dd")
                ? "bg-blue-600 text-white shadow-md border-blue-500 hover:shadow-lg hover:scale-105 hover:bg-blue-700"
                : "bg-white text-gray-700 border-gray-300 hover:bg-blue-50 hover:text-blue-700 hover:scale-102 hover:shadow-sm hover:border-blue-400"
            )}
          >
            Today
          </Button>
          <Button
            variant={
              filters.dateRange.from &&
              filters.dateRange.to &&
              format(filters.dateRange.from, "yyyy-MM-dd") ===
                format(startOfToday(), "yyyy-MM-dd") &&
              format(filters.dateRange.to, "yyyy-MM-dd") ===
                format(addDays(startOfToday(), 7), "yyyy-MM-dd")
                ? "primary"
                : "secondary"
            }
            size="sm"
            onClick={() => setQuickDateRange("week")}
            className={cn(
              "font-medium transition-all duration-200 transform border rounded-lg",
              filters.dateRange.from &&
                filters.dateRange.to &&
                format(filters.dateRange.from, "yyyy-MM-dd") ===
                  format(startOfToday(), "yyyy-MM-dd") &&
                format(filters.dateRange.to, "yyyy-MM-dd") ===
                  format(addDays(startOfToday(), 7), "yyyy-MM-dd")
                ? "bg-blue-600 text-white shadow-md border-blue-500 hover:shadow-lg hover:scale-105 hover:bg-blue-700"
                : "bg-white text-gray-700 border-gray-300 hover:bg-blue-50 hover:text-blue-700 hover:scale-102 hover:shadow-sm hover:border-blue-400"
            )}
          >
            Next 7 days
          </Button>

          <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                className={cn(
                  "min-w-[200px] justify-start text-left font-medium border rounded-lg transition-all duration-200 transform",
                  (filters.dateRange.from || filters.dateRange.to)
                    ? "bg-blue-600 text-white shadow-md border-blue-500 hover:shadow-lg hover:scale-105 hover:bg-blue-700"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-blue-50 hover:text-blue-700 hover:scale-102 hover:shadow-sm hover:border-blue-400"
                )}
              >
                <Calendar className="mr-2 h-4 w-4 text-current" />
                {filters.dateRange.from ? (
                  filters.dateRange.to ? (
                    <>
                      {format(filters.dateRange.from, "LLL dd")} -{" "}
                      {format(filters.dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(filters.dateRange.from, "LLL dd, y")
                  )
                ) : (
                  <span>Custom range</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 bg-white border border-gray-200 shadow-lg rounded-lg" align="start">
              <CalendarView
                mode="range"
                defaultMonth={filters.dateRange.from || undefined}
                selected={{
                  from: filters.dateRange.from || undefined,
                  to: filters.dateRange.to || undefined,
                }}
                onSelect={(range: any) => {
                  updateFilter("dateRange", {
                    from: range?.from || null,
                    to: range?.to || null,
                  });
                }}
                numberOfMonths={2}
                className="rounded-md border"
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Status Filter */}
        <Select
          value={filters.status}
          onValueChange={(value) => updateFilter("status", value)}
        >
          <SelectTrigger
            className={cn(
              "w-[170px] border transition-all duration-200 font-medium rounded-lg transform hover:scale-102",
              filters.status !== "all"
                ? "bg-blue-600 text-white shadow-md border-blue-500 hover:shadow-lg hover:bg-blue-700"
                : "bg-white text-gray-700 border-gray-300 hover:bg-blue-50 hover:text-blue-700 hover:shadow-sm hover:border-blue-400"
            )}
          >
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            {getBookingStatusOptions().map(({ value, label }) => (
              <SelectItem key={value} value={value}>{label}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Payment Status Filter */}
        <Select
          value={filters.paymentStatus}
          onValueChange={(value) => updateFilter("paymentStatus", value)}
        >
          <SelectTrigger
            className={cn(
              "w-[160px] border transition-all duration-200 font-medium rounded-lg transform hover:scale-102",
              filters.paymentStatus !== "all"
                ? "bg-green-600 text-white shadow-md border-green-500 hover:shadow-lg hover:bg-green-700"
                : "bg-white text-gray-700 border-gray-300 hover:bg-green-50 hover:text-green-700 hover:shadow-sm hover:border-green-400"
            )}
          >
            <SelectValue placeholder="Payment" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Payment</SelectItem>
            <SelectItem value="Paid">Paid</SelectItem>
            <SelectItem value="Unpaid">Unpaid</SelectItem>
          </SelectContent>
        </Select>

        {/* Extension Status Filter - Only show for extensions view */}
        {currentView === "extensions" && (
          <Select
            value={filters.extensionStatus}
            onValueChange={(value) => updateFilter("extensionStatus", value)}
          >
            <SelectTrigger
              className={cn(
                "w-[160px] border transition-all duration-200 font-medium rounded-lg transform hover:scale-102",
                filters.extensionStatus !== "all"
                  ? "bg-orange-600 text-white shadow-md border-orange-500 hover:shadow-lg hover:bg-orange-700"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-orange-50 hover:text-orange-700 hover:shadow-sm hover:border-orange-400"
              )}
            >
              <SelectValue placeholder="Extension Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Extensions</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
              <SelectItem value="expired">Expired</SelectItem>
            </SelectContent>
          </Select>
        )}

        {/* Vehicle Filter */}
        {availableVehicles.length > 0 && (
          <Select
            value={filters.vehicle}
            onValueChange={(value) => updateFilter("vehicle", value)}
          >
            <SelectTrigger
              className={cn(
                "w-[160px] border transition-all duration-200 font-medium rounded-lg transform hover:scale-102",
                filters.vehicle !== "all"
                  ? "bg-purple-600 text-white shadow-md border-purple-500 hover:shadow-lg hover:bg-purple-700"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-purple-50 hover:text-purple-700 hover:shadow-sm hover:border-purple-400"
              )}
            >
              <SelectValue placeholder="Vehicle" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Vehicles</SelectItem>
              {availableVehicles.map((vehicle) => (
                <SelectItem key={vehicle} value={vehicle}>
                  {vehicle}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

      </div>
    </div>
  );
}
