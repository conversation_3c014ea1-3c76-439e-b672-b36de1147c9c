"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  FileText, 
  Clock, 
  AlertTriangle, 
  Calendar,
  Upload,
  CheckCircle,
  ExternalLink
} from "lucide-react";
import { useDocumentNotifications } from "@/hooks/use-document-notifications";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";

const DOCUMENT_DISPLAY_NAMES: { [key: string]: string } = {
  'drivers_license': "Driver's License",
  'government_id': "Government ID",
  'proof_of_billing': "Proof of Billing"
};

interface DocumentRequestsSectionProps {
  onNavigateToDocuments?: () => void;
}

export function DocumentRequestsSection({ onNavigateToDocuments }: DocumentRequestsSectionProps) {
  const { user } = useCustomerAuth();
  const { pendingRequests, isLoading, refreshRequests, hasPendingRequests } = useDocumentNotifications(user?.id);
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTimeUntilExpiry = (expiresAt: string) => {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diffInHours = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours <= 0) {
      return { text: "Expired", isUrgent: true, isExpired: true };
    } else if (diffInHours <= 24) {
      return { text: `${diffInHours} hour${diffInHours > 1 ? 's' : ''} left`, isUrgent: true, isExpired: false };
    } else if (diffInHours <= 48) {
      return { text: `${Math.ceil(diffInHours / 24)} day${Math.ceil(diffInHours / 24) > 1 ? 's' : ''} left`, isUrgent: true, isExpired: false };
    } else {
      const daysLeft = Math.ceil(diffInHours / 24);
      return { text: `${daysLeft} days left`, isUrgent: false, isExpired: false };
    }
  };

  const handleUploadDocuments = () => {
    if (onNavigateToDocuments) {
      onNavigateToDocuments();
    } else {
      // Scroll to legal documents section or navigate to settings page
      const legalDocsSection = document.getElementById('legal-documents-section');
      if (legalDocsSection) {
        legalDocsSection.scrollIntoView({ behavior: 'smooth' });
      } else {
        window.location.href = '/customer/settings#legal-documents';
      }
    }
  };

  if (!hasPendingRequests && !isLoading) {
    return null; // Don't show the section if there are no pending requests
  }

  return (
    <Card className="w-full border-l-4 border-l-orange-500">
      <CardHeader className="bg-orange-50">
        <CardTitle className="text-lg font-semibold flex items-center gap-2 text-orange-900">
          <AlertTriangle className="w-5 h-5" />
          Document Upload Required
          <Badge className="bg-orange-100 text-orange-800 border-orange-200 ml-2">
            Action Needed
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
            <span className="ml-3 text-gray-600">Loading document requests...</span>
          </div>
        ) : (
          <div className="space-y-4">
            <Alert className="border-orange-200 bg-orange-50">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                <strong>Important:</strong> You have pending document requests that need your immediate attention. 
                Your booking cannot be processed until all required documents are uploaded and verified.
              </AlertDescription>
            </Alert>

            <div className="space-y-3">
              {pendingRequests.map((request) => {
                const timeInfo = getTimeUntilExpiry(request.expires_at);
                
                return (
                  <div 
                    key={request.id} 
                    className={`border rounded-lg p-4 ${
                      timeInfo.isUrgent 
                        ? 'bg-red-50 border-red-200' 
                        : 'bg-blue-50 border-blue-200'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <FileText className="w-4 h-4" />
                          <h4 className="font-medium">
                            {DOCUMENT_DISPLAY_NAMES[request.document_type]}
                          </h4>
                          <Badge 
                            className={
                              timeInfo.isExpired 
                                ? "bg-red-100 text-red-800 border-red-200"
                                : timeInfo.isUrgent 
                                ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                                : "bg-blue-100 text-blue-800 border-blue-200"
                            }
                          >
                            {request.request_type === 'missing' ? 'Missing' : 'Needs Revision'}
                          </Badge>
                        </div>
                        
                        <div className="text-sm text-gray-600 space-y-1">
                          <p className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            Requested: {formatDate(request.requested_at)}
                          </p>
                          <p className={`flex items-center gap-1 ${timeInfo.isUrgent ? 'text-red-600 font-medium' : ''}`}>
                            <Clock className="w-3 h-3" />
                            {timeInfo.isExpired ? 'Request expired' : `Deadline: ${timeInfo.text}`}
                          </p>
                          {request.admin_notes && (
                            <div className="mt-2 p-2 bg-white rounded border-l-2 border-l-blue-500">
                              <p className="text-sm"><strong>Admin Notes:</strong></p>
                              <p className="text-sm text-gray-700">{request.admin_notes}</p>
                            </div>
                          )}
                        </div>

                        {request.booking_details && (
                          <div className="mt-2 text-xs text-gray-500">
                            <p>Related to booking: {request.booking_details.car_model} ({request.booking_details.car_plate})</p>
                            <p>Pickup: {new Date(request.booking_details.pickup_datetime).toLocaleDateString()}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
              <Button 
                onClick={handleUploadDocuments}
                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
              >
                <Upload className="w-4 h-4" />
                Upload Documents Now
              </Button>
              <Button 
                variant="secondary" 
                onClick={refreshRequests}
                className="flex items-center gap-2"
              >
                <CheckCircle className="w-4 h-4" />
                Refresh Status
              </Button>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h5 className="font-medium text-blue-900 mb-2">How to Upload Documents:</h5>
              <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                <li>Click "Upload Documents Now" above</li>
                <li>Navigate to the Legal Documents section</li>
                <li>Upload the required documents shown above</li>
                <li>Click "Save All Documents" to submit for review</li>
                <li>Wait for admin verification (usually within 24 hours)</li>
              </ol>
            </div>

            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                <strong>Timeline:</strong> Document requests typically expire after 7 days. 
                Please upload your documents as soon as possible to avoid booking cancellation.
                Our team reviews documents within 24 hours of submission.
              </AlertDescription>
            </Alert>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
