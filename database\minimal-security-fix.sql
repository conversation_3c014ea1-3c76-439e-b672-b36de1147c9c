-- Minimal security fixes based on actual database schema
-- Only fixes what actually exists in your database

-- ========================================
-- 1. FIX RLS ERROR - Enable RLS on vehicle_categories
-- ========================================

-- Enable RLS on vehicle_categories table (if not already enabled)
DO $$ 
BEGIN
  IF NOT (SELECT rowsecurity FROM pg_tables WHERE tablename = 'vehicle_categories' AND schemaname = 'public') THEN
    ALTER TABLE public.vehicle_categories ENABLE ROW LEVEL SECURITY;
    RAISE NOTICE '✅ Enabled RLS on vehicle_categories';
  ELSE
    RAISE NOTICE '✅ RLS already enabled on vehicle_categories';
  END IF;
END $$;

-- Create policy to allow public read access (since categories are displayed to customers)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'vehicle_categories' 
    AND policyname = 'Allow public read access to vehicle categories'
  ) THEN
    CREATE POLICY "Allow public read access to vehicle categories" ON public.vehicle_categories
    FOR SELECT 
    USING (is_active = true);
    RAISE NOTICE '✅ Created public read policy for vehicle_categories';
  ELSE
    RAISE NOTICE '✅ Public read policy already exists for vehicle_categories';
  END IF;
END $$;

-- Create policy to allow admin full access
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'vehicle_categories' 
    AND policyname = 'Allow admin full access to vehicle categories'
  ) THEN
    CREATE POLICY "Allow admin full access to vehicle categories" ON public.vehicle_categories
    FOR ALL 
    USING (
      EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
      )
    );
    RAISE NOTICE '✅ Created admin access policy for vehicle_categories';
  ELSE
    RAISE NOTICE '✅ Admin access policy already exists for vehicle_categories';
  END IF;
END $$;

-- Grant necessary permissions (safe to re-run)
GRANT SELECT ON public.vehicle_categories TO anon, authenticated;
GRANT ALL ON public.vehicle_categories TO authenticated;

-- ========================================
-- 2. FIX HANDLE_UPDATED_AT FUNCTION SECURITY
-- ========================================

-- Fix handle_updated_at function (the only function that exists based on your triggers)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'handle_updated_at' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
    ALTER FUNCTION public.handle_updated_at() SET search_path = public;
    ALTER FUNCTION public.handle_updated_at() SECURITY DEFINER;
    RAISE NOTICE '✅ Fixed handle_updated_at() function security';
  ELSE
    RAISE NOTICE '❌ Function handle_updated_at() does not exist';
  END IF;
END $$;

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Verify RLS is enabled
SELECT 
  schemaname, 
  tablename, 
  rowsecurity,
  CASE WHEN rowsecurity THEN '✅ ENABLED' ELSE '❌ DISABLED' END as rls_status
FROM pg_tables 
WHERE tablename = 'vehicle_categories';

-- Verify policies exist
SELECT 
  tablename,
  policyname,
  '✅ EXISTS' as policy_status
FROM pg_policies 
WHERE tablename = 'vehicle_categories'
ORDER BY policyname;

-- Verify function security settings
SELECT 
  p.proname as function_name,
  p.prosecdef as security_definer,
  p.proconfig as search_path_config,
  CASE WHEN p.prosecdef THEN '✅ SECURE' ELSE '❌ INSECURE' END as security_status,
  CASE WHEN 'search_path=public' = ANY(p.proconfig) THEN '✅ FIXED' ELSE '❌ MUTABLE' END as search_path_status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
  AND p.proname = 'handle_updated_at';
