# GPS Device Mapping Setup Guide

## Problem Overview

The GPS tracking system was failing because GPS devices (like "lilygo-esp32-01") send device IDs that aren't UUIDs, but the `gps_locations` table requires a valid car UUID in the `car_id` field. This caused database insert failures and required manual URL token handling.

## Solution: Device ID to Car UUID Mapping

A new mapping system resolves GPS device IDs to actual car UUIDs automatically.

## Setup Instructions

### 1. Deploy Database Schema

Execute the SQL migration to create the GPS device mapping table:

```bash
# Apply the GPS device mapping schema
psql -h your-supabase-host -U postgres -d postgres -f database/gps-device-mapping.sql
```

Or run in Supabase SQL Editor:
- Open file: `database/gps-device-mapping.sql`
- Execute all statements

### 2. Configure Device Mappings

#### Option A: Using Admin Interface (Recommended)

1. Navigate to `/admin/gps-devices` in your admin panel
2. Click "Add GPS Device"
3. Fill in the device details:
   - **Device ID**: `lilygo-esp32-01` (the ID your GPS device sends)
   - **Device Name**: `LilyGO T-Call A7670E GPS Tracker`
   - **Device Type**: `lilygo`
   - **Assigned Car**: Select from your car fleet
   - **Active**: Checked

#### Option B: Manual SQL Insert

```sql
-- Find a car to assign the GPS device to
SELECT id, model, plate_number FROM cars WHERE is_archived = false LIMIT 5;

-- Insert the GPS device mapping (replace the car_id with actual UUID)
INSERT INTO gps_device_mapping (device_id, car_id, device_name, device_type) 
VALUES (
  'lilygo-esp32-01',
  'YOUR_ACTUAL_CAR_UUID_HERE',
  'LilyGO T-Call A7670E GPS Tracker',
  'lilygo'
);
```

### 3. Update Cars Table (Optional)

Add GPS tracking columns to cars table if not present:

```sql
-- Add GPS position tracking columns to cars table
ALTER TABLE cars 
ADD COLUMN IF NOT EXISTS current_latitude numeric(10, 8),
ADD COLUMN IF NOT EXISTS current_longitude numeric(11, 8),
ADD COLUMN IF NOT EXISTS last_gps_update timestamp with time zone;

-- Create index for GPS queries
CREATE INDEX IF NOT EXISTS idx_cars_gps_update ON cars (last_gps_update);
```

### 4. Test the System

1. **Verify Device Mapping**:
   ```sql
   SELECT * FROM get_gps_devices_with_cars();
   ```

2. **Test GPS Ingestion**:
   Send a test GPS payload to `/api/gps/ingest`:
   ```json
   {
     "carId": "lilygo-esp32-01",
     "latitude": 14.691597,
     "longitude": 121.042702,
     "speed": 0,
     "heading": 0,
     "status": "active"
   }
   ```

3. **Check Results**:
   ```sql
   -- Should now show GPS data with proper car_id
   SELECT * FROM gps_locations WHERE car_id IS NOT NULL ORDER BY created_at DESC LIMIT 5;
   
   -- Check car position updates
   SELECT model, plate_number, current_latitude, current_longitude, last_gps_update 
   FROM cars WHERE last_gps_update IS NOT NULL;
   ```

## How It Works

### Before (Problem)
```
GPS Device "lilygo-esp32-01" → API → ❌ UUID validation fails
                                   → 💾 Falls back to dev store
                                   → 🚫 No proper tracking
```

### After (Solution)
```
GPS Device "lilygo-esp32-01" → API → 🔍 Lookup device mapping
                                   → ✅ Get car UUID
                                   → 📊 Insert into gps_locations
                                   → 📍 Update car position
                                   → 🎯 Proper tracking!
```

## Key Features

- **Automatic Resolution**: Device IDs are automatically mapped to car UUIDs
- **Backward Compatibility**: Still works with direct UUID submissions
- **Admin Management**: Full CRUD interface for managing device mappings
- **Fallback Support**: Falls back to dev store if mapping fails
- **Real-time Updates**: Car positions are updated automatically

## API Changes

The GPS ingestion API (`/api/gps/ingest`) now:

1. Checks if `carId` is a valid UUID
2. If not, looks up device mapping using `get_car_id_from_device()`
3. Uses resolved car UUID for database operations
4. Updates car position snapshot automatically
5. Falls back to dev store only if mapping fails

## Security Notes

- GPS device mappings use Row Level Security (RLS)
- Only super admins can create/modify device mappings
- All users can read device mappings for tracking purposes
- Device tokens still require proper authentication headers

## Troubleshooting

### Device Not Found
```
⚠️ No mapping found for device: lilygo-esp32-01
```
**Solution**: Add device mapping via admin interface or SQL

### Database Insert Still Fails
```
🚨 Database insert failed: invalid input syntax for type uuid
```
**Solution**: Verify car UUID exists in cars table and device mapping is correct

### Admin Interface Not Accessible
**Solution**: Ensure user has `super_admin` role in profiles table

## Additional Device Setup

For multiple GPS devices, repeat the mapping process:

```sql
-- Example for multiple devices
INSERT INTO gps_device_mapping (device_id, car_id, device_name, device_type) VALUES
('lilygo-esp32-01', 'car-uuid-1', 'ESP32 Tracker #1', 'lilygo'),
('lilygo-esp32-02', 'car-uuid-2', 'ESP32 Tracker #2', 'lilygo'),
('generic-gps-01', 'car-uuid-3', 'Generic GPS Tracker', 'generic');
```

## Next Steps

1. ✅ Deploy database schema
2. ✅ Configure device mappings
3. ✅ Test GPS ingestion
4. 🎯 Monitor tracking in `/admin/tracker`
5. 📱 GPS tokens should no longer need manual URL pasting
