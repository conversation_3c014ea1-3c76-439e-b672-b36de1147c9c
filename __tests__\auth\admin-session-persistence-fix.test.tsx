/**
 * Test for the admin session persistence fix
 * 
 * This test verifies that admin users remain logged in after page refresh
 * and are not incorrectly redirected to the login page.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { middleware } from '../../middleware'

// Mock Supabase
const mockSupabaseClient = {
  auth: {
    getUser: vi.fn(),
  },
}

vi.mock('@supabase/ssr', () => ({
  createServerClient: vi.fn(() => mockSupabaseClient),
}))

describe('Admin Session Persistence Fix', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Admin Route Handling', () => {
    it('should allow admin routes to pass through without authentication checks', async () => {
      // Mock successful auth (though it shouldn't matter for admin routes)
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin-123', email: '<EMAIL>' } },
        error: null
      })

      // Create request for admin route
      const request = new NextRequest('http://localhost:3000/admin/dashboard')

      const response = await middleware(request)

      // Should allow the request to pass through (no redirect)
      expect(response.status).toBe(200)
      expect(response.headers.get('location')).toBeNull()
    })

    it('should allow admin routes even when auth fails', async () => {
      // Mock auth failure
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: new Error('No session')
      })

      // Create request for admin route
      const request = new NextRequest('http://localhost:3000/admin/cars')

      const response = await middleware(request)

      // Should still allow the request to pass through
      // The AdminProtection component will handle the actual auth check
      expect(response.status).toBe(200)
      expect(response.headers.get('location')).toBeNull()
    })

    it('should handle admin sub-routes correctly', async () => {
      const adminRoutes = [
        '/admin',
        '/admin/dashboard', 
        '/admin/cars',
        '/admin/bookings',
        '/admin/settings',
        '/admin/accounts',
        '/admin/tracker'
      ]

      for (const route of adminRoutes) {
        const request = new NextRequest(`http://localhost:3000${route}`)
        const response = await middleware(request)

        expect(response.status).toBe(200)
        expect(response.headers.get('location')).toBeNull()
      }
    })

    it('should not bypass admin-auth routes', async () => {
      // Admin auth routes should still go through normal processing
      const request = new NextRequest('http://localhost:3000/admin-auth')

      const response = await middleware(request)

      // Should process normally (not bypass)
      expect(response.status).toBe(200)
      // The auth check should have been called for non-admin routes
      expect(mockSupabaseClient.auth.getUser).toHaveBeenCalled()
    })
  })

  describe('Non-Admin Route Handling', () => {
    it('should process customer routes normally', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'customer-123', email: '<EMAIL>' } },
        error: null
      })

      const request = new NextRequest('http://localhost:3000/customer/dashboard')

      const response = await middleware(request)

      // Should process normally with auth check
      expect(response.status).toBe(200)
      expect(mockSupabaseClient.auth.getUser).toHaveBeenCalled()
    })

    it('should process public routes normally', async () => {
      const request = new NextRequest('http://localhost:3000/')

      const response = await middleware(request)

      // Should process normally
      expect(response.status).toBe(200)
      expect(mockSupabaseClient.auth.getUser).toHaveBeenCalled()
    })
  })

  describe('GPS Tracker Route Handling', () => {
    it('should handle GPS tracker routes with parameters', async () => {
      const request = new NextRequest('http://localhost:3000/admin/tracker?gps=true&lat=40.7128&lon=-74.0060')

      const response = await middleware(request)

      // Should allow the request to pass through
      expect(response.status).toBe(200)
      expect(response.headers.get('location')).toBeNull()
    })
  })

  describe('Session Persistence Scenario', () => {
    it('should simulate the page refresh scenario that was causing issues', async () => {
      // Simulate a page refresh where:
      // 1. Admin user has session in localStorage
      // 2. Middleware runs before client-side auth context loads
      // 3. Previously this would cause a redirect to login

      // Mock no server-side session (common on page refresh before sync)
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      // Request to admin page (simulating page refresh)
      const request = new NextRequest('http://localhost:3000/admin/bookings')

      const response = await middleware(request)

      // With the fix: should allow request through
      // AdminProtection component will handle auth check client-side
      expect(response.status).toBe(200)
      expect(response.headers.get('location')).toBeNull()

      // Before fix: this would have been a 307 redirect to /admin-auth
    })
  })
})
