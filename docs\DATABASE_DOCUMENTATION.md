# Ollie Track Database Schema Documentation

This document provides comprehensive documentation for the Supabase database schema used in the Ollie Track car rental management system.

## Overview

The database is designed with security, scalability, and performance in mind. It uses PostgreSQL features like Row Level Security (RLS), triggers, and proper indexing to ensure data integrity and optimal performance.

## Database Tables

### 1. `profiles` (User Management)

Extends Supabase `auth.users` with application-specific user data.

```sql
CREATE TABLE public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  full_name TEXT,
  phone TEXT,
  role TEXT NOT NULL DEFAULT 'customer' CHECK (role IN ('customer', 'admin')),
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Features:**
- Automatically created when user signs up (via trigger)
- Role-based access control (customer/admin)
- Links to Supabase auth system
- Soft deletion (cascades from auth.users)

**RLS Policies:**
- Public read access for basic profile info
- Users can only update their own profile
- Profile creation is automatic via trigger

### 2. `cars` (Vehicle Inventory)

Stores all vehicle information and current status.

```sql
CREATE TABLE public.cars (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  model TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('SUV', 'Sport', 'Coupe', 'Hatchback', 'MPV')),
  plate_number TEXT NOT NULL UNIQUE,
  status TEXT NOT NULL DEFAULT 'Available' CHECK (status IN ('Available', 'Rented', 'In Maintenance')),
  condition TEXT NOT NULL DEFAULT 'Good' CHECK (condition IN ('Good', 'Needs Repair')),
  fuel_capacity INTEGER NOT NULL,
  fuel_type TEXT NOT NULL,
  transmission TEXT NOT NULL CHECK (transmission IN ('Manual', 'Automatic', 'CVT')),
  seats INTEGER NOT NULL,
  price_per_day DECIMAL(10,2) NOT NULL,
  image_url TEXT,
  notes TEXT,
  is_archived BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  -- GPS tracking fields
  current_latitude DECIMAL(10, 8),
  current_longitude DECIMAL(11, 8),
  last_gps_update TIMESTAMPTZ
);
```

**Key Features:**
- Comprehensive vehicle specifications
- Real-time GPS location tracking
- Archive functionality (soft delete)
- Price management per vehicle
- Image storage integration

**RLS Policies:**
- Public read access (non-archived cars)
- Admin-only write access
- Archived cars hidden from public view

### 3. `bookings` (Reservation Management)

Manages all car rental bookings and their lifecycle.

```sql
CREATE TABLE public.bookings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  car_id UUID NOT NULL REFERENCES public.cars(id) ON DELETE CASCADE,
  pickup_location TEXT NOT NULL,
  dropoff_location TEXT NOT NULL,
  pickup_datetime TIMESTAMPTZ NOT NULL,
  dropoff_datetime TIMESTAMPTZ NOT NULL,
  special_requests TEXT,
  status TEXT NOT NULL DEFAULT 'Pending' CHECK (status IN ('Pending', 'Active', 'Completed', 'Cancelled')),
  total_amount DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Features:**
- Complete booking lifecycle management
- Flexible pickup/dropoff locations
- Special requests handling
- Automatic pricing calculation
- Status tracking

**RLS Policies:**
- Customers see only their own bookings
- Admins see all bookings
- Users can cancel their own bookings
- Status updates controlled by business logic

### 4. `payments` (Financial Transactions)

Tracks all payment transactions related to bookings.

```sql
CREATE TABLE public.payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID NOT NULL REFERENCES public.bookings(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'Pending' CHECK (status IN ('Pending', 'Paid', 'Failed', 'Refunded')),
  method TEXT NOT NULL CHECK (method IN ('Card', 'Wallet', 'Cash')),
  transaction_id TEXT,
  transaction_date TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Features:**
- Multiple payment methods support
- Transaction tracking
- Refund management
- Integration with payment gateways
- Audit trail

**RLS Policies:**
- Customers see payments for their bookings only
- Admin-only payment creation and updates
- Financial data strictly controlled

### 5. `gps_locations` (Real-time Tracking)

Stores real-time GPS location data for vehicles.

```sql
CREATE TABLE public.gps_locations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  car_id UUID NOT NULL REFERENCES public.cars(id) ON DELETE CASCADE,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  speed DECIMAL(5,2) DEFAULT 0, -- km/h
  heading DECIMAL(5,2) DEFAULT 0, -- degrees
  status TEXT NOT NULL DEFAULT 'offline' CHECK (status IN ('active', 'idle', 'offline')),
  driver_id UUID REFERENCES public.profiles(id),
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Features:**
- Real-time location tracking
- Speed and direction monitoring
- Driver assignment
- Vehicle status tracking
- High-precision coordinates

**RLS Policies:**
- Admin-only access (privacy protection)
- No customer access to GPS data
- Strict data control for security

### 6. `car_routes` (Historical Tracking)

Stores historical route data for analytics and reporting.

```sql
CREATE TABLE public.car_routes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  car_id UUID NOT NULL REFERENCES public.cars(id) ON DELETE CASCADE,
  booking_id UUID REFERENCES public.bookings(id) ON DELETE SET NULL,
  route_data JSONB NOT NULL, -- Array of {lat, lng, timestamp} objects
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ,
  total_distance DECIMAL(10,2), -- in kilometers
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Features:**
- Complete route history
- Distance calculation
- Booking association
- JSONB for flexible route data
- Analytics support

**RLS Policies:**
- Admin-only access
- Historical data for reporting
- Privacy-protected customer data

## Security Implementation

### Row Level Security (RLS)

All tables have RLS enabled with specific policies:

1. **Customer Data Isolation**: Customers can only access their own data
2. **Admin Privileges**: Admins have full access to manage the system
3. **Privacy Protection**: GPS and financial data strictly controlled
4. **Data Integrity**: Proper constraints and validation

### Authentication Flow

```mermaid
graph TD
    A[User Signup] --> B{Role?}
    B -->|Customer| C[Profile Created with role='customer']
    B -->|Admin| D[Manual Admin Creation]
    C --> E[Customer Dashboard Access]
    D --> F[Admin Dashboard Access]
    E --> G[Limited Data Access]
    F --> H[Full System Access]
```

### Role-Based Access

| Resource | Customer | Admin |
|----------|----------|-------|
| Own Profile | Read/Write | Read/Write |
| Other Profiles | Read (basic) | Read/Write |
| Cars | Read | Full CRUD |
| Own Bookings | Read/Write | Read/Write |
| All Bookings | None | Read/Write |
| Payments | Own only | Full Access |
| GPS Data | None | Full Access |

## Database Functions and Triggers

### 1. Auto Profile Creation

```sql
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
    COALESCE(NEW.raw_user_meta_data->>'role', 'customer')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

**Purpose**: Automatically creates a profile when a user signs up through Supabase Auth.

### 2. Updated At Trigger

```sql
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

**Purpose**: Automatically updates the `updated_at` timestamp on record changes.

## Performance Optimization

### Indexes

Strategic indexes are created for optimal query performance:

```sql
-- Profiles
CREATE INDEX idx_profiles_role ON public.profiles(role);
CREATE INDEX idx_profiles_email ON public.profiles(email);

-- Cars
CREATE INDEX idx_cars_status ON public.cars(status);
CREATE INDEX idx_cars_type ON public.cars(type);
CREATE INDEX idx_cars_archived ON public.cars(is_archived);

-- Bookings
CREATE INDEX idx_bookings_customer ON public.bookings(customer_id);
CREATE INDEX idx_bookings_car ON public.bookings(car_id);
CREATE INDEX idx_bookings_status ON public.bookings(status);
CREATE INDEX idx_bookings_dates ON public.bookings(pickup_datetime, dropoff_datetime);

-- GPS Locations
CREATE INDEX idx_gps_car ON public.gps_locations(car_id);
CREATE INDEX idx_gps_timestamp ON public.gps_locations(timestamp);
```

### Query Patterns

Common query patterns optimized by the schema:

1. **Car Availability Check**: Uses date range indexes on bookings
2. **User Bookings**: Uses customer_id index
3. **GPS Tracking**: Uses car_id and timestamp indexes
4. **Payment History**: Uses booking_id foreign key
5. **Admin Reports**: Uses status and date indexes

## API Integration

### Client-Side Operations (Customers)

```typescript
import { db } from '@/lib/supabase/database'

// Get available cars
const cars = await db.getAvailableCars(fromDate, toDate)

// Create booking
const booking = await db.createBooking({
  carId: 'car-uuid',
  pickUpLocation: 'Location A',
  dropOffLocation: 'Location B',
  pickUpDateTime: '2024-01-01T10:00:00Z',
  dropOffDateTime: '2024-01-03T10:00:00Z',
  paymentMethod: 'Card'
})

// Get my bookings
const myBookings = await db.getMyBookings()
```

### Server-Side Operations (Admin)

```typescript
import { serverDb } from '@/lib/supabase/database'

// Get all bookings
const allBookings = await serverDb.getAllBookings()

// Add new car
const newCar = await serverDb.addCar({
  model: 'Toyota Camry 2024',
  type: 'Coupe',
  plate_number: 'ABC-123',
  // ... other car details
})

// Update GPS location
const gpsUpdate = await serverDb.updateGPSLocation(carId, {
  latitude: 14.5995,
  longitude: 120.9842,
  speed: 45,
  heading: 90,
  status: 'active'
})
```

## Real-time Features

### Supabase Realtime

Tables enabled for real-time updates:

```sql
ALTER PUBLICATION supabase_realtime ADD TABLE public.bookings;
ALTER PUBLICATION supabase_realtime ADD TABLE public.cars;
ALTER PUBLICATION supabase_realtime ADD TABLE public.gps_locations;
ALTER PUBLICATION supabase_realtime ADD TABLE public.payments;
```

### Client Subscriptions

```typescript
// Subscribe to booking changes
const subscription = db.subscribeToBookings((payload) => {
  console.log('Booking updated:', payload)
  // Update UI accordingly
})

// Subscribe to GPS updates (admin only)
const gpsSubscription = db.subscribeToGPSLocations((payload) => {
  console.log('GPS update:', payload)
  // Update map markers
})
```

## Data Migration

### From Mock Store

The schema is designed to be compatible with the existing mock store structure:

1. **Property Mapping**: Snake_case database fields map to camelCase frontend
2. **Type Compatibility**: New types extend existing interfaces
3. **Gradual Migration**: Can migrate component by component
4. **Fallback Support**: Can temporarily support both systems

### Sample Data

The schema includes sample car data for development:

```sql
INSERT INTO public.cars (model, type, plate_number, status, condition, fuel_capacity, fuel_type, transmission, seats, price_per_day, image_url, notes) VALUES
  ('Toyota Vios E 2016 AT', 'Coupe', 'ABC-1001', 'Available', 'Good', 40, 'Gas/Premium', 'Automatic', 5, 1500.00, '/toyota_vios_e_2016_at.png', NULL),
  -- ... more sample cars
```

## Monitoring and Maintenance

### Database Health

Monitor these metrics:

1. **Query Performance**: Use Supabase dashboard
2. **Connection Pool**: Monitor active connections
3. **Storage Usage**: Track table sizes
4. **Index Usage**: Verify index effectiveness

### Maintenance Tasks

Regular maintenance:

1. **Vacuum GPS Tables**: High-frequency inserts need regular cleanup
2. **Archive Old Data**: Move old bookings to archive tables
3. **Index Maintenance**: Monitor and optimize slow queries
4. **Backup Verification**: Ensure backup integrity

### Scaling Considerations

For high-volume deployments:

1. **GPS Data Partitioning**: Partition by date for large datasets
2. **Read Replicas**: Use for reporting and analytics
3. **Connection Pooling**: Implement proper connection management
4. **Caching Layer**: Add Redis for session and frequently accessed data

## Troubleshooting

### Common Issues

1. **RLS Policy Errors**: Check user roles and policy conditions
2. **Foreign Key Violations**: Ensure referenced records exist
3. **Permission Denied**: Verify user authentication and role
4. **Slow Queries**: Check indexes and query patterns

### Debug Queries

```sql
-- Check user role
SELECT role FROM profiles WHERE id = auth.uid();

-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'bookings';

-- Monitor query performance
EXPLAIN ANALYZE SELECT * FROM cars WHERE status = 'Available';

-- Check active connections
SELECT * FROM pg_stat_activity;
```

This schema provides a robust foundation for the Ollie Track car rental system with proper security, performance, and scalability considerations.
