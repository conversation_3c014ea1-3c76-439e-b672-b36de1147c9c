"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { SalesFaqDialog } from "@/components/admin/sales-tracking/sales-faq-dialog";
import { RenterIssueTracking } from "@/components/admin/bookings/renter-issue-tracking";
import { BookingDetailsDrawer } from "@/components/admin/bookings/booking-details-drawer";
import { AuthRequiredModal } from "@/components/customer-side/auth/auth-required-modal";
import { PaymentReuploadModal } from "@/components/customer-side/dashboard/payment-reupload-modal";

// Mock data for testing
const mockBooking = {
  id: "BOOK-12345",
  status: "confirmed",
  pickup_date: new Date().toISOString(),
  return_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
  total_amount: 350,
  payment_status: "paid",
  renter: {
    id: "user-123",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+**********",
    address: "123 Main St, Anytown, USA"
  },
  car: {
    id: "car-123",
    make: "Toyota",
    model: "Camry",
    year: 2022,
    license_plate: "ABC123",
    color: "Silver"
  },
  branch: {
    id: "branch-1",
    name: "Downtown Branch",
    address: "456 Center Ave, Anytown, USA"
  },
  documents: [
    { id: "doc-1", type: "license", status: "verified", url: "/images/customer_1.jpg", title: "Driver's License" },
    { id: "doc-2", type: "insurance", status: "pending", url: "/images/customer_2.jpg", title: "Insurance Document" }
  ],
  payment: {
    id: "pay-123",
    amount: 350,
    method: "credit_card",
    status: "paid",
    date: new Date().toISOString()
  }
};

const mockIssues = [
  {
    id: "issue-1",
    title: "Scratched Door",
    description: "Customer reported scratch on driver's side door",
    status: "open",
    created_at: new Date().toISOString(),
    booking_id: "BOOK-12345",
    severity: "medium"
  }
];

export default function ModalCloseButtonTest() {
  const [viewportSize, setViewportSize] = useState({ width: 0, height: 0 });
  const [isBookingDrawerOpen, setIsBookingDrawerOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isPaymentReuploadOpen, setIsPaymentReuploadOpen] = useState(false);

  // Update viewport size on client side
  React.useEffect(() => {
    const updateSize = () => {
      setViewportSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };
    
    window.addEventListener('resize', updateSize);
    updateSize();
    
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  // Determine current breakpoint
  const getBreakpoint = (width: number) => {
    if (width < 375) return "xs (< 375px)";
    if (width < 640) return "sm (< 640px)";
    if (width < 768) return "md (< 768px)";
    if (width < 1024) return "lg (< 1024px)";
    if (width < 1280) return "xl (< 1280px)";
    return "2xl (≥ 1280px)";
  };

  return (
    <div className="container py-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Modal Close Button Test</CardTitle>
          <CardDescription>
            Test all modal components to ensure they have proper close buttons on mobile and tablet views
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-2 mb-4 bg-muted rounded-md flex items-center justify-between">
            <div>
              <p className="text-sm font-medium">Current viewport size:</p>
              <p className="text-lg font-bold">{viewportSize.width} × {viewportSize.height}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Breakpoint:</p>
              <p className="text-lg font-bold">{getBreakpoint(viewportSize.width)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="admin">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="admin">Admin Modals</TabsTrigger>
          <TabsTrigger value="customer">Customer Modals</TabsTrigger>
        </TabsList>
        
        <TabsContent value="admin" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Admin Modals</CardTitle>
              <CardDescription>Test admin-side modal components</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Sales FAQ Dialog</CardTitle>
                    <CardDescription>Tests the Sales FAQ modal with close button</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <SalesFaqDialog />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Renter Issue Tracking</CardTitle>
                    <CardDescription>Tests Add Issue and Resolve Issue dialogs</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <RenterIssueTracking 
                      customerId="user-123"
                      customerName="John Doe"
                      currentAdminId="admin-123"
                      issues={[
                        {
                          id: "issue-1",
                          customer_id: "user-123",
                          category_id: "cat-1",
                          description: "Customer reported scratch on driver's side door",
                          severity: "Medium",
                          resolved: false,
                          created_by: "admin-123",
                          created_at: new Date().toISOString(),
                          updated_at: new Date().toISOString()
                        }
                      ]}
                    />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Booking Details Drawer</CardTitle>
                    <CardDescription>Tests document viewer and decision modals</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button onClick={() => setIsBookingDrawerOpen(true)}>
                      Open Booking Details
                    </Button>
                    <BookingDetailsDrawer
                      booking={{
                        id: "BOOK-12345",
                        customer_id: "user-123",
                        car_id: "car-123",
                        pickup_location: "456 Center Ave, Anytown, USA",
                        dropoff_location: "456 Center Ave, Anytown, USA",
                        pickup_datetime: new Date().toISOString(),
                        dropoff_datetime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                        status: "Pending",
                        total_amount: 350,
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        userName: "John Doe",
                        carModel: "Toyota Camry",
                        from: new Date(),
                        to: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                        days: 7,
                        payStatus: "Paid",
                        totalAmount: 350
                      }}
                      open={isBookingDrawerOpen}
                      onOpenChange={(open) => setIsBookingDrawerOpen(open)}
                      currentAdminId="admin-123"
                    />
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customer" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Modals</CardTitle>
              <CardDescription>Test customer-side modal components</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Auth Required Modal</CardTitle>
                    <CardDescription>Tests the authentication required modal</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button onClick={() => setIsAuthModalOpen(true)}>
                      Open Auth Modal
                    </Button>
                    <AuthRequiredModal 
                      isOpen={isAuthModalOpen}
                      onClose={() => setIsAuthModalOpen(false)}
                      action="book this car"
                      carModel="Toyota Camry"
                    />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Payment Reupload Modal</CardTitle>
                    <CardDescription>Tests the payment reupload modal</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button onClick={() => setIsPaymentReuploadOpen(true)}>
                      Open Payment Reupload
                    </Button>
                    <PaymentReuploadModal
                      bookingId="BOOK-12345"
                      paymentId="pay-123"
                      onSuccess={() => setIsPaymentReuploadOpen(false)}
                    />
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">How to Test:</h3>
            <ol className="list-decimal list-inside space-y-1">
              <li>Open each modal by clicking the respective buttons</li>
              <li>Verify that each modal has a close button in the header</li>
              <li>Test the close button functionality by clicking it</li>
              <li>Resize your browser to test on different screen sizes:
                <ul className="list-disc list-inside ml-6 mt-1">
                  <li>Mobile S: 320px</li>
                  <li>Mobile M: 375px</li>
                  <li>Mobile L: 425px</li>
                  <li>Tablet: 768px</li>
                  <li>Laptop: 1024px</li>
                </ul>
              </li>
              <li>Ensure close buttons are properly sized and positioned on all screen sizes</li>
              <li>Verify that close buttons are accessible (proper aria labels)</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
