/**
 * Utility functions for generating and managing reference IDs
 * Formats: PMT-YYYYMMDD-XXXX for payments, BKG-YYYYMMDD-XXXX for bookings
 */

/**
 * Generate a payment reference ID
 * Format: PMT-YYYYMMDD-XXXX (e.g., PMT-20231025-7891)
 */
export function generatePaymentRef(): string {
  const now = new Date();
  const datePart = now.getFullYear().toString() + 
    (now.getMonth() + 1).toString().padStart(2, '0') + 
    now.getDate().toString().padStart(2, '0');
  
  const randomPart = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  
  return `PMT-${datePart}-${randomPart}`;
}

/**
 * Generate a booking reference ID  
 * Format: BKG-YYYYMMDD-XXXX (e.g., BKG-20231025-7891)
 */
export function generateBookingRef(): string {
  const now = new Date();
  const datePart = now.getFullYear().toString() + 
    (now.getMonth() + 1).toString().padStart(2, '0') + 
    now.getDate().toString().padStart(2, '0');
  
  const randomPart = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  
  return `BKG-${datePart}-${randomPart}`;
}

/**
 * Validate payment reference ID format
 */
export function isValidPaymentRef(ref: string): boolean {
  const pattern = /^PMT-\d{8}-\d{4}$/;
  return pattern.test(ref);
}

/**
 * Validate booking reference ID format
 */
export function isValidBookingRef(ref: string): boolean {
  const pattern = /^BKG-\d{8}-\d{4}$/;
  return pattern.test(ref);
}

/**
 * Extract date from reference ID
 */
export function extractDateFromRef(ref: string): Date | null {
  const match = ref.match(/^(PMT|BKG)-(\d{4})(\d{2})(\d{2})-\d{4}$/);
  if (!match) return null;
  
  const [, , year, month, day] = match;
  return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
}

/**
 * Format reference ID for display with hash prefix
 */
export function formatRefForDisplay(ref: string): string {
  return `#${ref}`;
}

/**
 * Format booking ID for display with standardized "#BKG" prefix
 * Handles both booking_ref (BKG-YYYYMMDD-XXXX) and raw ID formats
 */
export function formatBookingIdForDisplay(booking: { booking_ref?: string; id: string } | any): string {
  // If booking_ref exists and follows BKG format, use it with # prefix
  if (booking.booking_ref && isValidBookingRef(booking.booking_ref)) {
    return `#${booking.booking_ref}`;
  }

  // If booking_ref exists but doesn't follow BKG format, still use it with # prefix
  if (booking.booking_ref) {
    return `#${booking.booking_ref}`;
  }

  // Fallback to raw ID with #BKG prefix for consistency
  return `#BKG${booking.id.slice(-8)}`;
}

/**
 * Format payment ID for display with standardized "#PMT" prefix
 * Handles both payment_ref (PMT-YYYYMMDD-XXXX) and raw ID formats
 */
export function formatPaymentIdForDisplay(payment: { payment_ref?: string; id: string } | any): string {
  // Handle case where payment_ref might be in different property names
  const paymentRef = payment.payment_ref || payment.paymentRef;

  // If payment_ref exists and follows PMT format, use it with # prefix
  if (paymentRef && isValidPaymentRef(paymentRef)) {
    return `#${paymentRef}`;
  }

  // If payment_ref exists but doesn't follow PMT format, still use it with # prefix
  if (paymentRef) {
    return `#${paymentRef}`;
  }

  // Fallback to raw ID with #PMT prefix for consistency
  return `#PMT${payment.id.slice(-8)}`;
}

/**
 * Remove hash prefix from reference ID if present
 */
export function cleanRefId(ref: string): string {
  return ref.startsWith('#') ? ref.slice(1) : ref;
}

/**
 * Generate a unique reference ID by checking against existing ones
 * This is a client-side helper, actual uniqueness is enforced by database
 */
export async function generateUniqueRef(
  type: 'payment' | 'booking',
  checkExists: (ref: string) => Promise<boolean>
): Promise<string> {
  let attempts = 0;
  const maxAttempts = 10;
  
  while (attempts < maxAttempts) {
    const ref = type === 'payment' ? generatePaymentRef() : generateBookingRef();
    
    // Check if reference already exists
    const exists = await checkExists(ref);
    if (!exists) {
      return ref;
    }
    
    attempts++;
  }
  
  // Fallback: use timestamp-based ID
  const timestamp = Date.now().toString().slice(-4);
  const prefix = type === 'payment' ? 'PMT' : 'BKG';
  const datePart = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  
  return `${prefix}-${datePart}-${timestamp}`;
}
