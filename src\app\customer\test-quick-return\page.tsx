"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PublicAppShell } from "@/components/layout/public-app-shell";
import { ProtectedCustomerPage } from "@/components/customer-side/auth-guard";
import { useOngoingBooking } from "@/hooks/use-ongoing-booking";
import { 
  CheckCircle, 
  Clock, 
  Car, 
  MapPin, 
  User, 
  Phone, 
  Mail,
  AlertTriangle,
  RefreshCw,
  Trash2
} from "lucide-react";

/**
 * Test page for Quick-Return functionality
 * This page allows testing and verification of the ongoing booking banner
 */
export default function TestQuickReturnPage() {
  const { ongoingBooking, isLoading, shouldShowBanner, clearOngoingBooking } = useOngoingBooking();
  const [testData, setTestData] = useState<any>(null);

  // Simulate booking data for testing
  const createTestBookingData = (step: number = 1) => {
    const baseData = {
      selectedCar: {
        id: "test-car-1",
        make: "Toyota",
        model: "Vios",
        year: 2023,
        plate_number: "ABC-1001",
        price_per_day: 1200,
        status: "Available"
      },
      pickUpLocation: "SM City Cebu",
      dropOffLocation: "Ayala Center Cebu", 
      pickUpDate: "2024-01-15",
      pickUpTime: "09:00",
      dropOffDate: "2024-01-18",
      dropOffTime: "18:00",
      pickupTimePeriod: "day" as const,
      returnTimePeriod: "night" as const,
      deliveryFee: 150,
      returnFee: 200,
      totalDeliveryFees: 350,
      pickupGarageChecked: false,
      dropoffSameAsPickupChecked: false,
      isSpecialService: false,
      serviceType: null,
      customerName: "Juan Dela Cruz",
      customerEmail: "<EMAIL>",
      customerPhone: "+63912345678",
      familyMemberPhone: "+63987654321",
      familyMemberFacebook: "juan.delacruz",
      notes: "Test booking for Quick-Return functionality"
    };

    // Add documents based on step
    if (step >= 2) {
      Object.assign(baseData, {
        driversLicense: [{ id: "dl1", status: "completed", fileName: "license.jpg" }],
        governmentId: [{ id: "gov1", status: "completed", fileName: "valid-id.jpg" }],
        proofOfBilling: [{ id: "bill1", status: "completed", fileName: "bill.jpg" }]
      });
    }

    // Add payment info based on step
    if (step >= 4) {
      Object.assign(baseData, {
        paymentMethod: "GCash" as const,
        proofOfPayment: [{ id: "payment1", status: "completed", fileName: "gcash-receipt.jpg" }]
      });
    }

    return baseData;
  };

  const simulateBookingStep = (step: number) => {
    const data = createTestBookingData(step);
    localStorage.setItem("ollietrack-booking-data", JSON.stringify(data));
    localStorage.setItem("ollietrack-booking-meta", JSON.stringify({ 
      lastUpdated: Date.now() 
    }));
    
    // Trigger storage event to update hook
    window.dispatchEvent(new StorageEvent('storage', {
      key: 'ollietrack-booking-data',
      newValue: JSON.stringify(data)
    }));

    setTestData(data);
  };

  const clearTestData = () => {
    clearOngoingBooking();
    setTestData(null);
  };

  const formatDate = (date: string, time: string) => {
    if (!date) return 'Not set';
    try {
      const dateObj = new Date(`${date}T${time || '00:00'}`);
      return dateObj.toLocaleDateString('en-US', { 
        weekday: 'short',
        month: 'short', 
        day: 'numeric',
        hour: time ? 'numeric' : undefined,
        minute: time ? '2-digit' : undefined,
      });
    } catch {
      return date;
    }
  };

  return (
    <ProtectedCustomerPage>
      <PublicAppShell>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="max-w-4xl mx-auto px-4 space-y-6">
            
            {/* Header */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-blue-600" />
                  Quick-Return FAB Test Page
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Test and verify the floating action button (FAB) functionality for ongoing bookings across different states.
                </p>
              </CardHeader>
            </Card>

            {/* Current State */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Current State</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Loading:</span>
                    <Badge variant={isLoading ? "destructive" : "secondary"}>
                      {isLoading ? "Yes" : "No"}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Has Booking:</span>
                    <Badge variant={ongoingBooking ? "default" : "secondary"}>
                      {ongoingBooking ? "Yes" : "No"}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Show Banner:</span>
                    <Badge variant={shouldShowBanner ? "default" : "secondary"}>
                      {shouldShowBanner ? "Yes" : "No"}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Current Step:</span>
                    <Badge variant="secondary">
                      {ongoingBooking?.currentStep || "N/A"}
                    </Badge>
                  </div>
                </div>

                {ongoingBooking && (
                  <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h3 className="font-medium text-blue-900 mb-3">Active Booking Summary</h3>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      {ongoingBooking.selectedCar && (
                        <div className="flex items-center gap-2">
                          <Car className="h-4 w-4 text-blue-600" />
                          <span>
                            {ongoingBooking.selectedCar.year} {ongoingBooking.selectedCar.make} {ongoingBooking.selectedCar.model}
                          </span>
                        </div>
                      )}
                      
                      {ongoingBooking.pickUpDate && (
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-blue-600" />
                          <span>
                            {formatDate(ongoingBooking.pickUpDate, ongoingBooking.pickUpTime)}
                          </span>
                        </div>
                      )}
                      
                      {ongoingBooking.customerName && (
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-blue-600" />
                          <span>{ongoingBooking.customerName}</span>
                        </div>
                      )}
                      
                      {ongoingBooking.customerEmail && (
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-blue-600" />
                          <span>{ongoingBooking.customerEmail}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Test Controls */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Test Controls</CardTitle>
                <p className="text-sm text-gray-600">
                  Simulate different booking states to test banner behavior
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="space-y-3">
                    <h4 className="font-medium">Simulate Booking Steps</h4>
                    <div className="space-y-2">
                      {[1, 2, 3, 4, 5].map(step => (
                        <Button
                          key={step}
                          variant="secondary"
                          size="sm"
                          onClick={() => simulateBookingStep(step)}
                          className="w-full justify-start"
                        >
                          Step {step}: {
                            step === 1 ? "Booking Details" :
                            step === 2 ? "Requirements" :
                            step === 3 ? "Personal Info" :
                            step === 4 ? "Payment" :
                            "Review & Confirm"
                          }
                        </Button>
                      ))}
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <h4 className="font-medium">Actions</h4>
                    <div className="space-y-2">
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={clearTestData}
                        className="w-full"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Clear Booking Data
                      </Button>
                      
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() => window.location.reload()}
                        className="w-full"
                      >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh Page
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <h4 className="font-medium">Quick Navigation</h4>
                    <div className="space-y-2">
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() => window.open('/customer/booking/flow', '_blank')}
                        className="w-full"
                      >
                        Open Booking Flow
                      </Button>
                      
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() => window.open('/customer/dashboard', '_blank')}
                        className="w-full"
                      >
                        Open Dashboard
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Verification Checklist */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Verification Checklist</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium">FAB appears only with ongoing booking</p>
                      <p className="text-sm text-gray-600">Test by simulating booking steps above</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium">FAB visible on all customer pages including homepage</p>
                      <p className="text-sm text-gray-600">Navigate to homepage, dashboard, catalog, etc. FAB should persist</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Clicking FAB shows expanded details, then confirmation modal</p>
                      <p className="text-sm text-gray-600">Test expand → continue → modal flow</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Continue resumes booking flow at correct step</p>
                      <p className="text-sm text-gray-600">Should redirect to booking flow page</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Discard clears booking state completely</p>
                      <p className="text-sm text-gray-600">Banner should disappear after discard</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Responsive design works on all screen sizes</p>
                      <p className="text-sm text-gray-600">Test mobile, tablet, and desktop views</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Banner Behavior Info */}
            <Card className="bg-amber-50 border-amber-200">
              <CardContent className="p-4">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-amber-800">Important Notes</p>
                    <ul className="mt-2 space-y-1 text-sm text-amber-700">
                      <li>• Banner auto-expires after 24 hours</li>
                      <li>• Banner excludes: /customer/login, /customer/signup, /customer/booking/flow</li>
                      <li>• State persists across browser tabs and page refreshes</li>
                      <li>• Booking step is auto-detected from completion status</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </PublicAppShell>
    </ProtectedCustomerPage>
  );
}
