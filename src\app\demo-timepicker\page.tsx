"use client"

import React, { useState } from "react"
import { TimePicker } from "@/components/customer-side/time"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function TimePickerDemo() {
  const [pickupTime, setPickupTime] = useState("08:00")
  const [dropoffTime, setDropoffTime] = useState("19:00")
  const [customTime, setCustomTime] = useState("")

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            TimePicker Component Demo
          </h1>
          <p className="text-gray-600">
            Modern, accessible time picker component for OllieTrack
          </p>
        </div>

        {/* Basic Usage */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Usage - Booking Form Style</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Pick-up Time
                </label>
                <TimePicker
                  value={pickupTime}
                  onChange={setPickupTime}
                  placeholder="Select pick-up time"
                  showQuickActions={true}
                  showSecondaryFormat={false}
                  aria-label="Pick-up time"
                />
                <p className="text-xs text-gray-500">
                  Current value: {pickupTime || "none"}
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Drop-off Time
                </label>
                <TimePicker
                  value={dropoffTime}
                  onChange={setDropoffTime}
                  placeholder="Select drop-off time"
                  showQuickActions={true}
                  showSecondaryFormat={false}
                  aria-label="Drop-off time"
                />
                <p className="text-xs text-gray-500">
                  Current value: {dropoffTime || "none"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Advanced Usage */}
        <Card>
          <CardHeader>
            <CardTitle>Advanced Usage - Homepage Style</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Custom Time Range (9 AM - 6 PM, 30min intervals)
              </label>
              <TimePicker
                value={customTime}
                onChange={setCustomTime}
                placeholder="Select time"
                minTime="09:00"
                maxTime="18:00"
                step={30}
                showQuickActions={true}
                showSecondaryFormat={true}
                aria-label="Custom time range"
              />
              <p className="text-xs text-gray-500">
                Current value: {customTime || "none"}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Features Demo */}
        <Card>
          <CardHeader>
            <CardTitle>Component Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900">Design Features</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>✅ Opaque popover with clean styling</li>
                  <li>✅ Consistent with site typography</li>
                  <li>✅ 48px touch targets for mobile</li>
                  <li>✅ Scrollable dropdown list (max 320px)</li>
                  <li>✅ Blue theme integration</li>
                  <li>✅ Micro-animations with reduced motion support</li>
                </ul>
              </div>
              
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900">Accessibility Features</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>✅ ARIA combobox/listbox pattern</li>
                  <li>✅ Keyboard navigation (arrows, home/end, page up/down)</li>
                  <li>✅ Screen reader announcements</li>
                  <li>✅ Focus management and visual indicators</li>
                  <li>✅ WCAG AA contrast compliance</li>
                  <li>✅ Prevent body scroll when open</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Keyboard Testing</h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• Click or press Enter/Space to open</li>
                  <li>• Use Arrow keys to navigate options</li>
                  <li>• Press Home/End to jump to first/last option</li>
                  <li>• Use Page Up/Down to skip by multiple options</li>
                  <li>• Press Enter to select, Escape to close</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Quick Actions</h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• "Now" - Sets current time (rounded to nearest step)</li>
                  <li>• "+30m" - Adds 30 minutes to current selection</li>
                  <li>• "Clear" - Removes the selected time</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Mobile Testing</h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• Touch targets are ≥44px for easy tapping</li>
                  <li>• Popover prevents background scrolling</li>
                  <li>• Options are clearly visible and tappable</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
