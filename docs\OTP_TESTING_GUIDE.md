# OTP Verification Testing Guide

## Overview
The customer signup flow now uses a 6-digit OTP (One-Time Password) code instead of email confirmation links for better security and user experience.

## Testing the OTP Flow

### 1. Customer Signup
1. Go to `/customer/signup`
2. Fill out the registration form with a valid email
3. Submit the form
4. You should see a toast message: "Please check your email for a 6-digit verification code"
5. You'll be redirected to `/auth/verify-otp?email=<EMAIL>`

### 2. Email Verification
1. Check your email for a message from Supabase
2. The email should contain a 6-digit numeric code
3. The email will have the custom Ollie's Rent A Car branding

### 3. OTP Verification Page
1. Enter the 6-digit code from your email
2. The code input only accepts numbers and limits to 6 digits
3. Click "Verify Account"
4. On success, you'll see a success message and be redirected to login
5. If the code is wrong, you'll see an error toast

### 4. Resend Code Feature
1. If you don't receive the code, click "Resend Code"
2. A new code will be sent to your email
3. The button shows a loading state while sending

### 5. Error Handling
- Invalid codes show error toasts
- Expired codes are handled gracefully
- Network errors are caught and displayed

## Features

### Enhanced Security
- ✅ 6-digit numeric codes instead of long URLs
- ✅ Short expiration time (10 minutes)
- ✅ One-time use codes
- ✅ Resend functionality

### Better UX
- ✅ Custom branded email template
- ✅ Clear instructions in email
- ✅ Responsive verification page
- ✅ Loading states and error handling
- ✅ Toast notifications for feedback

### Mobile Friendly
- ✅ Large, easy-to-tap input field
- ✅ Numeric keyboard on mobile
- ✅ Centered, spaced digits for readability

## Email Template Features

The custom email template includes:
- 🎨 Ollie's Rent A Car branding
- 📱 Mobile-responsive design
- 🔢 Large, prominent verification code
- ⚠️ Clear expiration warning
- ✨ Feature list for new users
- 📍 Company contact information

## Admin vs Customer Flow

- **Customers**: Use OTP verification after signup
- **Admins**: Pre-created accounts, no OTP needed
- **Admin errors**: Only show customer login options (admin login removed from error pages)

## Configuration Notes

1. **Supabase Email Templates**: Custom HTML template configured
2. **Authentication Settings**: OTP enabled, email confirmation required
3. **Redirect URLs**: Properly configured for development and production
4. **Security**: Short expiration times, one-time use codes
