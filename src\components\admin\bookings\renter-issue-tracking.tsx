"use client";

import * as React from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import {
  AlertTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlusIcon,
  EditIcon,
  TrashIcon,
  TagIcon,
  UserIcon,
  CalendarIcon,
  MessageSquareIcon,
  X as XIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import type {
  RenterStatus,
  IssueCategory,
  RenterIssue,
  RenterCategoryTag,
  RenterBehaviorSummary,
  IssueSeverity,
  Profile,
} from "@/lib/types";
import {
  getIssueSeverityColor,
  getIssueSeverityBadgeVariant,
} from "@/lib/supabase/renter-issues";

interface RenterIssueTrackingProps {
  customerId: string;
  customerName: string;
  status?: RenterStatus | null;
  categoryTags?: RenterCategoryTag[];
  issues?: RenterIssue[];
  behaviorSummary?: RenterBehaviorSummary;
  availableCategories?: IssueCategory[];
  currentAdminId: string;
  onStatusUpdate?: (customerId: string, statusTagline: string) => Promise<void>;
  onAddCategoryTag?: (customerId: string, categoryId: string) => Promise<void>;
  onRemoveCategoryTag?: (
    customerId: string,
    categoryId: string
  ) => Promise<void>;
  onAddIssue?: (issue: {
    customerId: string;
    categoryId: string;
    bookingId?: string;
    description: string;
    severity: IssueSeverity;
  }) => Promise<void>;
  onUpdateIssue?: (
    issueId: string,
    updates: Partial<RenterIssue>
  ) => Promise<void>;
  onDeleteIssue?: (issueId: string) => Promise<void>;
}

export function RenterIssueTracking({
  customerId,
  customerName,
  status,
  categoryTags = [],
  issues = [],
  behaviorSummary,
  availableCategories = [],
  currentAdminId,
  onStatusUpdate,
  onAddCategoryTag,
  onRemoveCategoryTag,
  onAddIssue,
  onUpdateIssue,
  onDeleteIssue,
}: RenterIssueTrackingProps) {
  const { toast } = useToast();
  const [isEditingStatus, setIsEditingStatus] = React.useState(false);
  const [statusInput, setStatusInput] = React.useState(
    status?.status_tagline || ""
  );
  const [isAddingIssue, setIsAddingIssue] = React.useState(false);
  const [newIssue, setNewIssue] = React.useState({
    categoryId: "",
    bookingId: "",
    description: "",
    severity: "Medium" as IssueSeverity,
  });
  const [resolveDialogOpen, setResolveDialogOpen] = React.useState(false);
  const [issueToResolve, setIssueToResolve] = React.useState<RenterIssue | null>(null);
  const [resolveNotes, setResolveNotes] = React.useState("");

  const handleStatusUpdate = async () => {
    if (!onStatusUpdate || !statusInput.trim()) return;

    try {
      await onStatusUpdate(customerId, statusInput.trim());
      setIsEditingStatus(false);
    } catch (error) {
      console.error("Error updating status:", error);
    }
  };

  const handleAddIssue = async () => {
    if (!onAddIssue || !newIssue.categoryId || !newIssue.description.trim())
      return;

    try {
      await onAddIssue({
        customerId,
        categoryId: newIssue.categoryId,
        bookingId: newIssue.bookingId || undefined,
        description: newIssue.description.trim(),
        severity: newIssue.severity,
      });
      setNewIssue({
        categoryId: "",
        bookingId: "",
        description: "",
        severity: "Medium",
      });
      setIsAddingIssue(false);
    } catch (error) {
      console.error("Error adding issue:", error);
    }
  };

  const handleResolveIssue = (issue: RenterIssue) => {
    if (!onUpdateIssue) return;
    setIssueToResolve(issue);
    setResolveNotes("");
    setResolveDialogOpen(true);
  };

  const confirmResolveIssue = async () => {
    if (!onUpdateIssue || !issueToResolve) return;
    try {
      await onUpdateIssue(issueToResolve.id, {
        resolved: true,
        resolution_notes: resolveNotes || undefined,
        resolved_by: currentAdminId,
      });
      toast({ title: "Issue resolved", description: resolveNotes ? `Notes: ${resolveNotes}` : undefined });
      setResolveDialogOpen(false);
      setIssueToResolve(null);
      setResolveNotes("");
    } catch (error: any) {
      console.error("Error resolving issue:", error);
      toast({ variant: "destructive", title: "Failed to resolve issue", description: error?.message || "Please try again." });
    }
  };

  const getStatusBadgeVariant = (
    tagline: string
  ): "default" | "secondary" | "destructive" | "outline" => {
    const lower = tagline.toLowerCase();
    if (
      lower.includes("reliable") ||
      lower.includes("excellent") ||
      lower.includes("good")
    ) {
      return "secondary";
    }
    if (
      lower.includes("overdue") ||
      lower.includes("problem") ||
      lower.includes("issue")
    ) {
      return "destructive";
    }
    return "default";
  };

  return (
    <div className="space-y-6">
      {/* Renter Status/Tagline */}
      <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-bold flex items-center gap-3 text-blue-900">
            <UserIcon className="h-5 w-5" />
            Renter Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {!isEditingStatus ? (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {status ? (
                  <Badge
                    variant={getStatusBadgeVariant(status.status_tagline)}
                    className="text-sm font-medium px-3 py-1"
                  >
                    {status.status_tagline}
                  </Badge>
                ) : (
                  <span className="text-gray-500 italic">No status set</span>
                )}
              </div>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => {
                  setStatusInput(status?.status_tagline || "");
                  setIsEditingStatus(true);
                }}
                className="flex items-center gap-2"
              >
                <EditIcon className="h-4 w-4" />
                {status ? "Edit" : "Set Status"}
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              <Input
                placeholder="Enter status tagline (e.g., 'Reliable Renter', 'Prone to Overdue')"
                value={statusInput}
                onChange={(e) => setStatusInput(e.target.value)}
                className="w-full"
              />
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={handleStatusUpdate}
                  disabled={!statusInput.trim()}
                >
                  Save
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setIsEditingStatus(false)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
          {status && (
            <div className="text-xs text-gray-500">
              Last updated:{" "}
              {format(new Date(status.updated_at), "MMM d, yyyy • h:mm a")}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Category Tags */}
      <Card className="border-2 border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-bold flex items-center gap-3 text-purple-900">
            <TagIcon className="h-5 w-5" />
            Issue Categories
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            {categoryTags.map((tag) => (
              <Badge
                key={tag.id}
                variant="outline"
                style={{
                  borderColor: tag.category?.color,
                  color: tag.category?.color,
                }}
                className="flex items-center gap-2 px-3 py-1 text-sm font-medium"
              >
                {tag.category?.name}
                <button
                  onClick={() =>
                    onRemoveCategoryTag?.(customerId, tag.category_id)
                  }
                  className="text-gray-400 hover:text-gray-600 ml-1"
                >
                  <XCircleIcon className="h-3 w-3" />
                </button>
              </Badge>
            ))}
            {categoryTags.length === 0 && (
              <span className="text-gray-500 italic">
                No categories assigned
              </span>
            )}
          </div>

          <Select
            onValueChange={(categoryId) =>
              onAddCategoryTag?.(customerId, categoryId)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Add issue category..." />
            </SelectTrigger>
            <SelectContent>
              {availableCategories
                .filter(
                  (cat) =>
                    !categoryTags.some((tag) => tag.category_id === cat.id)
                )
                .map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                      {category.name}
                    </div>
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Behavior Summary */}
      {behaviorSummary && (
        <Card className="border-2 border-emerald-200 bg-gradient-to-r from-emerald-50 to-green-50">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-bold flex items-center gap-3 text-emerald-900">
              📊 Behavior Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="bg-white p-3 rounded-lg border">
                <div className="text-2xl font-bold text-blue-600">
                  {behaviorSummary.total_bookings}
                </div>
                <div className="text-sm text-gray-600">Total Bookings</div>
              </div>
              <div className="bg-white p-3 rounded-lg border">
                <div className="text-2xl font-bold text-green-600">
                  {behaviorSummary.completed_bookings}
                </div>
                <div className="text-sm text-gray-600">Completed</div>
              </div>
              <div className="bg-white p-3 rounded-lg border">
                <div className="text-2xl font-bold text-red-600">
                  {behaviorSummary.cancelled_bookings}
                </div>
                <div className="text-sm text-gray-600">Cancelled</div>
              </div>
              <div className="bg-white p-3 rounded-lg border">
                <div className="text-2xl font-bold text-orange-600">
                  {behaviorSummary.total_issues}
                </div>
                <div className="text-sm text-gray-600">Total Issues</div>
              </div>
            </div>

            {behaviorSummary.issue_categories.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Issue Breakdown:
                </h4>
                <div className="flex flex-wrap gap-2">
                  {behaviorSummary.issue_categories.map((cat, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      style={{ borderColor: cat.color, color: cat.color }}
                    >
                      {cat.category}: {cat.count}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Issue History */}
      <Card className="border-2 border-amber-200 bg-gradient-to-r from-amber-50 to-yellow-50">
        <CardHeader className="pb-4 flex flex-row items-center justify-between">
          <CardTitle className="text-lg font-bold flex items-center gap-3 text-amber-900">
            <MessageSquareIcon className="h-5 w-5" />
            Issue History ({issues.length})
          </CardTitle>
          <Dialog open={isAddingIssue} onOpenChange={setIsAddingIssue}>
            <DialogTrigger asChild>
              <Button size="sm" className="flex items-center gap-2">
                <PlusIcon className="h-4 w-4" />
                Add Issue
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader className="flex flex-row items-center justify-between">
                <DialogTitle>Add New Issue</DialogTitle>
                <DialogClose asChild>
                  <Button variant="secondary" size="icon" className="h-8 w-8 rounded-full" aria-label="Close">
                    <XIcon className="h-4 w-4" />
                  </Button>
                </DialogClose>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Category</label>
                  <Select
                    value={newIssue.categoryId}
                    onValueChange={(value) =>
                      setNewIssue((prev) => ({ ...prev, categoryId: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category..." />
                    </SelectTrigger>
                    <SelectContent>
                      {availableCategories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: category.color }}
                            />
                            {category.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium">Severity</label>
                  <Select
                    value={newIssue.severity}
                    onValueChange={(value: IssueSeverity) =>
                      setNewIssue((prev) => ({ ...prev, severity: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Low">Low</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="High">High</SelectItem>
                      <SelectItem value="Critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium">Description</label>
                  <Textarea
                    placeholder="Describe the issue..."
                    value={newIssue.description}
                    onChange={(e) =>
                      setNewIssue((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    rows={3}
                  />
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={handleAddIssue}
                    disabled={
                      !newIssue.categoryId || !newIssue.description.trim()
                    }
                    className="flex-1"
                  >
                    Add Issue
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={() => setIsAddingIssue(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent className="space-y-4">
          {issues.length === 0 ? (
            <p className="text-gray-500 italic text-center py-4">
              No issues recorded
            </p>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {issues.map((issue) => (
                <div key={issue.id} className="bg-white p-4 rounded-lg border">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge
                          variant="outline"
                          style={{
                            borderColor: issue.category?.color,
                            color: issue.category?.color,
                          }}
                        >
                          {issue.category?.name}
                        </Badge>
                        <Badge
                          variant={getIssueSeverityBadgeVariant(issue.severity)}
                        >
                          {issue.severity}
                        </Badge>
                        {issue.resolved && (
                          <Badge
                            variant="secondary"
                            className="bg-green-100 text-green-800"
                          >
                            Resolved
                          </Badge>
                        )}
                      </div>

                      <p className="text-sm text-gray-800 mb-2">
                        {issue.description}
                      </p>

                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <CalendarIcon className="h-3 w-3" />
                          {format(new Date(issue.created_at), "MMM d, yyyy")}
                        </span>
                        <span>
                          By: {issue.created_by_admin?.full_name || "Admin"}
                        </span>
                      </div>

                      {issue.resolved && issue.resolution_notes && (
                        <div className="mt-3 p-2 bg-green-50 rounded border-l-4 border-green-200">
                          <p className="text-sm text-green-800">
                            {issue.resolution_notes}
                          </p>
                          {issue.resolved_at && (
                            <p className="text-xs text-green-600 mt-1">
                              Resolved on{" "}
                              {format(
                                new Date(issue.resolved_at),
                                "MMM d, yyyy"
                              )}
                              by {issue.resolved_by_admin?.full_name || "Admin"}
                            </p>
                          )}
                        </div>
                      )}
                    </div>

                    <div className="flex gap-1">
                      {!issue.resolved && (
                        <Button
                          variant="secondary"
                          size="xs"
                          onClick={() => handleResolveIssue(issue)}
                          className="text-green-600 hover:text-green-700"
                        >
                          <CheckCircleIcon className="h-3 w-3" />
                        </Button>
                      )}
                      <Button
                        variant="secondary"
                        size="xs"
                        onClick={() => onDeleteIssue?.(issue.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <TrashIcon className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Resolve Issue Dialog */}
      <Dialog open={resolveDialogOpen} onOpenChange={(o) => { setResolveDialogOpen(o); if (!o) { setIssueToResolve(null); setResolveNotes(""); } }}>
        <DialogContent className="max-w-md">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle>Resolve Issue</DialogTitle>
            <DialogClose asChild>
              <Button variant="secondary" size="icon" className="h-8 w-8 rounded-full" aria-label="Close">
                <XIcon className="h-4 w-4" />
              </Button>
            </DialogClose>
          </DialogHeader>
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">Add resolution notes (optional).</p>
            <Textarea
              placeholder="Resolution notes (optional)..."
              value={resolveNotes}
              onChange={(e) => setResolveNotes(e.target.value)}
              rows={3}
            />
          </div>
          <DialogFooter>
            <Button variant="success" onClick={confirmResolveIssue} className="flex items-center gap-2">
              <CheckCircleIcon className="h-4 w-4" />
              Resolve
            </Button>
            <DialogClose asChild>
              <Button variant="secondary">Cancel</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
