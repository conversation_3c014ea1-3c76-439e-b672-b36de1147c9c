# Supabase Database Setup Guide

This guide will help you set up the Supabase database for the Ollie Track car rental system.

## Prerequisites

1. A Supabase account (free tier is sufficient for development)
2. A new Supabase project created

## Step 1: Create Supabase Project

1. Go to [Supabase](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - Name: `ollie-track`
   - Database Password: (generate a strong password and save it)
   - Region: Choose closest to your users
5. Click "Create new project"
6. Wait for the project to be ready (2-3 minutes)

## Step 2: Configure Environment Variables

1. In your Supabase project dashboard, go to **Settings > API**
2. Copy the following values:
   - Project URL
   - `anon` `public` key
   - `service_role` `secret` key (optional, for admin operations)

3. Create a `.env.local` file in your project root:
```bash
NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## Step 3: Run the Database Schema

1. In your Supabase project dashboard, go to **SQL Editor**
2. Click "New query"
3. Copy the entire contents of `supabase-schema.sql`
4. Paste it into the SQL editor
5. Click "Run" to execute the schema

This will create:
- All necessary tables (profiles, cars, bookings, payments, gps_locations, car_routes)
- Row Level Security (RLS) policies
- Triggers for automatic profile creation
- Indexes for performance
- Sample car data

## 3. Configure Email Templates and OTP

### 3.1 Enable OTP (One-Time Password) Authentication

1. **Go to Authentication > Settings in your Supabase dashboard**
2. **Navigate to "Auth" section**
3. **Find "Enable email confirmations" and make sure it's enabled**
4. **Configure OTP settings:**
   - Set "Confirm email" to `true`
   - Set "Secure email change" to `true`

### 3.2 Customize Email Templates

1. **Go to Authentication > Email Templates**
2. **Select "Confirm signup" template**
3. **Replace the default template with the following:**

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Verify Your Ollie's Rent A Car Account</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9fafb;
        }
        .container {
            background-color: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
        }
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        .logo {
            margin-bottom: 24px;
        }
        .verification-code {
            background-color: #3b82f6;
            color: white;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: 8px;
            padding: 20px 32px;
            border-radius: 8px;
            text-align: center;
            margin: 32px 0;
            font-family: 'Courier New', monospace;
        }
        .features {
            background-color: #f8fafc;
            border-radius: 8px;
            padding: 24px;
            margin: 24px 0;
        }
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .feature:last-child {
            margin-bottom: 0;
        }
        .feature-icon {
            color: #10b981;
            margin-right: 12px;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
        .warning {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 16px;
            margin: 20px 0;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <h1 style="color: #1f2937; margin: 0; font-size: 28px;">🚗 Ollie's Rent A Car</h1>
                <p style="color: #6b7280; margin: 8px 0 0 0;">Premium Car Rental in Ilocos</p>
            </div>
        </div>

        <h2 style="color: #1f2937; text-align: center; margin-bottom: 16px;">Verify Your Account</h2>
        
        <p style="text-align: center; color: #4b5563; margin-bottom: 24px;">
            Welcome to Ollie's Rent A Car! Use the verification code below to complete your account setup:
        </p>

        <div class="verification-code">
            {{ .Token }}
        </div>

        <div class="warning">
            <strong>⚠️ Important:</strong> This verification code will expire in 10 minutes. If you didn't create an account, please ignore this email.
        </div>

        <div class="features">
            <h3 style="color: #1f2937; margin-top: 0;">What you can do with your account:</h3>
            <div class="feature">
                <span class="feature-icon">✓</span>
                <span>Book vehicles instantly from our premium fleet</span>
            </div>
            <div class="feature">
                <span class="feature-icon">✓</span>
                <span>Track your rental history and upcoming bookings</span>
            </div>
            <div class="feature">
                <span class="feature-icon">✓</span>
                <span>Manage your account settings and preferences</span>
            </div>
            <div class="feature">
                <span class="feature-icon">✓</span>
                <span>Get exclusive offers and priority booking</span>
            </div>
        </div>

        <div class="footer">
            <p>You're receiving this email because you signed up for an account at Ollie's Rent A Car.</p>
            <p style="margin-top: 16px;">
                <strong>Ollie's Rent A Car</strong><br>
                Premium Car Rental Service<br>
                Ilocos Norte, Philippines
            </p>
            <p style="margin-top: 16px;">
                Need help? Contact us via our website or social media channels.
            </p>
        </div>
    </div>
</body>
</html>
```

4. **Save the email template**

### 3.3 Configure Authentication Settings

1. **Go to Authentication > Settings**
2. **Update the following settings:**
   - **Site URL:** `http://localhost:3000` (for development) or your production URL
   - **Redirect URLs:** Add `http://localhost:3000/auth/**` (for development)
   - **Email confirmation:** Enabled
   - **Secure email change:** Enabled
   - **Enable email confirmations:** Enabled

## 4. Set Up Admin User (Updated)

Since admin accounts should not be created through the signup flow, you need to create them manually:

### Option A: Using Supabase Dashboard

1. Go to **Authentication > Users**
2. Click "Add user"
3. Fill in:
   - Email: `<EMAIL>` (or your preferred admin email)
   - Password: (generate a strong password)
   - Auto Confirm User: ✅
4. Click "Create user"
5. Click on the newly created user
6. Go to the "Raw User Meta Data" section
7. Add the following JSON:
```json
{
  "role": "admin",
  "full_name": "Admin User"
}
```
8. Click "Save"

### Option B: Using SQL

Run this SQL in the SQL Editor (replace email and password):

```sql
-- Insert admin user into auth.users
INSERT INTO auth.users (
  instance_id,
  id,
  aud,
  role,
  email,
  encrypted_password,
  email_confirmed_at,
  raw_user_meta_data,
  created_at,
  updated_at
) VALUES (
  '********-0000-0000-0000-************',
  uuid_generate_v4(),
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('your_admin_password', gen_salt('bf')),
  NOW(),
  '{"role": "admin", "full_name": "Admin User"}'::jsonb,
  NOW(),
  NOW()
);
```

## Step 5: Test the Setup

1. Start your Next.js development server:
```bash
npm run dev
```

2. Test customer signup:
   - Go to `/customer/signup`
   - Create a new customer account
   - Verify the profile is created automatically

3. Test admin login:
   - Go to `/admin-auth`
   - Login with your admin credentials
   - Verify you can access the admin dashboard

## Step 6: Enable Realtime (Optional)

If you want real-time updates for bookings and GPS tracking:

1. Go to **Database > Replication**
2. Click "Create a new publication"
3. Name it `realtime`
4. Add these tables:
   - `public.bookings`
   - `public.cars`
   - `public.gps_locations`
   - `public.payments`

Or run this SQL:
```sql
CREATE PUBLICATION realtime FOR TABLE 
  public.bookings, 
  public.cars, 
  public.gps_locations, 
  public.payments;
```

## Authentication Flow

### Customer Flow
1. Customer visits `/customer/signup`
2. Fills out signup form with name, email, password
3. Supabase creates user in `auth.users`
4. Trigger automatically creates profile in `public.profiles` with role='customer'
5. Customer can now login at `/customer/login`

### Admin Flow
1. Admin accounts are pre-created in the database with role='admin'
2. Admin visits `/admin-auth` 
3. Logs in with pre-existing credentials
4. Middleware checks role and grants access to admin routes

## Security Features

- **Row Level Security (RLS)**: All tables have RLS enabled
- **Role-based Access**: Customers can only see their own data, admins can see everything
- **GPS Privacy**: Only admins can access GPS tracking data
- **Automatic Profile Creation**: Profiles are created automatically on signup
- **Input Validation**: Database constraints ensure data integrity

## Troubleshooting

### "relation does not exist" errors
- Make sure you ran the complete schema from `supabase-schema.sql`
- Check that all tables were created in the **Database > Tables** section

### Authentication errors
- Verify your environment variables are correct
- Check that the user exists in **Authentication > Users**
- Ensure the user has the correct role in their metadata

### Permission errors
- Check RLS policies are properly set up
- Verify user roles are correctly assigned
- Admin operations require role='admin' in user metadata

### Can't access admin dashboard
- Ensure admin user has role='admin' in raw_user_meta_data
- Check that admin authentication logic in middleware is working
- Verify admin routes are protected properly

## Next Steps

1. Test all major user flows (signup, login, booking, admin operations)
2. Customize the sample car data for your actual inventory
3. Set up production environment variables
4. Consider setting up database backups
5. Monitor usage and optimize queries as needed

## Database Maintenance

- The `gps_locations` table may grow quickly with real GPS data
- Consider partitioning by date for large-scale deployments
- Monitor query performance and add indexes as needed
- Set up automated backups for production

## Support

If you encounter issues:
1. Check the Supabase logs in the dashboard
2. Verify all environment variables are set correctly
3. Ensure the database schema was applied completely
4. Check the browser console for client-side errors
