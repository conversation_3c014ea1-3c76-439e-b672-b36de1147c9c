"use client"

import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { X, AlertTriangle, CheckCircle, Info, AlertCircle } from 'lucide-react'

interface ModalAction {
  label: string
  description?: string
  onClick: () => void
  variant?: 'default' | 'destructive' | 'outline' | 'secondary'
  disabled?: boolean
  loading?: boolean
}

interface EnhancedModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  description?: string
  icon?: React.ComponentType<{ className?: string }>
  iconColor?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  type?: 'default' | 'confirmation' | 'warning' | 'error' | 'success'
  children?: React.ReactNode
  actions?: ModalAction[]
  showCloseButton?: boolean
  closeOnOutsideClick?: boolean
  className?: string
}

const typeConfigs = {
  default: {
    icon: Info,
    iconColor: 'text-blue-600 bg-blue-100'
  },
  confirmation: {
    icon: AlertCircle,
    iconColor: 'text-blue-600 bg-blue-100'
  },
  warning: {
    icon: AlertTriangle,
    iconColor: 'text-amber-600 bg-amber-100'
  },
  error: {
    icon: AlertTriangle,
    iconColor: 'text-red-600 bg-red-100'
  },
  success: {
    icon: CheckCircle,
    iconColor: 'text-green-600 bg-green-100'
  }
}

const sizeClasses = {
  sm: 'sm:max-w-md',
  md: 'sm:max-w-lg',
  lg: 'sm:max-w-2xl',
  xl: 'sm:max-w-4xl',
  full: 'sm:max-w-[95vw]'
}

export function EnhancedModal({
  isOpen,
  onClose,
  title,
  description,
  icon: CustomIcon,
  iconColor,
  size = 'md',
  type = 'default',
  children,
  actions = [],
  showCloseButton = true,
  closeOnOutsideClick = true,
  className
}: EnhancedModalProps) {
  const typeConfig = typeConfigs[type]
  const IconComponent = CustomIcon || typeConfig.icon
  const iconColorClass = iconColor || typeConfig.iconColor

  return (
    <Dialog 
      open={isOpen} 
      onOpenChange={closeOnOutsideClick ? onClose : undefined}
    >
      <DialogContent 
        className={cn(
          // Base responsive classes
          "w-full max-w-[calc(100%-2rem)]",
          sizeClasses[size],
          // Mobile full screen, desktop modal
          "h-full sm:h-auto max-h-[100vh] sm:max-h-[90vh]",
          "rounded-none sm:rounded-lg",
          // Layout
          "flex flex-col gap-0 p-0",
          className
        )}
        showCloseButton={showCloseButton}
      >
        {/* Header */}
        <DialogHeader className="flex-shrink-0 px-6 py-4 border-b border-gray-200">
          <div className="flex items-start gap-3">
            {IconComponent && (
              <div className={cn(
                "w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0",
                iconColorClass
              )}>
                <IconComponent className="h-5 w-5" />
              </div>
            )}
            <div className="flex-1 min-w-0">
              <DialogTitle className="text-xl font-semibold text-left mb-1">
                {title}
              </DialogTitle>
              {description && (
                <DialogDescription className="text-left text-sm text-gray-600">
                  {description}
                </DialogDescription>
              )}
            </div>
          </div>
        </DialogHeader>

        {/* Content */}
        {children && (
          <div className="flex-1 overflow-y-auto px-6 py-4 min-h-0">
            {children}
          </div>
        )}

        {/* Actions */}
        {actions.length > 0 && (
          <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex flex-col sm:flex-row gap-3 justify-end">
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || 'default'}
                  onClick={action.onClick}
                  disabled={action.disabled || action.loading}
                  className={cn(
                    "min-w-0",
                    actions.length === 2 && "flex-1 sm:flex-none sm:min-w-[120px]"
                  )}
                >
                  <div className="flex flex-col items-center gap-1">
                    <span>{action.label}</span>
                    {action.description && (
                      <span className="text-xs opacity-70">{action.description}</span>
                    )}
                  </div>
                </Button>
              ))}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

// Preset modal types for common use cases
export function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmLabel = "Confirm",
  cancelLabel = "Cancel",
  confirmDescription,
  cancelDescription,
  destructive = false,
  loading = false
}: {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  description?: string
  confirmLabel?: string
  cancelLabel?: string
  confirmDescription?: string
  cancelDescription?: string
  destructive?: boolean
  loading?: boolean
}) {
  return (
    <EnhancedModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      description={description}
      type={destructive ? 'warning' : 'confirmation'}
      size="sm"
      actions={[
        {
          label: cancelLabel,
          description: cancelDescription,
          onClick: onClose,
          variant: 'outline',
          disabled: loading
        },
        {
          label: confirmLabel,
          description: confirmDescription,
          onClick: onConfirm,
          variant: destructive ? 'destructive' : 'default',
          disabled: loading,
          loading
        }
      ]}
    />
  )
}

export function AlertModal({
  isOpen,
  onClose,
  title,
  description,
  type = 'info',
  actionLabel = "OK"
}: {
  isOpen: boolean
  onClose: () => void
  title: string
  description?: string
  type?: 'info' | 'warning' | 'error' | 'success'
  actionLabel?: string
}) {
  const modalType = type === 'info' ? 'default' : type

  return (
    <EnhancedModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      description={description}
      type={modalType}
      size="sm"
      actions={[
        {
          label: actionLabel,
          onClick: onClose,
          variant: 'default'
        }
      ]}
    />
  )
}
