// Major Philippine locations organized by region for better UX
export const PHILIPPINES_LOCATIONS = [
  // National Capital Region (NCR)
  {
    region: "National Capital Region (NCR)",
    cities: [
      "Manila",
      "Quezon City",
      "Makati",
      "Taguig",
      "Pasig",
      "Mandaluyong",
      "San Juan",
      "Marikina",
      "Pasay",
      "Parañaque",
      "Las Piñas",
      "Muntinlupa",
      "Caloocan",
      "Malabon",
      "Navotas",
      "Valenzuela",
      "Pateros"
    ]
  },
  // Region I - Ilocos Region (Primary service area)
  {
    region: "Ilocos Region (Region I)",
    cities: [
      "Laoag City",
      "Vigan City",
      "Dagupan City",
      "San Fernando (La Union)",
      "Alaminos City",
      "Urdaneta City",
      "San Carlos City",
      "Candon City",
      "Batac City",
      "Bolinao",
      "Bangued"
    ]
  },
  // Region II - Cagayan Valley
  {
    region: "Cagayan Valley (Region II)",
    cities: [
      "Tuguegarao City",
      "Santiago City",
      "Cauayan City",
      "Ilagan City"
    ]
  },
  // Region III - Central Luzon
  {
    region: "Central Luzon (Region III)",
    cities: [
      "Angeles City",
      "Olongapo City",
      "San Fernando (Pampanga)",
      "Tarlac City",
      "Cabanatuan City",
      "Malolos City",
      "Meycauayan City",
      "San Jose del Monte City",
      "Marilao",
      "Baliuag"
    ]
  },
  // Region IV-A - CALABARZON
  {
    region: "CALABARZON (Region IV-A)",
    cities: [
      "Batangas City",
      "Lipa City",
      "Tanauan City",
      "Calamba City",
      "Santa Rosa City",
      "Biñan City",
      "San Pedro City",
      "Cabuyao City",
      "Lucena City",
      "Antipolo City",
      "Bacoor City",
      "Imus City",
      "Dasmariñas City",
      "General Trias City",
      "Trece Martires City"
    ]
  },
  // Region V - Bicol Region
  {
    region: "Bicol Region (Region V)",
    cities: [
      "Naga City",
      "Legazpi City",
      "Iriga City",
      "Tabaco City",
      "Ligao City",
      "Sorsogon City"
    ]
  },
  // Region VI - Western Visayas
  {
    region: "Western Visayas (Region VI)",
    cities: [
      "Iloilo City",
      "Bacolod City",
      "Dumaguete City",
      "Roxas City",
      "San Carlos City (Negros Occidental)",
      "Kabankalan City",
      "Silay City"
    ]
  },
  // Region VII - Central Visayas
  {
    region: "Central Visayas (Region VII)",
    cities: [
      "Cebu City",
      "Mandaue City",
      "Lapu-Lapu City",
      "Talisay City (Cebu)",
      "Toledo City",
      "Danao City",
      "Carcar City",
      "Tagbilaran City"
    ]
  },
  // Region VIII - Eastern Visayas
  {
    region: "Eastern Visayas (Region VIII)",
    cities: [
      "Tacloban City",
      "Ormoc City",
      "Calbayog City",
      "Catbalogan City",
      "Maasin City",
      "Baybay City"
    ]
  },
  // Region IX - Zamboanga Peninsula
  {
    region: "Zamboanga Peninsula (Region IX)",
    cities: [
      "Zamboanga City",
      "Pagadian City",
      "Dipolog City",
      "Dapitan City"
    ]
  },
  // Region X - Northern Mindanao
  {
    region: "Northern Mindanao (Region X)",
    cities: [
      "Cagayan de Oro City",
      "Iligan City",
      "Malaybalay City",
      "Valencia City",
      "Oroquieta City",
      "Ozamiz City",
      "Tangub City"
    ]
  },
  // Region XI - Davao Region
  {
    region: "Davao Region (Region XI)",
    cities: [
      "Davao City",
      "Tagum City",
      "Panabo City",
      "Samal City",
      "Digos City",
      "Mati City"
    ]
  },
  // Region XII - SOCCSKSARGEN
  {
    region: "SOCCSKSARGEN (Region XII)",
    cities: [
      "General Santos City",
      "Koronadal City",
      "Kidapawan City",
      "Cotabato City",
      "Tacurong City"
    ]
  },
  // CAR - Cordillera Administrative Region
  {
    region: "Cordillera Administrative Region (CAR)",
    cities: [
      "Baguio City",
      "Tabuk City",
      "La Trinidad"
    ]
  },
  // CARAGA - Region XIII
  {
    region: "CARAGA (Region XIII)",
    cities: [
      "Butuan City",
      "Cabadbaran City",
      "Bayugan City",
      "Surigao City",
      "Tandag City",
      "Bislig City"
    ]
  }
]

// Flattened list for simple dropdown usage
export const FLAT_PHILIPPINES_LOCATIONS = PHILIPPINES_LOCATIONS.flatMap(region => 
  region.cities.map(city => ({
    value: city,
    label: city,
    region: region.region
  }))
).sort((a, b) => a.label.localeCompare(b.label))

// Get locations by region for grouped dropdown
export const getLocationsByRegion = () => {
  return PHILIPPINES_LOCATIONS.map(region => ({
    region: region.region,
    cities: region.cities.sort()
  }))
}

// Popular destinations (can be used for quick selection)
export const POPULAR_DESTINATIONS = [
  "Manila",
  "Quezon City",
  "Makati", 
  "Cebu City",
  "Davao City",
  "Baguio City",
  "Iloilo City",
  "Bacolod City",
  "Laoag City", // Highlighting our service area
  "Vigan City",  // Highlighting our service area
  "Cagayan de Oro City",
  "General Santos City",
  "Zamboanga City"
]
