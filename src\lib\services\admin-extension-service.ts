"use client";

import { createClient } from "@/lib/supabase/client";
import { logError, logWithContext } from "@/lib/utils/logger";

export interface ExtensionRequest {
  id: string;
  booking_id: string;
  customer_id: string;
  original_dropoff_datetime: string;
  requested_dropoff_datetime: string;
  extension_duration_hours: number;
  additional_amount: number;
  request_reason?: string;
  request_notes?: string;
  status: 'pending' | 'approved' | 'rejected' | 'expired';
  reviewed_by?: string;
  reviewed_at?: string;
  admin_notes?: string;
  rejection_reason?: string;
  has_conflicts: boolean;
  alternative_cars?: string[];
  alternative_suggestions?: any;
  created_at: string;
  updated_at: string;
  expires_at: string;
  // Joined data
  customer_name?: string;
  customer_email?: string;
  booking_ref?: string;
  car_model?: string;
  car_plate?: string;
}

export interface ExtensionRequestWithDetails extends ExtensionRequest {
  bookings: {
    booking_ref: string;
    pickup_location: string;
    dropoff_location: string;
    cars: {
      model: string;
      plate_number: string;
    } | null;
  } | null;
  customers: {
    full_name: string;
    email: string;
  } | null;
}

/**
 * Fetch all extension requests with booking and customer details
 */
export async function getAllExtensionRequests(): Promise<{
  data: ExtensionRequestWithDetails[] | null;
  error: any;
}> {
  try {
    const supabase = createClient();
    logWithContext("Fetching all extension requests", "getAllExtensionRequests");

    const { data, error } = await supabase
      .from("booking_extensions")
      .select(`
        *,
        bookings!inner (
          booking_ref,
          pickup_location,
          dropoff_location,
          cars (
            model,
            plate_number
          )
        ),
        customers:profiles!booking_extensions_customer_id_fkey (
          full_name,
          email
        )
      `)
      .order("created_at", { ascending: false });

    if (error) {
      logError("Error fetching extension requests", error as Error, {
        function: "getAllExtensionRequests"
      });
      return { data: null, error };
    }

    logWithContext("Fetched extension requests", `getAllExtensionRequests - count: ${data?.length || 0}`);

    return { data: data as ExtensionRequestWithDetails[], error: null };
  } catch (error) {
    logError("Error fetching extension requests", error as Error, {
      function: "getAllExtensionRequests"
    });
    return { data: null, error: error };
  }
}

/**
 * Fetch extension requests filtered by status
 */
export async function getExtensionRequestsByStatus(
  status: 'pending' | 'approved' | 'rejected' | 'expired'
): Promise<{
  data: ExtensionRequestWithDetails[] | null;
  error: any;
}> {
  try {
    const supabase = createClient();
    logWithContext("Fetching extension requests by status", `getExtensionRequestsByStatus - status: ${status}`);

    const { data, error } = await supabase
      .from("booking_extensions")
      .select(`
        *,
        bookings!inner (
          booking_ref,
          pickup_location,
          dropoff_location,
          cars (
            model,
            plate_number
          )
        ),
        customers:profiles!booking_extensions_customer_id_fkey (
          full_name,
          email
        )
      `)
      .eq("status", status)
      .order("created_at", { ascending: false });

    if (error) {
      logError("Error fetching extension requests by status", error as Error, {
        function: "getExtensionRequestsByStatus",
        status
      });
      return { data: null, error };
    }

    return { data: data as ExtensionRequestWithDetails[], error: null };
  } catch (error) {
    logError("Error fetching extension requests by status", error as Error, {
      function: "getExtensionRequestsByStatus",
      status
    });
    return { data: null, error: error };
  }
}

/**
 * Approve an extension request
 */
export async function approveExtensionRequest(
  extensionId: string,
  adminId: string,
  adminNotes?: string
): Promise<{ success: boolean; error: any }> {
  try {
    const supabase = createClient();
    logWithContext("Approving extension request", `approveExtensionRequest - extensionId: ${extensionId}, adminId: ${adminId}`);

    // Start a transaction to update extension and booking
    const { data: extensionData, error: extensionError } = await supabase
      .from("booking_extensions")
      .select(`
        *,
        bookings!inner (
          id,
          dropoff_datetime,
          status
        )
      `)
      .eq("id", extensionId)
      .eq("status", "pending")
      .single();

    if (extensionError || !extensionData) {
      logError("Extension request not found or not pending", extensionError as Error);
      return { success: false, error: extensionError || "Extension request not found" };
    }

    // Update the extension request status
    const { error: updateExtensionError } = await supabase
      .from("booking_extensions")
      .update({
        status: "approved",
        reviewed_by: adminId,
        reviewed_at: new Date().toISOString(),
        admin_notes: adminNotes || null
      })
      .eq("id", extensionId);

    if (updateExtensionError) {
      logError("Error updating extension request", updateExtensionError, {
        function: "approveExtensionRequest",
        extensionId
      });
      return { success: false, error: updateExtensionError };
    }

    // Update the original booking's dropoff time
    const { error: bookingError } = await supabase
      .from("bookings")
      .update({
        dropoff_datetime: extensionData.requested_dropoff_datetime,
        updated_at: new Date().toISOString()
      })
      .eq("id", extensionData.booking_id);

    if (bookingError) {
      logError("Error updating booking dropoff time", bookingError, {
        function: "approveExtensionRequest",
        extensionId,
        bookingId: extensionData.booking_id
      });
      return { success: false, error: bookingError };
    }

    // Create notification for customer
    const { error: notificationError } = await supabase
      .from("booking_extension_notifications")
      .insert({
        extension_id: extensionId,
        recipient_id: extensionData.customer_id,
        notification_type: "request_approved",
        title: "Extension Request Approved",
        message: `Your extension request has been approved. Your new return time is ${new Date(extensionData.requested_dropoff_datetime).toLocaleString()}.`
      });

    if (notificationError) {
      logError("Error creating approval notification", notificationError, {
        function: "approveExtensionRequest",
        extensionId
      });
    }

    logWithContext("Successfully approved extension request", `approveExtensionRequest - extensionId: ${extensionId}, adminId: ${adminId}`);

    return { success: true, error: null };
  } catch (error) {
    logError("Error approving extension request", error as Error);
    return { success: false, error: error };
  }
}

/**
 * Reject an extension request
 */
export async function rejectExtensionRequest(
  extensionId: string,
  adminId: string,
  rejectionReason: string,
  adminNotes?: string
): Promise<{ success: boolean; error: any }> {
  try {
    const supabase = createClient();
    logWithContext("Rejecting extension request", `rejectExtensionRequest - extensionId: ${extensionId}, adminId: ${adminId}`);

    const { data: extension, error: fetchError } = await supabase
      .from("booking_extensions")
      .select("customer_id")
      .eq("id", extensionId)
      .eq("status", "pending")
      .single();

    if (fetchError || !extension) {
      logError("Extension request not found or not pending", fetchError as Error);
      return { success: false, error: fetchError || "Extension request not found" };
    }

    // Update the extension request status
    const { error: updateError } = await supabase
      .from("booking_extensions")
      .update({
        status: "rejected",
        reviewed_by: adminId,
        reviewed_at: new Date().toISOString(),
        rejection_reason: rejectionReason,
        admin_notes: adminNotes || null
      })
      .eq("id", extensionId);

    if (updateError) {
      logError("Failed to update extension request", updateError, {
        function: "rejectExtensionRequest",
        extensionId
      });
      return { success: false, error: updateError };
    }

    // Create notification for customer
    const { error: notificationError } = await supabase
      .from("booking_extension_notifications")
      .insert({
        extension_id: extensionId,
        recipient_id: extension.customer_id,
        notification_type: "request_rejected",
        title: "Extension Request Rejected",
        message: `Your extension request has been rejected. Reason: ${rejectionReason}`
      });

    if (notificationError) {
      logError("Failed to create rejection notification", notificationError, {
        function: "rejectExtensionRequest",
        extensionId
      });
    }

    logWithContext("Successfully rejected extension request", `rejectExtensionRequest - extensionId: ${extensionId}, adminId: ${adminId}`);

    return { success: true, error: null };
  } catch (err) {
    logError("Unexpected error rejecting extension request", err as Error);
    return { success: false, error: err };
  }
}

/**
 * Get extension request statistics
 */
export async function getExtensionRequestStats(): Promise<{
  data: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    expired: number;
  } | null;
  error: any;
}> {
  try {
    const supabase = createClient();
    logWithContext("Fetching extension request statistics", "getExtensionRequestStats");

    const { data, error } = await supabase
      .from("booking_extensions")
      .select("status");

    if (error) {
      logError("Failed to fetch extension request stats", error as Error);
      return { data: null, error };
    }

    const stats = {
      total: data.length,
      pending: data.filter((r: any) => r.status === 'pending').length,
      approved: data.filter((r: any) => r.status === 'approved').length,
      rejected: data.filter((r: any) => r.status === 'rejected').length,
      expired: data.filter((r: any) => r.status === 'expired').length
    };

    return { data: stats, error: null };
  } catch (err) {
    logError("Unexpected error fetching extension request stats", err as Error);
    return { data: null, error: err };
  }
}
