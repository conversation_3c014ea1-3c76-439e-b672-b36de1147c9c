"use client";

import * as React from "react";
import { Check, MapPin, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Command,
  CommandInput,
  CommandList,
  CommandItem,
  CommandEmpty,
  CommandGroup,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
// Client's available locations for both pickup and drop-off
export const ALL_AVAILABLE_LOCATIONS = [
  "Laoag Bus Ter",
  "Laoag Centro",
  "SM/Robinsons",
  "Sarrat/Bacarra Centro",
  "Laoag Airport",
  "Batac/Paoay",
  "Pasuquin",
  "Dingras",
  "Buttong/Nalbo",
  "Airport Road",
  "Vigan/Pagudpud",
  "Sinait/Cabugao/Badoc/Bangui",
] as const;

// Legacy constants for backward compatibility (deprecated - use ALL_AVAILABLE_LOCATIONS)
export const FIXED_PICKUP_LOCATION = "#9 Lubnac, Vintar, Ilocos Norte";
export const DROP_OFF_LOCATIONS = ALL_AVAILABLE_LOCATIONS;

interface LocationDropdownProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function PickupLocationDropdown({
  value,
  onValueChange,
  placeholder = "Select pickup location...",
  className,
  disabled = false,
}: LocationDropdownProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");

  // Filter locations based on search query with performance optimization
  const filteredLocations = React.useMemo(() => {
    if (!searchQuery.trim()) return ALL_AVAILABLE_LOCATIONS;

    const query = searchQuery.toLowerCase();
    return ALL_AVAILABLE_LOCATIONS.filter((location) =>
      location.toLowerCase().includes(query)
    );
  }, [searchQuery]);

  // Clear search when dropdown closes
  React.useEffect(() => {
    if (!open) {
      setSearchQuery("");
    }
  }, [open]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div
          role="combobox"
          aria-expanded={open}
          aria-disabled={disabled}
          className={cn(
            "flex h-12 w-full items-center justify-between rounded-lg border border-gray-300 bg-white px-4 py-3 text-sm shadow-sm transition-all duration-200 hover:border-blue-400 focus:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500/20 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer",
            !value && "text-gray-500",
            disabled && "cursor-not-allowed opacity-50 hover:border-gray-300",
            className
          )}
          onClick={() => !disabled && setOpen(!open)}
        >
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <MapPin className="h-5 w-5 text-green-600 flex-shrink-0" />
            <span className={cn("truncate", !value && "text-gray-500")}>
              {value || placeholder}
            </span>
          </div>
          <Search className="h-4 w-4 text-gray-400 flex-shrink-0" />
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="w-[--radix-popover-trigger-width] p-0 mt-1 bg-white border border-gray-200 shadow-xl rounded-lg z-50"
        sideOffset={4}
      >
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Search pickup locations..."
            value={searchQuery}
            onValueChange={setSearchQuery}
            className="h-11 border-0 border-b border-gray-100 focus:ring-0 px-4"
          />
          <CommandList className="max-h-60 overflow-y-auto">
            {filteredLocations.length === 0 ? (
              <CommandEmpty className="py-6 text-center text-gray-500">
                <MapPin className="h-8 w-8 mx-auto text-gray-300 mb-2" />
                <p>No locations found.</p>
                <p className="text-xs text-gray-400 mt-1">
                  Try searching with different keywords
                </p>
              </CommandEmpty>
            ) : (
              <CommandGroup>
                {filteredLocations.map((location) => (
                  <CommandItem
                    key={location}
                    value={location}
                    onSelect={() => {
                      onValueChange(location === value ? "" : location);
                      setOpen(false);
                    }}
                    className="flex items-center gap-3 px-4 py-3 cursor-pointer hover:bg-green-50 aria-selected:bg-green-50 transition-colors"
                  >
                    <Check
                      className={cn(
                        "h-4 w-4 text-green-600",
                        value === location ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <MapPin className="h-4 w-4 text-gray-400 flex-shrink-0" />
                    <span className="flex-1 truncate">{location}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

interface DropOffLocationDropdownProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function DropOffLocationDropdown({
  value,
  onValueChange,
  placeholder = "Select drop-off location...",
  className,
  disabled = false,
}: DropOffLocationDropdownProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");

  // Filter locations based on search query with performance optimization
  const filteredLocations = React.useMemo(() => {
    if (!searchQuery.trim()) return ALL_AVAILABLE_LOCATIONS;

    const query = searchQuery.toLowerCase();
    return ALL_AVAILABLE_LOCATIONS.filter((location) =>
      location.toLowerCase().includes(query)
    );
  }, [searchQuery]);

  // Clear search when dropdown closes
  React.useEffect(() => {
    if (!open) {
      setSearchQuery("");
    }
  }, [open]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div
          role="combobox"
          aria-expanded={open}
          aria-disabled={disabled}
          className={cn(
            "flex h-12 w-full items-center justify-between rounded-lg border border-gray-300 bg-white px-4 py-3 text-sm shadow-sm transition-all duration-200 hover:border-blue-400 focus:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500/20 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer",
            !value && "text-gray-500",
            disabled && "cursor-not-allowed opacity-50 hover:border-gray-300",
            className
          )}
          onClick={() => !disabled && setOpen(!open)}
        >
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <MapPin className="h-5 w-5 text-orange-600 flex-shrink-0" />
            <span className={cn("truncate", !value && "text-gray-500")}>
              {value || placeholder}
            </span>
          </div>
          <Search className="h-4 w-4 text-gray-400 flex-shrink-0" />
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="w-[--radix-popover-trigger-width] p-0 mt-1 bg-white border border-gray-200 shadow-xl rounded-lg z-50"
        sideOffset={4}
      >
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Search drop-off locations..."
            value={searchQuery}
            onValueChange={setSearchQuery}
            className="h-11 border-0 border-b border-gray-100 focus:ring-0 px-4"
          />
          <CommandList className="max-h-60 overflow-y-auto">
            {filteredLocations.length === 0 ? (
              <CommandEmpty className="py-6 text-center text-gray-500">
                <MapPin className="h-8 w-8 mx-auto text-gray-300 mb-2" />
                <p>No locations found.</p>
                <p className="text-xs text-gray-400 mt-1">
                  Try searching with different keywords
                </p>
              </CommandEmpty>
            ) : (
              <CommandGroup>
                {filteredLocations.map((location) => (
                  <CommandItem
                    key={location}
                    value={location}
                    onSelect={() => {
                      onValueChange(location === value ? "" : location);
                      setOpen(false);
                    }}
                    className="flex items-center gap-3 px-4 py-3 cursor-pointer hover:bg-orange-50 aria-selected:bg-orange-50 transition-colors"
                  >
                    <Check
                      className={cn(
                        "h-4 w-4 text-orange-600",
                        value === location ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <MapPin className="h-4 w-4 text-gray-400 flex-shrink-0" />
                    <span className="flex-1 truncate">{location}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

interface FixedPickupLocationFieldProps {
  className?: string;
  showLabel?: boolean;
}

export function FixedPickupLocationField({
  className,
  showLabel = false,
}: FixedPickupLocationFieldProps) {
  return (
    <div className={cn("space-y-2", className)}>
      {showLabel && (
        <label className="text-xs xs:text-sm font-medium text-gray-700 flex items-center gap-2 mb-2 xs:mb-3 text-center">
          <MapPin className="h-4 w-4 text-green-600 flex-shrink-0" />
          <span className="truncate">Pick-up Location</span>
        </label>
      )}
      <div className="flex min-h-[44px] xs:h-12 w-full items-center gap-2 xs:gap-3 rounded-lg border border-gray-200 bg-green-50 px-2 xs:px-4 py-2 xs:py-3 text-xs xs:text-sm overflow-hidden">
        <MapPin className="h-4 w-4 xs:h-5 xs:w-5 text-green-600 flex-shrink-0" />
        <span className="text-gray-700 font-medium flex-1 min-w-0 break-words leading-tight">
          <span className="block xs:inline">#9 Lubnac, Vintar,</span>
          <span className="block xs:inline xs:ml-1">Ilocos Norte</span>
        </span>
        <div className="text-[10px] xs:text-xs text-green-700 bg-green-100 px-1 xs:px-2 py-1 rounded-full flex-shrink-0 whitespace-nowrap">
          Fixed
        </div>
      </div>
    </div>
  );
}

// New dynamic pickup location field that replaces the fixed one
interface PickupLocationFieldProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  showLabel?: boolean;
  disabled?: boolean;
}

export function PickupLocationField({
  value,
  onValueChange,
  placeholder = "Select pickup location...",
  className,
  showLabel = false,
  disabled = false,
}: PickupLocationFieldProps) {
  return (
    <div className={cn("space-y-2", className)}>
      {showLabel && (
        <label className="text-xs xs:text-sm font-medium text-gray-700 flex items-center gap-2 mb-2 xs:mb-3">
          <MapPin className="h-4 w-4 text-green-600 flex-shrink-0" />
          <span className="truncate">Pick-up Location</span>
        </label>
      )}
      <PickupLocationDropdown
        value={value}
        onValueChange={onValueChange}
        placeholder={placeholder}
        disabled={disabled}
        className="w-full"
      />
    </div>
  );
}

// New dynamic drop-off location field
interface DropOffLocationFieldProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  showLabel?: boolean;
  disabled?: boolean;
}

export function DropOffLocationField({
  value,
  onValueChange,
  placeholder = "Select drop-off location...",
  className,
  showLabel = false,
  disabled = false,
}: DropOffLocationFieldProps) {
  return (
    <div className={cn("space-y-2", className)}>
      {showLabel && (
        <label className="text-xs xs:text-sm font-medium text-gray-700 flex items-center gap-2 mb-2 xs:mb-3">
          <MapPin className="h-4 w-4 text-orange-600 flex-shrink-0" />
          <span className="truncate">Drop-off Location</span>
        </label>
      )}
      <DropOffLocationDropdown
        value={value}
        onValueChange={onValueChange}
        placeholder={placeholder}
        disabled={disabled}
        className="w-full"
      />
    </div>
  );
}
