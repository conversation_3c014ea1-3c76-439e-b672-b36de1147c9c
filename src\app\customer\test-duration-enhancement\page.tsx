"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  formatRentalDuration,
  formatRentalDurationWithDateRange,
  formatDateRange,
  calculateDayOnlyRental
} from "@/utils/booking-date-validation";

export default function TestDurationEnhancementPage() {
  const [pickupDate, setPickupDate] = React.useState("2025-01-15");
  const [dropoffDate, setDropoffDate] = React.useState("2025-01-18");

  // Calculate rental details
  const rental = calculateDayOnlyRental(pickupDate, dropoffDate);
  const oldDurationDisplay = formatRentalDuration(rental.days);
  const newDurationDisplay = formatRentalDurationWithDateRange(pickupDate, dropoffDate);
  const dateRangeOnly = formatDateRange(pickupDate, dropoffDate);

  // Test scenarios
  const testScenarios = [
    {
      name: "Same Day Rental",
      pickup: "2025-01-15",
      dropoff: "2025-01-15"
    },
    {
      name: "Multi-Day Same Month",
      pickup: "2025-01-15", 
      dropoff: "2025-01-18"
    },
    {
      name: "Cross Month",
      pickup: "2025-01-30",
      dropoff: "2025-02-02"
    },
    {
      name: "Cross Year",
      pickup: "2024-12-29",
      dropoff: "2025-01-02"
    },
    {
      name: "Long Duration",
      pickup: "2025-03-01",
      dropoff: "2025-03-15"
    }
  ];

  // Viewport size hook for responsive testing
  const [windowSize, setWindowSize] = React.useState({ width: 0, height: 0 });

  React.useEffect(() => {
    function updateSize() {
      setWindowSize({ width: window.innerWidth, height: window.innerHeight });
    }
    
    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  const getBreakpoint = () => {
    if (windowSize.width >= 1440) return "2xl (≥1440px)";
    if (windowSize.width >= 1280) return "xl (≥1280px)";
    if (windowSize.width >= 1024) return "lg (≥1024px)";
    if (windowSize.width >= 768) return "md (≥768px)";
    if (windowSize.width >= 425) return "sm (≥425px)";
    if (windowSize.width >= 375) return "xs (≥375px)";
    return "Mobile S (<375px)";
  };

  return (
    <div className="max-w-6xl mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Duration Display Enhancement Test
          </CardTitle>
          <p className="text-gray-600">
            Testing enhanced duration display with date ranges across all breakpoints
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Current Viewport</Label>
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  {windowSize.width}px × {windowSize.height}px
                </p>
                <p className="text-xs text-blue-600 font-medium">
                  {getBreakpoint()}
                </p>
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Test Status</Label>
              <div className="p-3 bg-green-50 rounded-lg">
                <p className="text-sm text-green-800">
                  ✅ Enhanced duration display implemented
                </p>
                <p className="text-xs text-green-600">
                  Shows days + date range in parentheses
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interactive Testing */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Interactive Duration Testing
          </CardTitle>
          <p className="text-gray-600">
            Change dates to see dynamic updates
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Date Inputs */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="pickup">Pickup Date</Label>
              <Input
                id="pickup"
                type="date"
                value={pickupDate}
                onChange={(e) => setPickupDate(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="dropoff">Drop-off Date</Label>
              <Input
                id="dropoff"
                type="date"
                value={dropoffDate}
                onChange={(e) => setDropoffDate(e.target.value)}
                className="w-full"
              />
            </div>
          </div>

          {/* Before/After Comparison */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="border-red-200 bg-red-50">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-red-800">
                  Before (Old Format)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Rental Duration</span>
                    <span className="font-medium text-sm">
                      {oldDurationDisplay}
                    </span>
                  </div>
                  <p className="text-xs text-red-700">
                    Only shows number of days, no date context
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="border-green-200 bg-green-50">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-green-800">
                  After (Enhanced Format)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Rental Duration</span>
                    <span className="font-medium text-sm">
                      {newDurationDisplay}
                    </span>
                  </div>
                  <p className="text-xs text-green-700">
                    Shows both duration and actual date range
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Breakdown */}
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="pt-6">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-blue-800">
                    Number of Days:
                  </span>
                  <span className="text-sm text-blue-900">
                    {rental.days} {rental.days === 1 ? 'day' : 'days'}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-blue-800">
                    Date Range:
                  </span>
                  <span className="text-sm text-blue-900">
                    {dateRangeOnly || 'Invalid dates'}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-blue-800">
                    Combined Display:
                  </span>
                  <span className="text-sm text-blue-900 font-semibold">
                    {newDurationDisplay}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>

      {/* Test Scenarios */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Predefined Test Scenarios
          </CardTitle>
          <p className="text-gray-600">
            Click buttons to test various date range formats
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {testScenarios.map((scenario, index) => {
              const scenarioDisplay = formatRentalDurationWithDateRange(
                scenario.pickup,
                scenario.dropoff
              );
              
              return (
                <Card key={index} className="border-gray-200">
                  <CardContent className="pt-4">
                    <div className="space-y-3">
                      <h3 className="font-semibold text-sm text-gray-900">
                        {scenario.name}
                      </h3>
                      <div className="text-xs text-gray-600 space-y-1">
                        <p>Pickup: {scenario.pickup}</p>
                        <p>Dropoff: {scenario.dropoff}</p>
                      </div>
                      <div className="p-2 bg-gray-50 rounded text-sm font-medium text-center">
                        {scenarioDisplay}
                      </div>
                      <Button
                        size="sm"
                        variant="secondary"
                        className="w-full text-xs"
                        onClick={() => {
                          setPickupDate(scenario.pickup);
                          setDropoffDate(scenario.dropoff);
                        }}
                      >
                        Test This Scenario
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Responsive Testing Guide */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Responsive Testing Checklist
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                { name: "Mobile S", size: "320px", status: "✅" },
                { name: "Mobile M", size: "375px", status: "✅" },
                { name: "Mobile L", size: "425px", status: "✅" },
                { name: "Tablet", size: "768px", status: "✅" },
                { name: "Laptop", size: "1024px", status: "✅" },
                { name: "Desktop", size: "1440px+", status: "✅" }
              ].map((breakpoint, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{breakpoint.name}</span>
                    <span className="text-lg">{breakpoint.status}</span>
                  </div>
                  <p className="text-xs text-gray-600">{breakpoint.size}</p>
                </div>
              ))}
            </div>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-800 mb-2">
                Testing Instructions:
              </h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Resize browser window to test different breakpoints</li>
                <li>• Change dates and verify duration updates dynamically</li>
                <li>• Check text wrapping on small screens</li>
                <li>• Ensure no overflow or clipping occurs</li>
                <li>• Test with long date ranges (cross-month/year)</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
