"use client";

import { useState, useEffect } from "react";
import mqtt, { MqttClient } from "mqtt";
import { useToast } from "@/hooks/use-toast";
import { useAdminAuth } from "@/components/auth/admin-auth-context";
import { fetchCurrentGPSLocations, GPSLocation } from "@/lib/gps-data";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Plus, Edit, Trash2, MapPin, Car, Activity } from "lucide-react";
import { createClient } from "@/lib/supabase/client";

interface GPSDevice {
  id: string;
  device_id: string;
  car_id: string | null;
  device_name: string | null;
  device_type: string;
  is_active: boolean;
  mqtt_broker_host: string | null;
  mqtt_broker_port: number | null;
  mqtt_client_id: string | null;
  mqtt_pub_topic: string | null;
  mqtt_sub_topic: string | null;
  mqtt_username: string | null;
  mqtt_password: string | null;
  car_model: string | null;
  car_plate: string | null;
  created_at: string;
  updated_at: string;
}

interface LiveGPSData {
  lat: number;
  lng: number;
  timestamp: number;
}

interface Car {
  id: string;
  model: string;
  plate_number: string;
}

export default function GPSDevicesPage() {
  const { loading: authLoading } = useAdminAuth();
  const { toast } = useToast();
  const [devices, setDevices] = useState<GPSDevice[]>([]);
  const [cars, setCars] = useState<Car[]>([]);
  const [locations, setLocations] = useState<GPSLocation[]>([]);
  const [liveLocations, setLiveLocations] = useState<Record<string, LiveGPSData>>({});
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingDevice, setEditingDevice] = useState<GPSDevice | null>(null);
  const [deviceToDelete, setDeviceToDelete] = useState<GPSDevice | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    device_id: "",
    car_id: "none",
    device_name: "",
    device_type: "esp32",
    is_active: true,
    mqtt_broker_host: "mqtt.waveshare.cloud",
    mqtt_broker_port: 1883,
    mqtt_client_id: "",
    mqtt_pub_topic: "",
    mqtt_sub_topic: "",
    mqtt_username: "",
    mqtt_password: "",
  });

  const supabase = createClient();

  // Load GPS devices and cars
  useEffect(() => {
    loadData();
    loadGPSData(); // Load GPS data for device status display
  }, []);

  useEffect(() => {
    // Temporarily disable frontend MQTT to focus on core data flow
    // The GPS data will still show via database polling in loadGPSData()
    
    // TODO: Re-enable real-time MQTT once ESP32->Consumer flow is stable
    const enableFrontendMQTT = false;
    
    if (!enableFrontendMQTT) {
      console.log("Frontend MQTT disabled - using database polling for GPS data");
      toast({
        title: "GPS Tracking Active", 
        description: "Loading GPS data from database every 30 seconds.",
      });
      return;
    }

    const brokerUrl = "wss://broker.hivemq.com:8000/mqtt";
    const options: mqtt.IClientOptions = {
      clientId: `pathlink_admin_ui_${Math.random().toString(16).substr(2, 8)}`,
      clean: true,
      connectTimeout: 30000,
      reconnectPeriod: 5000,
    };

    const client: MqttClient = mqtt.connect(brokerUrl, options);

    client.on("connect", () => {
      console.log("HiveMQ MQTT client connected");
      toast({
        title: "Real-Time GPS Connection Active",
        description: "Connected to public broker - listening for live GPS updates.",
      });
      client.subscribe("pathlink/gps/#", (err) => {
        if (err) {
          console.error("MQTT Subscription Error:", err);
          toast({
            title: "MQTT Subscription Failed",
            description: "Could not listen for GPS topics.",
            variant: "destructive",
          });
        }
      });
    });

    client.on("message", (topic, payload) => {
      try {
        const topicParts = topic.split('/');
        // Assumes topic format is 'pathlink/gps/DEVICE_ID'
        const deviceId = topicParts[2]; 
        
        const message = JSON.parse(payload.toString());
        
        if (deviceId && message.lat && message.lng) {
          setLiveLocations(prev => ({
            ...prev,
            [deviceId]: {
              lat: message.lat,
              lng: message.lng,
              timestamp: Date.now(),
            },
          }));
        }
      } catch (e) {
        console.error("Failed to parse MQTT message:", e);
      }
    });

    client.on("error", (err) => {
      console.error("MQTT Client Error:", err);
      toast({
        title: "Real-Time Connection Error",
        description: "Check console for details.",
        variant: "destructive",
      });
    });

    // Cleanup on component unmount
    return () => {
      if (client) {
        client.end();
        console.log("MQTT client disconnected");
      }
    };
  }, [toast]);

  // Load GPS locations for status checking
  const loadGPSData = async () => {
    try {
      const gpsLocations = await fetchCurrentGPSLocations();
      setLocations(gpsLocations);
    } catch (error) {
      console.error('Failed to load GPS locations:', error);
    }
  };

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load GPS devices with car info
      const { data: devicesData, error: devicesError } = await supabase
        .rpc('get_gps_devices_with_mqtt_config');
      
      if (devicesError) throw devicesError;
      
      // Load available cars
      const { data: carsData, error: carsError } = await supabase
        .from('cars')
        .select('id, model, plate_number')
        .eq('is_archived', false)
        .order('model');
      
      if (carsError) throw carsError;
      
      setDevices(devicesData || []);
      setCars(carsData || []);
    } catch (error) {
      console.error('Failed to load data:', error);
      toast({
        title: "Error",
        description: "Failed to load GPS devices",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (editingDevice) {
        // Update existing device
        const { error } = await supabase
          .from('gps_device_mapping')
          .update({
            car_id: formData.car_id === 'none' ? null : formData.car_id || null,
            device_name: formData.device_name,
            device_type: formData.device_type,
            is_active: formData.is_active,
            mqtt_broker_host: formData.mqtt_broker_host,
            mqtt_broker_port: formData.mqtt_broker_port,
            mqtt_client_id: formData.mqtt_client_id,
            mqtt_pub_topic: formData.mqtt_pub_topic,
            mqtt_sub_topic: formData.mqtt_sub_topic,
            mqtt_username: formData.mqtt_username,
            mqtt_password: formData.mqtt_password,
            updated_at: new Date().toISOString(),
          })
          .eq('id', editingDevice.id);
          
        if (error) throw error;
        
        toast({
          title: "Success",
          description: "GPS device updated successfully",
        });
      } else {
        // Create new device
        const { error } = await supabase
          .from('gps_device_mapping')
          .insert({
            device_id: formData.device_id,
            car_id: formData.car_id === 'none' ? null : formData.car_id || null,
            device_name: formData.device_name,
            device_type: formData.device_type,
            is_active: formData.is_active,
            mqtt_broker_host: formData.mqtt_broker_host,
            mqtt_broker_port: formData.mqtt_broker_port,
            mqtt_client_id: formData.mqtt_client_id,
            mqtt_pub_topic: formData.mqtt_pub_topic,
            mqtt_sub_topic: formData.mqtt_sub_topic,
            mqtt_username: formData.mqtt_username,
            mqtt_password: formData.mqtt_password,
          });
          
        if (error) throw error;
        
        toast({
          title: "Success",
          description: "GPS device created successfully",
        });
      }
      
      setIsDialogOpen(false);
      setEditingDevice(null);
      resetForm();
      loadData();
    } catch (error: any) {
      console.error('Failed to save device:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to save GPS device",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (device: GPSDevice) => {
    setEditingDevice(device);
    setFormData({
      device_id: device.device_id,
      car_id: device.car_id || "none",
      device_name: device.device_name || "",
      device_type: device.device_type,
      is_active: device.is_active,
      mqtt_broker_host: device.mqtt_broker_host || "mqtt.waveshare.cloud",
      mqtt_broker_port: device.mqtt_broker_port || 1883,
      mqtt_client_id: device.mqtt_client_id || "",
      mqtt_pub_topic: device.mqtt_pub_topic || "",
      mqtt_sub_topic: device.mqtt_sub_topic || "",
      mqtt_username: device.mqtt_username || "",
      mqtt_password: device.mqtt_password || "",
    });
    setIsDialogOpen(true);
  };

  const handleDeleteClick = (device: GPSDevice) => {
    setDeviceToDelete(device);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!deviceToDelete) return;
    
    try {
      const { error } = await supabase
        .from('gps_device_mapping')
        .delete()
        .eq('id', deviceToDelete.id);
        
      if (error) throw error;
      
      toast({
        title: "Success",
        description: "GPS device deleted successfully",
      });
      
      setIsDeleteDialogOpen(false);
      setDeviceToDelete(null);
      loadData();
    } catch (error: any) {
      console.error('Failed to delete device:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete GPS device",
        variant: "destructive",
      });
    }
  };

  const handleCancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setDeviceToDelete(null);
  };

  const resetForm = () => {
    setFormData({
      device_id: "",
      car_id: "none",
      device_name: "",
      device_type: "esp32",
      is_active: true,
      mqtt_broker_host: "mqtt.waveshare.cloud",
      mqtt_broker_port: 1883,
      mqtt_client_id: "",
      mqtt_pub_topic: "",
      mqtt_sub_topic: "",
      mqtt_username: "",
      mqtt_password: "",
    });
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setEditingDevice(null);
    resetForm();
  };

  const handleDialogOpenChange = (open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      setEditingDevice(null);
      resetForm();
    }
  };

  // Wait for auth loading to complete before showing page loading
  if (authLoading) {
    return (
      <div className="space-y-4 p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">GPS Device Management</h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">Loading...</div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="space-y-4 p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">GPS Device Management</h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">Loading GPS devices...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">GPS Device Management</h1>
          <p className="text-muted-foreground">
            Manage GPS device mappings to car registrations
          </p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
          <DialogTrigger asChild>
            <Button onClick={() => setIsDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Add GPS Device
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>
                  {editingDevice ? "Edit GPS Device" : "Add GPS Device"}
                </DialogTitle>
                <Badge variant="outline">
                  {devices.length} devices
                </Badge>
                <DialogDescription>
                  {editingDevice 
                    ? "Update the GPS device mapping"
                    : "Map a GPS device ID to a car in your fleet"
                  }
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="device_id">Device ID</Label>
                  <Input
                    id="device_id"
                    value={formData.device_id}
                    onChange={(e) => setFormData(prev => ({ ...prev, device_id: e.target.value }))}
                    placeholder="e.g., lilygo-esp32-01"
                    disabled={!!editingDevice}
                    required
                  />
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="device_name">Device Name</Label>
                  <Input
                    id="device_name"
                    value={formData.device_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, device_name: e.target.value }))}
                    placeholder="e.g., LilyGO T-Call A7670E GPS Tracker"
                  />
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="device_type">Device Type</Label>
                  <Select
                    value={formData.device_type}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, device_type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="esp32">ESP32</SelectItem>
                      <SelectItem value="lilygo">LilyGO</SelectItem>
                      <SelectItem value="generic">Generic</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid gap-2">
                  <Label htmlFor="car_id">Assigned Car</Label>
                  <Select
                    value={formData.car_id}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, car_id: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a car (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No car assigned</SelectItem>
                      {cars.map((car) => (
                        <SelectItem key={car.id} value={car.id}>
                          {car.model} - {car.plate_number}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* MQTT Configuration Section */}
                <div className="border-t pt-4 mt-4">
                  <h4 className="font-medium mb-3 text-sm text-muted-foreground">MQTT Configuration</h4>
                  
                  <div className="grid gap-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="mqtt_broker_host">MQTT Broker Host</Label>
                        <Input
                          id="mqtt_broker_host"
                          value={formData.mqtt_broker_host}
                          onChange={(e) => setFormData(prev => ({ ...prev, mqtt_broker_host: e.target.value }))}
                          placeholder="mqtt.waveshare.cloud"
                        />
                      </div>
                      
                      <div className="grid gap-2">
                        <Label htmlFor="mqtt_broker_port">Port</Label>
                        <Input
                          id="mqtt_broker_port"
                          type="number"
                          value={formData.mqtt_broker_port}
                          onChange={(e) => setFormData(prev => ({ ...prev, mqtt_broker_port: parseInt(e.target.value) || 1883 }))}
                          placeholder="1883"
                          min="1"
                          max="65535"
                        />
                      </div>
                    </div>
                    
                    <div className="grid gap-2">
                      <Label htmlFor="mqtt_client_id">Client ID</Label>
                      <Input
                        id="mqtt_client_id"
                        value={formData.mqtt_client_id}
                        onChange={(e) => setFormData(prev => ({ ...prev, mqtt_client_id: e.target.value }))}
                        placeholder="e.g., b4922afa"
                      />
                    </div>
                    
                    <div className="grid gap-2">
                      <Label htmlFor="mqtt_pub_topic">Publish Topic</Label>
                      <Input
                        id="mqtt_pub_topic"
                        value={formData.mqtt_pub_topic}
                        onChange={(e) => setFormData(prev => ({ ...prev, mqtt_pub_topic: e.target.value }))}
                        placeholder="e.g., Pub/1149/37/b4922afa"
                      />
                    </div>
                    
                    <div className="grid gap-2">
                      <Label htmlFor="mqtt_sub_topic">Subscribe Topic</Label>
                      <Input
                        id="mqtt_sub_topic"
                        value={formData.mqtt_sub_topic}
                        onChange={(e) => setFormData(prev => ({ ...prev, mqtt_sub_topic: e.target.value }))}
                        placeholder="e.g., Sub/1149/37/b4922afa"
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="mqtt_username">Username (Optional)</Label>
                        <Input
                          id="mqtt_username"
                          value={formData.mqtt_username}
                          onChange={(e) => setFormData(prev => ({ ...prev, mqtt_username: e.target.value }))}
                          placeholder="MQTT username"
                        />
                      </div>
                      
                      <div className="grid gap-2">
                        <Label htmlFor="mqtt_password">Password (Optional)</Label>
                        <Input
                          id="mqtt_password"
                          type="password"
                          value={formData.mqtt_password}
                          onChange={(e) => setFormData(prev => ({ ...prev, mqtt_password: e.target.value }))}
                          placeholder="MQTT password"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="is_active"
                    checked={formData.is_active}
                    onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                    className="w-4 h-4"
                  />
                  <Label htmlFor="is_active">Active</Label>
                </div>
              </div>
              
              <DialogFooter>
                <Button type="button" variant="secondary" onClick={handleDialogClose}>
                  Cancel
                </Button>
                <Button type="submit">
                  {editingDevice ? "Update" : "Create"} Device
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <MapPin className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Devices</p>
                <p className="text-2xl font-bold">{devices.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Activity className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Active</p>
                <p className="text-2xl font-bold">
                  {devices.filter(d => d.is_active).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Car className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Mapped to Cars</p>
                <p className="text-2xl font-bold">
                  {devices.filter(d => d.car_id).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Devices Table */}
      <Card>
        <CardHeader>
          <CardTitle>GPS Devices</CardTitle>
        </CardHeader>
        <CardContent>
          {devices.length === 0 ? (
            <div className="text-center py-8">
              <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No GPS devices found</h3>
              <p className="text-muted-foreground mb-4">
                Add your first GPS device to start tracking your fleet
              </p>
              <Button onClick={() => setIsDialogOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add GPS Device
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Device ID</TableHead>
                    <TableHead>Device Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Assigned Car</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {devices.map((device) => (
                    <TableRow key={device.id}>
                      <TableCell className="font-medium">
                        {device.device_id}
                        {(() => {
                          const dbLocation = locations.find(loc => loc.carId === device.car_id);
                          // FIXED: Use same logic as status badge - both 'active' and 'idle' show as recent
                          const isRecent = dbLocation && 
                                          (dbLocation.status === 'active' || dbLocation.status === 'idle') && 
                                          (Date.now() - new Date(dbLocation.timestamp).getTime() < 300000); // 5-minute threshold
                          
                          return isRecent && (
                            <div className="flex items-center gap-2 mt-1">
                              <span className="relative flex h-2 w-2">
                                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                                <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {dbLocation.latitude.toFixed(4)}, {dbLocation.longitude.toFixed(4)}
                                {dbLocation.status === 'idle' ? ' (Idle)' : ''}
                              </span>
                            </div>
                          );
                        })()}
                      </TableCell>
                      <TableCell>
                        {device.device_name || (
                          <span className="text-muted-foreground">No name</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{device.device_type}</Badge>
                      </TableCell>
                      <TableCell>
                        {device.car_model && device.car_plate ? (
                          <div>
                            <div className="font-medium">{device.car_model}</div>
                            <div className="text-sm text-muted-foreground">
                              {device.car_plate}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Not assigned</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {(() => {
                          // Use database GPS data instead of frontend MQTT data
                          const dbLocation = locations.find(loc => loc.carId === device.car_id);
                          // FIXED: Consider both 'active' and 'idle' as online since both indicate connected device
                          // 'active' = moving (speed > 5 km/h), 'idle' = stationary but connected, 'offline' = no recent data
                          const isLive = dbLocation && 
                                        (dbLocation.status === 'active' || dbLocation.status === 'idle') && 
                                        (Date.now() - new Date(dbLocation.timestamp).getTime() < 300000); // 5-minute threshold

                          if (!device.is_active) {
                            return <Badge variant="secondary">Inactive</Badge>;
                          } else if (isLive) {
                            return (
                              <Badge variant="default" className="bg-green-600 text-white">
                                {dbLocation?.status === 'active' ? 'Online (Moving)' : 'Online (Idle)'}
                              </Badge>
                            );
                          } else {
                            return <Badge variant="destructive" className="bg-red-500 text-white border-red-500">Offline</Badge>;
                          }
                        })()}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="tertiary"
                            size="sm"
                            onClick={() => handleEdit(device)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="tertiary"
                            size="sm"
                            onClick={() => handleDeleteClick(device)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete GPS Device</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the GPS device "{deviceToDelete?.device_id}"?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button type="button" variant="secondary" onClick={handleCancelDelete}>
              Cancel
            </Button>
            <Button type="button" variant="destructive" onClick={handleConfirmDelete}>
              Delete Device
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
