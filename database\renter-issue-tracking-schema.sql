-- ============================================
-- Renter Issue Tracking Database Schema
-- ============================================
-- This schema adds tables for tracking renter issues and behavioral monitoring

-- ============================================
-- 1. RENTER_STATUS TABLE
-- ============================================
-- Stores status/taglines for renters (admin-managed)
CREATE TABLE public.renter_status (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  customer_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  status_tagline TEXT NOT NULL, -- e.g., "Prone to Overdue", "Reliable Renter"
  created_by UUID NOT NULL REFERENCES public.profiles(id), -- Admin who set the status
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure only one active status per customer
  UNIQUE(customer_id)
);

-- Enable RLS for renter_status
ALTER TABLE public.renter_status ENABLE ROW LEVEL SECURITY;

-- RLS Policies for renter_status
CREATE POLICY "Only admins can view renter status" 
  ON public.renter_status FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Only admins can manage renter status" 
  ON public.renter_status FOR ALL 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- ============================================
-- 2. ISSUE_CATEGORIES TABLE
-- ============================================
-- Predefined issue categories that can be applied to renters
CREATE TABLE public.issue_categories (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE, -- e.g., "Payment Issues", "Late Returns"
  description TEXT,
  color TEXT DEFAULT '#6B7280', -- Hex color for UI badges
  created_at TIMESTAMPTZ DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE
);

-- Enable RLS for issue_categories
ALTER TABLE public.issue_categories ENABLE ROW LEVEL SECURITY;

-- RLS Policies for issue_categories
CREATE POLICY "Issue categories are viewable by admins" 
  ON public.issue_categories FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Only admins can manage issue categories" 
  ON public.issue_categories FOR ALL 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- ============================================
-- 3. RENTER_ISSUES TABLE
-- ============================================
-- Stores issue tracking history for renters
CREATE TABLE public.renter_issues (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  customer_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  category_id UUID NOT NULL REFERENCES public.issue_categories(id) ON DELETE CASCADE,
  booking_id UUID REFERENCES public.bookings(id) ON DELETE SET NULL, -- Optional: link to specific booking
  description TEXT NOT NULL, -- Admin notes about the issue
  severity TEXT DEFAULT 'Medium' CHECK (severity IN ('Low', 'Medium', 'High', 'Critical')),
  resolved BOOLEAN DEFAULT FALSE,
  resolved_at TIMESTAMPTZ,
  resolved_by UUID REFERENCES public.profiles(id), -- Admin who resolved it
  resolution_notes TEXT,
  created_by UUID NOT NULL REFERENCES public.profiles(id), -- Admin who logged the issue
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS for renter_issues
ALTER TABLE public.renter_issues ENABLE ROW LEVEL SECURITY;

-- RLS Policies for renter_issues
CREATE POLICY "Only admins can view renter issues" 
  ON public.renter_issues FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Only admins can manage renter issues" 
  ON public.renter_issues FOR ALL 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- ============================================
-- 4. RENTER_CATEGORY_TAGS TABLE
-- ============================================
-- Junction table for many-to-many relationship between renters and issue categories
CREATE TABLE public.renter_category_tags (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  customer_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  category_id UUID NOT NULL REFERENCES public.issue_categories(id) ON DELETE CASCADE,
  tagged_by UUID NOT NULL REFERENCES public.profiles(id), -- Admin who added the tag
  tagged_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Prevent duplicate tags
  UNIQUE(customer_id, category_id)
);

-- Enable RLS for renter_category_tags
ALTER TABLE public.renter_category_tags ENABLE ROW LEVEL SECURITY;

-- RLS Policies for renter_category_tags
CREATE POLICY "Only admins can view renter category tags" 
  ON public.renter_category_tags FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Only admins can manage renter category tags" 
  ON public.renter_category_tags FOR ALL 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- ============================================
-- 5. INDEXES FOR PERFORMANCE
-- ============================================
CREATE INDEX idx_renter_status_customer_id ON public.renter_status(customer_id);
CREATE INDEX idx_renter_issues_customer_id ON public.renter_issues(customer_id);
CREATE INDEX idx_renter_issues_category_id ON public.renter_issues(category_id);
CREATE INDEX idx_renter_issues_booking_id ON public.renter_issues(booking_id);
CREATE INDEX idx_renter_issues_created_at ON public.renter_issues(created_at DESC);
CREATE INDEX idx_renter_category_tags_customer_id ON public.renter_category_tags(customer_id);
CREATE INDEX idx_renter_category_tags_category_id ON public.renter_category_tags(category_id);

-- ============================================
-- 6. INSERT DEFAULT ISSUE CATEGORIES
-- ============================================
INSERT INTO public.issue_categories (name, description, color) VALUES 
  ('Payment Issues', 'Problems with payment processing or delays', '#EF4444'),
  ('Late Returns', 'Vehicle returned after scheduled time', '#F59E0B'),
  ('Vehicle Damage', 'Damage caused to rental vehicle', '#DC2626'),
  ('Communication Problems', 'Poor communication or unresponsive behavior', '#7C3AED'),
  ('No-Show', 'Failed to pick up reserved vehicle', '#6B7280'),
  ('Cancellation Issues', 'Frequent cancellations or last-minute changes', '#F97316'),
  ('Documentation Issues', 'Problems with required documents or verification', '#059669'),
  ('Driving Violations', 'Traffic violations or reckless driving reported', '#BE185D'),
  ('Contract Violations', 'Breach of rental agreement terms', '#B91C1C'),
  ('Positive Feedback', 'Exemplary renter behavior or feedback', '#10B981')
ON CONFLICT (name) DO NOTHING;

-- ============================================
-- 7. TRIGGER FOR UPDATING TIMESTAMPS
-- ============================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers
CREATE TRIGGER update_renter_status_updated_at 
  BEFORE UPDATE ON public.renter_status 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_renter_issues_updated_at 
  BEFORE UPDATE ON public.renter_issues 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- 8. UTILITY FUNCTIONS
-- ============================================

-- Function to get renter behavior summary
CREATE OR REPLACE FUNCTION get_renter_behavior_summary(renter_id UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_bookings', COUNT(b.id),
    'completed_bookings', COUNT(CASE WHEN b.status = 'Completed' THEN 1 END),
    'cancelled_bookings', COUNT(CASE WHEN b.status = 'Cancelled' THEN 1 END),
    'overdue_bookings', COUNT(CASE WHEN b.status = 'Active' AND b.dropoff_datetime < NOW() THEN 1 END),
    'total_issues', COALESCE(issue_count.total, 0),
    'unresolved_issues', COALESCE(issue_count.unresolved, 0),
    'issue_categories', COALESCE(category_counts.categories, '[]'::json)
  ) INTO result
  FROM public.profiles p
  LEFT JOIN public.bookings b ON p.id = b.customer_id
  LEFT JOIN (
    SELECT 
      customer_id,
      COUNT(*) as total,
      COUNT(CASE WHEN NOT resolved THEN 1 END) as unresolved
    FROM public.renter_issues 
    WHERE customer_id = renter_id
    GROUP BY customer_id
  ) issue_count ON p.id = issue_count.customer_id
  LEFT JOIN (
    SELECT 
      ri.customer_id,
      json_agg(
        json_build_object(
          'category', ic.name,
          'count', category_count.count,
          'color', ic.color
        )
      ) as categories
    FROM public.renter_issues ri
    JOIN public.issue_categories ic ON ri.category_id = ic.id
    JOIN (
      SELECT category_id, customer_id, COUNT(*) as count
      FROM public.renter_issues
      WHERE customer_id = renter_id
      GROUP BY category_id, customer_id
    ) category_count ON ri.category_id = category_count.category_id 
      AND ri.customer_id = category_count.customer_id
    WHERE ri.customer_id = renter_id
    GROUP BY ri.customer_id
  ) category_counts ON p.id = category_counts.customer_id
  WHERE p.id = renter_id
  GROUP BY p.id, issue_count.total, issue_count.unresolved, category_counts.categories;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
