// Test script to validate AI agent functionality
// Run: node scripts/test-ai-agent.js

const testQueries = {
  admin: [
    "What cars are currently available?",
    "Show me recent booking statistics",
    "How do I verify customer documents?",
    "What are the admin procedures for handling customer complaints?",
    "Tell me about the current fleet status"
  ],
  customer: [
    "What cars do you have available for rent?",
    "How much does it cost to rent a sedan?",
    "What documents do I need to provide?",
    "What is your cancellation policy?",
    "How do I contact support if I have issues?"
  ]
};

async function testChatbot(userType, query) {
  try {
    const endpoint = userType === 'admin' ? '/api/chat/admin' : '/api/chat/customer';
    
    const response = await fetch(`http://localhost:3000${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'auth-token=test' // You'll need to replace with actual auth
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: query }
        ],
        sessionId: `test-${Date.now()}`
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const responseText = await response.text();
    return {
      success: true,
      query,
      response: responseText.substring(0, 200) + '...',
      userType
    };
  } catch (error) {
    return {
      success: false,
      query,
      error: error.message,
      userType
    };
  }
}

async function runTests() {
  console.log('🤖 PathLink AI Agent Test Suite\n');
  console.log('Testing chatbot responses with website knowledge integration...\n');

  // Test admin queries
  console.log('📊 Testing Admin Chatbot:');
  for (const query of testQueries.admin) {
    const result = await testChatbot('admin', query);
    if (result.success) {
      console.log(`✅ "${query}"`);
      console.log(`   Response: ${result.response}\n`);
    } else {
      console.log(`❌ "${query}"`);
      console.log(`   Error: ${result.error}\n`);
    }
  }

  // Test customer queries
  console.log('👤 Testing Customer Chatbot:');
  for (const query of testQueries.customer) {
    const result = await testChatbot('customer', query);
    if (result.success) {
      console.log(`✅ "${query}"`);
      console.log(`   Response: ${result.response}\n`);
    } else {
      console.log(`❌ "${query}"`);
      console.log(`   Error: ${result.error}\n`);
    }
  }

  console.log('🎯 Test suite completed!');
  console.log('\nNext steps:');
  console.log('1. Populate the knowledge base via Admin > AI Knowledge');
  console.log('2. Test the chatbots in the actual UI');
  console.log('3. Verify responses include live website data');
}

// Run tests if called directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testChatbot, runTests };
