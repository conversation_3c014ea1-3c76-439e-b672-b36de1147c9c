"use client";

import * as React from 'react';
import { Command, CommandInput, CommandList, CommandItem, CommandEmpty, CommandGroup } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Check, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';
import { searchLocations, type Location } from '@/lib/psgc';
import { useDebounce } from 'use-debounce';

interface SearchableLocationInputProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export function SearchableLocationInput({ value, onValueChange, placeholder = 'Select location...', className }: SearchableLocationInputProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [locations, setLocations] = React.useState<Location[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [debouncedSearchQuery] = useDebounce(searchQuery, 300);

  React.useEffect(() => {
    if (debouncedSearchQuery.length > 1) {
      setLoading(true);
      searchLocations(debouncedSearchQuery).then(results => {
        setLocations(results);
        setLoading(false);
      });
    } else {
      setLocations([]);
    }
  }, [debouncedSearchQuery]);

  const selectedLocationName = locations.find(l => l.name === value)?.name || value;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div
          role="combobox"
          aria-expanded={open}
          className={cn(
            'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer',
            className
          )}
          onClick={() => setOpen(!open)}
        >
          <div className="flex items-center">
            <MapPin className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <span className={cn(!value && 'text-muted-foreground')}>
              {value ? selectedLocationName : placeholder}
            </span>
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[--radix-popover-trigger-width] p-0 mt-1 bg-white border border-gray-200 shadow-lg rounded-md">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Search for a city or municipality..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            {loading && <CommandEmpty>Loading...</CommandEmpty>}
            {!loading && debouncedSearchQuery.length <= 1 && (
              <CommandEmpty>Start typing to search cities or municipalities…</CommandEmpty>
            )}
            {!loading && !locations.length && debouncedSearchQuery.length > 1 && (
              <CommandEmpty>No location found.</CommandEmpty>
            )}
            <CommandGroup>
              {locations.map(location => (
                <CommandItem
                  key={location.code}
                  value={location.name}
                  onSelect={currentValue => {
                    onValueChange(currentValue === value ? '' : currentValue);
                    setOpen(false);
                  }}
                >
                  <Check className={cn('mr-2 h-4 w-4', value === location.name ? 'opacity-100' : 'opacity-0')} />
                  {location.name}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
