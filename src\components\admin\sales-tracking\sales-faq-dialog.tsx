"use client";

import * as React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { HelpCircle, X } from "lucide-react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export function SalesFaqDialog() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="secondary" size="sm" className="gap-2">
          <HelpCircle className="h-4 w-4" />
          FAQ
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-start justify-between">
          <div>
            <DialogTitle>Sales Tracking FAQ</DialogTitle>
            <DialogDescription>
              Learn how sales metrics are calculated and what each value represents.
            </DialogDescription>
          </div>
          <DialogClose className="rounded-full h-8 w-8 flex items-center justify-center hover:bg-gray-100">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogHeader>
        
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="total-revenue">
            <AccordionTrigger>How is Total Revenue calculated?</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                <p><strong>Total Revenue</strong> includes only bookings with "Paid" payment status.</p>
                <p>Formula: Sum of all paid booking amounts within the selected date range</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Base rental rate</li>
                  <li>Add-ons (insurance, GPS, etc.)</li>
                  <li>Taxes and fees</li>
                  <li>Minus any discounts applied</li>
                </ul>
                <p className="text-sm text-muted-foreground">
                  Pending, refunded, or cancelled bookings are excluded from revenue calculations.
                </p>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="total-bookings">
            <AccordionTrigger>What counts as Total Bookings?</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                <p><strong>Total Bookings</strong> counts all bookings regardless of payment status.</p>
                <p>This includes:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Paid bookings</li>
                  <li>Pending payment bookings</li>
                  <li>Partially paid bookings</li>
                  <li>Cancelled/refunded bookings</li>
                </ul>
                <p className="text-sm text-muted-foreground">
                  This metric helps track overall booking volume and conversion rates.
                </p>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="average-order">
            <AccordionTrigger>How is Average Order Value calculated?</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                <p><strong>Average Order Value</strong> is calculated using only paid bookings.</p>
                <p>Formula: Total Revenue ÷ Number of Paid Bookings</p>
                <p className="text-sm text-muted-foreground">
                  This provides a more accurate representation of actual revenue per transaction, 
                  excluding unpaid or cancelled bookings that don't contribute to revenue.
                </p>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="fleet-utilization">
            <AccordionTrigger>How is Fleet Utilization calculated?</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                <p><strong>Fleet Utilization</strong> measures how efficiently vehicles are being used.</p>
                <p>Formula: (Total Rental Days ÷ (Fleet Size × Days in Period)) × 100</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li><strong>Total Rental Days:</strong> Sum of all rental periods for paid bookings</li>
                  <li><strong>Fleet Size:</strong> Total number of active vehicles</li>
                  <li><strong>Days in Period:</strong> Number of days in selected date range</li>
                </ul>
                <p className="text-sm text-muted-foreground">
                  Higher percentages indicate better fleet efficiency and revenue optimization.
                </p>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="payment-status">
            <AccordionTrigger>What do the Payment Statuses mean?</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-3">
                <div>
                  <p><strong className="text-green-600">Paid:</strong> Payment has been verified and confirmed</p>
                  <p className="text-sm text-muted-foreground">Includes historical statuses: "Approved", "Verified"</p>
                </div>
                <div>
                  <p><strong className="text-yellow-600">Pending:</strong> Payment submitted but awaiting verification</p>
                  <p className="text-sm text-muted-foreground">Includes: "Pending Verification", "Pending"</p>
                </div>
                <div>
                  <p><strong className="text-red-600">Refunded:</strong> Payment was returned to customer</p>
                  <p className="text-sm text-muted-foreground">Usually due to cancellations or booking issues</p>
                </div>
                <div>
                  <p><strong className="text-blue-600">Partial:</strong> Only part of the total amount has been paid</p>
                  <p className="text-sm text-muted-foreground">May require follow-up for remaining balance</p>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="date-filtering">
            <AccordionTrigger>How does Date Filtering work?</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                <p>Date filtering is based on the <strong>pickup date</strong> of bookings, not the booking creation date.</p>
                <p>This means:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>A booking made in July for an August pickup will appear in August data</li>
                  <li>Revenue is attributed to the period when the service was actually provided</li>
                  <li>Future bookings will appear in future date ranges</li>
                </ul>
                <p className="text-sm text-muted-foreground">
                  This approach provides more accurate operational and financial reporting.
                </p>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="data-refresh">
            <AccordionTrigger>How often is data updated?</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                <p>Sales data is updated in real-time when:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Payment statuses change</li>
                  <li>New bookings are created</li>
                  <li>Bookings are cancelled or modified</li>
                  <li>Date range or filters are adjusted</li>
                </ul>
                <p className="text-sm text-muted-foreground">
                  The dashboard automatically refreshes when you change filters or date ranges.
                </p>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </DialogContent>
    </Dialog>
  );
}
