/**
 * Comprehensive test for the admin session persistence fix
 * 
 * This test verifies the complete solution including:
 * - Improved cookie filtering
 * - AdminProtection wait period
 * - Enhanced middleware handling
 * - Session restoration timing
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { NextRequest } from 'next/server'
import { middleware } from '../../middleware'
import { AdminProtection } from '@/components/auth/admin-protection'
import { AdminAuthProvider } from '@/components/auth/admin-auth-context'

// Mock Supabase
const mockSupabaseClient = {
  auth: {
    getUser: vi.fn(),
    getSession: vi.fn(),
    onAuthStateChange: vi.fn(() => ({
      unsubscribe: vi.fn()
    })),
    signInWithPassword: vi.fn(),
    signOut: vi.fn(),
  },
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn(),
      })),
    })),
  })),
}

vi.mock('@supabase/ssr', () => ({
  createServerClient: vi.fn(() => mockSupabaseClient),
  createBrowserClient: vi.fn(() => mockSupabaseClient),
}))

vi.mock('@/lib/supabase/server', () => ({
  createContextClient: vi.fn(() => mockSupabaseClient),
}))

// Mock router
const mockRouter = {
  replace: vi.fn(),
  push: vi.fn(),
}

vi.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
  useSearchParams: () => new URLSearchParams(),
}))

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
})

describe('Admin Session Persistence Comprehensive Fix', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('Enhanced Middleware Cookie Handling', () => {
    it('should handle admin routes with improved session validation', async () => {
      // Mock successful admin session
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin-123', email: '<EMAIL>' } },
        error: null
      })

      const request = new NextRequest('http://localhost:3000/admin/dashboard')
      const response = await middleware(request)

      expect(response.status).toBe(200)
      expect(mockSupabaseClient.auth.getUser).toHaveBeenCalled()
    })

    it('should allow admin routes even when session validation fails', async () => {
      // Mock session validation failure
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: new Error('No session found')
      })

      const request = new NextRequest('http://localhost:3000/admin/cars')
      const response = await middleware(request)

      // Should still allow the request through
      expect(response.status).toBe(200)
      expect(response.headers.get('location')).toBeNull()
    })
  })

  describe('AdminProtection Wait Period', () => {
    const TestComponent = () => <div>Admin Content</div>

    it('should show loading state during wait period', async () => {
      // Mock loading state
      mockSupabaseClient.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null
      })

      render(
        <AdminAuthProvider>
          <AdminProtection>
            <TestComponent />
          </AdminProtection>
        </AdminAuthProvider>
      )

      // Should show loading state initially
      expect(screen.getByText(/Restoring session/)).toBeInTheDocument()
    })

    it('should wait for session restoration before redirecting', async () => {
      // Mock no session initially
      mockSupabaseClient.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null
      })

      render(
        <AdminAuthProvider>
          <AdminProtection>
            <TestComponent />
          </AdminProtection>
        </AdminAuthProvider>
      )

      // Should not redirect immediately
      expect(mockRouter.replace).not.toHaveBeenCalled()

      // Fast-forward past the wait period
      vi.advanceTimersByTime(1600)

      // Now should redirect after wait period
      await waitFor(() => {
        expect(mockRouter.replace).toHaveBeenCalledWith('/admin-auth')
      })
    })

    it('should render content when admin user is found', async () => {
      // Mock successful admin session
      mockSupabaseClient.auth.getSession.mockResolvedValue({
        data: { 
          session: { 
            user: { id: 'admin-123', email: '<EMAIL>' },
            access_token: 'token',
            refresh_token: 'refresh'
          } 
        },
        error: null
      })

      render(
        <AdminAuthProvider>
          <AdminProtection>
            <TestComponent />
          </AdminProtection>
        </AdminAuthProvider>
      )

      // Fast-forward past the wait period
      vi.advanceTimersByTime(1600)

      // Should render admin content
      await waitFor(() => {
        expect(screen.getByText('Admin Content')).toBeInTheDocument()
      })

      // Should not redirect
      expect(mockRouter.replace).not.toHaveBeenCalled()
    })
  })

  describe('Session Restoration Timing', () => {
    it('should handle the page refresh scenario correctly', async () => {
      // Simulate page refresh where localStorage has session but server doesn't yet
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key.includes('sb-admin-auth-token')) {
          return JSON.stringify({
            access_token: 'stored-token',
            refresh_token: 'stored-refresh',
            user: { id: 'admin-123', email: '<EMAIL>' }
          })
        }
        return null
      })

      // Initially no server session
      mockSupabaseClient.auth.getSession.mockResolvedValueOnce({
        data: { session: null },
        error: null
      })

      // Then session becomes available
      mockSupabaseClient.auth.getSession.mockResolvedValue({
        data: { 
          session: { 
            user: { id: 'admin-123', email: '<EMAIL>' },
            access_token: 'token',
            refresh_token: 'refresh'
          } 
        },
        error: null
      })

      const TestComponent = () => <div>Admin Dashboard</div>

      render(
        <AdminAuthProvider>
          <AdminProtection>
            <TestComponent />
          </AdminProtection>
        </AdminAuthProvider>
      )

      // Should show loading initially
      expect(screen.getByText(/Restoring session/)).toBeInTheDocument()

      // Fast-forward past the wait period
      vi.advanceTimersByTime(1600)

      // Should eventually show content without redirecting
      await waitFor(() => {
        expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
      })

      expect(mockRouter.replace).not.toHaveBeenCalled()
    })
  })

  describe('Error Handling and Fallbacks', () => {
    it('should handle auth context errors gracefully', async () => {
      // Mock auth error
      mockSupabaseClient.auth.getSession.mockRejectedValue(new Error('Auth service unavailable'))

      const TestComponent = () => <div>Admin Content</div>

      render(
        <AdminAuthProvider>
          <AdminProtection>
            <TestComponent />
          </AdminProtection>
        </AdminAuthProvider>
      )

      // Fast-forward past the wait period
      vi.advanceTimersByTime(1600)

      // Should redirect to login on persistent errors
      await waitFor(() => {
        expect(mockRouter.replace).toHaveBeenCalledWith('/admin-auth')
      })
    })
  })
})
