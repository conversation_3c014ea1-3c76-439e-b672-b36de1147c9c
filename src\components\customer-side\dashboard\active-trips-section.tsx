"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CalendarDays, MapPin, Car, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { finishTrip } from "@/app/customer/dashboard/actions/trip-actions";
import { useState } from "react";
import Image from "next/image";
import { format } from "date-fns";

interface ActiveTrip {
  id: string;
  status: string;
  from: string;
  to: string;
  pickup_location: string;
  total_amount: number;
  created_at: string;
  cars: {
    id: string;
    model: string;
    plate_number: string;
    image_url: string | null;
  };
}

interface ActiveTripsSectionProps {
  trips: ActiveTrip[];
  onTripCompleted: (tripId: string) => void;
}

export function ActiveTripsSection({ trips, onTripCompleted }: ActiveTripsSectionProps) {
  const { toast } = useToast();
  const [completingTrips, setCompletingTrips] = useState<Set<string>>(new Set());

  const handleFinishTrip = async (tripId: string) => {
    if (completingTrips.has(tripId)) return;

    setCompletingTrips(prev => new Set(prev).add(tripId));

    try {
      const result = await finishTrip(tripId);
      
      if (result.error) {
        toast({
          variant: "destructive",
          title: "Failed to complete trip",
          description: result.error.message,
        });
      } else {
        toast({
          title: "Trip Completed!",
          description: result.message,
        });
        onTripCompleted(tripId);
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred while completing your trip.",
      });
    } finally {
      setCompletingTrips(prev => {
        const next = new Set(prev);
        next.delete(tripId);
        return next;
      });
    }
  };

  if (!trips || trips.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Car className="h-5 w-5" />
            Active Trips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <Car className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No active trips at the moment.</p>
            <p className="text-sm">Book a car to start your next adventure!</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Car className="h-5 w-5" />
          Active Trips ({trips.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {trips.map((trip) => (
          <div
            key={trip.id}
            className="border rounded-lg p-4 bg-gradient-to-r from-blue-50 to-indigo-50"
          >
            <div className="flex flex-col md:flex-row gap-4">
              {/* Car Image */}
              <div className="relative h-24 w-full md:w-32 bg-white rounded-lg overflow-hidden flex-shrink-0">
                <Image
                  src={trip.cars.image_url || "/placeholder.svg"}
                  alt={trip.cars.model}
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 100vw, 128px"
                />
              </div>

              {/* Trip Details */}
              <div className="flex-1 min-w-0">
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 mb-3">
                  <div>
                    <h3 className="font-semibold text-lg text-gray-900 truncate">
                      {trip.cars.model}
                    </h3>
                    <p className="text-sm text-gray-600">
                      Plate: {trip.cars.plate_number}
                    </p>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-700 w-fit">
                    Active Trip
                  </Badge>
                </div>

                {/* Trip Info Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <CalendarDays className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-gray-600">Rental Period</p>
                      <p className="font-medium">
                        {format(new Date(trip.from), "MMM d")} - {format(new Date(trip.to), "MMM d, yyyy")}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-gray-600">Pickup Location</p>
                      <p className="font-medium truncate">{trip.pickup_location}</p>
                    </div>
                  </div>
                </div>

                {/* Total Amount */}
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Total Amount</p>
                      <p className="text-lg font-bold text-gray-900">
                        ₱{trip.total_amount.toLocaleString()}
                      </p>
                    </div>
                    
                    {/* Finish Trip Button */}
                    <Button
                      onClick={() => handleFinishTrip(trip.id)}
                      disabled={completingTrips.has(trip.id)}
                      className="bg-green-600 hover:bg-green-700 text-white gap-2"
                    >
                      <CheckCircle className="h-4 w-4" />
                      {completingTrips.has(trip.id) ? "Completing..." : "Trip Finished"}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
