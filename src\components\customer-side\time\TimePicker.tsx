"use client"

import * as React from "react"
import { Clock, ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"

interface TimePickerProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  minTime?: string
  maxTime?: string
  step?: number // minutes, default 60
  showQuickActions?: boolean
  showSecondaryFormat?: boolean
  id?: string
  "aria-label"?: string
  "aria-describedby"?: string
}

interface TimeOption {
  value: string
  label: string
  secondary?: string
}

// Generate time options based on min/max/step
function generateTimeOptions(
  minTime: string = "06:00",
  maxTime: string = "22:00", 
  step: number = 60
): TimeOption[] {
  const options: TimeOption[] = []
  const [minHour, minMinute] = minTime.split(":").map(Number)
  const [maxHour, maxMinute] = maxTime.split(":").map(Number)
  
  const startMinutes = minHour * 60 + minMinute
  const endMinutes = maxHour * 60 + maxMinute
  
  for (let minutes = startMinutes; minutes <= endMinutes; minutes += step) {
    const hour = Math.floor(minutes / 60)
    const minute = minutes % 60
    
    if (hour > 23) break
    
    const value = `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`
    
    // 12-hour format for display
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
    const period = hour >= 12 ? "PM" : "AM"
    const label = `${displayHour}:${minute.toString().padStart(2, "0")} ${period}`
    const secondary = value // 24-hour format as secondary
    
    options.push({ value, label, secondary })
  }
  
  return options
}

function getCurrentTime(): string {
  const now = new Date()
  const hour = now.getHours().toString().padStart(2, "0")
  const minute = now.getMinutes().toString().padStart(2, "0")
  return `${hour}:${minute}`
}

export function TimePicker({
  value,
  onChange,
  placeholder = "Select time",
  disabled = false,
  className,
  minTime = "06:00",
  maxTime = "22:00",
  step = 60,
  showQuickActions = true,
  showSecondaryFormat = true,
  id,
  "aria-label": ariaLabel,
  "aria-describedby": ariaDescribedBy,
}: TimePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [selectedIndex, setSelectedIndex] = React.useState(-1)
  const listRef = React.useRef<HTMLDivElement>(null)
  const triggerRef = React.useRef<HTMLButtonElement>(null)
  const optionsRef = React.useRef<HTMLDivElement[]>([])
  
  const timeOptions = React.useMemo(() => 
    generateTimeOptions(minTime, maxTime, step), 
    [minTime, maxTime, step]
  )
  
  const selectedOption = timeOptions.find(option => option.value === value)
  
  // Auto-scroll to selected option when opened
  React.useEffect(() => {
    if (isOpen && value && listRef.current) {
      const currentIndex = timeOptions.findIndex(option => option.value === value)
      if (currentIndex >= 0) {
        setSelectedIndex(currentIndex)
        const optionElement = optionsRef.current[currentIndex]
        if (optionElement) {
          optionElement.scrollIntoView({ 
            block: "center", 
            behavior: "instant" 
          })
        }
      }
    }
  }, [isOpen, value, timeOptions])
  
  // Prevent body scroll when popover is open
  React.useEffect(() => {
    if (isOpen) {
      const originalOverflow = document.body.style.overflow
      document.body.style.overflow = "hidden"
      return () => {
        document.body.style.overflow = originalOverflow
      }
    }
  }, [isOpen])
  
  const handleSelect = (optionValue: string) => {
    onChange?.(optionValue)
    setIsOpen(false)
    triggerRef.current?.focus()
  }
  
  const handleQuickAction = (action: "now" | "clear" | "+30m") => {
    switch (action) {
      case "now":
        const currentTime = getCurrentTime()
        // Round to nearest step
        const [hours, minutes] = currentTime.split(":").map(Number)
        const totalMinutes = hours * 60 + minutes
        const roundedMinutes = Math.round(totalMinutes / step) * step
        const roundedHour = Math.floor(roundedMinutes / 60)
        const roundedMin = roundedMinutes % 60
        const roundedTime = `${roundedHour.toString().padStart(2, "0")}:${roundedMin.toString().padStart(2, "0")}`
        handleSelect(roundedTime)
        break
      case "clear":
        onChange?.("")
        setIsOpen(false)
        break
      case "+30m":
        if (value) {
          const [hours, minutes] = value.split(":").map(Number)
          const newMinutes = (hours * 60 + minutes + 30) % (24 * 60)
          const newHour = Math.floor(newMinutes / 60)
          const newMin = newMinutes % 60
          const newTime = `${newHour.toString().padStart(2, "0")}:${newMin.toString().padStart(2, "0")}`
          handleSelect(newTime)
        }
        break
    }
  }
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === "ArrowDown" || e.key === "Enter" || e.key === " ") {
        e.preventDefault()
        setIsOpen(true)
      }
      return
    }
    
    switch (e.key) {
      case "Escape":
        e.preventDefault()
        setIsOpen(false)
        triggerRef.current?.focus()
        break
      case "ArrowDown":
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < timeOptions.length - 1 ? prev + 1 : 0
        )
        break
      case "ArrowUp":
        e.preventDefault()
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : timeOptions.length - 1
        )
        break
      case "Home":
        e.preventDefault()
        setSelectedIndex(0)
        break
      case "End":
        e.preventDefault()
        setSelectedIndex(timeOptions.length - 1)
        break
      case "PageDown":
        e.preventDefault()
        setSelectedIndex(prev => 
          Math.min(prev + Math.floor(timeOptions.length / 10), timeOptions.length - 1)
        )
        break
      case "PageUp":
        e.preventDefault()
        setSelectedIndex(prev => 
          Math.max(prev - Math.floor(timeOptions.length / 10), 0)
        )
        break
      case "Enter":
        e.preventDefault()
        if (selectedIndex >= 0) {
          handleSelect(timeOptions[selectedIndex].value)
        }
        break
    }
  }
  
  // Auto-scroll to highlighted option
  React.useEffect(() => {
    if (selectedIndex >= 0 && optionsRef.current[selectedIndex]) {
      optionsRef.current[selectedIndex].scrollIntoView({
        block: "nearest",
        behavior: "smooth"
      })
    }
  }, [selectedIndex])
  
  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          ref={triggerRef}
          variant="secondary"
          role="combobox"
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          aria-label={ariaLabel || "Select time"}
          aria-describedby={ariaDescribedBy}
          className={cn(
            "h-12 w-full justify-between border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100 transition-all duration-200",
            !value && "text-muted-foreground",
            disabled && "cursor-not-allowed opacity-50",
            className
          )}
          disabled={disabled}
          onKeyDown={handleKeyDown}
          id={id}
        >
          <div className="flex items-center gap-3">
            <Clock className="h-5 w-5 text-blue-600 flex-shrink-0" />
            <span className="text-sm">
              {selectedOption ? selectedOption.label : placeholder}
            </span>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      
      <PopoverContent 
        className="w-80 p-0 bg-white border border-gray-200 rounded-lg shadow-lg"
        align="start"
        sideOffset={4}
        onCloseAutoFocus={(e) => {
          e.preventDefault()
          triggerRef.current?.focus()
        }}
      >
        <div className="relative">
          {/* Quick Actions */}
          {showQuickActions && (
            <div className="flex items-center gap-2 p-3 border-b border-gray-100">
              <Button
                variant="tertiary"
                size="sm"
                onClick={() => handleQuickAction("now")}
                className="h-8 px-3 text-xs font-medium text-blue-600 hover:bg-blue-50"
              >
                Now
              </Button>
              {value && (
                <Button
                  variant="tertiary"
                  size="sm"
                  onClick={() => handleQuickAction("+30m")}
                  className="h-8 px-3 text-xs font-medium text-blue-600 hover:bg-blue-50"
                >
                  +30m
                </Button>
              )}
              <Button
                variant="tertiary"
                size="sm"
                onClick={() => handleQuickAction("clear")}
                className="h-8 px-3 text-xs font-medium text-gray-500 hover:bg-gray-50 ml-auto"
              >
                Clear
              </Button>
            </div>
          )}
          
          {/* Time Options List */}
          <div 
            ref={listRef}
            role="listbox"
            aria-label="Time options"
            className="max-h-80 overflow-y-auto overscroll-contain"
            tabIndex={-1}
          >
            {timeOptions.map((option, index) => (
              <div
                key={option.value}
                ref={(el) => {
                  if (el) optionsRef.current[index] = el
                }}
                role="option"
                aria-selected={option.value === value}
                className={cn(
                  "flex items-center justify-between h-12 px-4 cursor-pointer transition-colors",
                  "hover:bg-blue-50 focus:bg-blue-50",
                  option.value === value && "bg-blue-100 text-blue-700",
                  selectedIndex === index && "bg-blue-50",
                  "border-b border-gray-50 last:border-b-0"
                )}
                onClick={() => handleSelect(option.value)}
                onMouseEnter={() => setSelectedIndex(index)}
              >
                <span className="text-sm font-medium">
                  {option.label}
                </span>
                {showSecondaryFormat && (
                  <span className="text-xs text-gray-500 font-mono">
                    ({option.secondary})
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Export for compatibility
export default TimePicker
