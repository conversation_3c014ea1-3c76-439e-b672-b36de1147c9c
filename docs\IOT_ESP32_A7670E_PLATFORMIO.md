## ESP32-A7670E tracker integration (PlatformIO)

This guide shows how to flash an ESP32 + SIMCOM A7670E cellular GNSS module to send GPS updates to the OllieTrack app.

### 1) Create PlatformIO project

- Board: `esp32dev`
- Framework: `arduino`

`platformio.ini`

```ini
[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
lib_deps =
  mikalhart/TinyGPSPlus @ ^1.0.3
  vshymanskyy/TinyGSM @ ^0.12.0
build_flags = -D TINY_GSM_MODEM_SIM7600
```

The A7670E is AT-compatible with SIM7600 family so TinyGSM works with `TINY_GSM_MODEM_SIM7600`.

### 2) Wiring summary

- A7670E UART to ESP32: `TXD -> GPIO16 (RX2)`, `RXD -> GPIO17 (TX2)`
- Power the A7670E with a stable 5V/2A supply
- GNSS antenna connected; cellular SIM inserted

### 3) Firmware sketch

Update APN and endpoint values. The device sends <PERSON><PERSON><PERSON> over HTTPS to the app endpoint `/api/gps/ingest` every 5 seconds.

```cpp
#include <Arduino.h>
#include <TinyGsmClient.h>
#include <TinyGPSPlus.h>

// Serial ports
HardwareSerial SerialAT(2); // pins 16/17 on ESP32

// Cellular setup
#define TINY_GSM_MODEM_SIM7600
const char* APN = "your.apn"; // set your SIM APN
const char* APN_USER = "";
const char* APN_PASS = "";

// App endpoint
const char* HOST = "your-app-domain.com"; // e.g. myapp.vercel.app or your server IP
const int   PORT = 443; // 443 for HTTPS via TinyGSM SSL
const char* PATH = "/api/gps/ingest";
const char* DEVICE_KEY = "set_same_as_INBOUND_DEVICE_TOKEN";
const char* CAR_ID = "car_001"; // map device to a car id in DB

TinyGsm        modem(SerialAT);
TinyGsmClientSecure secureClient(modem);
TinyGPSPlus    gps;

void setup() {
  Serial.begin(115200);
  delay(3000);
  Serial.println("Booting...");

  // Start modem
  SerialAT.begin(115200, SERIAL_8N1, 16, 17);
  delay(600);
  modem.restart();
  Serial.println("Modem started");

  // Connect to network
  if (!modem.gprsConnect(APN, APN_USER, APN_PASS)) {
    Serial.println("GPRS connect failed");
  } else {
    Serial.println("GPRS connected");
  }
}

void loop() {
  // Try to read GNSS data via modem NMEA stream if available, else use modem GNSS API
  // For simplicity, we assume we have lat/lng from the modem directly
  float lat = NAN, lng = NAN, speedKph = 0; int heading = 0;

  // SIMCOM GNSS power on (if needed)
  modem.sendAT("AT+CGNSPWR=1");
  modem.waitResponse(1000);
  modem.sendAT("AT+CGNSINF");
  if (modem.waitResponse(2000, "+CGNSINF: ") == 1) {
    String g = modem.stream.readStringUntil('\n');
    // Format: +CGNSINF: <mode>,<status>,<date>,<lat>,<lon>,<alt>,<speed>...
    int idxLat = g.indexOf(','); // skip mode
    for (int i=0;i<2;i++) idxLat = g.indexOf(',', idxLat+1); // to date
    int s = idxLat+1; int e = g.indexOf(',', s); // lat
    lat = g.substring(s, e).toFloat();
    s = e+1; e = g.indexOf(',', s);
    lng = g.substring(s, e).toFloat();
    // optional speed field parsing depending on firmware
  }

  if (!isnan(lat) && !isnan(lng)) {
    if (secureClient.connect(HOST, PORT)) {
      String payload = String("{\"carId\":\"") + CAR_ID + "\",\"latitude\":" + String(lat, 6) + ",\"longitude\":" + String(lng, 6) + ",\"speed\":" + String(speedKph,1) + ",\"heading\":" + String(heading) + "}";

      String request = String("POST ") + PATH + " HTTP/1.1\r\n" +
                       "Host: " + HOST + "\r\n" +
                       "Content-Type: application/json\r\n" +
                       "X-Device-Key: " + DEVICE_KEY + "\r\n" +
                       "Content-Length: " + payload.length() + "\r\n\r\n" +
                       payload;
      secureClient.print(request);
      delay(100);
      while (secureClient.connected() && secureClient.available()) {
        String line = secureClient.readStringUntil('\n');
        Serial.print(line);
      }
      secureClient.stop();
    } else {
      Serial.println("HTTPS connect failed");
    }
  }

  delay(5000);
}
```

Tip: If your server uses a certificate unknown to the module, you may need to disable SNI/SSL verification on the client or upload CA certs. For testing, you can proxy through a public HTTPS endpoint or use HTTP on a private network.

### 4) Configure the web app

Add environment variables:

```
NEXT_PUBLIC_GPS_SOURCE=api
INBOUND_DEVICE_TOKEN=replace-with-strong-token
NEXT_PUBLIC_SUPABASE_URL=...
NEXT_PUBLIC_SUPABASE_ANON_KEY=...

# optional: show a local dev ESP32 marker without DB
NEXT_PUBLIC_DEV_ESP32_ENABLE=true
NEXT_PUBLIC_DEV_ESP32_NAME=Mitsubishi ni GELO ESP32
NEXT_PUBLIC_DEV_ESP32_LAT=14.6116
NEXT_PUBLIC_DEV_ESP32_LNG=121.0000
NEXT_PUBLIC_DEV_ESP32_SPEED=0
NEXT_PUBLIC_DEV_ESP32_HEADING=0
NEXT_PUBLIC_DEV_ESP32_STATUS=active

# hide markers that are older than N seconds (works for dev marker too)
NEXT_PUBLIC_GPS_STALE_SECONDS=60
```

The admin tracker map (`components/admin/gps-tracker-map.tsx`) will auto-switch to polling `/api/gps/current` when `NEXT_PUBLIC_GPS_SOURCE=api`. Devices post to `/api/gps/ingest` using the shared token.


