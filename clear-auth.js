// Simple script to clear all authentication tokens
// Run this in the browser console or as a separate script

console.log('Clearing all authentication tokens...');

// Clear all storage keys
localStorage.removeItem('sb-admin-auth-token');
localStorage.removeItem('sb-customer-auth-token');

// Clear any other auth-related items
Object.keys(localStorage).forEach(key => {
  if (key.startsWith('sb-') || key.includes('auth') || key.includes('session') || key.includes('supabase')) {
    console.log('Removing:', key);
    localStorage.removeItem(key);
  }
});

// Clear session storage too
Object.keys(sessionStorage).forEach(key => {
  if (key.startsWith('sb-') || key.includes('auth') || key.includes('session') || key.includes('supabase')) {
    console.log('Removing from session:', key);
    sessionStorage.removeItem(key);
  }
});

console.log('All authentication tokens cleared. Please refresh the page.');
