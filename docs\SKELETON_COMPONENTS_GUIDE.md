# Skeleton Components Guide

A comprehensive guide to implementing animated skeleton screens in the OllieTrack application for improved perceived loading performance and enhanced user experience.

## Overview

Skeleton screens are animated placeholders that display while content is loading, providing users with immediate visual feedback and reducing perceived wait time. Our skeleton system includes:

- **Shimmer Animation**: Smooth left-to-right shimmer effect using CSS animations
- **Accessibility**: Full ARIA support for screen readers
- **Responsive Design**: Works across all breakpoints (320px to 1440px+)
- **Theme Integration**: Consistent with project design system
- **Performance**: Lightweight with CSS-only animations

## Base Components

### Skeleton

The foundational skeleton component with customizable animation.

```tsx
import { Skeleton } from "@/components/ui/skeleton"

// Basic usage
<Skeleton className="h-4 w-32" />

// With pulse animation instead of shimmer
<Skeleton className="h-6 w-48" shimmer={false} />

// Custom accessibility label
<Skeleton 
  className="h-8 w-64" 
  aria-label="Loading product title"
/>
```

**Props:**
- `className`: Tailwind CSS classes for styling
- `shimmer`: Boolean to enable/disable shimmer animation (default: true)
- `aria-label`: Custom accessibility label (default: "Loading content")

### SkeletonText

Multi-line text skeleton with customizable line count and width.

```tsx
import { SkeletonText } from "@/components/ui/skeleton"

// Single line
<SkeletonText lines={1} />

// Multiple lines with custom last line width
<SkeletonText 
  lines={3} 
  lastLineWidth="60%" 
  className="mb-4"
/>
```

**Props:**
- `lines`: Number of text lines (default: 1)
- `lastLineWidth`: Width of the last line as percentage (default: "75%")
- `className`: Additional CSS classes

### SkeletonButton

Button placeholder with predefined sizes.

```tsx
import { SkeletonButton } from "@/components/ui/skeleton"

// Different sizes
<SkeletonButton size="sm" />
<SkeletonButton size="default" />
<SkeletonButton size="lg" />

// Custom styling
<SkeletonButton className="w-full bg-blue-200" />
```

**Props:**
- `size`: Button size - "sm", "default", or "lg"
- `className`: Additional CSS classes

### SkeletonCard

Complete card skeleton with optional image and actions.

```tsx
import { SkeletonCard } from "@/components/ui/skeleton"

// Basic card
<SkeletonCard />

// Card with image and action buttons
<SkeletonCard hasImage hasActions />

// Grid layout
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {Array.from({ length: 6 }, (_, i) => (
    <SkeletonCard key={i} hasImage />
  ))}
</div>
```

**Props:**
- `hasImage`: Include image placeholder (default: false)
- `hasActions`: Include action buttons (default: false)
- `className`: Additional CSS classes

### SkeletonTable

Table skeleton with customizable rows and columns.

```tsx
import { SkeletonTable } from "@/components/ui/skeleton"

// Basic table
<SkeletonTable />

// Custom dimensions
<SkeletonTable 
  rows={8} 
  columns={6} 
  hasHeader={true}
/>

// Without header
<SkeletonTable rows={5} hasHeader={false} />
```

**Props:**
- `rows`: Number of table rows (default: 5)
- `columns`: Number of table columns (default: 4)
- `hasHeader`: Include header row (default: true)
- `className`: Additional CSS classes

### SkeletonAvatar

Circular avatar placeholder with different sizes.

```tsx
import { SkeletonAvatar } from "@/components/ui/skeleton"

// User profile section
<div className="flex items-center gap-3">
  <SkeletonAvatar size="lg" />
  <div className="space-y-2">
    <Skeleton className="h-5 w-32" />
    <Skeleton className="h-4 w-24" />
  </div>
</div>
```

**Props:**
- `size`: Avatar size - "sm" (32px), "default" (40px), or "lg" (48px)
- `className`: Additional CSS classes

### SkeletonForm

Form skeleton with multiple fields and submit button.

```tsx
import { SkeletonForm } from "@/components/ui/skeleton"

// Basic form
<SkeletonForm />

// Custom configuration
<SkeletonForm 
  fields={5} 
  hasSubmit={false}
  className="max-w-md"
/>
```

**Props:**
- `fields`: Number of form fields (default: 3)
- `hasSubmit`: Include submit button (default: true)
- `className`: Additional CSS classes

## Page-Specific Components

### Customer Skeletons

Located in `/src/components/customer-side/loading/skeleton-components.tsx`

```tsx
import {
  CustomerDashboardSkeleton,
  CustomerBookingFlowSkeleton,
  CustomerVehicleCatalogSkeleton,
  CustomerSettingsSkeleton,
  CustomerUnifiedTableSkeleton
} from "@/components/customer-side/loading/skeleton-components"

// Usage in customer pages
if (loading) {
  return <CustomerDashboardSkeleton />
}
```

### Admin Skeletons

Located in `/src/components/admin/loading/skeleton-components.tsx`

```tsx
import {
  AdminDashboardSkeleton,
  AdminBookingsSkeleton,
  AdminCarsSkeleton,
  AdminPaymentsSkeleton,
  AdminSettingsSkeleton,
  AdminTrackerSkeleton,
  AdminModalSkeleton,
  AdminDrawerSkeleton
} from "@/components/admin/loading/skeleton-components"

// Usage in admin pages
if (loading) {
  return <AdminDashboardSkeleton />
}
```

## Implementation Patterns

### Basic Loading State

```tsx
function MyComponent() {
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState(null)

  useEffect(() => {
    async function fetchData() {
      try {
        const result = await api.getData()
        setData(result)
      } finally {
        setLoading(false)
      }
    }
    fetchData()
  }, [])

  if (loading) {
    return <SkeletonCard hasImage hasActions />
  }

  return <DataCard data={data} />
}
```

### Suspense Integration

```tsx
import { Suspense } from "react"
import { CustomerBookingFlowSkeleton } from "@/components/customer-side/loading/skeleton-components"

function BookingPage() {
  return (
    <Suspense fallback={<CustomerBookingFlowSkeleton />}>
      <BookingFlow />
    </Suspense>
  )
}
```

### List Loading States

```tsx
function ProductList() {
  const [loading, setLoading] = useState(true)
  const [products, setProducts] = useState([])

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 9 }, (_, i) => (
          <SkeletonCard key={i} hasImage hasActions />
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {products.map(product => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  )
}
```

## Accessibility Guidelines

### ARIA Attributes

All skeleton components include proper ARIA attributes:

```tsx
// Automatically included in skeleton components
<div 
  role="status"
  aria-busy="true"
  aria-label="Loading content"
>
  <Skeleton className="h-4 w-32" />
</div>
```

### Screen Reader Announcements

Skeleton components provide meaningful descriptions:

```tsx
// Table skeleton with descriptive labels
<SkeletonTable 
  rows={5} 
  columns={4}
  aria-label="Loading data table with 5 rows and 4 columns"
/>

// Form skeleton with field descriptions
<SkeletonForm 
  fields={3}
  aria-label="Loading form with 3 fields"
/>
```

### Focus Management

Ensure focus is properly managed during loading states:

```tsx
function SearchResults() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState([])

  // Announce loading state to screen readers
  if (loading) {
    return (
      <div 
        role="status" 
        aria-live="polite"
        aria-label="Loading search results"
      >
        <SkeletonTable rows={8} columns={3} />
      </div>
    )
  }

  return (
    <div role="region" aria-label="Search results">
      {results.map(result => (
        <ResultItem key={result.id} result={result} />
      ))}
    </div>
  )
}
```

## Responsive Design

### Breakpoint Considerations

Skeleton components adapt to different screen sizes:

```tsx
// Mobile-first responsive skeleton
<div className="space-y-4">
  {/* Mobile: Stack vertically */}
  <div className="block md:hidden">
    <SkeletonCard />
    <SkeletonCard />
    <SkeletonCard />
  </div>
  
  {/* Desktop: Grid layout */}
  <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-3 gap-6">
    <SkeletonCard hasImage />
    <SkeletonCard hasImage />
    <SkeletonCard hasImage />
  </div>
</div>
```

### Mobile Optimization

For mobile-specific skeletons:

```tsx
// Mobile-optimized table skeleton
function MobileTableSkeleton() {
  return (
    <div className="space-y-3">
      {Array.from({ length: 5 }, (_, i) => (
        <Card key={i} className="p-4">
          <div className="flex items-center justify-between mb-3">
            <SkeletonAvatar size="sm" />
            <Skeleton className="h-6 w-16 rounded-full" />
          </div>
          <SkeletonText lines={2} />
          <div className="flex gap-2 mt-3">
            <SkeletonButton size="sm" />
            <SkeletonButton size="sm" />
          </div>
        </Card>
      ))}
    </div>
  )
}
```

## Performance Optimization

### Animation Performance

Skeletons use CSS-only animations for optimal performance:

```css
/* Shimmer animation in tailwind.config.js */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}
```

### Reduce Motion Support

Respect user preferences for reduced motion:

```tsx
// Automatically handled in base Skeleton component
function Skeleton({ shimmer = true, ...props }) {
  const prefersReducedMotion = useMedia('(prefers-reduced-motion: reduce)')
  
  return (
    <div
      className={cn(
        "bg-accent rounded-md relative overflow-hidden",
        shimmer && !prefersReducedMotion ? "animate-shimmer" : "animate-pulse"
      )}
      {...props}
    />
  )
}
```

## Testing Guidelines

### Visual Testing

1. **Breakpoint Testing**: Test on all screen sizes (320px to 1440px+)
2. **Animation Testing**: Verify smooth animations across devices
3. **Layout Testing**: Ensure no layout shifts when content loads
4. **Theme Testing**: Check skeleton appearance in light/dark modes

### Accessibility Testing

1. **Screen Reader Testing**: Test with NVDA, JAWS, or VoiceOver
2. **Keyboard Navigation**: Verify focus management during loading
3. **Color Contrast**: Ensure skeleton colors meet WCAG standards
4. **Reduced Motion**: Test with `prefers-reduced-motion: reduce`

### Performance Testing

1. **Animation Performance**: Monitor frame rates during animation
2. **Memory Usage**: Check for memory leaks in long-running skeletons
3. **Bundle Size**: Verify skeleton components don't increase bundle size significantly

## Test Page

Visit `/test-skeleton-screens` to:

- View all skeleton components
- Test responsive behavior
- Verify accessibility features
- Check animation performance
- Validate visual consistency

## Troubleshooting

### Common Issues

**Skeleton doesn't animate:**
- Check if `animate-shimmer` class is applied
- Verify Tailwind config includes shimmer keyframes
- Ensure shimmer prop is true (default)

**Layout shifts when content loads:**
- Match skeleton dimensions to actual content
- Use consistent spacing and margins
- Test with various content lengths

**Accessibility warnings:**
- Ensure proper ARIA roles and labels
- Check screen reader announcements
- Verify focus management

**Performance issues:**
- Reduce number of animated elements
- Use CSS transforms instead of layout properties
- Consider disabling animations on low-end devices

### Best Practices

1. **Match Content Structure**: Skeleton should mirror the actual content layout
2. **Consistent Timing**: Use uniform loading durations across components
3. **Meaningful Labels**: Provide descriptive ARIA labels for context
4. **Progressive Enhancement**: Start with basic skeletons, add features gradually
5. **User Testing**: Validate skeleton effectiveness with real users

## Migration Guide

### From Existing Loading States

Replace existing loading spinners and placeholders:

```tsx
// Before
if (loading) {
  return <div className="animate-pulse">Loading...</div>
}

// After
if (loading) {
  return <SkeletonCard hasImage hasActions />
}
```

### Gradual Implementation

1. Start with high-traffic pages
2. Replace most visible loading states first
3. Add skeleton components to new features
4. Gradually update existing components

## Contributing

When creating new skeleton components:

1. Follow existing patterns and naming conventions
2. Add proper accessibility attributes
3. Include responsive design considerations
4. Test across all supported breakpoints
5. Update this documentation with new components

## Related Resources

- [Tailwind CSS Animation Documentation](https://tailwindcss.com/docs/animation)
- [ARIA Authoring Practices Guide](https://www.w3.org/WAI/ARIA/apg/)
- [Web Content Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [React Suspense Documentation](https://react.dev/reference/react/Suspense)
