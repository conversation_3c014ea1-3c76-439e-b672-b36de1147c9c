"use client";

import * as React from "react";
import { PublicAppShell } from "@/components/layout/public-app-shell";

interface AuthLoadingProps {
  message?: string;
  className?: string;
}

export function AuthLoading({ 
  message = "Authenticating...", 
  className = "" 
}: AuthLoadingProps) {
  return (
    <PublicAppShell>
      <div className={`max-w-4xl mx-auto px-4 py-12 ${className}`}>
        <div 
          className="text-center space-y-4"
          role="status"
          aria-live="polite"
          aria-label={message}
        >
          {/* Loading spinner */}
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
          
          {/* Loading message */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-gray-900">
              {message}
            </h2>
            <p className="text-gray-600">
              Please wait while we verify your credentials
            </p>
          </div>
          
          {/* Progress indicator */}
          <div className="w-full max-w-xs mx-auto">
            <div className="bg-gray-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full animate-pulse w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
    </PublicAppShell>
  );
}

export function QuickAuthLoading() {
  return (
    <div className="flex items-center justify-center p-8">
      <div className="text-center space-y-3">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="text-sm text-gray-600">Loading...</p>
      </div>
    </div>
  );
}
