"use client";

import * as React from "react";
import { FileText, Save } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import type { BookingPolicySettings } from "@/lib/types";

export function PoliciesSection({
  settings,
  onUpdate,
  onSave,
  saving,
}: {
  settings: BookingPolicySettings | null;
  onUpdate: (updates: Partial<BookingPolicySettings>) => void;
  onSave: () => void;
  saving?: boolean;
}) {
  if (!settings) return null;

  return (
    <Card data-testid="admin-settings-card-policies">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Booking Policies
        </CardTitle>
        <p className="text-sm text-gray-600">
          Set rental duration limits, advance booking requirements, and
          cancellation policies
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Rental Duration */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Rental Duration</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="min_duration">Minimum Duration (hours)</Label>
              <Input
                id="min_duration"
                type="number"
                value={settings.min_rental_duration_hours}
                onChange={(e) =>
                  onUpdate({
                    min_rental_duration_hours: parseInt(e.target.value) || 1,
                  })
                }
                placeholder="4"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="max_duration">Maximum Duration (days)</Label>
              <Input
                id="max_duration"
                type="number"
                value={settings.max_rental_duration_days}
                onChange={(e) =>
                  onUpdate({
                    max_rental_duration_days: parseInt(e.target.value) || 30,
                  })
                }
                placeholder="30"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="advance_booking">
                Advance Booking Cutoff (hours)
              </Label>
              <Input
                id="advance_booking"
                type="number"
                value={settings.advance_booking_cutoff_hours}
                onChange={(e) =>
                  onUpdate({
                    advance_booking_cutoff_hours: parseInt(e.target.value) || 2,
                  })
                }
                placeholder="2"
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Cancellation Policy */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Cancellation Policy</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="free_cancel">
                Free Cancellation (hours before)
              </Label>
              <Input
                id="free_cancel"
                type="number"
                value={settings.cancellation_policy.free_cancellation_hours}
                onChange={(e) =>
                  onUpdate({
                    cancellation_policy: {
                      ...settings.cancellation_policy,
                      free_cancellation_hours: parseInt(e.target.value) || 24,
                    },
                  })
                }
                placeholder="24"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="cancel_fee">Cancellation Fee (₱)</Label>
              <Input
                id="cancel_fee"
                type="number"
                value={settings.cancellation_policy.cancellation_fee}
                onChange={(e) =>
                  onUpdate({
                    cancellation_policy: {
                      ...settings.cancellation_policy,
                      cancellation_fee: parseFloat(e.target.value) || 0,
                    },
                  })
                }
                placeholder="500"
              />
            </div>
          </div>
        </div>

        <Separator />


        <div className="flex justify-end pt-4">
          <Button
            onClick={onSave}
            disabled={saving}
            className="px-6 bg-blue-600 hover:bg-blue-700"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
