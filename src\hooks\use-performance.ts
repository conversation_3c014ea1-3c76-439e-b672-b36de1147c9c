"use client"

import { useEffect, useState } from "react"
import { detectNetworkSpeed, shouldLoadHighQuality, measureWebVitals } from "@/lib/performance/bundle-analyzer"

export function usePerformance() {
  const [networkSpeed, setNetworkSpeed] = useState<string>('fast')
  const [webVitals, setWebVitals] = useState<Record<string, number>>({})

  useEffect(() => {
    // Detect network speed
    const speed = detectNetworkSpeed()
    setNetworkSpeed(speed)

    // Monitor Web Vitals
    measureWebVitals((metric) => {
      setWebVitals(prev => ({
        ...prev,
        [metric.name]: metric.value
      }))
    })
  }, [])

  return {
    networkSpeed,
    webVitals,
    shouldLoadHighQuality: shouldLoadHighQuality(),
    isSlowNetwork: ['slow-2g', '2g', '3g'].includes(networkSpeed)
  }
}
