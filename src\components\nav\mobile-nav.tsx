// DEPRECATED: Use ResponsiveNav from @/components/customer-side/nav instead
// This component is kept for backward compatibility and will be removed in a future version

"use client"

import { useState, useContext } from 'react'
import { Menu, X, Car, LayoutDashboard, Package, Users, CircleHelp, Phone, Scale, Cog, LogOut } from 'lucide-react'
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useCustomerAuth, CustomerAuthContext } from "../auth/customer-auth-context"
import { useRouter } from "next/navigation"
import { Button } from "../ui/button"
import { MoreMenu } from "../customer-side/nav/MoreMenu"
import { customerNavItems, authNavItems } from "../customer-side/nav/navItems"
import {
  Sheet,
  SheetContent,
  SheetHeader,
  Sheet<PERSON>it<PERSON>,
  SheetTrigger,
} from "../ui/sheet"

// Legacy nav items mapping for backward compatibility
const items = [
  { label: "Overview", icon: LayoutDashboard, href: "/" },
  { label: "Catalog", icon: Package, href: "/customer/catalog" },
  { label: "Dashboard", icon: Users, href: "/customer/dashboard" },
  { label: "FAQ", icon: CircleHelp, href: "/customer/faq" },
  { label: "Contact Us", icon: Phone, href: "/customer/contact" },
  { label: "Terms", icon: Scale, href: "/customer/terms" },
  { label: "Settings", icon: Cog, href: "/customer/settings" },
]

export function MobileNav() {
  const [isOpen, setIsOpen] = useState(false)
  
  // Check if we're within a CustomerAuthProvider
  const context = useContext(CustomerAuthContext)
  
  // Use auth safely - default to null if not in provider
  const user = context ? useCustomerAuth().user : null
  
  const pathname = usePathname()

  const isActive = (href: string) => {
    if (href === "/") {
      return pathname === "/"
    }
    return pathname.startsWith(href)
  }

  const handleNavClick = () => {
    setIsOpen(false)
  }

  // Filter items based on auth status for legacy compatibility
  const filteredItems = items.filter(item => {
    if (item.href === '/customer/dashboard' || item.href === '/customer/settings') {
      return Boolean(user)
    }
    return true
  })

  return (
    <>
      {/* Mobile Menu Button */}
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button
            variant="tertiary"
            size="sm"
            className="md:hidden p-2"
          >
            <Menu className="h-5 w-5" />
            <span className="sr-only">Open menu</span>
          </Button>
        </SheetTrigger>

        <SheetContent side="left" className="w-80 max-w-[85vw] p-0">
          <div className="flex flex-col h-full">
            {/* Header */}
            <SheetHeader className="p-4 border-b">
              <div className="flex items-center justify-between">
                <Link 
                  href="/" 
                  className="flex items-center gap-3"
                  onClick={handleNavClick}
                >
                  <Image
                    src="/ollie_logo.svg"
                    alt="Ollie Track Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                  <div className="flex flex-col">
                    <span className="font-bold text-gray-900">Ollie Track</span>
                    <span className="text-xs text-gray-500 font-medium">Car Rental</span>
                  </div>
                </Link>
              </div>
            </SheetHeader>

            {/* User Info */}
            {user && (
              <div className="p-4 bg-gray-50 border-b">
                <div className="flex items-center gap-3">
                  <Image
                    src="/avatar.svg"
                    alt="User avatar"
                    width={40}
                    height={40}
                    className="rounded-full bg-gray-100 p-1"
                  />
                  <div>
                    <div className="font-medium text-gray-900">{user.email}</div>
                    <div className="text-xs text-blue-600 font-medium">Customer</div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation */}
            <div className="flex-1 p-4 overflow-y-auto">
              <MoreMenu 
                items={filteredItems.map(item => ({
                  key: item.href,
                  label: item.label,
                  href: item.href,
                  icon: item.icon,
                  priority: 1
                }))}
                onItemClick={handleNavClick}
                showUserActions={false}
              />
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
}
