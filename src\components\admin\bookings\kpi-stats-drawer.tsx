"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rigger } from "@/components/ui/sheet"
import { BarChartIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"

interface KPIStatsDrawerProps {
  stats: {
    today: number
    thisWeek: number
    pending: number
    active: number
    completed: number
    cancelled: number
  }
  className?: string
}

export function KPIStatsDrawer({
  stats,
  className
}: KPIStatsDrawerProps) {
  const isMobile = useIsMobile()
  const [open, setOpen] = React.useState(false)

  const kpiItems = [
    { 
      key: 'today', 
      label: 'Today', 
      value: stats.today, 
      bgColor: 'bg-gradient-to-br from-slate-50 to-slate-100', 
      textColor: 'text-slate-900',
      borderColor: 'border-slate-400',
      icon: '📅'
    },
    { 
      key: 'thisWeek', 
      label: 'This Week', 
      value: stats.thisWeek, 
      bgColor: 'bg-gradient-to-br from-slate-50 to-slate-100', 
      textColor: 'text-slate-900',
      borderColor: 'border-slate-400',
      icon: '📊'
    },
    { 
      key: 'pending', 
      label: 'Pending', 
      value: stats.pending, 
      bgColor: 'bg-gradient-to-br from-amber-50 to-amber-100', 
      textColor: 'text-amber-900',
      borderColor: 'border-amber-400',
      icon: '⏳'
    },
    { 
      key: 'active', 
      label: 'Active', 
      value: stats.active, 
      bgColor: 'bg-gradient-to-br from-green-50 to-green-100', 
      textColor: 'text-green-900',
      borderColor: 'border-green-400',
      icon: '🚗'
    },
    { 
      key: 'completed', 
      label: 'Completed', 
      value: stats.completed, 
      bgColor: 'bg-gradient-to-br from-blue-50 to-blue-100', 
      textColor: 'text-blue-900',
      borderColor: 'border-blue-400',
      icon: '✅'
    },
    { 
      key: 'cancelled', 
      label: 'Cancelled', 
      value: stats.cancelled, 
      bgColor: 'bg-gradient-to-br from-red-50 to-red-100', 
      textColor: 'text-red-900',
      borderColor: 'border-red-400',
      icon: '❌'
    },
  ]

  // KPI Stats drawer is now purely informational - no filter actions

  if (!isMobile) return null

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button 
          variant="primary"
          size="lg"
          className={cn(
            "w-full h-14 text-base font-semibold shadow-lg flex items-center justify-center gap-2",
            className
          )}
        >
          <BarChartIcon className="h-5 w-5" />
          Booking Stats
        </Button>
      </SheetTrigger>
      <SheetContent side="bottom" className="h-[80vh] rounded-t-3xl p-6">
        <SheetHeader>
          <SheetTitle className="text-2xl font-bold text-center mb-4">Booking Statistics</SheetTitle>
        </SheetHeader>
        <div className="space-y-4 overflow-y-auto max-h-[calc(80vh-100px)] pb-6">
          {kpiItems.map((item) => {
            return (
              <div
                key={item.key}
                className={cn(
                  "w-full px-4 py-4 rounded-xl border-2 font-semibold text-sm shadow-lg",
                  item.bgColor,
                  item.textColor,
                  item.borderColor
                )}
              >
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{item.icon}</span>
                  <div className="text-left flex-1">
                    <div className="text-sm font-medium opacity-80">{item.label}</div>
                    <div className="text-3xl font-bold">{item.value}</div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </SheetContent>
    </Sheet>
  )
}
