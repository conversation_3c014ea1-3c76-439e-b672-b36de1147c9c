import { NextResponse } from 'next/server'
import { createClient as createServerClient } from '@/lib/supabase/server'
import { createClient as createSbDirect } from '@supabase/supabase-js'
import { readDevPoints } from '@/app/api/gps/dev-store'

export async function GET() {
  try {
    const serviceUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    const supabase = serviceUrl && serviceKey
      ? createSbDirect(serviceUrl, serviceKey)
      : await createServerClient()

    // Get only the latest GPS location per car with active GPS device mappings
    const { data, error } = await supabase.rpc('get_latest_gps_per_car')

    if (error) throw error

    const rows = data || []
    
    // ⚠️ DISABLED: Dev store fallback removed to prevent phantom GPS data
    // Only return real GPS data from database - no fake/mock data
    // const staleSeconds = Number(process.env.NEXT_PUBLIC_GPS_STALE_SECONDS || '60')
    // const devRows = rows.length === 0 ? readDevPoints(Math.max(0, staleSeconds) * 1000) : []
    
    console.log(`📍 GPS API: Returning ${rows.length} real GPS location(s)`);
    
    return NextResponse.json({ data: rows || [] })
  } catch (error) {
    console.error('GPS current fetch error:', error)
    return NextResponse.json({ error: 'Internal Server Error', details: String(error) }, { status: 500 })
  }
}


