"use client";

import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Smartphone, Tablet, Monitor, CheckCircle, AlertCircle } from "lucide-react";

// Mock booking data for testing
const mockBooking = {
  id: "booking-123",
  booking_ref: "BK-2024-001",
  userName: "John Doe",
  carModel: "Toyota Vios 2023",
  from: new Date("2024-01-15T09:00:00"),
  to: new Date("2024-01-18T17:00:00"),
  days: 3,
  status: "Active" as const,
  payStatus: "Paid" as const,
  totalAmount: 15000,
  pickup_location: "Makati CBD",
  created_at: "2024-01-10T08:00:00Z",
  updated_at: "2024-01-10T08:00:00Z"
};

const mockFilters = {
  search: "",
  status: "all",
  paymentStatus: "all",
  vehicle: "all",
  location: "all",
  extensionStatus: "all",
  dateRange: { from: null, to: null }
};

export default function MobileResponsiveTestPage() {
  const [currentBreakpoint, setCurrentBreakpoint] = React.useState("unknown");
  const [viewportWidth, setViewportWidth] = React.useState(0);
  const [testResults, setTestResults] = React.useState<Record<string, boolean>>({});

  // Update viewport width and breakpoint detection
  React.useEffect(() => {
    const updateViewport = () => {
      const width = window.innerWidth;
      setViewportWidth(width);
      
      if (width < 375) {
        setCurrentBreakpoint("Mobile S (320px)");
      } else if (width < 425) {
        setCurrentBreakpoint("Mobile M (375px)");
      } else if (width < 768) {
        setCurrentBreakpoint("Mobile L (425px)");
      } else if (width < 1024) {
        setCurrentBreakpoint("Tablet (768px)");
      } else if (width < 1440) {
        setCurrentBreakpoint("Laptop (1024px)");
      } else {
        setCurrentBreakpoint("Desktop (1440px+)");
      }
    };

    updateViewport();
    window.addEventListener('resize', updateViewport);
    return () => window.removeEventListener('resize', updateViewport);
  }, []);

  const runTest = (testName: string, condition: boolean) => {
    setTestResults(prev => ({ ...prev, [testName]: condition }));
  };

  const testCases = [
    {
      name: "Header Navigation",
      description: "View toggle buttons stack properly on mobile",
      test: () => viewportWidth < 768
    },
    {
      name: "Touch Targets",
      description: "All buttons meet 44px minimum touch target",
      test: () => true // Visual inspection required
    },
    {
      name: "Filter Drawer",
      description: "Filter drawer opens and displays correctly",
      test: () => viewportWidth < 768
    },
    {
      name: "Booking Cards",
      description: "Booking cards display essential info only on mobile",
      test: () => viewportWidth < 768
    },
    {
      name: "No Horizontal Scroll",
      description: "No horizontal scrolling at any mobile breakpoint",
      test: () => document.documentElement.scrollWidth <= document.documentElement.clientWidth
    },
    {
      name: "Payment Info Display",
      description: "Payment info shows in compact format on mobile",
      test: () => viewportWidth < 768
    }
  ];

  React.useEffect(() => {
    // Run automated tests
    testCases.forEach(testCase => {
      if (testCase.test) {
        runTest(testCase.name, testCase.test());
      }
    });
  }, [viewportWidth]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Fixed Test Header */}
      <div className="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-3 xs:px-4 py-3">
          <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between gap-2">
            <h1 className="text-lg xs:text-xl font-bold text-gray-900">
              📱 Mobile Booking Navigation Test
            </h1>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300 text-xs xs:text-sm">
                {currentBreakpoint}
              </Badge>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-300 text-xs xs:text-sm">
                {viewportWidth}px
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-3 xs:p-4 space-y-4 xs:space-y-6">
        {/* Viewport Information */}
        <Card className="border-2 border-blue-200 bg-blue-50">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-base xs:text-lg">
              <div className="h-6 w-6 xs:h-8 xs:w-8 bg-blue-500 rounded-lg flex items-center justify-center">
                {viewportWidth < 425 ? <Smartphone className="h-3 w-3 xs:h-4 xs:w-4 text-white" /> :
                 viewportWidth < 1024 ? <Tablet className="h-3 w-3 xs:h-4 xs:w-4 text-white" /> :
                 <Monitor className="h-3 w-3 xs:h-4 xs:w-4 text-white" />}
              </div>
              Current Viewport: {currentBreakpoint}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="text-xs xs:text-sm text-blue-700">
              <p><strong>Width:</strong> {viewportWidth}px</p>
              <p><strong>Target Breakpoints:</strong> Mobile S (320px), Mobile M (375px), Mobile L (425px)</p>
            </div>
            
            {/* Breakpoint Indicators */}
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className={`p-2 rounded text-center font-medium ${viewportWidth >= 320 && viewportWidth < 375 ? 'bg-green-100 text-green-800 border border-green-300' : 'bg-gray-100 text-gray-600'}`}>
                320px
                <br />Mobile S
              </div>
              <div className={`p-2 rounded text-center font-medium ${viewportWidth >= 375 && viewportWidth < 425 ? 'bg-green-100 text-green-800 border border-green-300' : 'bg-gray-100 text-gray-600'}`}>
                375px
                <br />Mobile M
              </div>
              <div className={`p-2 rounded text-center font-medium ${viewportWidth >= 425 && viewportWidth < 768 ? 'bg-green-100 text-green-800 border border-green-300' : 'bg-gray-100 text-gray-600'}`}>
                425px
                <br />Mobile L
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card className="border-2 border-green-200 bg-green-50">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-base xs:text-lg">
              <div className="h-6 w-6 xs:h-8 xs:w-8 bg-green-500 rounded-lg flex items-center justify-center">
                <CheckCircle className="h-3 w-3 xs:h-4 xs:w-4 text-white" />
              </div>
              Automated Test Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {testCases.map((testCase) => (
                <div key={testCase.name} className="flex items-start gap-2 p-2 bg-white rounded border">
                  <div className="flex-shrink-0 mt-0.5">
                    {testResults[testCase.name] ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-yellow-600" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs xs:text-sm font-medium text-gray-900">{testCase.name}</p>
                    <p className="text-xs text-gray-600">{testCase.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Navigation Test Section */}
        <Card className="border-2 border-purple-200 bg-purple-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-base xs:text-lg">🧭 Header Navigation Demo</CardTitle>
          </CardHeader>
          <CardContent>
            {/* Mock Header Navigation */}
            <div className="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 text-white rounded-lg p-3 xs:p-4">
              <div className="flex items-center gap-2 xs:gap-3 mb-3">
                <div className="w-6 h-6 xs:w-8 xs:h-8 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-sm xs:text-base">📋</span>
                </div>
                <h2 className="text-base xs:text-lg font-bold">Bookings</h2>
              </div>
              
              {/* Stats and View Toggle */}
              <div className="flex flex-col space-y-2 xs:space-y-3">
                <div className="bg-white/10 backdrop-blur-sm rounded-full px-3 py-1.5 w-fit">
                  <span className="text-xs font-medium">25 Total Bookings</span>
                </div>
                
                {/* Mobile-Optimized View Toggle */}
                <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-full p-1 w-full xs:w-fit overflow-hidden">
                  <Button
                    variant="primary"
                    size="sm"
                    className="flex-1 xs:flex-none px-2 xs:px-3 py-2 rounded-full text-xs font-semibold border-0 min-h-[44px] bg-gradient-to-r from-emerald-500 to-green-600 text-white"
                  >
                    <span className="hidden xs:inline">Table</span>
                    <span className="xs:hidden">📊</span>
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 xs:flex-none px-2 xs:px-3 py-2 rounded-full text-xs font-semibold border-0 min-h-[44px] text-white/70 bg-transparent"
                  >
                    <span className="hidden xs:inline">Calendar</span>
                    <span className="xs:hidden">📅</span>
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex-1 xs:flex-none px-2 xs:px-3 py-2 rounded-full text-xs font-semibold border-0 min-h-[44px] text-white/70 bg-transparent"
                  >
                    <span className="hidden xs:inline">Extensions</span>
                    <span className="xs:hidden">⏱️</span>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Filter Demo */}
        <Card className="border-2 border-orange-200 bg-orange-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-base xs:text-lg">🔍 Filter Interface Demo</CardTitle>
          </CardHeader>
          <CardContent>
            <Button 
              variant="secondary"
              size="lg"
              className="w-full min-h-[52px] text-sm xs:text-base font-semibold shadow-lg flex items-center justify-center gap-2 rounded-xl bg-white border-2 border-gray-300 text-gray-700"
            >
              <span className="h-4 w-4 xs:h-5 xs:w-5">🔍</span>
              <span>Filters</span>
            </Button>
          </CardContent>
        </Card>

        {/* Booking Card Demo */}
        <Card className="border-2 border-pink-200 bg-pink-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-base xs:text-lg">🎫 Booking Card Demo</CardTitle>
          </CardHeader>
          <CardContent>
            {/* Mock Booking Card */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-md overflow-hidden">
              {/* Top Section */}
              <div className="p-3 border-b border-gray-100 flex items-center justify-between gap-2">
                <div className="flex-1 min-w-0">
                  <h3 className="font-bold text-sm xs:text-base text-gray-900 truncate">{mockBooking.userName}</h3>
                  <p className="text-xs font-mono font-bold text-blue-700 mt-1">#{mockBooking.booking_ref}</p>
                </div>
                <Badge className="bg-green-100 text-green-800 text-xs">Active</Badge>
              </div>
              
              {/* Middle Section */}
              <div className="p-3 border-b border-gray-100">
                <div className="flex items-center mb-2">
                  <div className="h-7 w-7 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                    <span className="text-blue-700 text-base">🚗</span>
                  </div>
                  <p className="font-bold text-sm text-gray-900 truncate">{mockBooking.carModel}</p>
                </div>
                
                {/* Mobile: Compact date display */}
                {viewportWidth < 768 && (
                  <div className="bg-gray-50 rounded-lg p-2">
                    <div className="flex items-center justify-between text-xs text-gray-700">
                      <div className="flex items-center gap-1">
                        <span>📅</span>
                        <span>Jan 15 - Jan 18</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span>⏰</span>
                        <span>3d</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Bottom Section */}
              <div className="p-3">
                {/* Mobile: Payment info above buttons */}
                {viewportWidth < 768 && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-2 mb-3 flex items-center justify-between">
                    <Badge className="bg-green-100 text-green-800 text-xs">Paid</Badge>
                    <p className="font-bold text-sm text-green-700">₱{mockBooking.totalAmount.toFixed(2)}</p>
                  </div>
                )}
                
                {/* Action Buttons */}
                <div className="grid grid-cols-2 gap-2">
                  <Button 
                    variant="secondary" 
                    className="min-h-[48px] xs:min-h-[44px] flex items-center justify-center border-blue-300 gap-2 text-xs xs:text-sm"
                  >
                    <span className="h-4 w-4">👁️</span>
                    <span>{viewportWidth < 768 ? "Details" : "View Details"}</span>
                  </Button>
                  <Button 
                    className="min-h-[48px] xs:min-h-[44px] flex items-center justify-center bg-blue-600 gap-2 text-xs xs:text-sm"
                  >
                    <span className="h-4 w-4">📅</span>
                    <span>{viewportWidth < 768 ? "Calendar" : "Add to Calendar"}</span>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Manual Testing Instructions */}
        <Card className="border-2 border-yellow-200 bg-yellow-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-base xs:text-lg">📋 Manual Testing Checklist</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-xs xs:text-sm">
              <div className="space-y-2">
                <h4 className="font-semibold text-yellow-800">At 320px (Mobile S):</h4>
                <ul className="space-y-1 text-yellow-700 pl-4">
                  <li>• View toggle shows icons only</li>
                  <li>• Filter button is full width</li>
                  <li>• No horizontal scrolling</li>
                  <li>• All touch targets ≥48px</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-semibold text-yellow-800">At 375px (Mobile M):</h4>
                <ul className="space-y-1 text-yellow-700 pl-4">
                  <li>• Improved spacing and readability</li>
                  <li>• Booking cards show essential info</li>
                  <li>• Drawer navigation works smoothly</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-semibold text-yellow-800">At 425px (Mobile L):</h4>
                <ul className="space-y-1 text-yellow-700 pl-4">
                  <li>• Optimal mobile experience</li>
                  <li>• All elements properly sized</li>
                  <li>• Smooth transitions and interactions</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Navigation Links */}
        <Card className="border-2 border-gray-200 bg-gray-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-base xs:text-lg">🔗 Navigation</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-1 xs:grid-cols-2 gap-2">
              <Button 
                variant="secondary" 
                className="min-h-[44px] justify-start"
                onClick={() => window.location.href = '/admin/bookings'}
              >
                ← Back to Bookings Page
              </Button>
              <Button 
                variant="secondary" 
                className="min-h-[44px] justify-start"
                onClick={() => window.location.href = '/admin'}
              >
                🏠 Admin Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
