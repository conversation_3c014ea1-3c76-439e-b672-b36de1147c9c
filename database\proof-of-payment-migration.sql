-- Migration: Add proof of payment fields to payments table
-- This script adds fields to support proof of payment upload system

-- Add new columns to payments table
ALTER TABLE public.payments 
ADD COLUMN IF NOT EXISTS proof_of_payment_url TEXT,
ADD COLUMN IF NOT EXISTS verification_notes TEXT,
ADD COLUMN IF NOT EXISTS verified_by UUID REFERENCES public.profiles(id),
ADD COLUMN IF NOT EXISTS verified_at TIMESTAMPTZ;

-- Update payment status enum to include new verification statuses
-- Note: This will only work if the table is empty or you handle existing data
-- In production, you'd want to migrate existing data first

-- Add comment for documentation
COMMENT ON COLUMN public.payments.proof_of_payment_url IS 'URL to uploaded proof of payment file (screenshots, receipts)';
COMMENT ON COLUMN public.payments.verification_notes IS 'Admin notes during payment verification process';
COMMENT ON COLUMN public.payments.verified_by IS 'UUID of admin user who verified the payment';
COMMENT ON COLUMN public.payments.verified_at IS 'Timestamp when payment was verified by admin';

-- Update method column to support new payment methods
-- Note: In a production environment, you'd need to handle this constraint update more carefully
DO $$
BEGIN
    -- Try to drop the constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'payments_method_check' 
        AND table_name = 'payments'
    ) THEN
        ALTER TABLE public.payments DROP CONSTRAINT payments_method_check;
    END IF;
    
    -- Add the updated constraint
    ALTER TABLE public.payments ADD CONSTRAINT payments_method_check 
    CHECK (method IN ('Card', 'Wallet', 'Cash', 'GCash', 'PayMaya'));
END $$;

-- Update status column to support new payment verification statuses
DO $$
BEGIN
    -- Try to drop the constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'payments_status_check' 
        AND table_name = 'payments'
    ) THEN
        ALTER TABLE public.payments DROP CONSTRAINT payments_status_check;
    END IF;
    
    -- Add the updated constraint
    ALTER TABLE public.payments ADD CONSTRAINT payments_status_check 
    CHECK (status IN ('Pending', 'Paid', 'Failed', 'Refunded', 'Pending Verification', 'Rejected'));
END $$;

-- Create index for faster lookups on verification fields
CREATE INDEX IF NOT EXISTS idx_payments_verification_status 
ON public.payments(status) 
WHERE status IN ('Pending Verification', 'Rejected');

CREATE INDEX IF NOT EXISTS idx_payments_verified_by 
ON public.payments(verified_by) 
WHERE verified_by IS NOT NULL;

-- Enable RLS policy updates for payment verification
-- Allow admins to verify payments
CREATE POLICY IF NOT EXISTS "Admins can verify payments" ON public.payments
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);

-- Allow customers to view their payment status including verification info
CREATE POLICY IF NOT EXISTS "Customers can view their payment verification" ON public.payments
FOR SELECT
TO authenticated
USING (
  booking_id IN (
    SELECT id FROM public.bookings 
    WHERE customer_id = auth.uid()
  )
);
