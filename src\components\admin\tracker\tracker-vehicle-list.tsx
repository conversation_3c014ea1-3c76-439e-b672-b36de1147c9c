import { useState, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  User, 
  Search,
  MapPin,
  Navigation2,
  Clock,
  Zap
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { cn } from '@/lib/utils'
import { GPSLocation } from '@/lib/gps-data'

interface TrackerFilters {
  searchTerm: string
  categoryFilter: string
  branchFilter: string
  timeWindow: 'live' | '15m' | '1h' | '24h'
  sortBy: 'lastSeen' | 'speed' | 'distance'
  showClusters: boolean
  showTrails: boolean
  showGeofences: boolean
  denseList: boolean
}

interface TrackerVehicleListProps {
  vehicles: GPSLocation[]
  selectedCarId: string
  followingCarId: string
  selectedVehicles: string[]
  filters: TrackerFilters
  onVehicleSelect: (carId: string) => void
  onVehicleFollow: (carId: string) => void
  onSelectionChange: (vehicleIds: string[]) => void
}

interface VehicleRowProps {
  index: number
  style: React.CSSProperties
  data: {
    vehicles: GPSLocation[]
    selectedCarId: string
    followingCarId: string
    selectedVehicles: string[]
    isDense: boolean
    onVehicleSelect: (carId: string) => void
    onVehicleFollow: (carId: string) => void
    onVehicleToggle: (carId: string) => void
  }
}

function VehicleRow({ index, style, data }: VehicleRowProps) {
  const vehicle = data.vehicles[index]
  const isSelected = data.selectedCarId === vehicle.carId
  const isFollowing = data.followingCarId === vehicle.carId
  const isChecked = data.selectedVehicles.includes(vehicle.carId)

  return (
    <div style={style}>
      <div
        className={cn(
          "mx-2 mb-2 p-3 rounded-lg border cursor-pointer transition-all duration-200",
          "hover:shadow-md hover:border-blue-200",
          isSelected && "bg-blue-50 border-blue-300 shadow-sm",
          isFollowing && "ring-2 ring-blue-200",
          data.isDense ? "p-2" : "p-3"
        )}
        onClick={() => data.onVehicleSelect(vehicle.carId)}
      >
        <div className="flex items-start gap-3">
          {/* Checkbox */}
          <Checkbox
            checked={isChecked}
            onCheckedChange={() => data.onVehicleToggle(vehicle.carId)}
            onClick={(e) => e.stopPropagation()}
            className="mt-1"
            aria-label={`Select vehicle ${vehicle.carPlate}`}
          />

          {/* Vehicle Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2 mb-2">
              <div className="min-w-0 flex-1">
                <div className="font-semibold text-sm truncate">
                  {vehicle.carPlate}
                </div>
                <div className="text-xs text-muted-foreground truncate">
                  {vehicle.carModel}
                </div>
              </div>
            </div>

            {/* Details */}
            <div className={cn(
              "space-y-1",
              data.isDense ? "text-xs" : "text-xs"
            )}>
              {vehicle.status === 'active' && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <Navigation2 className="w-3 h-3" />
                    <span>Speed:</span>
                  </div>
                  <span className="font-medium">{vehicle.speed} km/h</span>
                </div>
              )}

              {vehicle.driverName && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <User className="w-3 h-3" />
                    <span>Driver:</span>
                  </div>
                  <span className="font-medium truncate max-w-24">
                    {vehicle.driverName}
                  </span>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1 text-muted-foreground">
                  <Clock className="w-3 h-3" />
                  <span>Last seen:</span>
                </div>
                <span className="text-muted-foreground">
                  {formatDistanceToNow(vehicle.timestamp, { addSuffix: true })}
                </span>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2 mt-2">
              <Button
                variant={isFollowing ? "primary" : "tertiary"}
                size="xs"
                onClick={(e) => {
                  e.stopPropagation()
                  data.onVehicleFollow(vehicle.carId)
                }}
                className="h-6 px-2 text-xs"
              >
                <Zap className="w-3 h-3 mr-1" />
                {isFollowing ? 'Following' : 'Follow'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export function TrackerVehicleList({
  vehicles,
  selectedCarId,
  followingCarId,
  selectedVehicles,
  filters,
  onVehicleSelect,
  onVehicleFollow,
  onSelectionChange
}: TrackerVehicleListProps) {
  const [quickSearch, setQuickSearch] = useState('')

  // Filter vehicles for quick search
  const filteredVehicles = useMemo(() => {
    if (!quickSearch) return vehicles
    
    return vehicles.filter(vehicle =>
      vehicle.carPlate.toLowerCase().includes(quickSearch.toLowerCase()) ||
      vehicle.carModel.toLowerCase().includes(quickSearch.toLowerCase()) ||
      (vehicle.driverName?.toLowerCase().includes(quickSearch.toLowerCase()) ?? false)
    )
  }, [vehicles, quickSearch])

  const handleVehicleToggle = (carId: string) => {
    const newSelection = selectedVehicles.includes(carId)
      ? selectedVehicles.filter(id => id !== carId)
      : [...selectedVehicles, carId]
    onSelectionChange(newSelection)
  }

  const handleSelectAll = () => {
    if (selectedVehicles.length === filteredVehicles.length) {
      onSelectionChange([])
    } else {
      onSelectionChange(filteredVehicles.map(v => v.carId))
    }
  }

  const isAllSelected = filteredVehicles.length > 0 && 
    selectedVehicles.length === filteredVehicles.length

  const isPartiallySelected = selectedVehicles.length > 0 && 
    selectedVehicles.length < filteredVehicles.length

  const itemData = {
    vehicles: filteredVehicles,
    selectedCarId,
    followingCarId,
    selectedVehicles,
    isDense: filters.denseList,
    onVehicleSelect,
    onVehicleFollow,
    onVehicleToggle: handleVehicleToggle
  }

  const itemHeight = filters.denseList ? 120 : 140

  return (
    <Card className="flex flex-col h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-semibold">
            Vehicles ({filteredVehicles.length})
          </CardTitle>
          <div className="flex items-center gap-2">
            <Checkbox
              checked={isAllSelected}
              onCheckedChange={handleSelectAll}
              aria-label="Select all vehicles"
            />
            <span className="text-xs text-muted-foreground">
              {selectedVehicles.length > 0 && `${selectedVehicles.length} selected`}
            </span>
          </div>
        </div>

        {/* Quick Search */}
        <div className="relative">
          <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Quick search..."
            value={quickSearch}
            onChange={(e) => setQuickSearch(e.target.value)}
            className="pl-9 h-8 text-sm"
          />
        </div>

      </CardHeader>

      <CardContent className="flex-1 p-0 overflow-hidden">
        {filteredVehicles.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-center p-6">
            <MapPin className="w-8 h-8 text-muted-foreground mb-2" />
            <div className="text-sm text-muted-foreground">
              No vehicles found
            </div>
            <div className="text-xs text-muted-foreground">
              Try adjusting your search or filters
            </div>
          </div>
        ) : (
          <ScrollArea className="h-96">
            <div className="space-y-2 p-2">
              {filteredVehicles.map((vehicle, index) => (
                <VehicleRow
                  key={vehicle.id}
                  index={index}
                  style={{}}
                  data={itemData}
                />
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  )
}
