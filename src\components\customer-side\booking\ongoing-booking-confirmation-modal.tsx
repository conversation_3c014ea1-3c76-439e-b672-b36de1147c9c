"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Car, 
  MapPin, 
  Calendar, 
  Clock, 
  User, 
  Phone, 
  Mail,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { useOngoingBooking, OngoingBookingData } from "@/hooks/use-ongoing-booking";

interface OngoingBookingConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  ongoingBooking: OngoingBookingData;
}

/**
 * Modal that shows booking summary and confirmation for continue/discard actions
 */
export function OngoingBookingConfirmationModal({
  isOpen,
  onClose,
  ongoingBooking
}: OngoingBookingConfirmationModalProps) {
  const { continueBooking, clearOngoingBooking } = useOngoingBooking();
  const [isDiscarding, setIsDiscarding] = useState(false);

  const handleContinue = () => {
    onClose();
    continueBooking();
  };

  const handleDiscard = async () => {
    setIsDiscarding(true);
    
    // Add a small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 500));
    
    clearOngoingBooking();
    setIsDiscarding(false);
    onClose();
  };

  const formatDate = (date: string, time: string) => {
    if (!date) return 'Not specified';
    try {
      const dateObj = new Date(`${date}T${time || '00:00'}`);
      return dateObj.toLocaleDateString('en-US', { 
        weekday: 'short',
        month: 'short', 
        day: 'numeric',
        year: 'numeric',
        hour: time ? 'numeric' : undefined,
        minute: time ? '2-digit' : undefined,
      });
    } catch {
      return date;
    }
  };

  const getStepProgress = (step: number) => {
    const steps = [
      { number: 1, label: "Booking Details", completed: step > 1 },
      { number: 2, label: "Requirements", completed: step > 2 },
      { number: 3, label: "Personal Info", completed: step > 3 },
      { number: 4, label: "Payment", completed: step > 4 },
      { number: 5, label: "Review & Confirm", completed: step > 5 }
    ];
    
    return steps;
  };

  const calculateDays = () => {
    if (!ongoingBooking.pickUpDate || !ongoingBooking.dropOffDate) return 0;
    
    try {
      const pickUp = new Date(ongoingBooking.pickUpDate);
      const dropOff = new Date(ongoingBooking.dropOffDate);
      const diffTime = Math.abs(dropOff.getTime() - pickUp.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    } catch {
      return 0;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-600" />
            Continue Your Booking?
          </DialogTitle>
          <DialogDescription>
            You have an ongoing booking that you can continue or discard.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Current Progress */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-sm">Booking Progress</h3>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  Step {ongoingBooking.currentStep} of 5
                </Badge>
              </div>
              
              <div className="grid grid-cols-5 gap-1 mb-2">
                {getStepProgress(ongoingBooking.currentStep).map((step) => (
                  <div
                    key={step.number}
                    className={`h-2 rounded-full ${
                      step.completed
                        ? 'bg-green-500'
                        : step.number === ongoingBooking.currentStep
                        ? 'bg-blue-500'
                        : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>
              
              <p className="text-xs text-gray-600">
                {getStepProgress(ongoingBooking.currentStep).find(s => s.number === ongoingBooking.currentStep)?.label}
              </p>
            </CardContent>
          </Card>

          {/* Booking Summary */}
          <Card>
            <CardContent className="p-4 space-y-3">
              <h3 className="font-semibold text-sm mb-3">Booking Summary</h3>
              
              {/* Vehicle Information */}
              {ongoingBooking.selectedCar && (
                <div className="flex items-center gap-2 text-sm">
                  <Car className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">
                    {ongoingBooking.selectedCar.year} {ongoingBooking.selectedCar.make} {ongoingBooking.selectedCar.model}
                  </span>
                </div>
              )}

              {/* Dates */}
              {(ongoingBooking.pickUpDate || ongoingBooking.dropOffDate) && (
                <div className="flex items-start gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-gray-500 mt-0.5" />
                  <div className="space-y-1">
                    {ongoingBooking.pickUpDate && (
                      <div>
                        <span className="font-medium">Pick-up:</span> {formatDate(ongoingBooking.pickUpDate, ongoingBooking.pickUpTime)}
                      </div>
                    )}
                    {ongoingBooking.dropOffDate && (
                      <div>
                        <span className="font-medium">Drop-off:</span> {formatDate(ongoingBooking.dropOffDate, ongoingBooking.dropOffTime)}
                      </div>
                    )}
                    {calculateDays() > 0 && (
                      <div className="text-xs text-gray-600">
                        Duration: {calculateDays()} {calculateDays() === 1 ? 'day' : 'days'}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Locations */}
              {(ongoingBooking.pickUpLocation || ongoingBooking.dropOffLocation) && (
                <div className="flex items-start gap-2 text-sm">
                  <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                  <div className="space-y-1">
                    {ongoingBooking.pickUpLocation && (
                      <div>
                        <span className="font-medium">From:</span> {ongoingBooking.pickUpLocation}
                      </div>
                    )}
                    {ongoingBooking.dropOffLocation && (
                      <div>
                        <span className="font-medium">To:</span> {ongoingBooking.dropOffLocation}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Customer Information */}
              {(ongoingBooking.customerName || ongoingBooking.customerEmail || ongoingBooking.customerPhone) && (
                <div className="space-y-2 pt-2 border-t">
                  {ongoingBooking.customerName && (
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4 text-gray-500" />
                      <span>{ongoingBooking.customerName}</span>
                    </div>
                  )}
                  {ongoingBooking.customerEmail && (
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <span>{ongoingBooking.customerEmail}</span>
                    </div>
                  )}
                  {ongoingBooking.customerPhone && (
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span>{ongoingBooking.customerPhone}</span>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Warning about discarding */}
          <Card className="bg-amber-50 border-amber-200">
            <CardContent className="p-4">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-amber-800">Important</p>
                  <p className="text-amber-700 mt-1">
                    Discarding will permanently remove all your booking progress and uploaded documents.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button 
            variant="secondary" 
            onClick={onClose}
            className="w-full sm:w-auto order-3 sm:order-1"
          >
            Cancel
          </Button>
          
          <Button 
            variant="destructive" 
            onClick={handleDiscard}
            disabled={isDiscarding}
            className="w-full sm:w-auto order-2"
          >
            {isDiscarding ? 'Discarding...' : 'Discard Booking'}
          </Button>
          
          <Button 
            onClick={handleContinue}
            className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 order-1 sm:order-3"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Continue Booking
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
