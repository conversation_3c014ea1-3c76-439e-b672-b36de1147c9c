"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import {
  CalendarIcon,
  Filter,
  RotateCcw,
  Search,
  Car,
  Wrench,
  Calendar as CalendarClock,
  AlertTriangle,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAdminAuth } from "@/components/auth/admin-auth-context";

// Types for car availability (inline for now)
interface CarAvailabilityItem {
  id: string;
  plateNo: string;
  model: string;
  location: string;
  status: "available" | "booked" | "underMaintenance" | "scheduledMaintenance";
  bookedUntil?: Date | null;
  nextMaintenanceDate?: Date | null;
}

// Helper functions
const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

const formatDate = (date: Date | null | undefined): string => {
  if (!date) return "N/A";
  return date.toLocaleDateString("en-GB"); // dd/mm/yyyy format
};

const formatDisplayDate = (date: Date): string => {
  return date.toLocaleDateString("en-GB"); // dd/mm/yyyy format
};

// Mock data function (TODO: Replace with real API)
const getMockCarAvailabilityData = async (): Promise<CarAvailabilityItem[]> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  return [
    {
      id: "c1",
      plateNo: "ABC-1001",
            model: "Toyota Vios XLE 2023 AT",
      location: "Manila",
      status: "available",
      bookedUntil: null,
      nextMaintenanceDate: addDays(new Date(), 30),
    },
    {
      id: "c2",
      plateNo: "ABC-1002",
      model: "Toyota Vios E 2016 AT",
      location: "Makati",
      status: "booked",
      bookedUntil: addDays(new Date(), 3),
      nextMaintenanceDate: addDays(new Date(), 45),
    },
    {
      id: "c3",
      plateNo: "SUV-2001",
      model: "Toyota Fortuner G 2017 AT",
      location: "Quezon City",
      status: "available",
      bookedUntil: null,
      nextMaintenanceDate: addDays(new Date(), 15),
    },
    {
      id: "c4",
      plateNo: "SUV-2002",
      model: "Toyota Fortuner V 2018 AT",
      location: "BGC Taguig",
      status: "booked",
      bookedUntil: addDays(new Date(), 7),
      nextMaintenanceDate: addDays(new Date(), 60),
    },
    {
      id: "c5",
      plateNo: "MPV-3001",
      model: "Toyota Innova J 2006 MT",
      location: "Manila",
      status: "underMaintenance",
      bookedUntil: null,
      nextMaintenanceDate: null,
    },
    {
      id: "c6",
      plateNo: "MPV-3002",
      model: "Toyota GL Grandia 2011 MT",
      location: "Alabang",
      status: "available",
      bookedUntil: null,
      nextMaintenanceDate: addDays(new Date(), 20),
    },
    {
      id: "c7",
      plateNo: "HAT-4001",
      model: "Mitsubishi Mirage GLS 2019 AT",
      location: "Makati",
      status: "scheduledMaintenance",
      bookedUntil: null,
      nextMaintenanceDate: addDays(new Date(), 5),
    },
    {
      id: "c8",
      plateNo: "HAT-4002",
      model: "Honda CR-V 2007 AT",
      location: "Quezon City",
      status: "available",
      bookedUntil: null,
      nextMaintenanceDate: addDays(new Date(), 25),
    },
    {
      id: "c9",
      plateNo: "SUV-2003",
      model: "Mitsubishi Montero 2014 MT",
      location: "BGC Taguig",
      status: "booked",
      bookedUntil: addDays(new Date(), 14),
      nextMaintenanceDate: addDays(new Date(), 40),
    },
    {
      id: "c10",
      plateNo: "MPV-3003",
      model: "Mitsubishi Xpander GLS 2019 AT",
      location: "Manila",
      status: "available",
      bookedUntil: null,
      nextMaintenanceDate: addDays(new Date(), 35),
    },
    {
      id: "c11",
      plateNo: "MPV-3004",
      model: "Mitsubishi Xpander GLS 2023 AT",
      location: "Alabang",
      status: "underMaintenance",
      bookedUntil: null,
      nextMaintenanceDate: null,
    },
    {
      id: "c12",
      plateNo: "MCY-5001",
            model: "Yamaha Mio i125 2019 AT",
      location: "Makati",
      status: "available",
      bookedUntil: null,
      nextMaintenanceDate: addDays(new Date(), 10),
    },
    {
      id: "c13",
      plateNo: "MCY-5002",
            model: "Yamaha NMAX 2019 AT",
      location: "Quequezon City",
      status: "scheduledMaintenance",
      bookedUntil: null,
      nextMaintenanceDate: addDays(new Date(), 2),
    },
  ];
};

const VEHICLE_TYPES = [
  "All Types",
  "SUV",
  "Sport",
  "Coupe",
  "Hatchback",
  "MPV",
];
const LOCATIONS = [
  "All Locations",
  "Manila",
  "Makati",
  "Quezon City",
  "BGC Taguig",
  "Alabang",
];

export default function CarAvailabilityPage() {
  const { loading: authLoading } = useAdminAuth();
  const [isLoading, setIsLoading] = React.useState(true);
  const [data, setData] = React.useState<CarAvailabilityItem[]>([]);
  const [filteredData, setFilteredData] = React.useState<CarAvailabilityItem[]>(
    []
  );

  // Filter states
  const [dateRange, setDateRange] = React.useState<{ from: Date; to: Date }>({
    from: new Date(),
    to: addDays(new Date(), 7),
  });
  const [location, setLocation] = React.useState("All Locations");
  const [vehicleType, setVehicleType] = React.useState("All Types");
  const [statusFilter, setStatusFilter] = React.useState<string[]>([]);
  const [searchQuery, setSearchQuery] = React.useState("");

  // Fetch data on component mount
  React.useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const result = await getMockCarAvailabilityData();
        setData(result);
        setFilteredData(result);
      } catch (error) {
        console.error("Failed to fetch car availability data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Apply filters
  React.useEffect(() => {
    let filtered = [...data];

    // Search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (item) =>
          item.plateNo.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.model.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Location filter
    if (location !== "All Locations") {
      filtered = filtered.filter((item) => item.location === location);
    }

    // Vehicle type filter
    if (vehicleType !== "All Types") {
      filtered = filtered.filter((item) => item.model.includes(vehicleType));
    }

    // Status filter
    if (statusFilter.length > 0) {
      filtered = filtered.filter((item) => statusFilter.includes(item.status));
    }

    setFilteredData(filtered);
  }, [data, searchQuery, location, vehicleType, statusFilter]);

  // Calculate KPI stats
  const stats = React.useMemo(() => {
    const available = filteredData.filter(
      (item) => item.status === "available"
    ).length;
    const booked = filteredData.filter(
      (item) => item.status === "booked"
    ).length;
    const underMaintenance = filteredData.filter(
      (item) => item.status === "underMaintenance"
    ).length;
    const scheduledMaintenance = filteredData.filter(
      (item) => item.status === "scheduledMaintenance"
    ).length;

    return { available, booked, underMaintenance, scheduledMaintenance };
  }, [filteredData]);

  const resetFilters = () => {
    setDateRange({ from: new Date(), to: addDays(new Date(), 7) });
    setLocation("All Locations");
    setVehicleType("All Types");
    setStatusFilter([]);
    setSearchQuery("");
  };

  const toggleStatusFilter = (status: string) => {
    setStatusFilter((prev) =>
      prev.includes(status)
        ? prev.filter((s) => s !== status)
        : [...prev, status]
    );
  };

  const getStatusBadge = (status: CarAvailabilityItem["status"]) => {
    switch (status) {
      case "available":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            Available
          </Badge>
        );
      case "booked":
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-200">
            Booked
          </Badge>
        );
      case "underMaintenance":
        return (
          <Badge className="bg-red-100 text-red-800 border-red-200">
            Under Maintenance
          </Badge>
        );
      case "scheduledMaintenance":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
            Scheduled Maintenance
          </Badge>
        );
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  // Wait for auth loading to complete before rendering
  if (authLoading) {
    return (
      <div className="space-y-6 p-2 sm:p-4 lg:p-6 max-w-full overflow-hidden">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Car Availability</h1>
            <p className="text-muted-foreground">
              Monitor vehicle availability and maintenance schedules
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3" />
            <span className="text-muted-foreground">Loading...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-2 sm:p-4 lg:p-6 max-w-full overflow-hidden">
      {/* Filters Bar - Sticky */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border border-border rounded-lg p-4 shadow-sm">
        <div className="flex flex-wrap gap-4 items-end">
          {/* Date Range */}
          <div className="flex flex-col space-y-2 min-w-[200px]">
            <label className="text-sm font-medium text-foreground">
              Date Range
            </label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="secondary"
                  className={cn(
                    "justify-start text-left font-normal bg-white hover:bg-gray-50 border-2 border-gray-200 hover:border-gray-300 transition-all duration-200 rounded-xl shadow-sm h-11",
                    !dateRange.from && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-3 h-4 w-4 text-gray-500" />
                  {dateRange.from ? (
                    dateRange.to ? (
                      <span className="font-medium text-gray-900">
                        {formatDisplayDate(dateRange.from)} -{" "}
                        {formatDisplayDate(dateRange.to)}
                      </span>
                    ) : (
                      <span className="font-medium text-gray-900">
                        {formatDisplayDate(dateRange.from)}
                      </span>
                    )
                  ) : (
                    <span className="text-gray-500">Pick a date range</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto p-0 bg-white shadow-xl border-0 rounded-2xl overflow-hidden"
                align="start"
              >
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-gray-100">
                  <h3 className="text-sm font-semibold text-gray-800 mb-1">
                    Select Date Range
                  </h3>
                  <p className="text-xs text-gray-600">
                    Choose your availability period
                  </p>
                </div>
                <div className="p-4 bg-white">
                  <Calendar
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={dateRange}
                    onSelect={(range) => {
                      if (range?.from && range?.to) {
                        setDateRange({ from: range.from, to: range.to });
                      }
                    }}
                    numberOfMonths={2}
                    className="rdp-modern"
                    classNames={{
                      months:
                        "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                      month: "space-y-4",
                      caption: "flex justify-center pt-1 relative items-center",
                      caption_label: "text-sm font-semibold text-gray-900",
                      nav: "space-x-1 flex items-center",
                      nav_button:
                        "h-8 w-8 bg-transparent hover:bg-gray-100 rounded-lg transition-colors duration-200 border-0 flex items-center justify-center",
                      nav_button_previous: "absolute left-1",
                      nav_button_next: "absolute right-1",
                      table: "w-full border-collapse space-y-1",
                      head_row: "flex",
                      head_cell:
                        "text-gray-500 rounded-md w-8 font-medium text-[0.8rem] uppercase tracking-wide",
                      row: "flex w-full mt-2",
                      cell: "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-blue-50 [&:has([aria-selected].day-outside)]:bg-gray-50/50 [&:has([aria-selected].day-range-end)]:rounded-r-lg [&:has([aria-selected].day-range-start)]:rounded-l-lg first:[&:has([aria-selected])]:rounded-l-lg last:[&:has([aria-selected])]:rounded-r-lg",
                      day: "h-8 w-8 p-0 font-normal aria-selected:opacity-100 hover:bg-blue-50 rounded-lg transition-colors duration-200 border-0 bg-transparent",
                      day_range_start:
                        "day-range-start bg-blue-500 text-white hover:bg-blue-600 rounded-lg",
                      day_range_end:
                        "day-range-end bg-blue-500 text-white hover:bg-blue-600 rounded-lg",
                      day_selected:
                        "bg-blue-500 text-white hover:bg-blue-600 hover:text-white focus:bg-blue-600 focus:text-white rounded-lg",
                      day_today:
                        "bg-gray-100 text-gray-900 font-semibold rounded-lg",
                      day_outside:
                        "text-gray-400 opacity-50 aria-selected:bg-gray-50/50 aria-selected:text-gray-500 aria-selected:opacity-30",
                      day_disabled: "text-gray-400 opacity-50",
                      day_range_middle:
                        "aria-selected:bg-blue-50 aria-selected:text-blue-900 hover:bg-blue-100 rounded-none",
                      day_hidden: "invisible",
                    }}
                  />
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* Location Filter */}
          <div className="flex flex-col space-y-2 min-w-[150px]">
            <label className="text-sm font-medium text-foreground">
              Location
            </label>
            <Select value={location} onValueChange={setLocation}>
              <SelectTrigger className="bg-white">
                <SelectValue placeholder="Select location" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                {LOCATIONS.map((loc) => (
                  <SelectItem key={loc} value={loc}>
                    {loc}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Vehicle Type Filter */}
          <div className="flex flex-col space-y-2 min-w-[150px]">
            <label className="text-sm font-medium text-foreground">
              Vehicle Type
            </label>
            <Select value={vehicleType} onValueChange={setVehicleType}>
              <SelectTrigger className="bg-white">
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                {VEHICLE_TYPES.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="flex flex-col space-y-2">
            <label className="text-sm font-medium text-foreground">
              Status
            </label>
            <div className="flex gap-2 flex-wrap">
              {[
                "available",
                "booked",
                "underMaintenance",
                "scheduledMaintenance",
              ].map((status) => (
                <Button
                  key={status}
                  variant={
                    statusFilter.includes(status) ? "primary" : "secondary"
                  }
                  size="sm"
                  onClick={() => toggleStatusFilter(status)}
                  className="text-xs"
                >
                  {status === "available" && "Available"}
                  {status === "booked" && "Booked"}
                  {status === "underMaintenance" && "Under Maintenance"}
                  {status === "scheduledMaintenance" && "Scheduled"}
                </Button>
              ))}
            </div>
          </div>

          {/* Search */}
          <div className="flex flex-col space-y-2 min-w-[200px]">
            <label className="text-sm font-medium text-foreground">
              Search
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search plate or model..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-white"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2 items-end">
            <Button
              variant="secondary"
              size="sm"
              onClick={resetFilters}
              className="bg-white"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </div>
      </div>

      {/* KPI Cards */}
      <section
        aria-labelledby="kpi-heading"
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <h2 id="kpi-heading" className="sr-only">
          Vehicle Availability Statistics
        </h2>

        <Card className="bg-white border border-border shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Available
            </CardTitle>
            <Car className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {isLoading ? <Skeleton className="h-8 w-16" /> : stats.available}
            </div>
            <p className="text-xs text-muted-foreground">
              +2.5% from last period
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white border border-border shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Booked
            </CardTitle>
            <CalendarClock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {isLoading ? <Skeleton className="h-8 w-16" /> : stats.booked}
            </div>
            <p className="text-xs text-muted-foreground">
              +12.3% from last period
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white border border-border shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Under Maintenance
            </CardTitle>
            <Wrench className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                stats.underMaintenance
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              -5.2% from last period
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white border border-border shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Scheduled Maintenance
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                stats.scheduledMaintenance
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              +1.8% from last period
            </p>
          </CardContent>
        </Card>
      </section>

      {/* Vehicles Table */}
      <Card className="bg-white border border-border shadow-sm">
        <CardHeader>
          <CardTitle>Vehicle Status ({filteredData.length} vehicles)</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader className="sticky top-0 bg-white border-b">
                <TableRow>
                  <TableHead className="min-w-[120px]">Plate Number</TableHead>
                  <TableHead className="min-w-[200px]">Model</TableHead>
                  <TableHead className="min-w-[120px]">Location</TableHead>
                  <TableHead className="min-w-[140px]">Status</TableHead>
                  <TableHead className="min-w-[120px]">Booked Until</TableHead>
                  <TableHead className="min-w-[140px]">
                    Next Maintenance
                  </TableHead>
                  <TableHead className="w-[80px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  // Loading skeletons
                  Array.from({ length: 5 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-6 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-8 w-16" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : filteredData.length === 0 ? (
                  // Empty state
                  <TableRow>
                    <TableCell colSpan={7} className="h-32 text-center">
                      <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <Car className="h-8 w-8 mb-2 opacity-50" />
                        <p className="text-sm">No vehicles found</p>
                        <p className="text-xs">Try adjusting your filters</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  // Data rows
                  filteredData.map((vehicle) => (
                    <TableRow key={vehicle.id} className="hover:bg-muted/50">
                      <TableCell className="font-medium">
                        {vehicle.plateNo}
                      </TableCell>
                      <TableCell>{vehicle.model}</TableCell>
                      <TableCell>{vehicle.location}</TableCell>
                      <TableCell>{getStatusBadge(vehicle.status)}</TableCell>
                      <TableCell>{formatDate(vehicle.bookedUntil)}</TableCell>
                      <TableCell>
                        {formatDate(vehicle.nextMaintenanceDate)}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="secondary"
                          size="sm"
                          className="h-8 px-2 text-xs"
                          onClick={() => {
                            // TODO: Open details modal or navigate to details page
                            console.log("View details for:", vehicle.id);
                          }}
                        >
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
