"use client"

import { useRouter } from "next/navigation"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { XCircle } from "lucide-react"
import Image from "next/image"

export default function ClientFeedbackPage() {
  const router = useRouter()

  const handleSignIn = () => {
    router.replace('/customer/login')
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Image 
              src="/ollie_logo.svg" 
              alt="Ollie's Rent A Car" 
              width={60} 
              height={60}
              className="dark:invert"
            />
          </div>
          <CardTitle className="text-red-600">Verification Failed</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <XCircle className="h-16 w-16 text-red-500 mx-auto" />
          <p className="text-gray-600">
            There was an error verifying your email address. The link may have expired or been used already.
          </p>
          <Button onClick={handleSignIn} className="w-full">
            Go to Sign In
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
