# PathLink GPS Tracker - Project Structure

This document provides an overview of the project file organization and explains the purpose of each file in the PathLink GPS Tracker GUI application.

## 📁 File Organization

```
LILYGO-PYTHON-GUI/
├── 📄 PathLink_GPS_PRD.md             # Product Requirements Document
├── 🐍 pathlink_gui.py                # Main GUI application
├── 📋 requirements.txt                # Python dependencies
├── 📖 README.md                       # Comprehensive documentation
├── 🧪 test_installation.py           # Installation verification script
├── 🪟 run_ollietrack.bat             # Windows launcher script
├── 🐧 run_ollietrack.sh              # Unix/Linux/macOS launcher script
├── 📊 PROJECT_STRUCTURE.md            # This file
└── ⚙️ ollietrack_config.ini          # Configuration file (auto-generated)
```

## 📄 File Descriptions

### Core Application Files

#### `pathlink_gui.py`
- **Purpose**: Main GUI application written in Python using Tkinter
- **Features**: Complete control panel for PathLink GPS system
- **Key Components**:
  - Configuration management
  - Protocol selection (HTTP, WebSocket, CoAP, AMQP)
  - Environment switching (DEV/PROD)
  - Test data sending
  - Real-time monitoring
  - Response logging

#### `requirements.txt`
- **Purpose**: Lists all required Python packages
- **Dependencies**:
  - `requests` - HTTP communication
  - `websocket-client` - WebSocket support
  - `urllib3` - Enhanced HTTP handling
- **Note**: Most modules are built into Python standard library

### Documentation Files

#### `PathLink_GPS_PRD.md`
- **Purpose**: Product Requirements Document
- **Content**: Complete system specifications and ESP32 code
- **Audience**: Developers and system architects
- **Key Sections**:
  - System overview and objectives
  - Feature specifications
  - ESP32 implementation details
  - Communication protocol alternatives

#### `README.md`
- **Purpose**: User-facing documentation
- **Content**: Installation, usage, and troubleshooting guides
- **Audience**: End users and administrators
- **Key Sections**:
  - Quick start guide
  - Feature overview
  - Configuration examples
  - Troubleshooting tips

#### `PROJECT_STRUCTURE.md`
- **Purpose**: This file - explains project organization
- **Content**: File descriptions and project overview
- **Audience**: Developers and contributors

### Utility and Launcher Files

#### `test_installation.py`
- **Purpose**: Verifies installation completeness
- **Features**:
  - Tests Python version compatibility
  - Verifies all dependencies are installed
  - Checks GUI application importability
  - Provides detailed error reporting
- **Usage**: Run before first launch to ensure everything works

#### `run_ollietrack.bat`
- **Purpose**: Windows batch file launcher
- **Features**:
  - Automatic dependency checking
  - Python installation verification
  - Error handling and user feedback
  - Easy one-click launching

#### `run_ollietrack.sh`
- **Purpose**: Unix-based system launcher
- **Features**:
  - Cross-platform Python detection
  - Automatic dependency installation
  - Shell script compatibility
  - Error handling and user feedback

### Configuration Files

#### `pathlink_config.ini`
- **Purpose**: Application configuration storage
- **Content**: Environment settings, URLs, and preferences
- **Auto-generated**: Created on first run with defaults
- **Sections**:
  - `[DEV]` - Development environment settings
  - `[PROD]` - Production environment settings
  - `[GENERAL]` - Application preferences

## 🏗️ Architecture Overview

### GUI Framework
- **Technology**: Tkinter (Python standard library)
- **Design Pattern**: Model-View-Controller (MVC)
- **Layout**: Grid-based responsive design
- **Styling**: Custom ttk styles for modern appearance

### Communication Layer
- **HTTP Client**: Requests library for REST API communication
- **WebSocket Client**: websocket-client for real-time communication
- **Protocol Support**: Extensible architecture for future protocols
- **Error Handling**: Comprehensive error handling and user feedback

### Configuration Management
- **File Format**: INI configuration files
- **Persistence**: Automatic saving and loading
- **Validation**: Input validation and error checking
- **Environment Support**: Separate DEV/PROD configurations

### Monitoring and Logging
- **Real-time Updates**: Live status monitoring
- **Response Logging**: Comprehensive communication logging
- **Thread Safety**: Background monitoring with thread safety
- **User Interface**: Scrollable text areas with timestamps

## 🔧 Development Workflow

### Setting Up Development Environment
1. **Clone Repository**: Get the source code
2. **Install Dependencies**: `pip install -r requirements.txt`
3. **Run Tests**: `python test_installation.py`
4. **Launch GUI**: `python pathlink_gui.py`

### Making Changes
1. **Edit Source**: Modify `ollietrack_gui.py`
2. **Test Changes**: Run the application
3. **Update Documentation**: Modify relevant docs
4. **Commit Changes**: Version control with clear messages

### Adding New Features
1. **Plan Feature**: Define requirements and design
2. **Implement**: Add code to appropriate sections
3. **Test**: Verify functionality works correctly
4. **Document**: Update README and other docs
5. **Deploy**: Release new version

## 📱 Platform Support

### Operating Systems
- **Windows**: Full support with batch launcher
- **macOS**: Full support with shell launcher
- **Linux**: Full support with shell launcher

### Python Versions
- **Minimum**: Python 3.7
- **Recommended**: Python 3.8+
- **Latest**: Python 3.11+ (fully tested)

### Dependencies
- **Built-in**: Most modules from Python standard library
- **External**: Minimal external dependencies
- **Optional**: Enhanced packages for better experience

## 🚀 Deployment

### Distribution
- **Source Code**: Python files for direct execution
- **Launcher Scripts**: Platform-specific launchers
- **Documentation**: Comprehensive user guides
- **Configuration**: Auto-generated config files

### Installation
- **Simple**: Just Python and pip required
- **Automatic**: Dependencies auto-installed
- **Verification**: Test script confirms installation
- **Configuration**: GUI-based setup

### Updates
- **Code Updates**: Replace Python files
- **Configuration**: Preserved across updates
- **Dependencies**: Check requirements.txt for changes
- **Documentation**: Review README for new features

## 🔍 Troubleshooting

### Common Issues
- **Python Version**: Ensure Python 3.7+
- **Dependencies**: Run `pip install -r requirements.txt`
- **Permissions**: Check file write permissions
- **Network**: Verify internet connectivity

### Debug Tools
- **Test Script**: `python test_installation.py`
- **Log Files**: Check response monitor in GUI
- **Error Messages**: Clear error reporting
- **Status Display**: Real-time connection status

### Getting Help
- **Documentation**: Review README.md
- **Test Script**: Run installation verification
- **Error Logs**: Check console output
- **Community**: Report issues with details

---

This project structure provides a clean, organized approach to the PathLink GPS Tracker GUI application, making it easy to understand, develop, and maintain.
