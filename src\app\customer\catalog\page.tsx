"use client";

import { PublicAppShell } from "@/components/layout/public-app-shell";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { CategoryCard } from "@/components/customer-side/cars/category-card";
import { CarCard } from "@/components/customer-side/cars/car-card";
import { CustomerVehicleCatalogSkeleton } from "@/components/customer-side/loading/skeleton-components";
import { createClient } from "@/lib/supabase/client";
import {
  Search,
  Filter,
  Car as CarIcon,
  Users,
  Fuel,
  Cog,
  ArrowLeft,
} from "lucide-react";
import type { Car } from "@/lib/types";
import * as React from "react";
import { useSearchParams } from "next/navigation";

export default function CatalogPage() {
  const searchParams = useSearchParams();
  const categoryParam = searchParams.get('category');
  
  const [selectedCategory, setSelectedCategory] = React.useState<string | null>(
    categoryParam
  );
  const [searchTerm, setSearchTerm] = React.useState("");
  const [categories, setCategories] = React.useState<any[]>([]);
  const [carsByCategory, setCarsByCategory] = React.useState<
    Record<string, Car[]>
  >({});
  const [loading, setLoading] = React.useState(true);

  // Set selected category from URL parameter if present
  React.useEffect(() => {
    if (categoryParam) {
      setSelectedCategory(categoryParam);
    }
  }, [categoryParam]);

  // Fetch vehicle categories and their associated cars from Supabase
  React.useEffect(() => {
    async function fetchCategoriesAndCars() {
      try {
        setLoading(true);
        const supabase = createClient();

        // Fetch categories with stats using RPC
        const { data: categoriesData, error: categoriesError } =
          await supabase.rpc("get_vehicle_categories_with_stats");

        if (categoriesError) throw categoriesError;

        // Fetch all cars (let CarCard component handle status display)
        const { data: carsData, error: carsError } = await supabase
          .from("cars")
          .select("*")
          .eq("is_archived", false);

        if (carsError) throw carsError;

        // Group cars by category type
        const grouped = (carsData || []).reduce(
          (acc: Record<string, Car[]>, car: Car) => {
            if (!acc[car.type]) {
              acc[car.type] = [];
            }
            acc[car.type].push(car);
            return acc;
          },
          {}
        );

        setCategories(categoriesData || []);
        setCarsByCategory(grouped);
      } catch (error) {
        console.error("Error fetching categories and cars:", error);
        setCategories([]);
        setCarsByCategory({});
      } finally {
        setLoading(false);
      }
    }

    fetchCategoriesAndCars();
  }, []);

  const availableCategories = categories.filter(
    (cat) => carsByCategory[cat.type]?.length > 0
  );

  // Filter cars in selected category by search term
  const filteredCars = React.useMemo(() => {
    if (!selectedCategory) return [];

    const categoryCars = carsByCategory[selectedCategory] || [];

    if (!searchTerm) return categoryCars;

    return categoryCars.filter((car) =>
      car.model.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [selectedCategory, carsByCategory, searchTerm]);

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
    setSearchTerm("");
  };

  const handleBackToCategories = () => {
    setSelectedCategory(null);
    setSearchTerm("");
  };

  if (loading) {
    return (
      <PublicAppShell>
        <CustomerVehicleCatalogSkeleton />
      </PublicAppShell>
    );
  }

  return (
    <PublicAppShell>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 xl:max-w-6xl">
        {!selectedCategory ? (
          // Show category overview
          <>
            <div className="text-center mb-8 max-w-4xl mx-auto">
              <h1 className="text-4xl font-bold mb-4 break-words">
                Our Vehicle Fleet
              </h1>
              <p className="text-lg text-gray-600 break-words">
                Discover our diverse range of vehicles, organized by category.
                Find the perfect car for your needs.
              </p>
            </div>

            <div className="mb-8 max-w-4xl mx-auto">
              <Card className="p-6 bg-gradient-to-r from-purple-50 to-blue-50 overflow-hidden">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 place-items-center text-center">
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <CarIcon className="h-12 w-12 text-purple-600 flex-shrink-0" />
                    <div className="text-2xl font-bold text-purple-600">
                      {categories.length}
                    </div>
                    <div className="text-gray-600 text-sm break-words">
                      Vehicle Categories
                    </div>
                  </div>
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <Users className="h-12 w-12 text-blue-600 flex-shrink-0" />
                    <div className="text-2xl font-bold text-blue-600">
                      {Object.values(carsByCategory).flat().filter((car: Car) => car.status === "Available").length}
                    </div>
                    <div className="text-gray-600 text-sm break-words">
                      Available Vehicles
                    </div>
                  </div>
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <Fuel className="h-12 w-12 text-green-600 flex-shrink-0" />
                    <div className="text-2xl font-bold text-green-600">
                      {
                        [
                          ...new Set(
                            Object.values(carsByCategory)
                              .flat()
                              .map((car: Car) => car.fuel_type)
                          ),
                        ].length
                      }
                    </div>
                    <div className="text-gray-600 text-sm break-words">
                      Fuel Types
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            <div className="mb-6 text-center max-w-3xl mx-auto">
              <h2 className="text-2xl font-bold mb-4 break-words">
                Browse by Category
              </h2>
              <p className="text-gray-600 mb-6 break-words">
                Select a category to view available vehicles and their
                specifications.
              </p>
            </div>

            <div className="flex flex-wrap justify-center gap-6 max-w-5xl mx-auto">
              {availableCategories.map((category) => (
                <CategoryCard
                  key={category.type}
                  category={category}
                  cars={carsByCategory[category.type] || []}
                  onSelect={handleCategorySelect}
                />
              ))}
            </div>
          </>
        ) : (
          // Show cars in selected category
          <>
            <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
              <Button
                variant="secondary"
                onClick={handleBackToCategories}
                className="flex items-center gap-2 w-full sm:w-auto"
              >
                <ArrowLeft className="h-4 w-4 flex-shrink-0" />
                <span>Back to Categories</span>
              </Button>
              <div className="min-w-0 flex-1">
                <h1 className="text-3xl font-bold break-words">
                  {selectedCategory} Vehicles
                </h1>
                <p className="text-gray-600 break-words">
                  {filteredCars.length} vehicle
                  {filteredCars.length !== 1 ? "s" : ""} available
                </p>
              </div>
            </div>

            <div className="mb-6 max-w-md mx-auto">
              <div className="flex gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 flex-shrink-0" />
                  <Input
                    placeholder={`Search ${selectedCategory.toLowerCase()} vehicles...`}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-full"
                  />
                </div>
              </div>
            </div>

            {filteredCars.length > 0 ? (
              <div className="flex flex-wrap justify-center gap-6 max-w-6xl mx-auto">
                {filteredCars.map((car) => (
                  <CarCard key={car.id} car={car} />
                ))}
              </div>
            ) : (
              <Card className="p-8 text-center max-w-md mx-auto overflow-hidden">
                <CarIcon className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-xl font-semibold mb-2 break-words">
                  No vehicles found
                </h3>
                <p className="text-gray-600 mb-4 break-words">
                  {searchTerm
                    ? `No ${selectedCategory.toLowerCase()} vehicles match "${searchTerm}"`
                    : `No ${selectedCategory.toLowerCase()} vehicles are currently available`}
                </p>
                {searchTerm && (
                  <Button
                    variant="secondary"
                    onClick={() => setSearchTerm("")}
                    className="w-full sm:w-auto"
                  >
                    Clear search
                  </Button>
                )}
              </Card>
            )}
          </>
        )}
      </div>
    </PublicAppShell>
  );
}
