import { PublicAppShell } from "@/components/layout/public-app-shell"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { CardSkeleton } from "@/components/ui/loading"
import { VehicleLoading } from "@/components/ui/loading"

export default function HomeLoading() {
  return (
    <PublicAppShell>
      <div className="space-y-8 px-4 md:px-6 py-8">
        {/* Hero section skeleton */}
        <div className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white rounded-2xl">
          <div className="px-4 md:px-6 py-16 md:py-24">
            <div className="max-w-7xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div className="space-y-8">
                  <div className="space-y-4">
                    <div className="h-4 bg-white/20 rounded w-32 animate-pulse"></div>
                    <div className="space-y-2">
                      <div className="h-12 bg-white/20 rounded w-full animate-pulse"></div>
                      <div className="h-12 bg-white/20 rounded w-4/5 animate-pulse"></div>
                    </div>
                    <div className="h-6 bg-white/20 rounded w-3/4 animate-pulse"></div>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="h-12 bg-white/20 rounded w-32 animate-pulse"></div>
                    <div className="h-12 bg-white/20 rounded w-32 animate-pulse"></div>
                  </div>
                </div>
                <div className="relative">
                  <div className="h-64 bg-white/20 rounded-2xl animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Featured vehicles skeleton */}
        <Card>
          <CardHeader>
            <CardTitle>Featured Vehicles</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Loading indicator */}
            <div className="flex flex-col items-center justify-center py-8">
              <VehicleLoading />
            </div>
            
            {/* Skeleton cards for recommended vehicles */}
            <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
              {Array.from({ length: 6 }, (_, i) => (
                <CardSkeleton key={i} />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </PublicAppShell>
  )
}
