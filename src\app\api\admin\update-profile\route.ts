import { NextRequest, NextResponse } from 'next/server'
import { createContextClient } from '@/lib/supabase/server'

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createContextClient('admin')
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin or super_admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || !['admin', 'super_admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Forbidden: Admin access required' }, { status: 403 })
    }

    // Get request body
    const body = await request.json()
    const { full_name, phone } = body

    // Update profile in database
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        full_name: full_name?.trim() || null,
        phone: phone?.trim() || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)

    if (updateError) {
      console.error('Profile update error:', updateError)
      return NextResponse.json({ error: updateError.message }, { status: 400 })
    }

    // Update user metadata in auth
    const { error: metadataError } = await supabase.auth.updateUser({
      data: {
        full_name: full_name?.trim() || null,
        phone: phone?.trim() || null
      }
    })

    if (metadataError) {
      console.error('Auth metadata update error:', metadataError)
      // Don't fail the request if metadata update fails
    }

    return NextResponse.json({ 
      success: true,
      message: 'Profile updated successfully'
    })

  } catch (error: any) {
    console.error('Profile update error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 })
  }
}
