"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useSearchParams } from "next/navigation";

import { DynamicGPSMap } from "@/components/admin/dynamic-gps-map";
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  fetchCurrentGPSLocations,
  getCurrentGPSLocations,
  calculateGPSStatistics,
  GPSLocation,
} from "@/lib/gps-data";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Activity,
  AlertTriangle,
  Car,
  ChevronUp,
  Clock,
  MapPin,
  Navigation,
  RotateCcw,
  Search,
  User,
  X,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAdminAuth } from "@/components/auth/admin-auth-context";

// GPS Data Encryption/Decryption
const ENCRYPT_KEY = "OllieGPS2024";

function decryptGPSToken(encryptedToken: string): string {
  try {
    // Ensure the token is in the correct format
    if (!/^[0-9a-f]+$/i.test(encryptedToken)) {
      console.error('Invalid token format (not hex):', encryptedToken);
      throw new Error('Invalid token format (not hex)');
    }
    
    const hexPairs = encryptedToken.match(/.{1,2}/g) || [];
    let decrypted = "";
    
    for (let i = 0; i < hexPairs.length; i++) {
      const hexChar = parseInt(hexPairs[i], 16);
      const keyChar = ENCRYPT_KEY.charCodeAt(i % ENCRYPT_KEY.length);
      const decryptedChar = String.fromCharCode(hexChar ^ keyChar);
      decrypted += decryptedChar;
    }
    
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log('Client-side decrypted:', decrypted); // Debug
    }
    return decrypted;
  } catch (error) {
    console.error('Decryption failed:', error);
    throw new Error('Invalid GPS token');
  }
}

interface TrackerFilters {
  selectedVehicleId: string;
  categoryFilter: string;
  branchFilter: string;
  timeWindow: "live" | "15m" | "1h" | "24h";
  sortBy: "lastSeen" | "speed" | "distance";
  showClusters: boolean;
  showTrails: boolean;
  showGeofences: boolean;
  denseList: boolean;
}

const initialFilters: TrackerFilters = {
  selectedVehicleId: "all",
  categoryFilter: "all",
  branchFilter: "all",
  timeWindow: "live",
  sortBy: "lastSeen",
  showClusters: true,
  showTrails: false,
  showGeofences: false,
  denseList: false,
};

export default function TrackerPage() {
  const { loading: authLoading } = useAdminAuth();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const searchParams = useSearchParams();

  // Using MQTT data directly - no token fetching needed


  // Parse Arduino ESP32 GPS data from URL parameters (now with encryption)
  const parseESP32GPSFromURL = useCallback((): GPSLocation | null => {
    // Check for encrypted GPS token
    const gpsToken = searchParams.get('gps');
    
    if (gpsToken) {
      try {
        if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
          console.log('Attempting to decrypt GPS token:', gpsToken.substring(0, 20) + '...');
        }
        
        // Decrypt the GPS token
        const decryptedData = decryptGPSToken(gpsToken);
        const parts = decryptedData.split(',');
        
        if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
          console.log('Decrypted parts:', parts);
        }
        
        if (parts.length >= 4) {
          const latitude = parseFloat(parts[0]);
          const longitude = parseFloat(parts[1]);
          const accuracy = parseFloat(parts[2]);
          const deviceId = parts[3];
          
          if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
            console.log('Parsed GPS values:', { latitude, longitude, accuracy, deviceId });
          }
          
          // Only create location if GPS coordinates are valid and within reasonable bounds
          if (!isNaN(latitude) && !isNaN(longitude) && 
              latitude >= -90 && latitude <= 90 && 
              longitude >= -180 && longitude <= 180 &&
              latitude !== 0 && longitude !== 0) { // Exclude null island
            
            return {
              id: `esp32-encrypted-${deviceId}`,
              carId: deviceId || 'lilygo-esp32-01',
              carModel: 'LilyGO T-Call A7670E',
              carPlate: 'ESP32-GPS',
              latitude,
              longitude,
              speed: 0,
              heading: 0,
              timestamp: new Date(),
              status: 'active' as const,
              driverName: 'Arduino ESP32'
            };
          }
        }
      } catch (error) {
        console.error('Failed to decrypt GPS token:', error);
        return null;
      }
    }
    
    // Fallback to old unencrypted format for backward compatibility
    const lat = searchParams.get('lat');
    const lon = searchParams.get('lon');
    const acc = searchParams.get('acc');
    const carId = searchParams.get('carId');
    
    if (!lat || !lon) return null;
    
    const latitude = parseFloat(lat);
    const longitude = parseFloat(lon);
    
    // Only create location if GPS coordinates are valid and within reasonable bounds
    if (isNaN(latitude) || isNaN(longitude) ||
        latitude < -90 || latitude > 90 ||
        longitude < -180 || longitude > 180 ||
        (latitude === 0 && longitude === 0)) { // Exclude null island
      return null;
    }
    
    return {
      id: `esp32-url-${carId || 'unknown'}`,
      carId: carId || 'lilygo-esp32-01',
      carModel: 'LilyGO T-Call A7670E',
      carPlate: 'ESP32-GPS',
      latitude,
      longitude,
      speed: 0, // Arduino doesn't send speed in URL currently
      heading: 0, // Arduino doesn't send heading in URL currently
      timestamp: new Date(),
      status: 'active' as const,
      driverName: 'Arduino ESP32'
    };
  }, [searchParams]);

  // Core state
  const [locations, setLocations] = useState<GPSLocation[]>([]);
  const [selectedCarId, setSelectedCarId] = useState<string>("");
  const [filters, setFilters] = useState<TrackerFilters>(initialFilters);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<
    "live" | "reconnecting" | "offline"
  >("live");

  // Load initial data - combine API data with URL parameter data
  useEffect(() => {
    const load = async () => {
      try {
        // Load GPS data from MQTT/database system (no more token dependency)
        const apiLocations = await fetchCurrentGPSLocations()
        
        // Check for ESP32 GPS data from URL parameters (only if no API data)
        const esp32Location = apiLocations.length === 0 ? parseESP32GPSFromURL() : null;
        
        // Combine API data with ESP32 data if needed
        const allLocations = [...apiLocations, ...(esp32Location ? [esp32Location] : [])];
        
        setLocations(allLocations);
        setLastUpdated(new Date());
        
        // Determine connection status based on fresh GPS data
        const stats = calculateGPSStatistics(allLocations);
        const hasFreshData = stats.activeCars > 0 || stats.idleCars > 0;
        setConnectionStatus(hasFreshData ? "live" : "offline");
        
        // Auto-select ESP32 device if it comes from URL
        if (esp32Location) {
          setSelectedCarId(esp32Location.carId);
        }
        
        // Show appropriate toast based on actual data status
        if (hasFreshData) {
          toast({
            title: "GPS Data Loaded",
            description: `Found ${stats.activeCars + stats.idleCars} active device(s)`,
          });
        } else if (allLocations.length > 0) {
          toast({
            title: "GPS Data Stale",
            description: "No recent GPS data from devices",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Failed to load GPS data:", error);
        setConnectionStatus("offline");
        setLocations([]);
        
        toast({
          title: "Connection Error",
          description: "Failed to load GPS data. Please check your connection.",
          variant: "destructive",
        });
      }
    };
    void load();
  }, [toast, parseESP32GPSFromURL, searchParams]);

  // Set up real-time updates - combine MQTT/API data with URL parameter data
  useEffect(() => {
    if (!isAutoRefresh) return;

    const interval = setInterval(async () => {
      try {
        // Load GPS data from MQTT/database system
        const apiLocations = await fetchCurrentGPSLocations()
        
        // Check for ESP32 GPS data from URL parameters (only if no API data)
        const esp32Location = apiLocations.length === 0 ? parseESP32GPSFromURL() : null;
        
        // Combine API data with ESP32 data if needed
        const allLocations = [...apiLocations, ...(esp32Location ? [esp32Location] : [])];
        
        setLocations(allLocations);
        setLastUpdated(new Date());
        
        // Determine connection status based on fresh GPS data
        const stats = calculateGPSStatistics(allLocations);
        const hasFreshData = stats.activeCars > 0 || stats.idleCars > 0;
        setConnectionStatus(hasFreshData ? "live" : "offline");
        
      } catch (error) {
        console.error("Failed to update GPS data:", error);
        setConnectionStatus("offline");
        setLocations([]);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoRefresh, parseESP32GPSFromURL]);

  // Filter and sort vehicles
  const filteredAndSortedLocations = useMemo(() => {
    let filtered = locations.filter((location) => {
      // Vehicle filter - show all vehicles if "all" is selected, otherwise show only selected vehicle
      if (filters.selectedVehicleId === "all") {
        return true;
      }
      return location.carId === filters.selectedVehicleId;
    });

    // Sort vehicles
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case "lastSeen":
          return b.timestamp.getTime() - a.timestamp.getTime();
        case "speed":
          return b.speed - a.speed;
        case "distance":
          // For now, use latitude as proxy for distance from center
          return (
            Math.abs(a.latitude - 14.5995) - Math.abs(b.latitude - 14.5995)
          );
        default:
          return 0;
      }
    });

    return filtered;
  }, [locations, filters]);

  // Get statistics using the new calculation function
  const stats = useMemo(() => {
    return calculateGPSStatistics(locations)
  }, [locations]);

  // Update connection status based on fresh GPS data (removed - now handled in data loading)

  // Handlers
  const handleCarSelect = useCallback((carId: string) => {
    setSelectedCarId(carId);
  }, []);

  const handleRefreshData = useCallback(() => {
    const run = async () => {
      try {
        // Load GPS data from MQTT/database system
        const apiLocations = await fetchCurrentGPSLocations()
        
        // Check for ESP32 GPS data from URL parameters (only if no API data)
        const esp32Location = apiLocations.length === 0 ? parseESP32GPSFromURL() : null;
        
        // Combine API data with ESP32 data if needed
        const allLocations = [...apiLocations, ...(esp32Location ? [esp32Location] : [])];
        
        setLocations(allLocations);
        setLastUpdated(new Date());
        
        // Determine connection status based on fresh GPS data
        const stats = calculateGPSStatistics(allLocations);
        const hasFreshData = stats.activeCars > 0 || stats.idleCars > 0;
        setConnectionStatus(hasFreshData ? "live" : "offline");
        
        if (hasFreshData) {
          toast({
            title: "Data Refreshed",
            description: `Updated GPS data for ${stats.activeCars + stats.idleCars} device(s)`,
          });
        } else if (allLocations.length > 0) {
          toast({
            title: "Data Refreshed - Stale Data",
            description: "GPS data is outdated. Devices may be offline.",
            variant: "destructive",
          });
        } else {
          toast({
            title: "No GPS Data",
            description: "No GPS data available from any devices",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Failed to refresh GPS data:", error);
        setConnectionStatus("offline");
        setLocations([]);
        
        toast({
          title: "Refresh Failed",
          description: "Failed to refresh GPS data. Please try again.",
          variant: "destructive",
        });
      }
    };
    void run();
  }, [toast, parseESP32GPSFromURL]);


  // Selected car details for the bottom sheet
  const selectedCar = useMemo(() => {
    return locations.find((l) => l.carId === selectedCarId);
  }, [locations, selectedCarId]);

  // Modal states
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  // Wait for auth loading to complete before rendering
  if (authLoading) {
    return (
      <div className="space-y-4 p-2 xs:p-3 sm:p-4">
        <header className="space-y-2">
          <h1 className="text-xl xs:text-2xl sm:text-3xl font-bold tracking-tight text-foreground truncate">Live Fleet Tracker</h1>
          <p className="text-sm sm:text-base text-muted-foreground">Monitor your vehicles in real-time.</p>
        </header>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3" />
            <span className="text-muted-foreground">Loading...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4 p-2 xs:p-3 sm:p-4">
      <header className="space-y-2">
        <h1 className="text-xl xs:text-2xl sm:text-3xl font-bold tracking-tight text-foreground truncate">Live Fleet Tracker</h1>
        <p className="text-sm sm:text-base text-muted-foreground">Monitor your vehicles in real-time.</p>
      </header>
      <div className="flex h-[calc(100vh-7rem)] xs:h-[calc(100vh-8rem)] bg-background overflow-hidden">
      <div className="flex flex-col w-full">
        {/* Header */}
        <div className="px-2 xs:px-3 sm:px-4 md:px-6 py-3 md:py-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div
                className={cn(
                  "flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium",
                  connectionStatus === "live" && "bg-green-100 text-green-800",
                  connectionStatus === "offline" && "bg-red-100 text-red-800"
                )}
              >
                <div
                  className={cn(
                    "w-2 h-2 rounded-full",
                    connectionStatus === "live" && "bg-green-500 animate-pulse",
                    connectionStatus === "offline" && "bg-red-500"
                  )}
                />
                {connectionStatus === "live" ? "Live" : "Offline"}
              </div>
              
              {/* Show refresh button on desktop, moved to FAB on mobile */}
              <Button 
                variant="secondary" 
                size="sm" 
                onClick={handleRefreshData}
                className="hidden md:flex"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
            
            {/* Mobile/Tablet Action Buttons */}
            <div className="flex items-center lg:hidden">
              {/* Action buttons container with proper spacing and background */}
              <div className="flex items-center rounded-lg bg-background border border-border p-0.5 xs:p-1 shadow-sm">
                {/* Utility buttons group */}
                <div className="flex items-center gap-1">
                  <Button 
                    variant="secondary" 
                    size="sm" 
                    onClick={handleRefreshData}
                    className="h-6 xs:h-7 w-6 xs:w-7 p-0 flex items-center justify-center rounded-md"
                    title="Refresh"
                  >
                    <RotateCcw className="w-3.5 h-3.5" />
                  </Button>
                  <Sheet>
                    <SheetTrigger asChild>
                      <Button 
                        variant="secondary" 
                        size="sm" 
                        className="h-6 xs:h-7 w-6 xs:w-7 p-0 flex items-center justify-center rounded-md"
                        title="Select Vehicle"
                      >
                        <Search className="w-3.5 h-3.5" />
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="bottom" className="h-[60vh] rounded-t-xl">
                      <SheetHeader>
                        <SheetTitle>Select Vehicle</SheetTitle>
                      </SheetHeader>
                      <div className="pt-4">
                        <Select
                          value={filters.selectedVehicleId}
                          onValueChange={(value) =>
                            setFilters((prev) => ({
                              ...prev,
                              selectedVehicleId: value,
                            }))
                          }
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select a vehicle..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Vehicles</SelectItem>
                            {locations.map((location) => (
                              <SelectItem key={location.carId} value={location.carId}>
                                {location.carPlate} - {location.carModel}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </SheetContent>
                  </Sheet>
                  <Button 
                    variant={filters.showTrails ? "primary" : "secondary"}
                    size="sm" 
                    onClick={() => setFilters(prev => ({ ...prev, showTrails: !prev.showTrails }))}
                    className="h-6 xs:h-7 w-6 xs:w-7 p-0 flex items-center justify-center rounded-md"
                    title="Toggle Trails"
                  >
                    <Navigation className="w-3.5 h-3.5" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* KPI Stats - Hidden on mobile/tablet, visible on desktop */}
        <div className="px-6 py-4 border-b overflow-x-auto hidden lg:block">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 min-w-[600px] md:min-w-0">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Car className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total</p>
                    <p className="text-2xl font-bold">{stats.totalCars}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Activity className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Active</p>
                    <p className="text-2xl font-bold">{stats.activeCars}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Clock className="w-5 h-5 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Idle</p>
                    <p className="text-2xl font-bold">{stats.idleCars}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-red-100 rounded-lg">
                    <AlertTriangle className="w-5 h-5 text-red-600" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Offline</p>
                    <p className="text-2xl font-bold">{stats.offlineCars}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Toolbar - Hide on mobile, moved to FAB */}
        <div className="px-6 py-4 border-b hidden md:block">
          <div className="flex items-center gap-4 mb-3">
            <div className="flex-1 max-w-md">
              <Select
                value={filters.selectedVehicleId}
                onValueChange={(value) =>
                  setFilters((prev) => ({
                    ...prev,
                    selectedVehicleId: value,
                  }))
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select a vehicle..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Vehicles</SelectItem>
                  {locations.map((location) => (
                    <SelectItem key={location.carId} value={location.carId}>
                      {location.carPlate} - {location.carModel}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Badge variant="outline">
              {filteredAndSortedLocations.length} vehicles
            </Badge>
          </div>
          
          {/* Map Display Controls */}
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-muted-foreground">Time Window:</span>
              <div className="flex gap-2">
                {(['live', '15m', '1h', '24h'] as const).map((window) => (
                  <Button
                    key={window}
                    variant={filters.timeWindow === window ? "primary" : "secondary"}
                    size="sm"
                    onClick={() => setFilters(prev => ({ ...prev, timeWindow: window }))}
                  >
                    {window === 'live' ? 'Live' : window}
                  </Button>
                ))}
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-muted-foreground">Display:</span>
              <div className="flex gap-3">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.showTrails}
                    onChange={(e) => setFilters(prev => ({ ...prev, showTrails: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Show Trails</span>
                </label>
                
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.showClusters}
                    onChange={(e) => setFilters(prev => ({ ...prev, showClusters: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Show Clusters</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex min-h-[300px]">
          {/* Map Container - Full width on mobile/tablet */}
          <div className="flex-1 relative min-h-[300px] min-w-0 w-full">
            <DynamicGPSMap
              selectedCarId={selectedCarId}
              followingCarId=""
              onCarSelect={(carId) => {
                handleCarSelect(carId);
                if (isMobile) {
                  setIsSheetOpen(true);
                }
              }}
              showClusters={filters.showClusters}
              showTrails={filters.showTrails}
              showGeofences={filters.showGeofences}
              timeWindow={filters.timeWindow}
              className="h-full w-full"
              // Pass locations and ESP32 URL params to map
              locations={locations}
              initialCenter={(() => {
                const esp32Location = parseESP32GPSFromURL();
                return esp32Location ? [esp32Location.latitude, esp32Location.longitude] : undefined;
              })()}
              initialZoom={searchParams.get('z') ? parseInt(searchParams.get('z')!) : undefined}
            />
            
            {/* Floating action buttons removed and moved to header */}
            
            {/* Mobile Bottom Sheet Trigger */}
            {isMobile && selectedCarId && (
              <div className="fixed bottom-4 left-0 right-0 flex justify-center z-10">
                <Button 
                  onClick={() => setIsSheetOpen(true)}
                  className="px-4 xs:px-6 py-2 rounded-full shadow-lg"
                  variant="secondary"
                >
                  <ChevronUp className="w-4 xs:w-5 h-4 xs:h-5 mr-1 xs:mr-2" />
                  <span className="text-xs xs:text-sm">View Details</span>
                </Button>
              </div>
            )}
          </div>
          
          {/* Desktop Side Panel - Hidden on mobile/tablet */}
          <div className="space-y-4 h-full flex-col w-1/3 hidden lg:flex">
            <Card className="flex-shrink-0">
              <CardHeader>
                <CardTitle className="text-sm">
                  {selectedCar ? 'Selected Vehicle' : 'Select a vehicle on the map'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {selectedCar ? (
                  <div className="space-y-4">
                    <div>
                      <div className="font-semibold">{selectedCar.carModel}</div>
                      <div className="text-sm text-muted-foreground">{selectedCar.carPlate}</div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Speed</span>
                        <span className="font-medium">{selectedCar.speed} km/h</span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Heading</span>
                        <span className="font-medium">{selectedCar.heading}°</span>
                      </div>
                      
                      {/* Show GPS accuracy for ESP32 devices */}
                      {selectedCar.carId?.includes('esp32') && searchParams.get('acc') && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm">GPS Accuracy</span>
                          <span className="font-medium">{searchParams.get('acc')} m</span>
                        </div>
                      )}
                      
                      {/* Show coordinates for ESP32 devices */}
                      {selectedCar.carId?.includes('esp32') && (
                        <>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Latitude</span>
                            <span className="font-medium text-xs">{selectedCar.latitude.toFixed(6)}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Longitude</span>
                            <span className="font-medium text-xs">{selectedCar.longitude.toFixed(6)}</span>
                          </div>
                        </>
                      )}
                    </div>
                    
                    {selectedCar.driverName && (
                      <div className="pt-2 border-t">
                        <div className="flex items-center gap-2 text-sm">
                          <User className="w-4 h-4" />
                          <span className="font-medium">{selectedCar.driverName}</span>
                        </div>
                      </div>
                    )}
                    
                    <div className="pt-2 border-t">
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="w-3 h-3" />
                        Last updated: {selectedCar.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">
                    Click on a car marker on the map to view detailed information.
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Quick Stats */}
            <Card className="flex-1">
              <CardHeader>
                <CardTitle className="text-sm">Fleet Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {locations.length}
                  </div>
                  <div className="text-muted-foreground">Total Vehicles</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
    
    {/* Mobile Bottom Sheet */}
    <Sheet open={isSheetOpen && isMobile} onOpenChange={setIsSheetOpen}>
      <SheetContent side="bottom" className="h-[60vh] rounded-t-xl">
        <SheetHeader className="flex justify-between items-center">
          <SheetTitle>Vehicle Details</SheetTitle>
          <Button variant="secondary" size="icon" onClick={() => setIsSheetOpen(false)}>
            <X className="h-4 w-4" />
          </Button>
        </SheetHeader>
        
        {selectedCar ? (
          <div className="space-y-6 pt-4">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">{selectedCar.carModel}</h3>
              <p className="text-muted-foreground">{selectedCar.carPlate}</p>
            </div>
            
            <div className="grid grid-cols-1 gap-3">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Activity className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="text-sm text-muted-foreground">Speed</p>
                      <p className="text-xl font-bold">{selectedCar.speed} km/h</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Navigation className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="text-sm text-muted-foreground">Heading</p>
                      <p className="text-xl font-bold">{selectedCar.heading}°</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Show GPS accuracy for ESP32 devices */}
              {selectedCar.carId?.includes('esp32') && searchParams.get('acc') && (
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <MapPin className="w-5 h-5 text-blue-600" />
                      <div>
                        <p className="text-sm text-muted-foreground">GPS Accuracy</p>
                        <p className="text-xl font-bold">{searchParams.get('acc')} m</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
            
            {selectedCar.driverName && (
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <User className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="text-sm text-muted-foreground">Driver</p>
                      <p className="text-xl font-bold">{selectedCar.driverName}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
            
            <div className="pt-2">
              <p className="text-sm text-muted-foreground flex items-center gap-1">
                <Clock className="w-4 h-4" />
                Last updated: {selectedCar.timestamp.toLocaleTimeString()}
              </p>
            </div>
            
            <div className="pt-4 border-t">
              <h3 className="text-lg font-semibold mb-4">Fleet Overview</h3>
              <div className="grid grid-cols-1 gap-3">
                <Card>
                  <CardContent className="p-4 text-center">
                    <p className="text-sm text-muted-foreground">Total</p>
                    <p className="text-2xl font-bold text-blue-600">{stats.totalCars}</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4 text-center">
                    <p className="text-sm text-muted-foreground">Active</p>
                    <p className="text-2xl font-bold text-green-600">{stats.activeCars}</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4 text-center">
                    <p className="text-sm text-muted-foreground">Idle</p>
                    <p className="text-2xl font-bold text-yellow-600">{stats.idleCars}</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4 text-center">
                    <p className="text-sm text-muted-foreground">Offline</p>
                    <p className="text-2xl font-bold text-red-600">{stats.offlineCars}</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <p className="text-muted-foreground">Select a vehicle on the map</p>
          </div>
        )}
      </SheetContent>
    </Sheet>

    {/* Status Modal for Mobile/Tablet */}
    <Sheet open={isStatusModalOpen} onOpenChange={setIsStatusModalOpen}>
      <SheetContent side="bottom" className="h-[65vh] rounded-t-xl">
        <SheetHeader className="flex justify-between items-center">
          <SheetTitle>Fleet Status</SheetTitle>
          <Button variant="secondary" size="icon" onClick={() => setIsStatusModalOpen(false)}>
            <X className="h-4 w-4" />
          </Button>
        </SheetHeader>
        
        <div className="space-y-3 pt-3 overflow-y-auto max-h-[calc(65vh-3.5rem)]">
          <div className="grid grid-cols-1 gap-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Fleet Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  <div className="flex items-center gap-2 p-2 bg-blue-50 rounded-lg">
                    <div className="p-1.5 bg-blue-100 rounded-md">
                      <Car className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="text-xl font-bold">{stats.totalCars}</div>
                      <div className="text-xs text-muted-foreground">Total</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
                    <div className="p-1.5 bg-green-100 rounded-md">
                      <Activity className="w-4 h-4 text-green-600" />
                    </div>
                    <div>
                      <div className="text-xl font-bold">{stats.activeCars}</div>
                      <div className="text-xs text-muted-foreground">Active</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-yellow-50 rounded-lg">
                    <div className="p-1.5 bg-yellow-100 rounded-md">
                      <Clock className="w-4 h-4 text-yellow-600" />
                    </div>
                    <div>
                      <div className="text-xl font-bold">{stats.idleCars}</div>
                      <div className="text-xs text-muted-foreground">Idle</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-red-50 rounded-lg">
                    <div className="p-1.5 bg-red-100 rounded-md">
                      <AlertTriangle className="w-4 h-4 text-red-600" />
                    </div>
                    <div>
                      <div className="text-xl font-bold">{stats.offlineCars}</div>
                      <div className="text-xs text-muted-foreground">Offline</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-2 gap-3">
              <Card className="p-3">
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs font-medium">Avg Speed</span>
                    <span className="text-xs font-medium">{stats.averageSpeed.toFixed(1)} km/h</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div 
                      className="bg-blue-600 h-1.5 rounded-full" 
                      style={{ width: `${Math.min(100, (stats.averageSpeed / 120) * 100)}%` }}
                    ></div>
                  </div>
                </div>
              </Card>
              <Card className="p-3">
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs font-medium">Active Rate</span>
                    <span className="text-xs font-medium">
                      {stats.totalCars > 0 ? ((stats.activeCars / stats.totalCars) * 100).toFixed(0) : 0}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div 
                      className="bg-green-600 h-1.5 rounded-full" 
                      style={{ width: `${stats.totalCars > 0 ? (stats.activeCars / stats.totalCars) * 100 : 0}%` }}
                    ></div>
                  </div>
                </div>
              </Card>
            </div>

            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1.5">
                  <div
                    className={cn(
                      "w-2 h-2 rounded-full",
                      connectionStatus === "live" && "bg-green-500 animate-pulse",
                      connectionStatus === "offline" && "bg-red-500"
                    )}
                  />
                  <span className="text-xs font-medium">
                    {connectionStatus === "live" ? "Live" : "Offline"}
                  </span>
                </div>
                <span className="text-xs text-muted-foreground">
                  Updated: {lastUpdated.toLocaleTimeString()}
                </span>
              </div>
            </Card>
          </div>
        </div>
      </SheetContent>
    </Sheet>

    {/* Details Modal for Mobile/Tablet */}
    <Sheet open={isDetailsModalOpen} onOpenChange={setIsDetailsModalOpen}>
      <SheetContent side="bottom" className="h-[65vh] rounded-t-xl">
        <SheetHeader className="flex justify-between items-center">
          <SheetTitle>Fleet Details</SheetTitle>
          <Button variant="secondary" size="icon" onClick={() => setIsDetailsModalOpen(false)}>
            <X className="h-4 w-4" />
          </Button>
        </SheetHeader>
        
        <div className="space-y-3 pt-3 overflow-y-auto max-h-[calc(65vh-3.5rem)]">
          <div className="relative">
            <div className="relative mb-4">
              <Select
                value={filters.selectedVehicleId}
                onValueChange={(value) =>
                  setFilters((prev) => ({
                    ...prev,
                    selectedVehicleId: value,
                  }))
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select a vehicle..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Vehicles</SelectItem>
                  {locations.map((location) => (
                    <SelectItem key={location.carId} value={location.carId}>
                      {location.carPlate} - {location.carModel}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Badge variant="secondary" className="mb-4 inline-block">
              {filteredAndSortedLocations.length} vehicles
            </Badge>
          </div>

          <div className="space-y-4">
            {filteredAndSortedLocations.length > 0 ? (
              filteredAndSortedLocations.map((car) => (
                <Card key={car.carId} className={cn(
                  "cursor-pointer transition-all",
                  car.carId === selectedCarId && "border-primary"
                )} onClick={() => {
                  handleCarSelect(car.carId);
                  setIsDetailsModalOpen(false);
                  setIsSheetOpen(true);
                }}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold">{car.carModel}</h3>
                        <p className="text-sm text-muted-foreground">{car.carPlate}</p>
                      </div>
                      <Badge className={cn(
                        car.status === "active" && "bg-green-100 text-green-800",
                        car.status === "idle" && "bg-yellow-100 text-yellow-800",
                        car.status === "offline" && "bg-red-100 text-red-800"
                      )}>
                        {car.status.charAt(0).toUpperCase() + car.status.slice(1)}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 mt-3">
                      <div className="text-sm">
                        <span className="text-muted-foreground">Speed:</span> {car.speed} km/h
                      </div>
                      {car.driverName && (
                        <div className="text-sm">
                          <span className="text-muted-foreground">Driver:</span> {car.driverName}
                        </div>
                      )}
                    </div>
                    
                    <div className="text-xs text-muted-foreground mt-2">
                      Updated: {car.timestamp.toLocaleTimeString()}
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No vehicles match your search</p>
              </div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>

    </div>
  );
}
