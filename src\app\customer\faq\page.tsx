"use client"

import { PublicAppShell } from "@/components/layout/public-app-shell"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Search, Phone, Mail, MessageCircle, MapPin, Clock, Star, Filter, BookOpen, HelpCircle, ChevronRight, Users, Shield, CreditCard, Settings } from "lucide-react"
import Link from "next/link"
import * as React from "react"

// Enhanced FAQ Topics with better structure
const faqTopics = [
  {
    id: "getting-started",
    title: "Getting Started",
    icon: BookOpen,
    color: "bg-blue-50 border-blue-200 text-blue-700",
    description: "Essential information for new customers"
  },
  {
    id: "booking-process",
    title: "Booking & Reservations",
    icon: Clock,
    color: "bg-green-50 border-green-200 text-green-700",
    description: "How to book and manage your rental"
  },
  {
    id: "pricing-payment",
    title: "Pricing & Payment",
    icon: CreditCard,
    color: "bg-yellow-50 border-yellow-200 text-yellow-700",
    description: "Rates, discounts, and payment options"
  },
  {
    id: "services",
    title: "Services & Options",
    icon: Settings,
    color: "bg-purple-50 border-purple-200 text-purple-700",
    description: "Available services and add-ons"
  },
  {
    id: "policies",
    title: "Policies & Terms",
    icon: Shield,
    color: "bg-red-50 border-red-200 text-red-700",
    description: "Rules, policies, and terms of service"
  },
  {
    id: "support",
    title: "Customer Support",
    icon: Users,
    color: "bg-indigo-50 border-indigo-200 text-indigo-700",
    description: "Getting help and customer service"
  }
]

const faqData = [
  // GETTING STARTED
  {
    id: "requirements",
    topic: "getting-started",
    question: "What are the requirements to rent a vehicle?",
    keywords: ["requirements", "documents", "age", "license", "deposit", "ID"],
    answer: (
      <div className="space-y-4">
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <p className="font-semibold text-blue-900 mb-2">✅ Quick Checklist:</p>
          <ul className="list-disc pl-6 space-y-1 text-sm text-blue-800">
            <li>Be at least 24 years old</li>
            <li>Valid driver's license (local or foreign)</li>
            <li>Government-issued ID</li>
            <li>Proof of billing address</li>
            <li>50% deposit to reserve</li>
          </ul>
        </div>
        <div>
          <p className="font-semibold mb-2">Detailed Requirements:</p>
          <ul className="list-disc pl-6 space-y-2 text-sm text-gray-600">
            <li><strong>50% deposit</strong> - Non-refundable but can be rebooked within 6 months</li>
            <li><strong>Valid Driver's License</strong> - Minimum 24 years old, local or foreign accepted</li>
            <li><strong>Government-issued ID</strong> - Will be collected and returned at rental end</li>
            <li><strong>Proof of Billing Address</strong> - Electric, water, or any utility bill</li>
            <li><strong>Emergency Contact</strong> - Phone number and Facebook account</li>
          </ul>
        </div>
      </div>
    )
  },
  {
    id: "foreign-license",
    topic: "getting-started",
    question: "Can I use my foreign driver's license?",
    keywords: ["foreign", "license", "international", "tourist", "90 days"],
    answer: (
      <div className="space-y-3">
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <p className="font-semibold text-green-900">✅ Yes, you can use your foreign license!</p>
        </div>
        <p>Foreign valid driver's licenses can be used in the Philippines for up to <span className="font-semibold">90 days</span> after arrival, provided the license is in English or has an official English translation.</p>
        <p className="text-sm text-gray-600"><span className="font-medium">Tip:</span> Bring both your original license and passport for verification.</p>
      </div>
    )
  },

  // BOOKING PROCESS
  {
    id: "how-to-book",
    topic: "booking-process",
    question: "How do I book a vehicle?",
    keywords: ["book", "reserve", "rental", "process", "messenger"],
    answer: (
      <div className="space-y-4">
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <p className="font-semibold text-green-900 mb-2">🚗 Simple Booking Process:</p>
          <ol className="list-decimal pl-6 space-y-2 text-sm text-green-800">
            <li>Browse our vehicle catalog or use the quick booking form</li>
            <li>Contact us via Messenger with your preferred vehicle and dates</li>
            <li>Provide your requirements and complete the booking details</li>
            <li>Pay 50% deposit to secure your reservation</li>
            <li>Receive confirmation and pickup instructions</li>
          </ol>
        </div>
        <p>We recommend booking at least 24-48 hours in advance to ensure availability of your preferred vehicle.</p>
      </div>
    )
  },
  {
    id: "cancellation",
    topic: "booking-process",
    question: "What is your cancellation policy?",
    keywords: ["cancel", "refund", "policy", "deposit", "rebooking"],
    answer: (
      <div className="space-y-3">
        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
          <p className="font-semibold text-yellow-900">📋 Cancellation Policy:</p>
          <ul className="list-disc pl-6 space-y-1 text-sm text-yellow-800 mt-2">
            <li>Deposits are non-refundable</li>
            <li>Can be rebooked within 6 months</li>
            <li>Subject to vehicle availability</li>
          </ul>
        </div>
        <p>While deposits cannot be refunded, we offer flexible rebooking options to ensure you can still enjoy our services when it's convenient for you.</p>
      </div>
    )
  },

  // PRICING & PAYMENT
  {
    id: "pricing",
    topic: "pricing-payment",
    question: "How much does it cost to rent a vehicle?",
    keywords: ["price", "cost", "rate", "fee", "daily", "weekly"],
    answer: (
      <div className="space-y-4">
        <p>Our rental rates vary by vehicle type, rental duration, and season. Contact us via Messenger for current pricing and availability.</p>
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <p className="font-semibold text-blue-900 mb-2">💰 Pricing Factors:</p>
          <ul className="list-disc pl-6 space-y-1 text-sm text-blue-800">
            <li>Vehicle type and model</li>
            <li>Rental duration (daily, weekly rates available)</li>
            <li>Peak season vs. regular season</li>
            <li>Additional services (driver, insurance, etc.)</li>
          </ul>
        </div>
        <p className="text-sm text-gray-600">Special discounts available for extended rentals and repeat customers!</p>
      </div>
    )
  },
  {
    id: "payment-methods",
    topic: "pricing-payment",
    question: "What payment methods do you accept?",
    keywords: ["payment", "cash", "card", "bank transfer", "gcash"],
    answer: (
      <div className="space-y-3">
        <p>We accept various payment methods for your convenience:</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div className="bg-gray-50 p-3 rounded-lg">
            <p className="font-medium text-gray-900 mb-1">Cash Payment</p>
            <p className="text-sm text-gray-600">Pay in person at pickup</p>
          </div>
          <div className="bg-gray-50 p-3 rounded-lg">
            <p className="font-medium text-gray-900 mb-1">Bank Transfer</p>
            <p className="text-sm text-gray-600">Secure online transfer</p>
          </div>
          <div className="bg-gray-50 p-3 rounded-lg">
            <p className="font-medium text-gray-900 mb-1">Digital Wallets</p>
            <p className="text-sm text-gray-600">GCash, PayMaya, and more</p>
          </div>
          <div className="bg-gray-50 p-3 rounded-lg">
            <p className="font-medium text-gray-900 mb-1">Credit/Debit Card</p>
            <p className="text-sm text-gray-600">Visa, Mastercard accepted</p>
          </div>
        </div>
      </div>
    )
  },

  // SUPPORT
  {
    id: "contact-support",
    topic: "support",
    question: "How can I contact customer support?",
    keywords: ["contact", "support", "help", "messenger", "phone", "email"],
    answer: (
      <div className="space-y-4">
        <p>Our customer support team is available 24/7 to assist you:</p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 text-center">
            <MessageCircle className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <p className="font-medium text-blue-900">Messenger</p>
            <p className="text-sm text-blue-700">Instant chat support</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg border border-green-200 text-center">
            <Phone className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <p className="font-medium text-green-900">Phone</p>
            <p className="text-sm text-green-700">Direct call support</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg border border-purple-200 text-center">
            <Mail className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <p className="font-medium text-purple-900">Email</p>
            <p className="text-sm text-purple-700">Send us a message</p>
          </div>
        </div>
        <p className="text-sm text-gray-600">Response time is typically under 5 minutes during business hours.</p>
      </div>
    )
  }
]

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = React.useState("")
  const [selectedTopic, setSelectedTopic] = React.useState<string | null>(null)
  const [openItems, setOpenItems] = React.useState<string[]>([])

  // Calculate topic statistics
  const topicStats = faqTopics.map(topic => ({
    ...topic,
    count: faqData.filter(faq => faq.topic === topic.id).length
  }))

  // Filter FAQs based on search and topic
  const filteredFaqs = React.useMemo(() => {
    let filtered = faqData

    if (selectedTopic) {
      filtered = filtered.filter(faq => faq.topic === selectedTopic)
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(faq => {
        const questionMatch = faq.question.toLowerCase().includes(query)
        const keywordMatch = faq.keywords.some(keyword => keyword.toLowerCase().includes(query))
        return questionMatch || keywordMatch
      })
    }

    return filtered
  }, [searchQuery, selectedTopic])

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
    
    if (e.target.value.trim()) {
      // Auto-expand search results
      const searchResults = faqData.filter(faq => {
        const query = e.target.value.toLowerCase()
        const questionMatch = faq.question.toLowerCase().includes(query)
        const keywordMatch = faq.keywords.some(keyword => keyword.toLowerCase().includes(query))
        return questionMatch || keywordMatch
      })
      setOpenItems(searchResults.map(faq => faq.id))
    } else {
      setOpenItems([])
    }
  }

  // Clear filters
  const clearFilters = () => {
    setSearchQuery("")
    setSelectedTopic(null)
    setOpenItems([])
  }

  return (
    <PublicAppShell>
      <div className="space-y-0 -mx-4 md:-mx-6">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 via-white to-indigo-50 px-4 md:px-6 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
              <HelpCircle className="h-8 w-8 text-blue-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Find quick answers to common questions about our car rental services. 
              Can't find what you're looking for? Contact us directly.
            </p>
            
            {/* Search Bar */}
            <div className="relative max-w-2xl mx-auto">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Search for questions, keywords, or topics..."
                value={searchQuery}
                onChange={handleSearch}
                className="pl-12 h-14 text-base border-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl shadow-sm"
              />
              {searchQuery && (
                <Button
                  onClick={clearFilters}
                  variant="secondary"
                  size="sm"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  Clear
                </Button>
              )}
            </div>
          </div>
        </section>

        {/* Quick Stats */}
        <section className="px-4 md:px-6 py-8 bg-white border-y">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{faqData.length}+</div>
                <div className="text-sm text-gray-600">Common Questions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{faqTopics.length}</div>
                <div className="text-sm text-gray-600">Topic Categories</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">24/7</div>
                <div className="text-sm text-gray-600">Support Available</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">&lt;5min</div>
                <div className="text-sm text-gray-600">Response Time</div>
              </div>
            </div>
          </div>
        </section>

        {/* Topic Categories */}
        <section className="px-4 md:px-6 py-12 bg-gray-50">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Browse by Category</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {topicStats.map((topic) => {
                const IconComponent = topic.icon
                const isSelected = selectedTopic === topic.id
                return (
                  <Card
                    key={topic.id}
                    className={`cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border-2 ${
                      isSelected 
                        ? `${topic.color} border-current shadow-lg` 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedTopic(selectedTopic === topic.id ? null : topic.id)}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className={`p-3 rounded-lg ${isSelected ? 'bg-white/20' : topic.color}`}>
                          <IconComponent className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg mb-2">{topic.title}</h3>
                          <p className={`text-sm mb-3 ${isSelected ? 'opacity-90' : 'text-gray-600'}`}>
                            {topic.description}
                          </p>
                          <div className="flex items-center justify-between">
                            <Badge variant="secondary" className="text-xs">
                              {topic.count} questions
                            </Badge>
                            <ChevronRight className={`h-4 w-4 transition-transform ${isSelected ? 'rotate-90' : ''}`} />
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        </section>

        {/* FAQ Content */}
        <section className="px-4 md:px-6 py-12">
          <div className="max-w-4xl mx-auto">
            {/* Filter indicators */}
            {(selectedTopic || searchQuery) && (
              <div className="mb-8 flex flex-wrap items-center gap-3">
                <span className="text-sm font-medium text-gray-700">Showing results for:</span>
                {selectedTopic && (
                  <Badge variant="secondary" className="flex items-center gap-2">
                    {faqTopics.find(t => t.id === selectedTopic)?.title}
                    <button
                      onClick={() => setSelectedTopic(null)}
                      className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                    >
                      ×
                    </button>
                  </Badge>
                )}
                {searchQuery && (
                  <Badge variant="secondary" className="flex items-center gap-2">
                    "{searchQuery}"
                    <button
                      onClick={() => setSearchQuery("")}
                      className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                    >
                      ×
                    </button>
                  </Badge>
                )}
                <Button variant="secondary" size="sm" onClick={clearFilters}>
                  Clear all filters
                </Button>
              </div>
            )}

            {/* FAQ Accordion */}
            <Accordion 
              type="multiple" 
              value={openItems} 
              onValueChange={setOpenItems}
              className="space-y-4"
            >
              {filteredFaqs.map((faq, index) => (
                <AccordionItem 
                  key={faq.id} 
                  value={faq.id}
                  className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow overflow-hidden"
                >
                  <AccordionTrigger className="px-6 py-5 hover:no-underline hover:bg-gray-50 text-left group">
                    <div className="flex items-center gap-4 w-full">
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
                        {index + 1}
                      </div>
                      <span className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                        {faq.question}
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-6 pb-6 pt-2">
                    <div className="ml-12 text-gray-700 leading-relaxed">
                      {faq.answer}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>

            {filteredFaqs.length === 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No questions found</h3>
                <p className="text-gray-600 mb-6">
                  Try adjusting your search terms or browse by category above.
                </p>
                <Button onClick={clearFilters}>
                  Clear filters
                </Button>
              </div>
            )}
          </div>
        </section>

        {/* Contact CTA */}
        <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white px-4 md:px-6 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">Still have questions?</h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Our friendly customer support team is here to help you 24/7. 
              Get in touch via your preferred method.
            </p>
            
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <Link href="/customer/contact">
                <Card className="bg-white/10 border-white/20 hover:bg-white/20 transition-colors cursor-pointer">
                  <CardContent className="p-6 text-center">
                    <MessageCircle className="h-8 w-8 text-yellow-400 mx-auto mb-3" />
                    <h3 className="font-semibold mb-2">Messenger</h3>
                    <p className="text-sm text-blue-100">Instant chat support</p>
                  </CardContent>
                </Card>
              </Link>
              
              <Link href="/customer/contact">
                <Card className="bg-white/10 border-white/20 hover:bg-white/20 transition-colors cursor-pointer">
                  <CardContent className="p-6 text-center">
                    <Phone className="h-8 w-8 text-yellow-400 mx-auto mb-3" />
                    <h3 className="font-semibold mb-2">Phone</h3>
                    <p className="text-sm text-blue-100">Call us directly</p>
                  </CardContent>
                </Card>
              </Link>
              
              <Link href="/customer/contact">
                <Card className="bg-white/10 border-white/20 hover:bg-white/20 transition-colors cursor-pointer">
                  <CardContent className="p-6 text-center">
                    <Mail className="h-8 w-8 text-yellow-400 mx-auto mb-3" />
                    <h3 className="font-semibold mb-2">Email</h3>
                    <p className="text-sm text-blue-100">Send us a message</p>
                  </CardContent>
                </Card>
              </Link>
            </div>
            
            <Link href="/customer/contact">
              <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold">
                Contact Support
              </Button>
            </Link>
          </div>
        </section>
      </div>
    </PublicAppShell>
  )
}
