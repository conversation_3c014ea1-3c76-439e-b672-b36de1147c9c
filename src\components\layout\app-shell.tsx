"use client"

import * as React from "react"
import { SidebarNav } from "../sidebar-nav"
import { Topbar } from "../topbar"
import { BreadcrumbNav } from "../nav/breadcrumb-nav"
import { SidebarProvider, useSidebar } from "../nav/sidebar-context"
import { Menu, ChevronsLeft, ChevronsRight } from "lucide-react"

function ShellInner({ children, right }: { children: React.ReactNode; right?: React.ReactNode }) {
  const { isCollapsed, toggle } = useSidebar()
  const hasRightPanel = Boolean(right)

  return (
    <div className="min-h-screen bg-muted/20">
      <div
        className="flex"
        data-collapsed={isCollapsed}
        style={{
          // @ts-ignore - CSS var used inline for smoother coordinated animations
          "--sidebar-w": isCollapsed ? "4rem" : "16rem",
        } as React.CSSProperties}
      >
        <aside
          className={
            "hidden md:block shrink-0 border-r bg-white transition-[width] duration-200 ease-in-out overflow-hidden"
          }
          style={{ width: "var(--sidebar-w)" } as React.CSSProperties}
        >
          <SidebarNav />
        </aside>
        <main
          className="flex-1 flex flex-col min-w-0"
          style={{ width: "calc(100% - var(--sidebar-w))", transition: "width 200ms ease" } as React.CSSProperties}
        >
          <Topbar />
          <div className="px-4 md:px-6 py-4">
            <div className="flex items-center justify-between mb-4">
              <BreadcrumbNav />
              <button
                className="hidden md:inline-flex items-center gap-2 rounded-md border px-2.5 py-1.5 text-sm bg-white hover:bg-muted transition-colors"
                onClick={toggle}
                aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
              >
                {isCollapsed ? <ChevronsRight className="h-4 w-4" /> : <ChevronsLeft className="h-4 w-4" />}
                <span className="hidden lg:inline">{isCollapsed ? "Expand" : "Collapse"}</span>
              </button>
            </div>
            {hasRightPanel ? (
              <div className="grid grid-cols-1 xl:grid-cols-12 gap-4">
                <div className="xl:col-span-9 space-y-4">{children}</div>
                <div className="xl:col-span-3 space-y-4">{right}</div>
              </div>
            ) : (
              <div className="w-full space-y-4">{children}</div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}

export function AppShell({ children, right }: { children: React.ReactNode; right?: React.ReactNode }) {
  return (
    <SidebarProvider>
      <ShellInner right={right}>{children}</ShellInner>
    </SidebarProvider>
  )
}
