"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts"
import { Calendar, TrendingUp } from "lucide-react"
import * as React from "react"

const COLORS = ["#2563eb", "#60a5fa", "#0ea5e9", "#93c5fd", "#1e40af"]

export function TopTypesChart({ data }: { data: { type: string; count: number }[] }) {
  const [timeFrame, setTimeFrame] = React.useState("30")
  const total = data.reduce((s, d) => s + d.count, 0)
  
  const timeFrameOptions = [
    { value: "7", label: "7 days" },
    { value: "30", label: "30 days" },
    { value: "90", label: "90 days" },
  ]

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Top Rental Types</CardTitle>
            <p className="card-secondary-content mt-1">Most popular car categories</p>
          </div>
          <div className="flex items-center gap-1 text-blue-600">
            <TrendingUp className="h-4 w-4" />
          </div>
        </div>
        
        {/* Time Frame Filter */}
        <div className="flex items-center gap-2 mt-4">
          <Calendar className="h-4 w-4 text-gray-500" />
          <div className="flex rounded-lg border border-gray-200 overflow-hidden">
            {timeFrameOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setTimeFrame(option.value)}
                className={`px-3 py-1 text-xs font-medium transition-colors ${
                  timeFrame === option.value
                    ? "bg-blue-600 text-white"
                    : "bg-white text-gray-600 hover:bg-gray-50"
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="h-48">
          <ResponsiveContainer>
            <PieChart>
              <Pie 
                data={data} 
                dataKey="count" 
                nameKey="type" 
                innerRadius={55} 
                outerRadius={80}
                paddingAngle={2}
              >
                {data.map((_, idx) => (
                  <Cell key={idx} fill={COLORS[idx % COLORS.length]} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-sm font-medium text-gray-900 mb-2">
            Total Rentals: {total.toLocaleString()}
          </div>
          <div className="space-y-2">
            {data.map((d, i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span 
                    className="inline-block h-3 w-3 rounded-full" 
                    style={{ background: COLORS[i % COLORS.length] }} 
                  />
                  <span className="text-sm text-gray-700">{d.type}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">{d.count.toLocaleString()}</div>
                  <div className="text-xs text-gray-500">
                    {((d.count / total) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
