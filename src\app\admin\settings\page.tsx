"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import { useAdminAuth } from "@/components/auth/admin-auth-context";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Building2,
  MapPin,
  DollarSign,
  FileText,
  CreditCard,
  Car,
  Wrench,
  Users,
  Bell,
  Puzzle,
  Shield,
  Database,
  Palette,
  History,
  Save,
  X,
  AlertCircle,
} from "lucide-react";
import {
  getOrganizationSettings,
  updateOrganizationSettings,
  getBranches,
  getPricingSettings,
  updatePricingSettings,
  getBookingPolicySettings,
  updateBookingPolicySettings,
  getThemeSettings,
  updateThemeSettings,
  getAuditLogs,
} from "./actions/settings-actions";
import { BranchesManagement } from "@/components/admin/settings/branches-management";
import { OrganizationSection } from "@/components/admin/settings/organization-section";
import { PricingSection } from "@/components/admin/settings/pricing-section";
import { PoliciesSection } from "@/components/admin/settings/policies-section";
import { ThemeSection } from "@/components/admin/settings/theme-section";
import { AuditLogSection } from "@/components/admin/settings/audit-log-section";
import { MobileNavDrawer } from "@/components/admin/settings/mobile-nav-drawer";
import { SettingsModal } from "@/components/admin/settings/settings-modal";
import type {
  OrganizationSettings,
  Branch,
  PricingSettings,
  BookingPolicySettings,
  ThemeSettings,
  AuditLogEntry,
  SettingsSection,
} from "@/lib/types";
import { cn } from "@/lib/utils";

type ActiveSection = SettingsSection;

interface SettingsState {
  organization: OrganizationSettings | null;
  branches: Branch[];
  pricing: PricingSettings | null;
  policies: BookingPolicySettings | null;
  theme: ThemeSettings | null;
  auditLogs: AuditLogEntry[];
}

interface SavingState {
  [key: string]: boolean;
}

const sidebarSections = [
  {
    id: "organization" as SettingsSection,
    title: "Organization",
    icon: Building2,
    description: "Company information and branding",
  },
  {
    id: "branches" as SettingsSection,
    title: "Branches",
    icon: MapPin,
    description: "Manage your business locations",
  },
  {
    id: "pricing" as SettingsSection,
    title: "Pricing",
    icon: DollarSign,
    description: "Rental rates and fees",
  },
  {
    id: "policies" as SettingsSection,
    title: "Policies",
    icon: FileText,
    description: "Booking and cancellation policies",
  },
  {
    id: "theme" as SettingsSection,
    title: "Theme",
    icon: Palette,
    description: "Application appearance",
  },
  {
    id: "audit" as SettingsSection,
    title: "Audit Log",
    icon: History,
    description: "System activity log",
  },
];

interface DirtyState {
  [key: string]: boolean;
}

export default function AdminSettingsPage() {
  const { loading: authLoading } = useAdminAuth();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [activeSection, setActiveSection] =
    React.useState<ActiveSection>("organization");
  const [settings, setSettings] = React.useState<SettingsState>({
    organization: null,
    branches: [],
    pricing: null,
    policies: null,
    theme: null,
    auditLogs: [],
  });
  const [dirtyState, setDirtyState] = React.useState<DirtyState>({});
  const [loading, setLoading] = React.useState(true);
  const [saving, setSaving] = React.useState<{ [key: string]: boolean }>({});
  const [modalOpen, setModalOpen] = React.useState(false);

  const sidebarItems: { id: SettingsSection; label: string; icon: React.ComponentType<any> }[] = [
    { id: "organization", label: "Organization", icon: Building2 },
    { id: "branches", label: "Branches", icon: MapPin },
    { id: "pricing", label: "Pricing", icon: DollarSign },
    { id: "policies", label: "Policies", icon: FileText },
    { id: "theme", label: "Theme", icon: Palette },
    { id: "audit", label: "Audit Logs", icon: Shield },
  ];

  const hasDirtyChanges = React.useMemo(() => {
    return Object.values(dirtyState).some(Boolean);
  }, [dirtyState]);

  // Load initial data
  React.useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const [org, branches, pricing, policies, theme, auditLogs] =
          await Promise.all([
            getOrganizationSettings(),
            getBranches(),
            getPricingSettings(),
            getBookingPolicySettings(),
            getThemeSettings(),
            getAuditLogs({ limit: 50 }),
          ]);

        setSettings({
          organization: org,
          branches,
          pricing,
          policies,
          theme,
          auditLogs,
        });
      } catch (error) {
        console.error("Failed to load settings:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load settings data",
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [toast]);

  const markDirty = (section: string) => {
    setDirtyState((prev) => ({ ...prev, [section]: true }));
  };

  const markClean = (section: string) => {
    setDirtyState((prev) => ({ ...prev, [section]: false }));
  };

  const saveSection = async (section: ActiveSection) => {
    setSaving((prev) => ({ ...prev, [section]: true }));

    try {
      let result: { success: boolean; error?: string } = { success: false };

      switch (section) {
        case "organization":
          if (settings.organization) {
            result = await updateOrganizationSettings(settings.organization);
          }
          break;
        case "pricing":
          if (settings.pricing) {
            result = await updatePricingSettings(settings.pricing);
          }
          break;
        case "policies":
          if (settings.policies) {
            result = await updateBookingPolicySettings(settings.policies);
          }
          break;
        case "theme":
          if (settings.theme) {
            result = await updateThemeSettings(settings.theme);
          }
          break;
      }

      if (result.success) {
        markClean(section);
        toast({
          title: "Settings saved",
          description: `${
            sidebarItems.find((item) => item.id === section)?.label
          } settings have been saved successfully.`,
        });
      } else {
        throw new Error(result.error || "Save failed");
      }
    } catch (error) {
      console.error("Failed to save settings:", error);
      toast({
        variant: "destructive",
        title: "Save failed",
        description: (error as Error).message || "Failed to save settings",
      });
    } finally {
      setSaving((prev) => ({ ...prev, [section]: false }));
    }
  };

  const saveAllChanges = async () => {
    const sectionsToSave = Object.entries(dirtyState)
      .filter(([_, isDirty]) => isDirty)
      .map(([section]) => section as ActiveSection);

    for (const section of sectionsToSave) {
      await saveSection(section);
    }
  };

  const discardChanges = async () => {
    // Reload all data to discard changes
    setLoading(true);
    try {
      const [org, branches, pricing, policies, theme] = await Promise.all([
        getOrganizationSettings(),
        getBranches(),
        getPricingSettings(),
        getBookingPolicySettings(),
        getThemeSettings(),
      ]);

      setSettings((prev) => ({
        ...prev,
        organization: org,
        branches,
        pricing,
        policies,
        theme,
      }));

      setDirtyState({});

      toast({
        title: "Changes discarded",
        description: "All unsaved changes have been discarded.",
      });
    } catch (error) {
      console.error("Failed to reload settings:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to reload settings data",
      });
    } finally {
      setLoading(false);
    }
  };

  // Define all hooks before any conditional returns
  // Get the current section title and icon
  const currentSection = React.useMemo(() => {
    return sidebarItems.find((item) => item.id === activeSection) || sidebarItems[0];
  }, [activeSection]);

  // Open the modal when a section is selected on mobile
  const handleOpenSettingsModal = React.useCallback(() => {
    if (isMobile) {
      setModalOpen(true);
    }
  }, [isMobile]);
  
  // Wait for auth loading to complete before showing page loading
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // This section was removed to avoid duplicate declarations

  return (
    <div className="space-y-4 p-4">
      <header className="flex justify-between items-center">
        <div className="space-y-2">
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-foreground">Organization Management</h1>
          <p className="text-sm sm:text-base text-muted-foreground">Manage your organization settings, pricing, policies, and business configuration.</p>
        </div>
        <MobileNavDrawer
          sidebarItems={sidebarItems}
          activeSection={activeSection}
          setActiveSection={setActiveSection}
          dirtyState={dirtyState}
          onOpenSettingsModal={handleOpenSettingsModal}
        />
      </header>
      <div className="h-full bg-gray-50/50" data-testid="admin-settings-page">
        <div className="flex h-full">
        {/* Settings Sidebar - Only visible on desktop */}
        <aside
          className="hidden lg:block w-80 bg-white border-r border-gray-200 flex-shrink-0"
          data-testid="admin-settings-sidebar"
        >
          <div className="p-6 border-b border-gray-200">
            <p className="text-sm text-gray-600 mt-1">
              Configure your car rental business settings
            </p>
          </div>

          <ScrollArea className="h-[calc(100vh-180px)]">
            <nav className="p-4 space-y-1">
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveSection(item.id as ActiveSection)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all",
                    "hover:bg-gray-100 hover:text-gray-900",
                    activeSection === item.id
                      ? "bg-blue-50 text-blue-700 border border-blue-200 font-medium"
                      : "text-gray-700",
                    dirtyState[item.id] && "relative"
                  )}
                >
                  <item.icon className="h-4 w-4 flex-shrink-0" />
                  <span className="flex-1 text-left">{item.label}</span>
                  {dirtyState[item.id] && (
                    <div className="w-2 h-2 rounded-full bg-orange-500 flex-shrink-0" />
                  )}
                </button>
              ))}
            </nav>
          </ScrollArea>
        </aside>

        {/* Main Content - Only visible on desktop */}
        <main className="flex-1 overflow-hidden">
          <div className="h-full flex flex-col">
            <div className="flex-1 overflow-y-auto p-6">
              {/* On mobile, show a card that prompts the user to select a section */}
              {isMobile && !modalOpen && (
                <div className="lg:hidden text-center py-12 space-y-6">
                  <div className="mx-auto w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center">
                    {React.createElement(currentSection.icon, { className: "h-8 w-8 text-blue-600" })}
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold mb-2">Settings</h2>
                    <p className="text-gray-500 mb-6">Tap the menu button to access settings options</p>
                    <Button 
                      onClick={() => handleOpenSettingsModal()}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      Open {currentSection.label}
                    </Button>
                  </div>
                </div>
              )}
              
              {/* Desktop content */}
              <div className="hidden lg:block">
              {activeSection === "organization" && settings.organization && (
                <OrganizationSection
                  settings={settings.organization}
                  onUpdate={(updates: Partial<OrganizationSettings>) => {
                    setSettings((prev) => ({
                      ...prev,
                      organization: { ...prev.organization!, ...updates },
                    }));
                    markDirty("organization");
                  }}
                  onSave={() => saveSection("organization")}
                  saving={saving.organization}
                />
              )}

              {activeSection === "branches" && (
                <BranchesManagement
                  branches={settings.branches}
                  onUpdate={(branches: Branch[]) => {
                    setSettings((prev) => ({ ...prev, branches }));
                    markDirty("branches");
                  }}
                />
              )}

              {activeSection === "pricing" && settings.pricing && (
                <PricingSection
                  settings={settings.pricing}
                  onUpdate={(updates: Partial<PricingSettings>) => {
                    setSettings((prev) => ({
                      ...prev,
                      pricing: { ...prev.pricing!, ...updates },
                    }));
                    markDirty("pricing");
                  }}
                  onSave={() => saveSection("pricing")}
                  saving={saving.pricing}
                />
              )}

              {activeSection === "policies" && settings.policies && (
                <PoliciesSection
                  settings={settings.policies}
                  onUpdate={(updates: Partial<BookingPolicySettings>) => {
                    setSettings((prev) => ({
                      ...prev,
                      policies: { ...prev.policies!, ...updates },
                    }));
                    markDirty("policies");
                  }}
                  onSave={() => saveSection("policies")}
                  saving={saving.policies}
                />
              )}

              {activeSection === "theme" && settings.theme && (
                <ThemeSection
                  settings={settings.theme}
                  onUpdate={(updates: Partial<ThemeSettings>) => {
                    setSettings((prev) => ({
                      ...prev,
                      theme: { ...prev.theme!, ...updates },
                    }));
                    markDirty("theme");
                  }}
                  onSave={() => saveSection("theme")}
                  saving={saving.theme}
                />
              )}

              {activeSection === "audit" && (
                <AuditLogSection auditLogs={settings.auditLogs} />
              )}

              {/* Placeholder sections for other features */}
              {![
                "organization",
                "branches", 
                "pricing",
                "policies",
                "theme",
                "audit",
              ].includes(activeSection) && (
                <Card className="p-4" data-testid={`admin-settings-card-${activeSection}`}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {React.createElement(
                        sidebarItems.find((item) => item.id === activeSection)?.icon || Building2,
                        { className: "h-5 w-5" }
                      )}
                      {sidebarItems.find((item) => item.id === activeSection)?.label}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-12 text-gray-500">
                      <div className="text-sm">
                        This section is coming soon. We're working on bringing you comprehensive {activeSection} management tools.
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
        </main>
        
        {/* Settings Modal for Mobile/Tablet */}
        <SettingsModal
          open={modalOpen}
          onOpenChange={setModalOpen}
          activeSection={activeSection}
          sectionTitle={currentSection.label}
          sectionIcon={currentSection.icon}
          isDirty={dirtyState[activeSection] || false}
          onSave={() => saveSection(activeSection)}
          onDiscard={() => discardChanges()}
          saving={saving[activeSection] || false}
        >
          {activeSection === "organization" && settings.organization && (
            <OrganizationSection
              settings={settings.organization}
              onUpdate={(updates: Partial<OrganizationSettings>) => {
                setSettings((prev) => ({
                  ...prev,
                  organization: { ...prev.organization!, ...updates },
                }));
                markDirty("organization");
              }}
              onSave={() => saveSection("organization")}
              saving={saving.organization}
            />
          )}

          {activeSection === "branches" && (
            <BranchesManagement
              branches={settings.branches}
              onUpdate={(branches: Branch[]) => {
                setSettings((prev) => ({ ...prev, branches }));
                markDirty("branches");
              }}
            />
          )}

          {activeSection === "pricing" && settings.pricing && (
            <PricingSection
              settings={settings.pricing}
              onUpdate={(updates: Partial<PricingSettings>) => {
                setSettings((prev) => ({
                  ...prev,
                  pricing: { ...prev.pricing!, ...updates },
                }));
                markDirty("pricing");
              }}
              onSave={() => saveSection("pricing")}
              saving={saving.pricing}
            />
          )}

          {activeSection === "policies" && settings.policies && (
            <PoliciesSection
              settings={settings.policies}
              onUpdate={(updates: Partial<BookingPolicySettings>) => {
                setSettings((prev) => ({
                  ...prev,
                  policies: { ...prev.policies!, ...updates },
                }));
                markDirty("policies");
              }}
              onSave={() => saveSection("policies")}
              saving={saving.policies}
            />
          )}

          {activeSection === "theme" && settings.theme && (
            <ThemeSection
              settings={settings.theme}
              onUpdate={(updates: Partial<ThemeSettings>) => {
                setSettings((prev) => ({
                  ...prev,
                  theme: { ...prev.theme!, ...updates },
                }));
                markDirty("theme");
              }}
              onSave={() => saveSection("theme")}
              saving={saving.theme}
            />
          )}

          {activeSection === "audit" && (
            <AuditLogSection auditLogs={settings.auditLogs} />
          )}

          {/* Placeholder sections for other features */}
          {!["organization", "branches", "pricing", "policies", "theme", "audit"].includes(activeSection) && (
            <Card data-testid={`admin-settings-card-${activeSection}`}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {React.createElement(
                    sidebarItems.find((item) => item.id === activeSection)?.icon || Building2,
                    { className: "h-5 w-5" }
                  )}
                  {sidebarItems.find((item) => item.id === activeSection)?.label}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-gray-500">
                  <div className="text-sm">
                    This section is coming soon. We're working on bringing you comprehensive {activeSection} management tools.
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </SettingsModal>
        </div>
      </div>

      {/* Global Save Bar - Only visible on desktop */}
      {hasDirtyChanges && !isMobile && (
        <div
          className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 shadow-lg z-50 hidden lg:block"
          data-testid="admin-settings-savebar"
        >
          <div className="max-w-screen-xl mx-auto flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <AlertCircle className="h-4 w-4 text-orange-500" />
              You have unsaved changes
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="secondary"
                onClick={discardChanges}
                className="px-6"
              >
                <X className="h-4 w-4 mr-2" />
                Discard
              </Button>
              <Button
                onClick={saveAllChanges}
                className="px-6 bg-blue-600 hover:bg-blue-700"
              >
                <Save className="h-4 w-4 mr-2" />
                Save All Changes
              </Button>
            </div>
          </div>
        </div>
      )}
      
      {/* Mobile Fixed Save Button - Only visible when changes are made */}
      {hasDirtyChanges && isMobile && !modalOpen && (
        <div className="lg:hidden fixed bottom-6 right-6 z-50">
          <Button
            onClick={() => handleOpenSettingsModal()}
            size="lg"
            className="rounded-full shadow-lg bg-blue-600 hover:bg-blue-700 h-14 w-14 p-0"
          >
            <Save className="h-6 w-6" />
          </Button>
        </div>
      )}
    </div>
  );
}
