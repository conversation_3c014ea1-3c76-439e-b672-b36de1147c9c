'use client'

import * as React from 'react'
import {
  ThemeProvider as NextThemesProvider,
  type ThemeProviderProps,
} from 'next-themes'

// Page transition provider for smooth navigation
export function PageTransitionProvider({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = React.useState(false)
  
  React.useEffect(() => {
    const handleStart = () => setIsLoading(true)
    const handleComplete = () => setIsLoading(false)
    
    // Listen for page navigation events
    window.addEventListener('beforeunload', handleStart)
    
    return () => {
      window.removeEventListener('beforeunload', handleStart)
    }
  }, [])
  
  return (
    <div className={`transition-opacity duration-300 ${isLoading ? 'opacity-80' : 'opacity-100'}`}>
      {children}
    </div>
  )
}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  // Prevent hydration mismatch by ensuring client-side only theme initialization
  const [mounted, setMounted] = React.useState(false)
  
  React.useEffect(() => {
    setMounted(true)
  }, [])
  
  // Render children without theme provider during SSR
  if (!mounted) {
    return (
      <PageTransitionProvider>
        {children}
      </PageTransitionProvider>
    )
  }
  
  return (
    <NextThemesProvider {...props}>
      <PageTransitionProvider>
        {children}
      </PageTransitionProvider>
    </NextThemesProvider>
  )
}
