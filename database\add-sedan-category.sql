-- Add SEDAN category to vehicle_categories table and update cars table constraint
-- Run this script to fix the missing SEDAN VEHICLES category

-- First, update the cars table constraint to include Sedan
ALTER TABLE public.cars DROP CONSTRAINT IF EXISTS cars_type_check;
ALTER TABLE public.cars ADD CONSTRAINT cars_type_check 
  CHECK (type IN ('SUV', 'Sport', 'Coupe', 'Hatchback', 'MPV', 'Sedan'));

-- Insert SEDAN category if it doesn't exist
INSERT INTO public.vehicle_categories (name, type, description, min_price, max_price, image_url, icon_color, display_order) 
SELECT 'SEDAN VEHICLES', 'Sedan', 'Classic four-door sedans perfect for business and comfort', 1500.00, 2500.00, '/placeholder.svg', '#6366F1', 6
WHERE NOT EXISTS (SELECT 1 FROM public.vehicle_categories WHERE type = 'Sedan');

-- Update existing cars with Sedan type to link with the category
UPDATE public.cars 
SET category_id = (SELECT id FROM public.vehicle_categories WHERE type = 'Sedan' LIMIT 1)
WHERE type = 'Sedan' AND category_id IS NULL;

-- Update any existing Coupe cars that should be Sedan (Toyota Vios models)
UPDATE public.cars 
SET type = 'Sedan', 
    category_id = (SELECT id FROM public.vehicle_categories WHERE type = 'Sedan' LIMIT 1)
WHERE model LIKE '%Vios%' AND type = 'Coupe';
