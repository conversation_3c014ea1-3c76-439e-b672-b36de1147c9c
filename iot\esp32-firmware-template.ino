/**
 * PathLink GPS Tracker Firmware for LilyGo T-Call A7670E
 *
 * Description:
 * This firmware connects the LilyGo T-Call A7670E board to a cellular network,
 * reads GPS data, and publishes it to an MQTT broker. The configuration for
 * both the cellular connection and MQTT broker should be provisioned from your
 * PathLink database.
 *
 * Hardware:
 * - Board: LilyGo T-Call A7670E (ESP32-WROVER-B, A7670E Modem)
 * - GPS: NEO-6M (or compatible) connected to the board.
 *
 * Dependencies (Arduino Library Manager):
 * - TinyGsm by <PERSON><PERSON><PERSON><PERSON>
 * - PubSubClient by <PERSON>
 * - TinyGPS++ by <PERSON><PERSON>
 * 
 * Instructions:
 * 1. Install the required libraries from the Arduino Library Manager.
 * 2. Fill in the placeholder values in the "CONFIGURATION" section below.
 *    In a real-world scenario, these would be fetched from your server/database.
 * 3. Upload the firmware to your ESP32 board.
 * 4. Monitor the Serial output (Baud: 115200) for debugging information.
 */

// =========================================================================
// LIBRARIES
// =========================================================================
#define TINY_GSM_MODEM_A7670  // Use A7670 modem
#include <TinyGsmClient.h>
#include <PubSubClient.h>
#include <TinyGPS++.h>
#include <HardwareSerial.h>

// =========================================================================
// CONFIGURATION
// (Replace with values from your PathLink database for each device)
// =========================================================================

// -- Cellular Configuration --
const char apn[]      = "internet"; // Your SIM card's APN
const char gsmUser[]  = "";         // APN username (if any)
const char gsmPass[]  = "";         // APN password (if any)

// -- MQTT Broker Configuration --
const char* mqtt_broker_host = "your_mqtt_broker.com"; // From gps_device_mapping table
const int   mqtt_broker_port = 1883;                   // From gps_device_mapping table
const char* mqtt_client_id   = "device_client_123";    // From gps_device_mapping table
const char* mqtt_username    = "your_mqtt_user";       // From gps_device_mapping table
const char* mqtt_password    = "your_mqtt_password";   // From gps_device_mapping table
const char* mqtt_pub_topic   = "gps/tracking/device1"; // From gps_device_mapping table

// =========================================================================
// PIN DEFINITIONS
// =========================================================================
#define UART_BAUD           115200
#define PIN_DTR             25
#define PIN_TX              26
#define PIN_RX              27
#define PWR_PIN             4
#define LED_PIN             12

// GPS Serial Port
HardwareSerial gpsSerial(1); // Use UART 1 for GPS
const int GPS_RX_PIN = 34;   // Adjust if your wiring is different
const int GPS_TX_PIN = 13;   // Adjust if your wiring is different

// =========================================================================
// GLOBALS
// =========================================================================
HardwareSerial modemSerial(2); // Use UART 2 for the modem
TinyGsm modem(modemSerial);
TinyGsmClient client(modem);
PubSubClient mqtt(client);

TinyGPSPlus gps;

unsigned long lastReconnectAttempt = 0;

// =========================================================================
// SETUP
// =========================================================================
void setup() {
  // Start serial for debugging
  Serial.begin(UART_BAUD);
  Serial.println("--- PathLink GPS Tracker Initializing ---");

  // Power on the modem
  pinMode(PWR_PIN, OUTPUT);
  digitalWrite(PWR_PIN, LOW);
  delay(1000);
  digitalWrite(PWR_PIN, HIGH);
  delay(1000);
  digitalWrite(PWR_PIN, LOW);

  // Start modem serial
  modemSerial.begin(UART_BAUD, SERIAL_8N1, PIN_RX, PIN_TX);

  // Start GPS serial
  gpsSerial.begin(9600, SERIAL_8N1, GPS_RX_PIN, GPS_TX_PIN);
  Serial.println("GPS Serial Initialized.");

  // Set MQTT Server
  mqtt.setServer(mqtt_broker_host, mqtt_broker_port);
}

// =========================================================================
// MODEM & MQTT CONNECTION LOGIC
// =========================================================================
bool mqttConnect() {
  Serial.print("Connecting to MQTT broker: ");
  Serial.println(mqtt_broker_host);

  if (!mqtt.connect(mqtt_client_id, mqtt_username, mqtt_password)) {
    Serial.print("MQTT connection failed, rc=");
    Serial.print(mqtt.state());
    Serial.println(" | Retrying in 5 seconds...");
    return false;
  }
  
  Serial.println("MQTT Connected!");
  // You can subscribe to topics here if needed
  // mqtt.subscribe(mqtt_sub_topic);
  return true;
}

bool ensureGsmConnection() {
    if (!modem.isNetworkRegistered()) {
        Serial.println("Network not registered. Waiting...");
        if (!modem.waitForNetwork(180000L)) { // Wait up to 3 minutes
            Serial.println("Network registration failed.");
            return false;
        }
    }
    Serial.println("Network registered.");

    if (!modem.isGprsConnected()) {
        Serial.print("Connecting to APN: ");
        Serial.println(apn);
        if (!modem.gprsConnect(apn, gsmUser, gsmPass)) {
            Serial.println("GPRS connection failed.");
            return false;
        }
    }
    Serial.println("GPRS Connected.");
    return true;
}


// =========================================================================
// MAIN LOOP
// =========================================================================
void loop() {
  // Ensure cellular and MQTT connections are active
  if (!mqtt.connected()) {
    unsigned long now = millis();
    if (now - lastReconnectAttempt > 5000L) {
      lastReconnectAttempt = now;
      if (ensureGsmConnection()) {
        mqttConnect();
      }
    }
    return; // Wait for connection before proceeding
  }

  // Keep MQTT connection alive
  mqtt.loop();

  // Process GPS data
  while (gpsSerial.available() > 0) {
    if (gps.encode(gpsSerial.read())) {
      if (gps.location.isUpdated()) {
        Serial.println("GPS location updated. Publishing to MQTT.");
        
        // Create JSON payload
        char payload[256];
        snprintf(payload, sizeof(payload),
                 "{\"lat\":%.6f,\"lng\":%.6f,\"speed_kmph\":%.2f,\"timestamp\":%lu}",
                 gps.location.lat(),
                 gps.location.lng(),
                 gps.speed.kmph(),
                 gps.time.value());

        // Publish to MQTT
        Serial.print("Publishing: ");
        Serial.println(payload);
        if (!mqtt.publish(mqtt_pub_topic, payload)) {
          Serial.println("MQTT Publish Failed!");
        }
      }
    }
  }
}
