"use server";

import { createContextClient } from "@/lib/supabase/server";
import { sendEmail, buildTripCompletedEmail } from "@/lib/email";
import { logWithContext, logError } from "@/lib/utils/logger";

export async function finishTrip(bookingId: string) {
  const supabase = await createContextClient('customer');
  
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (!user) {
    return { error: { message: "You must be logged in to finish a trip." } };
  }

  try {
    logWithContext("TripActions", "Customer finishing trip", { userId: user.id, bookingId });

    // First, let's check if the booking exists at all
    const { data: bookingCheck, error: bookingCheckError } = await supabase
      .from("bookings")
      .select("id, status, customer_id, car_id, pickup_datetime, dropoff_datetime, pickup_location")
      .eq("id", bookingId)
      .single();

    logWithContext("TripActions", "Booking check result", { 
      bookingId, 
      userId: user.id, 
      bookingCheck, 
      bookingCheckError 
    });

    if (bookingCheckError || !bookingCheck) {
      logError("TripActions", "Booking not found in database", { bookingId, userId: user.id, bookingCheckError });
      return { error: { message: "Booking not found in the system." } };
    }

    // Check if user owns the booking
    if (bookingCheck.customer_id !== user.id) {
      logError("TripActions", "User does not own this booking", { 
        bookingId, 
        userId: user.id, 
        bookingCustomerId: bookingCheck.customer_id 
      });
      return { error: { message: "You don't have permission to finish this trip." } };
    }

    // Check if booking is active
    if (bookingCheck.status !== "Active") {
      logError("TripActions", "Booking is not active", { 
        bookingId, 
        userId: user.id, 
        currentStatus: bookingCheck.status 
      });
      return { error: { message: `Cannot complete trip. Booking status is: ${bookingCheck.status}` } };
    }

    const booking = bookingCheck;

    // Update booking status to Completed
    const { error: updateError } = await supabase
      .from("bookings")
      .update({
        status: "Completed",
        updated_at: new Date().toISOString(),
      })
      .eq("id", bookingId);

    if (updateError) {
      logError("TripActions", "Error updating booking status to completed", updateError);
      return { error: { message: "Failed to complete the trip. Please try again." } };
    }

    // Update car status back to Available
    const { error: carUpdateError } = await supabase
      .from("cars")
      .update({ status: "Available" })
      .eq("id", booking.car_id);

    if (carUpdateError) {
      logError("TripActions", "Error updating car status to available", { carId: booking.car_id, carUpdateError });
      // Don't fail the trip completion if car update fails, but log it
    } else {
      logWithContext("TripActions", "Car status updated to Available", { carId: booking.car_id });
    }

    // Get user profile for notification
    const { data: profile } = await supabase
      .from("profiles")
      .select("email, full_name")
      .eq("id", user.id)
      .single();

    // Send completion notification email
    if (profile?.email) {
      try {
        const completionEmail = buildTripCompletedEmail({
          customerName: profile.full_name || null,
          bookingRef: bookingId, // TODO: Should be booking_ref from database
          dashboardUrl: "https://olliesrentalcar.pathlinkio.app/customer/dashboard"
        });

        await sendEmail({
          to: profile.email,
          subject: completionEmail.subject,
          html: completionEmail.html
        });

        logWithContext("TripActions", "Trip completion email sent", { email: profile.email });
      } catch (emailError) {
        logError("TripActions", "Failed to send trip completion email", emailError);
        // Don't fail the trip completion if email fails
      }
    }

    logWithContext("TripActions", "Trip completed successfully", { bookingId, carId: booking.car_id });
    
    return { 
      success: true, 
      message: "Trip completed successfully! Thank you for choosing Ollie's Rent A Car.",
      booking: { id: bookingId, status: "Completed" }
    };
  } catch (error) {
    logError("TripActions", "Error completing trip", error);
    return { error: { message: "An unexpected error occurred while completing your trip." } };
  }
}

export async function cancelBooking(bookingId: string) {
  const supabase = await createContextClient('customer');
  
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (!user) {
    return { error: { message: "You must be logged in to cancel a booking." } };
  }

  try {
    logWithContext("TripActions", "Customer cancelling booking", { userId: user.id, bookingId });

    // First, check if the booking exists and belongs to the user
    const { data: bookingCheck, error: bookingCheckError } = await supabase
      .from("bookings")
      .select("id, status, customer_id, car_id, pickup_datetime")
      .eq("id", bookingId)
      .single();

    logWithContext("TripActions", "Booking check result for cancellation", { 
      bookingId, 
      userId: user.id, 
      bookingCheck, 
      bookingCheckError 
    });

    if (bookingCheckError || !bookingCheck) {
      logError("TripActions", "Booking not found for cancellation", { bookingId, userId: user.id, bookingCheckError });
      return { error: { message: "Booking not found in the system." } };
    }

    // Check if user owns the booking
    if (bookingCheck.customer_id !== user.id) {
      logError("TripActions", "User does not own this booking for cancellation", { 
        bookingId, 
        userId: user.id, 
        bookingCustomerId: bookingCheck.customer_id 
      });
      return { error: { message: "You don't have permission to cancel this booking." } };
    }

    // Check if booking can be cancelled (only Pending bookings can be cancelled)
    if (bookingCheck.status !== "Pending") {
      logError("TripActions", "Booking cannot be cancelled", { 
        bookingId, 
        userId: user.id, 
        currentStatus: bookingCheck.status 
      });
      return { error: { message: `Cannot cancel booking. Current status is: ${bookingCheck.status}` } };
    }

    const booking = bookingCheck;

    // Update booking status to Cancelled
    const { error: updateError } = await supabase
      .from("bookings")
      .update({
        status: "Cancelled",
        updated_at: new Date().toISOString(),
      })
      .eq("id", bookingId);

    if (updateError) {
      logError("TripActions", "Error updating booking status to cancelled", updateError);
      return { error: { message: "Failed to cancel the booking. Please try again." } };
    }

    // Update car status back to Available if it was reserved
    if (booking.car_id) {
      const { error: carUpdateError } = await supabase
        .from("cars")
        .update({ status: "Available" })
        .eq("id", booking.car_id);

      if (carUpdateError) {
        logError("TripActions", "Error updating car status to available after cancellation", { carId: booking.car_id, carUpdateError });
        // Don't fail the cancellation if car update fails, but log it
      } else {
        logWithContext("TripActions", "Car status updated to Available after cancellation", { carId: booking.car_id });
      }
    }

    // Get user profile for notification
    const { data: profile } = await supabase
      .from("profiles")
      .select("email, full_name")
      .eq("id", user.id)
      .single();

    // Send cancellation notification email (optional, can be implemented later)
    if (profile?.email) {
      try {
        // TODO: Implement cancellation email template
        logWithContext("TripActions", "Would send cancellation email", { email: profile.email });
      } catch (emailError) {
        logError("TripActions", "Failed to send cancellation email", emailError);
        // Don't fail the cancellation if email fails
      }
    }

    logWithContext("TripActions", "Booking cancelled successfully", { bookingId, carId: booking.car_id });
    
    return { 
      success: true, 
      message: "Booking cancelled successfully.",
      booking: { id: bookingId, status: "Cancelled" }
    };
  } catch (error) {
    logError("TripActions", "Error cancelling booking", error);
    return { error: { message: "An unexpected error occurred while cancelling your booking." } };
  }
}

export async function getActiveBookings() {
  const supabase = await createContextClient('customer');
  
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (!user) {
    return { error: { message: "You must be logged in to view bookings." } };
  }

  try {
    const { data: bookings, error } = await supabase
      .from("bookings")
      .select(`
        id,
        status,
        from,
        to,
        pickup_location,
        total_amount,
        created_at,
        cars (
          id,
          model,
          plate_number,
          image_url
        )
      `)
      .eq("customer_id", user.id)
      .eq("status", "Active")
      .order("created_at", { ascending: false });

    if (error) {
      logError("TripActions", "Error fetching active bookings", error);
      return { error: { message: "Failed to fetch active bookings." } };
    }

    return { success: true, bookings };
  } catch (error) {
    logError("TripActions", "Error in getActiveBookings", error);
    return { error: { message: "An unexpected error occurred while fetching bookings." } };
  }
}
