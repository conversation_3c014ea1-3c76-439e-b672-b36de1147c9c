"use client";

import * as React from "react";
import { PublicAppShell } from "@/components/layout/public-app-shell";
import { ConfirmationStep } from "@/components/customer-side/booking/flow/confirmation-step";
import type { BookingData } from "@/components/customer-side/booking/flow/booking-flow";
import type { Car } from "@/lib/types";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestFinalSummaryFixPage() {
  const [viewportWidth, setViewportWidth] = React.useState<number>(0);

  React.useEffect(() => {
    const updateWidth = () => setViewportWidth(window.innerWidth);
    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => window.removeEventListener('resize', updateWidth);
  }, []);

  // Mock car data
  const mockCar: Car = {
    id: "test-car-1",
    model: "Toyota Vios 2023",
    type: "Sedan",
    seats: 5,
    transmission: "Manual",
    fuel_type: "Gasoline/Petrol",
    fuel_capacity: 50,
    price_per_day: 1200,
    status: "Available",
    condition: "Good",
    plate_number: "ABC-1234",
    image_url: "/images/toyota-vios.jpg",
    notes: "Test vehicle for booking flow",
    is_archived: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  // Mock booking data with all required fields
  const mockBookingData: BookingData = {
    selectedCar: mockCar,
    pickUpLocation: "123 Main Street, Quezon City, Metro Manila",
    dropOffLocation: "456 Business District, Makati City, Metro Manila",
    pickUpDate: "2024-01-15",
    pickUpTime: "09:00",
    dropOffDate: "2024-01-18",
    dropOffTime: "18:00",
    pickupTimePeriod: "day",
    returnTimePeriod: "night",
    deliveryFee: 200,
    returnFee: 300,
    totalDeliveryFees: 500,
    pickupGarageChecked: false,
    dropoffSameAsPickupChecked: false,
    isSpecialService: false,
    serviceType: null,
    customerName: "Juan Manuel Cruz-Rodriguez de la Silva",
    customerEmail: "<EMAIL>",
    customerPhone: "+63 ************",
    familyMemberPhone: "+63 ************",
    familyMemberFacebook: "https://facebook.com/juan.manuel.cruz.rodriguez.delasilva.backup.contact",
    notes: "This is a very long special request note that might cause content overlap issues on mobile devices. Please ensure the vehicle is thoroughly cleaned and inspected before pickup. I will be using this vehicle for an important business meeting and need it to be in perfect condition. Also, please make sure all safety equipment is present and functional.",
    driversLicense: [
      {
        id: "doc1",
        file: new File(["dummy"], "drivers_license_front_and_back.jpg", { type: "image/jpeg" }),
        url: "https://example.com/license.jpg",
        status: "completed" as const,
        fileName: "drivers_license_front_and_back.jpg",
        fileSize: 2048000,
        fileType: "image/jpeg",
        progress: 100
      }
    ],
    governmentId: [
      {
        id: "doc2",
        file: new File(["dummy"], "government_id_national_id_card.jpg", { type: "image/jpeg" }),
        url: "https://example.com/id.jpg",
        status: "completed" as const,
        fileName: "government_id_national_id_card.jpg",
        fileSize: 1536000,
        fileType: "image/jpeg",
        progress: 100
      }
    ],
    proofOfBilling: [
      {
        id: "doc3",
        file: new File(["dummy"], "utility_bill_electricity_proof_of_billing.pdf", { type: "application/pdf" }),
        url: "https://example.com/billing.jpg", 
        status: "completed" as const,
        fileName: "utility_bill_electricity_proof_of_billing.pdf",
        fileSize: 1024000,
        fileType: "application/pdf",
        progress: 100
      }
    ],
    proofOfPayment: [
      {
        id: "doc4",
        file: new File(["dummy"], "gcash_payment_proof_screenshot.png", { type: "image/png" }),
        url: "https://example.com/payment.jpg",
        status: "completed" as const, 
        fileName: "gcash_payment_proof_screenshot.png",
        fileSize: 512000,
        fileType: "image/png",
        progress: 100
      }
    ],
    paymentMethod: "GCash"
  };

  const getBreakpointInfo = (width: number) => {
    if (width < 320) return { name: "XS", color: "bg-red-500", range: "< 320px" };
    if (width < 375) return { name: "Mobile S", color: "bg-orange-500", range: "320px" };
    if (width < 425) return { name: "Mobile M", color: "bg-yellow-500", range: "375px" };
    if (width < 768) return { name: "Mobile L", color: "bg-green-500", range: "425px" };
    if (width < 1024) return { name: "Tablet", color: "bg-blue-500", range: "768px" };
    if (width < 1280) return { name: "Laptop", color: "bg-indigo-500", range: "1024px" };
    if (width < 1440) return { name: "Desktop", color: "bg-purple-500", range: "1280px" };
    return { name: "Large Desktop", color: "bg-pink-500", range: "1440px+" };
  };

  const breakpointInfo = getBreakpointInfo(viewportWidth);

  const testScenarios = [
    {
      name: "Resize to Mobile S (320px)",
      action: () => {
        // Note: This only simulates the viewport indicator, actual resizing requires dev tools
        console.log("Please resize browser to 320px width using dev tools");
      }
    },
    {
      name: "Resize to Mobile M (375px)", 
      action: () => {
        console.log("Please resize browser to 375px width using dev tools");
      }
    },
    {
      name: "Resize to Mobile L (425px)",
      action: () => {
        console.log("Please resize browser to 425px width using dev tools");
      }
    },
    {
      name: "Resize to Tablet (768px)",
      action: () => {
        console.log("Please resize browser to 768px width using dev tools");
      }
    }
  ];

  return (
    <PublicAppShell>
      <div className="min-h-screen bg-gray-50">
        {/* Viewport Indicator */}
        <div className="fixed top-4 right-4 z-50 bg-white shadow-lg rounded-lg p-3 border">
          <div className="text-sm font-medium">Current Viewport</div>
          <div className={`inline-block px-2 py-1 rounded text-white text-xs ${breakpointInfo.color}`}>
            {breakpointInfo.name}
          </div>
          <div className="text-xs text-gray-600 mt-1">{viewportWidth}px ({breakpointInfo.range})</div>
        </div>

        {/* Test Controls */}
        <Card className="mx-4 my-4">
          <CardHeader>
            <CardTitle className="text-lg">Final Summary Layout Test</CardTitle>
            <p className="text-sm text-gray-600">
              Testing content overlap issues across different breakpoints. Use browser dev tools to resize and observe layout behavior.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {testScenarios.map((scenario, index) => (
                <Button
                  key={index}
                  variant="secondary"
                  size="sm"
                  onClick={scenario.action}
                  className="text-xs"
                >
                  {scenario.name}
                </Button>
              ))}
            </div>
            <div className="mt-4 text-xs text-gray-500">
              <strong>Test Checklist:</strong>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>Check for text overlap in pricing section</li>
                <li>Verify button positioning and spacing</li>
                <li>Ensure card content doesn't overflow</li>
                <li>Test readability of long text content</li>
                <li>Validate proper spacing between sections</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* The actual Final Summary component being tested */}
        <div className="max-w-4xl mx-auto px-2 sm:px-3 lg:px-4 py-3 sm:py-4 lg:py-8">
          <ConfirmationStep bookingData={mockBookingData} />
        </div>
      </div>
    </PublicAppShell>
  );
}
