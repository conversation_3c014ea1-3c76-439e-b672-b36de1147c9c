# Typography System Guide

## Overview
A comprehensive typography system built for readability, accessibility, and visual hierarchy. Features optimal line lengths, WCAG AA contrast ratios, and consistent spacing.

## Font Pairing

### Geist Sans - Universal Font
- **Headings**: Geist Sans with enhanced kerning and ligatures
- **Body Text**: Same font family for consistency
- **Fallbacks**: System fonts for reliability

```css
font-family: Geist Sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif
```

## Typography Scale

### Display Headings (Hero Content)
```tsx
<Typography variant="display-1">Hero Headlines</Typography>
<Typography variant="display-2">Section Heroes</Typography>
```

### Content Headings (Structured Content)
```tsx
<Heading level={1}>Page Title</Heading>
<Heading level={2}>Major Section</Heading>
<Heading level={3}>Subsection</Heading>
<Heading level={4}>Component Title</Heading>
<Heading level={5}>Small Section</Heading>
<Heading level={6}>Micro Heading</Heading>
```

### Body Text
```tsx
<Text size="large">Lead paragraphs and emphasis</Text>
<Text size="base">Standard body content</Text>
<Text size="small">Secondary information</Text>
```

### UI Elements
```tsx
<Label>Form labels</Label>
<Caption>Help text and metadata</Caption>
<Typography variant="overline">SECTION MARKERS</Typography>
```

## Line Length and Alignment

### Optimal Reading Width
```tsx
// Automatic optimal width (recommended)
<Text maxWidth="prose">Content with 50-60 chars per line</Text>

// Manual control
<Text maxWidth="lg">Custom width constraint</Text>
```

### Text Alignment
```tsx
<Text align="left">Default alignment</Text>
<Text align="center">Centered text</Text>
<Text align="right">Right-aligned</Text>
<Text align="justify">Justified (use sparingly)</Text>
```

## Color and Contrast

### Text Colors (WCAG AA Compliant)
```tsx
<Text color="default">High contrast (16.94:1)</Text>
<Text color="muted">Medium contrast (9.35:1)</Text>
<Text color="subtle">Minimum AA (4.54:1)</Text>
<Text color="primary">Brand color</Text>
<Text color="success">Success states</Text>
<Text color="warning">Warning states</Text>
<Text color="error">Error states</Text>
<Text color="inverse">Dark backgrounds</Text>
```

## Spacing System

### Vertical Rhythm Classes
```tsx
<div className="space-text-tight">
  {/* 0.5rem spacing between elements */}
</div>

<div className="space-text-normal">
  {/* 1rem spacing (recommended) */}
</div>

<div className="space-text-loose">
  {/* 1.5rem spacing */}
</div>

<div className="space-text-extra-loose">
  {/* 2rem spacing */}
</div>
```

### Heading Spacing
```tsx
<Typography variant="h2" className="heading-spacing">
  Automatic heading spacing
</Typography>
```

## Special Elements

### Quotes and Code
```tsx
<Quote>
  "Typography is the craft of endowing human language with a durable visual form."
</Quote>

<Code>const example = "inline code"</Code>
```

### Lead Text
```tsx
<Lead>
  Introductory paragraph with emphasis and optimal reading width.
</Lead>
```

## Responsive Behavior

### Automatic Scaling
- All typography scales appropriately across devices
- Mobile-optimized line lengths (30-40 characters)
- Desktop-optimized line lengths (50-60 characters)

### Manual Responsive Control
```tsx
<Typography variant="h1" className="text-2xl md:text-4xl lg:text-5xl">
  Custom responsive sizing
</Typography>
```

## Accessibility Features

### Semantic HTML
The system automatically uses appropriate HTML elements:
- `h1-h6` for headings
- `p` for body text
- `label` for form labels
- `blockquote` for quotes
- `code` for code snippets

### Override Element
```tsx
<Typography variant="h1" as="div">
  Heading styling with div element
</Typography>
```

### Screen Reader Support
```tsx
<Typography variant="h2" role="heading" aria-level="2">
  Explicit heading level for complex layouts
</Typography>
```

## Best Practices

### ✅ Do
- Use consistent font pairing (Geist Sans)
- Maintain proper heading hierarchy
- Respect optimal line lengths
- Use semantic colors
- Apply consistent spacing
- Test contrast ratios
- Consider mobile users

### ❌ Avoid
- Mixing too many font families
- Skipping heading levels (h1 → h3)
- Lines longer than 75 characters
- Low contrast text combinations
- Inconsistent spacing
- Overusing decorative typography
- Ignoring responsive behavior

## Common Patterns

### Article Layout
```tsx
<article className="space-text-normal max-w-prose">
  <Typography variant="h1">Article Title</Typography>
  <Lead>Engaging introduction paragraph</Lead>
  <Typography variant="h2">Section Heading</Typography>
  <Text>Body content with optimal reading width</Text>
</article>
```

### Card Component
```tsx
<Card>
  <CardHeader>
    <Typography variant="h3">Card Title</Typography>
    <Text size="small" color="muted">Card description</Text>
  </CardHeader>
  <CardContent>
    <Text>Card content</Text>
  </CardContent>
</Card>
```

### Form Layout
```tsx
<div className="space-y-4">
  <div>
    <Label>Field Name</Label>
    <Caption>Help text for the field</Caption>
  </div>
</div>
```

## Performance Considerations

- Font loading optimized with `font-display: swap`
- System font fallbacks prevent layout shift
- Minimal custom CSS for better caching
- Tree-shakable component exports
