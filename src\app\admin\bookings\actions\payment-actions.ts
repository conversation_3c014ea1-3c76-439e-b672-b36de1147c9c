"use server";

import { createContextClient } from "@/lib/supabase/server";
import {
  sendEmail,
  buildPaymentApprovedEmail,
  buildPaymentRejectedEmail,
} from "@/lib/email";
import { logWithContext, logError } from "@/lib/utils/logger";

export async function verifyPayment(
  paymentId: string,
  action: "verify" | "reject",
  notes?: string
) {
  const supabase = await createContextClient('admin');

  logWithContext("PaymentActions", "STARTING payment verification", {
    paymentId,
    action,
    notes: notes || "No notes provided",
    timestamp: new Date().toISOString()
  });

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  logWithContext("PaymentActions", "Auth debug:", {
    user: user ? { id: user.id, email: user.email } : null,
    authError,
    paymentId,
    action,
  });

  if (!user) {
    return { error: { message: "You must be logged in to verify payments." } };
  }

  // Check if user is admin
  const { data: profile } = await supabase
    .from("profiles")
    .select("role")
    .eq("id", user.id)
    .single();

  logWithContext("PaymentActions", "Admin role check:", {
    userId: user.id,
    role: (profile as any)?.role,
  });

  if (!profile || (profile.role !== "admin" && profile.role !== "super_admin")) {
    return { error: { message: "You must be an admin to verify payments." } };
  }

  const updates: any = {
    verified_by: user.id,
    verified_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  if (action === "verify") {
    updates.status = "Paid";
    updates.verification_notes = notes || "Payment verified by admin";
  } else {
    updates.status = "Rejected";
    updates.verification_notes = notes || "Payment rejected by admin";
  }

  logWithContext("PaymentActions", "Payment update parameters:", {
    paymentId,
    updates,
    action
  });

  const { data, error } = await supabase
    .from("payments")
    .update(updates)
    .eq("id", paymentId)
    .select("id, status, booking_id, amount, verified_by, verified_at");

  if (error) {
    logError("PaymentActions", "CRITICAL: Error updating payment status", {
      paymentId,
      error,
      errorMessage: error?.message,
      errorCode: error?.code,
      updates
    });
    return { error };
  }

  logWithContext("PaymentActions", "SUCCESS: Payment status updated", {
    paymentId,
    updatedData: data,
    previousAction: action,
    newStatus: Array.isArray(data) ? (data[0] as any)?.status : (data as any)?.status
  });

  // Verify the update by fetching again
  const { data: verificationCheck, error: verifyError } = await supabase
    .from("payments")
    .select("id, status, booking_id, verified_by, verified_at")
    .eq("id", paymentId)
    .single();

  if (!verifyError && verificationCheck) {
    logWithContext("PaymentActions", "Payment verification FINAL CHECK", {
      paymentId,
      verificationCheck,
      statusConfirmed: verificationCheck.status === "Paid"
    });
  } else {
    logError("PaymentActions", "Failed to verify payment update", {
      paymentId,
      verifyError: verifyError?.message
    });
  }

  // Send notification email (best-effort, non-blocking failure)
  try {
    const updated = Array.isArray(data) ? (data[0] as any) : (data as any);
    // Ensure we have booking_id
    let bookingId = updated?.booking_id as string | undefined;
    if (!bookingId) {
      const { data: pmt } = await supabase
        .from("payments")
        .select("booking_id")
        .eq("id", paymentId)
        .single();
      bookingId = (pmt as any)?.booking_id;
    }

    if (bookingId) {
      // Fetch booking to get customer_id
      const { data: booking } = await supabase
        .from("bookings")
        .select("id, customer_id")
        .eq("id", bookingId)
        .single();

      const customerId = (booking as any)?.customer_id as string | undefined;
      if (customerId) {
        // Fetch customer profile for email + name
        const { data: customer } = await supabase
          .from("profiles")
          .select("email, full_name")
          .eq("id", customerId)
          .single();

        const to = (customer as any)?.email as string | undefined;
        const customerName = (customer as any)?.full_name as string | undefined;

        if (to) {
          if (action === "verify") {
            const { subject, html } = buildPaymentApprovedEmail({
              customerName: customerName || null,
              bookingId,
              dashboardUrl: "https://olliesrentalcar.pathlinkio.app/customer/dashboard",
            });
            await sendEmail({ to, subject, html });
          } else {
            const { subject, html } = buildPaymentRejectedEmail({
              customerName: customerName || null,
              bookingId,
              notes: notes || updated?.verification_notes || null,
              dashboardUrl: "https://olliesrentalcar.pathlinkio.app/customer/dashboard",
            });
            await sendEmail({ to, subject, html });
          }
        }
      }
    }
  } catch (notifyErr) {
    console.warn("Payment verified, but email notification failed/skipped:", notifyErr);
  }

  return { data };
}

export async function getPaymentForBooking(bookingId: string) {
  const supabase = await createContextClient('admin');

  const {
    data: { user },
    error: authError
  } = await supabase.auth.getUser();

  logWithContext("PaymentActions", "Auth debug:", { 
    user: user ? { id: user.id, email: user.email } : null, 
    authError,
    bookingId 
  });

  if (!user) {
    logError("PaymentActions", "No user found in getPaymentForBooking", "Authentication required");
    return { error: { message: "You must be logged in to view payments." } };
  }

  const { data, error } = await supabase
    .from("payments")
    .select("*")
    .eq("booking_id", bookingId)
    .single();

  if (error && error.code !== "PGRST116") {
    // PGRST116 = no rows returned
    console.error("Error fetching payment:", error);
    return { error };
  }

  return { data: data || null };
}
