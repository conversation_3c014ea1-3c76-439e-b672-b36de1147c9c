import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, Sheet<PERSON>ontent, SheetHeader, Sheet<PERSON><PERSON>le, SheetTrigger } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { cn } from "@/lib/utils";
import type { SettingsSection } from "@/lib/types";

interface MobileNavDrawerProps {
  sidebarItems: ReadonlyArray<{
    id: string;
    label: string;
    icon: React.ElementType;
  }>;
  activeSection: string;
  setActiveSection: (section: SettingsSection) => void;
  dirtyState: { [key: string]: boolean };
  onOpenSettingsModal: () => void;
}

export function MobileNavDrawer({
  sidebarItems,
  activeSection,
  setActiveSection,
  dirtyState,
  onOpenSettingsModal,
}: MobileNavDrawerProps) {
  const [open, setOpen] = React.useState(false);

  const handleSectionClick = (sectionId: string) => {
    setActiveSection(sectionId as SettingsSection);
    setOpen(false);
    onOpenSettingsModal();
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button
          variant="secondary"
          size="icon"
          className="lg:hidden"
          aria-label="Open navigation menu"
        >
          <Menu className="h-5 w-5" />
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[280px] sm:w-[320px] p-0">
        <SheetHeader className="p-6 border-b border-gray-200">
          <SheetTitle>Settings</SheetTitle>
          <p className="text-sm text-gray-600 mt-1">
            Configure your car rental business settings
          </p>
        </SheetHeader>
        <ScrollArea className="h-[calc(100vh-120px)]">
          <nav className="p-4 space-y-1">
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleSectionClick(item.id)}
                className={cn(
                  "w-full flex items-center gap-3 px-3 py-3 text-sm rounded-lg transition-all",
                  "hover:bg-gray-100 hover:text-gray-900",
                  activeSection === item.id
                    ? "bg-blue-50 text-blue-700 border border-blue-200 font-medium"
                    : "text-gray-700",
                  dirtyState[item.id] && "relative"
                )}
              >
                <item.icon className="h-4 w-4 flex-shrink-0" />
                <span className="flex-1 text-left">{item.label}</span>
                {dirtyState[item.id] && (
                  <div className="w-2 h-2 rounded-full bg-orange-500 flex-shrink-0" />
                )}
              </button>
            ))}
          </nav>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
}
