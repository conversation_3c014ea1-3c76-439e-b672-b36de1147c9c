"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { type Car } from "@/lib/types";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { ModernInput } from "@/components/ui/modern-input";
import { DateTimePicker } from "@/components/ui/datetime-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { LocationDropdown } from "@/components/ui/location-dropdown";
import {
  FixedPickupLocationField,
  DropOffLocationDropdown,
  FIXED_PICKUP_LOCATION,
  PickupLocationField,
  DropOffLocationField,
} from "@/components/ui/booking-location-components";
import {
  Calendar,
  MapPin,
  User,
  Phone,
  Mail,
  CheckCircle,
  MessageCircle,
  X,
} from "lucide-react";
import {
  LoadingOverlay,
  BookingLoading,
  useLoadingState,
} from "@/components/ui/loading";

interface BookingModalProps {
  car: Car | null;
  isOpen: boolean;
  onClose: () => void;
}

export function BookingModal({ car, isOpen, onClose }: BookingModalProps) {
  const router = useRouter();
  const [formData, setFormData] = React.useState({
    pickUpDate: "",
    pickUpTime: "08:00",
    pickUpLocation: "", // Use dynamic pickup location
    dropOffLocation: "",
    dropOffDate: "",
    dropOffTime: "19:00",
    customerName: "",
    customerEmail: "",
    customerPhone: "",
  });

  const { isLoading, startLoading, stopLoading } = useLoadingState(2000); // Minimum 2 seconds for booking
  const [isCompleted, setIsCompleted] = React.useState(false);
  const [bookingId, setBookingId] = React.useState("");

  // Reset states when modal opens/closes
  React.useEffect(() => {
    if (isOpen) {
      setIsCompleted(false);
      setBookingId("");
    }
  }, [isOpen]);

  const formatDateTime = (dateString: string, timeString: string = "") => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const dateStr = date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    if (timeString) {
      const [hours, minutes] = timeString.split(":");
      const hour = parseInt(hours);
      const ampm = hour >= 12 ? "PM" : "AM";
      const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour;
      return `${dateStr} at ${displayHour}:${minutes} ${ampm}`;
    }

    return dateStr;
  };

  const redirectToMessenger = () => {
    if (!car) return;

    // Construct the message with booking request details
    const message = `Hi! I would like to make a booking request through OllieTrack:

🚗 Car: ${car.model}
📋 Booking Reference: ${bookingId}
📅 Pick-up: ${formData.pickUpLocation} on ${formatDateTime(
      formData.pickUpDate,
      formData.pickUpTime
    )}
📅 Drop-off: ${formData.dropOffLocation} on ${formatDateTime(
      formData.dropOffDate,
      formData.dropOffTime
    )}
💰 Estimated Total: ₱${totalPrice.toFixed(2)}

Customer Details:
👤 Name: ${formData.customerName}
📧 Email: ${formData.customerEmail}
📱 Phone: ${formData.customerPhone}

Could you please confirm the availability and let me know the next steps to finalize this booking? Thank you!`;

    // For Facebook Messenger (replace with actual page ID)
    const messengerUrl = `https://m.me/ollietrack?text=${encodeURIComponent(
      message
    )}`;

    // Open with proper security attributes
    window.open(messengerUrl, "_blank", "noopener,noreferrer");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Start loading state
    startLoading();

    try {
      // Generate a simple booking reference ID for customer reference
      const bookingRef = `OT-${Date.now().toString().slice(-6)}-${car!.id
        .slice(0, 4)
        .toUpperCase()}`;

      // Simulate processing delay for better UX
      await new Promise((resolve) => setTimeout(resolve, 2500));

      // Set the booking reference and mark as completed
      setBookingId(bookingRef);
      setIsCompleted(true);

      console.log("Booking submitted:", { car, ...formData, bookingRef });

      // Stop loading
      stopLoading();

      // REMOVED: Auto-redirect to messenger - now only happens on explicit user action
      // User must click "Finalize in Messenger" button to proceed
    } catch (error) {
      console.error("Booking failed:", error);
      stopLoading();
    }
  };

  // Debounced messenger redirect to prevent double-fire
  const [isRedirecting, setIsRedirecting] = React.useState(false);

  const handleMessengerRedirect = async () => {
    if (isRedirecting) return; // Prevent concurrent clicks

    setIsRedirecting(true);

    try {
      redirectToMessenger();

      // Optional: Show a brief confirmation that Messenger was opened
      // Could add a toast here if desired
    } catch (error) {
      console.error("Failed to open Messenger:", error);
    } finally {
      // Reset after a brief delay to allow Messenger to open
      setTimeout(() => setIsRedirecting(false), 1000);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  if (!car) return null;

  // Calculate rental days and total price
  const calculateRentalDays = () => {
    if (
      !formData.pickUpDate ||
      !formData.dropOffDate ||
      !formData.pickUpTime ||
      !formData.dropOffTime
    )
      return 1;

    const pickUpDateTime = new Date(
      `${formData.pickUpDate}T${formData.pickUpTime}:00`
    );
    const dropOffDateTime = new Date(
      `${formData.dropOffDate}T${formData.dropOffTime}:00`
    );

    // Calculate the difference in time
    const timeDifference = dropOffDateTime.getTime() - pickUpDateTime.getTime();

    // Convert time difference from milliseconds to days
    const daysDifference = Math.ceil(timeDifference / (1000 * 3600 * 24));

    // Ensure minimum 1 day rental
    return Math.max(1, daysDifference);
  };

  const rentalDays = calculateRentalDays();
  const totalPrice = car.price_per_day * rentalDays;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="booking-modal max-w-2xl max-h-[85vh] flex flex-col p-0 gap-0">
        <LoadingOverlay
          isVisible={isLoading}
          loadingComponent={<BookingLoading />}
          className="flex flex-col h-full"
        >
          {/* Fixed Header */}
          <DialogHeader className="flex-shrink-0 px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-3">
                <div>
                  <DialogTitle className="text-xl font-semibold text-left">
                    Book {car.model}
                  </DialogTitle>
                  <DialogDescription className="text-left">
                    Complete your rental reservation by providing the required
                    details below.
                  </DialogDescription>
                </div>
              </div>
              <Button 
                variant="secondary" 
                size="icon" 
                className="flex items-center justify-center h-8 w-8 rounded-full hover:bg-gray-100" 
                onClick={onClose}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </Button>
            </div>
          </DialogHeader>

          {/* Scrollable Content */}
          <div
            className="flex-1 overflow-y-auto px-6 py-6 min-h-0 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-400"
            style={{ maxHeight: "calc(85vh - 280px)" }}
          >
            {isCompleted ? (
              // Success State
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Booking Request Submitted!
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Your booking request has been prepared. Click below to send it
                  to us via Messenger to finalize the details, confirm
                  availability, and arrange payment.
                </p>
                <div className="bg-blue-50 rounded-lg p-4 mb-6">
                  <p className="text-sm font-medium text-blue-900">
                    Booking Reference: {bookingId}
                  </p>
                  <p className="text-sm text-blue-800">
                    Please mention this reference when contacting us.
                  </p>
                </div>

                {/* Car Summary in Success State */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-100 mb-6">
                  <div className="flex items-center gap-4">
                    <div className="relative w-24 h-18 bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100">
                      <img
                        src={car.image_url}
                        alt={car.model}
                        className="w-full h-full object-contain p-1"
                      />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-bold text-gray-900 text-lg">
                        {car.model}
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">
                        {car.transmission}
                      </p>
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold text-blue-600">
                          ₱{totalPrice.toFixed(2)}
                        </span>
                        <span className="text-sm text-gray-500 font-medium">
                          for {rentalDays} day{rentalDays > 1 ? "s" : ""}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button
                    onClick={handleMessengerRedirect}
                    disabled={isRedirecting}
                    className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                    aria-live="polite"
                  >
                    <MessageCircle className="h-4 w-4" />
                    {isRedirecting
                      ? "Opening Messenger..."
                      : "Finalize in Messenger"}
                  </Button>
                  <Button
                    variant="secondary"
                    disabled={isRedirecting}
                    onClick={() => {
                      onClose();
                      router.push("/customer/dashboard");
                    }}
                  >
                    Go to Dashboard
                  </Button>
                </div>
              </div>
            ) : (
              // Booking Form
              <form
                onSubmit={handleSubmit}
                className="space-y-8"
                id="booking-form"
              >
                {/* Car Summary */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-100">
                  <div className="flex items-center gap-4">
                    <div className="relative w-24 h-18 bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100">
                      <img
                        src={car.image_url}
                        alt={car.model}
                        className="w-full h-full object-contain p-1"
                      />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-bold text-gray-900 text-lg">
                        {car.model}
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">
                        {car.transmission}
                      </p>
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold text-blue-600">
                          ₱{car.price_per_day}
                        </span>
                        <span className="text-sm text-gray-500 font-medium">
                          /day
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Location Selection */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <PickupLocationField
                    value={formData.pickUpLocation}
                    onValueChange={(value) =>
                      handleInputChange("pickUpLocation", value)
                    }
                    placeholder="Select pickup location"
                    showLabel={true}
                    className="w-full"
                  />

                  <DropOffLocationField
                    value={formData.dropOffLocation}
                    onValueChange={(value) =>
                      handleInputChange("dropOffLocation", value)
                    }
                    placeholder="Select drop-off location"
                    showLabel={true}
                    className="w-full"
                  />
                </div>

                {/* Date and Time Selection */}
                <DateTimePicker
                  pickupDate={formData.pickUpDate}
                  pickupTime={formData.pickUpTime}
                  dropoffDate={formData.dropOffDate}
                  dropoffTime={formData.dropOffTime}
                  onPickupDateChange={(date) =>
                    handleInputChange("pickUpDate", date)
                  }
                  onPickupTimeChange={(time) =>
                    handleInputChange("pickUpTime", time)
                  }
                  onDropoffDateChange={(date) =>
                    handleInputChange("dropOffDate", date)
                  }
                  onDropoffTimeChange={(time) =>
                    handleInputChange("dropOffTime", time)
                  }
                />

                {/* Customer Information */}
                <div className="space-y-4">
                  <Label className="text-sm font-medium text-gray-700">
                    Customer Information
                  </Label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div className="flex items-center gap-3">
                      <User className="h-5 w-5 text-blue-600 flex-shrink-0" />
                      <div className="flex-1">
                        <ModernInput
                          placeholder="Full Name"
                          value={formData.customerName}
                          onChange={(e) =>
                            handleInputChange("customerName", e.target.value)
                          }
                          required
                        />
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Mail className="h-5 w-5 text-blue-600 flex-shrink-0" />
                      <div className="flex-1">
                        <ModernInput
                          type="email"
                          placeholder="Email Address"
                          value={formData.customerEmail}
                          onChange={(e) =>
                            handleInputChange("customerEmail", e.target.value)
                          }
                          required
                        />
                      </div>
                    </div>
                    <div className="flex items-center gap-3 sm:col-span-2">
                      <Phone className="h-5 w-5 text-blue-600 flex-shrink-0" />
                      <div className="flex-1">
                        <ModernInput
                          type="tel"
                          placeholder="Phone Number"
                          value={formData.customerPhone}
                          onChange={(e) =>
                            handleInputChange("customerPhone", e.target.value)
                          }
                          required
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Pricing Summary */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 border border-green-100 shadow-sm">
                  <h4 className="font-semibold text-gray-900 mb-4 text-center">
                    Rental Summary
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center py-2">
                      <span className="text-sm text-gray-600">Daily Rate</span>
                      <span className="font-semibold text-gray-900">
                        ₱{car.price_per_day.toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2">
                      <span className="text-sm text-gray-600">
                        Number of Days
                      </span>
                      <span className="font-semibold text-gray-900">
                        {rentalDays} day{rentalDays > 1 ? "s" : ""}
                      </span>
                    </div>
                    <div className="border-t border-green-200 pt-3 mt-3">
                      <div className="flex justify-between items-center">
                        <span className="font-bold text-gray-900 text-lg">
                          Total Amount
                        </span>
                        <span className="text-2xl font-bold text-green-600">
                          ₱{totalPrice.toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            )}
          </div>

          {/* Fixed Footer - only show for form state */}
          {!isCompleted && (
            <div className="flex-shrink-0 px-6 py-5 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50">
              {/* Button descriptions */}
              <div className="text-center mb-5">
                <p className="text-xs text-gray-600 bg-white px-4 py-2 rounded-full border border-gray-200 inline-block">
                  By confirming, you agree to our rental terms and your booking
                  will be processed immediately.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={onClose}
                  className="flex-1 order-2 sm:order-1 h-auto py-3 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200"
                  disabled={isLoading}
                >
                  <span className="flex flex-col items-center gap-1">
                    <span className="font-semibold">Cancel Booking</span>
                    <span className="text-xs opacity-70">
                      Return to vehicle selection
                    </span>
                  </span>
                </Button>
                <Button
                  type="submit"
                  form="booking-form"
                  className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 order-1 sm:order-2 h-auto py-3 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                  disabled={
                    isLoading ||
                    !formData.pickUpLocation ||
                    !formData.pickUpDate ||
                    !formData.dropOffDate ||
                    !formData.dropOffLocation ||
                    !formData.customerName ||
                    !formData.customerEmail ||
                    !formData.customerPhone
                  }
                >
                  <div className="flex flex-col items-center gap-1">
                    <span className="font-semibold">Confirm & Book Now</span>
                    <span className="text-xs opacity-90 font-medium">
                      ₱{totalPrice.toFixed(2)} for {rentalDays} day
                      {rentalDays > 1 ? "s" : ""}
                    </span>
                  </div>
                </Button>
              </div>
            </div>
          )}
        </LoadingOverlay>
      </DialogContent>
    </Dialog>
  );
}
