@echo off
REM PathLink GPS Tracker GUI - Windows Launcher
REM This batch file launches the PathLink GUI application

echo ========================================
echo PathLink GPS Tracker - Control Panel
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    echo.
    pause
    exit /b 1
)

echo Python found. Checking dependencies...
echo.

REM Check if required packages are installed
python -c "import requests, websocket" >nul 2>&1
if errorlevel 1 (
    echo Installing required dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        echo Please run: pip install -r requirements.txt
        echo.
        pause
        exit /b 1
    )
    echo Dependencies installed successfully!
    echo.
)

echo Launching PathLink GUI...
echo.

REM Launch the application
python pathlink_gui.py

REM Check if the application exited with an error
if errorlevel 1 (
    echo.
    echo ERROR: Application exited with an error
    echo Check the console output above for details
    echo.
    pause
)

echo.
echo Application closed.
pause
