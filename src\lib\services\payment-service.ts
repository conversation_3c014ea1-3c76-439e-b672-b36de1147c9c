"use server";

import { createContextClient } from "@/lib/supabase/server";
import { uploadPaymentProof } from "@/lib/file-upload";

export interface PaymentRecord {
  id?: string;
  booking_id: string;
  amount: number;
  method: "GCash" | "Bank Transfer" | "Remittance Center";
  status: "Pending Verification" | "Paid" | "Rejected" | "Refunded";
  proof_of_payment_url?: string | null;
  reference_number?: string | null;
  transaction_date?: string;
  verification_notes?: string | null;
  verified_by?: string | null;
  verified_at?: string | null;
  created_at?: string;
  updated_at?: string;
}

// Ensure errors are serializable and descriptive when returned to the client
function normalizeError(err: unknown) {
  if (!err) return { message: "Unknown error" };
  if (typeof err === "string") return { message: err };
  if (err instanceof Error) return { message: err.message, name: err.name };
  const anyErr = err as Record<string, unknown>;
  return {
    message: (anyErr.message as string) || "Unexpected error",
    code: anyErr.code as string | undefined,
    details: anyErr.details as string | undefined,
    hint: anyErr.hint as string | undefined,
    status: anyErr.status as number | undefined,
  };
}

/**
 * Create or update a payment record in the payments table
 */
export async function upsertPaymentRecord(
  paymentData: Omit<PaymentRecord, "id" | "created_at" | "updated_at">
): Promise<{ data: PaymentRecord | null; error: any }> {
  const supabase = await createContextClient('customer');

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      data: null,
      error: { message: "You must be logged in to create payment records." },
    };
  }

  // Verify the booking belongs to the current user
  const { data: booking, error: bookingError } = await supabase
    .from("bookings")
    .select("id, customer_id, total_amount")
    .eq("id", paymentData.booking_id)
    .eq("customer_id", user.id)
    .single();

  if (bookingError || !booking) {
    return {
      data: null,
      error: normalizeError(bookingError ?? { message: "Booking not found or access denied." }),
    };
  }

  // Check if payment record already exists
  const { data: existingPayment, error: existingError } = await supabase
    .from("payments")
    .select("id")
    .eq("booking_id", paymentData.booking_id)
    .maybeSingle();

  if (existingError) {
    console.error("Error checking existing payment:", existingError);
    return { data: null, error: normalizeError(existingError) };
  }

  let result;
  
  if (existingPayment) {
    // Update existing payment record
    const { data, error } = await supabase
      .from("payments")
      .update({
        amount: paymentData.amount,
        method: paymentData.method,
        status: paymentData.status,
        proof_of_payment_url: paymentData.proof_of_payment_url,
        reference_number: paymentData.reference_number,
        transaction_date: paymentData.transaction_date || new Date().toISOString(),
        verification_notes: paymentData.verification_notes,
        updated_at: new Date().toISOString(),
      })
      .eq("id", existingPayment.id)
      .select()
      .single();

    result = { data, error };
  } else {
    // Insert new payment record
    const { data, error } = await supabase
      .from("payments")
      .insert([{
        ...paymentData,
        transaction_date: paymentData.transaction_date || new Date().toISOString(),
      }])
      .select()
      .single();

    result = { data, error };
  }

  if (result.error) {
    console.error("Error upserting payment record:", result.error);
    return { data: null, error: normalizeError(result.error) };
  }

  return { data: result.data, error: null };
}

/**
 * Upload payment proof file and create/update payment record
 */
export async function uploadAndRecordPayment(
  file: File,
  paymentData: Omit<PaymentRecord, "id" | "proof_of_payment_url" | "created_at" | "updated_at">
): Promise<{ data: PaymentRecord | null; error: any }> {
  const supabase = await createContextClient('customer');

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      data: null,
      error: { message: "You must be logged in to upload payment proof." },
    };
  }

  try {
    // Upload file to payment bucket
    const uploadResult = await uploadPaymentProof(file, user.id, paymentData.booking_id);

    if (uploadResult.error) {
      return {
        data: null,
        error: { message: uploadResult.error },
      };
    }

    // Create/update payment record with file URL
    const paymentResult = await upsertPaymentRecord({
      ...paymentData,
      proof_of_payment_url: uploadResult.url,
    });

    return paymentResult;
  } catch (error) {
    console.error("Error in uploadAndRecordPayment:", error);
    return {
      data: null,
      error: normalizeError(error),
    };
  }
}

/**
 * Get payment status for a specific booking
 */
export async function getPaymentByBookingId(
  bookingId: string
): Promise<{ data: PaymentRecord | null; error: any }> {
  const supabase = await createContextClient('customer');

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      data: null,
      error: { message: "You must be logged in to view payment status." },
    };
  }

  // Verify the booking belongs to the current user
  const { data: booking, error: bookingError } = await supabase
    .from("bookings")
    .select("id, customer_id")
    .eq("id", bookingId)
    .eq("customer_id", user.id)
    .single();

  if (bookingError || !booking) {
    return {
      data: null,
      error: normalizeError(bookingError ?? { message: "Booking not found or access denied." }),
    };
  }

  const { data: payment, error: paymentError } = await supabase
    .from("payments")
    .select("*")
    .eq("booking_id", bookingId)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (paymentError) {
    console.error("Error fetching payment:", paymentError);
    return { data: null, error: normalizeError(paymentError) };
  }

  return { data: payment, error: null };
}
