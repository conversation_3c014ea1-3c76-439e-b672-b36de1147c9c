-- ============================================
-- USER LEGAL DOCUMENTS TABLE
-- ============================================
-- Stores legal document uploads for user profiles
-- Independent of booking-specific documents

CREATE TABLE public.user_legal_documents (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  document_type TEXT NOT NULL CHECK (document_type IN (
    'drivers_license',
    'government_id', 
    'proof_of_billing'
  )),
  file_url TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL, -- e.g., 'application/pdf', 'image/jpeg'
  verification_status TEXT NOT NULL DEFAULT 'pending' CHECK (verification_status IN (
    'pending',
    'approved',
    'rejected',
    'requires_resubmission'
  )),
  verification_notes TEXT, -- Admin notes for rejected documents
  verified_by UUID REFERENCES public.profiles(id), -- Admin who verified
  verified_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure only one document per type per user
  UNIQUE(user_id, document_type)
);

-- Enable RLS for user_legal_documents
ALTER TABLE public.user_legal_documents ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_legal_documents
CREATE POLICY "Users can view their own legal documents" 
  ON public.user_legal_documents FOR SELECT 
  TO authenticated
  USING (
    user_id = (SELECT auth.uid()) OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Users can upload their own legal documents" 
  ON public.user_legal_documents FOR INSERT 
  TO authenticated
  WITH CHECK (
    user_id = (SELECT auth.uid())
  );

CREATE POLICY "Users can update their own legal documents or admins can update any" 
  ON public.user_legal_documents FOR UPDATE 
  TO authenticated
  USING (
    user_id = (SELECT auth.uid()) OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  )
  WITH CHECK (
    user_id = (SELECT auth.uid()) OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Users can delete their own legal documents or admins can delete any" 
  ON public.user_legal_documents FOR DELETE 
  TO authenticated
  USING (
    user_id = (SELECT auth.uid()) OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- Create indexes for better performance
CREATE INDEX idx_user_legal_documents_user_id ON public.user_legal_documents(user_id);
CREATE INDEX idx_user_legal_documents_type ON public.user_legal_documents(document_type);
CREATE INDEX idx_user_legal_documents_status ON public.user_legal_documents(verification_status);
CREATE INDEX idx_user_legal_documents_created ON public.user_legal_documents(created_at);

-- Create updated_at trigger
CREATE TRIGGER handle_updated_at_user_legal_documents 
  BEFORE UPDATE ON public.user_legal_documents 
  FOR EACH ROW 
  EXECUTE FUNCTION handle_updated_at();

-- Helper function to check if all required legal documents are uploaded for a user
CREATE OR REPLACE FUNCTION check_user_legal_documents_complete(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    required_docs TEXT[] := ARRAY['drivers_license', 'government_id', 'proof_of_billing'];
    uploaded_count INTEGER;
BEGIN
    SELECT COUNT(DISTINCT document_type) 
    INTO uploaded_count
    FROM public.user_legal_documents 
    WHERE user_id = user_uuid 
    AND document_type = ANY(required_docs);
    
    RETURN uploaded_count = array_length(required_docs, 1);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get user legal document status summary
CREATE OR REPLACE FUNCTION get_user_legal_document_status(user_uuid UUID)
RETURNS TABLE (
    total_required INTEGER,
    uploaded_count INTEGER,
    pending_count INTEGER,
    approved_count INTEGER,
    rejected_count INTEGER,
    is_complete BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        3 as total_required,
        COUNT(*)::INTEGER as uploaded_count,
        COUNT(*) FILTER (WHERE verification_status = 'pending')::INTEGER as pending_count,
        COUNT(*) FILTER (WHERE verification_status = 'approved')::INTEGER as approved_count,
        COUNT(*) FILTER (WHERE verification_status = 'rejected')::INTEGER as rejected_count,
        check_user_legal_documents_complete(user_uuid) as is_complete
    FROM public.user_legal_documents 
    WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
