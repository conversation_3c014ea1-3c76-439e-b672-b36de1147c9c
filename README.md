# Ollie Track – Car Rental Management System powered by Pathlink

Version: 1.0  
Date: 2025-08-08  
Prepared by: AIM PROJECTS Team

## Overview
Pathlink is a responsive web-based car rental management system designed for both customers and administrators. The platform enables users to browse available cars, book rentals, manage reservations, and make payments online. Administrators can manage inventory, bookings, and car status via a secure backend.

This repository currently contains the frontend built with Next.js 15 (App Router), React 19, and Tailwind CSS 4. Backend services are not included yet and are planned: Convex, or Node/Express with Supabase.

## Goals
- Provide an intuitive, mobile-first booking experience for customers.
- Offer real-time availability and pricing.
- Enable admins to manage cars, bookings, and payments efficiently.
- Ensure scalable, modular architecture for future expansion.

## Features & Requirements

### Customer-Facing UI

#### Booking Widget (Homepage)
- Dropdown for pick-up method:
  - “Pick-up at <PERSON><PERSON>’s Garage”
  - “Drop-off Delivery” (reveals delivery location dropdown)
- Location selector (city or delivery address)
- Pick-up date & time selectors
- Drop-off date & time selectors
- Car model selector (optional at this stage)
- Special requests text area
- “Search” or “View Available Cars” button

#### Car Catalog Page
- Search & filter options:
  - Car type (SUV, Sport, Coupe, etc.)
  - Availability dates
  - Condition (Good, Needs Repair)
- Car cards with:
  - Image
  - Name & type
  - Rental cost/day (with discount if applicable)
  - Transmission, fuel capacity, seating capacity
  - “Rent Now” button
- Responsive grid layout

#### Booking Summary Page
- Display selected car details
- Pick-up & drop-off details
- Total rental cost (including discounts, if any)
- “Confirm Booking” button
- Payment section (integrated with Stripe/PayMongo API)
- Error handling for:
  - Car unavailable
  - Invalid date/time selection

#### User Dashboard
- Current bookings (status: Active, Completed, Cancelled)
- Past bookings history
- Payment history
- Cancel booking option (if within allowed cancellation window)
- Download invoice option

### Admin-Facing UI

#### Dashboard
- Summary widgets:
  - Cars currently rented
  - Cars under maintenance
  - Recent bookings
  - Revenue overview
- Alerts for:
  - Overlapping bookings
  - Cars needing maintenance
  - Payment issues

#### Car Management
- CRUD for car records:
  - Model, type, plate number
  - Status (Available, Booked, Unavailable)
  - Condition (Good, Needs Repair)
  - Availability window
  - Image upload
  - Maintenance notes

#### Booking Management
- View all bookings
- Edit/update booking status
- Archive booking histories
- Manually adjust availability

#### Payment Management
- View all transactions
- Payment status (Pending, Paid, Failed)
- Refund processing

### Backend Requirements (Planned)

#### Data Models

##### Cars
- id
- model
- type
- plateNumber
- status
- condition
- availability
- fuelCapacity
- transmission
- seats
- pricePerDay
- imageURL

##### Bookings
- id
- customerId
- carId
- pickUpLocation
- dropOffLocation
- pickUpDateTime
- dropOffDateTime
- specialRequests
- status

##### Users
- id
- name
- email
- phone
- role

##### Payments
- id
- bookingId
- amount
- status
- method
- transactionDate

#### APIs
- CRUD for cars, bookings, users
- Availability check logic
- Payment integration endpoint

#### Validation & Security
- Sanitize all inputs
- Role-based access control (Admin/Customer)
- Error handling with meaningful messages

## Getting Started

- Clone the repository (default branch is `main`):
  ```bash
  git clone --branch dev-beta https://github.com/lucasram20/PathLink.git
  cd PathLink
  ```

- Install required tools (Windows):
  ```powershell
  # Git
  winget install --id Git.Git -e

  # Node.js LTS
  winget install -e --id OpenJS.NodeJS.LTS
  node -v

  # pnpm via Corepack (recommended)
  npm i -g corepack@latest
  corepack enable pnpm
  corepack use pnpm@latest-10

  # or pnpm via PowerShell script (from docs)
  Invoke-WebRequest https://get.pnpm.io/install.ps1 -UseBasicParsing | Invoke-Expression
  ```
  Reference: [pnpm installation (Windows)](https://pnpm.io/installation#on-windows)

- Install required tools (macOS):
  ```bash
  # Xcode Command Line Tools (provides Git and build tools)
  xcode-select --install
  
  # Optional: Homebrew package manager (if not installed)
  # Apple Silicon (M1/M2/M3):
  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
  echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
  eval "$(/opt/homebrew/bin/brew shellenv)"
  
  # Intel (x86_64):
  # /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
  # echo 'eval "$(/usr/local/bin/brew shellenv)"' >> ~/.zprofile
  # eval "$(/usr/local/bin/brew shellenv)"
  
  # Verify Git
  git --version
  
  # Install nvm (Node Version Manager)
  # Official install script: https://github.com/nvm-sh/nvm#installing-and-updating
  curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
  
  # Load nvm (restart terminal OR source your shell config)
  export NVM_DIR="$HOME/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"
  
  # Install and use Node.js 20 (see .nvmrc)
  nvm install 20
  nvm use 20
  node -v
  
  # pnpm via Corepack (recommended)
  corepack enable
  corepack prepare pnpm@latest-10 --activate
  pnpm -v
  
  # Alternatively, install pnpm with Homebrew
  # brew install pnpm
  ```
  References: [Git for macOS](https://git-scm.com/downloads/mac) · [Homebrew install](https://docs.brew.sh/Installation) · [nvm install](https://github.com/nvm-sh/nvm#installing-and-updating) · [pnpm installation (macOS)](https://pnpm.io/installation#macos)

- Prerequisites: Node.js 18.18+ (recommended 20+) and pnpm 9+
- Install pnpm (if not installed):
  ```bash
  corepack enable
  corepack prepare pnpm@latest --activate
  ```
- Install dependencies:
  ```bash
  pnpm install
  ```
- Run the dev server (http://localhost:3000):
  ```bash
  pnpm dev
  ```
- Build and start:
  ```bash
  pnpm build
  pnpm start
  ```

## Available Scripts

- `pnpm dev`: Start Next.js in development mode
- `pnpm build`: Build the production bundle
- `pnpm start`: Start the production server
- `pnpm lint`: Run Next.js lint

## Project Structure

```
├── config/             # Application configuration
│   └── components.json # shadcn/ui components configuration
├── docs/               # Documentation files
├── iot/                # IoT and GPS tracking related files
├── public/             # Static assets (images, icons, etc.)
├── scripts/            # Build and deployment scripts
├── src/                # Source code
│   ├── app/            # Next.js App Router pages and layouts
│   │   ├── admin/      # Admin dashboard pages
│   │   ├── auth/       # Authentication pages
│   │   ├── customer/   # Customer-facing pages
│   │   └── api/        # API routes
│   ├── components/     # Reusable React components
│   │   ├── ui/         # shadcn/ui base components
│   │   ├── admin/      # Admin-specific components
│   │   ├── auth/       # Authentication components
│   │   ├── customer-side/ # Customer-facing components
│   │   ├── layout/     # Layout components
│   │   └── nav/        # Navigation components
│   ├── hooks/          # Custom React hooks
│   ├── lib/            # Utility libraries and configurations
│   │   ├── auth/       # Authentication utilities
│   │   └── supabase/   # Supabase client and database utilities
│   └── styles/         # Global CSS and styling
└── tests/              # Test files (E2E and integration tests)
```

### Key Directories

- **`src/app/`**: Next.js App Router structure with pages and API routes
- **`src/components/`**: Modular components organized by feature area
- **`src/lib/`**: Core utilities, database configurations, and business logic
- **`src/hooks/`**: Reusable React hooks for state management and side effects
- **`config/`**: Application-wide configuration files
- **`public/`**: Static assets served directly by Next.js

## Technical Stack
- Frontend: Next.js 15 (App Router) + React 19, Tailwind CSS 4
- UI/Forms: Radix UI, React Hook Form, Zod, Lucide Icons
- Backend: Not yet implemented in this repo (planned: Convex, or Node/Express with Supabase)
- Payment API: Planned Stripe or PayMongo
- Hosting: Vercel (Frontend)
- Auth: TBD (NextAuth.js, Convex Auth, or Supabase Auth)

## Non-Functional Requirements
- Fully responsive (mobile, tablet, desktop)
- Fast loading (under 2s for main pages)
- Accessible design (WCAG 2.1 compliant)
- Scalable for >10,000 concurrent users

## Milestones
- Week 1–2: UI prototyping (Customer side)
- Week 3: Admin panel base setup
- Week 4: Convex backend setup with CRUD
- Week 5: Payment integration
- Week 6: Testing & bug fixes
- Week 7: Deployment
