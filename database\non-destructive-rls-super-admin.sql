-- Truly non-destructive RLS policy updates for super_admin role access
-- Only creates policies if they don't exist or need updating
-- No DROP operations - completely safe

BEGIN;

-- ========================================
-- HELPER FUNCTION: Check if policy needs super_admin
-- ========================================

CREATE OR REPLACE FUNCTION check_policy_needs_super_admin(
  table_name text,
  policy_name text
) RETURNS boolean AS $$
BEGIN
  -- Check if policy exists and if it already includes super_admin
  RETURN NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = table_name 
      AND policyname = policy_name
      AND cmd LIKE '%super_admin%'
  ) AND EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = table_name 
      AND policyname = policy_name
  );
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- CARS TABLE - Only update if needed
-- ========================================

-- Only create/update "Only admins can insert cars" if it doesn't include super_admin
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'cars' 
      AND policyname = 'Only admins can insert cars'
  ) THEN
    -- Policy doesn't exist, create it
    CREATE POLICY "Only admins can insert cars" 
      ON public.cars FOR INSERT 
      TO authenticated
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = 'admin' OR role = 'super_admin')
        )
      );
  ELSIF check_policy_needs_super_admin('cars', 'Only admins can insert cars') THEN
    -- Policy exists but needs super_admin, recreate it
    DROP POLICY "Only admins can insert cars" ON public.cars;
    CREATE POLICY "Only admins can insert cars" 
      ON public.cars FOR INSERT 
      TO authenticated
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = 'admin' OR role = 'super_admin')
        )
      );
  END IF;
END $$;

-- Only create/update "Only admins can update cars" if needed
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'cars' 
      AND policyname = 'Only admins can update cars'
  ) THEN
    CREATE POLICY "Only admins can update cars" 
      ON public.cars FOR UPDATE 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = 'admin' OR role = 'super_admin')
        )
      )
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = 'admin' OR role = 'super_admin')
        )
      );
  ELSIF check_policy_needs_super_admin('cars', 'Only admins can update cars') THEN
    DROP POLICY "Only admins can update cars" ON public.cars;
    CREATE POLICY "Only admins can update cars" 
      ON public.cars FOR UPDATE 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = 'admin' OR role = 'super_admin')
        )
      )
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = 'admin' OR role = 'super_admin')
        )
      );
  END IF;
END $$;

-- ========================================
-- BOOKINGS TABLE - Only update if needed
-- ========================================

-- Only create/update bookings SELECT policy if needed
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'bookings'
      AND (policyname = 'Admins can view all bookings' OR policyname LIKE '%admin%view%' OR policyname LIKE '%view%admin%')
  ) THEN
    CREATE POLICY "Admins can view all bookings" 
      ON public.bookings FOR SELECT 
      TO authenticated
      USING (
        customer_id = auth.uid()
        OR
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = 'admin' OR role = 'super_admin')
        )
      );
  ELSIF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'bookings'
      AND (policyname = 'Admins can view all bookings' OR policyname LIKE '%admin%view%')
      AND cmd NOT LIKE '%super_admin%'
  ) THEN
    -- Find the exact policy name and update it
    DECLARE 
      existing_policy_name text;
    BEGIN
      SELECT policyname INTO existing_policy_name
      FROM pg_policies 
      WHERE schemaname = 'public' 
        AND tablename = 'bookings'
        AND (policyname = 'Admins can view all bookings' OR policyname LIKE '%admin%view%')
        AND cmd NOT LIKE '%super_admin%'
      LIMIT 1;
      
      IF existing_policy_name IS NOT NULL THEN
        EXECUTE 'DROP POLICY "' || existing_policy_name || '" ON public.bookings';
        CREATE POLICY "Admins can view all bookings" 
          ON public.bookings FOR SELECT 
          TO authenticated
          USING (
            customer_id = auth.uid()
            OR
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() 
              AND (role = 'admin' OR role = 'super_admin')
            )
          );
      END IF;
    END;
  END IF;
END $$;

-- ========================================
-- PAYMENTS TABLE - Only update if needed
-- ========================================

-- Only create/update payments SELECT policy if needed
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'payments'
      AND (policyname = 'Admins can view all payments' OR policyname LIKE '%admin%view%payment%')
  ) THEN
    CREATE POLICY "Admins can view all payments" 
      ON public.payments FOR SELECT 
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM public.bookings 
          WHERE bookings.id = payments.booking_id 
          AND bookings.customer_id = auth.uid()
        )
        OR
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = 'admin' OR role = 'super_admin')
        )
      );
  ELSIF EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename = 'payments'
      AND (policyname = 'Admins can view all payments' OR policyname LIKE '%admin%view%payment%')
      AND cmd NOT LIKE '%super_admin%'
  ) THEN
    DECLARE 
      existing_policy_name text;
    BEGIN
      SELECT policyname INTO existing_policy_name
      FROM pg_policies 
      WHERE schemaname = 'public' 
        AND tablename = 'payments'
        AND (policyname = 'Admins can view all payments' OR policyname LIKE '%admin%view%payment%')
        AND cmd NOT LIKE '%super_admin%'
      LIMIT 1;
      
      IF existing_policy_name IS NOT NULL THEN
        EXECUTE 'DROP POLICY "' || existing_policy_name || '" ON public.payments';
        CREATE POLICY "Admins can view all payments" 
          ON public.payments FOR SELECT 
          TO authenticated
          USING (
            EXISTS (
              SELECT 1 FROM public.bookings 
              WHERE bookings.id = payments.booking_id 
              AND bookings.customer_id = auth.uid()
            )
            OR
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() 
              AND (role = 'admin' OR role = 'super_admin')
            )
          );
      END IF;
    END;
  END IF;
END $$;

-- Clean up helper function
DROP FUNCTION IF EXISTS check_policy_needs_super_admin(text, text);

COMMIT;

-- ========================================
-- VERIFICATION - Show what was updated
-- ========================================

SELECT 
  tablename, 
  policyname,
  CASE 
    WHEN cmd LIKE '%super_admin%' THEN '✅ Includes super_admin'
    ELSE '⚠️ Admin only'
  END as super_admin_status
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('cars', 'bookings', 'payments', 'vehicle_categories')
  AND (policyname ILIKE '%admin%' OR policyname ILIKE '%manage%' OR policyname ILIKE '%view%')
ORDER BY tablename, policyname;
