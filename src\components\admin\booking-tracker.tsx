"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { listAllBookings, listCars, getCurrentUser } from "@/lib/store"
import { formatBookingIdForDisplay } from "@/lib/reference-ids"
import { Calendar, Clock, User, Car as CarIcon } from "lucide-react"
import { format } from "date-fns"

interface BookingWithDetails {
  id: string
  renterName: string
  carName: string
  carModel: string
  pickUpDateTime: Date
  dropOffDateTime: Date
  status: string
  totalAmount: number
}

export function BookingTracker() {
  const bookings = listAllBookings()
  const cars = listCars()
  
  // Create a map of car IDs to car details for quick lookup
  const carMap = new Map(cars.map(car => [car.id, car]))
  
  // Transform bookings to include car details and sort by nearest upcoming start date
  const bookingsWithDetails: BookingWithDetails[] = bookings
    .map(booking => {
      const car = carMap.get(booking.carId)
      return {
        id: booking.id,
        renterName: "John Doe", // In real app, would fetch from user data
        carName: car?.model || "Unknown Car",
        carModel: car?.type || "Unknown",
        pickUpDateTime: new Date(booking.pickUpDateTime),
        dropOffDateTime: new Date(booking.dropOffDateTime),
        status: booking.status,
        totalAmount: booking.totalAmount
      }
    })
    .sort((a, b) => {
      // Sort by pickup date - nearest upcoming first
      const now = new Date()
      const aUpcoming = a.pickUpDateTime >= now
      const bUpcoming = b.pickUpDateTime >= now
      
      if (aUpcoming && bUpcoming) {
        return a.pickUpDateTime.getTime() - b.pickUpDateTime.getTime()
      } else if (aUpcoming && !bUpcoming) {
        return -1
      } else if (!aUpcoming && bUpcoming) {
        return 1
      } else {
        return b.pickUpDateTime.getTime() - a.pickUpDateTime.getTime()
      }
    })

  const getStatusConfig = (status: string) => {
    switch (status) {
      case "Active":
        return { color: "bg-green-100 text-green-800", label: "Active" }
      case "Pending":
        return { color: "bg-yellow-100 text-yellow-800", label: "Pending" }
      case "Completed":
        return { color: "bg-gray-100 text-gray-800", label: "Completed" }
      case "Cancelled":
        return { color: "bg-red-100 text-red-800", label: "Cancelled" }
      default:
        return { color: "bg-gray-100 text-gray-800", label: status }
    }
  }

  const formatDateTime = (date: Date) => {
    return {
      date: format(date, "MMM dd, yyyy"),
      time: format(date, "h:mm a")
    }
  }

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl font-semibold text-gray-900">Booking Tracker</CardTitle>
            <p className="text-sm text-gray-600 mt-1">Real-time overview of current and upcoming bookings</p>
          </div>
          <div className="flex items-center gap-2 text-blue-600">
            <Calendar className="h-5 w-5" />
            <span className="text-sm font-medium">{bookingsWithDetails.length} Total Bookings</span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {bookingsWithDetails.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No bookings found</p>
            </div>
          ) : (
            bookingsWithDetails.map((booking) => {
              const statusConfig = getStatusConfig(booking.status)
              const pickupFormatted = formatDateTime(booking.pickUpDateTime)
              const dropoffFormatted = formatDateTime(booking.dropOffDateTime)
              
              return (
                <div 
                  key={booking.id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-10 h-10 bg-blue-50 rounded-full">
                        <User className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{booking.renterName}</h3>
                        <p className="text-sm text-gray-600">Booking {formatBookingIdForDisplay(booking)}</p>
                      </div>
                    </div>
                    <Badge className={statusConfig.color}>
                      {statusConfig.label}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Car Information */}
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                        <CarIcon className="h-4 w-4 text-gray-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{booking.carName}</p>
                        <p className="text-sm text-gray-600">{booking.carModel}</p>
                      </div>
                    </div>

                    {/* Pickup Information */}
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-green-50 rounded-full">
                        <Calendar className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">Pick-up</p>
                        <p className="text-sm text-gray-600">{pickupFormatted.date}</p>
                        <p className="text-xs text-gray-500">{pickupFormatted.time}</p>
                      </div>
                    </div>

                    {/* Dropoff Information */}
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-orange-50 rounded-full">
                        <Clock className="h-4 w-4 text-orange-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">Drop-off</p>
                        <p className="text-sm text-gray-600">{dropoffFormatted.date}</p>
                        <p className="text-xs text-gray-500">{dropoffFormatted.time}</p>
                      </div>
                    </div>
                  </div>

                  {/* Total Amount */}
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Total Amount</span>
                      <span className="text-lg font-bold text-blue-600">₱{booking.totalAmount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              )
            })
          )}
        </div>
      </CardContent>
    </Card>
  )
}
