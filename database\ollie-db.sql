-- ============================================
-- Ollie Track - Supabase Database Schema
-- ============================================
-- This schema creates all necessary tables for the Ollie Track car rental system
-- with proper Row Level Security (RLS) policies

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================
-- 1. PROFILES TABLE (extends auth.users)
-- ============================================
-- Create a profiles table that extends Supabase auth.users
CREATE TABLE public.profiles (
  id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  full_name TEXT,
  phone TEXT,
  role TEXT NOT NULL DEFAULT 'customer' CHECK (role IN ('customer', 'admin')),
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  PRIMARY KEY (id)
);

-- Enable RLS for profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Profiles RLS Policies
CREATE POLICY "Public profiles are viewable by everyone" 
  ON public.profiles FOR SELECT 
  USING (true);

CREATE POLICY "Users can insert their own profile" 
  ON public.profiles FOR INSERT 
  WITH CHECK ((SELECT auth.uid()) = id);

CREATE POLICY "Users can update their own profile" 
  ON public.profiles FOR UPDATE 
  USING ((SELECT auth.uid()) = id)
  WITH CHECK ((SELECT auth.uid()) = id);

-- ============================================
-- 2. CARS TABLE
-- ============================================
CREATE TABLE public.cars (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  model TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('SUV', 'Sport', 'Coupe', 'Hatchback', 'MPV', 'Sedan')),
  plate_number TEXT NOT NULL UNIQUE,
  color TEXT NOT NULL DEFAULT 'White', -- NEW: Vehicle color field
  status TEXT NOT NULL DEFAULT 'Available' CHECK (status IN ('Available', 'Rented', 'In Maintenance')),
  condition TEXT NOT NULL DEFAULT 'Good' CHECK (condition IN ('Good', 'Needs Repair')),
  fuel_capacity INTEGER NOT NULL,
  fuel_type TEXT NOT NULL,
  transmission TEXT NOT NULL CHECK (transmission IN ('Manual', 'Automatic', 'CVT')),
  seats INTEGER NOT NULL,
  price_per_day DECIMAL(10,2) NOT NULL,
  image_url TEXT,
  notes TEXT,
  is_archived BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- GPS tracking fields
  current_latitude DECIMAL(10, 8),
  current_longitude DECIMAL(11, 8),
  last_gps_update TIMESTAMPTZ
);

-- Enable RLS for cars
ALTER TABLE public.cars ENABLE ROW LEVEL SECURITY;

-- Cars RLS Policies
CREATE POLICY "Cars are viewable by everyone" 
  ON public.cars FOR SELECT 
  USING (NOT is_archived OR is_archived = FALSE);

CREATE POLICY "Only admins can insert cars" 
  ON public.cars FOR INSERT 
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Only admins can update cars" 
  ON public.cars FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Only admins can delete cars" 
  ON public.cars FOR DELETE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- ============================================
-- 3. SIMPLE ARCHIVE FUNCTIONS (using is_archived field only)
-- ============================================

-- Function to archive a car (simple soft delete)
CREATE OR REPLACE FUNCTION public.archive_car(
  p_car_id UUID,
  p_reason TEXT DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  current_user_id UUID;
BEGIN
  -- Check if user is admin
  current_user_id := auth.uid();
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = current_user_id AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Only admins can archive cars';
  END IF;

  -- Check if car exists and is not already archived
  IF NOT EXISTS (
    SELECT 1 FROM public.cars WHERE id = p_car_id AND is_archived = FALSE
  ) THEN
    RAISE EXCEPTION 'Car not found or already archived';
  END IF;

  -- Mark car as archived (soft delete)
  UPDATE public.cars 
  SET is_archived = TRUE, updated_at = NOW()
  WHERE id = p_car_id;

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Failed to archive car: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to restore a car from archive
CREATE OR REPLACE FUNCTION public.restore_car(
  p_car_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
  current_user_id UUID;
BEGIN
  -- Check if user is admin
  current_user_id := auth.uid();
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = current_user_id AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Only admins can restore cars';
  END IF;

  -- Check if car exists and is archived
  IF NOT EXISTS (
    SELECT 1 FROM public.cars WHERE id = p_car_id AND is_archived = TRUE
  ) THEN
    RAISE EXCEPTION 'Car not found or not archived';
  END IF;

  -- Restore car (unmark as archived)
  UPDATE public.cars 
  SET is_archived = FALSE, updated_at = NOW()
  WHERE id = p_car_id;

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Failed to restore car: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get archived cars (simple query from cars table)
CREATE OR REPLACE FUNCTION public.get_archived_cars()
RETURNS TABLE (
  id UUID,
  model TEXT,
  type TEXT,
  plate_number TEXT,
  color TEXT,
  status TEXT,
  condition TEXT,
  fuel_capacity INTEGER,
  fuel_type TEXT,
  transmission TEXT,
  seats INTEGER,
  price_per_day DECIMAL(10,2),
  image_url TEXT,
  notes TEXT,
  is_archived BOOLEAN,
  current_latitude DECIMAL(10, 8),
  current_longitude DECIMAL(11, 8),
  last_gps_update TIMESTAMPTZ,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) AS $$
BEGIN
  -- Check if user is admin
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Only admins can view archived cars';
  END IF;

  RETURN QUERY
  SELECT 
    c.id,
    c.model,
    c.type,
    c.plate_number,
    c.color,
    c.status,
    c.condition,
    c.fuel_capacity,
    c.fuel_type,
    c.transmission,
    c.seats,
    c.price_per_day,
    c.image_url,
    c.notes,
    c.is_archived,
    c.current_latitude,
    c.current_longitude,
    c.last_gps_update,
    c.created_at,
    c.updated_at
  FROM public.cars c
  WHERE c.is_archived = TRUE
  ORDER BY c.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================
-- 4. BOOKINGS TABLE
-- ============================================
CREATE TABLE public.bookings (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  customer_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  car_id UUID NOT NULL REFERENCES public.cars(id) ON DELETE CASCADE,
  pickup_location TEXT NOT NULL,
  dropoff_location TEXT NOT NULL,
  pickup_datetime TIMESTAMPTZ NOT NULL,
  dropoff_datetime TIMESTAMPTZ NOT NULL,
  special_requests TEXT,
  status TEXT NOT NULL DEFAULT 'Pending' CHECK (status IN ('Pending', 'Active', 'Completed', 'Cancelled')),
  total_amount DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS for bookings
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;

-- Bookings RLS Policies
CREATE POLICY "Users can view their own bookings" 
  ON public.bookings FOR SELECT 
  TO authenticated
  USING (
    customer_id = (SELECT auth.uid()) OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Authenticated users can create bookings" 
  ON public.bookings FOR INSERT 
  TO authenticated
  WITH CHECK (customer_id = (SELECT auth.uid()));

CREATE POLICY "Users can update their own bookings or admins can update any" 
  ON public.bookings FOR UPDATE 
  TO authenticated
  USING (
    customer_id = (SELECT auth.uid()) OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  )
  WITH CHECK (
    customer_id = (SELECT auth.uid()) OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Users can cancel their own bookings or admins can delete any" 
  ON public.bookings FOR DELETE 
  TO authenticated
  USING (
    customer_id = (SELECT auth.uid()) OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- ============================================
-- 5. PAYMENTS TABLE
-- ============================================
CREATE TABLE public.payments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  booking_id UUID NOT NULL REFERENCES public.bookings(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'Pending' CHECK (status IN ('Pending', 'Paid', 'Failed', 'Refunded')),
  method TEXT NOT NULL CHECK (method IN ('Card', 'Wallet', 'Cash')),
  transaction_id TEXT,
  transaction_date TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS for payments
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

-- Payments RLS Policies
CREATE POLICY "Users can view payments for their bookings" 
  ON public.payments FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.bookings 
      WHERE id = booking_id AND (
        customer_id = (SELECT auth.uid()) OR 
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = (SELECT auth.uid()) AND role = 'admin'
        )
      )
    )
  );

CREATE POLICY "Only admins can insert payments" 
  ON public.payments FOR INSERT 
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Only admins can update payments" 
  ON public.payments FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- ============================================
-- 6. GPS LOCATIONS TABLE (for real-time tracking)
-- ============================================
CREATE TABLE public.gps_locations (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  car_id UUID NOT NULL REFERENCES public.cars(id) ON DELETE CASCADE,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  speed DECIMAL(5,2) DEFAULT 0, -- km/h
  heading DECIMAL(5,2) DEFAULT 0, -- degrees
  status TEXT NOT NULL DEFAULT 'offline' CHECK (status IN ('active', 'idle', 'offline')),
  driver_id UUID REFERENCES public.profiles(id),
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS for GPS locations
ALTER TABLE public.gps_locations ENABLE ROW LEVEL SECURITY;

-- GPS Locations RLS Policies
CREATE POLICY "Only admins can view GPS locations" 
  ON public.gps_locations FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Only admins can insert GPS locations" 
  ON public.gps_locations FOR INSERT 
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Only admins can update GPS locations" 
  ON public.gps_locations FOR UPDATE 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- ============================================
-- 7. CAR ROUTES TABLE (for tracking history)
-- ============================================
CREATE TABLE public.car_routes (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  car_id UUID NOT NULL REFERENCES public.cars(id) ON DELETE CASCADE,
  booking_id UUID REFERENCES public.bookings(id) ON DELETE SET NULL,
  route_data JSONB NOT NULL, -- Array of {lat, lng, timestamp} objects
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ,
  total_distance DECIMAL(10,2), -- in kilometers
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS for car routes
ALTER TABLE public.car_routes ENABLE ROW LEVEL SECURITY;

-- Car Routes RLS Policies
CREATE POLICY "Only admins can view car routes" 
  ON public.car_routes FOR SELECT 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "Only admins can manage car routes" 
  ON public.car_routes FOR ALL 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- ============================================
-- 8. TRIGGERS AND FUNCTIONS
-- ============================================

-- Function to automatically create a profile when a user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
    COALESCE(NEW.raw_user_meta_data->>'role', 'customer')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call handle_new_user function
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers to relevant tables
CREATE TRIGGER handle_updated_at_profiles
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_cars
  BEFORE UPDATE ON public.cars
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_bookings
  BEFORE UPDATE ON public.bookings
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_payments
  BEFORE UPDATE ON public.payments
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- ============================================
-- 9. INDEXES FOR PERFORMANCE
-- ============================================

-- Indexes for profiles
CREATE INDEX idx_profiles_role ON public.profiles(role);
CREATE INDEX idx_profiles_email ON public.profiles(email);

-- Indexes for cars
CREATE INDEX idx_cars_status ON public.cars(status);
CREATE INDEX idx_cars_type ON public.cars(type);
CREATE INDEX idx_cars_color ON public.cars(color); -- NEW: Index for color searches
CREATE INDEX idx_cars_archived ON public.cars(is_archived);

-- No separate archived_cars table - using is_archived field in cars table

-- Indexes for bookings
CREATE INDEX idx_bookings_customer ON public.bookings(customer_id);
CREATE INDEX idx_bookings_car ON public.bookings(car_id);
CREATE INDEX idx_bookings_status ON public.bookings(status);
CREATE INDEX idx_bookings_dates ON public.bookings(pickup_datetime, dropoff_datetime);

-- Indexes for payments
CREATE INDEX idx_payments_booking ON public.payments(booking_id);
CREATE INDEX idx_payments_status ON public.payments(status);

-- Indexes for GPS locations
CREATE INDEX idx_gps_car ON public.gps_locations(car_id);
CREATE INDEX idx_gps_timestamp ON public.gps_locations(timestamp);
CREATE INDEX idx_gps_status ON public.gps_locations(status);

-- Indexes for car routes
CREATE INDEX idx_routes_car ON public.car_routes(car_id);
CREATE INDEX idx_routes_booking ON public.car_routes(booking_id);
CREATE INDEX idx_routes_time ON public.car_routes(start_time, end_time);

-- ============================================
-- 10. SAMPLE DATA (for development/testing)
-- ============================================

-- Insert sample cars with colors (will only work if run by an admin)
INSERT INTO public.cars (id, model, type, plate_number, color, status, condition, fuel_capacity, fuel_type, transmission, seats, price_per_day, image_url, notes) VALUES
  (uuid_generate_v4(), 'Toyota Vios E 2016 AT', 'Coupe', 'ABC-1001', 'Silver', 'Available', 'Good', 40, 'Gas/Premium', 'Automatic', 5, 1500.00, '/toyota_vios_e_2016_at.png', NULL),
  (uuid_generate_v4(), 'Toyota Vios XLE 2023 CVT', 'Coupe', 'ABC-1002', 'White', 'Available', 'Good', 40, 'Gas/Premium', 'CVT', 5, 1800.00, '/toyota_vios_xle_2023_cvt.png', NULL),
  (uuid_generate_v4(), 'Yamaha Mio i125 2019 CVT', 'Sport', 'BIKE-1001', 'Blue', 'Available', 'Good', 5, 'Unleaded', 'CVT', 2, 500.00, '/yamaha_mio_i125_2019_cvt.png', NULL),
  (uuid_generate_v4(), 'Yamaha NMAX 2019 CVT', 'Sport', 'BIKE-1002', 'Black', 'Available', 'Good', 7, 'Unleaded', 'CVT', 2, 700.00, '/yamaha_nmax_2019_cvt.png', NULL),
  (uuid_generate_v4(), 'Toyota Fortuner G 2017 AT', 'SUV', 'SUV-3001', 'Gray', 'Available', 'Good', 80, 'Diesel', 'Automatic', 7, 3000.00, '/toyota_fortuner_g_2017_at.png', NULL),
  (uuid_generate_v4(), 'Toyota Fortuner V 2018 AT', 'SUV', 'SUV-3002', 'Black', 'Available', 'Good', 80, 'Diesel', 'Automatic', 7, 3200.00, '/toyota_fortuner_v_2018_at.png', NULL),
  (uuid_generate_v4(), 'Toyota GL Grandia 2011 MT', 'MPV', 'VAN-2001', 'White', 'Available', 'Good', 70, 'Diesel', 'Manual', 12, 3800.00, '/toyota_gl_grandia_2011_mt.png', 'With Driver only. Rate depends on destination.'),
  (uuid_generate_v4(), 'Mitsubishi Mirage GLS 2019 AT', 'Hatchback', 'HAT-4001', 'Red', 'Available', 'Good', 35, 'Gas/Premium', 'Automatic', 5, 1600.00, '/mitsubishi_mirage_gls_2019_at.png', NULL),
  (uuid_generate_v4(), 'Mitsubishi Xpander GLS 2019 AT', 'MPV', 'MPV-4001', 'Silver', 'Available', 'Good', 45, 'Gas/Premium', 'Automatic', 7, 2200.00, '/mitsubishi_xpander_gls_2019_at.png', NULL),
  (uuid_generate_v4(), 'Mitsubishi Xpander GLS 2023 AT', 'MPV', 'MPV-4002', 'Pearl White', 'Available', 'Good', 45, 'Gas/Premium', 'Automatic', 7, 2500.00, '/mitsubishi_xpander_gls_2023_at.png', NULL),
  (uuid_generate_v4(), 'Mitsubishi Montero 2014 MT', 'SUV', 'SUV-4001', 'Dark Gray', 'Available', 'Good', 88, 'Diesel', 'Manual', 8, 2500.00, '/mitsubishi_montero_2014_mt.png', NULL),
  (uuid_generate_v4(), 'Honda CRV 2007 AT', 'SUV', 'SUV-5001', 'Champagne', 'Available', 'Good', 58, 'Unleaded', 'Automatic', 6, 2000.00, '/honda_crv_2007_at.png', NULL);

-- ============================================
-- 11. MIGRATION SCRIPT (Add color to existing installations)
-- ============================================

-- For existing databases, run this to add the color field:
-- ALTER TABLE public.cars ADD COLUMN color TEXT NOT NULL DEFAULT 'White';
-- CREATE INDEX idx_cars_color ON public.cars(color);

-- ============================================
-- 12. REALTIME SETUP
-- ============================================

-- Enable realtime for important tables
ALTER PUBLICATION supabase_realtime ADD TABLE public.bookings;
ALTER PUBLICATION supabase_realtime ADD TABLE public.cars;
ALTER PUBLICATION supabase_realtime ADD TABLE public.gps_locations;
ALTER PUBLICATION supabase_realtime ADD TABLE public.payments;

-- ============================================
-- NOTES FOR DEVELOPERS
-- ============================================
-- 
-- 1. Admin Authentication:
--    - Admins should be created directly in the database with role='admin'
--    - The admin login page checks for the 'admin' role in user metadata
--    - Admin accounts should not be created through signup flows
--
-- 2. Customer Authentication:
--    - Customers can signup normally through the customer signup page
--    - Their accounts are automatically created with role='customer'
--    - The trigger handles profile creation automatically
--
-- 3. Security:
--    - All tables have RLS enabled
--    - Customers can only see their own bookings and payments
--    - Admins have full access to manage cars, bookings, and view GPS data
--    - GPS tracking is admin-only for privacy and security
--
-- 4. GPS Tracking:
--    - Real-time location updates are stored in gps_locations table
--    - Historical routes are stored in car_routes table with JSONB data
--    - Only admins can access GPS data
--
-- 5. Performance:
--    - Proper indexes are created for common query patterns
--    - RLS policies are optimized to use these indexes
--    - Consider partitioning GPS tables for high-volume deployments
--
-- 6. Color Field:
--    - Added to both cars and archived_cars tables
--    - Defaults to 'White' for consistency
--    - Indexed for efficient color-based filtering
--    - Sample data includes realistic vehicle colors
--
