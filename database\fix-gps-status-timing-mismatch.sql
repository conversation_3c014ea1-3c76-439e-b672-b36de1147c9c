-- ============================================
-- FIX GPS STATUS TIMING MISMATCH
-- ============================================
-- Fix the inconsistency where database marks GPS as offline after 2 minutes
-- but frontend expects data within 5 minutes

-- Update the get_latest_gps_per_car function to use 5-minute (300 seconds) threshold
-- This matches the frontend expectation and prevents premature offline status
CREATE OR REPLACE FUNCTION get_latest_gps_per_car()
RETURNS TABLE (
  id uuid,
  car_id uuid,
  latitude double precision,
  longitude double precision,
  speed double precision,
  heading double precision,
  status text,
  "timestamp" timestamptz,
  driver_id uuid,
  created_at timestamptz,
  car jsonb
) 
LANGUAGE sql
SECURITY DEFINER
AS $$
  WITH latest_per_car AS (
    SELECT DISTINCT ON (gl.car_id)
      gl.id,
      gl.car_id,
      gl.latitude,
      gl.longitude,
      gl.speed,
      gl.heading,
      CASE 
        -- FIXED: Use 5-minute (300 seconds) threshold to match frontend
        -- If GPS data is older than 5 minutes, mark as offline
        WHEN EXTRACT(EPOCH FROM (NOW() - gl."timestamp")) > 300 THEN 'offline'
        -- If explicitly set as offline in database, keep offline
        WHEN gl.status = 'offline' THEN 'offline'
        -- Otherwise use the original status (active, idle, etc.)
        ELSE gl.status
      END as status,
      gl."timestamp",
      gl.driver_id,
      gl.created_at,
      jsonb_build_object(
        'model', c.model,
        'plate_number', c.plate_number,
        'data_age_seconds', EXTRACT(EPOCH FROM (NOW() - gl."timestamp"))
      ) as car_info
    FROM gps_locations gl
    INNER JOIN cars c ON gl.car_id = c.id
    INNER JOIN gps_device_mapping gdm ON c.id = gdm.car_id
    WHERE gdm.is_active = true
    ORDER BY gl.car_id, gl."timestamp" DESC
  )
  SELECT 
    lpc.id,
    lpc.car_id,
    lpc.latitude,
    lpc.longitude,
    lpc.speed,
    lpc.heading,
    lpc.status,
    lpc."timestamp",
    lpc.driver_id,
    lpc.created_at,
    lpc.car_info as car
  FROM latest_per_car lpc;
$$;

-- Comment explaining the fix
COMMENT ON FUNCTION get_latest_gps_per_car() IS 'Returns latest GPS location per car. Uses 5-minute offline threshold to match frontend expectations. Status: active=moving, idle=stationary but connected, offline=no recent data or disconnected.';
