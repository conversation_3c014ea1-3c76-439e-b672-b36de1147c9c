# Supabase OTP Configuration Guide

## Problem
You're getting confirmation link emails instead of OTP code emails.

## Solution

### 1. Check Supabase Project Settings

**Go to Supabase Dashboard → Authentication → Settings:**

#### Email Settings Section:
- ✅ **Enable email confirmations**: ON
- ✅ **Secure email change**: ON  
- ✅ **Double confirm email changes**: ON

#### Authentication Providers Section:
- ✅ **Email**: Enabled
- ✅ **Enable email OTP**: **THIS MUST BE ON**

### 2. Email Template Configuration

**Go to Authentication → Email Templates → Confirm signup:**

Make sure you're editing the **"Confirm signup"** template, not the "Magic Link" template.

The template should use `{{ .Token }}` to display the OTP code.

### 3. Alternative: Force OTP Request

If the above doesn't work, we can explicitly request OTP by using a different flow:

1. Sign up the user
2. Immediately request an OTP for email confirmation
3. Direct them to the verification page

### 4. Check Your Supabase Version

Older versions of Supabase might not support email OTP. Make sure your project is updated.

### 5. Test Configuration

After making changes:
1. Try signing up with a new email
2. Check if you receive an OTP code instead of a link
3. The email subject should be about verification, not confirmation links

### 6. Debug Steps

If still not working:
1. Check Supabase logs for any errors
2. Verify the email template is saved correctly
3. Test with different email providers
4. Check if your Supabase project has OTP enabled in the plan

## Expected Result

After proper configuration, signup emails should contain a 6-digit numeric code like:

```
123456
```

Instead of a "Confirm your mail" link.
