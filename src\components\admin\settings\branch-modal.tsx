import * as React from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle, DialogClose } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { cn } from "@/lib/utils"
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  MapPin,
  Phone,
  Clock,
  Building,
  Check,
  AlertCircle
} from "lucide-react"
import type { Branch, BusinessHours, DayHours } from "@/lib/types"

interface BranchModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (branch: Omit<Branch, 'id' | 'created_at' | 'updated_at'>) => Promise<void>
  onUpdate: (id: string, updates: Partial<Branch>) => Promise<void>
  branch?: Branch | null
  saving?: boolean
}

interface BusinessHoursFormProps {
  businessHours: BusinessHours
  onChange: (hours: BusinessHours) => void
}

const DEFAULT_BUSINESS_HOURS: BusinessHours = {
  monday: { is_open: true, open_time: "08:00", close_time: "18:00" },
  tuesday: { is_open: true, open_time: "08:00", close_time: "18:00" },
  wednesday: { is_open: true, open_time: "08:00", close_time: "18:00" },
  thursday: { is_open: true, open_time: "08:00", close_time: "18:00" },
  friday: { is_open: true, open_time: "08:00", close_time: "18:00" },
  saturday: { is_open: true, open_time: "08:00", close_time: "17:00" },
  sunday: { is_open: false }
}

export function BranchModal({ isOpen, onClose, onSave, onUpdate, branch, saving }: BranchModalProps) {
  const { toast } = useToast()
  const [formData, setFormData] = React.useState({
    name: "",
    address: "",
    city: "",
    province: "",
    latitude: undefined as number | undefined,
    longitude: undefined as number | undefined,
    contact_numbers: [""],
    business_hours: DEFAULT_BUSINESS_HOURS,
    pickup_available: true,
    dropoff_available: true,
    blackout_dates: [] as string[],
    is_active: true
  })

  const [errors, setErrors] = React.useState<{ [key: string]: string }>({})

  React.useEffect(() => {
    if (branch) {
      setFormData({
        name: branch.name,
        address: branch.address,
        city: branch.city,
        province: branch.province,
        latitude: branch.latitude,
        longitude: branch.longitude,
        contact_numbers: branch.contact_numbers.length > 0 ? branch.contact_numbers : [""],
        business_hours: branch.business_hours,
        pickup_available: branch.pickup_available,
        dropoff_available: branch.dropoff_available,
        blackout_dates: branch.blackout_dates,
        is_active: branch.is_active
      })
    } else if (isOpen) {
      // Reset form for new branch
      setFormData({
        name: "",
        address: "",
        city: "",
        province: "",
        latitude: undefined,
        longitude: undefined,
        contact_numbers: [""],
        business_hours: DEFAULT_BUSINESS_HOURS,
        pickup_available: true,
        dropoff_available: true,
        blackout_dates: [],
        is_active: true
      })
    }
    setErrors({})
  }, [branch, isOpen])

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {}

    if (!formData.name.trim()) {
      newErrors.name = "Branch name is required"
    }
    if (!formData.address.trim()) {
      newErrors.address = "Address is required"
    }
    if (!formData.city.trim()) {
      newErrors.city = "City is required"
    }
    if (!formData.province.trim()) {
      newErrors.province = "Province is required"
    }

    const validPhoneNumbers = formData.contact_numbers.filter(num => num.trim())
    if (validPhoneNumbers.length === 0) {
      newErrors.contact_numbers = "At least one contact number is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please fix the errors in the form"
      })
      return
    }

    const cleanedContactNumbers = formData.contact_numbers.filter(num => num.trim())
    
    const branchData = {
      ...formData,
      contact_numbers: cleanedContactNumbers
    }

    try {
      if (branch) {
        await onUpdate(branch.id, branchData)
      } else {
        await onSave(branchData)
      }
      onClose()
    } catch (error) {
      console.error("Failed to save branch:", error)
    }
  }

  const updateContactNumber = (index: number, value: string) => {
    const newNumbers = [...formData.contact_numbers]
    newNumbers[index] = value
    setFormData(prev => ({ ...prev, contact_numbers: newNumbers }))
  }

  const addContactNumber = () => {
    setFormData(prev => ({
      ...prev,
      contact_numbers: [...prev.contact_numbers, ""]
    }))
  }

  const removeContactNumber = (index: number) => {
    if (formData.contact_numbers.length > 1) {
      const newNumbers = formData.contact_numbers.filter((_, i) => i !== index)
      setFormData(prev => ({ ...prev, contact_numbers: newNumbers }))
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent 
        className="max-w-3xl max-h-[90vh] overflow-hidden bg-white z-[1200]"
        data-testid="admin-settings-modal"
        showCloseButton={false}
      >
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            {branch ? "Edit Branch" : "Add New Branch"}
          </DialogTitle>
          <DialogClose className="rounded-full h-8 w-8 flex items-center justify-center hover:bg-gray-100">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <ScrollArea className="max-h-[70vh] pr-4">
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="branch_name">
                        Branch Name *
                      </Label>
                      <Input
                        id="branch_name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Main Branch"
                        className={cn(errors.name && "border-red-500")}
                      />
                      {errors.name && (
                        <p className="text-xs text-red-600">{errors.name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="branch_address">
                        Address *
                      </Label>
                      <Input
                        id="branch_address"
                        value={formData.address}
                        onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                        placeholder="123 Main Street"
                        className={cn(errors.address && "border-red-500")}
                      />
                      {errors.address && (
                        <p className="text-xs text-red-600">{errors.address}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="branch_city">
                        City *
                      </Label>
                      <Input
                        id="branch_city"
                        value={formData.city}
                        onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                        placeholder="Laoag City"
                        className={cn(errors.city && "border-red-500")}
                      />
                      {errors.city && (
                        <p className="text-xs text-red-600">{errors.city}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="branch_province">
                        Province *
                      </Label>
                      <Input
                        id="branch_province"
                        value={formData.province}
                        onChange={(e) => setFormData(prev => ({ ...prev, province: e.target.value }))}
                        placeholder="Ilocos Norte"
                        className={cn(errors.province && "border-red-500")}
                      />
                      {errors.province && (
                        <p className="text-xs text-red-600">{errors.province}</p>
                      )}
                    </div>
                  </div>

                  {/* GPS Coordinates */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="branch_latitude">
                        Latitude (Optional)
                      </Label>
                      <Input
                        id="branch_latitude"
                        type="number"
                        step="any"
                        value={formData.latitude || ""}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          latitude: e.target.value ? parseFloat(e.target.value) : undefined 
                        }))}
                        placeholder="18.1969"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="branch_longitude">
                        Longitude (Optional)
                      </Label>
                      <Input
                        id="branch_longitude"
                        type="number"
                        step="any"
                        value={formData.longitude || ""}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          longitude: e.target.value ? parseFloat(e.target.value) : undefined 
                        }))}
                        placeholder="120.5937"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Label>Contact Numbers *</Label>
                    {formData.contact_numbers.map((number, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Input
                          value={number}
                          onChange={(e) => updateContactNumber(index, e.target.value)}
                          placeholder="+63 9XX XXX XXXX"
                          className={cn(errors.contact_numbers && "border-red-500")}
                        />
                        {formData.contact_numbers.length > 1 && (
                          <Button
                            type="button"
                            variant="secondary"
                            size="sm"
                            onClick={() => removeContactNumber(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="secondary"
                      size="sm"
                      onClick={addContactNumber}
                      className="w-fit"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Phone Number
                    </Button>
                    {errors.contact_numbers && (
                      <p className="text-xs text-red-600">{errors.contact_numbers}</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Business Hours */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Business Hours
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <BusinessHoursForm
                    businessHours={formData.business_hours}
                    onChange={(hours) => setFormData(prev => ({ ...prev, business_hours: hours }))}
                  />
                </CardContent>
              </Card>

              {/* Service Options */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Service Options</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Pickup Available</Label>
                      <p className="text-xs text-gray-500">Allow customers to pick up vehicles from this branch</p>
                    </div>
                    <Switch
                      checked={formData.pickup_available}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, pickup_available: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Drop-off Available</Label>
                      <p className="text-xs text-gray-500">Allow customers to return vehicles to this branch</p>
                    </div>
                    <Switch
                      checked={formData.dropoff_available}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, dropoff_available: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Active Branch</Label>
                      <p className="text-xs text-gray-500">Include this branch in customer-facing interfaces</p>
                    </div>
                    <Switch
                      checked={formData.is_active}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </ScrollArea>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="secondary"
              onClick={onClose}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={saving}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {branch ? "Update Branch" : "Create Branch"}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

function BusinessHoursForm({ businessHours, onChange }: BusinessHoursFormProps) {
  const days = [
    { key: 'monday', label: 'Monday' },
    { key: 'tuesday', label: 'Tuesday' },
    { key: 'wednesday', label: 'Wednesday' },
    { key: 'thursday', label: 'Thursday' },
    { key: 'friday', label: 'Friday' },
    { key: 'saturday', label: 'Saturday' },
    { key: 'sunday', label: 'Sunday' }
  ] as const

  const updateDayHours = (day: keyof BusinessHours, hours: DayHours) => {
    onChange({
      ...businessHours,
      [day]: hours
    })
  }

  return (
    <div className="space-y-3">
      {days.map(({ key, label }) => (
        <div key={key} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
          <div className="w-20 text-sm font-medium">{label}</div>
          
          <div className="flex items-center gap-2">
            <Switch
              checked={businessHours[key].is_open}
              onCheckedChange={(checked) => updateDayHours(key, {
                ...businessHours[key],
                is_open: checked
              })}
            />
            <span className="text-xs text-gray-500">Open</span>
          </div>

          {businessHours[key].is_open && (
            <>
              <div className="flex items-center gap-2">
                <Label className="text-xs">From:</Label>
                <Input
                  type="time"
                  value={businessHours[key].open_time || ""}
                  onChange={(e) => updateDayHours(key, {
                    ...businessHours[key],
                    open_time: e.target.value
                  })}
                  className="w-24 h-8 text-xs"
                />
              </div>

              <div className="flex items-center gap-2">
                <Label className="text-xs">To:</Label>
                <Input
                  type="time"
                  value={businessHours[key].close_time || ""}
                  onChange={(e) => updateDayHours(key, {
                    ...businessHours[key],
                    close_time: e.target.value
                  })}
                  className="w-24 h-8 text-xs"
                />
              </div>
            </>
          )}

          {!businessHours[key].is_open && (
            <div className="text-xs text-gray-500 italic ml-4">Closed</div>
          )}
        </div>
      ))}
    </div>
  )
}
