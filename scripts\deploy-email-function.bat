@echo off
:: <PERSON><PERSON><PERSON> to deploy the send-email Edge Function to Supabase

echo Starting deployment of send-email Edge Function...

:: Navigate to the project root (assuming this script is run from the scripts directory)
cd /d "%~dp0\.."

:: Check if Supabase CLI is installed
where supabase >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Supabase CLI is not installed.
    echo Please install it using: npm install -g supabase
    exit /b 1
)

:: Deploy the function without JWT verification
echo Deploying send-email function...
supabase functions deploy send-email --no-verify-jwt

if %ERRORLEVEL% equ 0 (
    echo.
    echo ✅ send-email function deployed successfully!
    
    echo.
    echo IMPORTANT: Don't forget to:
    echo 1. Configure SMTP settings in Supabase dashboard
    echo 2. Deploy the SQL function 'send_email_smtp' using the SQL editor
    echo 3. Test by finalizing a booking in the admin dashboard
    echo.
    echo For detailed setup instructions, see docs/SUPABASE_EMAIL_SETUP.md
) else (
    echo ❌ Deployment failed. Please check the error message above.
)
