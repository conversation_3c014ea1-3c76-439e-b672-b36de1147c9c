import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { Suspense } from 'react'
import ClientFeedbackPage from './client-page'

export default async function EmailConfirmPage({ 
  searchParams 
}: { 
  searchParams: { [key: string]: string | string[] | undefined } 
}) {
  const token_hash = typeof searchParams.token_hash === 'string' ? searchParams.token_hash : null
  const type = typeof searchParams.type === 'string' ? searchParams.type : null
  const next = typeof searchParams.next === 'string' ? searchParams.next : '/'

  if (token_hash && type) {
    const supabase = await createClient()

    const { error } = await supabase.auth.verifyOtp({
      type: type as any,
      token_hash,
    })

    if (!error) {
      redirect(next)
    }
  }

  // if verification fails, show the error page
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ClientFeedbackPage />
    </Suspense>
  )
}
