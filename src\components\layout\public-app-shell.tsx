"use client";

import * as React from "react";
import { SidebarNav } from "../sidebar-nav";
import { Topbar } from "../topbar";
import { BreadcrumbNav } from "../nav/breadcrumb-nav";
import { SidebarProvider, useSidebar } from "../nav/sidebar-context";
import { Menu, ChevronsLeft, ChevronsRight, Car } from "lucide-react";
import { Button } from "../ui/button";
import { AuthButton } from "../auth/auth-button";
import { ResponsiveNav } from "../customer-side/nav";
import { CustomerChatbot } from "../customer-side/chatbot/customer-chatbot";
import Link from "next/link";
import Image from "next/image";

function PublicShellInner({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div
      className={`min-h-screen bg-white transition-all duration-300 overflow-x-hidden ${
        mounted ? "opacity-100" : "opacity-0"
      }`}
    >
      {/* Public Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-lg border-b border-gray-200/50 transition-all duration-300 overflow-x-hidden shadow-sm">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="flex items-center justify-between h-16 min-w-0">
            {/* Logo */}
            <Link
              href="/"
              className="flex items-center gap-2 hover:opacity-80 transition-opacity duration-200 min-w-0 flex-shrink-0"
            >
              <div className="relative h-8 w-8 flex-shrink-0">
                <Image
                  src="/ollie_logo.svg"
                  alt="Ollie's Rent A Car Logo"
                  width={32}
                  height={32}
                  className="object-contain"
                />
              </div>
              <div className="flex flex-col min-w-0 max-w-[200px] sm:max-w-none">
                <span className="text-lg font-bold text-gray-900 transition-colors duration-200 truncate">
                  Ollie's Rent A Car
                </span>
                <span className="text-xs text-gray-500 -mt-1 transition-colors duration-200 truncate">
                  Powered by Pathlink
                </span>
              </div>
            </Link>

            {/* Navigation - Responsive System */}
            <div className="flex-1 flex justify-center overflow-visible min-w-0">
              <ResponsiveNav className="hidden sm:flex" />
            </div>

            {/* CTA Buttons */}
            <div className="flex items-center gap-3 flex-shrink-0">
              {/* Authentication Buttons - Desktop only, mobile handled by navigation */}
              <div className="hidden sm:flex">
                <AuthButton />
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main
        className={`flex-1 transition-all duration-300 overflow-x-hidden pt-16 ${
          mounted ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"
        }`}
      >
        {children}
      </main>

      {/* Mobile Bottom Navigation */}
      <div className="sm:hidden">
        <ResponsiveNav />
      </div>

      {/* Customer Chatbot - Only on customer pages */}
      <CustomerChatbot />
    </div>
  );
}

function ShellInner({
  children,
  right,
}: {
  children: React.ReactNode;
  right?: React.ReactNode;
}) {
  const { isCollapsed, toggle } = useSidebar();
  const hasRightPanel = Boolean(right);

  return (
    <div className="min-h-screen bg-muted/20">
      <div
        className="flex"
        data-collapsed={isCollapsed}
        style={
          {
            // @ts-ignore - CSS var used inline for smoother coordinated animations
            "--sidebar-w": isCollapsed ? "4rem" : "16rem",
          } as React.CSSProperties
        }
      >
        <aside
          className={
            "hidden md:block shrink-0 border-r bg-white transition-[width] duration-200 ease-in-out overflow-hidden"
          }
          style={{ width: "var(--sidebar-w)" } as React.CSSProperties}
        >
          <SidebarNav />
        </aside>
        <main
          className="flex-1 flex flex-col min-w-0"
          style={
            {
              width: "calc(100% - var(--sidebar-w))",
              transition: "width 200ms ease",
            } as React.CSSProperties
          }
        >
          <Topbar />
          <div className="px-4 md:px-6 py-4">
            <div className="flex items-center justify-between mb-4">
              <BreadcrumbNav />
              <button
                className="hidden md:inline-flex items-center gap-2 rounded-md border px-2.5 py-1.5 text-sm bg-white hover:bg-muted transition-colors"
                onClick={toggle}
                aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
              >
                {isCollapsed ? (
                  <ChevronsRight className="h-4 w-4" />
                ) : (
                  <ChevronsLeft className="h-4 w-4" />
                )}
                <span className="hidden lg:inline">
                  {isCollapsed ? "Expand" : "Collapse"}
                </span>
              </button>
            </div>
            {hasRightPanel ? (
              <div className="grid grid-cols-1 xl:grid-cols-12 gap-4">
                <div className="xl:col-span-9 space-y-4">{children}</div>
                <div className="xl:col-span-3 space-y-4">{right}</div>
              </div>
            ) : (
              <div className="w-full space-y-4">{children}</div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}

// Public shell for customer-facing pages (homepage, catalog, etc.)
export function PublicAppShell({ children }: { children: React.ReactNode }) {
  return <PublicShellInner>{children}</PublicShellInner>;
}

// Admin shell for dashboard and admin pages
export function AppShell({
  children,
  right,
}: {
  children: React.ReactNode;
  right?: React.ReactNode;
}) {
  return (
    <SidebarProvider>
      <ShellInner right={right}>{children}</ShellInner>
    </SidebarProvider>
  );
}
