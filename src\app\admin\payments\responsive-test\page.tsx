"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { PaymentCard } from "@/components/admin/payment-card"

// Sample payment data for testing
const samplePayments = [
  {
    id: "PAY123456",
    bookingId: "BK789012",
    renterName: "<PERSON> Doe",
    renterEmail: "<EMAIL>",
    renterPhone: "+63 ************",
    amount: 2500,
    method: "GCash",
    status: "Completed",
    transactionDate: "2025-08-20T14:30:00Z",
    proofOfPaymentUrl: "https://example.com/receipt/123"
  },
  {
    id: "PAY123457",
    bookingId: "BK789013",
    renterName: "<PERSON>",
    renterEmail: "<EMAIL>",
    renterPhone: "+63 ************",
    amount: 3750,
    method: "Bank Transfer",
    status: "Completed",
    transactionDate: "2025-08-19T10:15:00Z",
    proofOfPaymentUrl: undefined
  },
  {
    id: "PAY123458",
    bookingId: "BK789014",
    renterName: "<PERSON> with a Very Long Name That Might Overflow",
    renterEmail: "<EMAIL>",
    renterPhone: "+63 ************",
    amount: 1800,
    method: "Remittance Center",
    status: "Completed",
    transactionDate: "2025-08-18T16:45:00Z",
    proofOfPaymentUrl: "https://example.com/receipt/125"
  }
]

export default function ResponsiveTestPage() {
  const [windowWidth, setWindowWidth] = React.useState(0)
  
  React.useEffect(() => {
    // Set initial window width
    setWindowWidth(window.innerWidth)
    
    // Update window width on resize
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])
  
  // Determine current breakpoint
  const getBreakpointName = (width: number) => {
    if (width < 320) return "< Mobile S"
    if (width < 375) return "Mobile S (320px)"
    if (width < 425) return "Mobile M (375px)"
    if (width < 768) return "Mobile L (425px)"
    if (width < 1024) return "Tablet (768px)"
    if (width < 1280) return "Laptop (1024px)"
    if (width < 1440) return "Desktop (1280px)"
    return "Large Desktop (1440px+)"
  }
  
  // Get background color based on breakpoint
  const getBreakpointColor = (width: number) => {
    if (width < 768) return "bg-blue-100" // Mobile
    if (width < 1024) return "bg-purple-100" // Tablet
    return "bg-green-100" // Desktop
  }

  return (
    <div className="space-y-6 p-4">
      <header className="space-y-2">
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Payments Page Responsive Test</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Test the responsive behavior of the Payments page across different screen sizes.
        </p>
      </header>
      
      {/* Viewport Size Indicator */}
      <Card className={`${getBreakpointColor(windowWidth)} border-2 border-dashed`}>
        <CardContent className="p-4">
          <div className="text-center">
            <p className="text-lg font-bold">Current Viewport: {windowWidth}px</p>
            <p className="text-sm font-medium">{getBreakpointName(windowWidth)}</p>
            <p className="text-xs mt-2">
              {windowWidth < 1024 ? "Mobile/Tablet View (Card Layout)" : "Desktop View (Table Layout)"}
            </p>
          </div>
        </CardContent>
      </Card>
      
      {/* Breakpoint Guide */}
      <Card>
        <CardHeader>
          <CardTitle>Breakpoint Guide</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-3 bg-blue-100 rounded-md">
              <p className="font-medium">Mobile Breakpoints (Cards)</p>
              <ul className="text-sm space-y-1 mt-1">
                <li>Mobile S: 320px</li>
                <li>Mobile M: 375px</li>
                <li>Mobile L: 425px</li>
              </ul>
            </div>
            <div className="p-3 bg-purple-100 rounded-md">
              <p className="font-medium">Tablet Breakpoint (Cards)</p>
              <ul className="text-sm space-y-1 mt-1">
                <li>Tablet: 768px</li>
              </ul>
            </div>
            <div className="p-3 bg-green-100 rounded-md">
              <p className="font-medium">Desktop Breakpoints (Table)</p>
              <ul className="text-sm space-y-1 mt-1">
                <li>Laptop: 1024px</li>
                <li>Desktop: 1280px</li>
                <li>Large Desktop: 1440px+</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Test Cards Section */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Cards Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {samplePayments.map((payment) => (
              <PaymentCard key={payment.id} payment={payment} />
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Testing Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal list-inside space-y-2">
            <li>Resize your browser window to test different breakpoints</li>
            <li>Verify that cards display correctly on mobile and tablet (below 1024px)</li>
            <li>Check that all text is legible (≥14px body, ≥16px headings)</li>
            <li>Confirm touch targets are at least 44px in height</li>
            <li>Verify that there is no overflow or misalignment at 320px width</li>
            <li>Test that payment method badges display correctly</li>
            <li><strong>Click "View Details" button</strong> to test the modal functionality</li>
            <li>Verify the modal displays all payment details correctly</li>
            <li>Test modal close button and outside click dismissal</li>
            <li>Check that the modal is properly sized and centered on all devices</li>
          </ol>
        </CardContent>
      </Card>
    </div>
  )
}
