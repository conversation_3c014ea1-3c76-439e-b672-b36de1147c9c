"use client"

import * as React from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  User, 
  LogIn, 
  UserPlus, 
  Settings, 
  LogOut,
  Shield,
  ChevronDown,
  LayoutDashboard 
} from "lucide-react"
import { useCustomerAuth, CustomerAuthContext } from "./customer-auth-context"

interface AuthButtonProps {
  variant?: "default" | "minimal" | "compact"
  className?: string
}

export function AuthButton({ variant = "default", className }: AuthButtonProps) {
  // Check if we're within a CustomerAuthProvider
  const context = React.useContext(CustomerAuthContext)
  
  if (!context) {
    // If used outside CustomerAuthProvider, show unauthenticated state
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Button asChild variant="secondary" size="sm">
          <Link href="/customer/login">
            <LogIn className="h-4 w-4 mr-2" />
            Sign In
          </Link>
        </Button>
        <Button asChild size="sm">
          <Link href="/customer/signup">
            <UserPlus className="h-4 w-4 mr-2" />
            Sign Up
          </Link>
        </Button>
      </div>
    )
  }

  const { user, signOut } = useCustomerAuth()
  const router = useRouter()

  const handleSignOut = async () => {
    try {
      await signOut()
      
      // Clear any local storage data
      if (typeof window !== 'undefined') {
        localStorage.removeItem('ollie_user')
        localStorage.removeItem('supabase.auth.token')
        // Clear any other auth-related local storage items
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('sb-') || key.includes('auth') || key.includes('session')) {
            localStorage.removeItem(key)
          }
        })
      }
      
      // Redirect to home page
      router.push("/")
      
      // Optional: Force a page reload to ensure all state is cleared
      setTimeout(() => {
        window.location.reload()
      }, 100)
    } catch (error) {
      console.error('Error during logout:', error)
      // Even if there's an error, redirect to home
      router.push("/")
    }
  }

  const handleAdminRedirect = () => {
    router.push("/admin")
  }

  const handleDashboardRedirect = () => {
    if (user?.role === 'admin') {
      router.push("/admin")
    } else {
      router.push("/customer/dashboard")
    }
  }

  // If user is authenticated, show user dropdown
  if (user) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="tertiary"
            className="h-10 px-3 gap-2 text-gray-700 hover:text-blue-700 hover:bg-blue-50 focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
            aria-label="User account menu"
          >
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span className="hidden sm:inline font-medium">{user.email}</span>
              <ChevronDown className="h-4 w-4" />
            </div>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56 p-2 mt-2 bg-white border border-gray-200 shadow-lg rounded-lg" sideOffset={8}>
          <div className="px-3 py-2 text-sm border-b border-gray-100">
            <div className="font-medium text-gray-900">{user.email}</div>
            <div className="text-xs text-gray-500">{user.email}</div>
            <div className="text-xs text-blue-600 font-medium mt-1">{user.role || 'Customer'}</div>
          </div>
          
          {/* Dashboard */}
          <DropdownMenuItem onClick={handleDashboardRedirect} className="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md cursor-pointer">
            <LayoutDashboard className="h-4 w-4 mr-3" />
            <span>Dashboard</span>
          </DropdownMenuItem>

          {/* Admin Panel (only for admin users) */}
          {user.role === "admin" && (
            <DropdownMenuItem onClick={handleAdminRedirect} className="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md cursor-pointer">
              <Shield className="h-4 w-4 mr-3" />
              <span>Admin Panel</span>
            </DropdownMenuItem>
          )}

          {/* Settings */}
          <DropdownMenuItem asChild>
            <Link href="/customer/settings" className="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md cursor-pointer">
              <Settings className="h-4 w-4 mr-3" />
              <span>Settings</span>
            </Link>
          </DropdownMenuItem>

          <DropdownMenuSeparator className="my-1 bg-gray-100" />
          
          {/* Sign Out */}
          <DropdownMenuItem onClick={handleSignOut} className="flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md cursor-pointer">
            <LogOut className="h-4 w-4 mr-3" />
            <span>Sign out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  // If user is not authenticated, show sign in/sign up buttons
  if (variant === "compact") {
    return (
      <Button asChild variant="primary" size="sm">
        <Link href="/customer/login">
          <span>Login</span>
        </Link>
      </Button>
    )
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Sign In Button */}
      <Button
        asChild
        variant="tertiary"
        size="sm"
        className="text-gray-700 hover:text-blue-700 hover:bg-blue-50 focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 transition-all duration-200"
      >
        <Link href="/customer/login" className="flex items-center gap-2">
          <LogIn className="h-4 w-4" />
          <span className="font-medium">Sign In</span>
        </Link>
      </Button>

      {/* Sign Up Button */}
      <Button
        asChild
        variant="primary"
        size="sm"
        className="bg-blue-600 hover:bg-blue-700 focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 shadow-sm hover:shadow-md transition-all duration-200"
      >
        <Link href="/customer/signup" className="flex items-center gap-2">
          <UserPlus className="h-4 w-4" />
          <span className="font-semibold">Get Started</span>
        </Link>
      </Button>
    </div>
  )
}