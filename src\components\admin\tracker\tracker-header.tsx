import { formatDistanceToNow } from 'date-fns'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Wifi, WifiOff, RotateCcw } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TrackerHeaderProps {
  lastUpdated: Date
  isAutoRefresh: boolean
  onAutoRefreshToggle: (enabled: boolean) => void
  connectionStatus: 'live' | 'reconnecting' | 'offline'
}

export function TrackerHeader({
  lastUpdated,
  isAutoRefresh,
  onAutoRefreshToggle,
  connectionStatus
}: TrackerHeaderProps) {
  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'live':
        return <Wifi className="w-4 h-4 text-green-600" />
      case 'reconnecting':
        return <RotateCcw className="w-4 h-4 text-yellow-600 animate-spin" />
      case 'offline':
        return <WifiOff className="w-4 h-4 text-red-600" />
    }
  }

  const getConnectionBadge = () => {
    const variants = {
      live: 'bg-green-100 text-green-800 border-green-200',
      reconnecting: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      offline: 'bg-red-100 text-red-800 border-red-200'
    }
    
    return (
      <Badge 
        variant="outline" 
        className={cn("flex items-center gap-1", variants[connectionStatus])}
      >
        {getConnectionIcon()}
        {connectionStatus.charAt(0).toUpperCase() + connectionStatus.slice(1)}
      </Badge>
    )
  }

  return (
    <header className="border-b bg-background px-6 py-4">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Tracker</h1>
          <p className="text-sm text-muted-foreground">
            Live GPS vehicle tracking and monitoring
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Last updated {formatDistanceToNow(lastUpdated, { addSuffix: true })}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Auto-refresh</span>
            <Switch
              checked={isAutoRefresh}
              onCheckedChange={onAutoRefreshToggle}
              aria-label="Toggle auto-refresh"
            />
          </div>
          
          {getConnectionBadge()}
        </div>
      </div>
    </header>
  )
}
