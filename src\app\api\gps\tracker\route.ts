import { NextRequest, NextResponse } from 'next/server'

// GPS Data Encryption/Decryption (same key as ESP32)
const ENCRYPT_KEY = "OllieGPS2024";

// In-memory cache for the latest GPS token
let latestTokenCache: {
  token: string;
  timestamp: Date;
  gpsData: { lat: number; lon: number; acc: number; deviceId: string };
} | null = null;

// Helper to encrypt GPS data (for regenerating tokens)
function encryptGPSData(data: string): string {
  let encrypted = "";
  const hexDigits = "0123456789abcdef";
  
  for (let i = 0; i < data.length; i++) {
    const charCode = data.charCodeAt(i);
    const keyCharCode = ENCRYPT_KEY.charCodeAt(i % ENCRYPT_KEY.length);
    const encryptedByte = charCode ^ keyCharCode;
    encrypted += hexDigits[(encryptedByte >> 4) & 0x0F];
    encrypted += hexDigits[encryptedByte & 0x0F];
  }
  
  return encrypted;
}

// Helper to create GPS token from coordinates
function createGPSToken(lat: number, lon: number, acc: number, deviceId: string): string {
  const payload = `${lat.toFixed(6)},${lon.toFixed(6)},${acc.toFixed(1)},${deviceId}`;
  return encryptGPSData(payload);
}

// Helper to update token cache
function updateTokenCache(gpsData: { lat: number; lon: number; acc: number; deviceId: string }, originalToken?: string) {
  const token = originalToken || createGPSToken(gpsData.lat, gpsData.lon, gpsData.acc, gpsData.deviceId);
  latestTokenCache = {
    token,
    timestamp: new Date(),
    gpsData
  };
  
  if (process.env.NODE_ENV === 'development') {
    console.log('🔄 Updated token cache:', {
      tokenPreview: token.substring(0, 20) + '...',
      deviceId: gpsData.deviceId,
      timestamp: latestTokenCache.timestamp.toISOString()
    });
  }
}

function decryptGPSToken(encryptedToken: string): { lat: number; lon: number; acc: number; deviceId: string } {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 Decrypting GPS token:', {
        tokenLength: encryptedToken.length,
        tokenSample: encryptedToken.substring(0, 20) + '...'
      });
    }
    
    // Ensure the token is in the correct format
    if (!/^[0-9a-f]+$/i.test(encryptedToken)) {
      console.error('🔐 Invalid token format (not hex):', encryptedToken);
      throw new Error('Invalid token format (not hex)');
    }
    
    const hexPairs = encryptedToken.match(/.{1,2}/g) || [];
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 Hex pairs count:', hexPairs.length);
      console.log('🔐 First few hex pairs:', hexPairs.slice(0, 10));
    }
    
    let decrypted = "";
    
    for (let i = 0; i < hexPairs.length; i++) {
      const hexValue = hexPairs[i];
      const hexChar = parseInt(hexValue, 16);
      const keyChar = ENCRYPT_KEY.charCodeAt(i % ENCRYPT_KEY.length);
      const decryptedChar = String.fromCharCode(hexChar ^ keyChar);
      
      // Debug first few characters
      if (i < 10) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`🔐 [${i}] hex: ${hexValue} (${hexChar}) ^ key: ${String.fromCharCode(keyChar)} (${keyChar}) = ${decryptedChar} (${hexChar ^ keyChar})`);
        }
      }
      
      decrypted += decryptedChar;
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 Decrypted data:', decrypted);
      console.log('🔐 Decrypted data (escaped):', JSON.stringify(decrypted));
    }
    
    // Parse decrypted data: "lat,lon,acc,deviceId"
    const parts = decrypted.split(',');
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 Parsed parts:', parts);
      console.log('🔐 Parts count:', parts.length);
    }
    
    if (parts.length >= 4) {
      const lat = parseFloat(parts[0]);
      const lon = parseFloat(parts[1]);
      const acc = parseFloat(parts[2]);
      const deviceId = parts[3];
      
      if (process.env.NODE_ENV === 'development') {
        console.log('🔐 Parsed values:', { lat, lon, acc, deviceId });
      }
      
      if (!isNaN(lat) && !isNaN(lon)) {
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ Successfully decrypted GPS data:', { lat, lon, acc, deviceId });
        }
        return { lat, lon, acc, deviceId };
      }
    }
    
    throw new Error('Invalid decrypted data format');
  } catch (error) {
    console.error('Decryption failed:', error);
    throw new Error('Invalid GPS token');
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams, pathname } = new URL(request.url);
    
    if (process.env.NODE_ENV === 'development') {
      console.log('📥 Received GPS tracker request:', {
        url: request.url,
        pathname,
        searchParams: Object.fromEntries(searchParams.entries())
      });
    }
    
    // Check for path parameter first (device is sending as /api/gps/tracker/?gps=XYZ)
    // And also check for query parameter (admin UI might be using ?gps=XYZ)
    const gpsToken = searchParams.get('gps');
    
    if (gpsToken) {
      try {
        if (process.env.NODE_ENV === 'development') {
          console.log('🔑 Found GPS token:', gpsToken.substring(0, 20) + '...');
        }
        
        // Decrypt the GPS token
        const gpsData = decryptGPSToken(gpsToken);
        
        // Update in-memory cache with the latest token
        updateTokenCache(gpsData, gpsToken);
        
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ Received encrypted GPS data from ESP32:', {
            latitude: gpsData.lat,
            longitude: gpsData.lon,
            accuracy: gpsData.acc,
            deviceId: gpsData.deviceId,
            timestamp: new Date().toISOString()
          });
        }
        
        // Store GPS data in database using the ingest API
        try {
          const ingestUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/gps/ingest/`;
          const deviceToken = process.env.INBOUND_DEVICE_TOKEN || '';
          
          if (process.env.NODE_ENV === 'development') {
            console.log('🔧 Debug info:', {
              ingestUrl,
              hasDeviceToken: !!process.env.INBOUND_DEVICE_TOKEN,
              deviceTokenLength: deviceToken.length
            });
          }
          
          const response = await fetch(ingestUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-Device-Key': deviceToken
            },
            body: JSON.stringify({
              carId: gpsData.deviceId,
              latitude: gpsData.lat,
              longitude: gpsData.lon,
              speed: 0, // ESP32 doesn't send speed in current implementation
              heading: 0, // ESP32 doesn't send heading in current implementation
              status: 'active'
            })
          });
          
          if (response.ok) {
            if (process.env.NODE_ENV === 'development') {
              console.log('✅ GPS data stored in database successfully');
              const result = await response.json();
              console.log('📈 Ingest API response:', result);
            }
          } else {
            const errorText = await response.text();
            if (process.env.NODE_ENV === 'development') {
              console.warn('⚠️ Failed to store GPS data in database:', {
                status: response.status,
                statusText: response.statusText,
                error: errorText
              });
            }
          }
        } catch (error) {
          console.error('❌ Error storing GPS data:', error);
        }
        
        // Return success response to ESP32
        return NextResponse.json({
          success: true,
          message: 'GPS data received and decrypted successfully',
          timestamp: new Date().toISOString(),
          deviceId: gpsData.deviceId
        });
        
      } catch (error) {
        console.error('❌ Failed to decrypt GPS token:', error);
        return NextResponse.json({
          success: false,
          error: 'Invalid GPS token'
        }, { status: 400 });
      }
    }
    
    // Fallback: Check for legacy unencrypted parameters
    const lat = searchParams.get('lat');
    const lon = searchParams.get('lon');
    const acc = searchParams.get('acc');
    const carId = searchParams.get('carId');
    
    if (lat && lon) {
      const latitude = parseFloat(lat);
      const longitude = parseFloat(lon);
      const accuracy = acc ? parseFloat(acc) : 0;
      
      if (!isNaN(latitude) && !isNaN(longitude)) {
        // Update cache for unencrypted data too
        const gpsData = { lat: latitude, lon: longitude, acc: accuracy, deviceId: carId || 'lilygo-esp32-01' };
        updateTokenCache(gpsData);
        
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ Received unencrypted GPS data from ESP32:', {
            latitude,
            longitude,
            accuracy,
            carId: carId || 'unknown',
            timestamp: new Date().toISOString()
          });
        }
        
        // Store GPS data in database using the ingest API
        try {
          const ingestUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/gps/ingest/`;
          const deviceToken = process.env.INBOUND_DEVICE_TOKEN || '';
          
          const response = await fetch(ingestUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-Device-Key': deviceToken
            },
            body: JSON.stringify({
              carId: carId || 'lilygo-esp32-01',
              latitude,
              longitude,
              speed: 0, // ESP32 doesn't send speed in current implementation
              heading: 0, // ESP32 doesn't send heading in current implementation
              status: 'active'
            })
          });
          
          if (response.ok) {
            if (process.env.NODE_ENV === 'development') {
              console.log('✅ Unencrypted GPS data stored in database successfully');
            }
          } else {
            const errorText = await response.text();
            if (process.env.NODE_ENV === 'development') {
              console.warn('⚠️ Failed to store unencrypted GPS data:', {
                status: response.status,
                statusText: response.statusText,
                error: errorText
              });
            }
          }
        } catch (error) {
          console.error('❌ Error storing unencrypted GPS data:', error);
        }
        
        return NextResponse.json({
          success: true,
          message: 'GPS data received successfully (unencrypted)',
          timestamp: new Date().toISOString(),
          carId: carId || 'lilygo-esp32-01'
        });
      }
    }
    
    // If no valid GPS data found, check for path segment
    const pathSegments = pathname.split('/').filter(Boolean);
    if (pathSegments.length >= 3) {
      const potentialGpsToken = pathSegments[pathSegments.length - 1];
      if (potentialGpsToken && potentialGpsToken.length > 20) {
        if (process.env.NODE_ENV === 'development') {
          console.log('🔍 Found potential GPS token in path:', potentialGpsToken);
        }
        try {
          const gpsData = decryptGPSToken(potentialGpsToken);
          
          // Same processing as above for token
          // Store in database, etc.
          if (process.env.NODE_ENV === 'development') {
            console.log('✅ Decrypted GPS data from path segment!');
          }
          
          return NextResponse.json({
            success: true,
            message: 'GPS data from path processed successfully',
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          console.error('❌ Failed to process path token:', error);
        }
      }
    }
    
    // If reached here, no valid data was found
    return NextResponse.json({
      success: false,
      error: 'No valid GPS data provided',
      info: 'Expected format: /api/gps/tracker/?gps=<encrypted_token> or ?lat=<lat>&lon=<lon>'
    });
    
  } catch (error) {
    console.error('❌ Error processing GPS data:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: String(error)
    }, { status: 500 });
  }
}

// Handle POST requests for bulk GPS data or other operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Handle bulk GPS data updates or other POST operations
    if (process.env.NODE_ENV === 'development') {
      console.log('📡 Received POST request to tracker endpoint:', body);
    }
    
    return NextResponse.json({
      success: true,
      message: 'POST request processed successfully',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error processing POST request:', error);
    return NextResponse.json({
      success: false,
      error: 'Invalid request body'
    }, { status: 400 });
  }
}
