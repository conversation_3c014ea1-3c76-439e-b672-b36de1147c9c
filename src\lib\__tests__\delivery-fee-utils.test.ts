import { calculateDeliveryFees } from "../delivery-fee-utils";
import { getTimePeriod } from "../delivery-fee-constants";

describe("Delivery Fee Utils", () => {
  describe("calculateDeliveryFees", () => {
    it("should calculate correct fees for day time pickup and return", () => {
      const result = calculateDeliveryFees(
        "Laoag Centro",
        "SM / Robinsons", 
        "day",
        "day"
      );

      expect(result.pickupFee).toBe(250);
      expect(result.returnFee).toBe(300);
      expect(result.totalFee).toBe(550);
      expect(result.pickupTimePeriod).toBe("day");
      expect(result.returnTimePeriod).toBe("day");
      expect(result.isPickupFree).toBe(false);
      expect(result.isReturnFree).toBe(false);
    });

    it("should calculate correct fees for night time pickup and return", () => {
      const result = calculateDeliveryFees(
        "Laoag Centro",
        "SM / Robinsons",
        "night",
        "night"
      );

      expect(result.pickupFee).toBe(350);
      expect(result.returnFee).toBe(400);
      expect(result.totalFee).toBe(750);
      expect(result.pickupTimePeriod).toBe("night");
      expect(result.returnTimePeriod).toBe("night");
    });

    it("should return free fees for garage/office location", () => {
      const result = calculateDeliveryFees(
        "#9 Lubnac, Vintar, Ilocos Norte",
        "#9 Lubnac, Vintar, Ilocos Norte",
        "day",
        "day"
      );

      expect(result.pickupFee).toBe(0);
      expect(result.returnFee).toBe(0);
      expect(result.totalFee).toBe(0);
      expect(result.isPickupFree).toBe(true);
      expect(result.isReturnFree).toBe(true);
    });

    it("should handle mixed day/night scenarios", () => {
      const result = calculateDeliveryFees(
        "Laoag Centro",
        "Sarrat Centro",
        "day",
        "night"
      );

      expect(result.pickupFee).toBe(250);
      expect(result.returnFee).toBe(350);
      expect(result.totalFee).toBe(600);
    });

    it("should handle locations with same day/night fees", () => {
      const result = calculateDeliveryFees(
        "Sinait / Cabugao / Badoc / Bangui",
        "Sinait / Cabugao / Badoc / Bangui",
        "day",
        "night"
      );

      expect(result.pickupFee).toBe(1200);
      expect(result.returnFee).toBe(1200);
      expect(result.totalFee).toBe(2400);
    });

    it("should return zero fees for unrecognized locations", () => {
      const result = calculateDeliveryFees(
        "Unknown Location",
        "Another Unknown Location",
        "day",
        "day"
      );

      expect(result.pickupFee).toBe(0);
      expect(result.returnFee).toBe(0);
      expect(result.totalFee).toBe(0);
    });
  });

  describe("getTimePeriod", () => {
    it("should correctly identify day time periods", () => {
      expect(getTimePeriod("07:00")).toBe("day");
      expect(getTimePeriod("12:00")).toBe("day");
      expect(getTimePeriod("19:00")).toBe("day");
    });

    it("should correctly identify night time periods", () => {
      expect(getTimePeriod("19:01")).toBe("night");
      expect(getTimePeriod("23:30")).toBe("night");
      expect(getTimePeriod("02:00")).toBe("night");
      expect(getTimePeriod("06:59")).toBe("night");
    });

    it("should handle edge cases", () => {
      expect(getTimePeriod("07:00")).toBe("day");
      expect(getTimePeriod("19:00")).toBe("day");
      expect(getTimePeriod("19:01")).toBe("night");
      expect(getTimePeriod("06:59")).toBe("night");
    });
  });

});
