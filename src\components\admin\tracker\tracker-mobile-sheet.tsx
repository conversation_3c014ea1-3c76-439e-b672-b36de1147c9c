import { useState } from 'react'
import { 
  Sheet, 
  SheetContent, 
  SheetDes<PERSON>, 
  SheetHeader, 
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  List, 
  Search, 
  Filter,
  Activity, 
  Clock, 
  AlertTriangle, 
  User,
  MapPin,
  Navigation2,
  Zap,
  ChevronUp
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { cn } from '@/lib/utils'
import { GPSLocation } from '@/lib/gps-data'

interface TrackerFilters {
  searchTerm: string
  statusFilter: string[]
  categoryFilter: string
  branchFilter: string
  timeWindow: 'live' | '15m' | '1h' | '24h'
  sortBy: 'status' | 'lastSeen' | 'speed' | 'distance'
  showClusters: boolean
  showTrails: boolean
  showGeofences: boolean
  denseList: boolean
}

interface TrackerMobileSheetProps {
  vehicles: GPSLocation[]
  selectedVehicle: GPSLocation | undefined
  isOpen: boolean
  onClose: () => void
  onVehicleSelect: (carId: string) => void
  onVehicleFollow: (carId: string) => void
  followingCarId: string
  filters: TrackerFilters
  onFilterChange: (filters: Partial<TrackerFilters>) => void
}

export function TrackerMobileSheet({
  vehicles,
  selectedVehicle,
  isOpen,
  onClose,
  onVehicleSelect,
  onVehicleFollow,
  followingCarId,
  filters,
  onFilterChange
}: TrackerMobileSheetProps) {
  const [quickSearch, setQuickSearch] = useState('')

  const getStatusIcon = (status: GPSLocation['status']) => {
    switch (status) {
      case 'active':
        return <Activity className="w-4 h-4 text-green-600" />
      case 'idle':
        return <Clock className="w-4 h-4 text-yellow-600" />
      case 'offline':
        return <AlertTriangle className="w-4 h-4 text-red-600" />
    }
  }

  const getStatusBadge = (status: GPSLocation['status']) => {
    const variants = {
      active: 'bg-green-100 text-green-800 border-green-200',
      idle: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      offline: 'bg-red-100 text-red-800 border-red-200'
    }
    
    return (
      <Badge variant="outline" className={cn("text-xs", variants[status])}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  // Filter vehicles based on quick search
  const filteredVehicles = vehicles.filter(vehicle => {
    if (!quickSearch) return true
    
    return (
      vehicle.carPlate.toLowerCase().includes(quickSearch.toLowerCase()) ||
      vehicle.carModel.toLowerCase().includes(quickSearch.toLowerCase()) ||
      (vehicle.driverName?.toLowerCase().includes(quickSearch.toLowerCase()) ?? false)
    )
  })

  const handleStatusToggle = (status: string) => {
    const newStatusFilter = filters.statusFilter.includes(status)
      ? filters.statusFilter.filter(s => s !== status)
      : [...filters.statusFilter, status]
    onFilterChange({ statusFilter: newStatusFilter })
  }

  const statusOptions = [
    { value: 'active', label: 'Active', color: 'text-green-600' },
    { value: 'idle', label: 'Idle', color: 'text-yellow-600' },
    { value: 'offline', label: 'Offline', color: 'text-red-600' }
  ]

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent 
        side="bottom" 
        className="h-[80vh] rounded-t-xl border-t"
      >
        <SheetHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <div className="flex items-center gap-2">
            <List className="w-5 h-5 text-blue-600" />
            <SheetTitle>Vehicles ({filteredVehicles.length})</SheetTitle>
          </div>
          <Button
            variant="tertiary"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <ChevronUp className="w-4 h-4" />
          </Button>
        </SheetHeader>

        {selectedVehicle ? (
          <Tabs defaultValue="details" className="flex-1">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details" className="text-sm">
                Vehicle Details
              </TabsTrigger>
              <TabsTrigger value="list" className="text-sm">
                All Vehicles
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="mt-4 space-y-4">
              <div className="space-y-4">
                {/* Vehicle Header */}
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="font-semibold text-lg">{selectedVehicle.carPlate}</h3>
                    <p className="text-sm text-muted-foreground">{selectedVehicle.carModel}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(selectedVehicle.status)}
                    <Button
                      variant={followingCarId === selectedVehicle.carId ? "primary" : "secondary"}
                      size="sm"
                      onClick={() => onVehicleFollow(selectedVehicle.carId)}
                      className="h-8"
                    >
                      <Zap className="w-4 h-4 mr-1" />
                      {followingCarId === selectedVehicle.carId ? 'Following' : 'Follow'}
                    </Button>
                  </div>
                </div>

                {/* Details Grid */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-xs text-muted-foreground">Speed</div>
                    <div className="flex items-center gap-2">
                      <Navigation2 className="w-4 h-4 text-blue-600" />
                      <span className="font-medium">{selectedVehicle.speed} km/h</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="text-xs text-muted-foreground">Heading</div>
                    <div className="font-medium">{selectedVehicle.heading}°</div>
                  </div>

                  {selectedVehicle.driverName && (
                    <div className="space-y-2 col-span-2">
                      <div className="text-xs text-muted-foreground">Driver</div>
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-blue-600" />
                        <span className="font-medium">{selectedVehicle.driverName}</span>
                      </div>
                    </div>
                  )}

                  <div className="space-y-2 col-span-2">
                    <div className="text-xs text-muted-foreground">Last Updated</div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-blue-600" />
                      <div>
                        <div className="font-medium text-sm">
                          {selectedVehicle.timestamp.toLocaleTimeString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatDistanceToNow(selectedVehicle.timestamp, { addSuffix: true })}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="space-y-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    className="w-full justify-start"
                    onClick={onClose}
                  >
                    <MapPin className="w-4 h-4 mr-2" />
                    Center on map
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="list" className="mt-4">
              <VehicleList
                vehicles={filteredVehicles}
                selectedVehicle={selectedVehicle}
                followingCarId={followingCarId}
                quickSearch={quickSearch}
                filters={filters}
                onQuickSearchChange={setQuickSearch}
                onVehicleSelect={onVehicleSelect}
                onVehicleFollow={onVehicleFollow}
                onFilterChange={onFilterChange}
                statusOptions={statusOptions}
                handleStatusToggle={handleStatusToggle}
                getStatusIcon={getStatusIcon}
                getStatusBadge={getStatusBadge}
              />
            </TabsContent>
          </Tabs>
        ) : (
          <VehicleList
            vehicles={filteredVehicles}
            selectedVehicle={selectedVehicle}
            followingCarId={followingCarId}
            quickSearch={quickSearch}
            filters={filters}
            onQuickSearchChange={setQuickSearch}
            onVehicleSelect={onVehicleSelect}
            onVehicleFollow={onVehicleFollow}
            onFilterChange={onFilterChange}
            statusOptions={statusOptions}
            handleStatusToggle={handleStatusToggle}
            getStatusIcon={getStatusIcon}
            getStatusBadge={getStatusBadge}
          />
        )}
      </SheetContent>
    </Sheet>
  )
}

interface VehicleListProps {
  vehicles: GPSLocation[]
  selectedVehicle: GPSLocation | undefined
  followingCarId: string
  quickSearch: string
  filters: TrackerFilters
  onQuickSearchChange: (search: string) => void
  onVehicleSelect: (carId: string) => void
  onVehicleFollow: (carId: string) => void
  onFilterChange: (filters: Partial<TrackerFilters>) => void
  statusOptions: Array<{ value: string; label: string; color: string }>
  handleStatusToggle: (status: string) => void
  getStatusIcon: (status: GPSLocation['status']) => React.ReactElement
  getStatusBadge: (status: GPSLocation['status']) => React.ReactElement
}

function VehicleList({
  vehicles,
  selectedVehicle,
  followingCarId,
  quickSearch,
  filters,
  onQuickSearchChange,
  onVehicleSelect,
  onVehicleFollow,
  onFilterChange,
  statusOptions,
  handleStatusToggle,
  getStatusIcon,
  getStatusBadge
}: VehicleListProps) {
  return (
    <div className="space-y-4">
      {/* Search */}
      <div className="relative">
        <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search vehicles..."
          value={quickSearch}
          onChange={(e) => onQuickSearchChange(e.target.value)}
          className="pl-9"
        />
      </div>

      {/* Status Filters */}
      <div className="flex gap-2 overflow-x-auto pb-2">
        <Button
          variant={filters.statusFilter.length === 0 ? "primary" : "secondary"}
          size="sm"
          onClick={() => onFilterChange({ statusFilter: [] })}
          className="whitespace-nowrap"
        >
          All ({vehicles.length})
        </Button>
        {statusOptions.map(option => {
          const isSelected = filters.statusFilter.includes(option.value)
          const count = vehicles.filter(v => v.status === option.value).length
          return (
            <Button
              key={option.value}
              variant={isSelected ? "primary" : "secondary"}
              size="sm"
              onClick={() => handleStatusToggle(option.value)}
              className="whitespace-nowrap"
            >
              {option.label} ({count})
            </Button>
          )
        })}
      </div>

      {/* Vehicle List */}
      <ScrollArea className="h-96">
        <div className="space-y-3">
          {vehicles.map((vehicle) => (
            <div
              key={vehicle.id}
              className={cn(
                "p-4 rounded-lg border cursor-pointer transition-all duration-200",
                "hover:shadow-md hover:border-blue-200 active:scale-[0.98]",
                selectedVehicle?.carId === vehicle.carId && "bg-blue-50 border-blue-300 shadow-sm",
                followingCarId === vehicle.carId && "ring-2 ring-blue-200"
              )}
              onClick={() => onVehicleSelect(vehicle.carId)}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  {getStatusIcon(vehicle.status)}
                  <div>
                    <div className="font-semibold">{vehicle.carPlate}</div>
                    <div className="text-sm text-muted-foreground">{vehicle.carModel}</div>
                  </div>
                </div>
                {getStatusBadge(vehicle.status)}
              </div>

              <div className="grid grid-cols-2 gap-3 text-sm">
                {vehicle.status === 'active' && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Speed:</span>
                    <span className="font-medium">{vehicle.speed} km/h</span>
                  </div>
                )}

                {vehicle.driverName && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Driver:</span>
                    <span className="font-medium truncate">{vehicle.driverName}</span>
                  </div>
                )}

                <div className="flex justify-between col-span-2">
                  <span className="text-muted-foreground">Last seen:</span>
                  <span className="text-muted-foreground text-xs">
                    {formatDistanceToNow(vehicle.timestamp, { addSuffix: true })}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-2 mt-3">
                <Button
                  variant={followingCarId === vehicle.carId ? "primary" : "tertiary"}
                  size="xs"
                  onClick={(e) => {
                    e.stopPropagation()
                    onVehicleFollow(vehicle.carId)
                  }}
                  className="h-7 px-3 text-xs"
                >
                  <Zap className="w-3 h-3 mr-1" />
                  {followingCarId === vehicle.carId ? 'Following' : 'Follow'}
                </Button>
              </div>
            </div>
          ))}

          {vehicles.length === 0 && (
            <div className="text-center py-12 text-muted-foreground">
              <MapPin className="w-8 h-8 mx-auto mb-3 opacity-50" />
              <div>No vehicles found</div>
              <div className="text-sm">Try adjusting your search or filters</div>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
