"use server";

import { createContextClient } from "@/lib/supabase/server";
import { cookies } from "next/headers";
import { sendEmail, buildBookingSubmissionEmail } from "@/lib/email";
import { format } from "date-fns";

// Ensure errors are serializable and descriptive when returned to the client
function normalizeError(err: unknown) {
  if (!err) return { message: "Unknown error" };
  if (typeof err === "string") return { message: err };
  if (err instanceof Error) return { message: err.message, name: err.name };
  const anyErr = err as Record<string, unknown>;
  return {
    message: (anyErr.message as string) || "Unexpected error",
    code: anyErr.code as string | undefined,
    details: anyErr.details as string | undefined,
    hint: anyErr.hint as string | undefined,
    status: anyErr.status as number | undefined,
  };
}

export async function createBooking(bookingData: any) {
  const supabase = await createContextClient('customer');

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { error: { message: "You must be logged in to create a booking." } };
  }

  if (!bookingData.selectedCar) {
    return { error: { message: "No car selected for the booking." } };
  }

  // Combine date and time for pickup and dropoff
  const pickupDateTime = new Date(
    `${bookingData.pickUpDate}T${bookingData.pickUpTime}`
  );
  const dropoffDateTime = new Date(
    `${bookingData.dropOffDate}T${bookingData.dropOffTime}`
  );

  // Calculate total amount (simple duration-based calculation)
  const rentalDurationHours =
    (dropoffDateTime.getTime() - pickupDateTime.getTime()) / (1000 * 60 * 60);
  const rentalDurationDays = Math.ceil(rentalDurationHours / 24);
  const totalAmount =
    rentalDurationDays * bookingData.selectedCar.price_per_day;

  const newBooking = {
    customer_id: user.id,
    car_id: bookingData.selectedCar.id,
    pickup_location: bookingData.pickUpLocation,
    dropoff_location: bookingData.dropOffLocation,
    pickup_datetime: pickupDateTime.toISOString(),
    dropoff_datetime: dropoffDateTime.toISOString(),
    special_requests: bookingData.notes,
    status: "Pending",
    total_amount: totalAmount,
  };

  const { data, error } = await supabase
    .from("bookings")
    .insert([newBooking])
    .select();

  if (error) {
    console.error("Error creating booking:", error);
    return { error };
  }

  // Note: Car status will be updated to 'Rented' only when booking becomes 'Active'
  // Pending bookings don't change car availability immediately

  // If we have booking data, optionally create dependent records
  if (data && data.length > 0) {
    const booking = data[0];

    // 1) Upsert booking documents (requirements + payment proof)
    try {
      const docMap: Array<{
        key:
          | "driversLicense"
          | "governmentId"
          | "proofOfBilling"
          | "proofOfPayment";
        type:
          | "drivers_license"
          | "government_id"
          | "proof_of_billing"
          | "proof_of_payment";
      }> = [
        { key: "driversLicense", type: "drivers_license" },
        { key: "governmentId", type: "government_id" },
        { key: "proofOfBilling", type: "proof_of_billing" },
        { key: "proofOfPayment", type: "proof_of_payment" },
      ];

      const toUpsert = docMap
        .map(({ key, type }) => {
          const arr = (bookingData as any)?.[key] as
            | Array<{
                url?: string | null;
                fileName?: string;
                fileSize?: number;
                fileType?: string;
              }>
            | undefined;
          const first = Array.isArray(arr) && arr.length > 0 ? arr[0] : null;
          if (!first || !first.url) return null;

          // Fallbacks for metadata
          const url = first.url as string;
          const nameFromUrl = (() => {
            try {
              const part = url.split("/").pop() || "document";
              return part.split("?")[0];
            } catch {
              return "document";
            }
          })();
          const fileName = first.fileName || nameFromUrl;
          const fileType = first.fileType || (fileName.endsWith(".pdf")
              ? "application/pdf"
              : fileName.match(/\.(png)$/i)
              ? "image/png"
              : fileName.match(/\.(jpe?g)$/i)
              ? "image/jpeg"
              : fileName.match(/\.(webp)$/i)
              ? "image/webp"
              : "application/octet-stream");
          const fileSize = typeof first.fileSize === "number" ? first.fileSize : 0;

          return {
            booking_id: booking.id,
            document_type: type,
            file_url: url,
            file_name: fileName,
            file_size: fileSize,
            file_type: fileType,
            verification_status: "pending" as const,
          };
        })
        .filter(Boolean) as Array<{
        booking_id: string;
        document_type: string;
        file_url: string;
        file_name: string;
        file_size: number;
        file_type: string;
        verification_status: "pending";
      }>;

      if (toUpsert.length > 0) {
        const { error: docsError } = await supabase
          .from("booking_documents")
          .upsert(toUpsert, { onConflict: "booking_id,document_type" });
        if (docsError) {
          console.error("Error upserting booking documents:", docsError);
        }
      }
    } catch (e) {
      console.error("Unexpected error while syncing booking documents:", e);
    }

    // 2) Create a payment record if payment info is provided
    if (bookingData.paymentMethod) {
      // Determine payment status based on method and proof
      let paymentStatus = "Pending";
      let proofOfPaymentUrl = null as string | null;

      if (bookingData.paymentMethod === "GCash" || 
          bookingData.paymentMethod === "Bank Transfer" || 
          bookingData.paymentMethod === "Remittance Center") {
        paymentStatus = "Pending Verification"; // Digital payments need verification
      } else if (
        bookingData.proofOfPayment &&
        bookingData.proofOfPayment.length > 0
      ) {
        paymentStatus = "Pending Verification"; // Digital payment with proof uploaded - requires admin approval
        // Get the first uploaded proof file URL
        proofOfPaymentUrl = bookingData.proofOfPayment[0].url || null;
      }

      const paymentRecord = {
        booking_id: booking.id,
        amount: totalAmount,
        status: paymentStatus,
        method: bookingData.paymentMethod,
        proof_of_payment_url: proofOfPaymentUrl,
        transaction_date: new Date().toISOString(),
      };

      const { error: paymentError } = await supabase
        .from("payments")
        .insert([paymentRecord]);

      if (paymentError) {
        console.error("Error creating payment record:", paymentError);
        // Note: We don't return error here as the booking was successful
        // The payment record can be created manually if needed
      }
    }
  }

  // Send booking submission confirmation email to customer
  try {
    const { data: customer, error: customerError } = await supabase
      .from("profiles")
      .select("email, full_name")
      .eq("id", user.id)
      .single();

    if (!customerError && customer?.email && data && data.length > 0) {
      const booking = data[0];
      
      // Get car details for the email
      const { data: carDetails, error: carError } = await supabase
        .from("cars")
        .select("model")
        .eq("id", bookingData.selectedCar.id)
        .single();

      if (!carError && carDetails) {
        const dashboardUrl = process.env.NEXT_PUBLIC_SITE_URL 
          ? `${process.env.NEXT_PUBLIC_SITE_URL}/customer/dashboard` 
          : "/customer/dashboard";

        const submissionEmail = buildBookingSubmissionEmail({
          customerName: customer.full_name || null,
          bookingRef: booking.booking_ref || booking.id,
          vehicleModel: carDetails.model,
          pickupDate: format(pickupDateTime, "MMM d, yyyy"),
          returnDate: format(dropoffDateTime, "MMM d, yyyy"),
          pickupLocation: bookingData.pickUpLocation,
          totalAmount: totalAmount,
          dashboardUrl,
        });

        const emailResult = await sendEmail({
          to: customer.email,
          subject: submissionEmail.subject,
          html: submissionEmail.html,
        });

        if (!emailResult.ok) {
          console.error("Failed to send booking submission email:", emailResult.error);
          // Don't fail the booking creation if email fails
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.log("Booking submission email sent successfully to:", customer.email);
          }
        }
      }
    }
  } catch (emailError) {
    console.error("Error sending booking submission email:", emailError);
    // Don't fail the booking creation if email fails
  }

  return { data };
}

export async function getCustomerBookings() {
  const supabase = await createContextClient('customer');

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { data: null, error: { message: "You must be logged in to view bookings." } };
  }

  const { data, error } = await supabase
    .from("bookings")
    .select(`
      id,
      booking_ref,
      pickup_location,
      dropoff_location,
      pickup_datetime,
      dropoff_datetime,
      special_requests,
      status,
      total_amount,
      created_at,
      updated_at,
      car_id,
      cars!inner (
        id,
        model,
        type,
        image_url,
        price_per_day
      )
    `)
    .eq("customer_id", user.id)
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching bookings:", error);
    return { data: null, error: normalizeError(error) };
  }

  return { data };
}

/**
 * Updates booking status and car availability accordingly
 */
export async function updateBookingStatus(bookingId: string, newStatus: string) {
  const supabase = await createContextClient('customer');

  // Get booking details first
  const { data: booking, error: bookingError } = await supabase
    .from("bookings")
    .select("id, booking_ref, car_id")
    .eq("id", bookingId)
    .single();

  if (bookingError || !booking) {
    console.error("Error fetching booking:", bookingError);
    return { error: normalizeError(bookingError) };
  }

  // Update booking status
  const { error: updateError } = await supabase
    .from("bookings")
    .update({ status: newStatus })
    .eq("id", bookingId);

  if (updateError) {
    console.error("Error updating booking status:", updateError);
    return { error: normalizeError(updateError) };
  }

  // Update car status based on booking status
  let carStatus = "Available";
  if (newStatus === "Active") {
    carStatus = "Rented";
  } else if (newStatus === "Completed" || newStatus === "Cancelled") {
    // Check if there are other active or confirmed bookings for this car
    const { data: activeBookings } = await supabase
      .from("bookings")
      .select("id")
      .eq("car_id", booking.car_id)
      .in("status", ["Active", "Confirmed"])
      .neq("id", bookingId);

    carStatus = activeBookings && activeBookings.length > 0 ? "Rented" : "Available";
  }
  // For Pending status, we need to check if there are any Active bookings
  else if (newStatus === "Pending") {
    const { data: activeBookings } = await supabase
      .from("bookings")
      .select("id")
      .eq("car_id", booking.car_id)
      .in("status", ["Active", "Confirmed"])
      .neq("id", bookingId);

    carStatus = activeBookings && activeBookings.length > 0 ? "Rented" : "Available";
  }

  // Update car status
  const { error: carUpdateError } = await supabase
    .from("cars")
    .update({ status: carStatus })
    .eq("id", booking.car_id);

  if (carUpdateError) {
    console.error("Error updating car status:", carUpdateError);
    // Don't fail the operation if car status update fails
  }

  return { success: true };
}

/**
 * Checks if a car is available for booking in a given date range
 */
export async function checkCarAvailability(carId: string, pickupDate: Date, dropoffDate: Date) {
  const supabase = await createContextClient('customer');

  // Check car status
  const { data: car, error: carError } = await supabase
    .from("cars")
    .select("status")
    .eq("id", carId)
    .single();

  if (carError || !car) {
    return { available: false, reason: "Car not found" };
  }

  if (car.status !== "Available") {
    return { available: false, reason: `Car is currently ${car.status.toLowerCase()}` };
  }

  // Check for conflicting bookings (Active, Confirmed, or Pending that overlap with requested dates)
  const { data: conflictingBookings, error: bookingError } = await supabase
    .from("bookings")
    .select("id, status")
    .eq("car_id", carId)
    .in("status", ["Active", "Confirmed", "Pending"])
    .or(`pickup_datetime.lte.${dropoffDate.toISOString()},dropoff_datetime.gte.${pickupDate.toISOString()}`);

  if (bookingError) {
    console.error("Error checking booking conflicts:", bookingError);
    return { available: false, reason: "Error checking availability" };
  }

  if (conflictingBookings && conflictingBookings.length > 0) {
    const activeBooking = conflictingBookings.find(b => b.status === "Active");
    if (activeBooking) {
      return { available: false, reason: "Car is currently rented" };
    }
    return { available: false, reason: "Car is already booked for this period" };
  }

  return { available: true };
}
