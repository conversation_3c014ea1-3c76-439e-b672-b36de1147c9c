import { NextRequest, NextResponse } from 'next/server'

/**
 * API route to test authentication cookie isolation
 * This provides real browser environment testing
 */
export async function GET(request: NextRequest) {
  // This will run in the browser environment
  const diagnostics = {
    timestamp: new Date().toISOString(),
    userAgent: request.headers.get('user-agent'),
    storageKeys: [],
    issues: [],
    success: true
  }

  try {
    // Return diagnostic script that runs in browser
    const diagnosticScript = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Authentication Cookie Diagnostics</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .success { color: green; }
          .error { color: red; }
          .warning { color: orange; }
          .info { color: blue; }
          .results { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
          pre { background: #333; color: #fff; padding: 10px; border-radius: 3px; overflow: auto; }
        </style>
      </head>
      <body>
        <h1>🔍 Authentication <PERSON><PERSON> Diagnostics</h1>
        <div id="results">Running diagnostics...</div>
        
        <script>
          async function runDiagnostics() {
            const results = {
              timestamp: new Date().toISOString(),
              storageKeys: [],
              issues: [],
              localStorage: {},
              sessionStorage: {},
              success: true
            };
            
            // Check localStorage for auth-related keys
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && key.includes('sb-')) {
                results.storageKeys.push(key);
                results.localStorage[key] = localStorage.getItem(key) ? 'has_data' : 'empty';
              }
            }
            
            // Check sessionStorage for auth-related keys
            for (let i = 0; i < sessionStorage.length; i++) {
              const key = sessionStorage.key(i);
              if (key && key.includes('sb-')) {
                results.storageKeys.push(key);
                results.sessionStorage[key] = sessionStorage.getItem(key) ? 'has_data' : 'empty';
              }
            }
            
            // Analyze findings
            const customerKeys = results.storageKeys.filter(k => k.includes('customer'));
            const adminKeys = results.storageKeys.filter(k => k.includes('admin'));
            const genericKeys = results.storageKeys.filter(k => !k.includes('customer') && !k.includes('admin'));
            
            if (results.storageKeys.length === 0) {
              results.issues.push('No Supabase storage keys found - auth may not be initialized');
            }
            
            if (customerKeys.length === 0 && adminKeys.length === 0 && genericKeys.length > 0) {
              results.issues.push('Found generic storage keys but no context-specific keys');
            }
            
            if (customerKeys.length === 0) {
              results.issues.push('No customer-specific storage keys detected');
            }
            
            if (adminKeys.length === 0) {
              results.issues.push('No admin-specific storage keys detected');
            }
            
            // Test storage isolation by setting test values
            if (process.env.NODE_ENV === 'development') {
              console.log('🧪 Testing storage isolation...');
            }
            
            // Set test customer data
            localStorage.setItem('sb-customer-auth-token.test', 'customer-test-data');
            
            // Set test admin data  
            localStorage.setItem('sb-admin-auth-token.test', 'admin-test-data');
            
            // Verify isolation
            const customerTest = localStorage.getItem('sb-customer-auth-token.test');
            const adminTest = localStorage.getItem('sb-admin-auth-token.test');
            
            if (customerTest === adminTest) {
              results.issues.push('Storage isolation test failed - identical values returned');
            } else if (customerTest && adminTest) {
              if (process.env.NODE_ENV === 'development') {
                console.log('✅ Storage isolation test passed');
              }
            }
            
            // Clean up test data
            localStorage.removeItem('sb-customer-auth-token.test');
            localStorage.removeItem('sb-admin-auth-token.test');
            
            results.success = results.issues.length === 0;
            
            // Display results
            displayResults(results);
          }
          
          function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            
            let html = '<div class="results">';
            html += '<h2>📊 Diagnostic Results</h2>';
            html += '<p><strong>Timestamp:</strong> ' + results.timestamp + '</p>';
            html += '<p><strong>Total Issues Found:</strong> <span class="' + (results.issues.length > 0 ? 'error' : 'success') + '">' + results.issues.length + '</span></p>';
            
            if (results.issues.length > 0) {
              html += '<h3 class="error">🚨 Issues Detected:</h3><ul>';
              results.issues.forEach(issue => {
                html += '<li class="error">' + issue + '</li>';
              });
              html += '</ul>';
            } else {
              html += '<p class="success">✅ No authentication cookie issues detected!</p>';
            }
            
            html += '<h3>🔑 Storage Keys Found:</h3>';
            if (results.storageKeys.length > 0) {
              html += '<ul>';
              results.storageKeys.forEach(key => {
                const type = key.includes('customer') ? 'info' : key.includes('admin') ? 'warning' : 'error';
                html += '<li class="' + type + '">' + key + '</li>';
              });
              html += '</ul>';
            } else {
              html += '<p class="error">No storage keys found</p>';
            }
            
            html += '<h3>📱 localStorage Data:</h3>';
            html += '<pre>' + JSON.stringify(results.localStorage, null, 2) + '</pre>';
            
            html += '<h3>🔧 sessionStorage Data:</h3>';
            html += '<pre>' + JSON.stringify(results.sessionStorage, null, 2) + '</pre>';
            
            html += '</div>';
            
            // Add recommendation section
            html += '<div class="results">';
            html += '<h3>🔧 Recommendations:</h3>';
            if (results.issues.length > 0) {
              html += '<ul>';
              html += '<li>Ensure customer and admin auth contexts use separate storage keys</li>';
              html += '<li>Check that Supabase clients are properly configured with different storageKey values</li>';
              html += '<li>Verify that auth providers are not sharing the same client instance</li>';
              html += '<li>Test tab close/reopen behavior to ensure sessions persist correctly</li>';
              html += '</ul>';
            } else {
              html += '<p class="success">Authentication cookie isolation appears to be working correctly!</p>';
            }
            html += '</div>';
            
            resultsDiv.innerHTML = html;
            
            // Also log to console for debugging
            if (process.env.NODE_ENV === 'development') {
              console.log('🔍 Authentication Diagnostics Results:', results);
            }
          }
          
          // Run diagnostics when page loads
          document.addEventListener('DOMContentLoaded', runDiagnostics);
        </script>
      </body>
      </html>
    `;

    return new NextResponse(diagnosticScript, {
      headers: {
        'Content-Type': 'text/html',
      },
    })
  } catch (error) {
    return NextResponse.json({
      error: 'Failed to run diagnostics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
