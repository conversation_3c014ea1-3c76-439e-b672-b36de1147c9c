// Test file to debug GPS token decryption

// GPS Data Encryption/Decryption
const ENCRYPT_KEY = "OllieGPS2024";

// Function to decrypt the GPS token (from ESP32)
function decryptGPSToken(encryptedToken: string): { lat: number; lon: number; acc: number; deviceId: string } | null {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 Decrypting GPS token:', {
        tokenLength: encryptedToken.length,
        tokenSample: encryptedToken.substring(0, 20) + '...'
      });
    }
    
    // Ensure the token is in the correct format
    if (!/^[0-9a-f]+$/i.test(encryptedToken)) {
      console.error('Invalid token format (not hex):', encryptedToken);
      return null;
    }
    
    const hexPairs = encryptedToken.match(/.{1,2}/g) || [];
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 Hex pairs count:', hexPairs.length);
      console.log('🔐 First few hex pairs:', hexPairs.slice(0, 10));
    }
    
    let decrypted = "";
    
    for (let i = 0; i < hexPairs.length; i++) {
      const hexValue = hexPairs[i];
      const hexChar = parseInt(hexValue, 16);
      const keyChar = ENCRYPT_KEY.charCodeAt(i % ENCRYPT_KEY.length);
      const decryptedChar = String.fromCharCode(hexChar ^ keyChar);
      
      // Debug first few characters
      if (i < 10) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`🔐 [${i}] hex: ${hexValue} (${hexChar}) ^ key: ${String.fromCharCode(keyChar)} (${keyChar}) = ${decryptedChar} (${hexChar ^ keyChar})`);
        }
      }
      
      decrypted += decryptedChar;
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 Decrypted data:', decrypted);
      console.log('🔐 Decrypted data (escaped):', JSON.stringify(decrypted));
    }
    
    // Parse decrypted data: "lat,lon,acc,deviceId"
    const parts = decrypted.split(',');
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 Parsed parts:', parts);
      console.log('🔐 Parts count:', parts.length);
    }
    
    if (parts.length >= 4) {
      const lat = parseFloat(parts[0]);
      const lon = parseFloat(parts[1]);
      const acc = parseFloat(parts[2]);
      const deviceId = parts[3];
      
      if (process.env.NODE_ENV === 'development') {
        console.log('🔐 Parsed values:', { lat, lon, acc, deviceId });
      }
      
      if (!isNaN(lat) && !isNaN(lon)) {
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ Successfully decrypted GPS data:', { lat, lon, acc, deviceId });
        }
        return { lat, lon, acc, deviceId };
      }
    }
    
    throw new Error('Invalid decrypted data format');
  } catch (error) {
    console.error('Decryption failed:', error);
    return null;
  }
}

// Test with the actual GPS token from your request
const testToken = "7e58425f5c766661061c03067e425c5d577f636a1e061c00630005051c203f7e574342077d415c58";
const decrypted = decryptGPSToken(testToken);
if (process.env.NODE_ENV === 'development') {
  console.log('Final decryption result:', decrypted);
}

// Encrypt a test GPS string to verify our encryption/decryption works correctly
function encryptGPSData(data: string): string {
  let encrypted = "";
  const hexDigits = "0123456789abcdef";
  
  for (let i = 0; i < data.length; i++) {
    const charCode = data.charCodeAt(i);
    const keyChar = ENCRYPT_KEY.charCodeAt(i % ENCRYPT_KEY.length);
    const encChar = charCode ^ keyChar;
    
    encrypted += hexDigits[(encChar >> 4) & 0x0F];  // High nibble
    encrypted += hexDigits[encChar & 0x0F];         // Low nibble
  }
  
  return encrypted;
}

// Test encryption
const testData = "14.691624,121.042839,6.4,lilygo-esp32-01";
const encrypted = encryptGPSData(testData);
if (process.env.NODE_ENV === 'development') {
  console.log('Test encryption:', encrypted);
}

// Verify that decryption works with our encrypted test data
const verifyDecryption = decryptGPSToken(encrypted);
if (process.env.NODE_ENV === 'development') {
  console.log('Verify decryption:', verifyDecryption);
}
