#!/bin/bash

echo "Installing PathLink GUI Dependencies..."
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.7+ from https://python.org or use your package manager"
    exit 1
fi

echo "Python found. Installing dependencies..."
echo

# Install required packages
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo
    echo "Error: Failed to install some dependencies"
    echo "Please check the error messages above"
    exit 1
fi

echo
echo "Dependencies installed successfully!"
echo "You can now run: python3 pathlink_gui.py"
echo
