-- Migration: Update booking document requirements to 4 required documents
-- Remove 'proof_of_age' and 'security_deposit_confirmation'
-- Add 'proof_of_billing' as a new document type
-- Safe to run multiple times due to IF EXISTS on DROP

BEGIN;

-- Update the document_type check constraint
ALTER TABLE public.booking_documents
  DROP CONSTRAINT IF EXISTS booking_documents_document_type_check;

ALTER TABLE public.booking_documents
  ADD CONSTRAINT booking_documents_document_type_check
  CHECK (
    document_type IN (
      'drivers_license',
      'government_id',
      'proof_of_billing',
      'proof_of_payment'
    )
  );

-- Update check function to include exactly 4 required docs
CREATE OR REPLACE FUNCTION check_booking_documents_complete(booking_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    required_docs TEXT[] := ARRAY[
        'drivers_license',
        'government_id',
        'proof_of_billing',
        'proof_of_payment'
    ];
    uploaded_count INTEGER;
BEGIN
    SELECT COUNT(DISTINCT document_type)
    INTO uploaded_count
    FROM public.booking_documents 
    WHERE booking_id = booking_uuid 
    AND document_type = ANY(required_docs);
    
    RETURN uploaded_count = array_length(required_docs, 1);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update status function to reflect 4 required docs
CREATE OR REPLACE FUNCTION get_booking_document_status(booking_uuid UUID)
RETURNS TABLE (
    total_required INTEGER,
    uploaded_count INTEGER,
    pending_count INTEGER,
    approved_count INTEGER,
    rejected_count INTEGER,
    is_complete BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        4 as total_required,
        COUNT(*)::INTEGER as uploaded_count,
        COUNT(*) FILTER (WHERE verification_status = 'pending')::INTEGER as pending_count,
        COUNT(*) FILTER (WHERE verification_status = 'approved')::INTEGER as approved_count,
        COUNT(*) FILTER (WHERE verification_status = 'rejected')::INTEGER as rejected_count,
        check_booking_documents_complete(booking_uuid) as is_complete
    FROM public.booking_documents 
    WHERE booking_id = booking_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMIT;
