"use server";

import { createClient } from "@/lib/supabase/server";

export async function syncServerSession(tokens: { access_token: string; refresh_token: string }) {
  const supabase = await createClient();
  // Set the session on the server so SSR can read it from cookies
  const { data, error } = await supabase.auth.setSession({
    access_token: tokens.access_token,
    refresh_token: tokens.refresh_token,
  });

  if (error) {
    console.error("Failed to sync server session:", error);
    return { ok: false, error };
  }

  return { ok: true, data };
}
