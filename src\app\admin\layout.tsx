"use client"

import * as React from "react"
import { AdminShell } from "@/components/layout/admin-shell"
import { AdminProtection } from "@/components/auth/admin-protection"
import { AdminAuthProvider } from "@/components/auth/admin-auth-context"
import { AdminChatbot } from "@/components/admin/chatbot/admin-chatbot"

// Per-segment layout for all /admin routes.
// This guarantees the Admin sidebar/shell is used instead of the customer shell.
export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <AdminAuthProvider>
      <AdminProtection>
        <AdminShell>{children}</AdminShell>
        <AdminChatbot />
      </AdminProtection>
    </AdminAuthProvider>
  )
}
