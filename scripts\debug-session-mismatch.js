#!/usr/bin/env node

/**
 * Debug Script for Session Mismatch Issue
 * 
 * This script helps debug why /admin/cars gets SIGNED_IN event
 * while other admin pages get INITIAL_SESSION with no session.
 */

console.log(`
🔧 SESSION MISMATCH DEBUGGING
=============================

ISSUE IDENTIFIED: /admin/cars gets SIGNED_IN event while other pages get INITIAL_SESSION

**Evidence**:
- /admin/cars: Auth state change: {event: 'SIGNED_IN', email: '<EMAIL>', hasSession: true}
- /admin/bookings: Auth state change: {event: 'INITIAL_SESSION', email: undefined, hasSession: false}

This suggests server-side session restoration is working for /admin/cars but not other pages.

🔍 DEBUGGING STEPS:
==================

**Phase 1: Check Server-Side Cookies**

1. 🍪 **Inspect Browser Cookies**:
   - Open DevTools → Application tab → Cookies → http://localhost:3000
   - Look for cookies with names like:
     - sb-admin-auth-token.access_token
     - sb-admin-auth-token.refresh_token
     - sb-admin-access-token (old format)
     - sb-admin-refresh-token (old format)

2. 📊 **Manual Cookie Check**:
   In browser console, run:
   \`\`\`javascript
   // Check all cookies
   console.log('All cookies:', document.cookie)
   
   // Check for admin-specific cookies
   const adminCookies = document.cookie.split(';').filter(c => 
     c.includes('sb-admin') || c.includes('admin-auth-token')
   )
   console.log('Admin cookies:', adminCookies)
   \`\`\`

**Phase 2: Test Different Admin Routes**

3. 🔄 **Compare Route Behavior**:
   - Navigate to /admin/cars (working)
   - Check console logs for SIGNED_IN event
   - Navigate to /admin/bookings (failing)  
   - Check console logs for INITIAL_SESSION event
   - Compare middleware logs for both routes

4. 🌐 **Check Network Requests**:
   - Open DevTools → Network tab
   - Navigate to /admin/cars
   - Look for any auth-related requests
   - Navigate to /admin/bookings
   - Compare the network requests

**Phase 3: Middleware Analysis**

5. 🔧 **Check Middleware Logs**:
   Look for these logs in the terminal:
   \`\`\`
   [Middleware] Admin route /admin/cars: user=<EMAIL>, error=none
   [Middleware] Admin route /admin/bookings: user=none, error=...
   \`\`\`

6. 📋 **Manual Middleware Test**:
   In browser console, run:
   \`\`\`javascript
   // Test auth callback endpoint
   fetch('/api/auth/callback', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({
       session: null,
       context: 'admin'
     })
   }).then(r => r.json()).then(console.log)
   \`\`\`

**Phase 4: Session Restoration Test**

7. 🧪 **Test Session Restoration**:
   In browser console on /admin/cars, run:
   \`\`\`javascript
   // Check if there's a global Supabase client
   if (window.supabase || window.__SUPABASE_CLIENT__) {
     const client = window.supabase || window.__SUPABASE_CLIENT__
     client.auth.getSession().then(({ data: { session } }) => {
       console.log('Global session:', session)
     })
   }
   
   // Check localStorage vs cookies
   console.log('localStorage keys:', Object.keys(localStorage))
   console.log('Document cookies:', document.cookie)
   \`\`\`

🔍 EXPECTED FINDINGS:
====================

**Scenario 1: Server-Side Cookies Exist**
- Browser has sb-admin-auth-token.* cookies
- Middleware can read these cookies
- /admin/cars gets session from server-side
- Other pages fail to restore from server-side

**Scenario 2: Route-Specific Behavior**
- /admin/cars has different middleware handling
- Different server actions or API calls
- Different session restoration timing

**Scenario 3: Client-Server Sync Issue**
- Server-side session exists
- Client-side localStorage empty
- Session sync failing between server and client

🛠️ POTENTIAL FIXES:
===================

**Fix 1: Force Session Sync on All Routes**
\`\`\`typescript
// In AdminAuthProvider, force session restoration from server
useEffect(() => {
  const restoreFromServer = async () => {
    try {
      const response = await fetch('/api/auth/session')
      const { session } = await response.json()
      if (session) {
        await supabase.auth.setSession(session)
      }
    } catch (error) {
      console.warn('Failed to restore session from server:', error)
    }
  }
  
  if (!session) {
    restoreFromServer()
  }
}, [])
\`\`\`

**Fix 2: Consistent Middleware Handling**
\`\`\`typescript
// Ensure all admin routes get same middleware treatment
if (isAdminRoute) {
  // Force session restoration for all admin routes
  const { data: { session } } = await adminSupabase.auth.getSession()
  if (session) {
    // Set session in response headers for client pickup
    response.headers.set('X-Admin-Session', JSON.stringify(session))
  }
}
\`\`\`

**Fix 3: Client-Side Session Polling**
\`\`\`typescript
// Poll for session changes to catch server-side updates
useEffect(() => {
  const pollSession = setInterval(async () => {
    const { data: { session } } = await supabase.auth.getSession()
    if (session && !currentSession) {
      // Session appeared, trigger SIGNED_IN event
      setSession(session)
    }
  }, 1000)
  
  return () => clearInterval(pollSession)
}, [currentSession])
\`\`\`

🎯 SUCCESS CRITERIA:
===================

After debugging, you should find:
- ✅ Why /admin/cars gets SIGNED_IN event
- ✅ Why other pages get INITIAL_SESSION  
- ✅ Whether server-side cookies exist
- ✅ Whether middleware handles routes differently
- ✅ How to make all routes behave like /admin/cars

The goal is to make ALL admin pages get the same SIGNED_IN event that /admin/cars receives!
`);

console.log('\n🔧 SESSION MISMATCH DEBUGGING READY:');
console.log('===================================');
console.log('1. Check browser cookies for server-side session data');
console.log('2. Compare /admin/cars vs /admin/bookings behavior');
console.log('3. Analyze middleware logs for route differences');
console.log('4. Test session restoration mechanisms');
console.log('\n🧪 START DEBUGGING:');
console.log('1. Open browser DevTools');
console.log('2. Check Application → Cookies');
console.log('3. Compare console logs between routes');
console.log('4. Find why /admin/cars gets SIGNED_IN event');
console.log('\n🎯 Make all admin pages behave like /admin/cars!');
