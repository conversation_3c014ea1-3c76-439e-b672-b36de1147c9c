"use server";

import { createClient } from "@/lib/supabase/server";
import { addHours, differenceInHours, isAfter, isBefore } from "date-fns";

export interface BookingExtensionRequest {
  bookingId: string;
  customerId: string;
  originalDropoffDatetime: string;
  requestedDropoffDatetime: string;
  requestReason?: string;
  requestNotes?: string;
}

export interface BookingConflict {
  conflictingBookingId: string;
  customerName: string;
  conflictPeriod: {
    start: string;
    end: string;
  };
}

export interface AlternativeCar {
  id: string;
  model: string;
  type: string;
  plateNumber: string;
  imageUrl: string | null;
  pricePerDay: number;
  available: boolean;
}

export interface ExtensionAvailabilityCheck {
  isAvailable: boolean;
  hasConflicts: boolean;
  conflicts: BookingConflict[];
  alternativeCars: AlternativeCar[];
  additionalCost: number;
  extensionHours: number;
}

/**
 * Check if a booking extension is possible without conflicts
 */
export async function checkExtensionAvailability(
  bookingId: string,
  requestedDropoffDatetime: string
): Promise<{ data: ExtensionAvailabilityCheck | null; error: any }> {
  const supabase = await createClient();

  try {
    // Get the current booking details
    const { data: currentBooking, error: bookingError } = await supabase
      .from("bookings")
      .select(`
        id,
        car_id,
        customer_id,
        pickup_datetime,
        dropoff_datetime,
        total_amount,
        status,
        cars (
          id,
          model,
          type,
          plate_number,
          price_per_day,
          image_url
        )
      `)
      .eq("id", bookingId)
      .single();

    if (bookingError || !currentBooking) {
      return { data: null, error: bookingError || new Error("Booking not found") };
    }

    // Validate that the booking is active
    if (currentBooking.status !== "Active") {
      return { 
        data: null, 
        error: new Error("Only active bookings can be extended") 
      };
    }

    const originalDropoff = new Date(currentBooking.dropoff_datetime);
    const requestedDropoff = new Date(requestedDropoffDatetime);

    // Validate that requested time is after original dropoff
    if (!isAfter(requestedDropoff, originalDropoff)) {
      return { 
        data: null, 
        error: new Error("Extension time must be after original dropoff time") 
      };
    }

    const extensionHours = differenceInHours(requestedDropoff, originalDropoff);
    const dailyRate = currentBooking.cars?.[0]?.price_per_day || 0;
    const additionalCost = Math.ceil(extensionHours / 24) * dailyRate;

    // Check for conflicts with other bookings on the same car
    const { data: conflictingBookings, error: conflictError } = await supabase
      .from("bookings")
      .select(`
        id,
        customer_id,
        pickup_datetime,
        dropoff_datetime,
        profiles!inner (
          full_name
        )
      `)
      .eq("car_id", currentBooking.car_id)
      .neq("id", bookingId)
      .in("status", ["Pending", "Active"])
      .or(`pickup_datetime.lt.${requestedDropoffDatetime},dropoff_datetime.gt.${currentBooking.dropoff_datetime}`);

    if (conflictError) {
      return { data: null, error: conflictError };
    }

    const conflicts: BookingConflict[] = (conflictingBookings || []).map((booking: any) => ({
      conflictingBookingId: booking.id,
      customerName: booking.profiles?.full_name || "Unknown",
      conflictPeriod: {
        start: booking.pickup_datetime,
        end: booking.dropoff_datetime,
      },
    }));

    const hasConflicts = conflicts.length > 0;

    // If there are conflicts, find alternative cars
    let alternativeCars: AlternativeCar[] = [];
    if (hasConflicts) {
      const { data: availableCars, error: carsError } = await supabase
        .from("cars")
        .select(`
          id,
          model,
          type,
          plate_number,
          price_per_day,
          image_url
        `)
        .eq("status", "Available")
        .neq("id", currentBooking.car_id);

      if (!carsError && availableCars) {
        // Check each car for availability during the extension period
        const alternativePromises = availableCars.map(async (car: any) => {
          const { data: carConflicts } = await supabase
            .from("bookings")
            .select("id")
            .eq("car_id", car.id)
            .in("status", ["Pending", "Active"])
            .or(`pickup_datetime.lt.${requestedDropoffDatetime},dropoff_datetime.gt.${currentBooking.dropoff_datetime}`);

          return {
            id: car.id,
            model: car.model,
            type: car.type,
            plateNumber: car.plate_number,
            imageUrl: car.image_url,
            pricePerDay: car.price_per_day,
            available: !carConflicts || carConflicts.length === 0,
          };
        });

        const allAlternatives = await Promise.all(alternativePromises);
        alternativeCars = allAlternatives.filter(car => car.available);
      }
    }

    return {
      data: {
        isAvailable: !hasConflicts,
        hasConflicts,
        conflicts,
        alternativeCars,
        additionalCost,
        extensionHours,
      },
      error: null,
    };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Create a new booking extension request
 */
export async function createExtensionRequest(
  request: BookingExtensionRequest
): Promise<{ data: any; error: any }> {
  const supabase = await createClient();

  try {
    // Check availability first
    const { data: availability, error: availabilityError } = await checkExtensionAvailability(
      request.bookingId,
      request.requestedDropoffDatetime
    );

    if (availabilityError) {
      return { data: null, error: availabilityError };
    }

    if (!availability) {
      return { data: null, error: new Error("Unable to check availability") };
    }

    const extensionHours = availability.extensionHours;
    const additionalAmount = availability.additionalCost;

    // Create the extension request
    const { data: extensionRequest, error: insertError } = await supabase
      .from("booking_extensions")
      .insert({
        booking_id: request.bookingId,
        customer_id: request.customerId,
        original_dropoff_datetime: request.originalDropoffDatetime,
        requested_dropoff_datetime: request.requestedDropoffDatetime,
        extension_duration_hours: extensionHours,
        additional_amount: additionalAmount,
        request_reason: request.requestReason,
        request_notes: request.requestNotes,
        has_conflicts: availability.hasConflicts,
        alternative_cars: availability.alternativeCars.map(car => car.id),
        alternative_suggestions: {
          alternatives: availability.alternativeCars,
          conflicts: availability.conflicts,
        },
        status: availability.hasConflicts ? "pending" : "pending", // All requests need admin approval
      })
      .select()
      .single();

    if (insertError) {
      return { data: null, error: insertError };
    }

    // Create notification for admins
    await createExtensionNotification(
      extensionRequest.id,
      request.customerId,
      "request_created",
      "New Booking Extension Request",
      `A customer has requested to extend booking ${request.bookingId.slice(-8)}`
    );

    return { data: extensionRequest, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get extension requests for a specific booking
 */
export async function getExtensionRequests(
  bookingId: string
): Promise<{ data: any[] | null; error: any }> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("booking_extensions")
    .select(`
      *,
      bookings!inner (
        id,
        booking_ref,
        cars (
          model,
          plate_number
        )
      ),
      profiles!customer_id (
        full_name,
        email
      ),
      reviewer:profiles!reviewed_by (
        full_name
      )
    `)
    .eq("booking_id", bookingId)
    .order("created_at", { ascending: false });

  return { data, error };
}

/**
 * Get all pending extension requests (for admin)
 */
export async function getPendingExtensionRequests(): Promise<{ data: any[] | null; error: any }> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("booking_extensions")
    .select(`
      *,
      bookings!inner (
        id,
        booking_ref,
        pickup_datetime,
        dropoff_datetime,
        total_amount,
        status,
        cars (
          model,
          plate_number,
          type
        )
      ),
      profiles!customer_id (
        full_name,
        email,
        phone
      )
    `)
    .eq("status", "pending")
    .order("created_at", { ascending: false });

  return { data, error };
}

/**
 * Approve or reject an extension request
 */
export async function reviewExtensionRequest(
  extensionId: string,
  action: "approve" | "reject",
  adminId: string,
  adminNotes?: string,
  rejectionReason?: string
): Promise<{ data: any; error: any }> {
  const supabase = await createClient();

  try {
    // Get the extension request
    const { data: extension, error: extensionError } = await supabase
      .from("booking_extensions")
      .select(`
        *,
        bookings!inner (
          id,
          car_id,
          dropoff_datetime
        )
      `)
      .eq("id", extensionId)
      .single();

    if (extensionError || !extension) {
      return { data: null, error: extensionError || new Error("Extension request not found") };
    }

    if (extension.status !== "pending") {
      return { data: null, error: new Error("Extension request is no longer pending") };
    }

    const newStatus = action === "approve" ? "approved" : "rejected";

    // Update the extension request
    const { data: updatedExtension, error: updateError } = await supabase
      .from("booking_extensions")
      .update({
        status: newStatus,
        reviewed_by: adminId,
        reviewed_at: new Date().toISOString(),
        admin_notes: adminNotes,
        rejection_reason: action === "reject" ? rejectionReason : null,
      })
      .eq("id", extensionId)
      .select()
      .single();

    if (updateError) {
      return { data: null, error: updateError };
    }

    // If approved, update the original booking
    if (action === "approve") {
      const { error: bookingUpdateError } = await supabase
        .from("bookings")
        .update({
          dropoff_datetime: extension.requested_dropoff_datetime,
          total_amount: supabase.rpc("get_updated_booking_amount", {
            booking_id: extension.booking_id,
            additional_amount: extension.additional_amount,
          }),
          updated_at: new Date().toISOString(),
        })
        .eq("id", extension.booking_id);

      if (bookingUpdateError) {
        // Rollback the extension approval
        await supabase
          .from("booking_extensions")
          .update({ status: "pending", reviewed_by: null, reviewed_at: null })
          .eq("id", extensionId);

        return { data: null, error: bookingUpdateError };
      }
    }

    // Create notification for customer
    await createExtensionNotification(
      extensionId,
      extension.customer_id,
      action === "approve" ? "request_approved" : "request_rejected",
      action === "approve" ? "Extension Request Approved" : "Extension Request Rejected",
      action === "approve" 
        ? "Your booking extension has been approved"
        : `Your extension request was rejected. ${rejectionReason || ""}`
    );

    return { data: updatedExtension, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Create a notification for extension events
 */
async function createExtensionNotification(
  extensionId: string,
  recipientId: string,
  type: string,
  title: string,
  message: string
): Promise<void> {
  const supabase = await createClient();

  await supabase
    .from("booking_extension_notifications")
    .insert({
      extension_id: extensionId,
      recipient_id: recipientId,
      notification_type: type,
      title,
      message,
    });
}

/**
 * Get extension notifications for a user
 */
export async function getExtensionNotifications(
  userId: string
): Promise<{ data: any[] | null; error: any }> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from("booking_extension_notifications")
    .select("*")
    .eq("recipient_id", userId)
    .order("created_at", { ascending: false });

  return { data, error };
}

/**
 * Mark extension notifications as read
 */
export async function markExtensionNotificationsAsRead(
  notificationIds: string[]
): Promise<{ error: any }> {
  const supabase = await createClient();

  const { error } = await supabase
    .from("booking_extension_notifications")
    .update({ is_read: true })
    .in("id", notificationIds);

  return { error };
}
