import { Car, CarType, TransmissionType } from "@/lib/types"
import { createClient } from "@/lib/supabase/client"

// Define the CategoryData interface to match what we need for the Category table
export interface CategoryData {
  id: string
  name: string
  type: string
  imageUrl: string
  priceRangeFrom: number
  priceRangeTo: number
  transmissionTypes: TransmissionType[]
  carCount: number
  cars: Car[] // Reference to actual cars in this category
}

// Array of custom category descriptions
const categoryDescriptions: Record<CarType, { title: string, description: string }> = {
  "Hatchback": {
    title: "HATCHBACK VEHICLES",
    description: "Perfect for city driving and daily commutes"
  },
  "SUV": {
    title: "SUV VEHICLES",
    description: "Spacious and powerful for family trips"
  },
  "MPV": {
    title: "MPV VEHICLES",
    description: "Multi-purpose vehicles for groups"
  },
  "Sport": {
    title: "SPORT VEHICLES",
    description: "High-performance vehicles for enthusiasts"
  },
  "Coupe": {
    title: "COUPE VEHICLES",
    description: "Stylish two-door vehicles"
  },
  "Sedan": {
    title: "SEDAN VEHICLES",
    description: "Classic four-door sedans perfect for business and comfort"
  }
}


// CRUD operations for categories

export async function listCategories(): Promise<CategoryData[]> {
  try {
    const supabase = createClient()
    
    // Get categories with stats from Supabase using RPC
    const { data: categories, error } = await supabase
      .rpc('get_vehicle_categories_with_stats')
    
    if (error) throw error
    
    // Transform to match our CategoryData interface
    return (categories || []).map((cat: any) => ({
      id: cat.id,
      name: cat.name,
      type: cat.type as string,
      imageUrl: cat.image_url || "/placeholder.svg",
      priceRangeFrom: cat.actual_min_price || cat.min_price,
      priceRangeTo: cat.actual_max_price || cat.max_price,
      transmissionTypes: cat.available_transmissions || [],
      carCount: cat.vehicle_count || 0,
      cars: [] // Will be populated when needed
    }))
  } catch (error) {
    console.error('Error fetching categories:', error)
    return []
  }
}

export async function getCategoryById(id: string): Promise<CategoryData | null> {
  try {
    const categories = await listCategories()
    return categories.find(cat => cat.id === id || cat.type === id) || null
  } catch (error) {
    console.error('Error fetching category:', error)
    return null
  }
}

export async function addCategoryAction(category: Partial<CategoryData>): Promise<string> {
  try {
    const supabase = createClient()
    
    // Derive a type string from the provided name when explicit type is not supplied
    const deriveTypeFromName = (name?: string, fallback: string = "SUV"): string => {
      if (!name) return fallback
      const raw = name.replace(/vehicles?/gi, "").trim()
      const lower = raw.toLowerCase()
      const knownTypes: CarType[] = ["SUV", "Sport", "Coupe", "Hatchback", "MPV", "Sedan"]
      const matchedKnown = knownTypes.find((kt) => kt.toLowerCase() === lower)
      if (matchedKnown) return matchedKnown
      // Preserve acronyms (e.g., SUV, MPV, VAN). Otherwise Title-Case the first letter.
      return raw === raw.toUpperCase() ? raw : raw.charAt(0).toUpperCase() + raw.slice(1).toLowerCase()
    }
    const resolvedType = (category.type as string) ?? deriveTypeFromName(category.name || undefined)
    
    const { data, error } = await supabase
      .from('vehicle_categories')
      .insert({
        name: category.name || '',
        type: resolvedType,
        description: categoryDescriptions[resolvedType as CarType]?.description || '',
        min_price: category.priceRangeFrom || 0,
        max_price: category.priceRangeTo || 0,
        image_url: category.imageUrl,
        available_transmissions: category.transmissionTypes || ['Automatic'],
        display_order: 0
      })
      .select('id')
      .single()
    
    if (error) throw error
    return data?.id || resolvedType
  } catch (error) {
    console.error('Error adding category:', error)
    // Try best effort fallback to the derived type string to keep UX flowing
    const fallback = (category.type as string) || 'SUV'
    return fallback
  }
}

export async function updateCategoryAction(id: string, data: Partial<CategoryData>): Promise<boolean> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('vehicle_categories')
      .update({
        name: data.name,
        min_price: data.priceRangeFrom,
        max_price: data.priceRangeTo,
        image_url: data.imageUrl,
        available_transmissions: data.transmissionTypes
      })
      .eq('id', id)
    
    if (error) throw error
    return true
  } catch (error) {
    console.error('Error updating category:', error)
    return false
  }
}

export async function deleteCategoryAction(id: string): Promise<boolean> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('vehicle_categories')
      .delete()
      .eq('id', id)
    
    if (error) throw error
    return true
  } catch (error) {
    console.error('Error deleting category:', error)
    return false
  }
}
