"use client"

import React from 'react'
import { cn } from "@/lib/utils"
import { Loader2, Car, MapPin, CreditCard, Search, Clock, CheckCircle2, Calendar, Users, Phone, Mail } from 'lucide-react'

// Enhanced customer-specific loading component with accessibility
interface CustomerLoadingProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'spinner' | 'dots' | 'pulse'
  text?: string
  description?: string
  icon?: React.ComponentType<{ className?: string }>
  showProgress?: boolean
  progress?: number
  children?: React.ReactNode
  testId?: string
  announceChanges?: boolean
  minimumDuration?: number
}

export function CustomerLoading({
  className,
  size = 'md',
  variant = 'spinner',
  text = 'Loading',
  description,
  icon: Icon,
  showProgress = false,
  progress = 0,
  children,
  testId = 'customer-loading',
  announceChanges = true,
  minimumDuration = 400
}: CustomerLoadingProps) {
  const [isVisible, setIsVisible] = React.useState(true)
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null)

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6', 
    lg: 'h-8 w-8'
  }

  const containerSizeClasses = {
    sm: 'gap-2 text-sm',
    md: 'gap-3 text-base',
    lg: 'gap-4 text-lg'
  }

  // Respect reduced motion preference
  const prefersReducedMotion = React.useMemo(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-reduced-motion: reduce)').matches
    }
    return false
  }, [])

  React.useEffect(() => {
    // Minimum display duration to avoid flash
    if (minimumDuration > 0) {
      timeoutRef.current = setTimeout(() => {
        setIsVisible(true)
      }, minimumDuration)
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [minimumDuration])

  if (!isVisible) return null

  return (
    <div 
      className={cn(
        "flex flex-col items-center justify-center p-6",
        containerSizeClasses[size],
        className
      )}
      role="status"
      aria-live={announceChanges ? "polite" : "off"}
      aria-label={text}
      aria-busy="true"
      data-testid={testId}
    >
      {/* Loading indicator */}
      <div className="flex items-center gap-3">
        {variant === 'spinner' && (
          <div className="relative">
            {Icon ? (
              <Icon className={cn(
                sizeClasses[size], 
                "text-blue-600",
                prefersReducedMotion ? "" : "animate-pulse"
              )} />
            ) : (
              <Loader2 className={cn(
                sizeClasses[size], 
                "text-blue-600",
                prefersReducedMotion ? "" : "animate-spin"
              )} />
            )}
          </div>
        )}
        
        {variant === 'dots' && (
          <div className="flex space-x-1">
            <div className={cn(
              "w-2 h-2 bg-blue-600 rounded-full",
              prefersReducedMotion ? "" : "animate-bounce"
            )} style={{ animationDelay: '0ms' }}></div>
            <div className={cn(
              "w-2 h-2 bg-blue-600 rounded-full",
              prefersReducedMotion ? "" : "animate-bounce"
            )} style={{ animationDelay: '150ms' }}></div>
            <div className={cn(
              "w-2 h-2 bg-blue-600 rounded-full",
              prefersReducedMotion ? "" : "animate-bounce"
            )} style={{ animationDelay: '300ms' }}></div>
          </div>
        )}

        {variant === 'pulse' && (
          <div className={cn(
            "bg-blue-600 rounded-full",
            prefersReducedMotion ? "" : "animate-pulse",
            sizeClasses[size]
          )}></div>
        )}
      </div>

      {/* Screen reader only text */}
      <span className="sr-only">{text}. Please wait.</span>

      {/* Text content */}
      <div className="text-center space-y-1">
        <div className="font-medium text-gray-900 dark:text-gray-100">{text}</div>
        {description && (
          <div className="text-sm text-gray-600 dark:text-gray-400">{description}</div>
        )}
      </div>

      {/* Progress bar */}
      {showProgress && (
        <div className="w-full max-w-xs">
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
              role="progressbar"
              aria-valuenow={progress}
              aria-valuemin={0}
              aria-valuemax={100}
            ></div>
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">
            {Math.round(progress)}%
          </div>
        </div>
      )}

      {/* Custom children content */}
      {children}
    </div>
  )
}

// Specialized customer loading components
export function CustomerBookingLoading({ className }: { className?: string }) {
  return (
    <CustomerLoading
      className={className}
      size="md"
      variant="spinner"
      icon={CreditCard}
      text="Processing your booking"
      description="Please wait while we confirm your reservation..."
      testId="customer-booking-loading"
    />
  )
}

export function CustomerCarSearchLoading({ className }: { className?: string }) {
  return (
    <CustomerLoading
      className={className}
      size="md"
      variant="spinner"
      icon={Search}
      text="Finding available vehicles"
      description="Searching through our fleet for the best options..."
      testId="customer-car-search-loading"
    />
  )
}

export function CustomerVehicleLoading({ className }: { className?: string }) {
  return (
    <CustomerLoading
      className={className}
      size="md"
      variant="spinner"
      icon={Car}
      text="Loading vehicle details"
      description="Getting the latest information..."
      testId="customer-vehicle-loading"
    />
  )
}

export function CustomerPaymentLoading({ 
  className,
  progress 
}: { 
  className?: string
  progress?: number 
}) {
  return (
    <CustomerLoading
      className={className}
      size="md"
      variant="spinner"
      icon={CreditCard}
      text="Processing payment"
      description="Securely handling your transaction..."
      showProgress={progress !== undefined}
      progress={progress}
      testId="customer-payment-loading"
    />
  )
}

export function CustomerDashboardLoading({ className }: { className?: string }) {
  return (
    <CustomerLoading
      className={className}
      size="md"
      variant="spinner"
      icon={Users}
      text="Loading your dashboard"
      description="Fetching your booking history and account details..."
      testId="customer-dashboard-loading"
    />
  )
}

export function CustomerContactLoading({ className }: { className?: string }) {
  return (
    <CustomerLoading
      className={className}
      size="md"
      variant="spinner"
      icon={Mail}
      text="Loading contact information"
      description="Getting the latest contact details..."
      testId="customer-contact-loading"
    />
  )
}

// Enhanced loading overlay for customer components
interface CustomerLoadingOverlayProps {
  isVisible: boolean
  children: React.ReactNode
  loadingComponent?: React.ReactNode
  className?: string
  testId?: string
}

export function CustomerLoadingOverlay({ 
  isVisible, 
  children, 
  loadingComponent,
  className,
  testId = 'customer-loading-overlay'
}: CustomerLoadingOverlayProps) {
  return (
    <div className={cn("relative", className)} data-testid={testId}>
      {children}
      {isVisible && (
        <div 
          className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg"
          aria-busy="true"
          role="status"
          aria-label="Loading content"
        >
          {loadingComponent || <CustomerLoading text="Please wait..." />}
        </div>
      )}
    </div>
  )
}

// Customer-specific skeleton components
export function CustomerCardSkeleton({ className }: { className?: string }) {
  return (
    <div 
      className={cn("animate-pulse space-y-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg", className)}
      data-testid="customer-skeleton-card"
      aria-hidden="true"
    >
      <div className="flex items-center justify-between">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
        <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
      <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
      <div className="grid grid-cols-3 gap-4">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
      <div className="flex items-center justify-between">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
        <div className="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    </div>
  )
}

export function CustomerTableSkeleton({ rows = 5, className }: { rows?: number; className?: string }) {
  return (
    <div 
      className={cn("space-y-4", className)}
      data-testid="customer-skeleton-table"
      aria-hidden="true"
    >
      {/* Table header */}
      <div className="grid grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        {Array.from({ length: 4 }, (_, i) => (
          <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
        ))}
      </div>
      {/* Table rows */}
      {Array.from({ length: rows }, (_, i) => (
        <div key={i} className="grid grid-cols-4 gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          {Array.from({ length: 4 }, (_, j) => (
            <div key={j} className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
      ))}
    </div>
  )
}

// Hook for managing loading states with timing controls
export function useCustomerLoadingState(minimumDuration = 400) {
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null)
  const startTimeRef = React.useRef<number | null>(null)

  const startLoading = React.useCallback(() => {
    setIsLoading(true)
    setError(null)
    startTimeRef.current = Date.now()
  }, [])

  const stopLoading = React.useCallback((errorMessage?: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    if (errorMessage) {
      setError(errorMessage)
      setIsLoading(false)
      return
    }

    const elapsed = startTimeRef.current ? Date.now() - startTimeRef.current : 0
    const remainingTime = Math.max(0, minimumDuration - elapsed)
    
    timeoutRef.current = setTimeout(() => {
      setIsLoading(false)
    }, remainingTime)
  }, [minimumDuration])

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return { isLoading, error, startLoading, stopLoading }
}
