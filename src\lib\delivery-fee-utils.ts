import {
  DROP_OFF_FEES,
  RETURN_FEES,
  GARAGE_OFFICE_LOCATION,
  TIME_CATEGORIES,
  getTimePeriod,
  isGarageOfficeLocation,
  type LocationName,
  type TimePeriod,
  type FeeType,
} from "./delivery-fee-constants";

/**
 * Interface for delivery fee calculation result
 */
export interface DeliveryFeeResult {
  pickupFee: number;
  returnFee: number;
  totalFee: number;
  pickupTimePeriod: TimePeriod;
  returnTimePeriod: TimePeriod;
  isPickupFree: boolean;
  isReturnFree: boolean;
  breakdown: {
    pickup: {
      location: string;
      timePeriod: TimePeriod;
      fee: number;
      isFree: boolean;
    };
    return: {
      location: string;
      timePeriod: TimePeriod;
      fee: number;
      isFree: boolean;
    };
  };
}

/**
 * Calculate delivery fees for pickup and return locations based on time periods
 * 
 * @param pickupLocation - Pickup location name
 * @param returnLocation - Return location name  
 * @param pickupTime - Pickup time in HH:MM format
 * @param returnTime - Return time in HH:MM format
 * @returns DeliveryFeeResult with detailed breakdown
 */
export function calculateDeliveryFees(
  pickupLocation: string,
  returnLocation: string,
  pickupTime: string,
  returnTime: string
): DeliveryFeeResult {
  // Check if locations are garage/office (free)
  const isPickupFree = isGarageOfficeLocation(pickupLocation);
  const isReturnFree = isGarageOfficeLocation(returnLocation);

  // Determine time periods
  const pickupTimePeriod = getTimePeriod(pickupTime);
  const returnTimePeriod = getTimePeriod(returnTime);

  // Calculate pickup fee
  let pickupFee = 0;
  if (!isPickupFree && pickupLocation in DROP_OFF_FEES) {
    const locationFees = DROP_OFF_FEES[pickupLocation as LocationName];
    pickupFee = locationFees[pickupTimePeriod];
  }

  // Calculate return fee
  let returnFee = 0;
  if (!isReturnFree && returnLocation in RETURN_FEES) {
    const locationFees = RETURN_FEES[returnLocation as LocationName];
    returnFee = locationFees[returnTimePeriod];
  }

  const totalFee = pickupFee + returnFee;

  return {
    pickupFee,
    returnFee,
    totalFee,
    pickupTimePeriod,
    returnTimePeriod,
    isPickupFree,
    isReturnFree,
    breakdown: {
      pickup: {
        location: pickupLocation,
        timePeriod: pickupTimePeriod,
        fee: pickupFee,
        isFree: isPickupFree,
      },
      return: {
        location: returnLocation,
        timePeriod: returnTimePeriod,
        fee: returnFee,
        isFree: isReturnFree,
      },
    },
  };
}

/**
 * Get fee for a specific location and time period
 * 
 * @param location - Location name
 * @param timePeriod - "day" or "night"
 * @param feeType - "pickup" or "return"
 * @returns Fee amount or 0 if location is garage/office
 */
export function getLocationFee(
  location: string,
  timePeriod: TimePeriod,
  feeType: FeeType
): number {
  // Free if garage/office location
  if (isGarageOfficeLocation(location)) {
    return 0;
  }

  // Get appropriate fee table
  const feeTable = feeType === "pickup" ? DROP_OFF_FEES : RETURN_FEES;
  
  if (location in feeTable) {
    const locationFees = feeTable[location as LocationName];
    return locationFees[timePeriod];
  }

  return 0;
}

/**
 * Format time period for display
 */
export function formatTimePeriod(timePeriod: TimePeriod): string {
  return timePeriod === "day" 
    ? TIME_CATEGORIES.DAY.label 
    : TIME_CATEGORIES.NIGHT.label;
}

/**
 * Format time range for display
 */
export function formatTimeRange(timePeriod: TimePeriod): string {
  return timePeriod === "day" 
    ? TIME_CATEGORIES.DAY.range 
    : TIME_CATEGORIES.NIGHT.range;
}

/**
 * Format delivery fee breakdown for display
 */
export function formatDeliveryFeeBreakdown(result: DeliveryFeeResult): string {
  const parts: string[] = [];

  if (result.pickupFee > 0) {
    parts.push(
      `Pickup (${result.breakdown.pickup.location}, ${formatTimePeriod(result.pickupTimePeriod)}): ₱${result.pickupFee.toFixed(2)}`
    );
  } else if (result.isPickupFree) {
    parts.push(`Pickup: Free (Garage/Office)`);
  }

  if (result.returnFee > 0) {
    parts.push(
      `Return (${result.breakdown.return.location}, ${formatTimePeriod(result.returnTimePeriod)}): ₱${result.returnFee.toFixed(2)}`
    );
  } else if (result.isReturnFree) {
    parts.push(`Return: Free (Garage/Office)`);
  }

  return parts.join(" + ");
}

/**
 * Validate if a location name exists in fee tables
 */
export function isValidLocation(location: string): boolean {
  return isGarageOfficeLocation(location) || location in DROP_OFF_FEES;
}

/**
 * Get suggested locations based on partial input
 */
export function getSuggestedLocations(input: string): string[] {
  const lowercaseInput = input.toLowerCase();
  const allLocations = [
    GARAGE_OFFICE_LOCATION.name,
    GARAGE_OFFICE_LOCATION.address,
    ...Object.keys(DROP_OFF_FEES),
  ];

  return allLocations.filter(location =>
    location.toLowerCase().includes(lowercaseInput)
  );
}

/**
 * Calculate total booking cost including vehicle rental and delivery fees
 */
export function calculateTotalBookingCost(
  vehiclePricePerDay: number,
  rentalDays: number,
  deliveryFees: DeliveryFeeResult
): {
  vehicleRental: number;
  deliveryFees: number;
  total: number;
} {
  const vehicleRental = vehiclePricePerDay * rentalDays;
  const total = vehicleRental + deliveryFees.totalFee;

  return {
    vehicleRental,
    deliveryFees: deliveryFees.totalFee,
    total,
  };
}
