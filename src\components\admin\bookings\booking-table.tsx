"use client"

import * as React from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { StatusBadge, PaymentBadge } from "./status-badges"
import { MoreVerticalIcon, EyeIcon, EditIcon, CalendarIcon, XIcon, ReceiptIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { type Booking } from "@/lib/types"
import { useIsMobile } from "@/hooks/use-mobile"
import { BookingCard } from "./booking-card"

interface BookingTableRowData extends Booking {
  booking_ref: string
  userName: string
  carModel: string
  from: Date
  to: Date
  days: number
  payStatus: "Paid" | "Unpaid" | "Partial" | "Refunded"
  totalAmount: number
}

interface BookingTableProps {
  bookings: BookingTableRowData[]
  onRowClick?: (booking: BookingTableRowData) => void
  onStatusChange?: (bookingId: string, status: string) => void
  onEdit?: (booking: BookingTableRowData) => void
  onCancel?: (booking: BookingTableRowData) => void
  onViewReceipt?: (booking: BookingTableRowData) => void
  onAddToCalendar?: (booking: BookingTableRowData) => void
  onAddToOnPageCalendar?: (booking: BookingTableRowData) => void
  className?: string
}

export function BookingTable({
  bookings,
  onRowClick,
  onStatusChange,
  onEdit,
  onCancel,
  onViewReceipt,
  onAddToCalendar,
  onAddToOnPageCalendar,
  className
}: BookingTableProps) {
  const isMobile = useIsMobile()
  
  const handleRowClick = (booking: BookingTableRowData, event: React.MouseEvent) => {
    // Don't trigger row click if clicking on dropdown or buttons
    if ((event.target as HTMLElement).closest('[data-dropdown-trigger]') || 
        (event.target as HTMLElement).closest('button')) {
      return
    }
    onRowClick?.(booking)
  }

  const formatDateRange = (from: Date, to: Date) => {
    const fromFormatted = format(from, "MMM d")
    const toFormatted = format(to, "MMM d, yyyy")
    const timeFormatted = format(from, "h:mm a")
    
    return {
      date: `${fromFormatted} - ${toFormatted}`,
      time: timeFormatted
    }
  }

  // Render card layout for mobile/tablet screens
  if (isMobile) {
    return (
      <div className={cn("p-2 xs:p-3 sm:p-4", className)}>
        {bookings.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground bg-white rounded-lg border border-gray-200 shadow-sm">
            No bookings found
          </div>
        ) : (
          <div className="space-y-4">
            {bookings.map((booking) => (
              <BookingCard
                key={booking.id}
                booking={booking}
                onRowClick={onRowClick}
                onStatusChange={onStatusChange}
                onEdit={onEdit}
                onCancel={onCancel}
                onViewReceipt={onViewReceipt}
                onAddToCalendar={onAddToCalendar || ((booking) => {
                  window.open(`https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(`${booking.userName} – ${booking.carModel}`)}&dates=${booking.from.toISOString().replace(/[-:]/g, '').split('.')[0]}Z/${booking.to.toISOString().replace(/[-:]/g, '').split('.')[0]}Z`, '_blank')
                })}
                onAddToOnPageCalendar={onAddToOnPageCalendar}
              />
            ))}
          </div>
        )}
      </div>
    )
  }
  
  // Render table layout for desktop screens
  return (
    <div className={cn("border border-gray-200 rounded-lg overflow-hidden shadow-sm bg-white", className)}>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader className="bg-gray-50 sticky top-0 z-10">
            <TableRow className="border-b border-gray-200">
              <TableHead className="w-[80px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">ID</TableHead>
              <TableHead className="min-w-[140px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Renter</TableHead>
              <TableHead className="min-w-[120px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Car</TableHead>
              <TableHead className="min-w-[140px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Pickup</TableHead>
              <TableHead className="min-w-[140px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Return</TableHead>
              <TableHead className="w-[80px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Duration</TableHead>
              <TableHead className="w-[90px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Status</TableHead>
              <TableHead className="w-[90px] font-semibold text-gray-900 text-xs border-r border-gray-200 text-left">Payment</TableHead>
              <TableHead className="w-[100px] text-left font-semibold text-gray-900 text-xs border-r border-gray-200">Total</TableHead>
              <TableHead className="w-[50px] font-semibold text-gray-900 text-xs text-left">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {bookings.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="text-center py-8 text-muted-foreground">
                  No bookings found
                </TableCell>
              </TableRow>
            ) : (
              bookings.map((booking, index) => {
                const dateRange = formatDateRange(booking.from, booking.to)
                const returnRange = formatDateRange(booking.to, booking.to)
                
                return (
                  <TableRow
                    key={booking.id}
                    className={cn(
                      "cursor-pointer hover:bg-gray-50 transition-colors border-b border-gray-200",
                      index % 2 === 0 ? "bg-white" : "bg-gray-50/30"
                    )}
                    onClick={(e) => handleRowClick(booking, e)}
                  >
                    <TableCell className="font-mono text-xs font-medium text-gray-600 py-3 border-r border-gray-200">
                      #{booking.booking_ref}
                    </TableCell>
                    
                    <TableCell className="py-3 border-r border-gray-200">
                      <div>
                        <p className="font-medium text-gray-900 text-sm">{booking.userName}</p>
                        <p className="text-xs text-gray-500">
                          {booking.pickup_location}
                        </p>
                      </div>
                    </TableCell>
                    
                    <TableCell className="py-3 border-r border-gray-200">
                      <p className="font-medium text-gray-900 text-sm">{booking.carModel}</p>
                    </TableCell>
                    
                    <TableCell className="py-3 border-r border-gray-200">
                      <div>
                        <p className="font-medium text-gray-900 text-sm">{dateRange.date.split(' - ')[0]} • {dateRange.time}</p>
                        <p className="text-xs text-gray-500">
                          {format(booking.from, "EEE")}
                        </p>
                      </div>
                    </TableCell>
                    
                    <TableCell className="py-3 border-r border-gray-200">
                      <div>
                        <p className="font-medium text-gray-900 text-sm">{returnRange.date.split(' - ')[0]} • {format(booking.to, "h:mm a")}</p>
                        <p className="text-xs text-gray-500">
                          {format(booking.to, "EEE")}
                        </p>
                      </div>
                    </TableCell>
                    
                    <TableCell className="py-3 border-r border-gray-200">
                      <div className="text-sm">
                        <p className="font-medium text-gray-900">{booking.days}d</p>
                        {booking.days > 1 && (
                          <p className="text-xs text-gray-500">
                            {Math.floor((booking.to.getTime() - booking.from.getTime()) / (1000 * 60 * 60)) % 24}h
                          </p>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell className="py-3 border-r border-gray-200">
                      <StatusBadge status={booking.status} />
                    </TableCell>
                    
                    <TableCell className="py-3 border-r border-gray-200">
                      <PaymentBadge status={booking.payStatus} />
                    </TableCell>
                    
                    <TableCell className="text-right py-3 border-r border-gray-200">
                      <p className="font-semibold text-base text-green-600">₱{booking.totalAmount.toFixed(2)}</p>
                    </TableCell>
                    
                    <TableCell className="py-3">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild data-dropdown-trigger>
                          <Button 
                            variant="secondary" 
                            size="sm" 
                            className="h-8 w-8 p-0 border border-gray-300 bg-white hover:border-gray-400 hover:bg-gray-50 transition-colors shadow-none hover:shadow-sm"
                          >
                            <MoreVerticalIcon className="h-4 w-4 text-gray-600" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48 bg-white border border-gray-200 shadow-lg rounded-lg p-1">
                          <DropdownMenuItem 
                            onClick={() => onRowClick?.(booking)}
                            className="flex items-center gap-2 p-2 rounded hover:bg-gray-50 hover:text-gray-900 transition-colors cursor-pointer font-normal text-sm"
                          >
                            <EyeIcon className="h-4 w-4 text-gray-600" />
                            View Details
                          </DropdownMenuItem>
                          {onEdit && (
                            <DropdownMenuItem 
                              onClick={() => onEdit(booking)}
                              className="flex items-center gap-2 p-2 rounded hover:bg-gray-50 hover:text-gray-900 transition-colors cursor-pointer font-normal text-sm"
                            >
                              <EditIcon className="h-4 w-4 text-gray-600" />
                              Edit Booking
                            </DropdownMenuItem>
                          )}
                          {onViewReceipt && (
                            <DropdownMenuItem 
                              onClick={() => onViewReceipt(booking)}
                              className="flex items-center gap-2 p-2 rounded hover:bg-gray-50 hover:text-gray-900 transition-colors cursor-pointer font-normal text-sm"
                            >
                              <ReceiptIcon className="h-4 w-4 text-gray-600" />
                              View Receipt
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem 
                            onClick={() => onAddToOnPageCalendar ? onAddToOnPageCalendar(booking) : window.open(`https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(`${booking.userName} – ${booking.carModel}`)}&dates=${booking.from.toISOString().replace(/[-:]/g, '').split('.')[0]}Z/${booking.to.toISOString().replace(/[-:]/g, '').split('.')[0]}Z`, '_blank')}
                            className="flex items-center gap-2 p-2 rounded hover:bg-gray-50 hover:text-gray-900 transition-colors cursor-pointer font-normal text-sm"
                          >
                            <CalendarIcon className="h-4 w-4 text-gray-600" />
                            {onAddToOnPageCalendar ? "Add to On-Page Calendar" : "Add to Calendar"}
                          </DropdownMenuItem>
                          {onCancel && booking.status !== 'Cancelled' && booking.status !== 'Completed' && (
                            <>
                              <div className="my-1 border-t border-gray-200"></div>
                              <DropdownMenuItem 
                                className="flex items-center gap-2 p-2 rounded hover:bg-red-50 hover:text-red-700 transition-colors cursor-pointer font-normal text-sm text-red-600"
                                onClick={() => onCancel(booking)}
                              >
                                <XIcon className="h-4 w-4 text-red-600" />
                                Cancel Booking
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
