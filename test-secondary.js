// Test with secondary connection string
const crypto = require('crypto');
const mqtt = require('mqtt');

// Try with SECONDARY connection string - copy this from Azure portal
const secondaryConnectionString = "HostName=pathlink-gps-hub.azure-devices.net;DeviceId=lilygo-a7670e-01;SharedAccessKey=4pL8aqaZv69X9H7p6TdJ9ORXWvtL9jmWBJjQz1hevuM=";

function parseConnectionString(connStr) {
  const dict = {};
  connStr.split(';').forEach(part => {
    const [key, value] = part.split('=');
    if (key && value) {
      dict[key] = value;
    }
  });
  return dict;
}

function generateSasToken(resourceUri, signingKey, expiresInMins) {
  resourceUri = encodeURIComponent(resourceUri).toLowerCase();
  const expires = Math.ceil(Date.now() / 1000) + (expiresInMins * 60);
  const stringToSign = `${resourceUri}\n${expires}`;
  
  const hmac = crypto.createHmac('sha256', Buffer.from(signingKey, 'base64'));
  hmac.update(stringToSign, 'utf8');
  const signature = encodeURIComponent(hmac.digest('base64'));
  
  return `SharedAccessSignature sr=${resourceUri}&sig=${signature}&se=${expires}`;
}

console.log('🔧 Testing with Secondary Connection String');
console.log('==========================================');

try {
  const connInfo = parseConnectionString(secondaryConnectionString);
  console.log('📋 Device Details:');
  console.log('  HostName:', connInfo.HostName);
  console.log('  DeviceId:', connInfo.DeviceId);
  console.log('  Key (Secondary):', connInfo.SharedAccessKey?.substring(0, 15) + '...');
  
  const resourceUri = `${connInfo.HostName}/devices/${connInfo.DeviceId}`;
  const sasToken = generateSasToken(resourceUri, connInfo.SharedAccessKey, 30); // 30 min expiry
  
  const brokerUrl = `mqtts://${connInfo.HostName}:8883`;
  const options = {
    clientId: connInfo.DeviceId,
    username: `${connInfo.HostName}/${connInfo.DeviceId}/?api-version=2020-09-30`,
    password: sasToken,
    protocol: 'mqtts',
    protocolVersion: 4,
    clean: true,
    connectTimeout: 45000,
    keepalive: 60
  };
  
  console.log('\n🔌 Testing Secondary Key Connection...');
  const client = mqtt.connect(brokerUrl, options);
  
  client.on('connect', () => {
    console.log('✅ SUCCESS! Secondary connection string works!');
    console.log('🎯 The issue was with the primary key');
    client.end();
  });
  
  client.on('error', (err) => {
    console.log('❌ Secondary key also failed:', err.message);
    console.log('\n🚨 Both primary and secondary keys failed!');
    console.log('📋 Next troubleshooting steps:');
    console.log('1. Check device is "Enabled" in Azure portal');
    console.log('2. Regenerate device keys in Azure portal');
    console.log('3. Verify IoT Hub has proper permissions');
    console.log('4. Check if there are any IP restrictions');
    client.end();
  });
  
  client.on('close', () => {
    console.log('🔌 Test completed');
  });
  
} catch (error) {
  console.error('❌ Script error:', error.message);
}
