"use client";

import Image from "next/image";
import Link from "next/link";
import { useAdminAuth } from "../auth/admin-auth-context";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { AdminTooltip } from "../ui/tooltip";

export function AdminTopbar() {
  const { user: adminUser, loading } = useAdminAuth();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);

    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Don't render until auth is loaded to prevent "outside provider" errors
  if (loading || !isMounted) {
    return (
      <header
        className={cn(
          "h-16 border-b bg-white transition-all duration-200 sticky top-0 z-40"
        )}
      >
        <div className="h-full flex items-center gap-3 px-4 md:px-6">
          <div className="flex-1"></div>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
      </header>
    );
  }

  return (
    <header
      className={cn(
        "h-16 border-b bg-white transition-all duration-200 sticky top-0 z-40",
        isMounted &&
          isScrolled &&
          "shadow-md bg-white/95 backdrop-blur-sm border-gray-300"
      )}
    >
      <div className="h-full flex items-center gap-3 px-4 md:px-6">
        {/* Spacer to push user info to the right */}
        <div className="flex-1"></div>

        {adminUser ? (
          <AdminTooltip
            content={
              <div>
                <div className="font-medium">Admin Profile</div>
                <div className="text-xs opacity-75 mt-1">
                  Logged in as {adminUser.email}
                </div>
                <div className="text-xs opacity-75">
                  Click to access profile settings
                </div>
              </div>
            }
            status="online"
            preview={
              <div className="space-y-2">
                <div className="text-xs">
                  <div className="font-medium">Session Info</div>
                  <div>Last login: Today at 9:32 AM</div>
                  <div>Session expires: In 6 hours</div>
                </div>
              </div>
            }
          >
            <Link
              href="/admin/settings"
              className="flex items-center gap-2 hover:bg-gray-50 rounded-md p-2 transition-colors"
            >
              <Image
                src="/avatar.svg"
                alt="Admin avatar"
                width={32}
                height={32}
                className="rounded-full bg-gray-100 p-1"
              />
              <div className="hidden sm:block">
                <div className="text-sm font-medium">{adminUser.email}</div>
                <div className="text-xs text-muted-foreground">
                  Administrator
                </div>
              </div>
            </Link>
          </AdminTooltip>
        ) : (
          <Link
            href="/admin-auth"
            className="text-sm text-blue-600 hover:underline"
          >
            Admin Sign in
          </Link>
        )}
      </div>
    </header>
  );
}
