"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Car, ArrowRight, X } from "lucide-react";
import { useOngoingBooking } from "@/hooks/use-ongoing-booking";
import { OngoingBookingConfirmationModal } from "./ongoing-booking-confirmation-modal";
import { cn } from "@/lib/utils";

/**
 * Floating action button for ongoing booking - appears when there's an active booking
 */
export function OngoingBookingBanner() {
  const { ongoingBooking, shouldShowBanner } = useOngoingBooking();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  // Don't render if no ongoing booking or shouldn't show banner
  if (!ongoingBooking || !shouldShowBanner) {
    return null;
  }

  const handleFABClick = () => {
    if (isExpanded) {
      setIsModalOpen(true);
    } else {
      setIsExpanded(true);
      // Auto-collapse after 5 seconds if not interacted with
      setTimeout(() => setIsExpanded(false), 5000);
    }
  };

  const handleDismiss = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(false);
  };

  const getStepLabel = (step: number) => {
    const steps = {
      1: "Step 1",
      2: "Step 2", 
      3: "Step 3",
      4: "Step 4",
      5: "Step 5"
    };
    return steps[step as keyof typeof steps] || `Step ${step}`;
  };

  return (
    <>
      {/* Floating Action Button positioned at top left below navbar */}
      <div className="fixed top-20 left-4 z-[9998]">
        {isExpanded ? (
          // Expanded card showing booking details
          <div 
            className={cn(
              "bg-white rounded-lg shadow-xl border border-gray-200 p-4 w-80 sm:w-96",
              "transition-all duration-300 ease-in-out transform",
              "animate-in slide-in-from-left-4"
            )}
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-blue-600" />
                <div>
                  <h3 className="font-semibold text-gray-900 text-sm">
                    Ongoing Booking
                  </h3>
                  <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800 mt-1">
                    {getStepLabel(ongoingBooking.currentStep)}
                  </Badge>
                </div>
              </div>
              <Button
                variant="secondary"
                size="icon"
                onClick={handleDismiss}
                className="h-6 w-6 text-gray-500 hover:text-gray-700"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>

            <div className="space-y-2 mb-4">
              {ongoingBooking.selectedCar && (
                <div className="flex items-center gap-2 text-sm text-gray-700">
                  <Car className="h-4 w-4 text-gray-500" />
                  <span className="truncate">
                    {ongoingBooking.selectedCar.year} {ongoingBooking.selectedCar.make} {ongoingBooking.selectedCar.model}
                  </span>
                </div>
              )}
              
              {ongoingBooking.customerName && (
                <div className="text-sm text-gray-600">
                  Customer: {ongoingBooking.customerName}
                </div>
              )}
            </div>

            <Button
              onClick={handleFABClick}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              <span>Continue Booking</span>
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        ) : (
          // Collapsed FAB button
          <Button
            onClick={handleFABClick}
            className={cn(
              "h-12 w-12 md:h-14 md:w-14 rounded-full shadow-lg",
              "bg-blue-600 hover:bg-blue-700 text-white",
              "transition-all duration-300 ease-in-out"
            )}
            size="icon"
          >
            <Clock className="h-5 w-5 md:h-6 md:w-6" />
          </Button>
        )}
      </div>

      {/* Confirmation Modal */}
      <OngoingBookingConfirmationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        ongoingBooking={ongoingBooking}
      />
    </>
  );
}
