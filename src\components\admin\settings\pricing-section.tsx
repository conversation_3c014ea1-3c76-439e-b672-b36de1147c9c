"use client";

import * as React from "react";
import { DollarSign, Save } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import type { PricingSettings } from "@/lib/types";

export function PricingSection({
  settings,
  onUpdate,
  onSave,
  saving,
}: {
  settings: PricingSettings | null;
  onUpdate: (updates: Partial<PricingSettings>) => void;
  onSave: () => void;
  saving?: boolean;
}) {
  if (!settings) return null;

  const categories = Object.keys(settings.base_rates);

  return (
    <Card data-testid="admin-settings-card-pricing">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Pricing & Fees
        </CardTitle>
        <p className="text-sm text-gray-600">
          Configure your rental rates, fees, and pricing policies
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Base Rates */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Base Daily Rates</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {categories.map((category) => (
              <div key={category} className="space-y-2">
                <Label htmlFor={`rate_${category}`}>{category}</Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                    ₱
                  </span>
                  <Input
                    id={`rate_${category}`}
                    type="number"
                    value={settings.base_rates[category]}
                    onChange={(e) => {
                      const newRates = { ...settings.base_rates };
                      newRates[category] = parseFloat(e.target.value) || 0;
                      onUpdate({ base_rates: newRates });
                    }}
                    className="pl-8"
                    placeholder="0.00"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Multipliers & Fees */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Rate Multipliers & Fees</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="weekend_multiplier">Weekend Multiplier</Label>
              <Input
                id="weekend_multiplier"
                type="number"
                step="0.1"
                value={settings.weekend_multiplier}
                onChange={(e) =>
                  onUpdate({
                    weekend_multiplier: parseFloat(e.target.value) || 1,
                  })
                }
                placeholder="1.0"
              />
              <p className="text-xs text-gray-500">
                1.0 = no markup, 1.2 = 20% increase
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="holiday_multiplier">Holiday Multiplier</Label>
              <Input
                id="holiday_multiplier"
                type="number"
                step="0.1"
                value={settings.holiday_multiplier}
                onChange={(e) =>
                  onUpdate({
                    holiday_multiplier: parseFloat(e.target.value) || 1,
                  })
                }
                placeholder="1.0"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="tax_rate">Tax Rate (%)</Label>
              <Input
                id="tax_rate"
                type="number"
                step="0.1"
                value={settings.tax_rate}
                onChange={(e) =>
                  onUpdate({ tax_rate: parseFloat(e.target.value) || 0 })
                }
                placeholder="12.0"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="security_deposit">Security Deposit (₱)</Label>
              <Input
                id="security_deposit"
                type="number"
                value={settings.security_deposit}
                onChange={(e) =>
                  onUpdate({
                    security_deposit: parseFloat(e.target.value) || 0,
                  })
                }
                placeholder="5000"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <Button
            onClick={onSave}
            disabled={saving}
            className="px-6 bg-blue-600 hover:bg-blue-700"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
