"use client"

import * as React from "react"
import { BookingTable } from "@/components/admin/bookings/booking-table"
import { KPIStatsDrawer } from "@/components/admin/bookings/kpi-stats-drawer"
import { FilterDrawer } from "@/components/admin/bookings/filter-drawer"
import { addDays, subDays } from "date-fns"

export default function ResponsiveBookingsTest() {
  // Mock data for testing
  const mockStats = {
    today: 12,
    thisWeek: 45,
    pending: 8,
    active: 15,
    completed: 32,
    cancelled: 5
  }

  const mockFilters = {
    search: "",
    status: "all",
    paymentStatus: "all",
    vehicle: "all",
    channel: "all",
    location: "all",
    dateRange: { from: null, to: null },
  }

  const today = new Date()
  const mockBookings = [
    {
      id: "B001-2023",
      userName: "<PERSON>",
      carModel: "Toyota Camry",
      from: subDays(today, 2),
      to: addDays(today, 3),
      days: 5,
      status: "Active" as const,
      payStatus: "Paid" as const,
      totalAmount: 25000,
      pickup_location: "Manila Airport Terminal 1"
    },
    {
      id: "B002-2023",
      userName: "<PERSON> with a very long name that might overflow",
      carModel: "Honda Civic",
      from: subDays(today, 1),
      to: addDays(today, 2),
      days: 3,
      status: "Pending" as const,
      payStatus: "Partial" as const,
      totalAmount: 15000,
      pickup_location: "Cebu City Downtown"
    },
    {
      id: "B003-2023",
      userName: "Robert Johnson",
      carModel: "Ford Explorer",
      from: addDays(today, 1),
      to: addDays(today, 8),
      days: 7,
      status: "Pending" as const,
      payStatus: "Unpaid" as const,
      totalAmount: 35000,
      pickup_location: "Davao International Airport"
    },
    {
      id: "B004-2023",
      userName: "Sarah Williams",
      carModel: "Nissan Altima",
      from: subDays(today, 5),
      to: subDays(today, 1),
      days: 4,
      status: "Completed" as const,
      payStatus: "Paid" as const,
      totalAmount: 20000,
      pickup_location: "Boracay Island Ferry Terminal"
    },
    {
      id: "B005-2023",
      userName: "Michael Brown",
      carModel: "Hyundai Tucson",
      from: subDays(today, 10),
      to: subDays(today, 5),
      days: 5,
      status: "Cancelled" as const,
      payStatus: "Refunded" as const,
      totalAmount: 25000,
      pickup_location: "Baguio City Center"
    }
  ]

  const [filters, setFilters] = React.useState(mockFilters)
  const [activeFilter, setActiveFilter] = React.useState<string | null>(null)
  const [activeDateFilter, setActiveDateFilter] = React.useState<"today" | "thisWeek" | null>(null)

  const handleFilterByStatus = (status: string | null) => {
    setActiveFilter(status)
    // In a real app, this would filter the bookings
  }

  const handleFilterByDate = (range: "today" | "thisWeek" | null) => {
    setActiveDateFilter(range)
    // In a real app, this would filter the bookings
  }

  const handleFiltersChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
    // In a real app, this would filter the bookings
  }

  const handleRowClick = (booking: any) => {
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log("View booking details", booking.id);
    }
  }

  const handleEdit = (booking: any) => {
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log("Edit booking", booking.id);
    }
  }

  const handleCancel = (booking: any) => {
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log("Cancel booking", booking.id);
    }
  }

  const handleViewReceipt = (booking: any) => {
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log("View receipt", booking.id);
    }
  }

  const handleAddToCalendar = (booking: any) => {
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log("Add to calendar", booking.id);
    }
    window.open(`https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(`${booking.userName} – ${booking.carModel}`)}&dates=${booking.from.toISOString().replace(/[-:]/g, '').split('.')[0]}Z/${booking.to.toISOString().replace(/[-:]/g, '').split('.')[0]}Z`, '_blank')
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Responsive Bookings Test</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-3">Current Viewport Size</h2>
        <div className="grid grid-cols-6 gap-2 text-center">
          <div className="bg-red-100 p-2 rounded block xs:hidden">xs: &lt;375px</div>
          <div className="bg-orange-100 p-2 rounded hidden xs:block sm:hidden">sm: 375px</div>
          <div className="bg-yellow-100 p-2 rounded hidden sm:block md:hidden">md: 425px</div>
          <div className="bg-green-100 p-2 rounded hidden md:block lg:hidden">lg: 768px</div>
          <div className="bg-blue-100 p-2 rounded hidden lg:block xl:hidden">xl: 1024px</div>
          <div className="bg-purple-100 p-2 rounded hidden xl:block">2xl: 1280px+</div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <h2 className="text-xl font-semibold mb-3">KPI Stats Drawer (Mobile)</h2>
          <KPIStatsDrawer 
            stats={mockStats}
            onFilterByStatus={handleFilterByStatus}
            onFilterByDate={handleFilterByDate}
            activeFilter={activeFilter}
            activeDateFilter={activeDateFilter}
          />
        </div>
        
        <div>
          <h2 className="text-xl font-semibold mb-3">Filter Drawer (Mobile)</h2>
          <FilterDrawer 
            filters={filters}
            onFiltersChange={handleFiltersChange}
            availableVehicles={["Toyota Camry", "Honda Civic", "Ford Explorer", "Nissan Altima", "Hyundai Tucson"]}
          />
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-3">Booking Table/Cards</h2>
        <BookingTable 
          bookings={mockBookings}
          onRowClick={handleRowClick}
          onEdit={handleEdit}
          onCancel={handleCancel}
          onViewReceipt={handleViewReceipt}
          onAddToCalendar={handleAddToCalendar}
        />
      </div>
      
      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Responsive Testing Instructions</h2>
        <ul className="list-disc pl-5 space-y-2">
          <li>Use browser dev tools to test different viewport sizes</li>
          <li>Verify that cards display properly on mobile (320px-767px)</li>
          <li>Verify that table displays properly on desktop (768px+)</li>
          <li>Test the action drawer functionality on mobile</li>
          <li>Check that all text is readable at all viewport sizes</li>
          <li>Verify touch targets are large enough on mobile</li>
          <li>Test horizontal/vertical spacing at all breakpoints</li>
        </ul>
      </div>
    </div>
  )
}
