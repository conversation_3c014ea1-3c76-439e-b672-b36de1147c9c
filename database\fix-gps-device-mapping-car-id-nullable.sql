-- ============================================
-- ESP32 GPS STORAGE FIX - Targeted Solution
-- ============================================

-- 1. First, ensure the get_car_id_from_device function exists and works with your schema
CREATE OR REPLACE FUNCTION public.get_car_id_from_device(device_id_param text)
RETURNS uuid
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
BEGIN
    -- Look up the car_id from the device mapping table
    -- Handle the case where car_id might be NULL in your schema
    RETURN (
        SELECT car_id 
        FROM public.gps_device_mapping 
        WHERE device_id = device_id_param 
        AND is_active = true 
        AND car_id IS NOT NULL  -- Important: handle NULL car_id
        LIMIT 1
    );
END;
$$;

-- 2. Grant proper permissions
GRANT EXECUTE ON FUNCTION public.get_car_id_from_device(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_car_id_from_device(text) TO service_role;
GRANT EXECUTE ON FUNCTION public.get_car_id_from_device(text) TO anon;

-- 3. Grant necessary permissions for GPS data insertion
GRANT INSERT, SELECT, UPDATE ON public.gps_locations TO service_role;
GRANT INSERT, SELECT, UPDATE ON public.gps_device_mapping TO service_role;
GRANT SELECT, UPDATE ON public.cars TO service_role;

-- 4. Fix the ESP32 device mapping to have a proper car_id
DO $$
DECLARE
    device_exists boolean;
    device_car_id uuid;
    first_car_id uuid;
BEGIN
    -- Check if the ESP32 device mapping exists
    SELECT EXISTS(
        SELECT 1 FROM public.gps_device_mapping 
        WHERE device_id = 'lilygo-esp32-01'
    ) INTO device_exists;
    
    IF device_exists THEN
        -- Check if it has a valid car_id
        SELECT car_id INTO device_car_id
        FROM public.gps_device_mapping 
        WHERE device_id = 'lilygo-esp32-01';
        
        IF device_car_id IS NULL THEN
            -- Get the first available car
            SELECT id INTO first_car_id 
            FROM public.cars 
            WHERE NOT is_archived OR is_archived = FALSE
            LIMIT 1;
            
            IF first_car_id IS NOT NULL THEN
                -- Update the device mapping with a valid car_id
                UPDATE public.gps_device_mapping 
                SET car_id = first_car_id,
                    device_name = 'LilyGO T-Call A7670E GPS Tracker',
                    device_type = 'lilygo',
                    is_active = true,
                    updated_at = NOW()
                WHERE device_id = 'lilygo-esp32-01';
                
                RAISE NOTICE 'Updated ESP32 device mapping: lilygo-esp32-01 -> car_id: %', first_car_id;
            ELSE
                RAISE NOTICE 'ERROR: No cars found in database to map ESP32 device to';
            END IF;
        ELSE
            RAISE NOTICE 'ESP32 device lilygo-esp32-01 already has car_id: %', device_car_id;
        END IF;
    ELSE
        -- Create new device mapping
        SELECT id INTO first_car_id 
        FROM public.cars 
        WHERE NOT is_archived OR is_archived = FALSE
        LIMIT 1;
        
        IF first_car_id IS NOT NULL THEN
            INSERT INTO public.gps_device_mapping (
                device_id,
                car_id,
                device_name,
                device_type,
                is_active
            ) VALUES (
                'lilygo-esp32-01',
                first_car_id,
                'LilyGO T-Call A7670E GPS Tracker',
                'lilygo',
                true
            );
            
            RAISE NOTICE 'Created ESP32 device mapping: lilygo-esp32-01 -> car_id: %', first_car_id;
        ELSE
            RAISE NOTICE 'ERROR: No cars found in database to create ESP32 device mapping';
        END IF;
    END IF;
END;
$$;

-- 5. Test the function to ensure it works
DO $$
DECLARE
    mapped_car_id uuid;
BEGIN
    SELECT public.get_car_id_from_device('lilygo-esp32-01') INTO mapped_car_id;
    
    IF mapped_car_id IS NOT NULL THEN
        RAISE NOTICE 'SUCCESS: ESP32 device lilygo-esp32-01 maps to car_id: %', mapped_car_id;
    ELSE
        RAISE NOTICE 'ERROR: ESP32 device mapping failed - check device_id and car_id';
    END IF;
END;
$$;

-- 6. Verify the current device mapping
SELECT 
    d.device_id,
    d.device_name,
    d.device_type,
    d.is_active,
    d.car_id,
    c.model as car_model,
    c.plate_number as car_plate,
    CASE 
        WHEN d.car_id IS NOT NULL THEN '✅ Ready for GPS data'
        ELSE '❌ Missing car_id mapping'
    END as status
FROM public.gps_device_mapping d
LEFT JOIN public.cars c ON d.car_id = c.id
WHERE d.device_id = 'lilygo-esp32-01';
