"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  X,
  Car,
  User,
  Calendar,
  MapPin,
  CreditCard,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  Upload,
  Eye,
  ShieldCheck,
  Plus,
} from "lucide-react";
import { format } from "date-fns";
import Image from "next/image";
import { formatBookingIdForDisplay } from "@/lib/reference-ids";

interface BookingDocument {
  id: string;
  booking_id: string;
  document_type: string;
  file_url: string;
  file_name: string;
  verification_status: "pending" | "approved" | "rejected" | "requires_resubmission";
  verification_notes?: string;
  created_at: string;
}

interface BookingDetailsModalProps {
  booking: {
    id: string;
    status: string;
    pickup_location: string;
    dropoff_location: string;
    pickup_datetime: string;
    dropoff_datetime: string;
    total_amount: number;
    created_at?: string;
    cars?: {
      id: string;
      model: string;
      type: string;
      plate_number: string;
      price_per_day: number;
      image_url?: string;
    } | null;
    customers?: {
      full_name: string;
      email?: string;
    } | null;
  } | null;
  isOpen: boolean;
  onClose: () => void;
  onDocumentUpload?: (bookingId: string, documentType: string) => void;
  onExtensionRequest?: (booking: any) => void;
}

export function BookingDetailsModal({ booking, isOpen, onClose, onDocumentUpload, onExtensionRequest }: BookingDetailsModalProps) {
  const [documents, setDocuments] = React.useState<BookingDocument[]>([]);
  const [loadingDocuments, setLoadingDocuments] = React.useState(false);

  // Fetch documents when modal opens
  React.useEffect(() => {
    if (booking && isOpen) {
      fetchDocuments();
    }
  }, [booking?.id, isOpen]);

  const fetchDocuments = async () => {
    if (!booking) return;
    
    setLoadingDocuments(true);
    try {
      const response = await fetch(`/api/bookings/${booking.id}/documents`);
      if (response.ok) {
        const data = await response.json();
        setDocuments(data.documents || []);
      }
    } catch (error) {
      console.error("Error fetching documents:", error);
    } finally {
      setLoadingDocuments(false);
    }
  };

  if (!booking) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-PH", {
      style: "currency",
      currency: "PHP",
    }).format(amount);
  };

  const formatDateTime = (dateString: string) => {
    return format(new Date(dateString), "MMM dd, yyyy 'at' h:mm a");
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM dd, yyyy");
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-blue-100 text-blue-700 border-blue-200";
      case "completed":
        return "bg-green-100 text-green-700 border-green-200";
      case "cancelled":
        return "bg-red-100 text-red-700 border-red-200";
      case "pending":
        return "bg-yellow-100 text-yellow-700 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return <Clock className="h-3 w-3" />;
      case "completed":
        return <CheckCircle className="h-3 w-3" />;
      case "cancelled":
        return <XCircle className="h-3 w-3" />;
      case "pending":
        return <AlertCircle className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const calculateDuration = () => {
    const pickup = new Date(booking.pickup_datetime);
    const dropoff = new Date(booking.dropoff_datetime);
    const diffTime = Math.abs(dropoff.getTime() - pickup.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const duration = calculateDuration();

  const requiredDocuments = [
    { type: "drivers_license", label: "Driver's License", required: true },
    { type: "government_id", label: "Government ID", required: true },
    { type: "proof_of_billing", label: "Proof of Billing", required: true },
    { type: "proof_of_downpayment", label: "Proof of Downpayment", required: true },
  ];

  const getDocumentStatus = (docType: string) => {
    const doc = documents.find(d => d.document_type === docType);
    if (!doc) return { status: "missing", doc: null };
    return { status: doc.verification_status, doc };
  };

  const getDocumentStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-700 border-green-200";
      case "rejected":
        return "bg-red-100 text-red-700 border-red-200";
      case "requires_resubmission":
        return "bg-orange-100 text-orange-700 border-orange-200";
      case "pending":
        return "bg-yellow-100 text-yellow-700 border-yellow-200";
      case "missing":
        return "bg-gray-100 text-gray-700 border-gray-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  const getDocumentStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle className="h-4 w-4" />;
      case "rejected":
        return <XCircle className="h-4 w-4" />;
      case "requires_resubmission":
        return <AlertCircle className="h-4 w-4" />;
      case "pending":
        return <Clock className="h-4 w-4" />;
      case "missing":
        return <Upload className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">Booking Receipt</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header with Booking ID and Status */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <div className="flex items-center gap-2">
                <Car className="h-5 w-5 text-blue-600" />
                <h3 className="font-semibold">Booking ID: {formatBookingIdForDisplay({ id: booking.id, booking_ref: booking.booking_ref })}</h3>
              </div>
              {booking.created_at && (
                <p className="text-sm text-gray-600 mt-1">
                  Created: {formatDateTime(booking.created_at)}
                </p>
              )}
            </div>
            <Badge className={`text-xs border ${getStatusColor(booking.status)}`}>
              {getStatusIcon(booking.status)}
              <span className="ml-1 capitalize">{booking.status}</span>
            </Badge>
          </div>

          {/* Customer Information */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-600" />
              <h4 className="font-medium">Customer Information</h4>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium text-gray-600">CUSTOMER NAME</p>
                <p>{booking.customers?.full_name || "N/A"}</p>
              </div>
              <div>
                <p className="font-medium text-gray-600">BOOKING CHANNEL</p>
                <p>Web</p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Vehicle Details */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Car className="h-4 w-4 text-gray-600" />
              <h4 className="font-medium">Vehicle Details</h4>
            </div>
            <div className="flex items-start gap-4 p-4 bg-blue-50 rounded-lg">
              {booking.cars?.image_url && (
                <div className="relative h-16 w-20 rounded-lg overflow-hidden bg-white flex-shrink-0">
                  <Image
                    src={booking.cars.image_url}
                    alt={booking.cars.model}
                    fill
                    className="object-cover"
                  />
                </div>
              )}
              <div className="flex-1 space-y-2">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-gray-600">VEHICLE MODEL</p>
                    <p className="font-medium">{booking.cars?.model || "Unknown Vehicle"}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-600">PLATE NUMBER</p>
                    <p className="font-medium">{booking.cars?.plate_number || "N/A"}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Rental Period */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-600" />
              <h4 className="font-medium">Rental Period</h4>
            </div>
            <div className="p-4 bg-green-50 rounded-lg space-y-4">
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="font-medium text-gray-600">PICKUP DATE</p>
                  <p className="font-medium">{formatDate(booking.pickup_datetime)}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-600">RETURN DATE</p>
                  <p className="font-medium">{formatDate(booking.dropoff_datetime)}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-600">DURATION</p>
                  <p className="font-medium">{duration} {duration === 1 ? 'day' : 'days'}</p>
                </div>
              </div>
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <MapPin className="h-4 w-4 text-gray-600" />
                  <p className="font-medium text-gray-600">PICKUP LOCATION</p>
                </div>
                <p className="text-sm">{booking.pickup_location} → {booking.dropoff_location}</p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Payment Breakdown */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <CreditCard className="h-4 w-4 text-gray-600" />
              <h4 className="font-medium">Payment Breakdown</h4>
            </div>
            <div className="p-4 bg-yellow-50 rounded-lg space-y-3">
              <div className="flex justify-between text-sm">
                <span>Base Rate ({duration} {duration === 1 ? 'day' : 'days'})</span>
                <span>{formatCurrency(booking.total_amount)}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-semibold text-lg">
                <span>Total Amount</span>
                <span className="text-green-600">{formatCurrency(booking.total_amount)}</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Document Verification Section */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <ShieldCheck className="h-4 w-4 text-gray-600" />
              <h4 className="font-medium">Document Verification</h4>
              {loadingDocuments ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
              ) : (
                <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                  {documents.filter(d => d.verification_status === "approved").length}/{requiredDocuments.length} Verified
                </Badge>
              )}
            </div>
            
            <div className="p-4 bg-orange-50 rounded-lg space-y-4">
              <div className="grid gap-3">
                {requiredDocuments.map((reqDoc) => {
                  const docStatus = getDocumentStatus(reqDoc.type);
                  const displayStatus = docStatus.status === "missing" ? "Not Uploaded" : 
                                       docStatus.status === "requires_resubmission" ? "Resubmission Required" :
                                       docStatus.status.charAt(0).toUpperCase() + docStatus.status.slice(1);
                  
                  return (
                    <div key={reqDoc.type} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-full ${getDocumentStatusColor(docStatus.status)}`}>
                          {getDocumentStatusIcon(docStatus.status)}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{reqDoc.label}</p>
                          <p className="text-sm text-gray-600">{displayStatus}</p>
                          {docStatus.doc?.verification_notes && (
                            <p className="text-xs text-orange-600 mt-1">
                              <strong>Note:</strong> {docStatus.doc.verification_notes}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {docStatus.doc?.file_url && (
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => window.open(docStatus.doc!.file_url, '_blank')}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        )}
                        {(docStatus.status === "missing" || docStatus.status === "rejected" || docStatus.status === "requires_resubmission") && onDocumentUpload && (
                          <Button
                            size="sm"
                            onClick={() => onDocumentUpload(booking.id, reqDoc.type)}
                          >
                            <Upload className="h-3 w-3 mr-1" />
                            {docStatus.status === "missing" ? "Upload" : "Reupload"}
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
              
              {booking.status === "Pending" && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h5 className="font-medium text-blue-900 mb-1">Verification Required</h5>
                      <p className="text-sm text-blue-800">
                        Please upload all required documents for verification. Your booking will be processed once all documents are approved by our admin team.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Additional Information */}
          <div className="space-y-3">
            <h4 className="font-medium">Additional Information</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium text-gray-600">DAILY RATE</p>
                <p>{formatCurrency(booking.cars?.price_per_day || 0)}/day</p>
              </div>
              <div>
                <p className="font-medium text-gray-600">VEHICLE ID</p>
                <p className="font-mono text-xs">{booking.cars?.id || "N/A"}</p>
              </div>
            </div>
          </div>

          {/* Extension Request Button for Active Bookings */}
          {booking.status === "Active" && onExtensionRequest && (
            <div className="space-y-3">
              <Separator />
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-start gap-3">
                    <Plus className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h5 className="font-medium text-green-900 mb-1">Extend Your Booking</h5>
                      <p className="text-sm text-green-800">
                        Need more time? Request an extension for your current booking.
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={() => onExtensionRequest(booking)}
                    size="sm"
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Request Extension
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="text-center text-xs text-gray-500 pt-4 border-t">
            <p>This is a system-generated receipt for booking #{booking.id.slice(-8)}</p>
            <p>Generated on {format(new Date(), "MMM dd, yyyy, hh:mm a")}</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
