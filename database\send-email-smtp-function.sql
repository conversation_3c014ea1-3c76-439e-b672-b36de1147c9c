-- Function to send emails via Supabase SMTP
-- This must be executed in the Supabase SQL Editor
CREATE OR REPLACE FUNCTION public.send_email_smtp(
  to_address TEXT,
  email_subject TEXT,
  email_html TEXT,
  email_text TEXT DEFAULT NULL
) RET<PERSON>NS BOOLEAN AS $$
DECLARE
  -- For Gmail, we can hardcode the sender email or get it from settings
  -- Replace with your actual Gmail address if hardcoding
  v_from_email TEXT := current_setting('app.settings.smtp_sender_email', TRUE);
  v_success BOOLEAN;
BEGIN
  -- Log for debugging
  RAISE LOG 'Sending email to: %', to_address;

  -- pgmail extension must be installed by Supabase support
  -- This uses the SMTP settings configured in Supabase dashboard
  PERFORM mail.send_mail(
    sender := v_from_email,
    recipient := to_address,
    subject := email_subject,
    html_body := email_html,
    text_body := email_text
  );
  
  -- If no exception occurs, consider it successful
  v_success := TRUE;
  
  RETURN v_success;
EXCEPTION WHEN OTHERS THEN
  RAISE LOG 'Error sending email: %', <PERSON><PERSON>ERRM;
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
