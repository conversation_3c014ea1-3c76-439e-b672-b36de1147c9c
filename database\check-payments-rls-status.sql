-- Check current RLS status and policies for payments table
-- This is a READ-ONLY diagnostic script

-- Check if R<PERSON> is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'payments';

-- Check existing policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'payments';

-- Check if there are any INSERT policies for authenticated users
SELECT COUNT(*) as insert_policies_count
FROM pg_policies 
WHERE tablename = 'payments' 
AND cmd = 'INSERT';
