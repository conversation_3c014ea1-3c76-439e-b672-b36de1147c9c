#!/usr/bin/env python3
"""
Test script to verify PathLink GUI fixes
"""

def test_imports():
    """Test that all required modules can be imported"""
    try:
        import tkinter as tk
        print("✓ tkinter imported successfully")
    except ImportError as e:
        print(f"✗ tkinter import failed: {e}")
        return False
    
    try:
        import requests
        print("✓ requests imported successfully")
    except ImportError as e:
        print(f"⚠ requests import failed: {e}")
        print("  HTTP functionality will be disabled")
    
    try:
        import websocket
        print("✓ websocket imported successfully")
    except ImportError as e:
        print(f"⚠ websocket import failed: {e}")
        print("  WebSocket functionality will be disabled")
    
    try:
        import configparser
        print("✓ configparser imported successfully")
    except ImportError as e:
        print(f"✗ configparser import failed: {e}")
        return False
    
    return True

def test_gui_class():
    """Test that the PathLinkGUI class can be instantiated"""
    try:
        import pathlink_gui
        print("✓ pathlink_gui module imported successfully")
        
        # Test that the class exists
        if hasattr(pathlink_gui, 'PathLinkGUI'):
            print("✓ PathLinkGUI class found")
        else:
            print("✗ PathLinkGUI class not found")
            return False
            
        # Test that the class has required attributes
        gui_class = pathlink_gui.PathLinkGUI
        
        required_attrs = [
            'environment',
            'current_protocol',
            'connection_status',
            'save_config',
            'load_config'
        ]
        
        for attr in required_attrs:
            if hasattr(gui_class, attr):
                print(f"✓ {attr} attribute found")
            else:
                print(f"✗ {attr} attribute missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing GUI class: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing PathLink GUI fixes...")
    print("=" * 40)
    
    # Test imports
    if not test_imports():
        print("\n✗ Import tests failed")
        return False
    
    print()
    
    # Test GUI class
    if not test_gui_class():
        print("\n✗ GUI class tests failed")
        return False
    
    print("\n" + "=" * 40)
    print("✓ All tests passed! The GUI should work correctly now.")
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
