// Simple MQTT connection test
require('dotenv').config({ path: '.env.local' });

console.log('🔧 Testing with EMQ Public Broker (Free, No Auth):');
console.log('BROKER:', 'mqtt://broker.emqx.io:1883');
console.log('REGION:', 'Global (with Asia-Pacific nodes)');
console.log('AUTH:', 'None required');

try {
  const mqtt = require('mqtt');
  
  const brokerUrl = "mqtt://broker.emqx.io:1883";
  const options = {
    clientId: `pathlink_test_${Date.now()}`,
    clean: true,
    connectTimeout: 30000
  };

  console.log('\n🔌 Connecting to MQTT...');
  const client = mqtt.connect(brokerUrl, options);
  
  client.on('connect', () => {
    console.log('✅ Connected successfully!');
    client.end();
  });
  
  client.on('error', (error) => {
    console.error('❌ Connection Error:', error.message);
    process.exit(1);
  });
  
  client.on('close', () => {
    console.log('🔌 Connection closed');
    process.exit(0);
  });
  
} catch (error) {
  console.error('❌ Script Error:', error.message);
  process.exit(1);
}
