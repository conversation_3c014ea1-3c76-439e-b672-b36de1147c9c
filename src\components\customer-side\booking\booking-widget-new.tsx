"use client";

import {
  ArrowLeftRight,
  Clock,
  MapPin,
  Waypoints,
  Calendar,
  Users,
  Car,
  Calculator,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import { buildBookingFlowUrl } from "@/lib/customer-paths";
import { POPULAR_DESTINATIONS } from "@/lib/philippines-locations";
import { TimePicker } from "@/components/customer-side/time";
import { DatePicker } from "@/components/customer-side/date";
import {
  FixedPickupLocationField,
  DropOffLocationDropdown,
} from "@/components/ui/booking-location-components";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { AuthRequiredModal } from "@/components/customer-side/auth/auth-required-modal";
import { CompactDayNightSelector } from "@/components/ui/day-night-selector";
import {
  calculateDeliveryFees,
  formatDeliveryFeeBreakdown,
  calculateTotalBookingCost,
  type DeliveryFeeResult,
} from "@/lib/delivery-fee-utils";
import {
  getTimePeriod,
  getAvailableLocations,
  isGarageOfficeLocation,
  GARAGE_OFFICE_LOCATION,
  type TimePeriod,
} from "@/lib/delivery-fee-constants";
import * as React from "react";

// Helper function for today's date
function getTodayDate(): string {
  const now = new Date();
  return now.toISOString().split("T")[0]; // YYYY-MM-DD format
}

export function BookingWidget() {
  const [dropOffLocation, setDropOffLocation] = React.useState("");
  const [pickUpLocation, setPickUpLocation] = React.useState("");
  const [pickUpDate, setPickUpDate] = React.useState("");
  const [pickUpTime, setPickUpTime] = React.useState("");
  const [dropOffDate, setDropOffDate] = React.useState("");
  const [dropOffTime, setDropOffTime] = React.useState("");
  const [sameLocation, setSameLocation] = React.useState(true);
  const [showPickupSuggestions, setShowPickupSuggestions] =
    React.useState(false);
  const [showDropoffSuggestions, setShowDropoffSuggestions] =
    React.useState(false);
  const router = useRouter();
  const { user } = useCustomerAuth();
  const [showAuthModal, setShowAuthModal] = React.useState(false);
  
  // Day/Night selection state
  const [pickupTimePeriod, setPickupTimePeriod] = React.useState<TimePeriod>("day");
  const [returnTimePeriod, setReturnTimePeriod] = React.useState<TimePeriod>("day");

  function swap() {
    const tempLocation = pickUpLocation;
    setPickUpLocation(dropOffLocation);
    setDropOffLocation(tempLocation);
  }

  function goSearch() {
    // Check if user is authenticated
    if (!user) {
      setShowAuthModal(true);
      return;
    }

    const pickUpDateTime = `${pickUpDate}T${pickUpTime}`;
    const dropOffDateTime = `${dropOffDate}T${dropOffTime}`;

    // Use pickup location as dropoff location if sameLocation is true
    const finalDropOffLocation = sameLocation
      ? pickUpLocation
      : dropOffLocation;

    const bookingUrl = buildBookingFlowUrl({
      pickUpLocation,
      dropOffLocation: finalDropOffLocation,
      pickUpDateTime,
      dropOffDateTime,
      pickupTimePeriod,
      returnTimePeriod,
      deliveryFee: deliveryFees?.totalFee || 0,
    });
    router.push(bookingUrl);
  }

  const getFilteredLocations = (query: string) => {
    // Include garage/office location and available delivery locations
    const deliveryLocations = [
      GARAGE_OFFICE_LOCATION.name,
      GARAGE_OFFICE_LOCATION.address,
      ...getAvailableLocations(),
    ];
    
    if (!query) {
      // Show garage/office first, then popular destinations
      return [GARAGE_OFFICE_LOCATION.address, ...POPULAR_DESTINATIONS.slice(0, 4)];
    }
    
    // Filter both delivery locations and popular destinations
    const allLocations = [...deliveryLocations, ...POPULAR_DESTINATIONS];
    return [...new Set(allLocations)].filter((location) =>
      location.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 6);
  };

  // Calculate delivery fees when locations/times change
  const deliveryFees = React.useMemo((): DeliveryFeeResult | null => {
    if (!pickUpLocation || !dropOffLocation || !pickUpTime || !dropOffTime) {
      return null;
    }
    
    const finalDropOffLocation = sameLocation ? pickUpLocation : dropOffLocation;
    
    return calculateDeliveryFees(
      pickUpLocation,
      finalDropOffLocation,
      pickUpTime,
      dropOffTime
    );
  }, [pickUpLocation, dropOffLocation, pickUpTime, dropOffTime, sameLocation]);

  // Handle auto-fill time when Day/Night buttons are clicked
  const handlePickupTimeAutoFill = (time: string) => {
    setPickUpTime(time);
  };

  const handleDropOffTimeAutoFill = (time: string) => {
    setDropOffTime(time);
  };

  // Auto-detect time period when time changes (only if not auto-filled)
  React.useEffect(() => {
    if (pickUpTime) {
      const detectedPeriod = getTimePeriod(pickUpTime);
      setPickupTimePeriod(detectedPeriod);
    }
  }, [pickUpTime]);

  React.useEffect(() => {
    if (dropOffTime) {
      const detectedPeriod = getTimePeriod(dropOffTime);
      setReturnTimePeriod(detectedPeriod);
    }
  }, [dropOffTime]);

  return (
    <Card className="shadow-lg border-0 bg-white">
      <CardHeader className="pb-6">
        <CardTitle className="text-xl font-semibold text-gray-900">
          Book Your Rental
        </CardTitle>
        <p className="text-sm text-gray-600 mt-2">
          Enter your details to find available cars
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Pick-up Location */}
        <div className="space-y-2">
          <Label
            htmlFor="pickup-location"
            className="text-sm font-medium text-gray-700"
          >
            Pick-up Location
          </Label>
          <div className="relative">
            <div className="flex items-center gap-3 border border-gray-300 rounded-lg px-4 py-3 bg-white focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
              <MapPin className="h-5 w-5 text-blue-600 flex-shrink-0" />
              <input
                id="pickup-location"
                className="w-full text-sm outline-none placeholder-gray-500"
                placeholder="Enter city or location"
                value={pickUpLocation}
                onChange={(e) => {
                  setPickUpLocation(e.target.value);
                  setShowPickupSuggestions(true);
                }}
                onFocus={() => setShowPickupSuggestions(true)}
                onBlur={() =>
                  setTimeout(() => setShowPickupSuggestions(false), 200)
                }
                aria-label="Pick-up location"
              />
            </div>
            {showPickupSuggestions && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto">
                {getFilteredLocations(pickUpLocation).map((location, idx) => (
                  <button
                    key={idx}
                    className="w-full text-left px-4 py-3 hover:bg-gray-50 text-sm border-b border-gray-100 last:border-b-0"
                    onClick={() => {
                      setPickUpLocation(location);
                      setShowPickupSuggestions(false);
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      {location}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Return to same location toggle */}
        <div className="flex items-center space-x-3">
          <Switch
            id="same-location"
            checked={sameLocation}
            onCheckedChange={setSameLocation}
            className="data-[state=checked]:bg-blue-600"
          />
          <Label htmlFor="same-location" className="text-sm text-gray-700">
            Return to same location
          </Label>
        </div>

        {/* Drop-off Location (conditional) */}
        {!sameLocation && (
          <div className="space-y-2">
            <Label
              htmlFor="dropoff-location"
              className="text-sm font-medium text-gray-700"
            >
              Drop-off Location
            </Label>
            <div className="relative">
              <div className="flex items-center gap-3 border border-gray-300 rounded-lg px-4 py-3 bg-white focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
                <MapPin className="h-5 w-5 text-blue-600 flex-shrink-0" />
                <input
                  id="dropoff-location"
                  className="w-full text-sm outline-none placeholder-gray-500"
                  placeholder="Enter drop-off location"
                  value={dropOffLocation}
                  onChange={(e) => {
                    setDropOffLocation(e.target.value);
                    setShowDropoffSuggestions(true);
                  }}
                  onFocus={() => setShowDropoffSuggestions(true)}
                  onBlur={() =>
                    setTimeout(() => setShowDropoffSuggestions(false), 200)
                  }
                  aria-label="Drop-off location"
                />
                <button
                  onClick={swap}
                  className="p-1 rounded-md hover:bg-gray-100 text-gray-500 hover:text-blue-600"
                  aria-label="Swap locations"
                  title="Swap locations"
                >
                  <ArrowLeftRight className="h-4 w-4" />
                </button>
              </div>
              {showDropoffSuggestions && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto">
                  {getFilteredLocations(dropOffLocation).map(
                    (location, idx) => (
                      <button
                        key={idx}
                        className="w-full text-left px-4 py-3 hover:bg-gray-50 text-sm border-b border-gray-100 last:border-b-0"
                        onClick={() => {
                          setDropOffLocation(location);
                          setShowDropoffSuggestions(false);
                        }}
                      >
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          {location}
                        </div>
                      </button>
                    )
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Date and Time Inputs */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Pick-up Date & Time */}
          <div className="space-y-4">
            <Label className="text-sm font-medium text-gray-700">
              Pick-up Date & Time
            </Label>
            <div className="grid grid-cols-2 gap-3">
              <DatePicker
                value={pickUpDate}
                onChange={setPickUpDate}
                placeholder="Select date"
                minDate={getTodayDate()}
                aria-label="Pick-up date"
                className="border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100"
              />
              <div className="space-y-2">
                <TimePicker
                  value={pickUpTime}
                  onChange={setPickUpTime}
                  placeholder="Select time"
                  minTime="06:00"
                  maxTime="22:00"
                  step={60}
                  showQuickActions={true}
                  showSecondaryFormat={false}
                  aria-label="Pick-up time"
                  className="border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100"
                />
              </div>
            </div>
            {/* Pickup Day/Night Selector with Auto-fill */}
            <div className="pt-2">
              <CompactDayNightSelector
                value={pickupTimePeriod}
                onChange={setPickupTimePeriod}
                onTimeAutoFill={handlePickupTimeAutoFill}
                currentTime={pickUpTime}
                enableAutoFill={true}
                className="justify-start"
              />
            </div>
          </div>

          {/* Drop-off Date & Time */}
          <div className="space-y-4">
            <Label className="text-sm font-medium text-gray-700">
              Drop-off Date & Time
            </Label>
            <div className="grid grid-cols-2 gap-3">
              <DatePicker
                value={dropOffDate}
                onChange={setDropOffDate}
                placeholder="Select date"
                minDate={getTodayDate()}
                aria-label="Drop-off date"
                className="border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100"
              />
              <div className="space-y-2">
                <TimePicker
                  value={dropOffTime}
                  onChange={setDropOffTime}
                  placeholder="Select time"
                  minTime="06:00"
                  maxTime="22:00"
                  step={60}
                  showQuickActions={true}
                  showSecondaryFormat={false}
                  aria-label="Drop-off time"
                  className="border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100"
                />
              </div>
            </div>
            {/* Return Day/Night Selector with Auto-fill */}
            <div className="pt-2">
              <CompactDayNightSelector
                value={returnTimePeriod}
                onChange={setReturnTimePeriod}
                onTimeAutoFill={handleDropOffTimeAutoFill}
                currentTime={dropOffTime}
                enableAutoFill={true}
                className="justify-start"
              />
            </div>
          </div>
        </div>

        {/* Delivery Fee Display */}
        {deliveryFees && deliveryFees.totalFee > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Calculator className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h4 className="font-medium text-blue-900 text-sm mb-1">
                  Delivery & Return Fees
                </h4>
                <p className="text-sm text-blue-700 mb-2">
                  {formatDeliveryFeeBreakdown(deliveryFees)}
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-blue-600">Total Delivery Fee:</span>
                  <span className="font-bold text-blue-900">
                    ₱{deliveryFees.totalFee.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Free Pickup/Return Notice */}
        {deliveryFees && deliveryFees.totalFee === 0 && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
              <p className="text-sm text-green-700">
                <strong>Free pickup & return</strong> - No delivery fees for garage/office location
              </p>
            </div>
          </div>
        )}

        {/* Search Button */}
        <div className="pt-4">
          <Button
            onClick={goSearch}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 text-base"
            size="lg"
          >
            Search Available Cars
          </Button>
        </div>
      </CardContent>

      {/* Authentication Modal */}
      <AuthRequiredModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        action="search for available cars"
      />
    </Card>
  );
}
