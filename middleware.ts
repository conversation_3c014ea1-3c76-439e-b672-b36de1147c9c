import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  // Special handling for GPS tracker routes with parameters
  const url = request.nextUrl.clone();

  // Check if this is a GPS tracker request with parameters
  // that should be forwarded to the API endpoint
  if (url.pathname === '/admin/tracker' || url.pathname === '/admin/tracker/') {
    const hasGpsParams = url.searchParams.has('gps') ||
                       (url.searchParams.has('lat') && url.searchParams.has('lon'));

    if (hasGpsParams) {
      // Make sure API gets called in the background, but let the page render normally
      // The page component will handle this on the client side
    }
  }

  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  // Handle admin routes with improved session handling
  const path = request.nextUrl.pathname
  const isAdminRoute = path.startsWith('/admin') && !path.startsWith('/admin-auth')

  if (isAdminRoute) {
    // For admin routes, try to validate session but don't redirect on failure
    // Let AdminProtection component handle redirects after proper session restoration
    try {
      const { createContextClient } = await import('@/lib/supabase/server')
      const adminSupabase = await createContextClient('admin')

      // Try to get user but don't fail if session isn't synced yet
      const { data: { user }, error } = await adminSupabase.auth.getUser()

      if (process.env.NODE_ENV === 'development') {
        console.log(`[Middleware] Admin route ${path}: user=${user?.email || 'none'}, error=${error?.message || 'none'}`)
      }

      // Always allow the request through - AdminProtection will handle auth
      return response
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Middleware] Admin auth check failed:`, error)
      }
      // Allow request through even if auth check fails
      return response
    }
  }

  // For non-admin routes, use the standard Supabase middleware approach
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          response = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            response.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // This will refresh session if expired - required for Server Components
  try {
    await supabase.auth.getUser()
  } catch (error) {
    // Log error but don't break middleware - allow request to continue
    console.error('Middleware auth error:', error)
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}