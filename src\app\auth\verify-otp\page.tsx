"use client";

import * as React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { useToast } from "@/hooks/use-toast";
import { CheckCircle, Mail, RefreshCw, ArrowLeft } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function VerifyOTPPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { verifyOTP, resendOTP } = useCustomerAuth();
  const { toast } = useToast();

  const [otp, setOtp] = React.useState("");
  const [email, setEmail] = React.useState("");
  const [isVerifying, setIsVerifying] = React.useState(false);
  const [isResending, setIsResending] = React.useState(false);
  const [isVerified, setIsVerified] = React.useState(false);

  React.useEffect(() => {
    const emailParam = searchParams.get("email");
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam));
    }
  }, [searchParams]);

  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isVerifying || !email || !otp) return;

    setIsVerifying(true);

    try {
      const { error } = await verifyOTP(email, otp, "signup");

      if (error) {
        toast({
          variant: "destructive",
          title: "Verification Failed",
          description: error.message,
        });
        return;
      }

      setIsVerified(true);
      toast({
        title: "Account Verified!",
        description: "Your account has been successfully verified.",
      });

      // Get stored user data and redirect to setup account page
      const pendingUserData = localStorage.getItem('pendingUserData');
      let redirectUrl = '/customer/setup-account';
      
      if (pendingUserData) {
        try {
          const userData = JSON.parse(pendingUserData);
          const params = new URLSearchParams({
            email: email,
            firstName: userData.firstName || '',
            lastName: userData.lastName || '',
            phone: userData.phone || ''
          });
          redirectUrl = `/customer/setup-account?${params.toString()}`;
          // Clear the stored data
          localStorage.removeItem('pendingUserData');
        } catch (error) {
          console.error('Error parsing pending user data:', error);
        }
      }

      // Redirect to setup account page after a short delay
      setTimeout(() => {
        router.replace(redirectUrl);
      }, 2000);
    } catch (err) {
      toast({
        variant: "destructive",
        title: "Verification Failed",
        description: "An unexpected error occurred. Please try again.",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendOTP = async () => {
    if (isResending || !email) return;

    setIsResending(true);

    try {
      const { error } = await resendOTP(email, "signup");

      if (error) {
        toast({
          variant: "destructive",
          title: "Failed to Resend Code",
          description: error.message,
        });
        return;
      }

      toast({
        title: "Verification Code Sent",
        description: "A new verification code has been sent to your email.",
      });
    } catch (err) {
      toast({
        variant: "destructive",
        title: "Failed to Resend Code",
        description: "An unexpected error occurred. Please try again.",
      });
    } finally {
      setIsResending(false);
    }
  };

  if (isVerified) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        {/* Back to Homepage Button */}
        <div className="fixed top-4 left-4 z-10">
          <Link href="/">
            <Button
              variant="secondary"
              size="sm"
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Homepage
            </Button>
          </Link>
        </div>

        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Link href="/" className="cursor-pointer">
                <Image
                  src="/ollie_logo.jpg"
                  alt="Ollie's Rent A Car"
                  width={60}
                  height={60}
                  className="hover:opacity-80 transition-opacity"
                  priority
                />
              </Link>
            </div>
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <CardTitle className="text-green-600">Account Verified!</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">
              Your account has been successfully verified. You will be
              redirected to the login page shortly.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      {/* Back to Homepage Button */}
      <div className="fixed top-4 left-4 z-10">
        <Link href="/">
          <Button
            variant="secondary"
            size="sm"
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Homepage
          </Button>
        </Link>
      </div>

      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Link href="/" className="cursor-pointer">
              <Image
                src="/ollie_logo.jpg"
                alt="Ollie's Rent A Car"
                width={60}
                height={60}
                className="hover:opacity-80 transition-opacity"
                priority
              />
            </Link>
          </div>
          <div className="flex justify-center mb-4">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full">
              <Mail className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <CardTitle>Verify Your Account</CardTitle>
          <p className="text-sm text-gray-600 mt-2">
            We've sent a 6-digit verification code to:
          </p>
          <p className="text-sm font-medium text-gray-900 mt-1">{email}</p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleVerifyOTP} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="otp">Verification Code</Label>
              <Input
                id="otp"
                type="text"
                placeholder="Enter 6-digit code"
                value={otp}
                onChange={(e) =>
                  setOtp(e.target.value.replace(/\D/g, "").slice(0, 6))
                }
                maxLength={6}
                className="text-center text-lg tracking-widest"
                required
              />
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={isVerifying || otp.length !== 6}
            >
              {isVerifying ? "Verifying..." : "Verify Account"}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600 mb-2">
              Didn't receive the code?
            </p>
            <Button
              variant="secondary"
              size="sm"
              onClick={handleResendOTP}
              disabled={isResending}
              className="text-sm"
            >
              {isResending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                "Resend Code"
              )}
            </Button>
          </div>

          <div className="mt-4 text-center">
            <Button
              variant="link"
              size="sm"
              onClick={() => router.replace("/customer/login")}
              className="text-sm text-gray-500"
            >
              Back to Login
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
