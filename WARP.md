# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Project Overview

PathLink is a comprehensive car rental management system called "Ollie Track" that provides both customer-facing booking capabilities and admin management tools. The system includes real-time GPS tracking of rental vehicles, payment processing, and a complete booking workflow.

**Key Features:**
- Customer booking flow with vehicle selection, scheduling, and payment
- Admin dashboard for managing cars, bookings, payments, and GPS tracking
- Real-time GPS tracking via MQTT and ESP32 devices  
- Document verification system for customer onboarding
- AI-powered chatbot for customer support
- Multi-role authentication (customer, admin, super_admin)

## Technology Stack

- **Frontend**: Next.js 15 (App Router), React 19, TypeScript
- **Styling**: Tailwind CSS 4, shadcn/ui components
- **Database**: Supabase (PostgreSQL) with Row Level Security (RLS)
- **Authentication**: Supabase Auth with role-based access control
- **Real-time**: Supabase subscriptions, MQTT for GPS data
- **Maps**: Leaflet with React-Leaflet for location visualization
- **Forms**: React Hook Form + Zod validation
- **Testing**: Jest with Testing Library
- **IoT**: ESP32-based GPS trackers with cellular connectivity

## Development Commands

### Core Development
```bash
# Start development server
pnpm dev

# Build for production
pnpm build

# Start production server  
pnpm start

# Run linter
pnpm lint
```

### GPS/MQTT Services
```bash
# Start development with MQTT consumer
pnpm dev:with-mqtt

# Run MQTT consumer standalone
pnpm mqtt:consumer

# Debug MQTT traffic
pnpm mqtt:debug

# Detect ESP32 devices
pnpm mqtt:detect-devices
```

### Testing
```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage

# Run specific test suites
pnpm test:auth
```

### Performance Analysis
```bash
# Analyze bundle size
pnpm build:analyze
```

## Architecture Overview

### App Router Structure
The application uses Next.js 15 App Router with feature-based organization:

```
src/app/
├── admin/              # Admin dashboard and management
│   ├── bookings/      # Booking management
│   ├── cars/          # Vehicle inventory
│   ├── payments/      # Payment processing
│   ├── tracker/       # GPS tracking interface
│   └── accounts/      # User management
├── customer/          # Customer-facing pages  
│   ├── booking/       # Booking flow
│   ├── dashboard/     # Customer dashboard
│   └── catalog/       # Vehicle browsing
├── admin-auth/        # Admin authentication
├── api/              # API routes
│   ├── gps/          # GPS data endpoints
│   ├── bookings/     # Booking operations
│   └── chat/         # AI chatbot
└── auth/             # Authentication callbacks
```

### Component Architecture
Components are organized by domain and feature:

```
src/components/
├── admin/            # Admin-specific components
├── auth/             # Authentication forms and contexts
├── customer-side/    # Customer booking flow
├── ui/               # shadcn/ui base components
├── nav/              # Navigation components
└── layout/           # Layout components
```

### Database Schema (Key Tables)
- **`cars`**: Vehicle inventory with GPS tracking fields
- **`bookings`**: Customer reservations with lifecycle management  
- **`profiles`**: User accounts with role-based permissions
- **`payments`**: Transaction records and verification status
- **`gps_data`**: Real-time vehicle location and telemetry
- **`gps_devices`**: GPS device registration and configuration

### Authentication Flow
- JWT-based authentication via Supabase Auth
- Role-based access control (customer, admin, super_admin)
- Row Level Security (RLS) policies for data isolation
- Separate auth contexts for admin and customer flows

## Key Business Logic

### GPS Tracking System
- ESP32 devices send encrypted GPS data via MQTT
- Real-time data ingestion through `/api/gps/` endpoints
- Live tracking visualization using Leaflet maps
- Device management through admin dashboard

### Booking Workflow
1. **Vehicle Selection**: Browse available cars with filtering
2. **Schedule Selection**: Pick-up/drop-off dates and locations
3. **Personal Information**: Customer details and verification
4. **Payment**: Support for multiple payment methods
5. **Document Upload**: Required documents with verification
6. **Confirmation**: Booking confirmation and tracking

### Payment Processing
- Multiple payment methods (GCash, PayMaya, Cash, Card)
- Proof of payment upload and verification
- Admin approval workflow
- Payment status tracking and notifications

## Configuration Files

### Environment Variables
Key environment variables (see `ENV_SETUP.md`):
- `NEXT_PUBLIC_SUPABASE_URL`: Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key for admin operations
- `INBOUND_DEVICE_TOKEN`: Authentication token for GPS devices
- `OPENAI_API_KEY`: For AI chatbot functionality

### TypeScript Configuration
- Strict mode enabled with comprehensive type definitions
- Path aliases configured (`@/*` maps to `src/*`)
- Next.js plugin integrated for App Router support

### Tailwind CSS
- Custom design system with shadcn/ui integration
- Custom color scheme and component variants
- Responsive breakpoints including mobile-first approach

## Development Patterns

### Component Patterns
- **Server Components**: Default for App Router pages
- **Client Components**: For interactive UI with "use client"
- **Provider Pattern**: Authentication and theme contexts
- **Hook Pattern**: Custom hooks for data fetching and state

### Data Fetching
- Supabase client for database operations
- Real-time subscriptions for live data updates
- Server actions for form submissions and mutations
- Custom hooks for component-level data management

### Form Handling
- React Hook Form with Zod validation schemas
- Type-safe form validation and error handling
- Multi-step form wizards for complex flows
- File upload with progress tracking

### Error Handling
- Centralized error boundaries
- Toast notifications for user feedback
- Comprehensive logging for debugging
- Graceful fallbacks for failed operations

## Testing Strategy

### Test Structure
- Unit tests for utility functions and hooks
- Component tests using React Testing Library
- Integration tests for API endpoints
- Authentication flow testing

### Key Test Files
- `__tests__/auth/`: Authentication-related tests
- `__tests__/document-verification-api.test.ts`: Document processing tests
- `__tests__/booking-flow-refactor.test.ts`: Booking workflow tests

## IoT and GPS Integration

### Hardware Components
- ESP32-based GPS tracking devices
- Cellular connectivity for remote tracking
- MQTT broker for real-time data transmission
- Battery-powered with power management

### Data Pipeline
1. ESP32 devices collect GPS coordinates and telemetry
2. Data transmitted via MQTT to cloud broker
3. MQTT consumer service processes incoming data
4. Data stored in Supabase with real-time subscriptions
5. Admin dashboard displays live vehicle locations

### Device Management
- Device registration and configuration via admin panel
- Remote monitoring of device status and connectivity
- Firmware update capabilities
- Battery level and signal strength monitoring

## Deployment Considerations

### Production Environment
- Vercel deployment for frontend application
- Supabase for database and authentication
- Environment variables properly configured
- GPS data endpoints secured with authentication tokens

### Performance Optimization
- Code splitting and lazy loading implemented
- Bundle analysis tools configured
- Image optimization with Next.js Image component
- Caching strategies for frequently accessed data

### Security Measures
- Row Level Security (RLS) policies implemented
- API endpoints protected with authentication
- GPS device communications encrypted
- Sensitive data properly sanitized

## Important Files to Review

### Core Configuration
- `package.json`: Dependencies and scripts
- `next.config.mjs`: Next.js configuration
- `tailwind.config.js`: Styling configuration
- `tsconfig.json`: TypeScript settings

### Key Business Logic
- `src/lib/types.ts`: TypeScript definitions for all data models
- `src/lib/supabase/database.ts`: Database query functions
- `src/services/mqtt-gps-consumer.ts`: MQTT data processing
- `src/components/customer-side/booking/flow/`: Booking workflow components

### Documentation
- `README.md`: Project overview and setup instructions
- `BYTEROVER.md`: Comprehensive system architecture documentation
- `ENV_SETUP.md`: Environment variable configuration
- `docs/`: Detailed implementation guides and setup instructions
