# Car Management System

This system provides full CRUD (Create, Read, Update, Delete) functionality for managing cars in the OllieTrack car rental application. It uses Supabase as the database backend and provides server actions for interacting with the database.

## Features

1. **List Active Cars**: View all cars that are currently active (not archived)
2. **List Archived Cars**: View all cars that have been archived
3. **Add New Car**: Add a new car to the inventory with details like model, type, status, etc.
4. **Edit Car**: Update an existing car's information
5. **Archive Car**: Soft-delete a car (mark it as archived)
6. **Restore Car**: Restore an archived car to active status
7. **Delete Car**: Permanently remove a car from the database

## Implementation Details

### Database Connection

All interactions with the Supabase database are handled through server actions in `/app/admin/cars/actions/car-actions.ts`. These functions are:

- `listCars()`: Fetches all active cars
- `listArchivedCars()`: Fetches all archived cars
- `addCarAction()`: Adds a new car
- `updateCarAction()`: Updates an existing car
- `archiveCarAction()`: Archives a car (soft delete)
- `unarchiveCarAction()`: Restores an archived car
- `deleteCarAction()`: Permanently deletes a car

### User Interface

The UI is implemented in `/app/admin/cars/page.tsx` and provides:

- A tabbed interface to switch between active and archived cars
- A data table showing car details
- Action buttons for editing, archiving, restoring, and deleting cars
- A form dialog for adding or editing cars
- Responsive design with different layouts for desktop and mobile

## Usage

1. Navigate to the Cars Management page via the admin dashboard
2. Use the tabs to switch between active and archived cars
3. Click "Add Car" to create a new car entry
4. Use the action buttons to edit, archive, restore, or delete cars

## Security

All database operations are protected by Supabase's Row Level Security (RLS) policies that ensure only authenticated admin users can perform these actions.
