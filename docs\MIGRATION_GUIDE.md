# Migration Guide: From Mock Store to Supabase

This guide explains how to migrate the existing codebase from using the mock store (`lib/store.ts`) to the new Supabase database.

## Overview

The codebase currently uses a mock in-memory store for data operations. With Supabase integration, we're moving to a real database with proper authentication and Row Level Security.

## Key Changes

### 1. Data Structure Changes

**Old (Mock Store):**
```typescript
// Snake_case properties were camelCase
interface Car {
  plateNumber: string
  imageURL: string
  isArchived?: boolean
  pricePerDay: number
}
```

**New (Supabase):**
```typescript
// Database uses snake_case
interface Car {
  plate_number: string
  image_url?: string
  is_archived: boolean
  price_per_day: number
}
```

### 2. Authentication Changes

**Old:**
- Mock users with hard-coded credentials
- localStorage-based sessions
- Role stored in mock user object

**New:**
- Supabase Auth with real authentication
- Server-side session management
- Role stored in user metadata and profiles table

### 3. Database Operations

**Old:**
```typescript
// Mock operations
import { getCar, createBooking, listCars } from '@/lib/store'
```

**New:**
```typescript
// Supabase operations
import { db } from '@/lib/supabase/database'

// Client-side
const car = await db.getCar(id)
const booking = await db.createBooking(bookingData)
const cars = await db.getCars()
```

## Migration Steps

### Step 1: Update Authentication Context

The `local-auth-context.tsx` should be replaced with `supabase-auth-context.tsx` that's already implemented. Update components to use the Supabase auth context:

```typescript
// Old
import { useAuth } from '@/components/auth/local-auth-context'

// New
import { useSupabaseAuth } from '@/components/auth/supabase-auth-context'
```

### Step 2: Update Data Fetching

Replace mock store calls with Supabase database calls:

**Customer Components:**
```typescript
// Old: lib/store.ts functions
const cars = availableCars(from, to)
const bookings = listBookingsByUser(userId)

// New: Supabase client
const cars = await db.getAvailableCars(from, to)
const bookings = await db.getMyBookings()
```

**Admin Components:**
```typescript
// Old: lib/store.ts functions
const allBookings = listAllBookings()
const allCars = listCars()

// New: Server-side Supabase
import { serverDb } from '@/lib/supabase/database'
const allBookings = await serverDb.getAllBookings()
const allCars = await db.getCars()
```

### Step 3: Update Server Actions

Update `app/actions.ts` to use Supabase instead of mock store:

```typescript
// Old
import { createBooking, addCar } from '@/lib/store'

// New
import { db, serverDb } from '@/lib/supabase/database'

export async function confirmBooking(input: CreateBookingRequest) {
  const booking = await db.createBooking(input)
  // ... handle response
}
```

### Step 4: Update Property Names

Update all components to use the new snake_case property names:

```typescript
// Update these property mappings:
plateNumber -> plate_number
imageURL -> image_url
isArchived -> is_archived
pricePerDay -> price_per_day
customerId -> customer_id
carId -> car_id
pickUpLocation -> pickup_location
dropOffLocation -> dropoff_location
pickUpDateTime -> pickup_datetime
dropOffDateTime -> dropoff_datetime
specialRequests -> special_requests
totalAmount -> total_amount
bookingId -> booking_id
transactionDate -> transaction_date
```

### Step 5: Update Type Imports

Update imports to use the new type definitions:

```typescript
// Old
import { Car, Booking, User, Payment } from '@/lib/types'

// New (some types renamed)
import { Car, Booking, Profile, Payment } from '@/lib/types'

// User interface is now Profile, but User extends Profile for compatibility
```

## Component-by-Component Migration

### 1. Admin Dashboard Components

**Files to update:**
- `components/admin/booking-tracker.tsx`
- `app/admin/bookings/page.tsx`
- `app/admin/cars/page.tsx`
- `app/admin/payments/page.tsx`

**Changes needed:**
- Replace store imports with Supabase database imports
- Update property names to snake_case
- Use server-side operations for admin data fetching
- Add proper loading states and error handling

### 2. Customer Components

**Files to update:**
- `app/customer/dashboard/page.tsx`
- `app/customer/booking/[carId]/page.tsx`
- `app/customer/catalog/page.tsx`

**Changes needed:**
- Replace store imports with Supabase client operations
- Update property names
- Add authentication checks
- Implement proper loading and error states

### 3. Authentication Components

**Files to update:**
- `app/customer/login/page.tsx`
- `app/customer/signup/page.tsx`
- `app/admin-auth/page.tsx`

**Changes needed:**
- Replace local auth with Supabase auth
- Update auth context usage
- Handle Supabase auth errors properly

### 4. Middleware Updates

**File:** `middleware.ts`

**Changes needed:**
- Already uses Supabase auth
- Ensure role checking works with new profile structure
- Update route protection logic if needed

## Testing the Migration

### 1. Set Up Test Data

Run the schema with sample data:
```sql
-- The schema includes sample cars
-- Add test bookings and payments as needed
```

### 2. Test User Flows

**Customer Flow:**
1. Sign up new customer account
2. Browse available cars
3. Create a booking
4. View booking history
5. Cancel a booking

**Admin Flow:**
1. Log in with admin account
2. View all bookings
3. Add/edit cars
4. Process payments
5. View GPS tracking (if implemented)

### 3. Verify Data Consistency

- Check that all CRUD operations work
- Verify RLS policies are enforced
- Test real-time updates
- Confirm GPS tracking works

## Rollback Plan

If issues arise during migration:

1. **Temporary Fallback:** Keep the mock store as a backup
2. **Environment Flag:** Use environment variable to switch between mock and Supabase
3. **Gradual Migration:** Migrate component by component rather than all at once

```typescript
// Example fallback implementation
const USE_SUPABASE = process.env.NEXT_PUBLIC_USE_SUPABASE === 'true'

export const dataService = USE_SUPABASE ? db : mockStore
```

## Performance Considerations

### 1. Caching

- Implement React Query or SWR for client-side caching
- Use Supabase's built-in caching for frequently accessed data
- Consider implementing Redis for session caching in production

### 2. Database Optimization

- Monitor query performance in Supabase dashboard
- Add indexes for commonly filtered columns
- Use database functions for complex operations

### 3. Real-time Features

- Use Supabase Realtime sparingly to avoid unnecessary re-renders
- Implement proper connection management
- Consider using polling for less critical updates

## Security Checklist

After migration, verify:

- [ ] RLS policies are working correctly
- [ ] Customers can only see their own data
- [ ] Admins have appropriate access levels
- [ ] Authentication is properly implemented
- [ ] Input validation is in place
- [ ] Environment variables are secure

## Deployment

### Development
1. Set up Supabase project
2. Run database schema
3. Update environment variables
4. Test all functionality

### Production
1. Create production Supabase project
2. Run schema and add real data
3. Set up proper backup strategy
4. Configure monitoring and alerts
5. Test thoroughly before going live

## Support

If you encounter issues during migration:

1. Check the Supabase dashboard for errors
2. Review the database logs
3. Verify RLS policies are correct
4. Ensure environment variables are set properly
5. Test authentication flows thoroughly

The migration should be straightforward since the new Supabase schema closely matches the existing mock store structure.
