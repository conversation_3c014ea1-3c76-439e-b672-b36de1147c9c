-- Add reference ID fields for payments and bookings
-- This creates human-readable reference IDs like PMT-20231025-7891 and BKG-20231025-7891

-- Add payment_ref column to payments table
ALTER TABLE public.payments 
ADD COLUMN IF NOT EXISTS payment_ref TEXT UNIQUE;

-- Add booking_ref column to bookings table
ALTER TABLE public.bookings 
ADD COLUMN IF NOT EXISTS booking_ref TEXT UNIQUE;

-- Create index for payment_ref for fast lookups
CREATE INDEX IF NOT EXISTS idx_payments_payment_ref ON public.payments USING btree (payment_ref);

-- Create index for booking_ref for fast lookups
CREATE INDEX IF NOT EXISTS idx_bookings_booking_ref ON public.bookings USING btree (booking_ref);

-- Function to generate payment reference ID
CREATE OR REPLACE FUNCTION generate_payment_ref() RETURNS TEXT AS $$
DECLARE
    date_part TEXT;
    random_part TEXT;
    counter INTEGER;
    ref_id TEXT;
BEGIN
    -- Get current date in YYYYMMDD format
    date_part := TO_CHAR(NOW(), 'YYYYMMDD');
    
    -- Try to generate a unique reference ID
    FOR counter IN 1..1000 LOOP
        -- Generate 4-digit random number
        random_part := LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
        ref_id := 'PMT-' || date_part || '-' || random_part;
        
        -- Check if this reference already exists
        IF NOT EXISTS (SELECT 1 FROM payments WHERE payment_ref = ref_id) THEN
            RETURN ref_id;
        END IF;
    END LOOP;
    
    -- Fallback: use timestamp if we couldn't generate unique ID
    RETURN 'PMT-' || date_part || '-' || EXTRACT(EPOCH FROM NOW())::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- Function to generate booking reference ID
CREATE OR REPLACE FUNCTION generate_booking_ref() RETURNS TEXT AS $$
DECLARE
    date_part TEXT;
    random_part TEXT;
    counter INTEGER;
    ref_id TEXT;
BEGIN
    -- Get current date in YYYYMMDD format
    date_part := TO_CHAR(NOW(), 'YYYYMMDD');
    
    -- Try to generate a unique reference ID
    FOR counter IN 1..1000 LOOP
        -- Generate 4-digit random number
        random_part := LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
        ref_id := 'BKG-' || date_part || '-' || random_part;
        
        -- Check if this reference already exists
        IF NOT EXISTS (SELECT 1 FROM bookings WHERE booking_ref = ref_id) THEN
            RETURN ref_id;
        END IF;
    END LOOP;
    
    -- Fallback: use timestamp if we couldn't generate unique ID
    RETURN 'BKG-' || date_part || '-' || EXTRACT(EPOCH FROM NOW())::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to auto-generate payment reference on insert
CREATE OR REPLACE FUNCTION set_payment_ref() RETURNS TRIGGER AS $$
BEGIN
    IF NEW.payment_ref IS NULL THEN
        NEW.payment_ref := generate_payment_ref();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function to auto-generate booking reference on insert
CREATE OR REPLACE FUNCTION set_booking_ref() RETURNS TRIGGER AS $$
BEGIN
    IF NEW.booking_ref IS NULL THEN
        NEW.booking_ref := generate_booking_ref();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers (only if they don't exist)
DO $$
BEGIN
    -- Create payment trigger if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'trigger_set_payment_ref' 
        AND tgrelid = 'public.payments'::regclass
    ) THEN
        CREATE TRIGGER trigger_set_payment_ref
            BEFORE INSERT ON public.payments
            FOR EACH ROW
            EXECUTE FUNCTION set_payment_ref();
    END IF;

    -- Create booking trigger if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'trigger_set_booking_ref' 
        AND tgrelid = 'public.bookings'::regclass
    ) THEN
        CREATE TRIGGER trigger_set_booking_ref
            BEFORE INSERT ON public.bookings
            FOR EACH ROW
            EXECUTE FUNCTION set_booking_ref();
    END IF;
END
$$;

-- Backfill existing records with reference IDs using a different approach
DO $$
DECLARE
    payment_record RECORD;
    booking_record RECORD;
    counter INTEGER;
BEGIN
    -- Backfill payments
    counter := 1;
    FOR payment_record IN 
        SELECT id, created_at FROM public.payments 
        WHERE payment_ref IS NULL 
        ORDER BY created_at
    LOOP
        UPDATE public.payments 
        SET payment_ref = 'PMT-' || TO_CHAR(payment_record.created_at, 'YYYYMMDD') || '-' || LPAD(counter::TEXT, 4, '0')
        WHERE id = payment_record.id;
        counter := counter + 1;
    END LOOP;
    
    -- Backfill bookings
    counter := 1;
    FOR booking_record IN 
        SELECT id, created_at FROM public.bookings 
        WHERE booking_ref IS NULL 
        ORDER BY created_at
    LOOP
        UPDATE public.bookings 
        SET booking_ref = 'BKG-' || TO_CHAR(booking_record.created_at, 'YYYYMMDD') || '-' || LPAD(counter::TEXT, 4, '0')
        WHERE id = booking_record.id;
        counter := counter + 1;
    END LOOP;
END
$$;

-- Add constraints to ensure reference IDs are always set
ALTER TABLE public.payments 
ALTER COLUMN payment_ref SET NOT NULL;

ALTER TABLE public.bookings 
ALTER COLUMN booking_ref SET NOT NULL;
