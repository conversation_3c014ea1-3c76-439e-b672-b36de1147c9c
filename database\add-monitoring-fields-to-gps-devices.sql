-- ============================================
-- NON-DESTRUCTIVE: ADD MONITORING FIELDS TO GPS DEVICE MAPPING
-- ============================================
-- Add monitoring and debugging fields for GPS device status tracking
-- This script is completely safe and non-destructive

-- Add monitoring columns to gps_device_mapping table (safe)
ALTER TABLE public.gps_device_mapping 
ADD COLUMN IF NOT EXISTS last_seen timestamp with time zone,
ADD COLUMN IF NOT EXISTS connection_status text DEFAULT 'offline',
ADD COLUMN IF NOT EXISTS last_error text,
ADD COLUMN IF NOT EXISTS total_messages_sent integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_gps_latitude double precision,
ADD COLUMN IF NOT EXISTS last_gps_longitude double precision,
ADD COLUMN IF NOT EXISTS last_gps_accuracy double precision,
ADD COLUMN IF NOT EXISTS firmware_version text;

-- Add constraints for monitoring fields (safe)
DO $$
BEGIN
  -- Add check constraint for connection_status if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints 
    WHERE constraint_name = 'gps_device_mapping_connection_status_check'
  ) THEN
    ALTER TABLE public.gps_device_mapping 
    ADD CONSTRAINT gps_device_mapping_connection_status_check 
    CHECK (connection_status = ANY (ARRAY['online'::text, 'offline'::text, 'connecting'::text, 'error'::text]));
  END IF;
END $$;

-- Create indexes for monitoring fields (safe)
CREATE INDEX IF NOT EXISTS idx_gps_device_mapping_last_seen ON public.gps_device_mapping USING btree (last_seen);
CREATE INDEX IF NOT EXISTS idx_gps_device_mapping_connection_status ON public.gps_device_mapping USING btree (connection_status);

-- Create new function with monitoring fields (doesn't affect existing function)
CREATE OR REPLACE FUNCTION public.get_gps_devices_with_monitoring()
RETURNS TABLE (
  id uuid,
  device_id text,
  car_id uuid,
  device_name text,
  device_type text,
  is_active boolean,
  mqtt_broker_host text,
  mqtt_broker_port integer,
  mqtt_client_id text,
  mqtt_pub_topic text,
  mqtt_sub_topic text,
  mqtt_username text,
  mqtt_password text,
  last_seen timestamp with time zone,
  connection_status text,
  last_error text,
  total_messages_sent integer,
  last_gps_latitude double precision,
  last_gps_longitude double precision,
  last_gps_accuracy double precision,
  firmware_version text,
  car_model text,
  car_plate text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    gdm.id,
    gdm.device_id,
    gdm.car_id,
    gdm.device_name,
    gdm.device_type,
    gdm.is_active,
    gdm.mqtt_broker_host,
    gdm.mqtt_broker_port,
    gdm.mqtt_client_id,
    gdm.mqtt_pub_topic,
    gdm.mqtt_sub_topic,
    gdm.mqtt_username,
    gdm.mqtt_password,
    gdm.last_seen,
    gdm.connection_status,
    gdm.last_error,
    gdm.total_messages_sent,
    gdm.last_gps_latitude,
    gdm.last_gps_longitude,
    gdm.last_gps_accuracy,
    gdm.firmware_version,
    c.model as car_model,
    c.plate_number as car_plate,
    gdm.created_at,
    gdm.updated_at
  FROM public.gps_device_mapping gdm
  LEFT JOIN public.cars c ON gdm.car_id = c.id
  ORDER BY gdm.created_at DESC;
END;
$$;

-- Create function to update device monitoring data
CREATE OR REPLACE FUNCTION public.update_device_status(
  device_id_param text,
  status_param text DEFAULT NULL,
  error_param text DEFAULT NULL,
  latitude_param double precision DEFAULT NULL,
  longitude_param double precision DEFAULT NULL,
  accuracy_param double precision DEFAULT NULL,
  increment_messages boolean DEFAULT false,
  firmware_param text DEFAULT NULL
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  UPDATE public.gps_device_mapping
  SET 
    last_seen = now(),
    connection_status = COALESCE(status_param, connection_status),
    last_error = CASE WHEN error_param IS NOT NULL THEN error_param ELSE last_error END,
    last_gps_latitude = COALESCE(latitude_param, last_gps_latitude),
    last_gps_longitude = COALESCE(longitude_param, last_gps_longitude),
    last_gps_accuracy = COALESCE(accuracy_param, last_gps_accuracy),
    total_messages_sent = CASE WHEN increment_messages THEN COALESCE(total_messages_sent, 0) + 1 ELSE total_messages_sent END,
    firmware_version = COALESCE(firmware_param, firmware_version),
    updated_at = now()
  WHERE device_id = device_id_param;
  
  RETURN FOUND;
END;
$$;

-- Grant permissions to the new functions
GRANT EXECUTE ON FUNCTION public.get_gps_devices_with_monitoring() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_gps_devices_with_monitoring() TO service_role;
GRANT EXECUTE ON FUNCTION public.update_device_status(text, text, text, double precision, double precision, double precision, boolean, text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_device_status(text, text, text, double precision, double precision, double precision, boolean, text) TO service_role;

-- Verify the schema changes
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'gps_device_mapping' 
AND (column_name LIKE '%last_%' OR column_name LIKE '%connection_%' OR column_name LIKE '%total_%' OR column_name = 'firmware_version')
ORDER BY ordinal_position;

-- Add comments for documentation
COMMENT ON COLUMN public.gps_device_mapping.last_seen IS 'Timestamp of last successful communication from device';
COMMENT ON COLUMN public.gps_device_mapping.connection_status IS 'Current connection status (online, offline, connecting, error)';
COMMENT ON COLUMN public.gps_device_mapping.last_error IS 'Last error message from device or system';
COMMENT ON COLUMN public.gps_device_mapping.total_messages_sent IS 'Total count of successful GPS messages sent by device';
COMMENT ON COLUMN public.gps_device_mapping.last_gps_latitude IS 'Last known GPS latitude coordinate from device';
COMMENT ON COLUMN public.gps_device_mapping.last_gps_longitude IS 'Last known GPS longitude coordinate from device';
COMMENT ON COLUMN public.gps_device_mapping.last_gps_accuracy IS 'Last known GPS accuracy in meters';
COMMENT ON COLUMN public.gps_device_mapping.firmware_version IS 'Current firmware version reported by device';
