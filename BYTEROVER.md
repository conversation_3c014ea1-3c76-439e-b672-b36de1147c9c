# PathLink Byterover Handbook

## Layer 1: System Overview

### Purpose
PathLink is a comprehensive car rental management system that enables customers to browse and book rental cars while providing administrators with tools to manage inventory, bookings, payments, and real-time GPS tracking of vehicles.

### Tech Stack
- **Frontend**: Next.js 15 (App Router), React 19, TypeScript
- **Styling**: Tailwind CSS 4, shadcn/ui components
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **UI Components**: Radix UI primitives
- **Forms**: React Hook Form + Zod validation
- **Maps**: Leaflet with React-Leaflet
- **Icons**: Lucide React
- **State Management**: React hooks + Context API
- **Testing**: Jest with Testing Library

### Architecture
- **App Router Structure**: Feature-based organization with separate admin and customer routes
- **Component Architecture**: Modular components organized by domain (admin, auth, customer-side)
- **Database Layer**: Supabase client with Row Level Security (RLS)
- **Real-time Features**: Supabase subscriptions for live data updates
- **Authentication Flow**: Role-based access (customer, admin, super_admin)

## Layer 2: Module Map

### Core Modules

#### `src/app/` - Next.js App Router Pages
- **`admin/`**: Admin dashboard, cars, bookings, payments, GPS tracking
- **`(customer)/`**: Customer-facing booking flow and dashboard  
- **`api/`**: Server-side API routes for GPS data, auth, and integrations
- **`admin-auth/`**: Admin authentication pages

#### `src/components/` - UI Components
- **`admin/`**: Admin-specific components (dashboards, management interfaces)
- **`auth/`**: Authentication forms and context providers
- **`customer-side/`**: Customer booking flow and dashboard components
- **`ui/`**: Base shadcn/ui components and primitives
- **`nav/`**: Navigation components (sidebar, breadcrumbs)

#### `src/lib/` - Business Logic & Utilities
- **`auth/`**: Authentication utilities and context management
- **`supabase/`**: Database client, queries, and real-time subscriptions
- **`data-ingestion/`**: GPS data processing and ingestion pipelines
- **`validation/`**: Zod schemas for form and API validation

#### `src/hooks/` - Custom React Hooks
- **State management**: Form validation, document notifications
- **Data fetching**: Car preloading, real-time subscriptions
- **UI interactions**: Modal states, responsive behaviors

## Layer 3: Integration Guide

### Database Schema (Supabase)
- **`cars`**: Vehicle inventory with status, availability, and specifications
- **`bookings`**: Customer reservations with lifecycle management
- **`profiles`**: User accounts with role-based permissions (customer, admin, super_admin)
- **`payments`**: Transaction records and payment status tracking
- **`gps_data`**: Real-time vehicle location and telemetry data
- **`gps_devices`**: GPS device registration and management

### API Endpoints
- **`/api/gps/`**: GPS data ingestion, tracking, and device management
- **Authentication**: Supabase Auth integration with role verification
- **Real-time**: WebSocket connections for live tracking updates

### External Integrations
- **GPS Hardware**: ESP32-based tracking devices with cellular connectivity
- **Maps**: Leaflet/OpenStreetMap for location visualization
- **Email**: Supabase Edge Functions for notifications

### Key Configurations
- **RLS Policies**: Row-level security for multi-tenant data isolation
- **Authentication Flow**: JWT-based with automatic session management
- **Real-time Subscriptions**: Live updates for bookings, GPS data, and system status

## Layer 4: Extension Points

### Design Patterns
- **Provider Pattern**: Authentication and theme contexts
- **Hook Pattern**: Custom hooks for data fetching and state management
- **Component Composition**: shadcn/ui component library integration
- **Server Components**: Next.js App Router with RSC optimization

### Customization Areas
- **GPS Device Integration**: Extensible device registration and data ingestion
- **Payment Processing**: Modular payment adapter interfaces
- **Notification System**: Email and SMS notification pipelines
- **Admin Dashboard**: Widget-based dashboard with configurable layouts
- **Booking Flow**: Multi-step form wizard with validation checkpoints

### Configuration Points
- **Database Policies**: RLS rules for access control customization
- **API Rate Limiting**: Configurable limits for GPS data ingestion
- **UI Themes**: Tailwind CSS configuration with design tokens
- **Form Validation**: Zod schema composition for business rules
- **Real-time Features**: Supabase subscription configuration

### Development Patterns
- **Component Testing**: Jest + Testing Library setup for UI components
- **API Testing**: Integration tests for GPS and booking endpoints
- **Type Safety**: TypeScript strict mode with comprehensive type definitions
- **Error Handling**: Centralized error boundaries and logging
- **Performance**: Lazy loading, code splitting, and caching strategies
