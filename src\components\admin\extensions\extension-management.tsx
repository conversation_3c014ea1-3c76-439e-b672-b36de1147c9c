"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { 
  Clock, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Timer, 
  AlertTriangle,
  Calendar,
  TrendingUp
} from "lucide-react";
import { 
  getAllExtensionRequests,
  getExtensionRequestsByStatus,
  approveExtensionRequest,
  rejectExtensionRequest,
  getExtensionRequestStats,
  type ExtensionRequestWithDetails
} from "@/lib/services/admin-extension-service";
import { ExtensionRequestsTable } from "./extension-requests-table";
import { ExtensionRequestModal } from "./extension-request-modal";
import { useAdminAuth } from "@/components/auth/admin-auth-context";

type StatusFilter = 'all' | 'pending' | 'approved' | 'rejected' | 'expired';

interface ExtensionManagementProps {
  extensionRequests?: ExtensionRequestWithDetails[];
  loading?: boolean;
}

export function ExtensionManagement({ extensionRequests: externalRequests, loading: externalLoading }: ExtensionManagementProps) {
  const { user } = useAdminAuth();
  const { toast } = useToast();
  
  // State management
  const [extensionRequests, setExtensionRequests] = React.useState<ExtensionRequestWithDetails[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [selectedRequest, setSelectedRequest] = React.useState<ExtensionRequestWithDetails | null>(null);
  const [stats, setStats] = React.useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    expired: 0
  });

  // Use external data if provided, otherwise fetch internally
  const displayRequests = externalRequests || extensionRequests;
  const isLoading = externalLoading !== undefined ? externalLoading : loading;

  // Fetch extension requests
  const fetchExtensionRequests = React.useCallback(async () => {
    setLoading(true);
    try {
      const { data, error } = await getAllExtensionRequests();
      if (error) {
        throw new Error(error.message || 'Failed to fetch extension requests');
      }
      
      setExtensionRequests(data || []);
      
      // Fetch stats
      const { data: statsData } = await getExtensionRequestStats();
      if (statsData) {
        setStats(statsData);
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error Loading Extension Requests",
        description: error.message,
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Load data on mount (only if no external data provided)
  React.useEffect(() => {
    if (!externalRequests) {
      fetchExtensionRequests();
    }
  }, [fetchExtensionRequests, externalRequests]);

  const handleApprove = async (requestId: string, adminNotes?: string) => {
    if (!user?.id) {
      toast({
        variant: "destructive",
        title: "Authentication Error",
        description: "Admin user not found",
      });
      return;
    }

    const { success, error } = await approveExtensionRequest(requestId, user.id, adminNotes);
    
    if (success) {
      toast({
        title: "Extension Approved",
        description: "The extension request has been approved successfully.",
      });
      await fetchExtensionRequests(); // Refresh data
    } else {
      toast({
        variant: "destructive",
        title: "Approval Failed",
        description: error?.message || "Failed to approve extension request",
      });
      throw error;
    }
  };

  const handleReject = async (requestId: string, rejectionReason: string, adminNotes?: string) => {
    if (!user?.id) {
      toast({
        variant: "destructive",
        title: "Authentication Error",
        description: "Admin user not found",
      });
      return;
    }

    const { success, error } = await rejectExtensionRequest(requestId, user.id, rejectionReason, adminNotes);
    
    if (success) {
      toast({
        title: "Extension Rejected",
        description: "The extension request has been rejected.",
      });
      await fetchExtensionRequests(); // Refresh data
    } else {
      toast({
        variant: "destructive",
        title: "Rejection Failed",
        description: error?.message || "Failed to reject extension request",
      });
      throw error;
    }
  };


  return (
    <div>
      <ExtensionRequestsTable
        extensionRequests={displayRequests}
        loading={isLoading}
        onViewDetails={setSelectedRequest}
        onApprove={(requestId) => handleApprove(requestId)}
        onReject={(requestId) => {
          // Find request to get context for quick rejection
          const request = displayRequests.find(r => r.id === requestId);
          if (request) {
            setSelectedRequest(request);
          }
        }}
      />

      {/* Extension Request Modal */}
      <ExtensionRequestModal
        request={selectedRequest}
        isOpen={!!selectedRequest}
        onClose={() => setSelectedRequest(null)}
        onApprove={handleApprove}
        onReject={handleReject}
      />
    </div>
  );
}
