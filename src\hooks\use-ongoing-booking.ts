"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, usePathname } from "next/navigation";

export interface OngoingBookingData {
  selectedCar: {
    id: string;
    make: string;
    model: string;
    year: number;
  } | null;
  pickUpLocation: string;
  dropOffLocation: string;
  pickUpDate: string;
  pickUpTime: string;
  dropOffDate: string;
  dropOffTime: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  currentStep: number;
  lastUpdated: number;
}

const BOOKING_STORAGE_KEY = "ollietrack-booking-data";
const BOOKING_META_KEY = "ollietrack-booking-meta";
const BOOKING_EXPIRY_HOURS = 24;

/**
 * Hook to manage ongoing booking state and provide Quick-Return functionality
 */
export function useOngoingBooking() {
  const [ongoingBooking, setOngoingBooking] = useState<OngoingBookingData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  /**
   * Check if current path should show the FAB
   */
  const shouldShowBanner = useCallback(() => {
    // Only exclude booking flow page to prevent duplicate UI
    // Allow on homepage, all customer pages, and even login/signup for better UX
    const excludedPaths = [
      '/customer/booking/flow'
    ];
    
    return !excludedPaths.some(path => pathname?.startsWith(path));
  }, [pathname]);

  /**
   * Determine current step based on booking data completeness
   */
  const determineCurrentStep = useCallback((data: any): number => {
    // Step 1: Booking Details
    if (!data.selectedCar || !data.pickUpDate || !data.dropOffDate) {
      return 1;
    }
    
    // Step 2: Requirements Upload
    const requiredDocs = ['driversLicense', 'governmentId', 'proofOfBilling'];
    const hasAllDocs = requiredDocs.every(docType => {
      const files = data[docType];
      return files && files.length > 0 && files.every((f: any) => f.status === 'completed');
    });
    if (!hasAllDocs) {
      return 2;
    }
    
    // Step 3: Personal Info
    if (!data.customerName || !data.customerEmail || !data.customerPhone || 
        !data.familyMemberPhone || !data.familyMemberFacebook) {
      return 3;
    }
    
    // Step 4: Payment
    if (!data.paymentMethod || !data.proofOfPayment || 
        !data.proofOfPayment.length || 
        !data.proofOfPayment.every((f: any) => f.status === 'completed')) {
      return 4;
    }
    
    // Step 5: Review & Confirm
    return 5;
  }, []);

  /**
   * Check if booking data is valid and not expired
   */
  const isValidBookingData = useCallback((data: any, meta: any): boolean => {
    if (!data || !meta) return false;
    
    // Check if booking has expired (24 hours)
    const now = Date.now();
    const bookingAge = now - meta.lastUpdated;
    const maxAge = BOOKING_EXPIRY_HOURS * 60 * 60 * 1000;
    
    if (bookingAge > maxAge) {
      return false;
    }
    
    // Check if essential booking data exists
    return !!(
      (data.selectedCar || data.pickUpDate || data.dropOffDate || 
       data.customerName || data.customerEmail || data.customerPhone ||
       data.driversLicense?.length || data.governmentId?.length || data.proofOfBilling?.length)
    );
  }, []);

  /**
   * Load ongoing booking from localStorage
   */
  const loadOngoingBooking = useCallback(() => {
    try {
      const savedData = localStorage.getItem(BOOKING_STORAGE_KEY);
      const savedMeta = localStorage.getItem(BOOKING_META_KEY);
      
      if (savedData) {
        const data = JSON.parse(savedData);
        const meta = savedMeta ? JSON.parse(savedMeta) : { lastUpdated: Date.now() };
        
        if (isValidBookingData(data, meta)) {
          const currentStep = determineCurrentStep(data);
          
          const ongoingData: OngoingBookingData = {
            selectedCar: data.selectedCar ? {
              id: data.selectedCar.id,
              make: data.selectedCar.make,
              model: data.selectedCar.model,
              year: data.selectedCar.year,
            } : null,
            pickUpLocation: data.pickUpLocation || '',
            dropOffLocation: data.dropOffLocation || '',
            pickUpDate: data.pickUpDate || '',
            pickUpTime: data.pickUpTime || '',
            dropOffDate: data.dropOffDate || '',
            dropOffTime: data.dropOffTime || '',
            customerName: data.customerName || '',
            customerEmail: data.customerEmail || '',
            customerPhone: data.customerPhone || '',
            currentStep,
            lastUpdated: meta.lastUpdated,
          };
          
          setOngoingBooking(ongoingData);
        } else {
          // Clean up expired or invalid data
          clearOngoingBooking();
        }
      }
    } catch (error) {
      console.error('Failed to load ongoing booking:', error);
      clearOngoingBooking();
    } finally {
      setIsLoading(false);
    }
  }, [isValidBookingData, determineCurrentStep]);

  /**
   * Clear ongoing booking data
   */
  const clearOngoingBooking = useCallback(() => {
    localStorage.removeItem(BOOKING_STORAGE_KEY);
    localStorage.removeItem(BOOKING_META_KEY);
    setOngoingBooking(null);
  }, []);

  /**
   * Continue with ongoing booking
   */
  const continueBooking = useCallback(() => {
    if (ongoingBooking) {
      router.push('/customer/booking/flow');
    }
  }, [ongoingBooking, router]);

  /**
   * Update booking metadata when booking data changes
   */
  const updateBookingMeta = useCallback(() => {
    try {
      const meta = {
        lastUpdated: Date.now(),
      };
      localStorage.setItem(BOOKING_META_KEY, JSON.stringify(meta));
    } catch (error) {
      console.error('Failed to update booking meta:', error);
    }
  }, []);

  // Load booking data on mount and set up listeners
  useEffect(() => {
    loadOngoingBooking();
    
    // Listen for changes to localStorage (cross-tab sync)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === BOOKING_STORAGE_KEY || e.key === BOOKING_META_KEY) {
        loadOngoingBooking();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [loadOngoingBooking]);

  // Periodic check for expired bookings (every 5 minutes)
  useEffect(() => {
    const interval = setInterval(() => {
      if (ongoingBooking) {
        const now = Date.now();
        const bookingAge = now - ongoingBooking.lastUpdated;
        const maxAge = BOOKING_EXPIRY_HOURS * 60 * 60 * 1000;
        
        if (bookingAge > maxAge) {
          clearOngoingBooking();
        }
      }
    }, 5 * 60 * 1000); // 5 minutes
    
    return () => clearInterval(interval);
  }, [ongoingBooking, clearOngoingBooking]);

  return {
    ongoingBooking,
    isLoading,
    shouldShowBanner: shouldShowBanner(),
    continueBooking,
    clearOngoingBooking,
    updateBookingMeta,
  };
}
