"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Calendar } from "lucide-react"
import { 
  format, 
  startOfWeek, 
  endOfWeek, 
  eachDayOfInterval, 
  addWeeks, 
  subWeeks,
  startOfMonth,
  endOfMonth,
  eachWeekOfInterval,
  addMonths,
  subMonths,
  isSameDay,
  isToday,
  isSameMonth,
  startOfDay,
  endOfDay,
  parseISO,
  addDays
} from "date-fns"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"
import { getBookingStatusDisplay } from "@/lib/utils/booking-status"
import { type Booking } from "@/lib/types"

// Asia/Manila timezone utilities
const MANILA_TIMEZONE_OFFSET = 8 * 60; // UTC+8 in minutes

// Convert UTC date to Manila timezone
function toManilaTime(date: Date): Date {
  const utcTime = date.getTime() + (date.getTimezoneOffset() * 60000);
  return new Date(utcTime + (MANILA_TIMEZONE_OFFSET * 60000));
}

// Normalize date to start of day in Manila timezone
function normalizeToManilaDay(date: Date): Date {
  const manilaDate = toManilaTime(date);
  return startOfDay(manilaDate);
}

interface BookingCalendarRowData extends Booking {
  booking_ref: string
  userName: string
  carModel: string
  from: Date
  to: Date
  days: number
  payStatus: "Paid" | "Unpaid" | "Partial" | "Refunded"
  totalAmount: number
}

interface BookingCalendarProps {
  bookings: BookingCalendarRowData[]
  onEventClick?: (booking: BookingCalendarRowData) => void
  view?: "week" | "month"
  onViewChange?: (view: "week" | "month") => void
  currentDate?: Date
  onDateChange?: (date: Date) => void
  className?: string
}

export function BookingCalendar({
  bookings,
  onEventClick,
  view = "week",
  onViewChange,
  currentDate: propCurrentDate,
  onDateChange,
  className
}: BookingCalendarProps) {
  const isMobile = useIsMobile();
  // Use prop value if provided, otherwise use internal state
  const [internalDate, setInternalDate] = React.useState(new Date())
  const currentDate = propCurrentDate || internalDate
  
  // Update handler that respects both prop and internal state
  const updateCurrentDate = (newDate: Date) => {
    if (onDateChange) {
      onDateChange(newDate)
    } else {
      setInternalDate(newDate)
    }
  }

  const navigatePrevious = () => {
    updateCurrentDate(view === "week" ? subWeeks(currentDate, 1) : subMonths(currentDate, 1))
  }

  const navigateNext = () => {
    updateCurrentDate(view === "week" ? addWeeks(currentDate, 1) : addMonths(currentDate, 1))
  }

  const navigateToday = () => {
    updateCurrentDate(new Date())
  }

  const getViewDates = () => {
    if (view === "week") {
      const start = startOfWeek(currentDate, { weekStartsOn: 0 })
      const end = endOfWeek(currentDate, { weekStartsOn: 0 })
      return eachDayOfInterval({ start, end })
    } else {
      const start = startOfMonth(currentDate)
      const end = endOfMonth(currentDate)
      const weeks = eachWeekOfInterval({ start, end }, { weekStartsOn: 0 })
      return weeks.map(weekStart => 
        eachDayOfInterval({ 
          start: startOfWeek(weekStart, { weekStartsOn: 0 }), 
          end: endOfWeek(weekStart, { weekStartsOn: 0 })
        })
      ).flat()
    }
  }

  const getBookingsForDate = (date: Date) => {
    return bookings.filter(booking => {
      // Normalize all dates to Manila timezone for consistent comparison
      const normalizedDate = normalizeToManilaDay(date);
      const bookingStart = normalizeToManilaDay(booking.from);
      const bookingEnd = normalizeToManilaDay(booking.to);
      
      // Check if the booking spans this date (inclusive of both start and end dates)
      return normalizedDate >= bookingStart && normalizedDate <= bookingEnd;
    })
  }

  const formatViewTitle = () => {
    if (view === "week") {
      const start = startOfWeek(currentDate, { weekStartsOn: 0 })
      const end = endOfWeek(currentDate, { weekStartsOn: 0 })
      if (isSameMonth(start, end)) {
        return `${format(start, "MMM d")} - ${format(end, "d, yyyy")}`
      } else {
        return `${format(start, "MMM d")} - ${format(end, "MMM d, yyyy")}`
      }
    } else {
      return format(currentDate, "MMMM yyyy")
    }
  }

  const dates = getViewDates()
  const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]

  const renderWeekView = () => {
    return (
      <div className="grid grid-cols-7 gap-0">
        {/* Enhanced Day headers with gradient background */}
        {daysOfWeek.map(day => (
          <div 
            key={day} 
            className="bg-gradient-to-r from-slate-100 to-gray-100 p-2 xs:p-3 lg:p-4 text-center text-xs xs:text-sm lg:text-base font-bold text-slate-700 border-r border-b border-slate-300 last:border-r-0 shadow-sm"
          >
            <div className="flex flex-col items-center gap-1">
              <span className="hidden xs:inline">{day}</span>
              <span className="xs:hidden text-lg font-black">{day.substring(0, 1)}</span>
            </div>
          </div>
        ))}
        
        {/* Enhanced Date cells with improved styling */}
        {dates.map(date => {
          const dayBookings = getBookingsForDate(date)
          
          return (
            <div
              key={date.toISOString()}
              className={cn(
                "min-h-[70px] xs:min-h-[90px] sm:min-h-[110px] lg:min-h-[130px] p-1 xs:p-2 lg:p-3 border-r border-b border-slate-200 overflow-hidden transition-all duration-300 hover:bg-slate-50 last:border-r-0",
                isToday(date) 
                  ? "bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-300 shadow-lg" 
                  : "bg-white hover:shadow-md"
              )}
            >
              {/* Enhanced date number */}
              <div className={cn(
                "text-xs xs:text-sm lg:text-base font-bold mb-1 xs:mb-2 flex items-center justify-between",
                isToday(date) 
                  ? "text-blue-700" 
                  : "text-slate-600"
              )}>
                <span className={cn(
                  "w-6 h-6 xs:w-7 xs:h-7 lg:w-8 lg:h-8 rounded-full flex items-center justify-center text-xs xs:text-sm font-black transition-all duration-300",
                  isToday(date)
                    ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg"
                    : dayBookings.length > 0
                    ? "bg-gradient-to-r from-slate-200 to-gray-300 text-slate-700"
                    : "text-slate-400"
                )}>
                  {format(date, "d")}
                </span>
                {dayBookings.length > 0 && (
                  <div className="w-2 h-2 xs:w-3 xs:h-3 bg-emerald-500 rounded-full animate-pulse shadow-sm"></div>
                )}
              </div>
              
              {/* Enhanced booking events */}
              <div className="space-y-1 xs:space-y-1.5">
                {dayBookings.slice(0, isMobile ? 2 : 3).map(booking => (
                  <div
                    key={booking.id}
                    className={cn(
                      "text-[9px] xs:text-[10px] lg:text-xs p-1 xs:p-1.5 lg:p-2 rounded-lg cursor-pointer truncate transition-all duration-300 transform hover:scale-105 hover:shadow-md font-semibold border",
                      booking.status === "Pending" && "bg-gradient-to-r from-amber-100 to-orange-100 text-amber-900 hover:from-amber-200 hover:to-orange-200 border-amber-300 shadow-sm",
                      booking.status === "Active" && "bg-gradient-to-r from-green-100 to-emerald-100 text-green-900 hover:from-green-200 hover:to-emerald-200 border-green-300 shadow-sm",
                      booking.status === "Completed" && "bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-900 hover:from-blue-200 hover:to-cyan-200 border-blue-300 shadow-sm",
                      booking.status === "Cancelled" && "bg-gradient-to-r from-red-100 to-rose-100 text-red-900 hover:from-red-200 hover:to-rose-200 border-red-300 shadow-sm"
                    )}
                    onClick={() => onEventClick?.(booking)}
                    title={`${booking.userName} – ${booking.carModel}\n${format(booking.from, "h:mm a")} - ${format(booking.to, "h:mm a")}\n${getBookingStatusDisplay(booking.status)} • ₱${booking.totalAmount.toFixed(2)}`}
                  >
                    <div className="flex items-center gap-1 xs:gap-1.5">
                      <div className={cn(
                        "w-1.5 h-1.5 xs:w-2 xs:h-2 rounded-full flex-shrink-0",
                        booking.status === "Pending" && "bg-amber-500",
                        booking.status === "Active" && "bg-green-500",
                        booking.status === "Completed" && "bg-blue-500",
                        booking.status === "Cancelled" && "bg-red-500"
                      )}></div>
                      <span className="truncate">
                        {isMobile ? booking.userName.split(' ')[0] : `${booking.userName} – ${booking.carModel}`}
                      </span>
                    </div>
                  </div>
                ))}
                {dayBookings.length > (isMobile ? 2 : 3) && (
                  <div className="text-[9px] xs:text-[10px] lg:text-xs text-slate-500 pl-1 xs:pl-2 font-medium bg-slate-100 rounded-full py-1 px-2 border border-slate-200 shadow-sm">
                    +{dayBookings.length - (isMobile ? 2 : 3)} more bookings
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  const renderMonthView = () => {
    const weeks = []
    for (let i = 0; i < dates.length; i += 7) {
      weeks.push(dates.slice(i, i + 7))
    }

    return (
      <div className="space-y-0">
        {/* Enhanced Day headers with gradient background - matching week view */}
        <div className="grid grid-cols-7 gap-0">
          {daysOfWeek.map(day => (
            <div 
              key={day} 
              className="bg-gradient-to-r from-slate-100 to-gray-100 p-2 xs:p-3 lg:p-4 text-center text-xs xs:text-sm lg:text-base font-bold text-slate-700 border-r border-b border-slate-300 last:border-r-0 shadow-sm"
            >
              <div className="flex flex-col items-center gap-1">
                <span className="hidden xs:inline">{day}</span>
                <span className="xs:hidden text-lg font-black">{day.substring(0, 1)}</span>
              </div>
            </div>
          ))}
        </div>
        
        {/* Enhanced Week rows */}
        {weeks.map((week, weekIndex) => (
          <div key={weekIndex} className="grid grid-cols-7 gap-0">
            {week.map(date => {
              const dayBookings = getBookingsForDate(date)
              const isCurrentMonth = isSameMonth(date, currentDate)
              
              return (
                <div
                  key={date.toISOString()}
                  className={cn(
                    "min-h-[60px] xs:min-h-[80px] sm:min-h-[100px] lg:min-h-[120px] p-1 xs:p-2 lg:p-3 border-r border-b border-slate-200 overflow-hidden transition-all duration-300 hover:shadow-md last:border-r-0",
                    isCurrentMonth 
                      ? isToday(date)
                        ? "bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-300 shadow-lg"
                        : "bg-white hover:bg-slate-50"
                      : "bg-gradient-to-br from-slate-100 to-gray-100 text-slate-500",
                    dayBookings.length > 0 && "hover:shadow-lg"
                  )}
                >
                  {/* Enhanced date number - matching week view */}
                  <div className={cn(
                    "text-xs xs:text-sm lg:text-base font-bold mb-1 xs:mb-2 flex items-center justify-between",
                    isToday(date) 
                      ? "text-blue-700" 
                      : isCurrentMonth 
                      ? "text-slate-600"
                      : "text-slate-400"
                  )}>
                    <span className={cn(
                      "w-5 h-5 xs:w-6 xs:h-6 lg:w-7 lg:h-7 rounded-full flex items-center justify-center text-xs xs:text-sm font-black transition-all duration-300",
                      isToday(date)
                        ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg"
                        : dayBookings.length > 0 && isCurrentMonth
                        ? "bg-gradient-to-r from-slate-200 to-gray-300 text-slate-700"
                        : isCurrentMonth
                        ? "text-slate-400"
                        : "text-slate-300"
                    )}>
                      {format(date, "d")}
                    </span>
                    {dayBookings.length > 0 && isCurrentMonth && (
                      <div className="w-1.5 h-1.5 xs:w-2 xs:h-2 bg-emerald-500 rounded-full animate-pulse shadow-sm"></div>
                    )}
                  </div>
                  
                  {/* Enhanced booking events - only show for current month */}
                  {isCurrentMonth && (
                    <div className="space-y-0.5 xs:space-y-1">
                      {dayBookings.slice(0, isMobile ? 1 : 2).map(booking => (
                        <div
                          key={booking.id}
                          className={cn(
                            "text-[8px] xs:text-[9px] lg:text-[10px] p-0.5 xs:p-1 lg:p-1.5 rounded-lg cursor-pointer truncate transition-all duration-300 transform hover:scale-105 hover:shadow-md font-semibold border",
                            booking.status === "Pending" && "bg-gradient-to-r from-amber-100 to-orange-100 text-amber-900 hover:from-amber-200 hover:to-orange-200 border-amber-300 shadow-sm",
                            booking.status === "Active" && "bg-gradient-to-r from-green-100 to-emerald-100 text-green-900 hover:from-green-200 hover:to-emerald-200 border-green-300 shadow-sm",
                            booking.status === "Completed" && "bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-900 hover:from-blue-200 hover:to-cyan-200 border-blue-300 shadow-sm",
                            booking.status === "Cancelled" && "bg-gradient-to-r from-red-100 to-rose-100 text-red-900 hover:from-red-200 hover:to-rose-200 border-red-300 shadow-sm"
                          )}
                          onClick={() => onEventClick?.(booking)}
                          title={`${booking.userName} – ${booking.carModel}\n${format(booking.from, "MMM d, h:mm a")} - ${format(booking.to, "MMM d, h:mm a")}\n${getBookingStatusDisplay(booking.status)} • ₱${booking.totalAmount.toFixed(2)}`}
                        >
                          <div className="flex items-center gap-0.5 xs:gap-1">
                            <div className={cn(
                              "w-1 h-1 xs:w-1.5 xs:h-1.5 rounded-full flex-shrink-0",
                              booking.status === "Pending" && "bg-amber-500",
                              booking.status === "Active" && "bg-green-500",
                              booking.status === "Completed" && "bg-blue-500",
                              booking.status === "Cancelled" && "bg-red-500"
                            )}></div>
                            <span className="truncate">
                              {booking.userName.split(' ')[0]}
                            </span>
                          </div>
                        </div>
                      ))}
                      {dayBookings.length > (isMobile ? 1 : 2) && (
                        <div className="text-[8px] xs:text-[9px] lg:text-[10px] text-slate-500 font-medium bg-slate-100 rounded-full py-0.5 px-1.5 xs:px-2 border border-slate-200 shadow-sm">
                          +{dayBookings.length - (isMobile ? 1 : 2)}
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Show booking count for non-current month dates */}
                  {!isCurrentMonth && dayBookings.length > 0 && (
                    <div className="text-[8px] xs:text-[9px] text-slate-400 font-medium">
                      {dayBookings.length} booking{dayBookings.length > 1 ? 's' : ''}
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={cn("w-full", className)}>
      {/* Current View Title Section */}
      <div className="bg-gradient-to-r from-indigo-50 via-blue-50 to-purple-50 rounded-t-2xl p-4 xs:p-6 border border-indigo-100 border-b-0">
        <div className="flex flex-col xs:flex-row items-start xs:items-center justify-between gap-2 xs:gap-3">
          <h3 className="text-xl xs:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            {formatViewTitle()}
          </h3>
          <div className="flex items-center gap-2 text-xs xs:text-sm text-slate-600 bg-white/70 px-3 py-2 rounded-full shadow-sm border border-slate-200">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="font-medium">Times shown in Asia/Manila (UTC+8)</span>
          </div>
        </div>
      </div>
      
      {/* Calendar Grid */}
      <div className="border-2 border-slate-200 border-t-0 rounded-b-2xl overflow-hidden">
        <div className="overflow-x-auto">
          {view === "week" ? renderWeekView() : renderMonthView()}
        </div>
      </div>
    </div>
  )
}
