import { streamText } from 'ai';
import { NextRequest } from 'next/server';
import { openai, MODEL_SETTINGS } from '@/lib/openrouter';
import { searchKnowledge, buildRagContext } from '@/lib/rag';
import { getLiveWebsiteContext } from '@/lib/data-ingestion/live-data-fetcher';
import { createContextClient } from '@/lib/supabase/server';

// System prompt for admin chatbot
const ADMIN_SYSTEM_PROMPT = `You are PathLink AI, an advanced AI assistant specifically designed and built for <PERSON><PERSON>'s car rental management system. You are the dedicated administrative intelligence that powers PathLink's operations, helping administrators efficiently manage the entire car rental platform.

IDENTITY & INTRODUCTION:
When users first interact with you, introduce yourself as: "Hi! I'm PathLink AI 🤖, your dedicated administrative assistant built specifically for <PERSON><PERSON>'s car rental management system. I'm here to help you efficiently manage bookings, fleet operations, customer accounts, and all administrative tasks. With access to real-time data and comprehensive system knowledge, I can provide instant insights and operational support. How can I assist you with PathLink operations today? 🚗💼"

IMPORTANT CONTENT POLICY:
- ONLY respond to queries related to car rental management, fleet operations, bookings, payments, customers, and PathLink business operations
- REJECT personal questions, emotional support, general life advice, or unrelated topics
- If asked about off-topic subjects, politely redirect to PathLink-related assistance
- Example rejection response: "I'm designed specifically to help with PathLink car rental operations. Please ask about bookings, fleet management, customers, or system administration. 🚗💼"

Key Guidelines:
- Be professional, efficient, and detail-oriented
- Focus on administrative tasks, system management, and operational support
- Provide technical guidance for platform operations
- Help with booking management, customer issues, and system troubleshooting
- Always prioritize security and data protection
- Provide clear, actionable advice
- Use appropriate emojis to make responses more engaging and clear 🚗💼📊
- All currency amounts must be displayed in Philippine Peso (₱) format

Admin Capabilities:
- Booking management and finalization 📋✅
- Customer account management 👥💳
- Car fleet management 🚗🔧
- Payment processing and verification 💰✔️
- Document verification 📄🔍
- System monitoring and troubleshooting 🖥️⚡
- Reports and analytics 📊📈

Currency Format:
- Always use Philippine Peso symbol (₱) instead of dollar sign ($)
- Example: ₱1,800/day instead of $1800/day
- Format large amounts with commas: ₱15,000, ₱125,500

Response Style:
- Use relevant emojis to enhance clarity and engagement
- Vehicle mentions: 🚗 🚙 🚐
- Money/pricing: ₱ 💰 💳
- Status indicators: ✅ ❌ ⚠️ ⏳
- Admin actions: 📋 🔧 📊 💼
- Use plain text format without markdown symbols (no ### headers, no **bold** markers)
- List vehicle information in numbered format for clear presentation

Security Notes:
- Never provide sensitive customer data in chat 🔒
- Always reference proper admin procedures 📋
- Escalate security concerns appropriately ⚠️
- Maintain audit trails for admin actions 📝`;

export async function POST(request: NextRequest) {
  try {
    const supabase = await createContextClient('admin');
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Verify admin privileges
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['admin', 'super_admin'].includes(profile.role)) {
      return new Response('Admin access required', { status: 403 });
    }

    const { messages, sessionId } = await request.json();
    
    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid messages format', { status: 400 });
    }

    // Get the latest user message for RAG search
    const userMessage = messages[messages.length - 1];
    if (!userMessage || userMessage.role !== 'user') {
      return new Response('No valid user message found', { status: 400 });
    }

    // Handle multiple possible content formats from AI SDK
    let messageContent = '';
    
    if (typeof userMessage.content === 'string') {
      messageContent = userMessage.content.trim();
    } else if (Array.isArray(userMessage.content)) {
      // Handle array format where content is an array of parts
      const textParts = userMessage.content.filter((part: any) => part.type === 'text');
      messageContent = textParts.map((part: any) => part.text).join(' ').trim();
    } else if (userMessage.text) {
      messageContent = userMessage.text.trim();
    }
    
    if (!messageContent) {
      return new Response('Empty message content', { status: 400 });
    }

    // Get live website context
    const liveContext = await getLiveWebsiteContext(messageContent, 'admin', user.id);

    // Search knowledge base for relevant context
    const knowledgeResults = await searchKnowledge(
      messageContent,
      'admin',
      {
        matchThreshold: 0.6,
        matchCount: 5,
        filterSource: undefined // Allow all sources for admin
      }
    );

    // Build comprehensive RAG context
    const ragContext = buildRagContext(
      messageContent,
      knowledgeResults,
      'admin'
    );

    // Combine static knowledge and live data
    const combinedContext = `
LIVE WEBSITE DATA:
${liveContext}

${ragContext}
    `.trim();

    // Create conversation if needed
    if (sessionId && messageContent) {
      await supabase
        .from('chatbot_conversations')
        .upsert({
          user_id: user.id,
          user_type: 'admin',
          session_id: sessionId,
          title: messageContent.substring(0, Math.min(100, messageContent.length))
        }, { onConflict: 'session_id' });
    }

    // Prepare messages with enhanced system prompt and combined context
    const systemMessage = {
      role: 'system' as const,
      content: `${ADMIN_SYSTEM_PROMPT}\n\n${combinedContext}`
    };

    const conversationMessages = [systemMessage, ...messages];

    // Generate streaming response
    const result = await streamText({
      model: openai(MODEL_SETTINGS.model),
      messages: conversationMessages,
      temperature: MODEL_SETTINGS.temperature,
      frequencyPenalty: MODEL_SETTINGS.frequencyPenalty,
      presencePenalty: MODEL_SETTINGS.presencePenalty,
    });

    return result.toTextStreamResponse();
  } catch (error) {
    console.error('Admin chat error:', error);
    return new Response('Internal server error', { status: 500 });
  }
}
