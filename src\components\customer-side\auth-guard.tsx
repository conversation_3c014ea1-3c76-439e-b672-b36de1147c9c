"use client";

import * as React from "react";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { AuthLoading, QuickAuthLoading } from "./loading/auth-loading";
import { useRouter } from "next/navigation";

interface CustomerAuthGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
  loadingComponent?: React.ComponentType;
  requireAuth?: boolean;
}

export function CustomerAuthGuard({
  children,
  redirectTo = "/customer/login",
  loadingComponent: LoadingComponent = QuickAuthLoading,
  requireAuth = true
}: CustomerAuthGuardProps) {
  const { user, profile } = useCustomerAuth();
  const router = useRouter();
  const [hasCheckedAuth, setHasCheckedAuth] = React.useState(false);
  const [isRedirecting, setIsRedirecting] = React.useState(false);

  React.useEffect(() => {
    // Mark that we've checked auth after initial mount
    const timer = setTimeout(() => {
      setHasCheckedAuth(true);
    }, 100); // Very quick initial check

    return () => clearTimeout(timer);
  }, []);

  React.useEffect(() => {
    if (hasCheckedAuth && requireAuth && !user && !isRedirecting) {
      setIsRedirecting(true);
      router.replace(redirectTo);
    }
  }, [hasCheckedAuth, requireAuth, user, isRedirecting, router, redirectTo]);

  // Show loading only for a very short time during initial auth check
  if (!hasCheckedAuth || (requireAuth && !user && !isRedirecting)) {
    return <LoadingComponent />;
  }

  // Handle admin redirect
  if (user && profile?.role === "admin") {
    router.replace("/admin");
    return <LoadingComponent />;
  }

  return <>{children}</>;
}

// Lightweight wrapper for pages that don't require authentication
export function CustomerPageWrapper({ children }: { children: React.ReactNode }) {
  return (
    <CustomerAuthGuard requireAuth={false}>
      {children}
    </CustomerAuthGuard>
  );
}

// Wrapper for pages that require authentication
export function ProtectedCustomerPage({ children }: { children: React.ReactNode }) {
  return (
    <CustomerAuthGuard requireAuth={true}>
      {children}
    </CustomerAuthGuard>
  );
}
