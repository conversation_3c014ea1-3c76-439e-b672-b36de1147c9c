"use client";

import { PublicAppShell } from "@/components/layout/public-app-shell";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { ProtectedCustomerPage } from "@/components/customer-side/auth-guard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Car,
  CalendarDays,
  CreditCard,
  CheckCircle,
  Clock,
  Loader2,
  Eye,
  Receipt,
  ShieldCheck,
  Upload,
  XCircle,
  AlertCircle,
  FileText,
  Zap,
  ArrowRight,
  Settings,
  MessageCircle,
  BarChart3,
  Shield,
  Star,
} from "lucide-react";
import Link from "next/link";
import * as React from "react";
import {
  getCustomerPayments,
  getPaymentStatusForBooking,
} from "./actions/payment-actions";
import { getCustomerBookings } from "../booking/actions/booking-actions";
import { Payment } from "@/lib/types";
import { PaymentReuploadModal } from "@/components/customer-side/dashboard/payment-reupload-modal";
import { UnifiedDashboardTable } from "@/components/customer-side/dashboard/unified-dashboard-table";
import { BookingDetailsModal } from "@/components/customer-side/dashboard/booking-details-modal";
import { DocumentUploadModal } from "@/components/customer-side/dashboard/document-upload-modal";
import { ExtensionRequestModal } from "@/components/customer-side/booking/extension-request-modal";
import { finishTrip, cancelBooking } from "./actions/trip-actions";
import { useToast } from "@/hooks/use-toast";
import { CustomerDashboardSkeleton } from "@/components/customer-side/loading/skeleton-components";

interface BookingData {
  id: string;
  booking_ref: string;
  status: string;
  pickup_location: string;
  dropoff_location: string;
  pickup_datetime: string;
  dropoff_datetime: string;
  total_amount: number;
  created_at?: string;
  cars: {
    id: string;
    model: string;
    type: string;
    plate_number: string;
    image_url?: string;
    price_per_day: number;
  } | null;
  customers?: {
    full_name: string;
    email?: string;
  } | null;
}

export default function UserDashboard() {
  const { user, profile, loading } = useCustomerAuth();
  const { toast } = useToast();
  const [payments, setPayments] = React.useState<Payment[]>([]);
  const [bookingPayments, setBookingPayments] = React.useState<
    Record<string, Payment>
  >({});
  const [bookings, setBookings] = React.useState<BookingData[]>([]);
  const [loadingPayments, setLoadingPayments] = React.useState(true);
  const [loadingBookings, setLoadingBookings] = React.useState(true);
  const [selectedPaymentForReupload, setSelectedPaymentForReupload] = React.useState<string | null>(null);
  const [selectedBookingForDetails, setSelectedBookingForDetails] = React.useState<BookingData | null>(null);
  const [completingTrips, setCompletingTrips] = React.useState<Set<string>>(new Set());
  const [cancellingBookings, setCancellingBookings] = React.useState<Set<string>>(new Set());
  const [documentUploadModal, setDocumentUploadModal] = React.useState<{ bookingId: string; documentType: string } | null>(null);
  const [extensionRequestModal, setExtensionRequestModal] = React.useState<BookingData | null>(null);

  // Serialize unknown error shapes for reliable logging
  const toErrorObject = (err: unknown) => {
    if (err && typeof err === "object") {
      const anyErr = err as Record<string, any>;
      return {
        message: anyErr.message || anyErr.msg || anyErr.error || "Unknown error",
        code: anyErr.code,
        name: anyErr.name,
        status: anyErr.status,
        details: anyErr.details,
        hint: anyErr.hint,
        ...("stack" in anyErr ? { stack: anyErr.stack } : {}),
      };
    }
    return { message: String(err) };
  };

  const toErrorString = (err: unknown) => {
    try {
      return JSON.stringify(toErrorObject(err));
    } catch {
      return String(err);
    }
  };

  // Fetch data when component mounts and user is available
  React.useEffect(() => {
    console.log("Dashboard auth state:", { user: !!user, loading, profile });
    if (user) {
      fetchPaymentData();
      fetchBookingData();
    }
  }, [user, loading, profile]);

  const fetchPaymentData = async () => {
    if (!user) return;

    setLoadingPayments(true);

    try {
      // Fetch all customer payments
      const { data: paymentsData, error: paymentsError } =
        await getCustomerPayments();

      if (paymentsError) {
        console.error(
          "Error fetching payments:",
          paymentsError,
          toErrorString(paymentsError)
        );
      } else {
        setPayments(paymentsData || []);
      }
    } catch (error) {
      console.error("Error fetching payment data:", error, toErrorString(error));
    } finally {
      setLoadingPayments(false);
    }
  };

  const fetchBookingData = async () => {
    if (!user) return;

    setLoadingBookings(true);

    try {
      // Fetch customer bookings
      const { data: bookingsData, error: bookingsError } =
        await getCustomerBookings();

      if (bookingsError) {
        console.error(
          "Error fetching bookings:",
          bookingsError,
          toErrorString(bookingsError)
        );
      } else {
        // Transform the data to match our interface (cars can be an array or an object from Supabase)
        const transformedBookings = (bookingsData || []).map((booking: any) => {
          const rel = booking.cars;
          const car = Array.isArray(rel)
            ? (rel[0] ?? null)
            : rel && typeof rel === "object"
            ? rel
            : null;
          return { ...booking, cars: car } as BookingData;
        });
        
        setBookings(transformedBookings);

        // Fetch payment status for each booking in parallel
        const paymentResults = await Promise.all(
          (bookingsData || []).map((b: { id: string }) =>
            getPaymentStatusForBooking(b.id)
          )
        );

        const bookingPaymentData: Record<string, Payment> = {};
        for (let idx = 0; idx < (bookingsData || []).length; idx++) {
          const b = (bookingsData as Array<{ id: string }>)[idx];
          const result = paymentResults[idx] as { data?: Payment | null } | undefined;
          const data = result?.data ?? null;
          if (data) bookingPaymentData[b.id] = data;
        }
        setBookingPayments(bookingPaymentData);
      }
    } catch (error) {
      console.error("Error fetching booking data:", error, toErrorString(error));
    } finally {
      setLoadingBookings(false);
    }
  };

  const handleFinishTrip = async (bookingId: string) => {
    if (completingTrips.has(bookingId)) return;

    setCompletingTrips(prev => new Set(prev).add(bookingId));

    try {
      const result = await finishTrip(bookingId);
      
      if (result.error) {
        toast({
          variant: "destructive",
          title: "Failed to Complete Trip",
          description: result.error.message,
        });
      } else {
        toast({
          title: "Trip Completed Successfully!",
          description: result.message,
          className: "bg-white border-green-200",
        });
        // Refresh data to reflect the changes
        fetchBookingData();
        fetchPaymentData();
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Trip Completion Failed",
        description: "An unexpected error occurred while completing your trip.",
      });
    } finally {
      setCompletingTrips(prev => {
        const next = new Set(prev);
        next.delete(bookingId);
        return next;
      });
    }
  };

  const handleExtensionRequest = (booking: any) => {
    // Transform booking to match ExtensionRequestModal expected type
    const transformedBooking = {
      ...booking,
      cars: booking.cars ? {
        ...booking.cars,
        image_url: booking.cars.image_url || "/images/default-car.png"
      } : null
    };
    setExtensionRequestModal(transformedBooking);
  };

  const handleExtensionCreated = () => {
    setExtensionRequestModal(null);
    // Refresh booking data after extension request
    fetchBookingData();
    toast({
      title: "Extension Request Submitted",
      description: "Your extension request has been submitted for admin review.",
      className: "bg-white border-green-200",
    });
  };

  // Show loading state while authentication is being resolved
  if (loading) {
    return (
      <PublicAppShell>
        <CustomerDashboardSkeleton />
      </PublicAppShell>
    );
  }

  // Redirect admin users to admin dashboard
  if (user && profile?.role === "admin") {
    return (
      <PublicAppShell>
        <div className="max-w-4xl mx-auto px-4 py-12">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Admin Access Detected
            </h1>
            <p className="text-gray-600 mb-6">
              Redirecting to admin dashboard...
            </p>
            <Link href="/admin">
              <Button>Go to Admin Dashboard</Button>
            </Link>
          </div>
        </div>
      </PublicAppShell>
    );
  }

  // Show access denied for unauthenticated users (only after loading is complete)
  if (!loading && !user) {
    return (
      <PublicAppShell>
        <div className="max-w-4xl mx-auto px-4 py-12">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Access Denied
            </h1>
            <p className="text-gray-600 mb-6">
              Please sign in to view your dashboard.
            </p>
            <Link href="/customer/login">
              <Button>Sign In</Button>
            </Link>
          </div>
        </div>
      </PublicAppShell>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-blue-100 text-blue-700 border-blue-200";
      case "completed":
        return "bg-green-100 text-green-700 border-green-200";
      case "cancelled":
        return "bg-red-100 text-red-700 border-red-200";
      case "pending":
        return "bg-yellow-100 text-yellow-700 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-green-100 text-green-700 border-green-200";
      case "pending verification":
        return "bg-yellow-100 text-yellow-700 border-yellow-200";
      case "rejected":
        return "bg-red-100 text-red-700 border-red-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return <CheckCircle className="h-3 w-3" />;
      case "pending verification":
        return <Clock className="h-3 w-3" />;
      case "rejected":
        return <XCircle className="h-3 w-3" />;
      default:
        return <AlertCircle className="h-3 w-3" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return <Clock className="h-3 w-3" />;
      case "completed":
        return <CheckCircle className="h-3 w-3" />;
      case "cancelled":
        return <XCircle className="h-3 w-3" />;
      case "pending":
        return <AlertCircle className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-PH", {
      style: "currency",
      currency: "PHP",
    }).format(amount);
  };

  // Transform bookings for the unified table
  const transformedBookings = bookings.map((booking) => ({
    ...booking,
    type: "booking" as const,
    status: booking.status as "Pending" | "Active" | "Completed" | "Cancelled",
    cars: booking.cars ? {
      ...booking.cars,
      image_url: booking.cars.image_url || "/images/default-car.png"
    } : null
  }));

  const transformedPayments = payments.map((payment) => ({
    id: payment.id,
    type: "payment" as const,
    payment_id: payment.id,
    payment_ref: (payment as any).payment_ref,
    payment_method: payment.method,
    payment_status: payment.status as "Paid" | "Pending" | "Rejected" | "Pending Verification",
    payment_amount: payment.amount,
    payment_date: payment.transaction_date,
    booking_reference: (payment as any).bookings?.booking_ref || payment.booking_id,
    proof_of_payment_url: payment.proof_of_payment_url,
    admin_notes: (payment as any).admin_notes || undefined,
  }));

  // Handle actions for unified table
  const handleViewReceipt = (paymentId: string) => {
    const payment = payments.find((p) => p.id === paymentId);
    if (payment?.proof_of_payment_url) {
      window.open(payment.proof_of_payment_url, "_blank");
    }
  };

  const handleReuploadPayment = (bookingId: string) => {
    setSelectedPaymentForReupload(bookingId);
  };

  const handleViewBookingDetails = (bookingId: string) => {
    const booking = bookings.find(b => b.id === bookingId);
    if (booking) {
      setSelectedBookingForDetails(booking);
    }
  };

  const handleCancelBooking = async (bookingId: string) => {
    if (cancellingBookings.has(bookingId)) return;

    setCancellingBookings(prev => new Set(prev).add(bookingId));

    try {
      const result = await cancelBooking(bookingId);
      
      if (result.error) {
        toast({
          variant: "destructive",
          title: "Failed to Cancel Booking",
          description: result.error.message,
        });
      } else {
        toast({
          title: "Booking Cancelled Successfully!",
          description: result.message,
          className: "bg-white border-green-200",
        });
        // Refresh data to reflect the changes
        fetchBookingData();
        fetchPaymentData();
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Booking Cancellation Failed",
        description: "An unexpected error occurred while cancelling your booking.",
      });
    } finally {
      setCancellingBookings(prev => {
        const next = new Set(prev);
        next.delete(bookingId);
        return next;
      });
    }
  };

  const handleDocumentUpload = (bookingId: string, documentType: string) => {
    setDocumentUploadModal({ bookingId, documentType });
  };

  const getDocumentLabel = (documentType: string) => {
    switch (documentType) {
      case "drivers_license":
        return "Driver's License";
      case "government_id":
        return "Government ID";
      case "proof_of_billing":
        return "Proof of Billing";
      case "proof_of_downpayment":
        return "Proof of Downpayment";
      default:
        return "Document";
    }
  };

  // Prefer customer's full name from profile; fall back to auth metadata, then email prefix, then generic label
  const displayName =
    (profile?.full_name && profile.full_name.trim()) ||
    user?.user_metadata?.full_name ||
    user?.email?.split("@")[0] ||
    "User";

  return (
    <PublicAppShell>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 overflow-x-hidden">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2 break-words">
            Welcome back, {displayName}!
          </h1>
          <p className="text-gray-600 break-words">
            Manage your bookings and view your rental history.
          </p>
        </div>

        {/* Enhanced Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 max-w-full">
          <Card className="relative bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 border-0 overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 to-blue-800/5"></div>
            <CardContent className="p-6 relative">
              <div className="flex items-center justify-between min-w-0">
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <p className="text-blue-700 text-sm font-semibold uppercase tracking-wider">
                      Active Bookings
                    </p>
                  </div>
                  <p className="text-3xl font-bold text-blue-800 mb-1">
                    {bookings.filter((b) => b.status === "Active").length}
                  </p>
                  <p className="text-xs text-blue-600">Currently in progress</p>
                </div>
                <div className="relative">
                  <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-xl group-hover:blur-2xl transition-all"></div>
                  <Car className="h-10 w-10 text-blue-600 relative z-10 group-hover:scale-110 transition-transform" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="relative bg-gradient-to-br from-green-50 via-green-100 to-green-200 border-0 overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group">
            <div className="absolute inset-0 bg-gradient-to-br from-green-600/5 to-green-800/5"></div>
            <CardContent className="p-6 relative">
              <div className="flex items-center justify-between min-w-0">
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                    <p className="text-green-700 text-sm font-semibold uppercase tracking-wider">
                      Completed Trips
                    </p>
                  </div>
                  <p className="text-3xl font-bold text-green-800 mb-1">
                    {bookings.filter((b) => b.status === "Completed").length}
                  </p>
                  <p className="text-xs text-green-600">Successfully finished</p>
                </div>
                <div className="relative">
                  <div className="absolute inset-0 bg-green-500/20 rounded-full blur-xl group-hover:blur-2xl transition-all"></div>
                  <CheckCircle className="h-10 w-10 text-green-600 relative z-10 group-hover:scale-110 transition-transform" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="relative bg-gradient-to-br from-purple-50 via-purple-100 to-purple-200 border-0 overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 to-purple-800/5"></div>
            <CardContent className="p-6 relative">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-2 w-2 bg-purple-500 rounded-full animate-pulse"></div>
                    <p className="text-purple-700 text-sm font-semibold uppercase tracking-wider">
                      Total Spent
                    </p>
                  </div>
                  <p className="text-3xl font-bold text-purple-800 mb-1">
                    {formatCurrency(
                      payments
                        .filter((p) => p.status?.toLowerCase() === "paid")
                        .reduce((sum, p) => sum + Number(p.amount), 0)
                    )}
                  </p>
                  <p className="text-xs text-purple-600">Lifetime spending</p>
                </div>
                <div className="relative">
                  <div className="absolute inset-0 bg-purple-500/20 rounded-full blur-xl group-hover:blur-2xl transition-all"></div>
                  <CreditCard className="h-10 w-10 text-purple-600 relative z-10 group-hover:scale-110 transition-transform" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions Section */}
        <Card className="mb-8 overflow-hidden border-0 shadow-lg bg-gradient-to-r from-gray-50 to-white">
          <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <Zap className="h-5 w-5" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold">Quick Actions</CardTitle>
                <p className="text-blue-100 text-sm mt-1">Everything you need at your fingertips</p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Link href="/customer/catalog" className="group">
                <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white p-6 transition-all duration-200 hover:shadow-lg hover:scale-105 hover:border-blue-300">
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0">
                      <div className="p-3 bg-blue-50 rounded-xl group-hover:bg-blue-100 transition-colors">
                        <Car className="h-6 w-6 text-blue-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                        Browse Vehicles
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        Find your perfect ride
                      </p>
                    </div>
                  </div>
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <ArrowRight className="h-4 w-4 text-blue-500" />
                  </div>
                </div>
              </Link>

              <Link href="/customer/settings" className="group">
                <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white p-6 transition-all duration-200 hover:shadow-lg hover:scale-105 hover:border-green-300">
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0">
                      <div className="p-3 bg-green-50 rounded-xl group-hover:bg-green-100 transition-colors">
                        <Settings className="h-6 w-6 text-green-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                        Account Settings
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        Manage your profile
                      </p>
                    </div>
                  </div>
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <ArrowRight className="h-4 w-4 text-green-500" />
                  </div>
                </div>
              </Link>

              <Link href="/contact" className="group">
                <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white p-6 transition-all duration-200 hover:shadow-lg hover:scale-105 hover:border-purple-300">
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0">
                      <div className="p-3 bg-purple-50 rounded-xl group-hover:bg-purple-100 transition-colors">
                        <MessageCircle className="h-6 w-6 text-purple-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">
                        Contact Support
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        Get help when needed
                      </p>
                    </div>
                  </div>
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <ArrowRight className="h-4 w-4 text-purple-500" />
                  </div>
                </div>
              </Link>

              <div className="group cursor-pointer" onClick={() => {
                const tableElement = document.querySelector('[data-testid="unified-dashboard-table"]');
                if (tableElement) {
                  tableElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
              }}>
                <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white p-6 transition-all duration-200 hover:shadow-lg hover:scale-105 hover:border-orange-300">
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0">
                      <div className="p-3 bg-orange-50 rounded-xl group-hover:bg-orange-100 transition-colors">
                        <BarChart3 className="h-6 w-6 text-orange-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-semibold text-gray-900 group-hover:text-orange-600 transition-colors">
                        View Activity
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        Check your history
                      </p>
                    </div>
                  </div>
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <ArrowRight className="h-4 w-4 text-orange-500" />
                  </div>
                </div>
              </div>
            </div>

          </CardContent>
        </Card>
        
        {/* Unified Dashboard Table */}
        <UnifiedDashboardTable
          bookings={transformedBookings}
          payments={transformedPayments}
          loading={loadingBookings || loadingPayments}
          onFinishTrip={handleFinishTrip}
          onViewReceipt={handleViewReceipt}
          onReuploadPayment={handleReuploadPayment}
          onViewBookingDetails={handleViewBookingDetails}
          onCancelBooking={handleCancelBooking}
          onExtensionRequest={handleExtensionRequest}
          completingTrips={completingTrips}
          cancellingBookings={cancellingBookings}
        />

        {/* Booking Details Modal */}
        <BookingDetailsModal
          booking={selectedBookingForDetails}
          isOpen={!!selectedBookingForDetails}
          onClose={() => setSelectedBookingForDetails(null)}
          onDocumentUpload={handleDocumentUpload}
          onExtensionRequest={handleExtensionRequest}
        />

        {/* Payment Reupload Modal */}
        {selectedPaymentForReupload && (
          <PaymentReuploadModal
            bookingId={selectedPaymentForReupload}
            paymentId={payments.find(p => p.booking_id === selectedPaymentForReupload)?.id || ""}
            onSuccess={() => {
              setSelectedPaymentForReupload(null);
              fetchPaymentData();
              fetchBookingData();
            }}
          />
        )}

        {/* Document Upload Modal */}
        {documentUploadModal && (
          <DocumentUploadModal
            isOpen={!!documentUploadModal}
            onClose={() => setDocumentUploadModal(null)}
            bookingId={documentUploadModal.bookingId}
            documentType={documentUploadModal.documentType}
            documentLabel={getDocumentLabel(documentUploadModal.documentType)}
            onSuccess={() => {
              setDocumentUploadModal(null);
              // Refresh booking details if modal is open
              if (selectedBookingForDetails) {
                // Force refresh of the booking details modal by closing and reopening
                const currentBooking = selectedBookingForDetails;
                setSelectedBookingForDetails(null);
                setTimeout(() => setSelectedBookingForDetails(currentBooking), 100);
              }
            }}
          />
        )}

        {/* Extension Request Modal */}
        {extensionRequestModal && (
          <ExtensionRequestModal
            booking={{
              id: extensionRequestModal.id,
              status: extensionRequestModal.status,
              pickup_location: extensionRequestModal.pickup_location,
              dropoff_location: extensionRequestModal.dropoff_location,
              pickup_datetime: extensionRequestModal.pickup_datetime,
              dropoff_datetime: extensionRequestModal.dropoff_datetime,
              total_amount: extensionRequestModal.total_amount,
              cars: extensionRequestModal.cars ? {
                id: extensionRequestModal.cars.id,
                model: extensionRequestModal.cars.model,
                type: extensionRequestModal.cars.type,
                image_url: extensionRequestModal.cars.image_url || "/images/default-car.png",
                price_per_day: extensionRequestModal.cars.price_per_day
              } : null,
              customers: extensionRequestModal.customers
            }}
            isOpen={!!extensionRequestModal}
            onClose={() => setExtensionRequestModal(null)}
            onExtensionCreated={handleExtensionCreated}
          />
        )}
      </div>
    </PublicAppShell>
  );
}
