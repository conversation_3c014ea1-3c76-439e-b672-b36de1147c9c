-- Complete fix for all Supabase security warnings and errors
-- Run this script in your Supabase SQL Editor

-- ========================================
-- 1. FIX RLS ERROR - Enable RLS on vehicle_categories
-- ========================================

-- Enable RLS on vehicle_categories table
ALTER TABLE public.vehicle_categories ENABLE ROW LEVEL SECURITY;

-- Create policy to allow public read access (since categories are displayed to customers)
CREATE POLICY "Allow public read access to vehicle categories" ON public.vehicle_categories
FOR SELECT 
USING (is_active = true);

-- Create policy to allow admin full access
CREATE POLICY "Allow admin full access to vehicle categories" ON public.vehicle_categories
FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);

-- Grant necessary permissions
GRANT SELECT ON public.vehicle_categories TO anon, authenticated;
GRANT ALL ON public.vehicle_categories TO authenticated;

-- ========================================
-- 2. FIX FUNCTION SEARCH_PATH WARNINGS - Update all functions
-- ========================================

-- Drop existing functions first to avoid return type conflicts
DROP FUNCTION IF EXISTS public.get_vehicle_categories_with_stats();
DROP FUNCTION IF EXISTS public.get_cars_by_category(text);
DROP FUNCTION IF EXISTS public.update_category_pricing(uuid, numeric, numeric);
DROP FUNCTION IF EXISTS public.check_booking_documents_complete(uuid);
DROP FUNCTION IF EXISTS public.get_booking_document_status(uuid);
DROP FUNCTION IF EXISTS public.get_archived_cars();
DROP FUNCTION IF EXISTS public.can_archive_car(uuid);
DROP FUNCTION IF EXISTS public.car_has_archive_history(uuid);
DROP FUNCTION IF EXISTS public.get_car_archive_history(uuid);
DROP FUNCTION IF EXISTS public.sync_car_archive_status(uuid, boolean);
DROP FUNCTION IF EXISTS public.handle_new_user();
DROP FUNCTION IF EXISTS public.handle_updated_at();

-- 1. get_vehicle_categories_with_stats function
CREATE OR REPLACE FUNCTION public.get_vehicle_categories_with_stats()
RETURNS TABLE(
  id uuid,
  name text,
  type text,
  description text,
  min_price numeric,
  max_price numeric,
  image_url text,
  icon_color text,
  available_transmissions text[],
  display_order integer,
  is_active boolean,
  vehicle_count bigint,
  available_count bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    vc.id,
    vc.name,
    vc.type,
    vc.description,
    vc.min_price,
    vc.max_price,
    vc.image_url,
    vc.icon_color,
    vc.available_transmissions,
    vc.display_order,
    vc.is_active,
    COALESCE(car_counts.total_count, 0) as vehicle_count,
    COALESCE(car_counts.available_count, 0) as available_count
  FROM vehicle_categories vc
  LEFT JOIN (
    SELECT 
      c.category_id,
      COUNT(*) as total_count,
      COUNT(CASE WHEN c.status = 'Available' AND c.is_archived = false THEN 1 END) as available_count
    FROM cars c
    GROUP BY c.category_id
  ) car_counts ON vc.id = car_counts.category_id
  WHERE vc.is_active = true
  ORDER BY vc.display_order, vc.name;
END;
$$;

-- 2. get_cars_by_category function
CREATE OR REPLACE FUNCTION public.get_cars_by_category(category_type text)
RETURNS TABLE(
  id uuid,
  model text,
  type text,
  plate_number text,
  status text,
  condition text,
  fuel_capacity integer,
  fuel_type text,
  transmission text,
  seats integer,
  price_per_day numeric,
  image_url text,
  color text,
  category_id uuid
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    c.model,
    c.type,
    c.plate_number,
    c.status,
    c.condition,
    c.fuel_capacity,
    c.fuel_type,
    c.transmission,
    c.seats,
    c.price_per_day,
    c.image_url,
    c.color,
    c.category_id
  FROM cars c
  LEFT JOIN vehicle_categories vc ON c.category_id = vc.id
  WHERE c.is_archived = false 
    AND (c.type = category_type OR vc.type = category_type)
  ORDER BY c.model;
END;
$$;

-- 3. update_category_pricing function
CREATE OR REPLACE FUNCTION public.update_category_pricing(
  category_id_param uuid,
  new_min_price numeric,
  new_max_price numeric
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  UPDATE vehicle_categories 
  SET 
    min_price = new_min_price,
    max_price = new_max_price,
    updated_at = now()
  WHERE id = category_id_param;
  
  RETURN FOUND;
END;
$$;

-- 4. check_booking_documents_complete function
CREATE OR REPLACE FUNCTION public.check_booking_documents_complete(booking_id_param uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  required_docs text[] := ARRAY['drivers_license', 'government_id', 'proof_of_age', 'security_deposit_confirmation', 'proof_of_payment'];
  doc text;
  doc_exists boolean;
BEGIN
  FOREACH doc IN ARRAY required_docs
  LOOP
    SELECT EXISTS(
      SELECT 1 FROM booking_documents 
      WHERE booking_id = booking_id_param 
        AND document_type = doc 
        AND verification_status IN ('approved', 'pending')
    ) INTO doc_exists;
    
    IF NOT doc_exists THEN
      RETURN false;
    END IF;
  END LOOP;
  
  RETURN true;
END;
$$;

-- 5. get_booking_document_status function  
CREATE OR REPLACE FUNCTION public.get_booking_document_status(booking_id_param uuid)
RETURNS TABLE(
  document_type text,
  verification_status text,
  file_url text,
  created_at timestamptz
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    bd.document_type,
    bd.verification_status,
    bd.file_url,
    bd.created_at
  FROM booking_documents bd
  WHERE bd.booking_id = booking_id_param
  ORDER BY bd.created_at DESC;
END;
$$;

-- 6. get_archived_cars function
CREATE OR REPLACE FUNCTION public.get_archived_cars()
RETURNS TABLE(
  id uuid,
  model text,
  type text,
  plate_number text,
  status text,
  condition text,
  price_per_day numeric,
  image_url text,
  color text,
  created_at timestamptz,
  updated_at timestamptz
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    c.model,
    c.type,
    c.plate_number,
    c.status,
    c.condition,
    c.price_per_day,
    c.image_url,
    c.color,
    c.created_at,
    c.updated_at
  FROM cars c
  WHERE c.is_archived = true
  ORDER BY c.updated_at DESC;
END;
$$;

-- 7. can_archive_car function
CREATE OR REPLACE FUNCTION public.can_archive_car(car_id_param uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  active_bookings_count integer;
BEGIN
  SELECT COUNT(*) INTO active_bookings_count
  FROM bookings b
  WHERE b.car_id = car_id_param 
    AND b.status IN ('Pending', 'Active');
  
  RETURN active_bookings_count = 0;
END;
$$;

-- 8. car_has_archive_history function
CREATE OR REPLACE FUNCTION public.car_has_archive_history(car_id_param uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Since we're using soft delete, check if car exists and has been archived before
  RETURN EXISTS(
    SELECT 1 FROM cars 
    WHERE id = car_id_param 
      AND is_archived = true
  );
END;
$$;

-- 9. get_car_archive_history function
CREATE OR REPLACE FUNCTION public.get_car_archive_history(car_id_param uuid)
RETURNS TABLE(
  car_id uuid,
  model text,
  plate_number text,
  archived_status boolean,
  last_updated timestamptz
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id as car_id,
    c.model,
    c.plate_number,
    c.is_archived as archived_status,
    c.updated_at as last_updated
  FROM cars c
  WHERE c.id = car_id_param;
END;
$$;

-- 10. sync_car_archive_status function
CREATE OR REPLACE FUNCTION public.sync_car_archive_status(car_id_param uuid, archive_status boolean)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  UPDATE cars 
  SET 
    is_archived = archive_status,
    updated_at = now()
  WHERE id = car_id_param;
  
  RETURN FOUND;
END;
$$;

-- 11. handle_new_user function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'full_name', '')
  );
  RETURN new;
END;
$$;

-- 12. handle_updated_at function
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$;

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Verify RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'vehicle_categories';

-- Verify function security settings
SELECT 
  p.proname as function_name,
  p.prosecdef as security_definer,
  p.proconfig as config_settings
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
  AND p.proname IN (
    'get_vehicle_categories_with_stats',
    'get_cars_by_category', 
    'update_category_pricing',
    'check_booking_documents_complete',
    'get_booking_document_status',
    'get_archived_cars',
    'can_archive_car',
    'car_has_archive_history',
    'get_car_archive_history',
    'sync_car_archive_status',
    'handle_new_user',
    'handle_updated_at'
  );
