"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { DynamicGPSMap } from "@/components/admin/dynamic-gps-map"
import { useIsMobile } from "@/hooks/use-mobile"

export default function ResponsiveTestPage() {
  const isMobile = useIsMobile()
  const [activeBreakpoint, setActiveBreakpoint] = useState<string>("default")
  const [selectedCarId, setSelectedCarId] = useState<string | undefined>(undefined)

  const breakpoints = [
    { id: "default", name: "Default" },
    { id: "mobile-s", name: "Mobile S (320px)" },
    { id: "mobile-m", name: "Mobile M (375px)" },
    { id: "mobile-l", name: "Mobile L (425px)" },
    { id: "tablet", name: "Tablet (768px)" },
    { id: "laptop", name: "Lapt<PERSON> (1024px)" },
    { id: "laptop-l", name: "Laptop L (1440px)" },
  ]

  const getBreakpointWidth = (id: string): string => {
    switch (id) {
      case "mobile-s": return "320px"
      case "mobile-m": return "375px"
      case "mobile-l": return "425px"
      case "tablet": return "768px"
      case "laptop": return "1024px"
      case "laptop-l": return "1440px"
      default: return "100%"
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col gap-4">
        <h1 className="text-2xl font-bold">Admin Tracking Page - Responsive Test</h1>
        <p className="text-muted-foreground">
          Test the responsive behavior of the Admin Tracking Page across different device sizes.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Select Breakpoint</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeBreakpoint} onValueChange={setActiveBreakpoint}>
            <TabsList className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7">
              {breakpoints.map((breakpoint) => (
                <TabsTrigger key={breakpoint.id} value={breakpoint.id}>
                  {breakpoint.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </CardContent>
      </Card>

      <div className="border rounded-lg overflow-hidden bg-background">
        <div className="p-4 border-b flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
          </div>
          <div className="text-sm text-muted-foreground">
            {getBreakpointWidth(activeBreakpoint)}
          </div>
        </div>
        
        <div 
          className="transition-all duration-300 mx-auto border-x" 
          style={{ 
            width: activeBreakpoint === "default" ? "100%" : getBreakpointWidth(activeBreakpoint),
            height: "800px",
            maxWidth: "100%",
            overflow: "hidden"
          }}
        >
          <iframe 
            src="/admin/tracker" 
            className="w-full h-full"
            title="Admin Tracker Preview"
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Responsive Implementation Notes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold">Mobile & Tablet Improvements</h3>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Full-width GPS map for better visibility</li>
              <li>Bottom sheet modal for vehicle details and fleet overview</li>
              <li>KPI cards moved to Status modal (hidden on mobile/tablet)</li>
              <li>Compact header buttons for Refresh, Search, Status, and Details</li>
              <li>Touch-friendly map markers (≥44px)</li>
              <li>Optimized text readability with proper truncation</li>
              <li>No horizontal scrolling on any mobile size (320px-425px)</li>
              <li>Responsive padding and spacing for small screens</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold">Breakpoints</h3>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Mobile: &lt;768px</li>
              <li>Tablet: 768px - 1023px</li>
              <li>Desktop: ≥1024px</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
