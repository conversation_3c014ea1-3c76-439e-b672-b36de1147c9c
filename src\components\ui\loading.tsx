"use client"

import React from 'react'
import { cn } from "@/lib/utils"
import { Loader2, Car, MapPin, CreditCard, Search, Clock, CheckCircle2 } from 'lucide-react'

// Base loading component with customizable content
interface LoadingProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'spinner' | 'dots' | 'pulse'
  text?: string
  description?: string
  icon?: React.ComponentType<{ className?: string }>
  showProgress?: boolean
  progress?: number
  children?: React.ReactNode
}

export function Loading({
  className,
  size = 'md',
  variant = 'spinner',
  text = 'Loading',
  description,
  icon: Icon,
  showProgress = false,
  progress = 0,
  children
}: LoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  const containerSizeClasses = {
    sm: 'gap-2 text-sm',
    md: 'gap-3 text-base',
    lg: 'gap-4 text-lg'
  }

  return (
    <div 
      className={cn(
        "flex flex-col items-center justify-center p-6",
        containerSizeClasses[size],
        className
      )}
      role="status"
      aria-live="polite"
      aria-label={text}
    >
      {/* Loading indicator */}
      <div className="flex items-center gap-3">
        {variant === 'spinner' && (
          <div className="relative">
            {Icon ? (
              <Icon className={cn(sizeClasses[size], "text-blue-600 animate-pulse")} />
            ) : (
              <Loader2 className={cn(sizeClasses[size], "text-blue-600 animate-spin")} />
            )}
          </div>
        )}
        
        {variant === 'dots' && (
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
        )}

        {variant === 'pulse' && (
          <div className={cn(
            "bg-blue-600 rounded-full animate-pulse",
            sizeClasses[size]
          )}></div>
        )}
      </div>

      {/* Text content */}
      <div className="text-center space-y-1">
        <div className="font-medium text-gray-900">{text}</div>
        {description && (
          <div className="text-sm text-gray-600">{description}</div>
        )}
      </div>

      {/* Progress bar */}
      {showProgress && (
        <div className="w-full max-w-xs">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            ></div>
          </div>
          <div className="text-xs text-gray-500 mt-1 text-center">
            {Math.round(progress)}%
          </div>
        </div>
      )}

      {/* Custom children content */}
      {children}
    </div>
  )
}

// Specialized loading components for specific use cases

export function BookingLoading({ className }: { className?: string }) {
  return (
    <Loading
      className={className}
      size="md"
      variant="spinner"
      icon={CreditCard}
      text="Processing your booking"
      description="Please wait while we confirm your reservation..."
    />
  )
}

export function CarSearchLoading({ className }: { className?: string }) {
  return (
    <Loading
      className={className}
      size="md"
      variant="spinner"
      icon={Search}
      text="Finding available vehicles"
      description="Searching through our fleet for the best options..."
    />
  )
}

export function LocationLoading({ className }: { className?: string }) {
  return (
    <Loading
      className={className}
      size="md"
      variant="pulse"
      icon={MapPin}
      text="Loading location data"
      description="Fetching GPS information..."
    />
  )
}

export function VehicleLoading({ className }: { className?: string }) {
  return (
    <Loading
      className={className}
      size="md"
      variant="spinner"
      icon={Car}
      text="Loading vehicle details"
      description="Getting the latest information..."
    />
  )
}

export function PaymentLoading({ 
  className,
  progress 
}: { 
  className?: string
  progress?: number 
}) {
  return (
    <Loading
      className={className}
      size="md"
      variant="spinner"
      icon={CreditCard}
      text="Processing payment"
      description="Securely handling your transaction..."
      showProgress={progress !== undefined}
      progress={progress}
    />
  )
}

// Loading overlay component
interface LoadingOverlayProps {
  isVisible: boolean
  children: React.ReactNode
  loadingComponent?: React.ReactNode
  className?: string
}

export function LoadingOverlay({ 
  isVisible, 
  children, 
  loadingComponent,
  className 
}: LoadingOverlayProps) {
  return (
    <div className={cn("relative", className)}>
      {children}
      {isVisible && (
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
          {loadingComponent || <Loading text="Please wait..." />}
        </div>
      )}
    </div>
  )
}

// Loading skeleton for cards
export function CardSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("animate-pulse space-y-4 p-4 border border-gray-200 rounded-lg", className)}>
      <div className="flex items-center justify-between">
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        <div className="h-6 w-16 bg-gray-200 rounded"></div>
      </div>
      <div className="h-32 bg-gray-200 rounded"></div>
      <div className="grid grid-cols-3 gap-4">
        <div className="h-8 bg-gray-200 rounded"></div>
        <div className="h-8 bg-gray-200 rounded"></div>
        <div className="h-8 bg-gray-200 rounded"></div>
      </div>
      <div className="flex items-center justify-between">
        <div className="h-6 bg-gray-200 rounded w-1/3"></div>
        <div className="h-8 w-20 bg-gray-200 rounded"></div>
      </div>
    </div>
  )
}

// Loading states with timing control
export function useLoadingState(minimumDuration = 500) {
  const [isLoading, setIsLoading] = React.useState(false)
  const timeoutRef = React.useRef<NodeJS.Timeout>()

  const startLoading = React.useCallback(() => {
    setIsLoading(true)
  }, [])

  const stopLoading = React.useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsLoading(false)
    }, minimumDuration)
  }, [minimumDuration])

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return { isLoading, startLoading, stopLoading }
}
