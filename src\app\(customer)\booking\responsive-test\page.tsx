"use client";

import * as React from "react";
import { BookingSummaryStep } from "@/components/customer-side/booking/flow/booking-summary-step";
import type { BookingData } from "@/components/customer-side/booking/flow/booking-flow";

// Mock car data for testing
const mockSelectedCar = {
  id: "1",
  model: "Toyota Vios E 2016 AT",
  type: "Sedan" as const,
  seats: 5,
  transmission: "Automatic" as const,
  fuel_type: "Gas/Premium",
  fuel_capacity: 35,
  condition: "Good" as const,
  price_per_day: 600,
  image_url: "/customer_1.jpg",
  notes: "This vehicle requires a valid driver's license and insurance coverage.",
  status: "Available" as const,
  plate_number: "ABC-1001",
  is_archived: false,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
};

// Mock booking data
const initialBookingData = {
  selectedCar: mockSelectedCar,
  pickUpLocation: "#9 Lubnac, Vintar, Ilocos Norte",
  dropOffLocation: "#9 Lubnac, Vintar, Ilocos Norte", 
  pickUpDate: "2025-08-30",
  pickUpTime: "12:00",
  dropOffDate: "2025-09-12",
  dropOffTime: "07:00",
  pickupGarageChecked: true,
  dropoffSameAsPickupChecked: true,
  isSpecialService: false
} as Partial<BookingData>;

export default function BookingResponsiveTestPage() {
  const [bookingData, setBookingData] = React.useState<Partial<BookingData>>(initialBookingData);
  const [currentViewport, setCurrentViewport] = React.useState("lg");

  // Track viewport changes
  React.useEffect(() => {
    const updateViewport = () => {
      if (typeof window === 'undefined') return;
      
      const width = window.innerWidth;
      if (width < 375) setCurrentViewport("xs");
      else if (width < 425) setCurrentViewport("sm");
      else if (width < 768) setCurrentViewport("md");
      else if (width < 1024) setCurrentViewport("lg");
      else if (width < 1280) setCurrentViewport("xl");
      else setCurrentViewport("2xl");
    };

    updateViewport();
    if (typeof window !== 'undefined') {
      window.addEventListener("resize", updateViewport);
      return () => window.removeEventListener("resize", updateViewport);
    }
  }, []);

  const handleUpdate = (updates: Partial<BookingData>) => {
    setBookingData(prev => ({ ...prev, ...updates }));
  };

  const viewportInfo = {
    xs: "Mobile S (320px-374px)",
    sm: "Mobile M (375px-424px)", 
    md: "Mobile L (425px-767px)",
    lg: "Tablet (768px-1023px)",
    xl: "Laptop (1024px-1279px)",
    "2xl": "Desktop (1280px+)"
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Viewport Indicator */}
      <div className="sticky top-0 z-50 bg-blue-600 text-white px-4 py-2 text-center text-sm font-medium">
        Current Viewport: {viewportInfo[currentViewport as keyof typeof viewportInfo]} 
        {typeof window !== 'undefined' && `(${window.innerWidth}px × ${window.innerHeight}px)`}
      </div>

      {/* Page Header */}
      <div className="bg-white border-b border-gray-200 p-4 sm:p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Customer Booking Step 1 - Vehicle Card Redesign Test
          </h1>
          <p className="text-gray-600 mb-4">
            This page tests the redesigned vehicle card across all responsive breakpoints.
          </p>
          
          {/* Testing Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h2 className="font-semibold text-blue-900 mb-2">Testing Instructions:</h2>
            <ul className="text-sm text-blue-800 space-y-1">
              <li><strong>Mobile/Tablet (320px-1023px):</strong> Should display the original vertical card layout</li>
              <li><strong>Desktop (≥1024px):</strong> Should display the new horizontal reference-style layout</li>
              <li><strong>Functionality:</strong> All features should work (Change Vehicle button, pricing calculations)</li>
              <li><strong>Content:</strong> All original data fields should be visible</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto p-4 sm:p-6">
        <BookingSummaryStep
          bookingData={bookingData as BookingData}
          onUpdate={setBookingData}
        />
      </div>

      {/* Testing Data Display */}
      <div className="max-w-7xl mx-auto p-4 sm:p-6 mt-8">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Current Booking Data</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <div className="font-medium text-gray-700">Selected Vehicle:</div>
              <div className="text-gray-600">{bookingData.selectedCar?.model}</div>
            </div>
            <div>
              <div className="font-medium text-gray-700">Daily Rate:</div>
              <div className="text-gray-600">₱{bookingData.selectedCar?.price_per_day}</div>
            </div>
            <div>
              <div className="font-medium text-gray-700">Pickup Location:</div>
              <div className="text-gray-600">{bookingData.pickUpLocation}</div>
            </div>
            <div>
              <div className="font-medium text-gray-700">Return Location:</div>
              <div className="text-gray-600">{bookingData.dropOffLocation}</div>
            </div>
            <div>
              <div className="font-medium text-gray-700">Pickup Date/Time:</div>
              <div className="text-gray-600">{bookingData.pickUpDate} at {bookingData.pickUpTime}</div>
            </div>
            <div>
              <div className="font-medium text-gray-700">Return Date/Time:</div>
              <div className="text-gray-600">{bookingData.dropOffDate} at {bookingData.dropOffTime}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Breakpoint Reference */}
      <div className="max-w-7xl mx-auto p-4 sm:p-6 mt-4">
        <div className="bg-gray-100 rounded-lg border border-gray-300 p-4">
          <h3 className="font-semibold text-gray-900 mb-2">Responsive Breakpoints Reference:</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 text-xs">
            <div className="bg-white p-2 rounded border">
              <div className="font-medium">xs</div>
              <div className="text-gray-600">320px-374px</div>
              <div className="text-gray-500">Mobile S</div>
            </div>
            <div className="bg-white p-2 rounded border">
              <div className="font-medium">sm</div>
              <div className="text-gray-600">375px-424px</div>
              <div className="text-gray-500">Mobile M</div>
            </div>
            <div className="bg-white p-2 rounded border">
              <div className="font-medium">md</div>
              <div className="text-gray-600">425px-767px</div>
              <div className="text-gray-500">Mobile L</div>
            </div>
            <div className="bg-white p-2 rounded border">
              <div className="font-medium">lg</div>
              <div className="text-gray-600">768px-1023px</div>
              <div className="text-gray-500">Tablet</div>
            </div>
            <div className="bg-white p-2 rounded border">
              <div className="font-medium">xl</div>
              <div className="text-gray-600">1024px-1279px</div>
              <div className="text-gray-500">Laptop</div>
            </div>
            <div className="bg-white p-2 rounded border">
              <div className="font-medium">2xl</div>
              <div className="text-gray-600">1280px+</div>
              <div className="text-gray-500">Desktop</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
