"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { PaymentCard } from "@/components/admin/payment-card"
import { PaymentDetailsModal } from "@/components/admin/payment-details-modal"
import type { AdminPayment } from "@/app/admin/payments/actions/payment-actions"

// Mock payment data for testing
const mockPayments: AdminPayment[] = [
  {
    id: "1",
    paymentRef: "PAY-001",
    bookingRef: "BK-2024-001",
    bookingId: "BK-2024-001",
    renterName: "<PERSON>",
    renterEmail: "<EMAIL>",
    renterPhone: "+63 ************",
    amount: 15750.50,
    method: "GCash",
    status: "Completed",
    transactionDate: new Date().toISOString(),
    proofOfPaymentUrl: "https://example.com/receipt1.jpg",
  },
  {
    id: "2",
    paymentRef: "PAY-002",
    bookingRef: "BK-2024-002",
    bookingId: "BK-2024-002",
    renterName: "Maria Santos",
    renterEmail: "<EMAIL>",
    renterPhone: "+63 ************",
    amount: 8500.00,
    method: "Bank Transfer",
    status: "Pending",
    transactionDate: new Date(Date.now() - ********).toISOString(),
    proofOfPaymentUrl: undefined,
  },
  {
    id: "3",
    paymentRef: "PAY-003",
    bookingRef: "BK-2024-003",
    bookingId: "BK-2024-003",
    renterName: "Robert Johnson",
    renterEmail: "<EMAIL>",
    renterPhone: "N/A",
    amount: 23500.75,
    method: "Remittance Center",
    status: "Completed",
    transactionDate: new Date(Date.now() - *********).toISOString(),
    proofOfPaymentUrl: "https://example.com/receipt3.jpg",
  }
]

export default function PaymentsMobileResponsiveTestPage() {
  const [selectedPayment, setSelectedPayment] = React.useState<AdminPayment | null>(null)
  const [isModalOpen, setIsModalOpen] = React.useState(false)
  const [currentBreakpoint, setCurrentBreakpoint] = React.useState("")
  const [viewportWidth, setViewportWidth] = React.useState(0)

  // Track viewport size
  React.useEffect(() => {
    const updateViewport = () => {
      const width = window.innerWidth
      setViewportWidth(width)
      
      if (width >= 1024) {
        setCurrentBreakpoint("Desktop (≥1024px)")
      } else if (width >= 768) {
        setCurrentBreakpoint("Tablet (768px-1023px)")
      } else if (width >= 425) {
        setCurrentBreakpoint("Mobile L (425px-767px)")
      } else if (width >= 375) {
        setCurrentBreakpoint("Mobile M (375px-424px)")
      } else if (width >= 320) {
        setCurrentBreakpoint("Mobile S (320px-374px)")
      } else {
        setCurrentBreakpoint("Extra Small (<320px)")
      }
    }

    updateViewport()
    window.addEventListener('resize', updateViewport)
    return () => window.removeEventListener('resize', updateViewport)
  }, [])

  const handleViewDetails = (payment: AdminPayment) => {
    setSelectedPayment(payment)
    setIsModalOpen(true)
  }

  const breakpointTests = [
    { width: 320, name: "Mobile S", description: "iPhone SE, older Android phones" },
    { width: 375, name: "Mobile M", description: "iPhone 12/13/14, common mobile" },
    { width: 425, name: "Mobile L", description: "iPhone Plus, large phones" },
  ]

  const resizeViewport = (width: number) => {
    // This is just for demo - actual resizing would need browser dev tools
    alert(`Please resize your browser to ${width}px width to test this breakpoint, or use browser dev tools to simulate the viewport.`)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Fixed viewport indicator */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-blue-600 text-white p-2 text-center text-sm">
        <div className="flex flex-col xs:flex-row xs:justify-center xs:items-center gap-1 xs:gap-4">
          <span className="font-semibold">{currentBreakpoint}</span>
          <span>Width: {viewportWidth}px</span>
        </div>
      </div>

      <div className="pt-16 p-3 xs:p-4 lg:p-6 space-y-4 xs:space-y-6">
        {/* Test Information */}
        <Card>
          <CardHeader className="pb-3 xs:pb-4">
            <CardTitle className="text-lg xs:text-xl">Mobile Responsive Test - Admin Payments Page</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-sm xs:text-base mb-2">Target Mobile Breakpoints:</h3>
                <div className="grid grid-cols-1 xs:grid-cols-3 gap-2 xs:gap-3">
                  {breakpointTests.map((test) => (
                    <div key={test.width} className="text-center">
                      <Button
                        variant="secondary"
                        size="sm"
                        className="w-full h-auto p-2 xs:p-3 flex flex-col gap-1"
                        onClick={() => resizeViewport(test.width)}
                      >
                        <span className="font-semibold text-xs xs:text-sm">{test.name}</span>
                        <span className="text-xs text-muted-foreground">{test.width}px</span>
                        <span className="text-xs text-muted-foreground">{test.description}</span>
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              <div className="border-t pt-3 xs:pt-4">
                <h3 className="font-semibold text-sm xs:text-base mb-2">Mobile UI Improvements:</h3>
                <ul className="text-xs xs:text-sm text-muted-foreground space-y-1">
                  <li>• Optimized typography and spacing for mobile screens</li>
                  <li>• Grid-based filter controls with proper touch targets (≥48px)</li>
                  <li>• Improved search input with responsive sizing</li>
                  <li>• Enhanced payment cards with better mobile layout</li>
                  <li>• Mobile-optimized pagination with full-width buttons</li>
                  <li>• Responsive modal design with mobile-friendly interactions</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Cards Test */}
        <Card>
          <CardHeader className="pb-3 xs:pb-4">
            <CardTitle className="text-lg xs:text-xl">Payment Cards Mobile Layout</CardTitle>
            <p className="text-xs xs:text-sm text-muted-foreground mt-1">
              Test payment cards across different mobile screen sizes
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 xs:space-y-3">
              {mockPayments.map((payment) => (
                <PaymentCard key={payment.id} payment={payment} />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Modal Test */}
        <Card>
          <CardHeader className="pb-3 xs:pb-4">
            <CardTitle className="text-lg xs:text-xl">Payment Details Modal Test</CardTitle>
            <p className="text-xs xs:text-sm text-muted-foreground mt-1">
              Test modal responsiveness on mobile devices
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 xs:grid-cols-3 gap-2 xs:gap-3">
              {mockPayments.map((payment) => (
                <Button
                  key={payment.id}
                  variant="secondary"
                  className="w-full h-auto p-3 flex flex-col gap-1 text-left"
                  onClick={() => handleViewDetails(payment)}
                >
                  <span className="font-semibold text-xs xs:text-sm truncate w-full">
                    {payment.renterName}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    ₱{payment.amount.toLocaleString('en-PH', { minimumFractionDigits: 2 })}
                  </span>
                  <Badge className="text-xs w-fit" variant="secondary">
                    {payment.method}
                  </Badge>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Testing Checklist */}
        <Card>
          <CardHeader className="pb-3 xs:pb-4">
            <CardTitle className="text-lg xs:text-xl">Mobile Testing Checklist</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <h3 className="font-semibold text-sm xs:text-base mb-2">✅ Layout & Spacing (320px-425px)</h3>
                <ul className="text-xs xs:text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• No horizontal scrolling or overflow</li>
                  <li>• Proper responsive typography scaling</li>
                  <li>• Adequate padding and margins for touch interaction</li>
                  <li>• Cards and components fit within viewport</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-sm xs:text-base mb-2">✅ Touch Targets & Navigation</h3>
                <ul className="text-xs xs:text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• All interactive elements ≥48px height</li>
                  <li>• Filter dropdowns are touch-friendly</li>
                  <li>• Pagination buttons are easily tappable</li>
                  <li>• Modal close buttons are accessible</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-sm xs:text-base mb-2">✅ Content & Readability</h3>
                <ul className="text-xs xs:text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• Text is legible at small screen sizes</li>
                  <li>• Payment information is clearly displayed</li>
                  <li>• Truncation works properly for long text</li>
                  <li>• Badge and status indicators are visible</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-sm xs:text-base mb-2">✅ Interactive Elements</h3>
                <ul className="text-xs xs:text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• Search input responds properly to focus</li>
                  <li>• Filter dropdowns open and close smoothly</li>
                  <li>• Modal opens and displays content correctly</li>
                  <li>• All buttons provide visual feedback on tap</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Details Modal */}
      {selectedPayment && (
        <PaymentDetailsModal
          payment={selectedPayment}
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false)
            setSelectedPayment(null)
          }}
        />
      )}
    </div>
  )
}
