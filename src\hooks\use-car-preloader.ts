"use client"

import { useEffect } from "react"
import { usePathname } from "next/navigation"
import { preloadCriticalResources, preloadPopularCarImages, shouldLoadHighQuality } from "@/lib/performance/bundle-analyzer"
import type { Car } from "@/lib/types"

interface UseCarPreloaderProps {
  cars?: Car[]
  priority?: boolean
}

export function useCarPreloader({ cars = [], priority = false }: UseCarPreloaderProps) {
  const pathname = usePathname()

  useEffect(() => {
    if (typeof window === 'undefined') return

    // Get car image URLs from your Supabase data
    const carImageUrls = cars
      .filter(car => car.image_url && !car.is_archived)
      .map(car => car.image_url!)
      .slice(0, 5) // Limit to first 5 cars for performance

    // Preload critical resources based on current route
    preloadCriticalResources(pathname, carImageUrls)

    // Only preload additional images on fast networks and for priority routes
    if (shouldLoadHighQuality() && (priority || pathname.includes('/customer'))) {
      preloadPopularCarImages(carImageUrls)
    }
  }, [cars, pathname, priority])

  return {
    isPreloading: cars.length > 0,
    carCount: cars.length
  }
}

// Utility hook for components that need to trigger preloading manually
export function useManualPreloader() {
  const preloadImages = (imageUrls: string[]) => {
    if (!shouldLoadHighQuality()) return

    preloadPopularCarImages(imageUrls)
  }

  const preloadCarImages = (cars: Car[]) => {
    const imageUrls = cars
      .filter(car => car.image_url && !car.is_archived)
      .map(car => car.image_url!)
    
    preloadImages(imageUrls)
  }

  return {
    preloadImages,
    preloadCarImages
  }
}
