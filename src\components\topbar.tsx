"use client"

import Image from "next/image"
import Link from "next/link"
import { useCustomerAuth, CustomerAuthContext } from "./auth/customer-auth-context"
import { useEffect, useState, useContext } from "react"
import { cn } from "@/lib/utils"
import { MobileNav } from "./nav/mobile-nav"

export function Topbar() {
  // Check if we're within a CustomerAuthProvider
  const context = useContext(CustomerAuthContext)
  
  // Use auth safely - default to null if not in provider
  const user = context ? useCustomerAuth().user : null
  
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <header className={cn(
      "h-16 border-b bg-white transition-all duration-200 sticky top-0 z-40",
      isMounted && isScrolled && "shadow-md bg-white/95 backdrop-blur-sm border-gray-300"
    )}>
      <div className="h-full flex items-center gap-3 px-4 md:px-6">
        {/* Mobile Navigation */}
        <MobileNav />
        
        {/* Logo */}
        <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
          <div className="relative w-8 h-8">
            <Image
              src="/ollie_logo.svg"
              alt="Ollie's Rent A Car"
              width={32}
              height={32}
              className="object-contain"
            />
          </div>
          <div className="hidden sm:block">
            <span className="font-bold text-gray-900">Ollie's</span>
          </div>
        </Link>
        
        {/* Spacer to push CTAs to the right */}
        <div className="flex-1"></div>

        {user ? (
          <div className="flex items-center gap-2">
            <Image
              src="/avatar.svg"
              alt="User avatar"
              width={32}
              height={32}
              className="rounded-full bg-gray-100 p-1"
            />
            <div className="hidden sm:block">
              <div className="text-sm font-medium">{user.email}</div>
              <div className="text-xs text-muted-foreground">{user.email} • {user.role}</div>
            </div>
          </div>
        ) : (
          <Link href="/customer/login" className="text-sm text-blue-600 hover:underline">
            Sign in
          </Link>
        )}
      </div>
    </header>
  )
}
