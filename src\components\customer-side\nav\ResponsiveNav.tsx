"use client"

import React from 'react'
import { cn } from '@/lib/utils'
import { BottomNav } from './BottomNav'
import { TopNav, DesktopNav } from './TopNav'

interface ResponsiveNavProps {
  className?: string
}

export function ResponsiveNav({ className }: ResponsiveNavProps) {
  return (
    <div className={cn("responsive-navigation overflow-visible", className)}>
      {/* Mobile: Bottom Tab Bar (≤640px) */}
      <div className="sm:hidden">
        <BottomNav />
      </div>
      
      {/* Tablet: Priority+ Top Navigation (641-1023px) */}
      <div className="hidden sm:block lg:hidden overflow-visible">
        <TopNav maxVisibleItems={4} />
      </div>
      
      {/* Desktop: Full Top Navigation (≥1024px) */}
      <div className="hidden lg:block overflow-visible">
        <DesktopNav />
      </div>
    </div>
  )
}

// Hook to determine current navigation mode
export function useNavigationMode() {
  const [mode, setMode] = React.useState<'mobile' | 'tablet' | 'desktop'>('desktop')
  
  React.useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      if (width < 640) {
        setMode('mobile')
      } else if (width < 1024) {
        setMode('tablet')
      } else {
        setMode('desktop')
      }
    }
    
    // Initial check
    checkScreenSize()
    
    // Listen for resize events
    window.addEventListener('resize', checkScreenSize)
    
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])
  
  return mode
}

// Utility component to add bottom spacing when bottom nav is active
export function BottomNavSpacer() {
  return (
    <div 
      className="sm:hidden h-16 w-full flex-shrink-0" 
      style={{ paddingBottom: 'env(safe-area-inset-bottom)' }}
      aria-hidden="true"
    />
  )
}

export default ResponsiveNav
