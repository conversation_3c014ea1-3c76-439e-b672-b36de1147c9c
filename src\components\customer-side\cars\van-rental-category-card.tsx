"use client";

import * as React from "react";
import {
  Card,
  CardContent,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useRouter } from "next/navigation";
import { Users, MapPin, Clock } from "lucide-react";
import { CUSTOMER_PATHS, buildBookingFlowUrl } from "@/lib/customer-paths";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { AuthRequiredModal } from "@/components/customer-side/auth/auth-required-modal";
import { SPECIAL_SERVICES } from "@/lib/pricing-constants";

interface VanRentalCategoryCardProps {
  onSelect?: () => void;
}

export function VanRentalCategoryCard({ onSelect }: VanRentalCategoryCardProps) {
  const router = useRouter();
  const { user } = useCustomerAuth();
  const [showAuthModal, setShowAuthModal] = React.useState(false);

  const handleSelect = () => {
    if (onSelect) {
      onSelect();
      return;
    }
    
    // Check if user is authenticated
    if (!user) {
      setShowAuthModal(true);
      return;
    }
    
    // Navigate to booking flow with special service flag
    const bookingUrl = buildBookingFlowUrl({ 
      specialService: "true",
      serviceType: "van-with-driver"
    });
    router.push(bookingUrl);
  };

  return (
    <>
      <Card
        variant="elevated"
        className="van-rental-category-card overflow-hidden cursor-pointer group flex flex-col h-full w-full max-w-sm mx-auto border-2 border-orange-200 bg-gradient-to-br from-orange-50 to-yellow-50"
        onClick={handleSelect}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            handleSelect();
          }
        }}
        tabIndex={0}
        role="button"
        aria-label="Book van rental with driver"
      >
        <CardContent className="p-0 flex flex-col h-full">
          {/* Card Header with Image */}
          <div className="relative h-64 w-full overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/30 z-10" />
            <div
              className="absolute inset-0 bg-cover bg-center z-0 transition-transform duration-500 group-hover:scale-105"
              style={{
                backgroundImage:
                  "url('/toyota_gl_grandia_2011_mt.png')",
                backgroundPosition: 'center 40%',
                backgroundSize: '100% auto'
              }}
            />
            <div className="absolute top-4 right-4 z-20">
              <Badge
                variant="secondary"
                className="bg-orange-500 hover:bg-orange-600 text-white font-semibold px-3 py-1.5 text-sm shadow-md"
              >
                Special Service
              </Badge>
            </div>
          </div>

          {/* Card Content */}
          <div className="p-5 flex flex-col h-full">
            <CardTitle className="text-xl font-bold mb-2 text-gray-900">
              Van Rental with Driver
            </CardTitle>
            <CardDescription className="text-gray-600 mb-2">
              Premium van rental with professional driver for group travels, events, and special occasions.
            </CardDescription>
            <div className="mb-4 flex items-center">
              <span className="text-orange-600 font-bold text-lg">{SPECIAL_SERVICES.VAN_WITH_DRIVER.CURRENCY}{SPECIAL_SERVICES.VAN_WITH_DRIVER.BASE_PRICE}</span>
              <span className="text-gray-600 text-sm ml-1">starting price</span>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 gap-3 mb-6">
              <div className="flex items-center text-gray-700 bg-orange-50 p-2 rounded-md">
                <Users className="h-5 w-5 mr-2 text-orange-500 flex-shrink-0" />
                <span className="text-sm font-medium">Up to 15 passengers</span>
              </div>
              <div className="flex items-center text-gray-700 bg-orange-50 p-2 rounded-md">
                <MapPin className="h-5 w-5 mr-2 text-orange-500 flex-shrink-0" />
                <span className="text-sm font-medium">Local and long-distance travel</span>
              </div>
              <div className="flex items-center text-gray-700 bg-orange-50 p-2 rounded-md">
                <Clock className="h-5 w-5 mr-2 text-orange-500 flex-shrink-0" />
                <span className="text-sm font-medium">Hourly or daily rates available</span>
              </div>
            </div>

            {/* Contact Button */}
            <div className="mt-auto">
              <Button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleSelect();
                }}
                className="w-full bg-orange-600 hover:bg-orange-700 text-white font-semibold py-3 rounded-full transition-all duration-200 text-sm tracking-wide shadow-lg hover:shadow-xl"
              >
                AVAIL NOW
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Authentication Modal */}
      <AuthRequiredModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        action="book this special service"
        carModel="Van with Driver"
      />
    </>
  );
}
