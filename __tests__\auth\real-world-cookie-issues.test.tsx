import React from 'react'
import { render, screen, waitFor, cleanup } from '@testing-library/react'
import '@testing-library/jest-dom'
import { CustomerAuthProvider, useCustomerAuth } from '../../src/components/auth/customer-auth-context'
import { AdminAuthProvider, useAdminAuth } from '../../src/components/auth/admin-auth-context'

/**
 * Real-World Authentication Cookie Issues Test
 * 
 * This test suite focuses on identifying actual cookie inconsistencies
 * that occur when tabs are closed and reopened, without heavy mocking.
 * It tests the real behavior of your authentication system.
 */

// Test component to observe auth state
const AuthStateObserver = ({ 
  type, 
  onStateChange 
}: { 
  type: 'customer' | 'admin'
  onStateChange: (state: any) => void 
}) => {
  if (type === 'customer') {
    const auth = useCustomerAuth()
    React.useEffect(() => {
      onStateChange({
        user: auth.user,
        session: auth.session,
        profile: auth.profile,
        isCustomer: auth.isCustomer
      })
    }, [auth.user, auth.session, auth.profile, auth.isCustomer, onStateChange])
    
    return (
      <div data-testid="customer-observer">
        <div data-testid="customer-user">{auth.user?.email || 'No user'}</div>
        <div data-testid="customer-session">{auth.session ? 'Has session' : 'No session'}</div>
        <div data-testid="customer-role">{auth.profile?.role || 'No role'}</div>
        <div data-testid="customer-is-customer">{auth.isCustomer.toString()}</div>
      </div>
    )
  } else {
    const auth = useAdminAuth()
    React.useEffect(() => {
      onStateChange({
        user: auth.user,
        session: auth.session,
        profile: auth.profile,
        isAdmin: auth.isAdmin,
        loading: auth.loading
      })
    }, [auth.user, auth.session, auth.profile, auth.isAdmin, auth.loading, onStateChange])
    
    return (
      <div data-testid="admin-observer">
        <div data-testid="admin-user">{auth.user?.email || 'No user'}</div>
        <div data-testid="admin-session">{auth.session ? 'Has session' : 'No session'}</div>
        <div data-testid="admin-role">{auth.profile?.role || 'No role'}</div>
        <div data-testid="admin-is-admin">{auth.isAdmin.toString()}</div>
        <div data-testid="admin-loading">{auth.loading.toString()}</div>
      </div>
    )
  }
}

// Helper to check localStorage state
const getStorageState = () => {
  const customerKey = 'sb-customer-auth-token'
  const adminKey = 'sb-admin-auth-token'
  
  return {
    customer: localStorage.getItem(customerKey),
    admin: localStorage.getItem(adminKey),
    allKeys: Object.keys(localStorage).filter(key => key.includes('sb-')),
    localStorage: { ...localStorage }
  }
}

describe('Real-World Authentication Cookie Issues', () => {
  let customerStates: any[] = []
  let adminStates: any[] = []
  
  const trackCustomerState = (state: any) => {
    customerStates.push({ ...state, timestamp: Date.now() })
  }
  
  const trackAdminState = (state: any) => {
    adminStates.push({ ...state, timestamp: Date.now() })
  }
  
  beforeEach(() => {
    // Clear all browser storage
    localStorage.clear()
    sessionStorage.clear()
    
    // Reset state tracking
    customerStates = []
    adminStates = []
    
    // Clear any existing auth tokens
    Object.keys(localStorage).forEach(key => {
      if (key.includes('sb-')) {
        localStorage.removeItem(key)
      }
    })
  })
  
  afterEach(() => {
    cleanup()
    localStorage.clear()
    sessionStorage.clear()
  })

  test('ISSUE DETECTOR: Storage key isolation verification', async () => {
    console.log('\n=== STORAGE KEY ISOLATION TEST ===')
    
    const TestWrapper = () => (
      <div>
        <CustomerAuthProvider>
          <AuthStateObserver type="customer" onStateChange={trackCustomerState} />
        </CustomerAuthProvider>
        <AdminAuthProvider>
          <AuthStateObserver type="admin" onStateChange={trackAdminState} />
        </AdminAuthProvider>
      </div>
    )
    
    render(<TestWrapper />)
    
    // Wait for initial auth state
    await waitFor(() => {
      expect(screen.getByTestId('customer-observer')).toBeInTheDocument()
      expect(screen.getByTestId('admin-observer')).toBeInTheDocument()
    })
    
    // Check storage state
    const storageState = getStorageState()
    console.log('Storage Keys Found:', storageState.allKeys)
    console.log('Customer Token:', storageState.customer ? 'Present' : 'Absent')
    console.log('Admin Token:', storageState.admin ? 'Present' : 'Absent')
    
    // Verify separate storage keys are being used
    const hasCustomerKey = storageState.allKeys.some(key => key.includes('customer'))
    const hasAdminKey = storageState.allKeys.some(key => key.includes('admin'))
    
    console.log('Uses separate customer key:', hasCustomerKey)
    console.log('Uses separate admin key:', hasAdminKey)
    
    // Issue detection
    const issues: string[] = []
    
    if (!hasCustomerKey && !hasAdminKey) {
      issues.push('Both contexts appear to use the same storage key')
    }
    
    if (storageState.customer === storageState.admin && storageState.customer !== null) {
      issues.push('Customer and admin have identical storage values')
    }
    
    if (issues.length > 0) {
      console.error('🚨 STORAGE KEY ISSUES DETECTED:')
      issues.forEach(issue => console.error(`  - ${issue}`))
    } else {
      console.log('✅ Storage key isolation appears correct')
    }
    
    expect(issues.length).toBe(0)
  })

  test('ISSUE DETECTOR: Tab close/reopen simulation', async () => {
    console.log('\n=== TAB CLOSE/REOPEN SIMULATION ===')
    
    // Simulate having auth data in storage (like a previous session)
    const mockCustomerToken = JSON.stringify({
      access_token: 'customer-token-123',
      refresh_token: 'customer-refresh-123',
      user: { email: '<EMAIL>', id: 'customer-id' }
    })
    
    const mockAdminToken = JSON.stringify({
      access_token: 'admin-token-456',
      refresh_token: 'admin-refresh-456',
      user: { email: '<EMAIL>', id: 'admin-id' }
    })
    
    // Set tokens as if user had previous sessions
    localStorage.setItem('sb-customer-auth-token', mockCustomerToken)
    localStorage.setItem('sb-admin-auth-token', mockAdminToken)
    
    console.log('Initial storage setup complete')
    console.log('Customer token preset:', !!localStorage.getItem('sb-customer-auth-token'))
    console.log('Admin token preset:', !!localStorage.getItem('sb-admin-auth-token'))
    
    // Simulate "opening a new tab" by rendering the auth providers
    const { unmount } = render(
      <div>
        <CustomerAuthProvider>
          <AuthStateObserver type="customer" onStateChange={trackCustomerState} />
        </CustomerAuthProvider>
        <AdminAuthProvider>
          <AuthStateObserver type="admin" onStateChange={trackAdminState} />
        </AdminAuthProvider>
      </div>
    )
    
    // Wait for auth initialization
    await waitFor(() => {
      expect(customerStates.length).toBeGreaterThan(0)
      expect(adminStates.length).toBeGreaterThan(0)
    }, { timeout: 3000 })
    
    console.log('Auth states after tab "reopen":')
    console.log('Customer states:', customerStates.length)
    console.log('Admin states:', adminStates.length)
    
    // Check for cross-contamination
    const finalStorageState = getStorageState()
    const latestCustomerState = customerStates[customerStates.length - 1]
    const latestAdminState = adminStates[adminStates.length - 1]
    
    console.log('Final customer state:', latestCustomerState)
    console.log('Final admin state:', latestAdminState)
    
    // Issue detection
    const issues: string[] = []
    
    // Check if customer context shows admin data
    if (latestCustomerState?.user?.email && latestCustomerState.user.email.includes('admin')) {
      issues.push('Customer context contains admin user data')
    }
    
    // Check if admin context shows customer data
    if (latestAdminState?.user?.email && latestAdminState.user.email.includes('customer')) {
      issues.push('Admin context contains customer user data')
    }
    
    // Check if storage tokens got mixed up
    if (finalStorageState.customer === finalStorageState.admin && finalStorageState.customer !== null) {
      issues.push('Storage tokens are identical after tab reopen')
    }
    
    // Check if tokens disappeared unexpectedly
    if (!finalStorageState.customer && !finalStorageState.admin) {
      issues.push('All auth tokens disappeared after tab reopen')
    }
    
    if (issues.length > 0) {
      console.error('🚨 TAB REOPEN ISSUES DETECTED:')
      issues.forEach(issue => console.error(`  - ${issue}`))
    } else {
      console.log('✅ Tab close/reopen behavior appears correct')
    }
    
    // Simulate closing tab
    unmount()
    
    // Check if tokens persist after unmount
    const persistedStorage = getStorageState()
    console.log('Storage after tab close:')
    console.log('Customer token persisted:', !!persistedStorage.customer)
    console.log('Admin token persisted:', !!persistedStorage.admin)
    
    expect(issues.length).toBe(0)
  })

  test('ISSUE DETECTOR: Cross-context contamination check', async () => {
    console.log('\n=== CROSS-CONTEXT CONTAMINATION CHECK ===')
    
    // Set up conflicting auth states to see if they get mixed
    localStorage.setItem('sb-customer-auth-token', JSON.stringify({
      access_token: 'customer-specific-token',
      user: { email: '<EMAIL>', role: 'customer' }
    }))
    
    localStorage.setItem('sb-admin-auth-token', JSON.stringify({
      access_token: 'admin-specific-token',
      user: { email: '<EMAIL>', role: 'admin' }
    }))
    
    render(
      <div>
        <CustomerAuthProvider>
          <AuthStateObserver type="customer" onStateChange={trackCustomerState} />
        </CustomerAuthProvider>
        <AdminAuthProvider>
          <AuthStateObserver type="admin" onStateChange={trackAdminState} />
        </AdminAuthProvider>
      </div>
    )
    
    await waitFor(() => {
      expect(customerStates.length).toBeGreaterThan(0)
      expect(adminStates.length).toBeGreaterThan(0)
    }, { timeout: 3000 })
    
    const issues: string[] = []
    const latestCustomer = customerStates[customerStates.length - 1]
    const latestAdmin = adminStates[adminStates.length - 1]
    
    // Check for role contamination
    if (latestCustomer?.profile?.role === 'admin') {
      issues.push('Customer context shows admin role')
    }
    
    if (latestAdmin?.profile?.role === 'customer') {
      issues.push('Admin context shows customer role')
    }
    
    // Check for session contamination
    if (latestCustomer?.session && latestAdmin?.session) {
      // If both have sessions, they should be different
      const customerSession = JSON.stringify(latestCustomer.session)
      const adminSession = JSON.stringify(latestAdmin.session)
      
      if (customerSession === adminSession) {
        issues.push('Customer and admin contexts share identical sessions')
      }
    }
    
    console.log('Cross-contamination check results:')
    console.log('Customer role:', latestCustomer?.profile?.role || 'none')
    console.log('Admin role:', latestAdmin?.profile?.role || 'none')
    console.log('Customer has session:', !!latestCustomer?.session)
    console.log('Admin has session:', !!latestAdmin?.session)
    
    if (issues.length > 0) {
      console.error('🚨 CROSS-CONTAMINATION ISSUES DETECTED:')
      issues.forEach(issue => console.error(`  - ${issue}`))
    } else {
      console.log('✅ No cross-contamination detected')
    }
    
    expect(issues.length).toBe(0)
  })

  test('ISSUE DETECTOR: Rapid context switching simulation', async () => {
    console.log('\n=== RAPID CONTEXT SWITCHING TEST ===')
    
    // Simulate rapidly switching between customer and admin views
    const TestApp = ({ showCustomer }: { showCustomer: boolean }) => (
      <div>
        {showCustomer ? (
          <CustomerAuthProvider>
            <AuthStateObserver type="customer" onStateChange={trackCustomerState} />
          </CustomerAuthProvider>
        ) : (
          <AdminAuthProvider>
            <AuthStateObserver type="admin" onStateChange={trackAdminState} />
          </AdminAuthProvider>
        )}
      </div>
    )
    
    // Set up initial auth state
    localStorage.setItem('sb-customer-auth-token', JSON.stringify({
      access_token: 'customer-token',
      user: { email: '<EMAIL>' }
    }))
    
    localStorage.setItem('sb-admin-auth-token', JSON.stringify({
      access_token: 'admin-token',
      user: { email: '<EMAIL>' }
    }))
    
    // Rapidly switch contexts
    let { rerender } = render(<TestApp showCustomer={true} />)
    await waitFor(() => expect(customerStates.length).toBeGreaterThan(0))
    
    rerender(<TestApp showCustomer={false} />)
    await waitFor(() => expect(adminStates.length).toBeGreaterThan(0))
    
    rerender(<TestApp showCustomer={true} />)
    await waitFor(() => expect(customerStates.length).toBeGreaterThan(1))
    
    rerender(<TestApp showCustomer={false} />)
    await waitFor(() => expect(adminStates.length).toBeGreaterThan(1))
    
    const finalStorage = getStorageState()
    const issues: string[] = []
    
    // Check if storage state remained consistent
    if (!finalStorage.customer || !finalStorage.admin) {
      issues.push('Auth tokens lost during rapid switching')
    }
    
    // Check if tokens got corrupted
    try {
      const customerData = JSON.parse(finalStorage.customer || '{}')
      const adminData = JSON.parse(finalStorage.admin || '{}')
      
      if (customerData.user?.email?.includes('admin')) {
        issues.push('Customer token contains admin user data')
      }
      
      if (adminData.user?.email?.includes('customer')) {
        issues.push('Admin token contains customer user data')
      }
    } catch (e) {
      issues.push('Auth tokens became corrupted/unparseable')
    }
    
    console.log('Rapid switching results:')
    console.log('Customer state changes:', customerStates.length)
    console.log('Admin state changes:', adminStates.length)
    console.log('Final storage integrity:', issues.length === 0 ? 'Good' : 'Compromised')
    
    if (issues.length > 0) {
      console.error('🚨 RAPID SWITCHING ISSUES DETECTED:')
      issues.forEach(issue => console.error(`  - ${issue}`))
    } else {
      console.log('✅ Rapid context switching handled correctly')
    }
    
    expect(issues.length).toBe(0)
  })

  test('COMPREHENSIVE ISSUE REPORT', async () => {
    console.log('\n=== COMPREHENSIVE AUTHENTICATION ISSUE REPORT ===')
    
    // This test provides a summary of all potential issues
    const report = {
      storageKeys: [] as string[],
      contamination: [] as string[],
      persistence: [] as string[],
      performance: [] as string[]
    }
    
    // Check storage key configuration
    const initialStorage = getStorageState()
    if (initialStorage.allKeys.length === 0) {
      report.storageKeys.push('No Supabase storage keys found')
    }
    
    if (initialStorage.allKeys.filter(key => key.includes('customer')).length === 0) {
      report.storageKeys.push('No customer-specific storage key detected')
    }
    
    if (initialStorage.allKeys.filter(key => key.includes('admin')).length === 0) {
      report.storageKeys.push('No admin-specific storage key detected')
    }
    
    // Test with mock data
    localStorage.setItem('sb-customer-auth-token', JSON.stringify({
      access_token: 'test-customer-token',
      user: { email: '<EMAIL>', role: 'customer' }
    }))
    
    localStorage.setItem('sb-admin-auth-token', JSON.stringify({
      access_token: 'test-admin-token',
      user: { email: '<EMAIL>', role: 'admin' }
    }))
    
    render(
      <div>
        <CustomerAuthProvider>
          <AuthStateObserver type="customer" onStateChange={trackCustomerState} />
        </CustomerAuthProvider>
        <AdminAuthProvider>
          <AuthStateObserver type="admin" onStateChange={trackAdminState} />
        </AdminAuthProvider>
      </div>
    )
    
    await waitFor(() => {
      expect(customerStates.length).toBeGreaterThan(0)
      expect(adminStates.length).toBeGreaterThan(0)
    })
    
    // Analyze results
    const latestCustomer = customerStates[customerStates.length - 1]
    const latestAdmin = adminStates[adminStates.length - 1]
    
    if (latestCustomer?.profile?.role === 'admin') {
      report.contamination.push('Customer context contains admin profile')
    }
    
    if (latestAdmin?.profile?.role === 'customer') {
      report.contamination.push('Admin context contains customer profile')
    }
    
    const finalStorage = getStorageState()
    if (finalStorage.customer === finalStorage.admin) {
      report.contamination.push('Identical storage values for customer and admin')
    }
    
    // Generate report
    console.log('\n📊 AUTHENTICATION ISSUE REPORT')
    console.log('=====================================')
    
    if (report.storageKeys.length > 0) {
      console.log('🔑 Storage Key Issues:')
      report.storageKeys.forEach(issue => console.log(`  ❌ ${issue}`))
    } else {
      console.log('🔑 Storage Keys: ✅ OK')
    }
    
    if (report.contamination.length > 0) {
      console.log('🔄 Cross-Contamination Issues:')
      report.contamination.forEach(issue => console.log(`  ❌ ${issue}`))
    } else {
      console.log('🔄 Cross-Contamination: ✅ OK')
    }
    
    if (report.persistence.length > 0) {
      console.log('💾 Persistence Issues:')
      report.persistence.forEach(issue => console.log(`  ❌ ${issue}`))
    } else {
      console.log('💾 Persistence: ✅ OK')
    }
    
    const totalIssues = report.storageKeys.length + report.contamination.length + report.persistence.length + report.performance.length
    
    console.log('\n📈 Summary:')
    console.log(`Total Issues Found: ${totalIssues}`)
    console.log(`Customer State Changes: ${customerStates.length}`)
    console.log(`Admin State Changes: ${adminStates.length}`)
    console.log('=====================================\n')
    
    // This test always passes but provides diagnostic information
    expect(true).toBe(true)
  })
})
