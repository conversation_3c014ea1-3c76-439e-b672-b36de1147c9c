"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { getSalesData, getSalesAggregates, SalesBookingItem, SalesAggregates } from "./actions/sales-actions";
import { BookingReceiptCard } from "@/components/admin/sales-tracking/booking-receipt-card";
import { SalesFaqDialog } from "@/components/admin/sales-tracking/sales-faq-dialog";
import { db } from "@/lib/supabase/database";
import { formatBookingIdForDisplay } from "@/lib/reference-ids";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  CalendarIcon,
  Filter,
  RotateCcw,
  Search,
  TrendingUp,
  Car,
  CalendarRange,
  RefreshCw,
  ChevronUp,
  ChevronDown,
  Activity,
  BarChart3,
  Users,
  CreditCard,
  Banknote,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAdminAuth } from "@/components/auth/admin-auth-context";

// Types for sales tracking data
interface SalesFilters {
  dateFrom: string;
  dateTo: string;
  location?: string;
  channel?: string;
  paymentStatus?: string;
  searchQuery?: string;
}

// Helper functions
const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

const formatDate = (date: Date | null | undefined): string => {
  if (!date) return "N/A";
  return date.toLocaleDateString("en-GB"); // dd/mm/yyyy format
};

const formatDisplayDate = (date: Date): string => {
  return date.toLocaleDateString("en-GB"); // dd/mm/yyyy format
};

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("en-PH", {
    style: "currency",
    currency: "PHP",
    minimumFractionDigits: 2,
  }).format(amount);
};

const calculatePercentageChange = (
  current: number,
  previous: number
): number => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
};

const LOCATIONS = [
  "All Locations",
  "Manila",
  "Makati",
  "Quezon City",
  "BGC Taguig",
  "Alabang",
];
const CHANNELS = ["All Channels", "Web", "App", "Partner"];
const VEHICLE_CLASSES = ["All Classes", "Economy", "SUV", "MPV"];
const PAYMENT_STATUSES = [
  "All Statuses",
  "Paid",
  "Pending",
  "Refunded",
  "Partial",
];

export default function SalesTrackingPage() {
  const { loading: authLoading } = useAdminAuth();
  const [isLoading, setIsLoading] = React.useState(true);
  const [data, setData] = React.useState<SalesBookingItem[]>([]);
  const [filteredData, setFilteredData] = React.useState<SalesBookingItem[]>([]);
  const [aggregates, setAggregates] = React.useState<SalesAggregates | null>(null);
  const [errorState, setErrorState] = React.useState<{
    hasError: boolean;
    message: string;
    details: string;
    type: string;
  } | null>(null);

  // Filter states - Set wider date range to capture all bookings
  const [dateRange, setDateRange] = React.useState<{ from: Date; to: Date }>({
    from: addDays(new Date(), -90), // Look back 90 days
    to: addDays(new Date(), 90),    // Look ahead 90 days for future bookings
  });
  const [location, setLocation] = React.useState("All Locations");
  const [vehicleClass, setVehicleClass] = React.useState("All Classes");
  const [channel, setChannel] = React.useState("All Channels");
  const [paymentStatus, setPaymentStatus] = React.useState("All Statuses");
  const [searchQuery, setSearchQuery] = React.useState("");
  const [selectedBooking, setSelectedBooking] = React.useState<SalesBookingItem | null>(null);

  // Fetch data function
  const fetchData = React.useCallback(async () => {
    setIsLoading(true);
    try {
      const filters: SalesFilters = {
        dateFrom: dateRange.from.toISOString(),
        dateTo: dateRange.to.toISOString(),
        location: location !== "All Locations" ? location : undefined,
        channel: channel !== "All Channels" ? channel : undefined,
        paymentStatus: paymentStatus !== "All Statuses" ? paymentStatus : undefined,
        searchQuery: searchQuery.trim() || undefined,
      };

      const { data: salesData, error: salesError } = await getSalesData(filters);
      
      if (salesError) {
        // Handle both structured error objects and empty/undefined errors
        const errorMessage = salesError?.message || "Failed to fetch sales data";
        const errorDetails = salesError?.details || "An unknown error occurred while loading sales data";
        const errorCode = salesError?.code || "UNKNOWN_ERROR";
        const errorType = salesError?.type || "unknown_error";
        
        console.error("Failed to fetch sales data:", salesError);
        console.error("Error details:", {
          message: errorMessage,
          details: errorDetails,
          code: errorCode,
          type: errorType,
          originalError: salesError
        });
        
        // Set empty data and show user-friendly error
        setData([]);
        setFilteredData([]);
        setAggregates(null);
        
        // Set error state for user-friendly display
        setErrorState({
          hasError: true,
          message: errorMessage,
          details: errorDetails,
          type: errorType
        });
        
        // Also log for debugging
        console.warn(`Sales Data Error (${errorCode}): ${errorMessage} - ${errorDetails}`);
        return;
      }

      if (salesData) {
        // Clear any previous error state on successful data fetch
        setErrorState(null);
        setData(salesData);
        setFilteredData(salesData);

        const { data: aggregatesData, error: aggregatesError } = await getSalesAggregates(salesData, filters);
        
        if (aggregatesError) {
          const errorMessage = aggregatesError?.message || "Failed to calculate aggregates";
          const errorDetails = aggregatesError?.details || "An error occurred while calculating sales metrics";
          console.error("Failed to calculate aggregates:", aggregatesError);
          console.error("Aggregates error details:", {
            message: errorMessage,
            details: errorDetails,
            code: aggregatesError?.code || "AGGREGATES_ERROR",
            type: aggregatesError?.type || "calculation_error"
          });
          // Set null aggregates but don't fail the whole operation
          setAggregates(null);
        } else if (aggregatesData) {
          setAggregates(aggregatesData);
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("Failed to fetch sales data:", error);
      console.error("Fetch error details:", {
        message: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : "UnknownError"
      });
      
      // Set empty data on unexpected errors
      setData([]);
      setFilteredData([]);
      setAggregates(null);
      
      // Set error state for user display
      setErrorState({
        hasError: true,
        message: "Unexpected Error",
        details: errorMessage,
        type: "unexpected_error"
      });
      
      // Log user-friendly error
      console.warn(`Sales Data Fetch Error: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  }, [dateRange, location, channel, paymentStatus, searchQuery]);

  // Fetch initial data and subscribe to real-time updates
  React.useEffect(() => {
    fetchData();

    // Subscribe to bookings table changes since sales data is derived from bookings
    const bookingsSubscription = db.subscribeToBookings((payload) => {
      console.log("Sales tracking: Booking change received!", payload);
      // Re-fetch sales data when bookings change
      fetchData();
    });

    // Subscribe to payments table changes since payment status affects sales data
    const paymentsSubscription = db.subscribeToPayments((payload) => {
      console.log("Sales tracking: Payment change received!", payload);
      // Re-fetch sales data when payments change
      fetchData();
    });

    // Subscribe to cars table changes since car data is part of sales analytics
    const carsSubscription = db.subscribeToCars((payload) => {
      console.log("Sales tracking: Car change received!", payload);
      // Re-fetch sales data when car information changes
      fetchData();
    });

    // Cleanup subscriptions on component unmount
    return () => {
      bookingsSubscription.unsubscribe();
      paymentsSubscription.unsubscribe();
      carsSubscription.unsubscribe();
    };
  }, [fetchData]);

  // Apply filters
  React.useEffect(() => {
    let filtered = [...data];

    // Date range filter
    filtered = filtered.filter(
      (item) =>
        item.pickupDate >= dateRange.from && item.pickupDate <= dateRange.to
    );

    // Search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (item) =>
          item.bookingId.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.plateNo.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.customerName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Location filter
    if (location !== "All Locations") {
      filtered = filtered.filter((item) => item.location === location);
    }

    // Vehicle class filter
    if (vehicleClass !== "All Classes") {
      filtered = filtered.filter((item) => {
        if (vehicleClass === "Economy") return item.model.includes("Vios");
        if (vehicleClass === "SUV")
          return item.model.includes("Fortuner") || item.model.includes("CR-V");
        if (vehicleClass === "MPV")
          return (
            item.model.includes("Xpander") || item.model.includes("Innova")
          );
        return true;
      });
    }

    // Channel filter
    if (channel !== "All Channels") {
      filtered = filtered.filter((item) => item.channel === channel);
    }

    // Payment status filter
    if (paymentStatus !== "All Statuses") {
      filtered = filtered.filter(
        (item) => item.paymentStatus === paymentStatus
      );
    }

    setFilteredData(filtered);
  }, [
    data,
    dateRange,
    searchQuery,
    location,
    vehicleClass,
    channel,
    paymentStatus,
  ]);

  // Recalculate aggregates when filtered data changes
  React.useEffect(() => {
    const recalculateAggregates = async () => {
      if (filteredData.length > 0) {
        const filters: SalesFilters = {
          dateFrom: dateRange.from.toISOString(),
          dateTo: dateRange.to.toISOString(),
          location: location !== "All Locations" ? location : undefined,
          channel: channel !== "All Channels" ? channel : undefined,
          paymentStatus: paymentStatus !== "All Statuses" ? paymentStatus : undefined,
          searchQuery: searchQuery.trim() || undefined,
        };

        const { data: newAggregates, error } = await getSalesAggregates(filteredData, filters);
        
        if (error) {
          const errorMessage = error?.message || "Failed to recalculate aggregates";
          const errorDetails = error?.details || "An error occurred while recalculating sales metrics";
          console.error("Failed to recalculate aggregates:", error);
          console.error("Recalculate aggregates error details:", {
            message: errorMessage,
            details: errorDetails,
            code: error?.code || "RECALCULATE_ERROR",
            type: error?.type || "calculation_error"
          });
        } else if (newAggregates) {
          setAggregates(newAggregates);
        }
      }
    };

    recalculateAggregates();
  }, [filteredData, dateRange, location, channel, paymentStatus, searchQuery]);

  const resetFilters = () => {
    setDateRange({ from: addDays(new Date(), -30), to: new Date() });
    setLocation("All Locations");
    setVehicleClass("All Classes");
    setChannel("All Channels");
    setPaymentStatus("All Statuses");
    setSearchQuery("");
  };

  // Wait for auth loading to complete before rendering
  if (authLoading) {
    return (
      <div className="space-y-6 p-2 sm:p-4 lg:p-6 max-w-full overflow-hidden">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Sales Tracking</h1>
            <p className="text-muted-foreground">
              Track sales performance and revenue analytics
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3" />
            <span className="text-muted-foreground">Loading...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-2 sm:p-4 lg:p-6 max-w-full overflow-hidden">
      {/* Header with FAQ */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Sales Tracking</h1>
          <p className="text-muted-foreground">Monitor revenue, bookings, and performance metrics</p>
        </div>
        <SalesFaqDialog />
      </div>

      {/* Error Display */}
      {errorState?.hasError && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Unable to Load Sales Data
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p><strong>Error:</strong> {errorState.message}</p>
                <p className="mt-1">{errorState.details}</p>
                {errorState.type === 'authentication_error' && (
                  <div className="mt-3">
                    <button 
                      onClick={() => window.location.reload()} 
                      className="bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded text-sm font-medium"
                    >
                      Refresh Page
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters Bar - Sticky */}
      <div className="sticky top-0 z-10 bg-white border border-border rounded-xl p-4 shadow-sm">
        <div className="flex flex-wrap gap-4 items-end">
          {/* Date Range */}
          <div className="flex flex-col space-y-2 min-w-[220px]">
            <label className="text-sm font-medium text-foreground">
              Date Range
            </label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="secondary"
                  className={cn(
                    "justify-start text-left font-normal bg-white hover:bg-gray-50 border-2 border-gray-200 hover:border-gray-300 transition-all duration-200 rounded-xl shadow-sm h-11",
                    !dateRange.from && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-3 h-4 w-4 text-gray-500" />
                  {dateRange.from ? (
                    dateRange.to ? (
                      <span className="font-medium text-gray-900">
                        {formatDisplayDate(dateRange.from)} -{" "}
                        {formatDisplayDate(dateRange.to)}
                      </span>
                    ) : (
                      <span className="font-medium text-gray-900">
                        {formatDisplayDate(dateRange.from)}
                      </span>
                    )
                  ) : (
                    <span className="text-gray-500">Pick a date range</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto p-0 bg-white shadow-xl border-0 rounded-2xl overflow-hidden"
                align="start"
              >
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-gray-100">
                  <h3 className="text-sm font-semibold text-gray-800 mb-1">
                    Select Date Range
                  </h3>
                  <p className="text-xs text-gray-600">
                    Choose your analysis period
                  </p>
                </div>
                <div className="p-4 bg-white">
                  <Calendar
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={dateRange}
                    onSelect={(range) => {
                      if (range?.from && range?.to) {
                        setDateRange({ from: range.from, to: range.to });
                      }
                    }}
                    numberOfMonths={2}
                    className="rdp-modern"
                    classNames={{
                      months:
                        "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                      month: "space-y-4",
                      caption: "flex justify-center pt-1 relative items-center",
                      caption_label: "text-sm font-semibold text-gray-900",
                      nav: "space-x-1 flex items-center",
                      nav_button:
                        "h-8 w-8 bg-transparent hover:bg-gray-100 rounded-lg transition-colors duration-200 border-0 flex items-center justify-center",
                      nav_button_previous: "absolute left-1",
                      nav_button_next: "absolute right-1",
                      table: "w-full border-collapse space-y-1",
                      head_row: "flex",
                      head_cell:
                        "text-gray-500 rounded-md w-8 font-medium text-[0.8rem] uppercase tracking-wide",
                      row: "flex w-full mt-2",
                      cell: "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-blue-50 [&:has([aria-selected].day-outside)]:bg-gray-50/50 [&:has([aria-selected].day-range-end)]:rounded-r-lg [&:has([aria-selected].day-range-start)]:rounded-l-lg first:[&:has([aria-selected])]:rounded-l-lg last:[&:has([aria-selected])]:rounded-r-lg",
                      day: "h-8 w-8 p-0 font-normal aria-selected:opacity-100 hover:bg-blue-50 rounded-lg transition-colors duration-200 border-0 bg-transparent",
                      day_range_start:
                        "day-range-start bg-blue-500 text-white hover:bg-blue-600 rounded-lg",
                      day_range_end:
                        "day-range-end bg-blue-500 text-white hover:bg-blue-600 rounded-lg",
                      day_selected:
                        "bg-blue-500 text-white hover:bg-blue-600 hover:text-white focus:bg-blue-600 focus:text-white rounded-lg",
                      day_today:
                        "bg-gray-100 text-gray-900 font-semibold rounded-lg",
                      day_outside:
                        "text-gray-400 opacity-50 aria-selected:bg-gray-50/50 aria-selected:text-gray-500 aria-selected:opacity-30",
                      day_disabled: "text-gray-400 opacity-50",
                      day_range_middle:
                        "aria-selected:bg-blue-50 aria-selected:text-blue-900 hover:bg-blue-100 rounded-none",
                      day_hidden: "invisible",
                    }}
                  />
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* Location Filter */}
          <div className="flex flex-col space-y-2 min-w-[150px]">
            <label className="text-sm font-medium text-foreground">
              Location
            </label>
            <Select value={location} onValueChange={setLocation}>
              <SelectTrigger className="bg-white border-2 border-gray-200 rounded-xl h-11">
                <SelectValue placeholder="Select location" />
              </SelectTrigger>
              <SelectContent className="bg-white rounded-xl border-0 shadow-xl">
                {LOCATIONS.map((loc) => (
                  <SelectItem key={loc} value={loc}>
                    {loc}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Vehicle Class Filter */}
          <div className="flex flex-col space-y-2 min-w-[150px]">
            <label className="text-sm font-medium text-foreground">
              Vehicle Class
            </label>
            <Select value={vehicleClass} onValueChange={setVehicleClass}>
              <SelectTrigger className="bg-white border-2 border-gray-200 rounded-xl h-11">
                <SelectValue placeholder="Select class" />
              </SelectTrigger>
              <SelectContent className="bg-white rounded-xl border-0 shadow-xl">
                {VEHICLE_CLASSES.map((cls) => (
                  <SelectItem key={cls} value={cls}>
                    {cls}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Channel Filter */}
          <div className="flex flex-col space-y-2 min-w-[140px]">
            <label className="text-sm font-medium text-foreground">
              Channel
            </label>
            <Select value={channel} onValueChange={setChannel}>
              <SelectTrigger className="bg-white border-2 border-gray-200 rounded-xl h-11">
                <SelectValue placeholder="Select channel" />
              </SelectTrigger>
              <SelectContent className="bg-white rounded-xl border-0 shadow-xl">
                {CHANNELS.map((ch) => (
                  <SelectItem key={ch} value={ch}>
                    {ch}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Payment Status Filter */}
          <div className="flex flex-col space-y-2 min-w-[140px]">
            <label className="text-sm font-medium text-foreground">
              Payment Status
            </label>
            <Select value={paymentStatus} onValueChange={setPaymentStatus}>
              <SelectTrigger className="bg-white border-2 border-gray-200 rounded-xl h-11">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent className="bg-white rounded-xl border-0 shadow-xl">
                {PAYMENT_STATUSES.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Search */}
          <div className="flex flex-col space-y-2 min-w-[200px]">
            <label className="text-sm font-medium text-foreground">
              Search
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search bookings..."
                value={searchQuery}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
                className="pl-10 bg-white border-2 border-gray-200 rounded-xl h-11"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2 items-end">
            <Button
              variant="secondary"
              size="sm"
              onClick={resetFilters}
              className="bg-white border-2 border-gray-200 rounded-xl h-11"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </div>
      </div>

      {/* KPI Cards */}
      <section
        aria-labelledby="kpi-heading"
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        <h2 id="kpi-heading" className="sr-only">
          Sales Performance Indicators
        </h2>

        {/* Total Revenue */}
        <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow rounded-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 bg-green-100 rounded-lg">
                <Banknote className="h-5 w-5 text-green-600" />
              </div>
              {!isLoading && aggregates && (
                <div className="flex items-center gap-1 text-sm">
                  {calculatePercentageChange(
                    aggregates.revenueTotal,
                    aggregates.previousPeriodRevenue
                  ) >= 0 ? (
                    <ChevronUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-red-600" />
                  )}
                  <span className="font-medium text-gray-900">
                    {Math.abs(
                      calculatePercentageChange(
                        aggregates.revenueTotal,
                        aggregates.previousPeriodRevenue
                      )
                    ).toFixed(0)}
                    %
                  </span>
                </div>
              )}
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <div className="text-3xl font-bold text-gray-900">
                {isLoading ? (
                  <Skeleton className="h-9 w-32" />
                ) : (
                  formatCurrency(aggregates?.revenueTotal || 0)
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Bookings */}
        <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow rounded-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 bg-blue-100 rounded-lg">
                <CalendarRange className="h-5 w-5 text-blue-600" />
              </div>
              {!isLoading && aggregates && (
                <div className="flex items-center gap-1 text-sm">
                  {calculatePercentageChange(
                    aggregates.bookingsCount,
                    aggregates.previousPeriodBookings
                  ) >= 0 ? (
                    <ChevronUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-red-600" />
                  )}
                  <span className="font-medium text-gray-900">
                    {Math.abs(
                      calculatePercentageChange(
                        aggregates.bookingsCount,
                        aggregates.previousPeriodBookings
                      )
                    ).toFixed(0)}
                    %
                  </span>
                </div>
              )}
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-gray-600">
                Total Bookings
              </p>
              <div className="text-3xl font-bold text-gray-900">
                {isLoading ? (
                  <Skeleton className="h-9 w-20" />
                ) : (
                  aggregates?.bookingsCount || 0
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Average Order Value */}
        <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow rounded-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-5 w-5 text-purple-600" />
              </div>
              {!isLoading && aggregates && (
                <div className="flex items-center gap-1 text-sm">
                  {calculatePercentageChange(
                    aggregates.avgOrderValue,
                    aggregates.previousPeriodAOV
                  ) >= 0 ? (
                    <ChevronUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-red-600" />
                  )}
                  <span className="font-medium text-gray-900">
                    {Math.abs(
                      calculatePercentageChange(
                        aggregates.avgOrderValue,
                        aggregates.previousPeriodAOV
                      )
                    ).toFixed(0)}
                    %
                  </span>
                </div>
              )}
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-gray-600">Average Order</p>
              <div className="text-3xl font-bold text-gray-900">
                {isLoading ? (
                  <Skeleton className="h-9 w-28" />
                ) : (
                  formatCurrency(aggregates?.avgOrderValue || 0)
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Occupancy Rate */}
        <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow rounded-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <Car className="h-5 w-5 text-indigo-600" />
              </div>
              {!isLoading && aggregates && (
                <div className="flex items-center gap-1 text-sm">
                  {calculatePercentageChange(
                    aggregates.occupancyRate,
                    aggregates.previousPeriodOccupancy
                  ) >= 0 ? (
                    <ChevronUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-red-600" />
                  )}
                  <span className="font-medium text-gray-900">
                    {Math.abs(
                      calculatePercentageChange(
                        aggregates.occupancyRate,
                        aggregates.previousPeriodOccupancy
                      )
                    ).toFixed(0)}
                    %
                  </span>
                </div>
              )}
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-gray-600">
                Fleet Utilization
              </p>
              <div className="text-3xl font-bold text-gray-900">
                {isLoading ? (
                  <Skeleton className="h-9 w-20" />
                ) : (
                  `${aggregates?.occupancyRate?.toFixed(0) || 0}%`
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Additional Sales Metrics */}
      <section
        aria-labelledby="additional-metrics"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        <h2 id="additional-metrics" className="sr-only">
          Additional Sales Metrics
        </h2>

        {/* Average Daily Sales */}
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 shadow-sm hover:shadow-md transition-all duration-300 rounded-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-500 rounded-lg shadow-sm">
                <Activity className="h-5 w-5 text-white" />
              </div>
              <Badge
                variant="secondary"
                className="bg-blue-100 text-blue-700 hover:bg-blue-200"
              >
                Daily Avg
              </Badge>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-blue-700">
                Average Daily Sales
              </p>
              <div className="text-2xl font-bold text-blue-900">
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  formatCurrency(aggregates?.avgDailySales || 0)
                )}
              </div>
              <p className="text-xs text-blue-600">Per day performance</p>
            </div>
          </CardContent>
        </Card>

        {/* Average Weekly Sales */}
        <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100 border border-emerald-200 shadow-sm hover:shadow-md transition-all duration-300 rounded-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-emerald-500 rounded-lg shadow-sm">
                <BarChart3 className="h-5 w-5 text-white" />
              </div>
              <Badge
                variant="secondary"
                className="bg-emerald-100 text-emerald-700 hover:bg-emerald-200"
              >
                Weekly Avg
              </Badge>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-emerald-700">
                Average Weekly Sales
              </p>
              <div className="text-2xl font-bold text-emerald-900">
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  formatCurrency(aggregates?.avgWeeklySales || 0)
                )}
              </div>
              <p className="text-xs text-emerald-600">Per week performance</p>
            </div>
          </CardContent>
        </Card>

        {/* Total Customers */}
        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 shadow-sm hover:shadow-md transition-all duration-300 rounded-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-purple-500 rounded-lg shadow-sm">
                <Users className="h-5 w-5 text-white" />
              </div>
              <Badge
                variant="secondary"
                className="bg-purple-100 text-purple-700 hover:bg-purple-200"
              >
                Customers
              </Badge>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-purple-700">
                Total Customers
              </p>
              <div className="text-2xl font-bold text-purple-900">
                {isLoading ? (
                  <Skeleton className="h-8 w-16" />
                ) : (
                  (aggregates?.totalCustomers || 0).toLocaleString()
                )}
              </div>
              <p className="text-xs text-purple-600">Unique customer count</p>
            </div>
          </CardContent>
        </Card>

        {/* Repeat Customers */}
        <Card className="bg-gradient-to-br from-amber-50 to-amber-100 border border-amber-200 shadow-sm hover:shadow-md transition-all duration-300 rounded-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-amber-500 rounded-lg shadow-sm">
                <CreditCard className="h-5 w-5 text-white" />
              </div>
              <Badge
                variant="secondary"
                className="bg-amber-100 text-amber-700 hover:bg-amber-200"
              >
                Repeat
              </Badge>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-amber-700">
                Repeat Customers
              </p>
              <div className="text-2xl font-bold text-amber-900">
                {isLoading ? (
                  <Skeleton className="h-8 w-16" />
                ) : (
                  (aggregates?.repeatCustomers || 0).toLocaleString()
                )}
              </div>
              <p className="text-xs text-amber-600">
                {aggregates && aggregates.totalCustomers > 0
                  ? `${(
                      (aggregates.repeatCustomers / aggregates.totalCustomers) *
                      100
                    ).toFixed(1)}% of total`
                  : "Customer retention"}
              </p>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Detailed Revenue Breakdown */}
      <section className="space-y-8">
        {/* Monthly Revenue with Detailed Booking Table */}
        <Card className="bg-gradient-to-br from-slate-50 to-slate-100 border border-slate-200 shadow-sm hover:shadow-lg transition-all duration-300 rounded-xl">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold text-slate-900 flex items-center gap-2">
              <Banknote className="h-5 w-5 text-slate-600" />
              Monthly Revenue Summary
            </CardTitle>
            <p className="text-sm text-slate-600">
              Detailed breakdown with customer account information
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-white rounded-lg border border-slate-200">
                <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                  Monthly Total
                </p>
                <div className="text-xl font-bold text-slate-900 mt-1">
                  {isLoading ? (
                    <Skeleton className="h-6 w-24 mx-auto" />
                  ) : (
                    formatCurrency(aggregates?.revenueTotal || 0)
                  )}
                </div>
              </div>
              <div className="text-center p-4 bg-white rounded-lg border border-slate-200">
                <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                  Total Bookings
                </p>
                <div className="text-xl font-bold text-slate-900 mt-1">
                  {isLoading ? (
                    <Skeleton className="h-6 w-16 mx-auto" />
                  ) : (
                    aggregates?.bookingsCount || 0
                  )}
                </div>
              </div>
              <div className="text-center p-4 bg-white rounded-lg border border-slate-200">
                <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                  Average Order
                </p>
                <div className="text-xl font-bold text-slate-900 mt-1">
                  {isLoading ? (
                    <Skeleton className="h-6 w-20 mx-auto" />
                  ) : (
                    formatCurrency(aggregates?.avgOrderValue || 0)
                  )}
                </div>
              </div>
              <div className="text-center p-4 bg-white rounded-lg border border-slate-200">
                <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                  Per Vehicle
                </p>
                <div className="text-xl font-bold text-slate-900 mt-1">
                  {isLoading ? (
                    <Skeleton className="h-6 w-20 mx-auto" />
                  ) : (
                    formatCurrency(aggregates?.revenuePerVehicle || 0)
                  )}
                </div>
              </div>
            </div>

            {/* Performance Comparison */}
            <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-slate-200">
              <div>
                <p className="text-sm font-medium text-slate-700">
                  Performance vs. Previous Period
                </p>
                <p className="text-xs text-slate-500 mt-1">
                  Revenue comparison
                </p>
              </div>
              <div className="flex items-center gap-2">
                {aggregates && (
                  <>
                    {calculatePercentageChange(
                      aggregates.revenueTotal,
                      aggregates.previousPeriodRevenue
                    ) >= 0 ? (
                      <ChevronUp className="h-5 w-5 text-green-600" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-red-600" />
                    )}
                    <span
                      className={`text-lg font-bold ${
                        calculatePercentageChange(
                          aggregates.revenueTotal,
                          aggregates.previousPeriodRevenue
                        ) >= 0
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      {Math.abs(
                        calculatePercentageChange(
                          aggregates.revenueTotal,
                          aggregates.previousPeriodRevenue
                        )
                      ).toFixed(1)}
                      %
                    </span>
                  </>
                )}
              </div>
            </div>

            {/* Detailed Booking Table */}
            <div className="bg-white rounded-lg border border-slate-200 overflow-hidden">
              <div className="px-4 py-3 bg-slate-50 border-b border-slate-200">
                <h4 className="text-sm font-semibold text-slate-900">Detailed Booking Breakdown</h4>
                <p className="text-xs text-slate-600 mt-1">Customer account details and transaction information</p>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-slate-50 border-b border-slate-200">
                    <tr>
                      <th className="px-4 py-3 text-left font-medium text-slate-700">Booking ID</th>
                      <th className="px-4 py-3 text-left font-medium text-slate-700">Customer</th>
                      <th className="px-4 py-3 text-left font-medium text-slate-700">Vehicle</th>
                      <th className="px-4 py-3 text-left font-medium text-slate-700">Duration</th>
                      <th className="px-4 py-3 text-left font-medium text-slate-700">Payment</th>
                      <th className="px-4 py-3 text-right font-medium text-slate-700">Amount</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-slate-200">
                    {isLoading ? (
                      Array.from({ length: 5 }).map((_, i) => (
                        <tr key={i}>
                          <td className="px-4 py-3"><Skeleton className="h-4 w-20" /></td>
                          <td className="px-4 py-3"><Skeleton className="h-4 w-32" /></td>
                          <td className="px-4 py-3"><Skeleton className="h-4 w-24" /></td>
                          <td className="px-4 py-3"><Skeleton className="h-4 w-16" /></td>
                          <td className="px-4 py-3"><Skeleton className="h-4 w-20" /></td>
                          <td className="px-4 py-3 text-right"><Skeleton className="h-4 w-16" /></td>
                        </tr>
                      ))
                    ) : filteredData.length === 0 ? (
                      <tr>
                        <td colSpan={6} className="px-4 py-8 text-center text-slate-500">
                          No booking data available for the selected period
                        </td>
                      </tr>
                    ) : (
                      filteredData.slice(0, 10).map((booking) => (
                        <Dialog key={booking.bookingId}>
                          <DialogTrigger asChild>
                            <tr className="hover:bg-slate-50 cursor-pointer transition-colors">
                              <td className="px-4 py-3">
                                <div className="font-medium text-slate-900">
                                  {formatBookingIdForDisplay({ id: booking.bookingId, booking_ref: booking.bookingRef })}
                                </div>
                                <div className="text-xs text-slate-500">
                                  {formatDate(booking.createdAt)}
                                </div>
                              </td>
                              <td className="px-4 py-3">
                                <div className="font-medium text-slate-900">
                                  {booking.customerName}
                                </div>
                                <div className="text-xs text-slate-500">
                                  {booking.location}
                                </div>
                              </td>
                              <td className="px-4 py-3">
                                <div className="font-medium text-slate-900">
                                  {booking.model}
                                </div>
                                <div className="text-xs text-slate-500">
                                  {booking.plateNo}
                                </div>
                              </td>
                              <td className="px-4 py-3">
                                <div className="font-medium text-slate-900">
                                  {booking.rentalDays} day{booking.rentalDays !== 1 ? 's' : ''}
                                </div>
                                <div className="text-xs text-slate-500">
                                  {formatDate(booking.pickupDate)} - {formatDate(booking.dropoffDate)}
                                </div>
                              </td>
                              <td className="px-4 py-3">
                                <Badge
                                  variant={booking.paymentStatus === 'Paid' ? 'default' : 
                                          booking.paymentStatus === 'Pending' ? 'secondary' : 'destructive'}
                                  className={`text-xs ${
                                    booking.paymentStatus === 'Paid' ? 'bg-green-100 text-green-800 border-green-200' :
                                    booking.paymentStatus === 'Pending' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                                    'bg-red-100 text-red-800 border-red-200'
                                  }`}
                                >
                                  {booking.paymentStatus}
                                </Badge>
                                <div className="text-xs text-slate-500 mt-1">
                                  {booking.channel}
                                </div>
                              </td>
                              <td className="px-4 py-3 text-right">
                                <div className="font-bold text-slate-900">
                                  {formatCurrency(booking.totalAmount)}
                                </div>
                                <div className="text-xs text-slate-500">
                                  {formatCurrency(booking.baseRate)}/day
                                </div>
                              </td>
                            </tr>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle>Booking Receipt - {formatBookingIdForDisplay({ id: booking.bookingId, booking_ref: booking.bookingRef })}</DialogTitle>
                            </DialogHeader>
                            <BookingReceiptCard booking={booking} />
                          </DialogContent>
                        </Dialog>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
              {filteredData.length > 10 && (
                <div className="px-4 py-3 bg-slate-50 border-t border-slate-200 text-center">
                  <p className="text-sm text-slate-600">
                    Showing 10 of {filteredData.length} bookings • Click any row to view detailed receipt
                    <Button variant="secondary" size="sm" className="ml-2">
                      View All
                    </Button>
                  </p>
                </div>
              )}
              {filteredData.length <= 10 && filteredData.length > 0 && (
                <div className="px-4 py-2 bg-slate-50 border-t border-slate-200 text-center">
                  <p className="text-xs text-slate-500">
                    Click any row to view detailed receipt
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        
      </section>
    </div>
  );
}
