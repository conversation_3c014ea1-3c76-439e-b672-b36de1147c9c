"use server";

import { createClient, createContextClient } from "@/lib/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { session, context } = await request.json();

    if (process.env.NODE_ENV === 'development') {
      console.log(`[AuthCallback] Received request: context=${context}, hasSession=${!!session}, userEmail=${session?.user?.email}`);
    }

    if (!session) {
      return NextResponse.json({ error: "No session provided" }, { status: 400 });
    }

    // Validate context
    const authContext = context as 'admin' | 'customer' | undefined;
    if (authContext && !['admin', 'customer'].includes(authContext)) {
      return NextResponse.json({ error: "Invalid context provided" }, { status: 400 });
    }

    // Use context-specific client if context is provided, otherwise use default
    const supabase = authContext
      ? await createContextClient(authContext)
      : await createClient();

    if (process.env.NODE_ENV === 'development') {
      console.log(`[AuthCallback] Created ${authContext || 'default'} client, attempting to set session...`);
    }

    // Set the session in the appropriate server-side client
    const { error } = await supabase.auth.setSession({
      access_token: session.access_token,
      refresh_token: session.refresh_token,
    });

    if (error) {
      console.error(`[AuthCallback] Error setting ${authContext || 'default'} session:`, error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`[AuthCallback] Successfully set ${authContext || 'default'} session`);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[AuthCallback] Callback error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
