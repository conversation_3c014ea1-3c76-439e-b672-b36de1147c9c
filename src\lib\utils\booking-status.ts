/**
 * Booking Status Display Utilities
 * Maps database values to user-friendly admin display labels
 */

import type { BookingStatus } from "@/lib/types";

/**
 * Mapping table: Database Value → Admin Display Label
 */
export const BOOKING_STATUS_DISPLAY_MAP: Record<BookingStatus, string> = {
  "Pending": "Pending Verification",
  "Active": "Active Rental", 
  "Completed": "Completed",
  "Cancelled": "Cancelled"
} as const;

/**
 * Convert database booking status to admin-friendly display label
 */
export function getBookingStatusDisplay(status: BookingStatus): string {
  return BOOKING_STATUS_DISPLAY_MAP[status] || status;
}

/**
 * Get all booking status options for dropdowns/filters
 * Returns array of {value: database_value, label: display_label}
 */
export function getBookingStatusOptions() {
  return Object.entries(BOOKING_STATUS_DISPLAY_MAP).map(([value, label]) => ({
    value: value as BookingStatus,
    label
  }));
}

/**
 * Status badge styling - matches existing design patterns
 */
export const BOOKING_STATUS_STYLES = {
  "Pending": {
    className: "bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-900 border-2 border-amber-400 hover:from-amber-200 hover:to-yellow-200 shadow-md",
    icon: "⏳"
  },
  "Active": {
    className: "bg-gradient-to-r from-green-100 to-emerald-100 text-green-900 border-2 border-green-400 hover:from-green-200 hover:to-emerald-200 shadow-md",
    icon: "🚗"
  },
  "Completed": {
    className: "bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-900 border-2 border-blue-400 hover:from-blue-200 hover:to-cyan-200 shadow-md",
    icon: "✅"
  },
  "Cancelled": {
    className: "bg-gradient-to-r from-red-100 to-pink-100 text-red-900 border-2 border-red-400 hover:from-red-200 hover:to-pink-200 shadow-md",
    icon: "❌"
  }
} as const;
