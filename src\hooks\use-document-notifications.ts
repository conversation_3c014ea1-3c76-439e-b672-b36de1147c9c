"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/lib/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { getCustomerPendingDocumentRequests } from "@/lib/services/document-request-service";

interface PendingDocumentRequest {
  id: string;
  booking_id: string;
  document_type: string;
  request_type: 'missing' | 'revision';
  admin_notes?: string;
  requested_at: string;
  expires_at: string;
  booking_details: {
    id: string;
    status: string;
    pickup_datetime: string;
    dropoff_datetime: string;
    car_model?: string;
    car_plate?: string;
  };
}

export function useDocumentNotifications(userId?: string) {
  const [pendingRequests, setPendingRequests] = useState<PendingDocumentRequest[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const { toast } = useToast();

  const fetchPendingRequests = async (showToast = false) => {
    if (!userId) return;

    setIsLoading(true);
    try {
      const result = await getCustomerPendingDocumentRequests(userId);
      
      if (result.success && result.data) {
        const newRequests = result.data;
        
        // Show toast for new requests (only if we have a previous check time)
        if (showToast && lastChecked) {
          const newRequestsCount = newRequests.filter((req: PendingDocumentRequest) => 
            new Date(req.requested_at) > lastChecked
          ).length;
          
          if (newRequestsCount > 0) {
            toast({
              title: "Document Upload Required",
              description: `You have ${newRequestsCount} new document request${newRequestsCount > 1 ? 's' : ''}. Please update your legal documents.`,
              duration: 10000, // Show for 10 seconds
            });
          }
        }
        
        setPendingRequests(newRequests);
        setLastChecked(new Date());
      } else {
        console.error("Error fetching pending document requests:", result.error);
      }
    } catch (error) {
      console.error("Error fetching pending document requests:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    if (userId) {
      fetchPendingRequests(false);
    }
  }, [userId]);

  // Set up real-time listener for new document requests
  useEffect(() => {
    if (!userId) return;

    const supabase = createClient();
    
    // Listen for new document requests
    const channel = supabase
      .channel('document_requests')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'document_requests',
          filter: `customer_id=eq.${userId}`,
        },
        (payload) => {
          console.log('New document request received:', payload);
          fetchPendingRequests(true);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'document_requests',
          filter: `customer_id=eq.${userId}`,
        },
        (payload) => {
          console.log('Document request updated:', payload);
          fetchPendingRequests(false);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [userId]);

  // Periodic check for pending requests (every 5 minutes)
  useEffect(() => {
    if (!userId) return;

    const interval = setInterval(() => {
      fetchPendingRequests(true);
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [userId, lastChecked]);

  // Show persistent toast if there are pending requests
  useEffect(() => {
    if (pendingRequests.length > 0 && lastChecked) {
      // Check if any request is expiring soon (within 24 hours)
      const expiringSoon = pendingRequests.some(req => {
        const expiresAt = new Date(req.expires_at);
        const now = new Date();
        const hoursUntilExpiry = (expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60);
        return hoursUntilExpiry <= 24 && hoursUntilExpiry > 0;
      });

      const documentTypes = [...new Set(pendingRequests.map(req => {
        const displayNames: { [key: string]: string } = {
          'drivers_license': "Driver's License",
          'government_id': "Government ID",
          'proof_of_billing': "Proof of Billing"
        };
        return displayNames[req.document_type] || req.document_type;
      }))];

      toast({
        title: expiringSoon ? "⚠️ Document Upload Urgent" : "📋 Document Upload Required",
        description: `Please upload: ${documentTypes.join(', ')}${expiringSoon ? ' (expires soon!)' : ''}`,
        duration: expiringSoon ? 15000 : 8000, // Show longer if expiring soon
      });
    }
  }, [pendingRequests.length, lastChecked, toast]);

  const refreshRequests = () => {
    fetchPendingRequests(false);
  };

  return {
    pendingRequests,
    isLoading,
    refreshRequests,
    hasPendingRequests: pendingRequests.length > 0,
    pendingCount: pendingRequests.length
  };
}
