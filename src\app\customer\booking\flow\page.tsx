"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { PublicAppShell } from "@/components/layout/public-app-shell";
import { BookingFlow } from "@/components/customer-side/booking/flow/booking-flow";
import { ProtectedCustomerPage } from "@/components/customer-side/auth-guard";
import { CustomerBookingFlowSkeleton } from "@/components/customer-side/loading/skeleton-components";
import { Suspense } from "react";

function BookingFlowContent() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Helper function to split datetime into date and time
  const splitDateTime = (dateTimeStr: string) => {
    if (!dateTimeStr) return { date: "", time: "" };
    const dt = new Date(dateTimeStr);
    const date = dt.toISOString().split("T")[0]; // YYYY-MM-DD
    const time = dt.toTimeString().slice(0, 5); // HH:MM
    return { date, time };
  };

  const pickUpDateTime = searchParams.get("pickUpDateTime") || "";
  const dropOffDateTime = searchParams.get("dropOffDateTime") || "";

  const pickUp = splitDateTime(pickUpDateTime);
  const dropOff = splitDateTime(dropOffDateTime);

  // Get initial data from URL params if coming from booking widget
  const initialData = {
    pickUpLocation: searchParams.get("pickUpLocation") || "",
    dropOffLocation: searchParams.get("dropOffLocation") || "",
    pickUpDate: pickUp.date,
    pickUpTime: pickUp.time,
    dropOffDate: dropOff.date,
    dropOffTime: dropOff.time,
    carId: searchParams.get("carId") || null,
    specialService: searchParams.get("specialService") === "true",
    serviceType: searchParams.get("serviceType") || null,
  };

  return (
    <ProtectedCustomerPage>
      <PublicAppShell>
        <div className="min-h-screen bg-gray-50">
          <BookingFlow initialData={initialData} />
        </div>
      </PublicAppShell>
    </ProtectedCustomerPage>
  );
}

export default function BookingFlowPage() {
  return (
    <Suspense fallback={
      <PublicAppShell>
        <CustomerBookingFlowSkeleton />
      </PublicAppShell>
    }>
      <BookingFlowContent />
    </Suspense>
  );
}
