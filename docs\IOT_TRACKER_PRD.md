# IoT GPS Tracker PRD (ESP32-S3 A7670E + MQTT + PlatformIO)

## 1. Problem & Goal
- __Problem__: No end-to-end, production-ready pipeline for ingesting live GPS data from physical cars into the Admin Tracker map.
- __Goal__: Stream secure, low-latency GPS telemetry from Waveshare ESP32-S3-A7670E-4G devices to the app, persist it, and render it live on `admin/tracker`.

## 2. Scope
- __In scope__:
  - Firmware on ESP32-S3-A7670E for LTE + GNSS + publish telemetry (MQTT or HTTPS to GeoLinker optional).
  - MQTT broker integration and backend consumer to store last-known locations and history.
  - Admin UI wiring to display live locations and historical trails using `src/components/admin/gps-tracker-map.tsx`.
- __Out of scope__:
  - Full fleet management, geofencing alerts, route optimization.

## 3. Users & Success Metrics
- __Users__: Admin operators viewing `admin/tracker`.
- __KPIs__:
  - Location latency: P50 ≤ 3s; P95 ≤ 7s.
  - Device online rate ≥ 98% during test window.
  - Data integrity: ≤ 0.1% malformed payloads.

## 4. Requirements
- __Telemetry payload__ (JSON): `{ id, lat, lon, speed, heading, ts }` with ISO timestamp or ms epoch.
- __Topic scheme__: `fleet/{deviceId}/location` (QoS 1, retain last).
- __Security__: Per-device credentials; TLS for MQTT (8883) if broker supports.
- __Resilience__: Reconnect LTE/MQTT, backoff, publish at fixed interval (e.g., 5–10s).
- __Persistence__: Store latest per device + optional rolling history for 24–72h.
- __UI__: `gps-tracker-map.tsx` consumes API/stream, filters by freshness using `NEXT_PUBLIC_GPS_STALE_SECONDS`.

## 5. Architecture Overview
- __Device (ESP32-S3 + A7670E)__
  - Option A: SIMCom MQTT via AT (`AT+CMQTT*`), publish JSON directly.
  - Option B: TinyGSM PPP + standard MQTT client (PubSubClient) or HTTPS to GeoLinker.
  - GNSS on A7670E provides NMEA; parsed to lat/lon/speed/heading.
- __Broker__
  - EMQX/HiveMQ Cloud/AWS IoT (start with EMQX/HiveMQ Cloud for speed).
- __Backend consumer__
  - A long-lived worker subscribes to topics, validates payloads, stores to DB (e.g., Supabase/Postgres tables: `device_locations`, `device_location_events`).
  - Provide REST endpoints + SSE/WebSocket stream for the UI.
- __Frontend__
  - `src/components/admin/gps-tracker-map.tsx` reads latest and updates every 5s or via push stream.

## 6. Data Model (proposed)
- `device_locations` (latest)
  - id (PK, uuid), device_id (text), lat (double), lon (double), speed (double), heading (double), ts (timestamptz), updated_at (timestamptz)
- `device_location_events` (history)
  - id (PK), device_id, lat, lon, speed, heading, ts (indexed), raw (jsonb)

## 7. Operational Considerations
- __SIM/APN__: Configurable per device.
- __Publish rate__: 5–10s; rate-limit to avoid data storms.
- __Clock__: Use modem network time; fall back to device RTC.
- __Firmware OTA__: Future work.

## 8. Risks & Mitigations
- __Cellular instability__: Implement reconnect/backoff; retain message at broker.
- __Power__: Use 18650 + proper DIP switch; ensure GNSS lock and antenna placement.
- __Serverless limits__: If deployed to serverless, run the MQTT consumer in a persistent worker (container/VM) or managed broker rule -> webhook.

## 9. Acceptance Criteria
- At least one device publishes to `fleet/{deviceId}/location` every ≤10s.
- Backend stores latest and 1h history.
- Admin map shows moving marker in near real time (<7s P95).
- End-to-end tested on real SIM and outdoor GNSS lock.

## 10. Milestones
- __M1 (Day 1–2)__: Pick broker; provision creds; define payload & topics; create DB tables.
- __M2 (Day 3–4)__: Firmware PoC (LTE attach, GNSS read, publish JSON every 10s).
- __M3 (Day 5–6)__: Backend subscriber + REST endpoint `/api/tracker/locations` + optional SSE `/api/tracker/stream`.
- __M4 (Day 7)__: UI wiring to live data; map refresh; freshness filters.
- __M5 (Day 8)__: Hardening (TLS, reconnection, metrics); docs.

## 11. Implementation Choices
- __Firmware IDE__: PlatformIO (Arduino framework) in `iot/GPS/`.
- __GNSS__: Parse NMEA from A7670E; TinyGPS++ or minmea.
- __Connectivity__: Option B (TinyGSM PPP + PubSubClient) as default; Option A (AT CMQTT) as fallback.
- __GeoLinker (optional)__: Also POST to Circuit Digest GeoLinker for external visualization.

## 12. References
- Waveshare ESP32-S3-A7670E-4G wiki (HTTP/MQTT AT, GNSS, demos).
- Circuit Digest GeoLinker (tutorial + PlatformIO library).
