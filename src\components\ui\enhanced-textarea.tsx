"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"
import { AlertCircle, CheckCircle } from "lucide-react"

interface EnhancedTextareaProps extends React.ComponentProps<"textarea"> {
  label?: string
  description?: string
  error?: string
  success?: string
  showCharCount?: boolean
  maxLength?: number
  autoResize?: boolean
}

const EnhancedTextarea = React.forwardRef<HTMLTextAreaElement, EnhancedTextareaProps>(
  ({ 
    className, 
    label, 
    description, 
    error, 
    success, 
    showCharCount = false,
    maxLength,
    autoResize = false,
    disabled,
    required,
    ...props 
  }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false)
    const [charCount, setCharCount] = React.useState(
      typeof props.value === 'string' ? props.value.length : 
      typeof props.defaultValue === 'string' ? props.defaultValue.length : 0
    )
    
    const textareaRef = React.useRef<HTMLTextAreaElement>(null)
    const inputId = React.useId()
    const descriptionId = React.useId()
    const errorId = React.useId()

    // Auto-resize functionality
    const adjustHeight = React.useCallback(() => {
      const textarea = textareaRef.current
      if (textarea && autoResize) {
        textarea.style.height = 'auto'
        textarea.style.height = `${textarea.scrollHeight}px`
      }
    }, [autoResize])

    React.useEffect(() => {
      if (autoResize) {
        adjustHeight()
      }
    }, [props.value, adjustHeight, autoResize])

    // State classes
    const getStateClasses = () => {
      if (disabled) return "opacity-50 cursor-not-allowed pointer-events-none"
      if (error) return "border-destructive focus-visible:border-destructive focus-visible:ring-destructive/20"
      if (success) return "border-green-500 focus-visible:border-green-500 focus-visible:ring-green-500/20"
      return "focus-visible:border-ring focus-visible:ring-ring/20"
    }

    const handleFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      setIsFocused(true)
      props.onFocus?.(e)
    }

    const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      setIsFocused(false)
      props.onBlur?.(e)
    }

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setCharCount(e.target.value.length)
      props.onChange?.(e)
      if (autoResize) {
        adjustHeight()
      }
    }

    // Enhanced placeholder text
    const getEnhancedPlaceholder = () => {
      if (props.placeholder) return props.placeholder
      return `Enter ${label?.toLowerCase() || 'your message'}...`
    }

    // Character count color
    const getCharCountColor = () => {
      if (!maxLength) return "text-muted-foreground"
      const percentage = (charCount / maxLength) * 100
      if (percentage >= 100) return "text-destructive"
      if (percentage >= 80) return "text-yellow-600"
      return "text-muted-foreground"
    }

    return (
      <div className="space-y-2">
        {/* Label */}
        {label && (
          <Label 
            htmlFor={inputId} 
            className={cn(
              "text-sm font-medium",
              error && "text-destructive",
              success && "text-green-700",
              disabled && "opacity-50"
            )}
          >
            {label}
            {required && <span className="text-destructive ml-1">*</span>}
          </Label>
        )}

        {/* Description */}
        {description && (
          <p 
            id={descriptionId}
            className={cn(
              "text-xs text-muted-foreground",
              disabled && "opacity-50"
            )}
          >
            {description}
          </p>
        )}

        {/* Textarea Container */}
        <div className="relative">
          <textarea
            id={inputId}
            ref={(node) => {
              textareaRef.current = node
              if (typeof ref === 'function') {
                ref(node)
              } else if (ref) {
                ref.current = node
              }
            }}
            disabled={disabled}
            required={required}
            maxLength={maxLength}
            placeholder={getEnhancedPlaceholder()}
            className={cn(
              // Base styles
              "flex w-full min-w-0 rounded-md border shadow-xs transition-all duration-200 outline-none",
              "px-3 py-2 text-base md:text-sm",
              autoResize ? "min-h-[80px] resize-none overflow-hidden" : "min-h-[100px] resize-y",
              
              // Default background
              "border-input bg-transparent",
              
              // State styles
              getStateClasses(),
              
              // Focus styles
              "focus-visible:ring-[3px]",
              
              // Placeholder styles
              "placeholder:text-muted-foreground/60",
              
              // Hover styles (when not disabled)
              !disabled && "hover:border-muted-foreground/30",
              
              // Custom styles
              className
            )}
            aria-describedby={cn(
              description && descriptionId,
              error && errorId
            )}
            aria-invalid={!!error}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            {...props}
          />
        </div>

        {/* Footer: Character count and messages */}
        <div className="flex justify-between items-start gap-2">
          <div className="flex-1">
            {/* Error Message */}
            {error && (
              <p 
                id={errorId}
                className="text-xs text-destructive flex items-center gap-1"
                role="alert"
              >
                <AlertCircle className="h-3 w-3 flex-shrink-0" />
                {error}
              </p>
            )}

            {/* Success Message */}
            {success && !error && (
              <p className="text-xs text-green-600 flex items-center gap-1">
                <CheckCircle className="h-3 w-3 flex-shrink-0" />
                {success}
              </p>
            )}
          </div>

          {/* Character Count */}
          {showCharCount && (
            <div className={cn(
              "text-xs font-medium flex-shrink-0",
              getCharCountColor()
            )}>
              {charCount}
              {maxLength && `/${maxLength}`}
            </div>
          )}
        </div>
      </div>
    )
  }
)

EnhancedTextarea.displayName = "EnhancedTextarea"

export { EnhancedTextarea }
