-- ============================================
-- VEHICLE CATEGORIES TABLE SCHEMA
-- ============================================
-- This schema creates the vehicle categories table for organizing cars
-- by category with pricing ranges and metadata for the Fleet page

-- ============================================
-- UPDATE EXISTING VEHICLE CATEGORIES TABLE
-- ============================================
-- Add image_url column to existing vehicle_categories table
ALTER TABLE public.vehicle_categories 
ADD COLUMN IF NOT EXISTS image_url text null;

create index IF not exists idx_vehicle_categories_type on public.vehicle_categories using btree (type) TABLESPACE pg_default;

create index IF not exists idx_vehicle_categories_active on public.vehicle_categories using btree (is_active) TABLESPACE pg_default;

create index IF not exists idx_vehicle_categories_display_order on public.vehicle_categories using btree (display_order) TABLESPACE pg_default;

-- Create trigger only if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'handle_updated_at_vehicle_categories'
    ) THEN
        CREATE TRIGGER handle_updated_at_vehicle_categories 
        BEFORE UPDATE ON vehicle_categories 
        FOR EACH ROW 
        EXECUTE FUNCTION handle_updated_at();
    END IF;
END $$;

-- ============================================
-- UPDATE CARS TABLE TO REFERENCE CATEGORIES
-- ============================================
-- Add foreign key relationship between cars and vehicle_categories
alter table public.cars 
add column category_id uuid null references public.vehicle_categories(id) on delete set null;

-- Create index for the foreign key
create index if not exists idx_cars_category_id on public.cars using btree (category_id) tablespace pg_default;

-- ============================================
-- FUNCTIONS FOR CATEGORY MANAGEMENT
-- ============================================

-- Function to get vehicle categories with car counts and actual price ranges
create or replace function public.get_vehicle_categories_with_stats()
returns table (
  id uuid,
  name text,
  type text,
  description text,
  min_price numeric(10,2),
  max_price numeric(10,2),
  actual_min_price numeric(10,2),
  actual_max_price numeric(10,2),
  image_url text,
  icon_color text,
  available_transmissions text[],
  display_order integer,
  is_active boolean,
  vehicle_count bigint,
  available_count bigint,
  created_at timestamp with time zone,
  updated_at timestamp with time zone
) as $$
begin
  return query
  select 
    vc.id,
    vc.name,
    vc.type,
    vc.description,
    vc.min_price,
    vc.max_price,
    coalesce(min(c.price_per_day), vc.min_price) as actual_min_price,
    coalesce(max(c.price_per_day), vc.max_price) as actual_max_price,
    vc.image_url,
    vc.icon_color,
    vc.available_transmissions,
    vc.display_order,
    vc.is_active,
    count(c.id) as vehicle_count,
    count(case when c.status = 'Available' and c.is_archived = false then 1 end) as available_count,
    vc.created_at,
    vc.updated_at
  from public.vehicle_categories vc
  left join public.cars c on vc.id = c.category_id and c.is_archived = false
  where vc.is_active = true
  group by vc.id, vc.name, vc.type, vc.description, vc.min_price, vc.max_price, 
           vc.image_url, vc.icon_color, vc.available_transmissions, vc.display_order, 
           vc.is_active, vc.created_at, vc.updated_at
  order by vc.display_order asc, vc.name asc;
end;
$$ language plpgsql security definer;

-- Function to get cars by category
create or replace function public.get_cars_by_category(category_type_param text)
returns table (
  id uuid,
  model text,
  type text,
  plate_number text,
  color text,
  status text,
  condition text,
  fuel_capacity integer,
  fuel_type text,
  transmission text,
  seats integer,
  price_per_day numeric(10,2),
  image_url text,
  notes text,
  category_name text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone
) as $$
begin
  return query
  select 
    c.id,
    c.model,
    c.type,
    c.plate_number,
    c.color,
    c.status,
    c.condition,
    c.fuel_capacity,
    c.fuel_type,
    c.transmission,
    c.seats,
    c.price_per_day,
    c.image_url,
    c.notes,
    vc.name as category_name,
    c.created_at,
    c.updated_at
  from public.cars c
  left join public.vehicle_categories vc on c.category_id = vc.id
  where c.is_archived = false 
    and (category_type_param is null or vc.type = category_type_param)
  order by c.price_per_day asc, c.model asc;
end;
$$ language plpgsql security definer;

-- Function to update category pricing based on actual car prices
create or replace function public.update_category_pricing()
returns void as $$
declare
  category_record record;
  min_price_val numeric(10,2);
  max_price_val numeric(10,2);
begin
  for category_record in 
    select id, type from public.vehicle_categories where is_active = true
  loop
    -- Get actual price range for this category
    select 
      coalesce(min(price_per_day), 0),
      coalesce(max(price_per_day), 0)
    into min_price_val, max_price_val
    from public.cars 
    where category_id = category_record.id 
      and is_archived = false 
      and status = 'Available';

    -- Update category pricing if we found cars
    if min_price_val > 0 and max_price_val > 0 then
      update public.vehicle_categories 
      set 
        min_price = min_price_val,
        max_price = max_price_val,
        updated_at = now()
      where id = category_record.id;
    end if;
  end loop;
end;
$$ language plpgsql security definer;


-- ============================================
-- SAMPLE VEHICLE CATEGORIES DATA
-- ============================================

-- Insert sample vehicle categories with placeholder images
insert into public.vehicle_categories (name, type, description, min_price, max_price, image_url, icon_color, display_order) values
  ('SUV VEHICLES', 'SUV', 'Sport Utility Vehicles perfect for family trips and off-road adventures', 2000.00, 2700.00, '/placeholder.svg', '#10B981', 1),
  ('MPV VEHICLES', 'MPV', 'Multi-Purpose Vehicles ideal for large groups and families', 1600.00, 3800.00, '/placeholder.svg', '#F59E0B', 2),
  ('HATCHBACK VEHICLES', 'Hatchback', 'Compact and fuel-efficient cars perfect for city driving', 1600.00, 2000.00, '/placeholder.svg', '#3B82F6', 3),
  ('SPORT VEHICLES', 'Sport', 'High-performance motorcycles and sports cars', 4500.00, 8500.00, '/placeholder.svg', '#EF4444', 4),
  ('COUPE VEHICLES', 'Coupe', 'Stylish two-door cars with elegant design', 12000.00, 12000.00, '/placeholder.svg', '#8B5CF6', 5),
  ('SEDAN VEHICLES', 'Sedan', 'Classic four-door sedans perfect for business and comfort', 1500.00, 2500.00, '/placeholder.svg', '#6366F1', 6);

-- ============================================
-- UPDATE EXISTING CARS WITH CATEGORIES
-- ============================================

-- Update existing cars to link them with appropriate categories
-- This should be run after inserting the categories above

update public.cars 
set category_id = (select id from public.vehicle_categories where type = 'SUV' limit 1)
where type = 'SUV';

update public.cars 
set category_id = (select id from public.vehicle_categories where type = 'MPV' limit 1)
where type = 'MPV';

update public.cars 
set category_id = (select id from public.vehicle_categories where type = 'Hatchback' limit 1)
where type = 'Hatchback';

update public.cars 
set category_id = (select id from public.vehicle_categories where type = 'Sport' limit 1)
where type = 'Sport';

update public.cars 
set category_id = (select id from public.vehicle_categories where type = 'Coupe' limit 1)
where type = 'Coupe';

update public.cars 
set category_id = (select id from public.vehicle_categories where type = 'Sedan' limit 1)
where type = 'Sedan';


-- ============================================
-- NOTES
-- ============================================
--
-- 1. Category Relationship:
--    - Cars have an optional foreign key to vehicle_categories
--    - This allows flexible categorization without breaking existing data
--    - Categories can exist without cars (for future planning)
--    - Cars can exist without categories (legacy support)
--
-- 2. Pricing Strategy:
--    - Categories have min/max price ranges for display
--    - Actual car prices can vary within or outside these ranges
--    - Use update_category_pricing() to sync category ranges with actual car prices
--
-- 3. Category Management:
--    - Only admins can create/edit/delete categories
--    - Categories can be deactivated instead of deleted (soft delete)
--    - Display order controls how categories appear on the Fleet page
--
-- 4. Fleet Page Integration:
--    - Use get_vehicle_categories_with_stats() for the main Fleet page
--    - Use get_cars_by_category() to show cars within a specific category
--    - Categories show actual vehicle counts and availability
--
-- 5. Archive Integration:
--    - When cars are archived, category information is preserved
--    - This maintains historical data integrity
--    - Archived cars don't affect category statistics
--
