/**
 * Cookie Isolation Test Suite
 * 
 * This test suite specifically targets cookie isolation issues between
 * admin and customer authentication contexts that occur when tabs are
 * closed and reopened.
 */

import { render, screen, waitFor, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import { createBrowserClient } from '@supabase/ssr'
import { CustomerAuthProvider } from '../../src/components/auth/customer-auth-context'
import { AdminAuthProvider } from '../../src/components/auth/admin-auth-context'

// Mock Supabase client
jest.mock('@supabase/ssr', () => ({
  createBrowserClient: jest.fn()
}))

const mockCreateBrowserClient = createBrowserClient as jest.MockedFunction<typeof createBrowserClient>

describe('Cookie Isolation Tests', () => {
  let mockSupabaseCustomer: any
  let mockSupabaseAdmin: any

  beforeEach(() => {
    // Reset all mocks and clear storage
    jest.clearAllMocks()
    window.localStorage.clear()
    window.sessionStorage.clear()
    
    // Create separate mock clients for customer and admin
    mockSupabaseCustomer = {
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        onAuthStateChange: jest.fn().mockReturnValue({
          data: { subscription: { unsubscribe: jest.fn() } }
        }),
        signInWithPassword: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn().mockResolvedValue({ error: null }),
        resetPasswordForEmail: jest.fn(),
        verifyOtp: jest.fn(),
        resend: jest.fn()
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } })
          })
        })
      })
    }

    mockSupabaseAdmin = {
      auth: {
        getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        onAuthStateChange: jest.fn().mockReturnValue({
          data: { subscription: { unsubscribe: jest.fn() } }
        }),
        signInWithPassword: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn().mockResolvedValue({ error: null }),
        resetPasswordForEmail: jest.fn(),
        verifyOtp: jest.fn(),
        resend: jest.fn()
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } })
          })
        })
      })
    }

    // Setup createBrowserClient mock to return appropriate client based on storage key
    mockCreateBrowserClient.mockImplementation((url, key, options) => {
      if (options?.auth?.storageKey === 'sb-customer-auth-token') {
        return mockSupabaseCustomer
      } else if (options?.auth?.storageKey === 'sb-admin-auth-token') {
        return mockSupabaseAdmin
      }
      return mockSupabaseCustomer // Default fallback
    })
  })

  test('should use separate storage keys for admin and customer contexts', async () => {
    await act(async () => {
      render(
        <CustomerAuthProvider>
          <div data-testid="customer-context">Customer Context</div>
        </CustomerAuthProvider>
      )
    })

    await act(async () => {
      render(
        <AdminAuthProvider>
          <div data-testid="admin-context">Admin Context</div>
        </AdminAuthProvider>
      )
    })

    // Verify createBrowserClient was called with separate storage keys
    const calls = mockCreateBrowserClient.mock.calls
    expect(calls.some(call => call[2]?.auth?.storageKey === 'sb-customer-auth-token')).toBe(true)
    expect(calls.some(call => call[2]?.auth?.storageKey === 'sb-admin-auth-token')).toBe(true)
  })

  test('should maintain separate localStorage entries for each context', () => {
    const customerToken = JSON.stringify({ access_token: 'customer-token', user: { id: '1' } })
    const adminToken = JSON.stringify({ access_token: 'admin-token', user: { id: '2' } })
    
    window.localStorage.setItem('sb-customer-auth-token', customerToken)
    window.localStorage.setItem('sb-admin-auth-token', adminToken)
    
    expect(window.localStorage.getItem('sb-customer-auth-token')).toBe(customerToken)
    expect(window.localStorage.getItem('sb-admin-auth-token')).toBe(adminToken)
    expect(window.localStorage.getItem('sb-customer-auth-token')).not.toBe(window.localStorage.getItem('sb-admin-auth-token'))
  })

  test('should handle tab close/reopen scenario without cross-contamination', async () => {
    // Setup legitimate customer and admin sessions
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>' }
    }
    
    const adminSession = {
      access_token: 'admin-token',
      user: { id: 'admin-1', email: '<EMAIL>' }
    }

    // Mock successful sessions for each context
    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseAdmin.auth.getSession.mockResolvedValue({
      data: { session: adminSession },
      error: null
    })

    // Mock profile fetches - customer context gets customer profile
    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })
    
    // Admin context gets admin profile
    mockSupabaseAdmin.from().select().eq().single.mockResolvedValue({
      data: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
      error: null
    })

    // Simulate tab close by unmounting components
    const { unmount: unmountCustomer } = await act(async () => {
      return render(
        <CustomerAuthProvider>
          <div data-testid="customer-reopened">Customer Reopened</div>
        </CustomerAuthProvider>
      )
    })

    const { unmount: unmountAdmin } = await act(async () => {
      return render(
        <AdminAuthProvider>
          <div data-testid="admin-reopened">Admin Reopened</div>
        </AdminAuthProvider>
      )
    })

    // Wait for initial setup
    await waitFor(() => {
      expect(screen.getByTestId('customer-reopened')).toBeInTheDocument()
      expect(screen.getByTestId('admin-reopened')).toBeInTheDocument()
    })

    // Clear the mock call history to track reopen calls
    mockSupabaseCustomer.auth.getSession.mockClear()
    mockSupabaseAdmin.auth.getSession.mockClear()

    // Simulate tab close
    unmountCustomer()
    unmountAdmin()

    // Simulate tab reopen by re-rendering
    await act(async () => {
      render(
        <CustomerAuthProvider>
          <div data-testid="customer-reopened">Customer Reopened</div>
        </CustomerAuthProvider>
      )
    })

    await act(async () => {
      render(
        <AdminAuthProvider>
          <div data-testid="admin-reopened">Admin Reopened</div>
        </AdminAuthProvider>
      )
    })

    // Verify sessions are retrieved correctly after "reopen"
    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
      expect(mockSupabaseAdmin.auth.getSession).toHaveBeenCalled()
    })
  })

  test('should prevent customer session from appearing in admin context', async () => {
    // Setup customer session that both contexts will see
    const customerSession = {
      access_token: 'customer-token',
      user: { id: 'customer-1', email: '<EMAIL>', role: 'customer' }
    }

    // Both contexts get the same customer session (simulating contamination)
    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    mockSupabaseAdmin.auth.getSession.mockResolvedValue({
      data: { session: customerSession },
      error: null
    })

    // Customer context gets customer profile (should work)
    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    // Admin context also gets customer profile (should be rejected)
    mockSupabaseAdmin.from().select().eq().single.mockResolvedValue({
      data: { id: 'customer-1', email: '<EMAIL>', role: 'customer' },
      error: null
    })

    await act(async () => {
      render(
        <CustomerAuthProvider>
          <div data-testid="customer">Customer</div>
        </CustomerAuthProvider>
      )
    })

    await act(async () => {
      render(
        <AdminAuthProvider>
          <div data-testid="admin">Admin</div>
        </AdminAuthProvider>
      )
    })

    // Wait for auth initialization
    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
      expect(mockSupabaseAdmin.auth.getSession).toHaveBeenCalled()
    })

    // Admin context should reject customer user by calling signOut
    await waitFor(() => {
      expect(mockSupabaseAdmin.auth.signOut).toHaveBeenCalled()
    })
  })

  test('should prevent admin session from appearing in customer context', async () => {
    // Setup admin session that both contexts will see
    const adminSession = {
      access_token: 'admin-token',
      user: { id: 'admin-1', email: '<EMAIL>', role: 'admin' }
    }

    // Both contexts get the same admin session (simulating contamination)
    mockSupabaseCustomer.auth.getSession.mockResolvedValue({
      data: { session: adminSession },
      error: null
    })

    mockSupabaseAdmin.auth.getSession.mockResolvedValue({
      data: { session: adminSession },
      error: null
    })

    // Admin context gets admin profile (should work)
    mockSupabaseAdmin.from().select().eq().single.mockResolvedValue({
      data: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
      error: null
    })

    // Customer context also gets admin profile (should be rejected)
    mockSupabaseCustomer.from().select().eq().single.mockResolvedValue({
      data: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
      error: null
    })

    await act(async () => {
      render(
        <AdminAuthProvider>
          <div data-testid="admin">Admin</div>
        </AdminAuthProvider>
      )
    })

    await act(async () => {
      render(
        <CustomerAuthProvider>
          <div data-testid="customer">Customer</div>
        </CustomerAuthProvider>
      )
    })

    // Wait for auth initialization
    await waitFor(() => {
      expect(mockSupabaseAdmin.auth.getSession).toHaveBeenCalled()
      expect(mockSupabaseCustomer.auth.getSession).toHaveBeenCalled()
    })

    // Customer context should reject admin user by calling signOut
    await waitFor(() => {
      expect(mockSupabaseCustomer.auth.signOut).toHaveBeenCalled()
    })
  })

  test('should handle simultaneous signout correctly', async () => {
    const mockSignOut = jest.fn().mockResolvedValue({ error: null })
    
    mockSupabaseCustomer.auth.signOut = mockSignOut
    mockSupabaseAdmin.auth.signOut = mockSignOut

    // Both contexts should be able to sign out independently
    expect(await mockSupabaseCustomer.auth.signOut()).toEqual({ error: null })
    expect(await mockSupabaseAdmin.auth.signOut()).toEqual({ error: null })
    
    expect(mockSignOut).toHaveBeenCalledTimes(2)
  })

  test('should handle malformed session data gracefully', async () => {
    // Setup malformed session data in localStorage
    window.localStorage.setItem('sb-customer-auth-token', 'invalid-json')
    window.localStorage.setItem('sb-admin-auth-token', 'also-invalid')

    // Auth contexts should handle this gracefully without crashing
    await act(async () => {
      render(
        <CustomerAuthProvider>
          <div data-testid="customer-malformed">Customer</div>
        </CustomerAuthProvider>
      )
    })

    await act(async () => {
      render(
        <AdminAuthProvider>
          <div data-testid="admin-malformed">Admin</div>
        </AdminAuthProvider>
      )
    })

    // Components should render without errors
    expect(screen.getByTestId('customer-malformed')).toBeInTheDocument()
    expect(screen.getByTestId('admin-malformed')).toBeInTheDocument()
  })
})
