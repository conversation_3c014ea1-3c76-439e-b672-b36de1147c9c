import { useState } from 'react'
import { 
  Sheet, 
  SheetContent, 
  SheetDescription, 
  SheetHeader, 
  SheetTitle
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  User, 
  Phone, 
  MapPin, 
  Navigation2, 
  Gauge, 
  Clock, 
  Copy,
  Zap,
  Share,
  Car,
  Fuel,
  Battery,
  Key,
  Route
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { GPSLocation } from '@/lib/gps-data'
import { useToast } from '@/hooks/use-toast'

interface TrackerDetailDrawerProps {
  vehicle: GPSLocation | undefined
  isOpen: boolean
  onClose: () => void
  onFollow: (carId: string) => void
  isFollowing: boolean
  timeWindow: 'live' | '15m' | '1h' | '24h'
}

export function TrackerDetailDrawer({
  vehicle,
  isOpen,
  onClose,
  onFollow,
  isFollowing,
  timeWindow
}: TrackerDetailDrawerProps) {
  const { toast } = useToast()
  const [isCopying, setIsCopying] = useState(false)

  if (!vehicle) return null

  const copyToClipboard = async (text: string, label: string) => {
    setIsCopying(true)
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard`,
      })
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Unable to copy to clipboard",
        variant: "destructive",
      })
    }
    setIsCopying(false)
  }

  const handleShareView = async () => {
    const shareUrl = `${window.location.origin}/admin/tracker?vehicle=${vehicle.carId}&lat=${vehicle.latitude}&lng=${vehicle.longitude}`
    await copyToClipboard(shareUrl, "Share link")
  }

  // Mock location data
  const mockAddress = "Makati City, Metro Manila, Philippines"
  const mockGeofence = "Manila CBD Zone"

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-full sm:w-96 sm:max-w-96 overflow-y-auto">
        <SheetHeader className="space-y-4 pb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Car className="w-6 h-6 text-blue-600" />
              <div>
                <SheetTitle className="text-xl font-bold">
                  {vehicle.carPlate}
                </SheetTitle>
                <SheetDescription className="text-sm text-muted-foreground">
                  {vehicle.carModel}
                </SheetDescription>
              </div>
            </div>
            <Button
              variant={isFollowing ? "primary" : "secondary"}
              size="sm"
              onClick={() => onFollow(vehicle.carId)}
              className="h-9 px-4"
            >
              <Zap className="w-4 h-4 mr-2" />
              {isFollowing ? 'Following' : 'Follow Live'}
            </Button>
          </div>
        </SheetHeader>

        <div className="space-y-4 mt-6">
          {/* Essential Vehicle Information - Condensed */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-base font-semibold">Vehicle Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Driver Information */}
              {vehicle.driverName && (
                <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-3">
                    <User className="w-5 h-5 text-blue-600" />
                    <div>
                      <div className="font-medium">{vehicle.driverName}</div>
                      <div className="text-xs text-muted-foreground">Driver</div>
                    </div>
                  </div>
                  <Button
                    variant="tertiary"
                    size="xs"
                    onClick={() => copyToClipboard("+63 ************", "Phone number")}
                    disabled={isCopying}
                    className="h-8 w-8 p-0"
                  >
                    <Phone className="w-4 h-4" />
                  </Button>
                </div>
              )}

              {/* Current Location */}
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <MapPin className="w-4 h-4" />
                  Current Location
                </div>
                <div className="pl-6 space-y-2">
                  <p className="text-sm font-medium">{mockAddress}</p>
                  <div className="flex items-center justify-between">
                    <span className="font-mono text-xs text-muted-foreground">
                      {vehicle.latitude.toFixed(6)}, {vehicle.longitude.toFixed(6)}
                    </span>
                    <Button
                      variant="tertiary"
                      size="xs"
                      onClick={() => copyToClipboard(
                        `${vehicle.latitude.toFixed(6)}, ${vehicle.longitude.toFixed(6)}`,
                        "Coordinates"
                      )}
                      disabled={isCopying}
                      className="h-6 px-2"
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Real-time Telemetry */}
              <div className="space-y-3">
                <div className="text-sm font-medium text-muted-foreground">Real-time Status</div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Gauge className="w-4 h-4 text-blue-600" />
                    <div>
                      <div className="text-lg font-bold">{vehicle.speed}</div>
                      <div className="text-xs text-muted-foreground">km/h</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Navigation2 className="w-4 h-4 text-blue-600" />
                    <div>
                      <div className="text-lg font-bold">{vehicle.heading}°</div>
                      <div className="text-xs text-muted-foreground">Heading</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* System Status */}
              <div className="grid grid-cols-2 gap-4 pt-2 border-t">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Key className="w-4 h-4" />
                    <span>Ignition</span>
                  </div>
                  <span className="font-medium text-sm">
                    {vehicle.status === 'active' ? 'ON' : 'OFF'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Fuel className="w-4 h-4" />
                    <span>Fuel</span>
                  </div>
                  <span className="font-medium text-sm">75%</span>
                </div>
              </div>

              {/* Last Update */}
              <div className="flex items-center justify-between pt-3 border-t">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span>Last updated</span>
                </div>
                <div className="text-right">
                  <div className="font-medium text-sm">
                    {vehicle.timestamp.toLocaleTimeString()}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatDistanceToNow(vehicle.timestamp, { addSuffix: true })}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="secondary"
                  size="sm"
                  className="justify-start"
                  onClick={() => {
                    toast({
                      title: "Map Centered",
                      description: `Centered map on ${vehicle.carPlate}`,
                    })
                  }}
                >
                  <MapPin className="w-4 h-4 mr-2" />
                  Center Map
                </Button>

                <Button
                  variant="secondary"
                  size="sm"
                  className="justify-start"
                  onClick={handleShareView}
                >
                  <Share className="w-4 h-4 mr-2" />
                  Share View
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </SheetContent>
    </Sheet>
  )
}
