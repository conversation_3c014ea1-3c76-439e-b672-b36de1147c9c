/**
 * Customer-side route paths constants
 * This file contains all customer-related route paths to ensure consistency
 * and avoid hardcoded path strings throughout the application.
 */

export const CUSTOMER_PATHS = {
  // Auth routes
  LOGIN: '/customer/login',
  SIGNUP: '/customer/signup',
  FORGOT_PASSWORD: '/customer/forgot-password',
  
  // Main pages
  DASHBOARD: '/customer/dashboard',
  CATALOG: '/customer/catalog',
  CONTACT: '/customer/contact',
  FAQ: '/customer/faq',
  TERMS: '/customer/terms',
  SETTINGS: '/customer/settings',
  
  // Booking flow
  BOOKING_FLOW: '/customer/booking/flow',
  BOOKING_CAR: (carId: string) => `/customer/booking/${carId}`,
  
  // Public routes (no customer prefix needed)
  HOME: '/',
} as const

/**
 * Helper function to build booking flow URL with query parameters
 */
export function buildBookingFlowUrl(params?: {
  carId?: string
  pickUpLocation?: string
  dropOffLocation?: string
  pickUpDateTime?: string
  dropOffDateTime?: string
  pickupGarageChecked?: boolean
  dropoffSameAsPickupChecked?: boolean
  specialService?: string
  serviceType?: string
  pickupTimePeriod?: "day" | "night"
  returnTimePeriod?: "day" | "night"
  deliveryFee?: number
}) {
  const url = new URL(CUSTOMER_PATHS.BOOKING_FLOW, window.location.origin)
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.set(key, String(value))
      }
    })
  }
  
  return url.pathname + url.search
}
