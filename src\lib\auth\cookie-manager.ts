/**
 * Authentication Cookie Isolation Manager
 *
 * This module fixes cookie inconsistencies between admin and customer contexts
 * by implementing strict storage isolation and session management.
 */

import { SessionValidator } from './session-validator'

export class AuthCookieManager {
  private static instance: AuthCookieManager
  private storagePrefix: string
  private contextType: 'admin' | 'customer'
  private cacheTimeouts: Map<string, NodeJS.Timeout> = new Map()

  private constructor(contextType: 'admin' | 'customer') {
    this.contextType = contextType
    this.storagePrefix = `sb-${contextType}-auth-token`
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`🍪 AuthCookieManager initialized for ${contextType} context`)
    }
  }

  static getInstance(contextType: 'admin' | 'customer'): AuthCookieManager {
    const key = `${contextType}_auth_manager`
    if (!(window as any)[key]) {
      (window as any)[key] = new AuthCookieManager(contextType)
    }
    return (window as any)[key]
  }

  /**
   * Clears all authentication data for this context
   */
  clearAuthData(): void {
    if (typeof window === 'undefined') return

    const keysToRemove: string[] = []
    
    // Find all keys that belong to this context
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(this.storagePrefix)) {
        keysToRemove.push(key)
      }
    }

    // Remove all context-specific keys
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
      // Reduce logging frequency for better performance
      if (process.env.NODE_ENV === 'development' && Math.random() < 0.2) {
        console.log(`🗑️ Removed ${this.contextType} auth key: ${key}`)
      }
    })

    // Also clear any session storage
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i)
      if (key && key.startsWith(this.storagePrefix)) {
        sessionStorage.removeItem(key)
      }
    }
  }

  /**
   * Prevents cross-contamination by checking for wrong context data
   * Optimized to run less frequently for better performance
   */
  preventCrossContamination(): void {
    if (typeof window === 'undefined') return

    // Only run this check very occasionally for performance (2% chance)
    if (Math.random() > 0.02) return

    const wrongContext = this.contextType === 'admin' ? 'customer' : 'admin'
    const wrongPrefix = `sb-${wrongContext}-auth-token`
    
    // Quick check for wrong context data
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(wrongPrefix)) {
        const value = localStorage.getItem(key)
        if (value) {
          console.warn(`⚠️ Found ${wrongContext} auth data in ${this.contextType} context: ${key}`)
          // Auto-remove conflicting data for better performance
          localStorage.removeItem(key)
        }
      }
    }
  }

  // Session validation cache to avoid repeated checks
  private sessionValidationCache = new Map<string, boolean>()
  
  /**
   * Optimized session validation with caching using standardized validator
   */
  validateSession(session: any): boolean {
    if (!session || !session.user) return true // No session is valid

    // Check cache first
    const cacheKey = `${session.user.id}-${session.user.email}-${this.contextType}`
    if (this.sessionValidationCache.has(cacheKey)) {
      return this.sessionValidationCache.get(cacheKey) || false
    }

    try {
      // Use standardized session validation with proper import
      const validationResult = SessionValidator.validateSession(session, this.contextType)
      const isValid = validationResult.isValid

      // Log validation details for debugging
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 Session validation [${this.contextType}]:`, {
          isValid,
          reason: validationResult.reason,
          userId: session.user.id,
          email: session.user.email
        })
      }

      // Cache the result
      this.sessionValidationCache.set(cacheKey, isValid)

      // Clear cache after 5 minutes to prevent memory leaks
      const timeoutId = setTimeout(() => {
        this.sessionValidationCache.delete(cacheKey)
      }, 5 * 60 * 1000)

      // Store timeout ID for cleanup
      this.cacheTimeouts.set(cacheKey, timeoutId)

      return isValid
    } catch (error) {
      // If validation fails due to an error, log it and return true to avoid breaking auth
      console.warn(`⚠️ Session validation error for ${this.contextType} context:`, error)
      return true
    }
  }

  /**
   * Clean up cache timeouts to prevent memory leaks
   */
  clearCacheTimeouts(): void {
    this.cacheTimeouts.forEach((timeoutId) => {
      clearTimeout(timeoutId)
    })
    this.cacheTimeouts.clear()
    this.sessionValidationCache.clear()
  }

  /**
   * Destructor-like method for cleanup
   */
  destroy(): void {
    this.clearCacheTimeouts()
  }

  /**
   * Gets all storage keys for this context
   */
  getStorageKeys(): string[] {
    if (typeof window === 'undefined') return []

    const keys: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(this.storagePrefix)) {
        keys.push(key)
      }
    }
    return keys
  }

  /**
   * Diagnostic information about storage state
   */
  getDiagnostics(): {
    contextType: string
    storagePrefix: string
    keys: string[]
    hasData: boolean
    crossContamination: string[]
  } {
    const keys = this.getStorageKeys()
    const wrongContext = this.contextType === 'admin' ? 'customer' : 'admin'
    const wrongPrefix = `sb-${wrongContext}-auth-token`
    
    const crossContamination: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(wrongPrefix)) {
        crossContamination.push(key)
      }
    }

    return {
      contextType: this.contextType,
      storagePrefix: this.storagePrefix,
      keys,
      hasData: keys.length > 0,
      crossContamination
    }
  }
}

/**
 * Utility functions for cookie management
 */
export const AuthCookieUtils = {
  /**
   * Force clear all authentication data
   */
  clearAllAuthData(): void {
    if (typeof window === 'undefined') return

    const keysToRemove: string[] = []
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.includes('sb-') && key.includes('auth')) {
        keysToRemove.push(key)
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key))
    
    // Also clear session storage
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i)
      if (key && key.includes('sb-') && key.includes('auth')) {
        sessionStorage.removeItem(key)
      }
    }

    console.log('🧹 Cleared all authentication data')
  },

  /**
   * Get comprehensive diagnostics
   */
  getComprehensiveDiagnostics(): {
    allStorageKeys: string[]
    customerKeys: string[]
    adminKeys: string[]
    genericKeys: string[]
    issues: string[]
  } {
    if (typeof window === 'undefined') {
      return {
        allStorageKeys: [],
        customerKeys: [],
        adminKeys: [],
        genericKeys: [],
        issues: ['Window object not available']
      }
    }

    const allStorageKeys: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.includes('sb-')) {
        allStorageKeys.push(key)
      }
    }

    const customerKeys = allStorageKeys.filter(k => k.includes('customer'))
    const adminKeys = allStorageKeys.filter(k => k.includes('admin'))
    const genericKeys = allStorageKeys.filter(k => !k.includes('customer') && !k.includes('admin'))

    const issues: string[] = []
    
    if (allStorageKeys.length === 0) {
      issues.push('No Supabase storage keys found')
    }
    
    if (customerKeys.length === 0) {
      issues.push('No customer-specific storage keys detected')
    }
    
    if (adminKeys.length === 0) {
      issues.push('No admin-specific storage keys detected')
    }
    
    if (genericKeys.length > 0) {
      issues.push(`Found ${genericKeys.length} generic storage keys that should be context-specific`)
    }

    return {
      allStorageKeys,
      customerKeys,
      adminKeys,
      genericKeys,
      issues
    }
  }
}
