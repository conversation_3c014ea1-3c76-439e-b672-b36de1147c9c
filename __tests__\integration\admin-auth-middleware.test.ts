/**
 * Integration test for admin authentication middleware fix
 * 
 * This test verifies that the middleware correctly handles admin authentication
 * and doesn't redirect authenticated admin users to the login page.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { updateSession } from '@/lib/supabase/middleware'

// Mock the Supabase server client
const mockSupabaseClient = {
  auth: {
    getUser: vi.fn(),
    setSession: vi.fn(),
  },
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn(),
      })),
    })),
  })),
}

vi.mock('@supabase/ssr', () => ({
  createServerClient: vi.fn(() => mockSupabaseClient),
}))

describe('Admin Authentication Middleware Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Admin Route Protection', () => {
    it('should allow authenticated admin users to access admin routes', async () => {
      // Mock authenticated admin user
      const mockAdminUser = {
        id: 'admin-123',
        email: '<EMAIL>',
        user_metadata: { role: 'admin' }
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockAdminUser },
        error: null
      })

      // Mock profile lookup
      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn().mockResolvedValue({
              data: { role: 'admin' },
              error: null
            })
          }))
        }))
      })

      // Create request with admin auth cookies
      const request = new NextRequest('http://localhost:3000/admin/dashboard', {
        headers: {
          cookie: 'sb-admin-auth-token.access_token=valid-admin-token; sb-admin-auth-token.refresh_token=valid-refresh-token'
        }
      })

      const response = await updateSession(request)

      // Should allow access (no redirect)
      expect(response.status).toBe(200)
      expect(response.headers.get('location')).toBeNull()
    })

    it('should allow super admin email to access admin routes', async () => {
      // Mock super admin user
      const mockSuperAdminUser = {
        id: 'super-admin-123',
        email: '<EMAIL>',
        user_metadata: {}
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockSuperAdminUser },
        error: null
      })

      const request = new NextRequest('http://localhost:3000/admin/cars', {
        headers: {
          cookie: 'sb-admin-auth-token.access_token=valid-super-admin-token'
        }
      })

      const response = await updateSession(request)

      // Should allow access
      expect(response.status).toBe(200)
      expect(response.headers.get('location')).toBeNull()
    })

    it('should redirect unauthenticated users from admin routes', async () => {
      // Mock no user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      const request = new NextRequest('http://localhost:3000/admin/settings')

      const response = await updateSession(request)

      // Should redirect to admin login
      expect(response.status).toBe(307)
      const location = response.headers.get('location')
      expect(location).toContain('/admin-auth')
      expect(location).toContain('redirect=%2Fadmin%2Fsettings')
    })

    it('should redirect customer users from admin routes', async () => {
      // Mock customer user
      const mockCustomerUser = {
        id: 'customer-123',
        email: '<EMAIL>',
        user_metadata: { role: 'customer' }
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockCustomerUser },
        error: null
      })

      // Mock profile lookup
      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn().mockResolvedValue({
              data: { role: 'customer' },
              error: null
            })
          }))
        }))
      })

      const request = new NextRequest('http://localhost:3000/admin/bookings', {
        headers: {
          cookie: 'sb-customer-auth-token.access_token=valid-customer-token'
        }
      })

      const response = await updateSession(request)

      // Should redirect to admin login with error
      expect(response.status).toBe(307)
      const location = response.headers.get('location')
      expect(location).toContain('/admin-auth')
      expect(location).toContain('error=Access%20denied')
    })
  })

  describe('Customer Route Handling', () => {
    it('should allow customer users to access customer routes', async () => {
      // Mock customer user
      const mockCustomerUser = {
        id: 'customer-123',
        email: '<EMAIL>',
        user_metadata: { role: 'customer' }
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockCustomerUser },
        error: null
      })

      const request = new NextRequest('http://localhost:3000/customer/dashboard', {
        headers: {
          cookie: 'sb-customer-auth-token.access_token=valid-customer-token'
        }
      })

      const response = await updateSession(request)

      // Should allow access
      expect(response.status).toBe(200)
      expect(response.headers.get('location')).toBeNull()
    })

    it('should redirect admin users from customer protected routes to admin dashboard', async () => {
      // Mock admin user
      const mockAdminUser = {
        id: 'admin-123',
        email: '<EMAIL>',
        user_metadata: { role: 'admin' }
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockAdminUser },
        error: null
      })

      // Mock profile lookup
      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn().mockResolvedValue({
              data: { role: 'admin' },
              error: null
            })
          }))
        }))
      })

      const request = new NextRequest('http://localhost:3000/customer/dashboard', {
        headers: {
          cookie: 'sb-admin-auth-token.access_token=valid-admin-token'
        }
      })

      const response = await updateSession(request)

      // Should redirect to admin dashboard
      expect(response.status).toBe(307)
      expect(response.headers.get('location')).toContain('/admin')
    })
  })

  describe('Public Route Handling', () => {
    it('should allow access to public routes without authentication', async () => {
      // Mock no user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      const request = new NextRequest('http://localhost:3000/')

      const response = await updateSession(request)

      // Should allow access
      expect(response.status).toBe(200)
      expect(response.headers.get('location')).toBeNull()
    })

    it('should allow access to customer catalog without authentication', async () => {
      // Mock no user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      const request = new NextRequest('http://localhost:3000/customer/catalog')

      const response = await updateSession(request)

      // Should allow access
      expect(response.status).toBe(200)
      expect(response.headers.get('location')).toBeNull()
    })
  })

  describe('Auth Page Redirects', () => {
    it('should redirect authenticated admin users away from admin auth page', async () => {
      // Mock admin user
      const mockAdminUser = {
        id: 'admin-123',
        email: '<EMAIL>',
        user_metadata: { role: 'admin' }
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockAdminUser },
        error: null
      })

      // Mock profile lookup
      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn().mockResolvedValue({
              data: { role: 'admin' },
              error: null
            })
          }))
        }))
      })

      const request = new NextRequest('http://localhost:3000/admin-auth', {
        headers: {
          cookie: 'sb-admin-auth-token.access_token=valid-admin-token'
        }
      })

      const response = await updateSession(request)

      // Should redirect to admin dashboard
      expect(response.status).toBe(307)
      expect(response.headers.get('location')).toContain('/admin')
    })
  })
})
