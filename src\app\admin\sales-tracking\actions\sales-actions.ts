"use server";

import { createContextClient } from "@/lib/supabase/server";

// Types for sales tracking data
export interface SalesBookingItem {
  bookingId: string;
  bookingRef?: string; // Added booking reference ID field
  vehicleId: string;
  plateNo: string;
  model: string;
  location: string;
  channel: "Web" | "App" | "Partner";
  pickupDate: Date;
  dropoffDate: Date;
  rentalDays: number;
  baseRate: number;
  addonsTotal: number;
  discounts: number;
  taxes: number;
  fees: number;
  totalAmount: number;
  paymentStatus: "Paid" | "Pending" | "Refunded" | "Partial";
  customerName: string;
  // Enhanced customer information
  customerEmail?: string;
  customerPhone?: string;
  customerFirstName?: string;
  customerMiddleInitial?: string;
  customerLastName?: string;
  customerFullName?: string;
  // Payment information
  paymentId?: string;
  paymentRef?: string;
  createdAt: Date;
}

export interface SalesAggregates {
  revenueTotal: number;
  bookingsCount: number;
  avgOrderValue: number;
  occupancyRate: number;
  revenuePerVehicle: number;
  refundsTotal: number;
  previousPeriodRevenue: number;
  previousPeriodBookings: number;
  previousPeriodAOV: number;
  previousPeriodOccupancy: number;
  previousPeriodRefunds: number;
  avgDailySales: number;
  avgWeeklySales: number;
  avgMonthlySales: number;
  totalCustomers: number;
  repeatCustomers: number;
}

export interface SalesFilters {
  dateFrom: string;
  dateTo: string;
  location?: string;
  vehicleClass?: string;
  channel?: string;
  paymentStatus?: string;
  searchQuery?: string;
}

// Helper function to normalize payment status
function normalizePaymentStatus(status: string): "Paid" | "Pending" | "Refunded" | "Partial" {
  const normalizedStatus = status.toLowerCase();
  
  if (normalizedStatus === "paid" || normalizedStatus === "approved" || normalizedStatus === "verified") {
    return "Paid";
  }
  
  if (normalizedStatus === "pending" || normalizedStatus === "pending verification") {
    return "Pending";
  }
  
  if (normalizedStatus === "refunded") {
    return "Refunded";
  }
  
  if (normalizedStatus === "rejected" || normalizedStatus === "failed") {
    return "Refunded"; // Treat rejected/failed as refunded for sales purposes
  }
  
  return "Pending"; // Default fallback
}

// Helper function to calculate rental days
function calculateRentalDays(pickupDate: Date, dropoffDate: Date): number {
  const diffTime = Math.abs(dropoffDate.getTime() - pickupDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(1, diffDays); // Minimum 1 day
}

export async function getSalesData(filters: SalesFilters): Promise<{
  data: SalesBookingItem[] | null;
  error: any;
}> {
  try {
    const supabase = await createContextClient('admin');

    // Check admin authentication with detailed error handling
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError) {
      console.error("Authentication error in getSalesData:", authError);
      return { 
        data: null, 
        error: { 
          message: "Authentication failed", 
          details: authError.message,
          code: authError.code || "AUTH_ERROR",
          type: "authentication_error"
        } 
      };
    }
    
    if (!user) {
      return { 
        data: null, 
        error: { 
          message: "User not authenticated", 
          details: "No user session found. Please log in to access sales data.",
          code: "NO_USER",
          type: "authentication_error"
        } 
      };
    }

    // Get user profile to verify admin role with error handling
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("role")
      .eq("id", user.id)
      .single();

    if (profileError) {
      console.error("Profile fetch error in getSalesData:", profileError);
      return { 
        data: null, 
        error: { 
          message: "Failed to fetch user profile", 
          details: profileError.message,
          code: profileError.code || "PROFILE_ERROR",
          type: "database_error"
        } 
      };
    }

    if (!profile || (profile.role !== "admin" && profile.role !== "super_admin")) {
      return { 
        data: null, 
        error: { 
          message: "Admin access required", 
          details: `User role '${profile?.role || 'unknown'}' does not have permission to access sales data.`,
          code: "INSUFFICIENT_PERMISSIONS",
          type: "authorization_error"
        } 
      };
    }

    // Fetch bookings data directly using server-side client
    const { data: bookingsData, error: bookingsError } = await supabase
      .from('bookings')
      .select(`
        *,
        car:cars(*),
        customer:profiles(
          id,
          email,
          full_name,
          first_name,
          middle_initial,
          last_name,
          phone,
          role
        )
      `)
      .order('created_at', { ascending: false });

    if (bookingsError) {
      console.error('Error fetching bookings:', bookingsError);
      console.error('Bookings error details:', {
        message: bookingsError.message,
        details: bookingsError.details,
        hint: bookingsError.hint,
        code: bookingsError.code
      });
      return { data: null, error: { 
        message: bookingsError.message || "Failed to fetch bookings data",
        details: bookingsError.details || "Unable to retrieve bookings from database",
        code: bookingsError.code || "BOOKINGS_FETCH_ERROR",
        type: "database_error"
      } };
    }

    if (!bookingsData || bookingsData.length === 0) {
      return { data: [], error: null };
    }

    // Attach latest payment status to each booking
    const bookingIds = bookingsData.map((b: any) => b.id);
    let payments: any[] = [];
    
    // Only fetch payments if we have bookings
    if (bookingIds.length > 0) {
      const { data: paymentsData, error: paymentsError } = await supabase
        .from('payments')
        .select('id, payment_ref, booking_id, status, amount, created_at, transaction_date')
        .in('booking_id', bookingIds)
        .order('created_at', { ascending: false });

      if (paymentsError) {
        console.error('Error fetching payments:', paymentsError);
        console.error('Payments error details:', {
          message: paymentsError.message,
          details: paymentsError.details,
          hint: paymentsError.hint,
          code: paymentsError.code
        });
        // Don't fail the whole operation if payments can't be fetched
        // Continue with empty payments array
        payments = [];
      } else {
        payments = paymentsData || [];
      }
    }

    if (payments.length > 0) {
      const latestByBooking = new Map<string, { status?: string; amount?: number; id?: string; payment_ref?: string }>();
      for (const p of payments as Array<{ booking_id: string; status?: string; amount?: number; id: string; payment_ref?: string }>) {
        // Since results are ordered desc by created_at, first seen is latest
        if (!latestByBooking.has(p.booking_id)) {
          latestByBooking.set(p.booking_id, {
            status: p.status,
            amount: p.amount,
            id: p.id,
            payment_ref: p.payment_ref
          });
        }
      }

      for (const b of bookingsData as Array<Record<string, unknown>>) {
        const lp = latestByBooking.get(b.id as string);
        if (lp) {
          b.latest_payment_status = lp.status || null;
          b.latest_payment_amount = lp.amount ?? null;
          b.latest_payment_id = lp.id || null;
          b.latest_payment_ref = lp.payment_ref || null;
        } else {
          b.latest_payment_status = null;
          b.latest_payment_amount = null;
          b.latest_payment_id = null;
          b.latest_payment_ref = null;
        }
      }
    }

    // Transform data to match SalesBookingItem interface using same logic as admin bookings
    const salesData: SalesBookingItem[] = bookingsData.map((booking: any) => {
      const pickupDate = new Date(booking.pickup_datetime);
      const dropoffDate = new Date(booking.dropoff_datetime);
      const rentalDays = Math.max(
        1,
        Math.ceil((+dropoffDate - +pickupDate) / (1000 * 60 * 60 * 24))
      );
      
      // Use the same payment status logic as admin bookings page
      const paymentStatus = (() => {
        if (booking.status === "Cancelled") return "Refunded";
        const latest = booking.latest_payment_status as string | null | undefined;
        if (latest && typeof latest === "string") {
          const norm = latest.trim().toLowerCase();
          // Map database status values to display values
          if (norm === "paid") {
            return "Paid";
          }
          if (norm === "pending verification") {
            return "Pending";
          }
          if (norm === "pending") {
            return "Pending";
          }
          if (norm === "rejected") {
            return "Pending";
          }
          if (norm === "failed") {
            return "Pending";
          }
          if (norm === "refunded") {
            return "Refunded";
          }
        }
        return "Pending";
      })();

      // Calculate breakdown - use actual total_amount as base rate
      const totalAmount = booking.total_amount || 0;
      const baseRate = totalAmount; // Use total amount as base rate for now
      const addonsTotal = 0; // Add logic for addons if available
      const discounts = 0; // Add logic for discounts if available
      const taxes = 0; // Taxes already included in total_amount
      const fees = 0; // Fees already included in total_amount

      // Build customer name from available fields
      const customer = booking.customer;
      let customerName = "Unknown Customer";
      if (customer) {
        if (customer.full_name && customer.full_name.trim()) {
          customerName = customer.full_name.trim();
        } else if (customer.first_name || customer.last_name) {
          // Construct name from separate fields
          const parts = [];
          if (customer.first_name) parts.push(customer.first_name.trim());
          if (customer.middle_initial) parts.push(customer.middle_initial.trim());
          if (customer.last_name) parts.push(customer.last_name.trim());
          customerName = parts.join(" ");
        } else if (customer.email) {
          customerName = customer.email;
        }
      }

      return {
        bookingId: booking.id,
        bookingRef: booking.booking_ref || null, // Include booking reference ID
        vehicleId: booking.car?.id || "N/A",
        plateNo: booking.car?.plate_number || "N/A",
        model: booking.car?.model || "Unknown Vehicle",
        location: booking.pickup_location || "Unknown",
        channel: "Web" as const, // Default to Web, add logic to determine channel
        pickupDate,
        dropoffDate,
        rentalDays,
        baseRate,
        addonsTotal,
        discounts,
        taxes,
        fees,
        totalAmount,
        paymentStatus: paymentStatus as "Paid" | "Pending" | "Refunded" | "Partial",
        customerName,
        // Enhanced customer information
        customerEmail: customer?.email || null,
        customerPhone: customer?.phone || null,
        customerFirstName: customer?.first_name || null,
        customerMiddleInitial: customer?.middle_initial || null,
        customerLastName: customer?.last_name || null,
        customerFullName: customer?.full_name || null,
        // Payment information
        paymentId: booking.latest_payment_id || null,
        paymentRef: booking.latest_payment_ref || null,
        createdAt: new Date(booking.created_at),
      };
    });

    // Apply filters
    let filteredData = salesData;

    // Date range filter
    if (filters.dateFrom && filters.dateTo) {
      const fromDate = new Date(filters.dateFrom);
      const toDate = new Date(filters.dateTo);
      filteredData = filteredData.filter(
        (item) => item.pickupDate >= fromDate && item.pickupDate <= toDate
      );
    }

    // Location filter
    if (filters.location && filters.location !== "All Locations") {
      filteredData = filteredData.filter(item => item.location === filters.location);
    }

    // Payment status filter
    if (filters.paymentStatus && filters.paymentStatus !== "All Statuses") {
      filteredData = filteredData.filter(item => item.paymentStatus === filters.paymentStatus);
    }

    // Search filter
    if (filters.searchQuery && filters.searchQuery.trim()) {
      const query = filters.searchQuery.toLowerCase();
      filteredData = filteredData.filter(
        (item) =>
          item.bookingId.toLowerCase().includes(query) ||
          item.plateNo.toLowerCase().includes(query) ||
          item.model.toLowerCase().includes(query) ||
          item.customerName.toLowerCase().includes(query)
      );
    }

    return { data: filteredData, error: null };

  } catch (error) {
    console.error("Unexpected error in getSalesData:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to fetch sales data";
    const errorDetails = {
      message: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : "UnknownError"
    };
    console.error("Error details:", errorDetails);
    
    return { 
      data: null, 
      error: { 
        message: errorMessage,
        details: `An unexpected error occurred while fetching sales data: ${errorMessage}`,
        code: "UNEXPECTED_ERROR",
        type: "unexpected_error",
        originalError: error instanceof Error ? error.name : String(error)
      } 
    };
  }
}

export async function getSalesAggregates(
  salesData: SalesBookingItem[],
  filters: SalesFilters
): Promise<{
  data: SalesAggregates | null;
  error: any;
}> {
  try {
    const supabase = await createContextClient('admin');

    // Debug logging for aggregates calculation
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log("Calculating aggregates for salesData:", salesData.length, "items");
    }
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log("Payment statuses in data:", salesData.map(b => b.paymentStatus));
    }
    
    // Only include paid bookings for revenue calculations
    const paidBookings = salesData.filter(booking => booking.paymentStatus?.toLowerCase() === "paid");
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log("Paid bookings count:", paidBookings.length);
    }
    
    // Calculate current period aggregates
    const revenueTotal = paidBookings.reduce((sum, booking) => sum + booking.totalAmount, 0);
    const bookingsCount = paidBookings.length;
    // Fix: Average order value should be calculated from paid bookings only
    const avgOrderValue = bookingsCount > 0 ? revenueTotal / bookingsCount : 0;
    
    // Calculate refunds
    const refundedBookings = salesData.filter(booking => booking.paymentStatus?.toLowerCase() === "refunded");
    const refundsTotal = refundedBookings.reduce((sum, booking) => sum + booking.totalAmount, 0);

    // Calculate time-based averages
    const dateFrom = new Date(filters.dateFrom);
    const dateTo = new Date(filters.dateTo);
    const daysDiff = Math.ceil((dateTo.getTime() - dateFrom.getTime()) / (1000 * 60 * 60 * 24));
    const weeksDiff = Math.ceil(daysDiff / 7);
    const monthsDiff = Math.ceil(daysDiff / 30);

    const avgDailySales = daysDiff > 0 ? revenueTotal / daysDiff : 0;
    const avgWeeklySales = weeksDiff > 0 ? revenueTotal / weeksDiff : 0;
    const avgMonthlySales = monthsDiff > 0 ? revenueTotal / monthsDiff : 0;

    // Calculate customer metrics
    const uniqueCustomers = new Set(paidBookings.map(booking => booking.customerName));
    const totalCustomers = uniqueCustomers.size;
    
    // Get repeat customers (customers with more than one booking)
    const customerBookingCounts = new Map<string, number>();
    paidBookings.forEach(booking => {
      const count = customerBookingCounts.get(booking.customerName) || 0;
      customerBookingCounts.set(booking.customerName, count + 1);
    });
    const repeatCustomers = Array.from(customerBookingCounts.values()).filter(count => count > 1).length;

    // Calculate fleet utilization (simplified)
    const { count: totalVehicles } = await supabase
      .from("cars")
      .select("id", { count: "exact" });
    
    const vehicleCount = totalVehicles || 12; // Fallback to 12 if query fails
    const revenuePerVehicle = revenueTotal / vehicleCount;
    
    // Calculate occupancy rate (simplified - based on booking days vs available days)
    const totalBookingDays = paidBookings.reduce((sum, booking) => sum + booking.rentalDays, 0);
    const totalAvailableDays = vehicleCount * daysDiff;
    const occupancyRate = totalAvailableDays > 0 ? (totalBookingDays / totalAvailableDays) * 100 : 0;

    // Calculate previous period data for comparison
    const previousPeriodStart = new Date(dateFrom);
    previousPeriodStart.setDate(previousPeriodStart.getDate() - daysDiff);
    const previousPeriodEnd = new Date(dateFrom);

    const { data: previousPeriodData } = await getSalesData({
      ...filters,
      dateFrom: previousPeriodStart.toISOString(),
      dateTo: previousPeriodEnd.toISOString(),
    });

    const previousPaidBookings = previousPeriodData?.filter(booking => booking.paymentStatus === "Paid") || [];
    const previousPeriodRevenue = previousPaidBookings.reduce((sum, booking) => sum + booking.totalAmount, 0);
    const previousPeriodBookings = previousPaidBookings.length;
    const previousPeriodAOV = previousPeriodBookings > 0 ? previousPeriodRevenue / previousPeriodBookings : 0;
    const previousRefundedBookings = previousPeriodData?.filter(booking => booking.paymentStatus === "Refunded") || [];
    const previousPeriodRefunds = previousRefundedBookings.reduce((sum, booking) => sum + booking.totalAmount, 0);
    
    // Calculate previous period occupancy
    const previousTotalBookingDays = previousPaidBookings.reduce((sum, booking) => sum + booking.rentalDays, 0);
    const previousTotalAvailableDays = vehicleCount * daysDiff;
    const previousPeriodOccupancy = previousTotalAvailableDays > 0 ? (previousTotalBookingDays / previousTotalAvailableDays) * 100 : 0;

    const aggregates: SalesAggregates = {
      revenueTotal,
      bookingsCount,
      avgOrderValue,
      occupancyRate,
      revenuePerVehicle,
      refundsTotal,
      previousPeriodRevenue,
      previousPeriodBookings,
      previousPeriodAOV,
      previousPeriodOccupancy,
      previousPeriodRefunds,
      avgDailySales,
      avgWeeklySales,
      avgMonthlySales,
      totalCustomers,
      repeatCustomers,
    };

    return { data: aggregates, error: null };

  } catch (error) {
    console.error("Error calculating sales aggregates:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to calculate sales aggregates";
    return { 
      data: null, 
      error: { 
        message: errorMessage,
        details: `An error occurred while calculating sales aggregates: ${errorMessage}`,
        code: "AGGREGATES_CALCULATION_ERROR",
        type: "calculation_error",
        originalError: error instanceof Error ? error.name : String(error)
      } 
    };
  }
}
