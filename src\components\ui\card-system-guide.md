# Card Component System Guide

## Overview

The Card component system has been redesigned to provide a consistent, accessible, and responsive foundation for all card-based UI components in the application. This guide covers the implementation, usage, and design principles of the new Card system.

## ✅ Checklist Implementation Status

### 1. Style ✅
- **Consistent default visual style** - All cards inherit from `cardVariants` with theme-based colors
- **Background** - Uses `bg-card` theme token (light/dark mode compatible)
- **Border** - Consistent `border` with theme-based color
- **Shadow** - Standardized shadows across variants (`shadow-sm` to `shadow-lg`)

### 2. Consistency ✅  
- **Base card style** - `cardVariants` using class-variance-authority provides unified styling
- **Shared styling** - No more inline overrides, all variations use the variant system
- **Inheritance** - All card components inherit from the same base styles

### 3. Spacing ✅
- **4px spacing scale** - Implemented as utility classes (`.card-spacing-xs` to `.card-spacing-2xl`)
- **Token-based spacing** - Replaced arbitrary values with standardized scale
- **Visual balance** - Proper spacing between text, images, and buttons

### 4. Responsiveness ✅
- **Mobile-first design** - Cards stack vertically on mobile (320px+)
- **Tablet layout** - Two-column grid on tablets (768px+)  
- **Desktop layout** - Multi-column grid on desktop (1024px+ and 1440px+)
- **CSS Grid/Flexbox** - Responsive `.card-grid` utility maintains structure

### 5. Content Hierarchy ✅
- **Proper structure** - CardTitle → CardDescription → CardContent → CardAction
- **Primary action placement** - CardAction positioned at bottom
- **Typography hierarchy** - Consistent heading/body/meta text styling
- **Semantic components** - CardTitle (h3), CardDescription (p) for accessibility

### 6. Accessibility ✅
- **Color contrast** - Uses theme tokens for sufficient contrast
- **Focus states** - Maintained through base button/interactive components
- **Keyboard navigation** - Tab order flows logically through card content
- **Semantic HTML** - Proper heading hierarchy and ARIA structure

## Card Component API

### Card (Base Component)

```tsx
interface CardProps extends React.ComponentProps<"div">, VariantProps<typeof cardVariants> {
  variant?: "default" | "elevated" | "outlined" | "ghost"
  size?: "sm" | "default" | "lg"
}

// Usage
<Card variant="elevated" size="lg">
  {/* Card content */}
</Card>
```

### Card Variants

- **default** - `shadow-sm hover:shadow-md` - Standard card with subtle shadow
- **elevated** - `shadow-md hover:shadow-lg` - Emphasized cards with stronger shadow  
- **outlined** - `border-2 shadow-none hover:shadow-sm` - Minimal cards with border focus
- **ghost** - `border-transparent shadow-none hover:bg-accent/50` - Subtle background cards

### Card Sizes

- **sm** - `p-3` (12px) - Compact cards
- **default** - `p-4` (16px) - Standard cards
- **lg** - `p-6` (24px) - Spacious cards

### Content Components

```tsx
// Semantic structure
<Card>
  <CardHeader>
    <CardTitle>Primary heading</CardTitle>
    <CardDescription>Supporting description</CardDescription>
  </CardHeader>
  <CardContent>
    {/* Main content area */}
  </CardContent>
  <CardFooter>
    <CardAction>
      <Button>Primary Action</Button>
    </CardAction>
  </CardFooter>
</Card>
```

## Spacing Utilities

### 4px Spacing Scale

```css
.card-spacing-xs  /* 4px padding */
.card-spacing-sm  /* 8px padding */
.card-spacing-md  /* 12px padding */
.card-spacing-lg  /* 16px padding */
.card-spacing-xl  /* 20px padding */
.card-spacing-2xl /* 24px padding */
```

### Content Hierarchy

```css
.card-primary-content   /* Primary text (titles, headings) */
.card-secondary-content /* Secondary text (descriptions, meta) */
```

## Responsive Grid System

### Card Grid Utilities

```tsx
// Auto-sizing grid (minimum 280px columns)
<div className="card-grid">
  <Card>...</Card>
  <Card>...</Card>
</div>

// Auto-fit grid (minimum 250px columns)  
<div className="card-grid-auto">
  <Card>...</Card>
  <Card>...</Card>
</div>
```

### Responsive Breakpoints

- **320px+** - Single column layout
- **768px+** - Two column layout
- **1024px+** - Three column layout  
- **1440px+** - Four column layout

## Usage Examples

### Basic Card

```tsx
<Card>
  <CardContent>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card description text</CardDescription>
  </CardContent>
</Card>
```

### Car Listing Card

```tsx
<Card variant="elevated" className="overflow-hidden group">
  <div className="relative h-48">
    <Image src={car.imageURL} alt={car.model} fill className="object-contain group-hover:scale-105" />
  </div>
  <CardContent className="text-center">
    <CardTitle className="text-xl mb-3">{car.model}</CardTitle>
    <CardDescription className="mb-4">
      From <span className="text-2xl font-bold">₱{car.pricePerDay.toLocaleString()}</span>
    </CardDescription>
    <CardAction>
      <Button onClick={handleBook}>BOOK NOW</Button>
    </CardAction>
  </CardContent>
</Card>
```

### Feature Card

```tsx
<Card variant="elevated" className="text-center group">
  <CardContent className="pt-8 pb-6">
    <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-4 group-hover:scale-110 transition-transform">
      <Icon className="h-8 w-8 text-blue-600" />
    </div>
    <CardTitle className="mb-2">Feature Title</CardTitle>
    <CardDescription className="leading-relaxed">
      Feature description explaining the benefit to users.
    </CardDescription>
  </CardContent>
</Card>
```

## Migration Guide

### From Old Card System

**Before:**
```tsx
<Card className="border-0 bg-white shadow-md hover:shadow-xl">
  <CardContent className="p-6">
    <h3 className="text-xl font-bold mb-3">{title}</h3>
    <p className="text-gray-600 mb-4">{description}</p>
    <Button>{action}</Button>
  </CardContent>
</Card>
```

**After:**
```tsx
<Card variant="elevated">
  <CardContent>
    <CardTitle className="text-xl mb-3">{title}</CardTitle>
    <CardDescription className="mb-4">{description}</CardDescription>
    <CardAction>
      <Button>{action}</Button>
    </CardAction>
  </CardContent>
</Card>
```

### Key Changes

1. **Remove hardcoded overrides** - No more `border-0 bg-white shadow-md`
2. **Use semantic components** - CardTitle instead of h3, CardDescription instead of p
3. **Apply variant system** - Use `variant` prop instead of className overrides
4. **Standardize spacing** - Use `.card-spacing-*` classes or component defaults
5. **Implement proper hierarchy** - CardAction for buttons, proper content structure

## Theme Integration

The Card system integrates with the design system theme:

```css
:root {
  --card: oklch(1 0 0);                    /* Card background (light) */
  --card-foreground: oklch(0.145 0 0);     /* Card text (light) */
  --border: oklch(0.922 0 0);              /* Border color (light) */
}

.dark {
  --card: oklch(0.145 0 0);                /* Card background (dark) */
  --card-foreground: oklch(0.985 0 0);     /* Card text (dark) */
  --border: oklch(0.269 0 0);              /* Border color (dark) */
}
```

## Performance Considerations

- **CSS-in-JS optimization** - Uses class-variance-authority for efficient class generation
- **Responsive images** - Proper `object-contain` and `fill` usage in card images
- **Hover animations** - GPU-accelerated transforms (`group-hover:scale-105`)
- **Minimal re-renders** - Stateless components with efficient prop passing

## Testing Recommendations

1. **Visual regression testing** - Test all card variants across breakpoints
2. **Accessibility testing** - Verify focus states, color contrast, screen reader support
3. **Performance testing** - Check rendering performance with large card grids
4. **Cross-browser testing** - Ensure CSS Grid/Flexbox compatibility

## Next Steps

1. **Component library documentation** - Add interactive Storybook stories
2. **Design tokens expansion** - Add more spacing/sizing tokens as needed
3. **Animation library integration** - Consider Framer Motion for advanced transitions
4. **A/B testing setup** - Test card variant performance and user engagement
