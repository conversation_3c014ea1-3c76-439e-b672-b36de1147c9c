"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  CarFront,
  CalendarRange,
  CreditCard,
  Settings,
  Car,
  LogOut,
  MapPin,
  Activity,
  TrendingUp,
  ChevronsLeft,
  ChevronsRight,
  User,
  MoreHorizontal,
  Users,
  Brain,
  Satellite,
} from "lucide-react";
import { useAdminAuth } from "@/components/auth/admin-auth-context";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { useSidebar } from "./sidebar-context";
import { AdminTooltip } from "../ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const items = [
  { label: "Overview", icon: LayoutDashboard, href: "/admin" },
  { label: "Cars", icon: CarFront, href: "/admin/cars" },
  { label: "Bookings", icon: CalendarRange, href: "/admin/bookings" },
  { label: "Sales Tracking", icon: TrendingUp, href: "/admin/sales-tracking" },
  { label: "Tracker", icon: MapPin, href: "/admin/tracker" },
  { label: "GPS Devices", icon: Satellite, href: "/admin/gps-devices" },
  { label: "Payments", icon: CreditCard, href: "/admin/payments" },
  { label: "Organization", icon: Settings, href: "/admin/settings" },
];

// Super admin only items
const superAdminItems = [
  { label: "AI Knowledge", icon: Brain, href: "/admin/ai-knowledge" },
  { label: "Accounts", icon: Users, href: "/admin/accounts" },
];

// Note: GPS Devices is available to all admins for fleet management

export function AdminSidebarNav() {
  const { signOut, user, isSuperAdmin, profile, loading } = useAdminAuth();
  const router = useRouter();
  const pathname = usePathname();
  const { isCollapsed, toggle } = useSidebar();

  // Don't render navigation until auth is fully loaded and user is confirmed
  if (loading || !user) {
    return (
      <div className="h-screen sticky top-0 pt-4 px-4 pb-2 flex flex-col">
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  // Get role display text for the Ollie Track badge
  const getRoleDisplayText = () => {
    if (loading) return "Loading...";
    if (!profile?.role) return "Admin"; // Fallback

    switch (profile.role) {
      case 'super_admin':
        return "Super Admin";
      case 'admin':
        return "Admin";
      default:
        return "Admin"; // Fallback for any unexpected role
    }
  };

  // Filter navigation items based on user role
  const navigationItems = React.useMemo(() => {
    const baseItems = [...items];
    
    // Add super admin only items
    if (isSuperAdmin) {
      // Insert super admin items before Settings
      baseItems.splice(-1, 0, ...superAdminItems);
    }
    
    return baseItems;
  }, [isSuperAdmin]);

  const isActive = (href: string) => {
    if (href === "/admin") {
      return pathname === "/admin";
    }
    return pathname.startsWith(href);
  };

  return (
    <div className="h-screen sticky top-0 pt-4 px-4 pb-2 flex flex-col">
      {/* Logo and Toggle Section */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between px-2 py-4 hover:bg-gray-50 rounded-lg transition-colors">
          <Link
            href="/admin"
            className={cn(
              "flex items-center gap-3",
              isCollapsed ? "justify-center" : "flex-1 min-w-0"
            )}
          >
            <div className="relative flex-shrink-0">
              <Image
                src="/ollie_logo.svg"
                alt="Ollie Track Admin Logo"
                width={32}
                height={32}
                className={cn(
                  "transition-all duration-200",
                  isCollapsed ? "w-8 h-8" : "w-8 h-8"
                )}
              />
            </div>
            {!isCollapsed && (
              <div className="flex flex-col min-w-0">
                <span className="font-bold text-gray-900 text-lg truncate">
                  Ollie's Rent A Car
                </span>
                <div className="flex items-center gap-2">
                  <span className={cn(
                    "text-xs text-white px-2 py-0.5 rounded-full font-medium transition-colors",
                    profile?.role === 'super_admin'
                      ? "bg-purple-600"
                      : "bg-blue-600"
                  )}>
                    {getRoleDisplayText()}
                  </span>
                </div>
              </div>
            )}
          </Link>

          {/* Expand/Collapse Toggle Button - Always visible */}
          {!isCollapsed && (
            <div className="flex-shrink-0">
              <AdminTooltip
                content="Collapse sidebar for more space"
                shortcut="Ctrl+B"
                side="right"
              >
                <button
                  onClick={toggle}
                  className={cn(
                    "flex items-center justify-center rounded-md p-1.5 transition-all duration-200",
                    "text-muted-foreground/70 hover:text-foreground hover:bg-muted/50",
                    "focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-blue-500"
                  )}
                  aria-label={
                    isCollapsed ? "Expand sidebar" : "Collapse sidebar"
                  }
                  aria-expanded={!isCollapsed}
                  type="button"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </button>
              </AdminTooltip>
            </div>
          )}
        </div>

        {/* Collapse button when sidebar is collapsed */}
        {isCollapsed && (
          <div className="flex justify-center">
            <AdminTooltip
              content="Expand sidebar to show labels"
              shortcut="Ctrl+B"
              side="right"
            >
              <button
                onClick={toggle}
                className={cn(
                  "flex items-center justify-center rounded-md p-1.5 transition-all duration-200",
                  "text-muted-foreground/70 hover:text-foreground hover:bg-muted/50",
                  "focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-blue-500"
                )}
                aria-label="Expand sidebar"
                aria-expanded={false}
                type="button"
              >
                <ChevronsRight className="h-4 w-4" />
              </button>
            </AdminTooltip>
          </div>
        )}
      </div>

      {/* Navigation Items */}
      <nav className="mt-6 space-y-1 flex-1">
        {navigationItems.map((it) => (
          <AdminTooltip
            key={it.href}
            content={
              <div className="space-y-1">
                <div className="font-medium">{it.label}</div>
                <div className="text-xs opacity-75">
                  Navigate to {it.label.toLowerCase()} management
                </div>
              </div>
            }
            shortcut={isCollapsed ? undefined : ""}
            side="right"
            delayDuration={isCollapsed ? 200 : 1000}
          >
            <Link
              href={it.href}
              className={cn(
                "flex items-center gap-3 rounded-md text-sm transition-colors",
                isCollapsed ? "justify-center px-2 py-3" : "px-3 py-2",
                isActive(it.href)
                  ? "admin-nav-active bg-blue-50 text-blue-700 font-medium border-r-2 border-blue-600"
                  : "text-muted-foreground hover:bg-muted hover:text-foreground"
              )}
            >
              <it.icon className={cn("h-4 w-4", isCollapsed && "h-5 w-5")} />
              {!isCollapsed && <span>{it.label}</span>}
            </Link>
          </AdminTooltip>
        ))}
      </nav>

      {/* Admin Profile Section */}
      <div className="mt-auto">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              type="button"
              className={cn(
                "w-full flex items-center gap-3 rounded-md p-2 mt-2 text-sm hover:bg-muted transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                isCollapsed ? "justify-center" : "justify-start"
              )}
            >
              <div
                className={cn(
                  "flex items-center justify-center rounded-full bg-blue-600 text-white font-medium",
                  isCollapsed ? "w-8 h-8 text-sm" : "w-8 h-8 text-sm"
                )}
              >
                {user?.email ? (
                  user.email.charAt(0).toUpperCase()
                ) : (
                  <User className="h-4 w-4" />
                )}
              </div>
              {!isCollapsed && (
                <div className="flex flex-col items-start min-w-0 flex-1">
                  <span className="text-sm font-medium text-foreground truncate">
                    {user?.email ? user.email.split('@')[0] : 'Admin'}
                  </span>
                  <span className="text-xs text-muted-foreground truncate">
                    {user?.email || '<EMAIL>'}
                  </span>
                </div>
              )}
              {!isCollapsed && (
                <MoreHorizontal className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              )}
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            side={isCollapsed ? "right" : "top"}
            align="start"
            className="w-56"
          >
            <DropdownMenuItem
              onClick={() => router.push("/admin/account")}
              className="cursor-pointer"
            >
              <User className="h-4 w-4" />
              <span>Account Settings</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                signOut();
                router.push("/admin-auth");
              }}
              className="cursor-pointer text-red-600 focus:text-red-600"
              variant="destructive"
            >
              <LogOut className="h-4 w-4" />
              <span>Log out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
