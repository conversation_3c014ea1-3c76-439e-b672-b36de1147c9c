import React from 'react';
import Image from 'next/image';
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { formatCurrency } from '@/lib/utils/format-currency';
import { formatBookingIdForDisplay } from '@/lib/reference-ids';
import {
  CarIcon,
  Clock,
  CalendarCheck,
  MapPin,
  CreditCard,
  FileText,
  UserIcon
} from 'lucide-react';

// Types
interface Car {
  id: string;
  model: string;
  type?: string;
  seats?: number;
  plate_number: string;
  image_url?: string;
}

interface User {
  id: string;
  name?: string;
  full_name?: string;
  email?: string;
}

interface Booking {
  id: string;
  status: string;
  pickup_datetime: string;
  dropoff_datetime: string;
  pickup_location: string;
  dropoff_location: string;
  total_amount: number;
  special_requests?: string;
}

interface Payment {
  id: string;
  status: string;
  method: string;
  amount: number;
}

interface ModernBookingCardProps {
  booking: Booking;
  car: Car;
  customer: User;
  payment?: Payment | null;
}

export function ModernBookingCard({ booking, car, customer, payment }: ModernBookingCardProps) {
  // Format dates
  const formatDate = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }) + ', ' + date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Helper function to get status badge color
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'bg-emerald-500 text-white';
      case 'pending':
        return 'bg-amber-500 text-white';
      case 'cancelled':
        return 'bg-red-500 text-white';
      case 'completed':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  // Get status badge classes
  const statusBadgeClasses = getStatusBadge(booking.status);

  return (
    <Card className="overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm hover:shadow-lg transition-all duration-300">
      <div className="flex flex-col sm:flex-row">
        {/* Left side - Car image */}
        <div className="relative w-full sm:w-32 h-32 sm:h-auto">
          <Image
            src={car?.image_url || '/placeholder.jpg'}
            alt={car?.model || 'Vehicle'}
            fill
            className="object-cover"
          />
          <div className="absolute top-2 left-2 z-10">
            <Badge className={`${statusBadgeClasses} border-0 px-2 py-0.5 text-xs font-medium uppercase`}>
              {booking.status}
            </Badge>
          </div>
        </div>
        
        {/* Right side - Details */}
        <div className="flex-1 p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* Car details */}
            <div>
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-bold text-gray-900 text-lg">{car?.model}</h3>
                <span className="text-xs text-gray-500 font-medium">{formatBookingIdForDisplay(booking)}</span>
              </div>
              
              <div className="flex items-center gap-2 mb-3">
                <span className="text-xs text-gray-500">{car?.type || 'Sedan'} • {car?.seats || 5} seats</span>
              </div>
              
              <div className="flex items-center gap-2 mb-1">
                <p className="text-sm text-gray-500 flex items-center gap-1">
                  <CarIcon className="h-3.5 w-3.5" />
                  <span>{car?.plate_number}</span>
                </p>
              </div>

              <div className="flex items-center gap-1.5 mb-3">
                <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                  <UserIcon className="h-2.5 w-2.5 text-blue-600" />
                </div>
                <p className="font-medium text-gray-700 text-xs">{customer?.full_name || customer?.name}</p>
              </div>
            </div>
            
            {/* Price */}
            <div className="text-right">
              <p className="text-2xl font-bold text-emerald-600">
                {formatCurrency(booking.total_amount)}
              </p>
              {payment && (
                <div className="flex items-center justify-end gap-1 mt-1">
                  <CreditCard className="h-3 w-3 text-gray-500" />
                  <span className="text-xs text-gray-500">{payment.method}</span>
                </div>
              )}
            </div>
          </div>
          
          {/* Pickup and Return Info */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4 pt-4 border-t border-gray-200">
            <div>
              <div className="flex items-center gap-2 mb-1">
                <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                <p className="text-base font-medium text-gray-900">Pickup</p>
              </div>
              <div className="flex items-center gap-1 text-sm text-gray-500 ml-4">
                <MapPin className="h-3.5 w-3.5 flex-shrink-0" />
                <span>{booking.pickup_location}</span>
              </div>
              <div className="flex items-center gap-1 text-sm text-gray-500 ml-4 mt-1">
                <Clock className="h-3.5 w-3.5 flex-shrink-0" />
                <span>{formatDate(booking.pickup_datetime)}</span>
              </div>
            </div>
            
            <div>
              <div className="flex items-center gap-2 mb-1">
                <div className="w-2 h-2 rounded-full bg-red-500"></div>
                <p className="text-base font-medium text-gray-900">Return</p>
              </div>
              <div className="flex items-center gap-1 text-sm text-gray-500 ml-4">
                <MapPin className="h-3.5 w-3.5 flex-shrink-0" />
                <span>{booking.dropoff_location}</span>
              </div>
              <div className="flex items-center gap-1 text-sm text-gray-500 ml-4 mt-1">
                <CalendarCheck className="h-3.5 w-3.5 flex-shrink-0" />
                <span>{formatDate(booking.dropoff_datetime)}</span>
              </div>
            </div>
          </div>

          {/* Special Requests */}
          {booking.special_requests && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <p className="text-xs text-gray-500 flex items-start gap-2">
                <FileText className="h-3 w-3 mt-0.5 flex-shrink-0" />
                <span><span className="font-medium">Special Request:</span> "{booking.special_requests}"</span>
              </p>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}
