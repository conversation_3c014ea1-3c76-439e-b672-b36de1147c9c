"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { formatCurrency } from "@/lib/utils/format-currency"
import { Activity, CalendarCheck, CarIcon, Clock, CreditCard, FileText, Mail, MapPin, Settings, TrendingUp, UserIcon } from "lucide-react"
import Image from "next/image"
import type { Booking, Car, Payment, User } from "@/lib/types"
import { useEffect, useState, useCallback } from "react"
import { db } from "@/lib/supabase/database"
import { MobileBookingCard } from "@/components/admin/mobile-booking-card"
import { ActiveRentalCard } from "@/components/admin/active-rental-card"
import { ModernBookingCard } from '@/components/admin/modern-booking-card'
import { AdminDashboardSkeleton } from '@/components/admin/loading/skeleton-components'

export default function AdminOverview() {
  const [activeRentals, setActiveRentals] = useState<any[]>([])
  const [allBookings, setAllBookings] = useState<any[]>([])
  const [fleetStats, setFleetStats] = useState({ available: 0, rented: 0, maintenance: 0 })
  const [loading, setLoading] = useState(true)

  const fetchData = useCallback(async () => {
    try {
      // Debug car status synchronization first
      await db.debugCarStatusSync();
      
      const [rentalsData, bookingsData, statsData] = await Promise.all([
        db.getActiveRentals(),
        db.getAllBookings(),
        db.getFleetStats(),
      ]);
      
      setActiveRentals(rentalsData);
      setAllBookings(bookingsData);
      setFleetStats(statsData);
    } catch (error) {
      console.error('Error fetching admin data:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    setLoading(true);
    fetchData();

    const bookingsSubscription = db.subscribeToBookings((payload) => {
        if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
          console.log('Bookings change detected, refetching admin data...', payload);
        }
        fetchData();
    });

    const carsSubscription = db.subscribeToCars((payload) => {
        if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
          console.log('Cars change detected, refetching admin data...', payload);
        }
        fetchData();
    });

    return () => {
        bookingsSubscription.unsubscribe();
        carsSubscription.unsubscribe();
    };
  }, [fetchData]);

  if (loading) {
    return <AdminDashboardSkeleton />
  }

  return (
    <div className="space-y-8 p-2 sm:p-4 lg:p-6 max-w-full overflow-hidden">
      {/* Page Header */}
      <header className="space-y-2">
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-foreground">Fleet Overview</h1>
        <p className="text-sm sm:text-base text-muted-foreground">Monitor your vehicle fleet status and active bookings</p>
      </header>

      {/* Modern Statistics Cards */}
      <section aria-labelledby="stats-heading" className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <h2 id="stats-heading" className="sr-only">Fleet Statistics</h2>
        <ModernStatCard
          title="AVAILABLE CARS"
          value={fleetStats.available}
          color="green"
          icon={<CarIcon className="h-5 w-5" />}
          trend="+2.5%"
        />
        <ModernStatCard
          title="RENTED CARS"
          value={fleetStats.rented}
          color="blue"
          icon={<Activity className="h-5 w-5" />}
          trend="+12.3%"
        />
        <ModernStatCard
          title="UNDER MAINTENANCE"
          value={fleetStats.maintenance}
          color="orange"
          icon={<Settings className="h-5 w-5" />}
          trend="-5.1%"
        />
      </section>

      {/* Active Rentals Section */}
      <section aria-labelledby="active-rentals-heading" className="space-y-6">
        <div className="flex items-center gap-4">
          <h2 id="active-rentals-heading" className="text-2xl font-bold text-foreground">Active Rentals</h2>
          <Badge 
            className="bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300 hover:bg-emerald-200 dark:hover:bg-emerald-800 px-3 py-1 text-sm font-medium rounded-full"
            aria-label={`${activeRentals.length} active rentals`}
          >
            {activeRentals.length} Active
          </Badge>
        </div>

        <div className="space-y-4 max-h-[500px] overflow-y-auto pr-2" role="region" aria-label="Active rentals list">
          {activeRentals.length === 0 ? (
            <Card className="border-dashed border-2 border-muted-foreground/25 bg-muted/20">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
                  <CarIcon className="h-8 w-8 text-muted-foreground" aria-hidden="true" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No Active Rentals</h3>
                <p className="text-muted-foreground text-center max-w-md">
                  All vehicles are currently available or in maintenance. New bookings will appear here.
                </p>
              </CardContent>
            </Card>
          ) : (
            activeRentals.map((rental) => (
              <div key={rental.id}>
                {/* Mobile-only compact card with modal (320-425px) */}
                <div className="sm:hidden block">
                  <MobileBookingCard booking={rental} car={rental.car} customer={rental.customer} />
                </div>
                {/* Regular card for tablet and desktop (≥640px) */}
                <div className="hidden sm:block">
                  <ActiveRentalCard booking={rental} car={rental.car} customer={rental.customer} />
                </div>
              </div>
            ))
          )}
        </div>
      </section>

      {/* All Booking Status Section */}
      <section aria-labelledby="all-bookings-heading" className="space-y-6">
        <div className="flex items-center gap-4">
          <h2 id="all-bookings-heading" className="text-2xl font-bold text-foreground">All Booking Status</h2>
          <Badge 
            className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800 px-3 py-1 text-sm font-medium rounded-full"
            aria-label={`${allBookings.length} total bookings`}
          >
            {allBookings.length} Total
          </Badge>
        </div>

        <div className="space-y-4 max-h-[70vh] overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent pr-2" role="region" aria-label="All bookings list">
          {allBookings.length === 0 ? (
            <Card className="border-dashed border-2 border-muted-foreground/25 bg-muted/20">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
                  <FileText className="h-8 w-8 text-muted-foreground" aria-hidden="true" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No Bookings Found</h3>
                <p className="text-muted-foreground text-center max-w-md">
                  No bookings have been made yet. Start accepting reservations to see them here.
                </p>
              </CardContent>
            </Card>
          ) : (
            allBookings.map((booking) => (
              <div key={booking.id}>
                {/* Mobile-only compact card with modal (320-425px) */}
                <div className="sm:hidden block">
                  <MobileBookingCard 
                    booking={booking} 
                    car={booking.car} 
                    customer={booking.customer}
                    payment={null}
                  />
                </div>
                {/* Regular card for tablet and desktop (≥640px) */}
                <div className="hidden sm:block">
                  <ModernBookingCard 
                    booking={booking} 
                    car={booking.car} 
                    customer={booking.customer}
                    payment={booking.payment}
                  />
                </div>
              </div>
            ))
          )}
        </div>
      </section>
    </div>
  )
}

// Modern Statistics Card Component
interface ModernStatCardProps {
  title: string
  value: number
  color: "green" | "blue" | "orange"
  icon: React.ReactNode
  trend: string
}

function ModernStatCard({ title, value, color, icon, trend }: ModernStatCardProps) {
  const colorConfig = {
    green: {
      bg: "bg-gradient-to-br from-emerald-500 to-emerald-600",
      border: "border-emerald-300",
      iconBg: "bg-white/20 backdrop-blur-sm",
      iconText: "text-white",
      valueText: "text-white",
      trendText: "text-emerald-100"
    },
    blue: {
      bg: "bg-gradient-to-br from-blue-500 to-blue-600",
      border: "border-blue-300",
      iconBg: "bg-white/20 backdrop-blur-sm",
      iconText: "text-white",
      valueText: "text-white",
      trendText: "text-blue-100"
    },
    orange: {
      bg: "bg-gradient-to-br from-orange-500 to-orange-600",
      border: "border-orange-300",
      iconBg: "bg-white/20 backdrop-blur-sm",
      iconText: "text-white",
      valueText: "text-white",
      trendText: "text-orange-100"
    }
  }

  const config = colorConfig[color]

  return (
    <Card 
      className={`${config.bg} ${config.border} border hover:shadow-lg transition-all duration-200 group cursor-pointer focus-within:ring-2 focus-within:ring-white/30`}
      tabIndex={0}
    >
      <CardContent className="p-3 xs:p-4 sm:p-5 md:p-6">
        <div className="flex items-start justify-between">
          <div className="space-y-1 xs:space-y-2">
            <p className="text-2xs xs:text-xs font-semibold tracking-wider text-white/80 uppercase">{title}</p>
            <div className="flex flex-wrap items-baseline gap-1 xs:gap-2">
              <p 
                className={`text-xl xs:text-2xl sm:text-3xl lg:text-5xl font-bold ${config.valueText}`} 
                aria-label={`${title}: ${value}`}
              >
                {value}
              </p>
              <div className="flex items-center gap-1">
                <TrendingUp className={`h-2 w-2 xs:h-3 xs:w-3 ${config.trendText}`} aria-hidden="true" />
                <span 
                  className={`text-3xs xs:text-2xs sm:text-xs font-medium ${config.trendText}`} 
                  aria-label={`Trend: ${trend}`}
                >
                  {trend}
                </span>
              </div>
            </div>
          </div>
          <div 
            className={`p-2 xs:p-2.5 sm:p-3 rounded-lg xs:rounded-xl ${config.iconBg} ${config.iconText} group-hover:scale-110 transition-transform duration-200 shadow-lg`}
            aria-hidden="true"
          >
            <div className="h-3 w-3 xs:h-4 xs:w-4 sm:h-5 sm:w-5 flex items-center justify-center">
              {icon}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Modern Active Rental Card Component
interface ModernActiveRentalCardProps {
  booking: Booking;
  car: Car;
  customer: User;
}

function ModernActiveRentalCard({ booking, car, customer }: ModernActiveRentalCardProps) {
  // Handle rental actions
  const handleViewDetails = () => {
    // Navigate to rental details page
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log(`View rental details for ID: ${booking.id}`);
    }
  };
  
  const handleExtend = () => {
    // Open extend rental modal
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log(`Extend rental ID: ${booking.id}`);
    }
  };
  
  const handleContact = () => {
    // Contact customer
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log(`Contact customer for rental ID: ${booking.id}`);
    }
  };
  // Format dates
  const formatDate = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleDateString("en-US", { 
      month: "short", 
      day: "numeric", 
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  };

  // Get customer name
  const customerName = customer?.full_name || customer?.name || "Unknown Customer";

  return (
    <Card 
      className="group hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-900 border border-gray-100 dark:border-gray-800 rounded-xl overflow-hidden focus-within:ring-2 focus-within:ring-emerald-500/30"
      tabIndex={0}
    >
      <div className="absolute top-0 left-0 w-full h-1 bg-emerald-500"></div>
      <CardContent className="p-0">
        {/* Mobile Layout (xs to lg) */}
        <div className="xl:hidden p-4 xs:p-5 sm:p-6 space-y-4">
          {/* Header with Status */}
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              <h4 className="font-bold text-foreground text-xs xs:text-sm">#{booking.id}</h4>
              <Badge className="bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300 text-2xs xs:text-xs px-1.5 xs:px-2 py-0.5 xs:py-1">
                {booking.status}
              </Badge>
            </div>
            <div className="text-right">
              <p className="font-bold text-foreground text-sm xs:text-base text-emerald-600 dark:text-emerald-400">
                {formatCurrency(booking.total_amount)}
              </p>
            </div>
          </div>

          {/* Car and Customer Info */}
          <div className="flex gap-3">
            {/* Car Image */}
            <div className="flex-shrink-0">
              <div className="relative w-16 xs:w-20 aspect-[4/3] rounded-lg overflow-hidden bg-muted">
                <Image
                  src={car?.image_url || '/placeholder.jpg'}
                  alt={`${car?.model} - ${car?.plate_number}`}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
            </div>

            {/* Car and Customer Details */}
            <div className="flex-1 min-w-0 space-y-1 xs:space-y-2">
              <h3 className="font-semibold text-foreground text-xs xs:text-sm line-clamp-1">{car?.model}</h3>
              <p className="text-2xs xs:text-xs text-muted-foreground flex items-center gap-1">
                <CarIcon className="h-2.5 w-2.5 xs:h-3 xs:w-3 flex-shrink-0" />
                <span className="truncate">{car?.plate_number}</span>
              </p>
              <div className="flex items-center gap-1.5">
                <div className="w-4 h-4 xs:w-5 xs:h-5 rounded-full bg-emerald-100 dark:bg-emerald-900 flex items-center justify-center flex-shrink-0">
                  <UserIcon className="h-2 w-2 xs:h-2.5 xs:w-2.5 text-emerald-600 dark:text-emerald-400" />
                </div>
                <p className="font-medium text-foreground text-2xs xs:text-xs truncate">{customerName}</p>
                {customer?.email && (
                  <p className="text-2xs xs:text-xs text-muted-foreground truncate">{customer.email}</p>
                )}
              </div>
            </div>
          </div>
            
          {/* Location & Schedule */}
          <div className="grid grid-cols-1 xs:grid-cols-2 gap-2 xs:gap-3">
            <div className="space-y-1">
              <p className="text-2xs xs:text-xs text-muted-foreground flex items-center gap-1">
                <MapPin className="h-2.5 w-2.5 xs:h-3 xs:w-3 text-emerald-500 flex-shrink-0" />
                <span className="font-medium">Pickup:</span> 
                <span className="truncate">{booking.pickup_location}</span>
              </p>
              <p className="text-2xs xs:text-xs text-muted-foreground flex items-center gap-1">
                <MapPin className="h-2.5 w-2.5 xs:h-3 xs:w-3 text-red-500 flex-shrink-0" />
                <span className="font-medium">Return:</span> 
                <span className="truncate">{booking.dropoff_location}</span>
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-2xs xs:text-xs text-muted-foreground flex items-center gap-1">
                <Clock className="h-2.5 w-2.5 xs:h-3 xs:w-3 text-emerald-500 flex-shrink-0" />
                <span className="font-medium">Start:</span> 
                <span className="truncate">{formatDate(booking.pickup_datetime)}</span>
              </p>
              <p className="text-2xs xs:text-xs text-muted-foreground flex items-center gap-1">
                <CalendarCheck className="h-2.5 w-2.5 xs:h-3 xs:w-3 text-red-500 flex-shrink-0" />
                <span className="font-medium">End:</span> 
                <span className="truncate">{formatDate(booking.dropoff_datetime)}</span>
              </p>
            </div>
          </div>
            
          {/* Special Requests */}
          {booking.special_requests && (
            <div className="pt-2 border-t border-muted">
              <p className="text-2xs xs:text-xs text-muted-foreground flex items-start gap-2">
                <FileText className="h-2.5 w-2.5 xs:h-3 xs:w-3 mt-0.5 flex-shrink-0" />
                <span><span className="font-medium">Special Request:</span> "{booking.special_requests}"</span>
              </p>
            </div>
          )}
          
          {/* Mobile Actions */}
          <div className="flex items-center justify-end gap-2 mt-3 pt-2 border-t border-muted">
            <Button 
              variant="secondary" 
              size="sm" 
              className="text-2xs xs:text-xs h-7 xs:h-8 px-2 xs:px-3 rounded-md touch-manipulation" 
              onClick={handleViewDetails}
              aria-label="View rental details"
            >
              Details
            </Button>
            <Button 
              variant="secondary" 
              size="sm" 
              className="text-2xs xs:text-xs h-7 xs:h-8 px-2 xs:px-3 rounded-md touch-manipulation" 
              onClick={handleExtend}
              aria-label="Extend rental"
            >
              Extend
            </Button>
            <Button 
              variant="secondary" 
              size="sm" 
              className="text-2xs xs:text-xs h-7 xs:h-8 px-2 xs:px-3 rounded-md touch-manipulation" 
              onClick={handleContact}
              aria-label="Contact customer"
            >
              Contact
            </Button>
          </div>
        </div>
        
        {/* Desktop Layout (xl and above) */}
        <div className="hidden xl:block">
          <div className="grid grid-cols-12 gap-4 items-start">
            {/* Car Image */}
            <div className="col-span-2">
              <div className="relative aspect-[4/3] rounded-lg overflow-hidden bg-muted max-w-[200px]">
                <Image
                  src={car?.image_url || '/placeholder.jpg'}
                  alt={`${car?.model} - ${car?.plate_number}`}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-2 left-2">
                  <Badge className="bg-emerald-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
                    ACTIVE
                  </Badge>
                </div>
              </div>
            </div>

            {/* Vehicle & Booking Info */}
            <div className="col-span-3 space-y-2">
              <div className="space-y-1">
                <div className="flex items-center gap-2 flex-wrap">
                  <h4 className="font-bold text-foreground text-sm">#{booking.id}</h4>
                  <Badge className="bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300 text-xs px-2 py-1">
                    {booking.status}
                  </Badge>
                </div>
                <h3 className="font-semibold text-base text-foreground line-clamp-1">{car?.model}</h3>
                <p className="text-sm text-muted-foreground flex items-center gap-1">
                  <CarIcon className="h-3 w-3 flex-shrink-0" />
                  <span className="truncate">{car?.plate_number}</span>
                </p>
              </div>
              <div className="flex gap-1 flex-wrap">
                <Badge variant="secondary" className="text-2xs xs:text-xs px-1.5 xs:px-2 py-0.5 xs:py-1">
                  {car?.type}
                </Badge>
                <Badge variant="outline" className="text-2xs xs:text-xs px-1.5 xs:px-2 py-0.5 xs:py-1">
                  {car?.seats} seats
                </Badge>
              </div>
            </div>

            {/* Customer Info */}
            <div className="col-span-2 space-y-2">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 rounded-full bg-emerald-100 dark:bg-emerald-900 flex items-center justify-center flex-shrink-0">
                    <UserIcon className="h-3 w-3 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <p className="font-medium text-foreground text-sm truncate">{customerName}</p>
                </div>
                {customer?.email && (
                  <p className="text-xs text-muted-foreground flex items-center gap-1 ml-8">
                    <Mail className="h-3 w-3 flex-shrink-0" />
                    <span className="truncate">{customer.email}</span>
                  </p>
                )}
              </div>
            </div>

            {/* Location & Schedule */}
            <div className="col-span-3 space-y-2">
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <MapPin className="h-3 w-3 text-emerald-500 flex-shrink-0" />
                  <span className="font-medium">Pickup:</span> 
                  <span className="truncate">{booking.pickup_location}</span>
                </p>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <MapPin className="h-3 w-3 text-red-500 flex-shrink-0" />
                  <span className="font-medium">Return:</span> 
                  <span className="truncate">{booking.dropoff_location}</span>
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <Clock className="h-3 w-3 text-emerald-500 flex-shrink-0" />
                  <span className="font-medium">Start:</span> 
                  <span className="truncate">{formatDate(booking.pickup_datetime)}</span>
                </p>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <CalendarCheck className="h-3 w-3 text-red-500 flex-shrink-0" />
                  <span className="font-medium">End:</span> 
                  <span className="truncate">{formatDate(booking.dropoff_datetime)}</span>
                </p>
              </div>
            </div>

            {/* Revenue */}
            <div className="col-span-2 text-right space-y-1">
              <div>
                <p className="text-3xl font-bold text-emerald-600 dark:text-emerald-400">
                  {formatCurrency(booking.total_amount)}
                </p>
                <p className="text-xs text-muted-foreground font-medium">
                  Total Revenue
                </p>
              </div>
            </div>
          </div>

          {/* Special Requests */}
          {booking.special_requests && (
            <div className="mt-3 pt-3 border-t border-muted">
              <p className="text-xs text-muted-foreground flex items-start gap-2">
                <FileText className="h-3 w-3 mt-0.5 flex-shrink-0" />
                <span><span className="font-medium">Special Request:</span> "{booking.special_requests}"</span>
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Modern Booking List Item Component
interface ModernBookingListItemProps {
  booking: Booking
  car: Car
  customer: User
  payment: Payment | null
}

function ModernBookingListItem({ booking, car, customer, payment }: ModernBookingListItemProps) {
  // Format dates
  const formatDate = (isoString: string) => {
    const date = new Date(isoString)
    return date.toLocaleDateString("en-US", { 
      month: "short", 
      day: "numeric", 
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    })
  }

  // Helper function to get status badge color
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300';
      case 'pending':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  // Helper function to get payment status badge color
  const getPaymentStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800';
      case 'pending':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300 border-amber-200 dark:border-amber-800';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 border-red-200 dark:border-red-800';
      case 'refunded':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300 border-purple-200 dark:border-purple-800';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-700';
    }
  };
  
  // Handle booking actions
  const handleViewDetails = () => {
    // Navigate to booking details page
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log(`View booking details for ID: ${booking.id}`);
    }
  };
  
  const handleEdit = () => {
    // Open edit booking modal/page
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log(`Edit booking ID: ${booking.id}`);
    }
  };
  
  const handleCancel = () => {
    // Open cancel booking confirmation
    if (process.env.NODE_ENV === 'development' || (typeof window !== 'undefined' && window.location.hostname === 'localhost')) {
      console.log(`Cancel booking ID: ${booking.id}`);
    }
  };
  // Get status badge classes
  const statusBadgeClasses = getStatusBadge(booking.status);
  const paymentStatusBadge = payment ? getPaymentStatusBadge(payment.status) : null;

  return (
    <Card 
      className={`group hover:shadow-md transition-all duration-200 border-l-4 border-${booking.status.toLowerCase() === 'confirmed' ? 'emerald' : booking.status.toLowerCase() === 'pending' ? 'amber' : booking.status.toLowerCase() === 'cancelled' ? 'red' : 'blue'}-500 bg-card focus-within:ring-2 focus-within:ring-primary/20`}
      tabIndex={0}
    >
      <CardContent className="p-3 xs:p-4 sm:p-5">
        {/* Mobile Layout (xs to md) */}
        <div className="md:hidden space-y-4">
          {/* Header with ID and Status */}
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              <h4 className="font-bold text-foreground text-xs xs:text-sm">#{booking.id}</h4>
              <Badge className={`${statusBadgeClasses} text-2xs xs:text-xs px-1.5 xs:px-2 py-0.5 xs:py-1`}>
                {booking.status}
              </Badge>
            </div>
            <div className="text-right">
              <p className="font-bold text-foreground text-sm xs:text-base">
                {formatCurrency(booking.total_amount)}
              </p>
            </div>
          </div>

          {/* Car and Customer Info */}
          <div className="flex gap-3">
            {/* Car Image */}
            <div className="flex-shrink-0">
              <div className="relative w-16 xs:w-20 aspect-[4/3] rounded-lg overflow-hidden bg-muted">
                <Image
                  src={car?.image_url || '/placeholder.jpg'}
                  alt={`${car?.model} - ${car?.plate_number}`}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
            </div>

            {/* Car and Customer Details */}
            <div className="flex-1 min-w-0 space-y-1 xs:space-y-2">
              <h3 className="font-semibold text-foreground text-xs xs:text-sm line-clamp-1">{car?.model}</h3>
              <p className="text-2xs xs:text-xs text-muted-foreground flex items-center gap-1">
                <CarIcon className="h-2.5 w-2.5 xs:h-3 xs:w-3 flex-shrink-0" />
                <span className="truncate">{car?.plate_number}</span>
              </p>
              <div className="flex items-center gap-1.5">
                <div className="w-4 h-4 xs:w-5 xs:h-5 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center flex-shrink-0">
                  <UserIcon className="h-2 w-2 xs:h-2.5 xs:w-2.5 text-blue-600 dark:text-blue-400" />
                </div>
                <p className="font-medium text-foreground text-2xs xs:text-xs truncate">{customer?.full_name || customer?.name}</p>
              </div>
            </div>
          </div>

          {/* Dates and Locations */}
          <div className="grid grid-cols-2 gap-2 xs:gap-3">
            <div className="space-y-1 xs:space-y-2">
              <p className="text-2xs xs:text-xs text-muted-foreground flex items-center gap-1">
                <Clock className="h-2.5 w-2.5 xs:h-3 xs:w-3 text-emerald-500 flex-shrink-0" />
                <span className="truncate">{formatDate(booking.pickup_datetime)}</span>
              </p>
              <p className="text-2xs xs:text-xs text-muted-foreground flex items-center gap-1">
                <CalendarCheck className="h-2.5 w-2.5 xs:h-3 xs:w-3 text-red-500 flex-shrink-0" />
                <span className="truncate">{formatDate(booking.dropoff_datetime)}</span>
              </p>
            </div>
            <div className="space-y-1 xs:space-y-2">
              <p className="text-2xs xs:text-xs text-muted-foreground flex items-center gap-1">
                <MapPin className="h-2.5 w-2.5 xs:h-3 xs:w-3 text-emerald-500 flex-shrink-0" />
                <span className="truncate">{booking.pickup_location}</span>
              </p>
              <p className="text-2xs xs:text-xs text-muted-foreground flex items-center gap-1">
                <MapPin className="h-2.5 w-2.5 xs:h-3 xs:w-3 text-red-500 flex-shrink-0" />
                <span className="truncate">{booking.dropoff_location}</span>
              </p>
            </div>
          </div>

          {/* Payment Status */}
          {(paymentStatusBadge || payment) && (
            <div className="flex items-center justify-between gap-2">
              {paymentStatusBadge && (
                <Badge className={`${paymentStatusBadge} text-2xs xs:text-xs px-1.5 xs:px-2 py-0.5 xs:py-1`}>
                  {payment?.status}
                </Badge>
              )}
              {payment && (
                <div className="flex items-center gap-1">
                  <CreditCard className="h-2.5 w-2.5 xs:h-3 xs:w-3 text-muted-foreground" />
                  <span className="text-2xs xs:text-xs text-muted-foreground">{payment.method}</span>
                </div>
              )}
            </div>
          )}

          {/* Special Requests */}
          {booking.special_requests && (
            <div className="pt-2 border-t border-muted">
              <p className="text-2xs xs:text-xs text-muted-foreground flex items-start gap-1.5">
                <FileText className="h-2.5 w-2.5 xs:h-3 xs:w-3 mt-0.5 flex-shrink-0" />
                <span><span className="font-medium">Special Request:</span> "{booking.special_requests}"</span>
              </p>
            </div>
          )}
        </div>

        {/* Tablet Layout (md to xl) */}
        <div className="hidden md:block xl:hidden">
          <div className="grid grid-cols-12 gap-3 items-start">
            {/* Car Image */}
            <div className="col-span-2">
              <div className="relative aspect-[4/3] rounded-lg overflow-hidden bg-muted max-w-[120px]">
                <Image
                  src={car?.image_url || '/placeholder.jpg'}
                  alt={`${car?.model} - ${car?.plate_number}`}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
            </div>

            {/* Booking Info */}
            <div className="col-span-3 space-y-1.5">
              <div className="flex items-center gap-2 flex-wrap">
                <h4 className="font-bold text-foreground text-sm">#{booking.id}</h4>
                <Badge className={`${statusBadgeClasses} text-xs px-2 py-0.5`}>
                  {booking.status}
                </Badge>
              </div>
              <h3 className="font-semibold text-foreground text-sm line-clamp-1">{car?.model}</h3>
              <p className="text-xs text-muted-foreground flex items-center gap-1">
                <CarIcon className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">{car?.plate_number}</span>
              </p>
              <div className="flex items-center gap-1.5">
                <div className="w-5 h-5 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center flex-shrink-0">
                  <UserIcon className="h-2.5 w-2.5 text-blue-600 dark:text-blue-400" />
                </div>
                <p className="font-medium text-foreground text-xs truncate">{customer?.full_name || customer?.name}</p>
              </div>
            </div>

            {/* Locations & Dates */}
            <div className="col-span-4 space-y-1.5">
              <div className="grid grid-cols-2 gap-2">
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <MapPin className="h-3 w-3 text-emerald-500 flex-shrink-0" />
                  <span className="truncate">{booking.pickup_location}</span>
                </p>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <Clock className="h-3 w-3 text-emerald-500 flex-shrink-0" />
                  <span className="truncate">{formatDate(booking.pickup_datetime)}</span>
                </p>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <MapPin className="h-3 w-3 text-red-500 flex-shrink-0" />
                  <span className="truncate">{booking.dropoff_location}</span>
                </p>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <CalendarCheck className="h-3 w-3 text-red-500 flex-shrink-0" />
                  <span className="truncate">{formatDate(booking.dropoff_datetime)}</span>
                </p>
              </div>
            </div>

            {/* Payment & Amount */}
            <div className="col-span-3 text-right space-y-1.5">
              <p className="font-bold text-foreground text-sm lg:text-3xl">
                {formatCurrency(booking.total_amount)}
              </p>
              {paymentStatusBadge && (
                <Badge className={`${paymentStatusBadge} text-xs px-2 py-0.5`}>
                  {payment?.status}
                </Badge>
              )}
              {payment && (
                <div className="flex items-center justify-end gap-1">
                  <CreditCard className="h-3 w-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">{payment.method}</span>
                </div>
              )}
            </div>
          </div>

          {/* Special Requests */}
          {booking.special_requests && (
            <div className="mt-3 pt-3 border-t border-muted">
              <p className="text-xs text-muted-foreground flex items-start gap-2">
                <FileText className="h-3 w-3 mt-0.5 flex-shrink-0" />
                <span><span className="font-medium">Special Request:</span> "{booking.special_requests}"</span>
              </p>
            </div>
          )}
        </div>

        {/* Desktop Layout (xl and above) */}
        <div className="hidden xl:block">
          <div className="grid grid-cols-12 gap-4 items-start">
            {/* Car Image */}
            <div className="col-span-2">
              <div className="relative aspect-[4/3] rounded-lg overflow-hidden bg-muted max-w-[150px]">
                <Image
                  src={car?.image_url || '/placeholder.jpg'}
                  alt={`${car?.model} - ${car?.plate_number}`}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
            </div>

            {/* Booking Info */}
            <div className="col-span-2 space-y-2">
              <div className="space-y-1">
                <div className="flex items-center gap-2 flex-wrap">
                  <h4 className="font-bold text-foreground text-sm">#{booking.id}</h4>
                  <Badge className={`${statusBadgeClasses} text-xs px-2 py-1`}>
                    {booking.status}
                  </Badge>
                </div>
                <h3 className="font-semibold text-foreground text-sm line-clamp-1">{car?.model}</h3>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <CarIcon className="h-3 w-3 flex-shrink-0" />
                  <span className="truncate">{car?.plate_number}</span>
                </p>
              </div>
            </div>

            {/* Customer Info */}
            <div className="col-span-2 space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center flex-shrink-0">
                  <UserIcon className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                </div>
                <p className="font-medium text-foreground text-sm truncate">{customer?.full_name || customer?.name}</p>
              </div>
              <p className="text-xs text-muted-foreground flex items-center gap-1 ml-8">
                <Mail className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">{customer?.email}</span>
              </p>
            </div>

            {/* Locations */}
            <div className="col-span-2 space-y-2">
              <p className="text-xs text-muted-foreground flex items-center gap-1">
                <MapPin className="h-3 w-3 text-emerald-500 flex-shrink-0" />
                <span className="truncate">{booking.pickup_location}</span>
              </p>
              <p className="text-xs text-muted-foreground flex items-center gap-1">
                <MapPin className="h-3 w-3 text-red-500 flex-shrink-0" />
                <span className="truncate">{booking.dropoff_location}</span>
              </p>
            </div>

            {/* Schedule */}
            <div className="col-span-2 space-y-2">
              <p className="text-xs text-muted-foreground flex items-center gap-1">
                <Clock className="h-3 w-3 text-emerald-500 flex-shrink-0" />
                <span className="truncate">{formatDate(booking.pickup_datetime)}</span>
              </p>
              <p className="text-xs text-muted-foreground flex items-center gap-1">
                <CalendarCheck className="h-3 w-3 text-red-500 flex-shrink-0" />
                <span className="truncate">{formatDate(booking.dropoff_datetime)}</span>
              </p>
            </div>

            {/* Payment & Amount */}
            <div className="col-span-2 text-right space-y-1">
              <p className="font-bold text-foreground text-sm lg:text-3xl">
                {formatCurrency(booking.total_amount)}
              </p>
              {paymentStatusBadge && (
                <Badge className={`${paymentStatusBadge} text-xs px-2 py-1`}>
                  {payment?.status}
                </Badge>
              )}
              {payment && (
                <div className="flex items-center justify-end gap-1">
                  <CreditCard className="h-3 w-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">{payment.method}</span>
                </div>
              )}
            </div>
          </div>

          {/* Special Requests */}
          {booking.special_requests && (
            <div className="mt-3 pt-3 border-t border-muted">
              <p className="text-xs text-muted-foreground flex items-start gap-2">
                <FileText className="h-3 w-3 mt-0.5 flex-shrink-0" />
                <span><span className="font-medium">Special Request:</span> "{booking.special_requests}"</span>
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
