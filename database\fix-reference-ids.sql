-- Fix missing reference IDs by manually updating existing records
-- Run this AFTER running check-reference-ids.sql to see what's missing

-- Manual backfill for payments (if needed)
DO $$
DECLARE
    payment_record RECORD;
    counter INTEGER := 1;
BEGIN
    FOR payment_record IN 
        SELECT id, created_at, transaction_date FROM public.payments 
        WHERE payment_ref IS NULL 
        ORDER BY COALESCE(transaction_date, created_at)
    LOOP
        UPDATE public.payments 
        SET payment_ref = 'PMT-' || TO_CHAR(COALESCE(payment_record.transaction_date, payment_record.created_at), 'YYYYMMDD') || '-' || LPAD(counter::TEXT, 4, '0')
        WHERE id = payment_record.id;
        counter := counter + 1;
    END LOOP;
    
    RAISE NOTICE 'Updated % payment records', counter - 1;
END
$$;

-- Manual backfill for bookings (if needed)
DO $$
DECLARE
    booking_record RECORD;
    counter INTEGER := 1;
BEGIN
    FOR booking_record IN 
        SELECT id, created_at FROM public.bookings 
        WHERE booking_ref IS NULL 
        ORDER BY created_at
    LOOP
        UPDATE public.bookings 
        SET booking_ref = 'BKG-' || TO_CHAR(booking_record.created_at, 'YYYYMMDD') || '-' || LPAD(counter::TEXT, 4, '0')
        WHERE id = booking_record.id;
        counter := counter + 1;
    END LOOP;
    
    RAISE NOTICE 'Updated % booking records', counter - 1;
END
$$;

-- Verify the updates worked
SELECT 'payments' as table_name, COUNT(*) as total, COUNT(payment_ref) as with_ref
FROM public.payments
UNION ALL
SELECT 'bookings' as table_name, COUNT(*) as total, COUNT(booking_ref) as with_ref  
FROM public.bookings;
