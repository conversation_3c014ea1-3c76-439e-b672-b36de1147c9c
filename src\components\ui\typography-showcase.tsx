"use client"

import React from 'react'
import { Typography, Heading, Text, Label, Caption, Lead, Quote, Code } from './typography'
import { Card, CardContent, CardHeader, CardTitle } from './card'
import { Badge } from './badge'

/**
 * Typography Showcase Component
 * 
 * Demonstrates the comprehensive typography system with:
 * - Font pairings and hierarchy
 * - Proper scale and contrast
 * - Line length controls
 * - Spacing system
 * - Accessibility compliance
 */
export function TypographyShowcase() {
  return (
    <div className="space-y-12 p-6 max-w-6xl mx-auto">
      <div>
        <Typography variant="display-1" className="mb-4">Typography System</Typography>
        <Lead>
          A comprehensive typography system built for readability, accessibility, and visual hierarchy.
          Features optimal line lengths, WCAG AA contrast ratios, and consistent spacing.
        </Lead>
      </div>

      {/* Font Pairing */}
      <Card>
        <CardHeader>
          <CardTitle>Font Pairing</CardTitle>
          <Text size="small" color="muted">
            Consistent font choices that work well together across all content types
          </Text>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <Badge variant="outline" className="mb-3">Headings & UI</Badge>
              <div className="font-heading space-y-2">
                <Typography variant="h2">Geist Sans</Typography>
                <Text>Clean, modern sans-serif optimized for headings and interface elements</Text>
                <div className="text-sm text-gray-500 font-mono">
                  font-family: Geist Sans, system-ui, sans-serif
                </div>
              </div>
            </div>
            
            <div>
              <Badge variant="outline" className="mb-3">Body Text</Badge>
              <div className="font-body space-y-2">
                <Typography variant="h2">Geist Sans</Typography>
                <Text>
                  Same font family ensures consistency while maintaining excellent readability 
                  for longer content with optimized spacing and line height.
                </Text>
                <div className="text-sm text-gray-500 font-mono">
                  font-family: Geist Sans, system-ui, sans-serif
                </div>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <Typography variant="h6" className="text-blue-900 mb-2">✨ Font Features</Typography>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Kerning enabled for optimal letter spacing</li>
              <li>• Ligatures for improved character combinations</li>
              <li>• Contextual alternates for enhanced readability</li>
              <li>• Optimized for both screen and print</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Typography Scale */}
      <Card>
        <CardHeader>
          <CardTitle>Scale and Hierarchy</CardTitle>
          <Text size="small" color="muted">
            Clear visual hierarchy with noticeable size differences between heading levels
          </Text>
        </CardHeader>
        <CardContent className="space-y-8">
          <div>
            <Badge variant="outline" className="mb-4">Display Headings</Badge>
            <div className="space-y-4">
              <Typography variant="display-1">Display 1 - Hero Headlines</Typography>
              <Typography variant="display-2">Display 2 - Section Heroes</Typography>
              <div className="text-sm text-gray-500">
                Large, attention-grabbing text for landing pages and marketing content
              </div>
            </div>
          </div>

          <div>
            <Badge variant="outline" className="mb-4">Content Headings</Badge>
            <div className="space-y-3">
              <Heading level={1}>Heading 1 - Page Title</Heading>
              <Heading level={2}>Heading 2 - Major Section</Heading>
              <Heading level={3}>Heading 3 - Subsection</Heading>
              <Heading level={4}>Heading 4 - Component Title</Heading>
              <Heading level={5}>Heading 5 - Small Section</Heading>
              <Heading level={6}>Heading 6 - Micro Heading</Heading>
            </div>
          </div>

          <div>
            <Badge variant="outline" className="mb-4">Body Text</Badge>
            <div className="space-y-4">
              <Text size="large">
                Large body text for introductions and emphasis. Optimal for lead paragraphs 
                and important content that needs extra visibility.
              </Text>
              <Text size="base">
                Standard body text for regular content. This size provides the best reading 
                experience for most users across different devices and screen sizes.
              </Text>
              <Text size="small">
                Small body text for secondary information, captions, and metadata. 
                Still maintains readability while being less prominent.
              </Text>
            </div>
          </div>

          <div>
            <Badge variant="outline" className="mb-4">UI Elements</Badge>
            <div className="space-y-3">
              <Label>Form Label - Clear and Direct</Label>
              <Caption>Caption text for additional context and help</Caption>
              <Typography variant="overline">OVERLINE - SECTION MARKERS</Typography>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Line Alignment and Length */}
      <Card>
        <CardHeader>
          <CardTitle>Line Alignment and Length</CardTitle>
          <Text size="small" color="muted">
            Optimal reading experience with proper line lengths and alignment options
          </Text>
        </CardHeader>
        <CardContent className="space-y-8">
          <div>
            <Badge variant="outline" className="mb-4">Optimal Reading Width</Badge>
            <div className="space-y-4">
              <Text className="max-w-prose">
                This paragraph demonstrates optimal reading width. Research shows that 50-60 
                characters per line on desktop (about 65ch) provides the best reading experience. 
                On mobile devices, 30-40 characters per line works better due to smaller screens 
                and different reading patterns.
              </Text>
              <div className="text-sm text-gray-500">
                Desktop: ~50-60 characters • Mobile: ~30-40 characters
              </div>
            </div>
          </div>

          <div>
            <Badge variant="outline" className="mb-4">Text Alignment</Badge>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Text align="left">Left aligned text (default) - Best for most content as it provides consistent starting points for each line.</Text>
                <Text align="center">Center aligned text - Good for headings, quotes, and short pieces of content that need emphasis.</Text>
              </div>
              <div className="space-y-4">
                <Text align="right">Right aligned text - Useful for specific layouts, numbers, and secondary information.</Text>
                <Text align="justify">Justified text creates even edges on both sides but can create awkward spacing and should be used sparingly in web design.</Text>
              </div>
            </div>
          </div>

          <div>
            <Badge variant="outline" className="mb-4">Special Formatting</Badge>
            <div className="space-y-4">
              <Quote>
                "Typography is the craft of endowing human language with a durable visual form."
                — Robert Bringhurst
              </Quote>
              <Code>const typography = "readable && beautiful"</Code>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contrast and Accessibility */}
      <Card>
        <CardHeader>
          <CardTitle>Contrast and Accessibility</CardTitle>
          <Text size="small" color="muted">
            WCAG AA compliant color combinations ensuring readability for all users
          </Text>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <Badge variant="outline" className="mb-4">Text Colors</Badge>
              <div className="space-y-3">
                <Text color="default">Default text - High contrast (16.94:1)</Text>
                <Text color="muted">Muted text - Medium contrast (9.35:1)</Text>
                <Text color="subtle">Subtle text - Minimum AA (4.54:1)</Text>
                <Text color="primary">Primary blue - Brand color</Text>
              </div>
            </div>

            <div>
              <Badge variant="outline" className="mb-4">Semantic Colors</Badge>
              <div className="space-y-3">
                <Text color="success">Success messages and positive states</Text>
                <Text color="warning">Warning messages and caution states</Text>
                <Text color="error">Error messages and critical states</Text>
                <Text color="inverse" className="bg-gray-900 p-2 rounded">Inverse text for dark backgrounds</Text>
              </div>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <Typography variant="h6" className="text-green-900 mb-2">♿ Accessibility Features</Typography>
            <ul className="text-sm text-green-800 space-y-1">
              <li>✅ All text meets WCAG AA contrast requirements (4.5:1 minimum)</li>
              <li>✅ Font sizes scale appropriately across devices</li>
              <li>✅ Line heights optimized for readability</li>
              <li>✅ Semantic HTML elements for screen readers</li>
              <li>✅ Sufficient spacing between interactive elements</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Typography Spacing */}
      <Card>
        <CardHeader>
          <CardTitle>Spacing System</CardTitle>
          <Text size="small" color="muted">
            Consistent vertical rhythm and spacing between different text elements
          </Text>
        </CardHeader>
        <CardContent className="space-y-8">
          <div>
            <Badge variant="outline" className="mb-4">Vertical Rhythm</Badge>
            <div className="space-text-normal bg-gray-50 p-6 rounded-lg">
              <Typography variant="h3">Section Heading</Typography>
              <Text>
                This demonstrates normal spacing between text elements. Each paragraph 
                and heading maintains consistent vertical rhythm for better readability.
              </Text>
              <Text>
                Multiple paragraphs flow naturally with appropriate spacing that doesn't 
                feel too cramped or too loose.
              </Text>
              <Typography variant="h4">Subsection Heading</Typography>
              <Text>
                Headings have more space above them to create clear section breaks 
                while maintaining visual connection to the content below.
              </Text>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <Badge variant="outline" className="mb-4">Tight Spacing</Badge>
              <div className="space-text-tight bg-gray-50 p-4 rounded-lg">
                <Typography variant="h5">Compact Layout</Typography>
                <Text size="small">Used for dense information</Text>
                <Text size="small">Like lists or technical content</Text>
                <Text size="small">Where space is at a premium</Text>
              </div>
            </div>

            <div>
              <Badge variant="outline" className="mb-4">Loose Spacing</Badge>
              <div className="space-text-loose bg-gray-50 p-4 rounded-lg">
                <Typography variant="h5">Generous Layout</Typography>
                <Text size="small">Perfect for marketing content</Text>
                <Text size="small">When you want breathing room</Text>
                <Text size="small">For a more premium feel</Text>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Real-world Examples</CardTitle>
          <Text size="small" color="muted">
            How the typography system works in actual content scenarios
          </Text>
        </CardHeader>
        <CardContent className="space-y-8">
          <div>
            <Badge variant="outline" className="mb-4">Article Layout</Badge>
            <article className="space-text-normal max-w-prose">
              <Typography variant="h1">The Future of Car Rentals</Typography>
              <Lead>
                Exploring how technology and changing consumer preferences are reshaping 
                the car rental industry in 2024 and beyond.
              </Lead>
              <Typography variant="h2">Digital Transformation</Typography>
              <Text>
                The car rental industry has undergone significant digital transformation 
                in recent years. Mobile apps, contactless pickup, and AI-powered 
                recommendations have become standard features.
              </Text>
              <Text>
                Customers now expect seamless digital experiences from booking to 
                return, with real-time updates and 24/7 support accessibility.
              </Text>
              <Typography variant="h3">Key Technology Trends</Typography>
              <Text>
                Several technological innovations are driving change in the industry, 
                from IoT-enabled vehicles to blockchain-based identity verification.
              </Text>
            </article>
          </div>

          <div>
            <Badge variant="outline" className="mb-4">Interface Layout</Badge>
            <div className="bg-white border rounded-lg p-6 space-y-4">
              <div className="flex items-center justify-between">
                <Typography variant="h4">Account Settings</Typography>
                <Caption>Last updated 2 hours ago</Caption>
              </div>
              <div className="space-y-3">
                <div>
                  <Label>Full Name</Label>
                  <Text size="small" color="muted">Your display name across the platform</Text>
                </div>
                <div>
                  <Label>Email Address</Label>
                  <Text size="small" color="muted">Used for notifications and account recovery</Text>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default TypographyShowcase
