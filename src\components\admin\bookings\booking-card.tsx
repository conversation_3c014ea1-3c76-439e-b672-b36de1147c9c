"use client"

import * as React from "react"
import { StatusBadge, PaymentBadge } from "./status-badges"
import { CalendarIcon, EyeIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { Button } from "@/components/ui/button"
import { type Booking } from "@/lib/types"
import { BookingDetailsModal } from "./booking-details-modal"
import { useIsMobile } from "@/hooks/use-mobile"

interface BookingCardProps {
  booking: {
    id: string
    booking_ref: string
    userName: string
    carModel: string
    from: Date
    to: Date
    days: number
    status: "Pending" | "Active" | "Completed" | "Cancelled"
    payStatus: "Paid" | "Unpaid" | "Partial" | "Refunded"
    totalAmount: number
    pickup_location: string
    created_at: string
    updated_at: string
  }
  onRowClick?: (booking: any) => void
  onStatusChange?: (bookingId: string, status: string) => void
  onEdit?: (booking: any) => void
  onCancel?: (booking: any) => void
  onViewReceipt?: (booking: any) => void
  onAddToCalendar?: (booking: any) => void
  onAddToOnPageCalendar?: (booking: any) => void
  className?: string
}

export function BookingCard({
  booking,
  onRowClick,
  onStatusChange,
  onEdit,
  onCancel,
  onViewReceipt,
  onAddToCalendar,
  onAddToOnPageCalendar,
  className
}: BookingCardProps) {
  const [isModalOpen, setIsModalOpen] = React.useState(false)
  const isMobile = useIsMobile() // Detects mobile/tablet (< 768px)
  
  // Add dropoff_location property to booking for compatibility with BookingDetailsModal
  const bookingWithDropoff = {
    ...booking,
    dropoff_location: booking.pickup_location // Default to pickup location if dropoff not specified
  }

  const formatDateRange = (date: Date) => {
    const dateFormatted = format(date, "MMM d, yyyy")
    const timeFormatted = format(date, "h:mm a")
    const dayFormatted = format(date, "EEE")
    
    return {
      date: dateFormatted,
      time: timeFormatted,
      day: dayFormatted
    }
  }

  const pickupInfo = formatDateRange(booking.from)
  const returnInfo = formatDateRange(booking.to)

  return (
    <>
      <div 
        className={cn(
          "bg-white rounded-lg xs:rounded-xl border border-gray-200 xs:border-2 shadow-md xs:shadow-lg overflow-hidden mb-3 xs:mb-4",
          "transition-all duration-300 hover:shadow-lg xs:hover:shadow-xl hover:border-blue-300",
          "max-w-full", // Ensure full width on all screen sizes
          className
        )}
      >
        {/* Top Section: Renter, ID, Status - Mobile Optimized */}
        <div className="p-3 xs:p-4 border-b border-gray-100 flex items-center justify-between gap-2">
          <div className="flex-1 min-w-0">
            <h3 className="font-bold text-sm xs:text-base sm:text-lg text-gray-900 truncate leading-tight">{booking.userName}</h3>
            <p className="text-xs font-mono font-bold text-blue-700 mt-1">#{booking.booking_ref}</p>
          </div>
          <StatusBadge status={booking.status} className="flex-shrink-0 text-xs" />
        </div>
        
        {/* Middle Section: Car Model and Essential Info */}
        <div className="p-3 xs:p-4 border-b border-gray-100">
          <div className="flex items-center mb-2 xs:mb-3">
            <div className="h-7 w-7 xs:h-8 xs:w-8 flex-shrink-0 bg-blue-100 rounded-full flex items-center justify-center mr-2 xs:mr-3">
              <span className="text-blue-700 text-base xs:text-lg">🚗</span>
            </div>
            <p className="font-bold text-sm xs:text-base text-gray-900 truncate flex-1 min-w-0">{booking.carModel}</p>
          </div>
          
          {/* Mobile: Show date range in compact format */}
          {isMobile && (
            <div className="bg-gray-50 rounded-lg p-2 xs:p-3">
              <div className="flex items-center justify-between text-xs xs:text-sm text-gray-700">
                <div className="flex items-center gap-1">
                  <span className="font-medium">📅</span>
                  <span>{format(booking.from, "MMM d")} - {format(booking.to, "MMM d")}</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="font-medium">⏰</span>
                  <span>{booking.days}d</span>
                </div>
              </div>
            </div>
          )}
          
          {/* Desktop: Show detailed date info */}
          {!isMobile && (
            <div className="flex items-center justify-between text-sm text-gray-700 mt-2">
              <div>
                <span className="font-medium">From:</span> {format(booking.from, "MMM d")}
              </div>
              <div>
                <span className="font-medium">To:</span> {format(booking.to, "MMM d")}
              </div>
              <div>
                <span className="font-medium">Days:</span> {booking.days}
              </div>
            </div>
          )}
        </div>
        
        {/* Bottom Section: Payment & Action Buttons */}
        <div className="p-3 xs:p-4">
          {/* Mobile: Show payment info in compact format above buttons */}
          {isMobile && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-2 xs:p-3 mb-3 flex items-center justify-between">
              <PaymentBadge status={booking.payStatus} className="text-xs" />
              <p className="font-bold text-sm xs:text-base text-green-700">₱{booking.totalAmount.toFixed(2)}</p>
            </div>
          )}
          
          {/* Desktop: Show payment info in horizontal layout */}
          {!isMobile && (
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <PaymentBadge status={booking.payStatus} />
                <p className="font-bold text-base text-green-700">₱{booking.totalAmount.toFixed(2)}</p>
              </div>
            </div>
          )}
          
          {/* Action Buttons - Mobile Optimized with larger touch targets */}
          <div className="grid grid-cols-2 gap-2 xs:gap-3">
            <Button 
              variant="secondary" 
              className={cn(
                "min-h-[48px] xs:min-h-[44px] flex items-center justify-center border-blue-300 hover:bg-blue-50 transition-all duration-200",
                isMobile ? "px-3 gap-2" : "gap-2" 
              )}
              onClick={() => setIsModalOpen(true)}
              title="View Details"
            >
              <EyeIcon className="h-4 w-4 xs:h-5 xs:w-5 flex-shrink-0" />
              <span className="text-xs xs:text-sm font-medium truncate">
                {isMobile ? "Details" : "View Details"}
              </span>
            </Button>
            
            <Button 
              className={cn(
                "min-h-[48px] xs:min-h-[44px] flex items-center justify-center bg-blue-600 hover:bg-blue-700 transition-all duration-200",
                isMobile ? "px-3 gap-2" : "gap-2"
              )}
              onClick={() => {
                // Prefer on-page calendar if available
                if (onAddToOnPageCalendar) {
                  onAddToOnPageCalendar(booking)
                } else if (onAddToCalendar) {
                  onAddToCalendar(booking)
                } else {
                  // Open Google Calendar directly
                  window.open(
                    `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(`${booking.userName} – ${booking.carModel}`)}&dates=${booking.from.toISOString().replace(/[-:]/g, '').split('.')[0]}Z/${booking.to.toISOString().replace(/[-:]/g, '').split('.')[0]}Z`,
                    '_blank'
                  )
                }
              }}
              title={onAddToOnPageCalendar ? "Add to On-Page Calendar" : "Add to Calendar"}
            >
              <CalendarIcon className="h-4 w-4 xs:h-5 xs:w-5 flex-shrink-0" />
              <span className="text-xs xs:text-sm font-medium truncate">
                {isMobile ? "Calendar" : (onAddToOnPageCalendar ? "Add to Calendar" : "Add to Calendar")}
              </span>
            </Button>
          </div>
        </div>
      </div>

      <BookingDetailsModal
        booking={bookingWithDropoff}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onAddToCalendar={onAddToCalendar ? () => onAddToCalendar(booking) : () => {}}
        onAddToOnPageCalendar={onAddToOnPageCalendar ? () => onAddToOnPageCalendar(booking) : () => {}}
      />
    </>
  )
}
