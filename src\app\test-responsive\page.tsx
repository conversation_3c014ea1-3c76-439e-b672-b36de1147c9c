"use client"

import React, { useState } from 'react'
import { BookingDetailsModal } from '@/components/admin/bookings/booking-details-modal'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestResponsivePage() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [useOnPageCalendar, setUseOnPageCalendar] = useState(false)
  const [calendarEventAdded, setCalendarEventAdded] = useState(false)

  const mockBooking = {
    id: '12345678-abcd-efgh-ijkl-mnopqrstuvwx',
    userName: '<PERSON> with a very long name that might cause overflow issues on small screens',
    carModel: 'Toyota Camry 2023 Deluxe Edition with Premium Package',
    from: new Date('2023-10-15T10:00:00'),
    to: new Date('2023-10-18T14:00:00'),
    days: 3,
    status: 'Active' as const,
    payStatus: 'Paid' as const,
    totalAmount: 12500,
    pickup_location: '123 Main Street, Building Complex A, Floor 5, Unit 512, Anytown, Philippines 12345',
    dropoff_location: '123 Main Street, Building Complex A, Floor 5, Unit 512, Anytown, Philippines 12345',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }

  const handleAddToOnPageCalendar = () => {
    console.log('Adding to on-page calendar:', mockBooking)
    setCalendarEventAdded(true)
    setIsModalOpen(false)
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Responsive Testing Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Test BookingDetailsModal</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  id="useOnPageCalendar" 
                  checked={useOnPageCalendar}
                  onChange={() => setUseOnPageCalendar(!useOnPageCalendar)}
                />
                <label htmlFor="useOnPageCalendar">Use On-Page Calendar</label>
              </div>
              
              <Button onClick={() => setIsModalOpen(true)}>
                Open Booking Modal
              </Button>
              
              {calendarEventAdded && (
                <div className="mt-4 p-3 bg-green-100 text-green-800 rounded-md">
                  Event added to on-page calendar!
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Responsive Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-2">
              <li>Open this page on different devices or use browser dev tools to resize the viewport</li>
              <li>Test with both checkbox options (on and off)</li>
              <li>Verify that the modal adapts correctly to different screen sizes</li>
              <li>Check that text doesn't overflow and buttons are properly sized</li>
              <li>Verify that the "Add to Calendar" button shows the correct text based on the checkbox</li>
            </ol>
          </CardContent>
        </Card>
      </div>
      
      <div className="bg-gray-100 p-4 rounded-lg">
        <h2 className="font-bold mb-2">Current Viewport Size:</h2>
        <div className="grid grid-cols-4 gap-2 text-center">
          <div className="bg-blue-500 text-white p-2 rounded block sm:hidden">xs</div>
          <div className="bg-green-500 text-white p-2 rounded hidden sm:block md:hidden">sm</div>
          <div className="bg-yellow-500 text-white p-2 rounded hidden md:block lg:hidden">md</div>
          <div className="bg-red-500 text-white p-2 rounded hidden lg:block xl:hidden">lg</div>
          <div className="bg-purple-500 text-white p-2 rounded hidden xl:block">xl</div>
        </div>
      </div>
      
      <BookingDetailsModal
        booking={mockBooking}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onAddToCalendar={useOnPageCalendar ? handleAddToOnPageCalendar : () => {
          console.log('Default calendar action')
          window.open(
            `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(`${mockBooking.userName} – ${mockBooking.carModel}`)}&dates=${mockBooking.from.toISOString().replace(/[-:]/g, '').split('.')[0]}Z/${mockBooking.to.toISOString().replace(/[-:]/g, '').split('.')[0]}Z`,
            '_blank'
          )
          setIsModalOpen(false)
        }}
      />
    </div>
  )
}
