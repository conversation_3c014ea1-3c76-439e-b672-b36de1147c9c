import { NextRequest, NextResponse } from 'next/server';
import { createContextClient } from '@/lib/supabase/server';
import { refreshWebsiteKnowledge } from '@/lib/data-ingestion/website-data-extractor';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createContextClient('admin');
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify super_admin privileges only
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || profile.role !== 'super_admin') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 });
    }

    const body = await request.json().catch(() => ({}));
    const { 
      includeCars = true,
      includeBookingInfo = true, 
      includePolicies = true,
      includeAdminProcedures = true,
      includeCustomerInfo = true,
      clearExisting = false
    } = body;

    // Clear existing knowledge if requested
    if (clearExisting) {
      console.log('Clearing existing knowledge base...');
      await supabase.from('chatbot_knowledge').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    }

    // Populate knowledge base
    await refreshWebsiteKnowledge({
      includeCars,
      includeBookingInfo,
      includePolicies,
      includeAdminProcedures,
      includeCustomerInfo
    });

    // Get knowledge count
    const { count } = await supabase
      .from('chatbot_knowledge')
      .select('*', { count: 'exact', head: true });

    return NextResponse.json({
      success: true,
      message: 'Knowledge base populated successfully',
      knowledgeCount: count || 0,
      config: {
        includeCars,
        includeBookingInfo,
        includePolicies,
        includeAdminProcedures,
        includeCustomerInfo,
        clearExisting
      }
    });

  } catch (error) {
    console.error('Error populating knowledge base:', error);
    return NextResponse.json(
      { 
        error: 'Failed to populate knowledge base', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      }, 
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const supabase = await createContextClient('admin');
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify super_admin privileges only
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || profile.role !== 'super_admin') {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 });
    }

    // Get knowledge base statistics
    const [countResult, categoriesResult, sourcesResult] = await Promise.all([
      supabase.from('chatbot_knowledge').select('*', { count: 'exact', head: true }),
      supabase.from('chatbot_knowledge').select('category').order('category'),
      supabase.from('chatbot_knowledge').select('source').order('source')
    ]);

    const categories = [...new Set(categoriesResult.data?.map(item => item.category) || [])];
    const sources = [...new Set(sourcesResult.data?.map(item => item.source) || [])];

    return NextResponse.json({
      knowledgeCount: countResult.count || 0,
      categories,
      sources,
      lastUpdated: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error getting knowledge base info:', error);
    return NextResponse.json(
      { error: 'Failed to get knowledge base info' }, 
      { status: 500 }
    );
  }
}
