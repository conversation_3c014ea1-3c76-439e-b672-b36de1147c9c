import { <PERSON>, CardContent, <PERSON>Header } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export default function SalesTrackingLoading() {
  return (
    <div className="space-y-6 p-2 sm:p-4 lg:p-6 max-w-full overflow-hidden">
      {/* Page Header Skeleton */}
      <header className="space-y-2">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-4 w-96" />
      </header>

      {/* Filters Bar Skeleton */}
      <div className="bg-white border border-border rounded-xl p-4 shadow-sm">
        <div className="flex flex-wrap gap-4 items-end">
          <div className="flex flex-col space-y-2 min-w-[220px]">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-11 w-full" />
          </div>
          <div className="flex flex-col space-y-2 min-w-[150px]">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-11 w-full" />
          </div>
          <div className="flex flex-col space-y-2 min-w-[150px]">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-11 w-full" />
          </div>
          <div className="flex flex-col space-y-2 min-w-[140px]">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-11 w-full" />
          </div>
          <div className="flex flex-col space-y-2 min-w-[140px]">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-11 w-full" />
          </div>
          <div className="flex flex-col space-y-2 min-w-[200px]">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-11 w-full" />
          </div>
          <Skeleton className="h-11 w-20" />
        </div>
      </div>

      {/* KPI Cards Skeleton */}
      <section className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="bg-white border border-gray-200 shadow-sm rounded-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4 rounded" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-24 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </section>

      {/* Charts Section Skeleton */}
      <section className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Revenue Over Time Chart Skeleton */}
        <Card className="bg-white border border-gray-200 shadow-sm rounded-xl">
          <CardHeader>
            <Skeleton className="h-6 w-40" />
            <Skeleton className="h-4 w-56" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px] w-full" />
          </CardContent>
        </Card>

        {/* Revenue by Location Chart Skeleton */}
        <Card className="bg-white border border-gray-200 shadow-sm rounded-xl">
          <CardHeader>
            <Skeleton className="h-6 w-44" />
            <Skeleton className="h-4 w-60" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px] w-full" />
          </CardContent>
        </Card>
      </section>

      {/* Table Skeleton */}
      <Card className="bg-white border border-gray-200 shadow-sm rounded-xl">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <Skeleton className="h-6 w-40" />
            <Skeleton className="h-4 w-64" />
          </div>
          <Skeleton className="h-9 w-28" />
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            {/* Table Header Skeleton */}
            <div className="bg-gray-50 border-b border-gray-200">
              <div className="flex">
                {Array.from({ length: 12 }).map((_, i) => (
                  <div key={i} className="min-w-[100px] p-4">
                    <Skeleton className="h-4 w-16" />
                  </div>
                ))}
              </div>
            </div>
            
            {/* Table Rows Skeleton */}
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="border-b border-gray-100">
                <div className="flex">
                  {Array.from({ length: 12 }).map((_, j) => (
                    <div key={j} className="min-w-[100px] p-4">
                      <Skeleton className="h-4 w-full" />
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
          
          {/* Pagination Skeleton */}
          <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200">
            <Skeleton className="h-4 w-48" />
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-8 w-16" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
