#!/bin/bash
# <PERSON>ript to deploy the send-email Edge Function to Supabase

echo "Starting deployment of send-email Edge Function..."

# Navigate to the project root (assuming this script is run from the scripts directory)
cd "$(dirname "$0")/.."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Error: Supabase CLI is not installed."
    echo "Please install it using: npm install -g supabase"
    exit 1
fi

# Deploy the function without JWT verification
# This allows the function to be called without authentication
echo "Deploying send-email function..."
supabase functions deploy send-email --no-verify-jwt

if [ $? -eq 0 ]; then
    echo "✅ send-email function deployed successfully!"
    
    echo ""
    echo "IMPORTANT: Don't forget to:"
    echo "1. Configure SMTP settings in Supabase dashboard"
    echo "2. Deploy the SQL function 'send_email_smtp' using the SQL editor"
    echo "3. Test by finalizing a booking in the admin dashboard"
    echo ""
    echo "For detailed setup instructions, see docs/SUPABASE_EMAIL_SETUP.md"
else
    echo "❌ Deployment failed. Please check the error message above."
fi
