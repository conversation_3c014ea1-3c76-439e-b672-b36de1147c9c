"use client"

import { useAdminAuth } from "@/components/auth/admin-auth-context"
import { useEffect, useState } from "react"

export default function AdminDebugAuthPage() {
  const { user, profile, loading } = useAdminAuth()
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
  }

  useEffect(() => {
    addLog(`Auth state: loading=${loading}, user=${!!user}, profile=${!!profile}`)

    if (typeof window !== 'undefined') {
      addLog(`Current path: ${window.location.pathname}`)
      addLog(`Current href: ${window.location.href}`)
    } else {
      addLog(`Current path: SSR (window not available)`)
    }

    if (user) {
      addLog(`User email: ${user.email}`)
    }

    if (profile) {
      addLog(`Profile role: ${profile.role}`)
    }
  }, [loading, user, profile])

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Admin Auth Debug Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Current Auth State</h2>
          <div className="space-y-2 text-sm">
            <div>Loading: <span className={loading ? "text-orange-600" : "text-green-600"}>{loading.toString()}</span></div>
            <div>User: <span className={user ? "text-green-600" : "text-red-600"}>{user ? "✓ Authenticated" : "✗ Not authenticated"}</span></div>
            <div>Profile: <span className={profile ? "text-green-600" : "text-red-600"}>{profile ? "✓ Loaded" : "✗ Not loaded"}</span></div>
            {user && <div>Email: <span className="text-blue-600">{user.email}</span></div>}
            {profile && <div>Role: <span className="text-blue-600">{profile.role}</span></div>}
          </div>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Current Location</h2>
          <div className="space-y-2 text-sm">
            <div>Pathname: <span className="text-blue-600">{typeof window !== 'undefined' ? window.location.pathname : 'N/A'}</span></div>
            <div>Href: <span className="text-blue-600 break-all">{typeof window !== 'undefined' ? window.location.href : 'N/A'}</span></div>
          </div>
        </div>
      </div>

      <div className="mt-6 bg-gray-100 p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">Auth State Log</h2>
        <div className="bg-black text-green-400 p-3 rounded text-sm font-mono max-h-64 overflow-y-auto">
          {logs.map((log, index) => (
            <div key={index}>{log}</div>
          ))}
        </div>
      </div>

      <div className="mt-6 bg-blue-50 p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">Test Instructions</h2>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Navigate to this page: <code>/admin/debug-auth</code></li>
          <li>Observe the current auth state</li>
          <li>Press F5 to refresh the page</li>
          <li>Watch the auth state log to see what happens during reload</li>
          <li>Check if you get redirected to <code>/admin-auth</code></li>
        </ol>
      </div>

      <div className="mt-6 bg-yellow-50 p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">Expected Behavior</h2>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>After refresh, should stay on this page (no redirect)</li>
          <li>Loading should be true initially, then false</li>
          <li>User and profile should be populated after loading completes</li>
          <li>Should NOT redirect to <code>/admin-auth</code></li>
        </ul>
      </div>
    </div>
  )
}
