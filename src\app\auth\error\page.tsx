"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { AlertTriangle } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export default function AuthErrorPage() {
  return (
    <div className="min-h-screen grid place-items-center bg-muted/20 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <Image
              src="/ollie_logo.svg"
              alt="Ollie Track"
              width={120}
              height={40}
              className="h-10 w-auto"
            />
          </div>
          <div className="flex justify-center mb-4">
            <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-full">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
          </div>
          <CardTitle className="text-xl font-semibold text-red-600">Authentication Error</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            There was a problem with your authentication request
          </p>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-sm text-muted-foreground">
            This could happen if:
          </p>
          <ul className="text-xs text-left text-muted-foreground space-y-1">
            <li>• The confirmation link has expired</li>
            <li>• The link has already been used</li>
            <li>• The link was invalid or corrupted</li>
          </ul>
          
          <div className="flex flex-col gap-2 pt-4">
            <Link href="/customer/login">
              <Button className="w-full">
                Go to Customer Login
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
