"use client";

import * as React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { MapPin } from "lucide-react";
import { cn } from "@/lib/utils";

// Pickup garage location checkbox component
interface PickupGarageCheckboxProps {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  className?: string;
  disabled?: boolean;
}

export function PickupGarageCheckbox({
  checked,
  onCheckedChange,
  className,
  disabled = false,
}: PickupGarageCheckboxProps) {
  return (
    <div className={cn("flex items-start gap-3 p-3 rounded-lg border border-gray-200 bg-gray-50 hover:bg-gray-100 transition-colors", className)}>
      <Checkbox
        id="pickup-garage-checkbox"
        checked={checked}
        onCheckedChange={onCheckedChange}
        disabled={disabled}
        className="mt-0.5 flex-shrink-0"
      />
      <div className="flex items-start gap-2 flex-1 min-w-0">
        <MapPin className="h-4 w-4 text-green-600 flex-shrink-0 mt-0.5" />
        <label
          htmlFor="pickup-garage-checkbox"
          className="text-sm font-medium text-gray-700 cursor-pointer leading-tight break-words"
        >
          Pick Up Ollie's Garage: #9 Lubnac, Vintar, Ilocos Norte
        </label>
      </div>
    </div>
  );
}

// Drop-off same as pickup checkbox component
interface DropoffSameAsPickupCheckboxProps {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  pickupLocation?: string;
  className?: string;
  disabled?: boolean;
}

export function DropoffSameAsPickupCheckbox({
  checked,
  onCheckedChange,
  pickupLocation,
  className,
  disabled = false,
}: DropoffSameAsPickupCheckboxProps) {
  return (
    <div className={cn("flex items-start gap-3 p-3 rounded-lg border border-gray-200 bg-gray-50 hover:bg-gray-100 transition-colors", className)}>
      <Checkbox
        id="dropoff-same-pickup-checkbox"
        checked={checked}
        onCheckedChange={onCheckedChange}
        disabled={disabled || !pickupLocation}
        className="mt-0.5 flex-shrink-0"
      />
      <div className="flex items-start gap-2 flex-1 min-w-0">
        <MapPin className="h-4 w-4 text-orange-600 flex-shrink-0 mt-0.5" />
        <div className="flex-1 min-w-0">
          <label
            htmlFor="dropoff-same-pickup-checkbox"
            className={cn(
              "text-sm font-medium cursor-pointer leading-tight break-words block",
              disabled || !pickupLocation ? "text-gray-400" : "text-gray-700"
            )}
          >
            Same location as pickup
          </label>
          {pickupLocation && (
            <p className="text-xs text-gray-500 mt-1 break-words">
              Drop off at: {pickupLocation}
            </p>
          )}
          {!pickupLocation && (
            <p className="text-xs text-gray-400 mt-1">
              Select a pickup location first
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
