"use client"

import * as React from "react"
import { Calendar, Clock } from "lucide-react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { TimePicker } from "@/components/customer-side/time"
import { DatePicker } from "@/components/customer-side/date"
import { 
  calculateDayOnlyRental, 
  formatRentalDuration,
  getMinimumDate,
  getMinimumDropoffDate
} from "@/utils/booking-date-validation"

// Helper function for today's date
function getTodayDate(): string {
  const now = new Date()
  return now.toISOString().split('T')[0] // YYYY-MM-DD format
}

interface DateTimePickerProps {
  pickupDate: string
  pickupTime: string
  dropoffDate: string
  dropoffTime: string
  onPickupDateChange: (date: string) => void
  onPickupTimeChange: (time: string) => void
  onDropoffDateChange: (date: string) => void
  onDropoffTimeChange: (time: string) => void
  className?: string
}

export function DateTimePicker({
  pickupDate,
  pickupTime,
  dropoffDate,
  dropoffTime,
  onPickupDateChange,
  onPickupTimeChange,
  onDropoffDateChange,
  onDropoffTimeChange,
  className
}: DateTimePickerProps) {
  // Set default time to 8:00 AM if not provided
  React.useEffect(() => {
    if (!pickupTime) {
      onPickupTimeChange("08:00")
    }
    if (!dropoffTime) {
      onDropoffTimeChange("19:00")
    }
  }, [pickupTime, dropoffTime, onPickupTimeChange, onDropoffTimeChange])

  return (
    <div className={cn("space-y-6", className)}>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        {/* Pick-up Date */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
            <Calendar className="h-4 w-4 text-blue-600" />
            Pick-up Date
          </Label>
          <DatePicker
            value={pickupDate}
            onChange={onPickupDateChange}
            placeholder="Select date"
            minDate={getMinimumDate()}
            className="h-12 border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100 transition-all duration-200"
          />
        </div>

        {/* Pick-up Time */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
            <Clock className="h-4 w-4 text-blue-600" />
            Pick-up Time
          </Label>
          <TimePicker
            value={pickupTime}
            onChange={onPickupTimeChange}
            placeholder="Select time"
            minTime="06:00"
            maxTime="22:00"
            step={60}
            showQuickActions={false}
            showSecondaryFormat={true}
            aria-label="Pick-up time"
            className="h-12 border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100"
          />
        </div>

        {/* Drop-off Date */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
            <Calendar className="h-4 w-4 text-blue-600" />
            Drop-off Date
          </Label>
          <DatePicker
            value={dropoffDate}
            onChange={onDropoffDateChange}
            placeholder="Select date"
            minDate={getMinimumDropoffDate(pickupDate)}
            className="h-12 border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100 transition-all duration-200"
          />
        </div>

        {/* Drop-off Time */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
            <Clock className="h-4 w-4 text-blue-600" />
            Drop-off Time
          </Label>
          <TimePicker
            value={dropoffTime}
            onChange={onDropoffTimeChange}
            placeholder="Select time"
            minTime="06:00"
            maxTime="22:00"
            step={60}
            showQuickActions={false}
            showSecondaryFormat={true}
            aria-label="Drop-off time"
            className="h-12 border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100"
          />
        </div>
      </div>


      {/* Duration Display */}
      {pickupDate && dropoffDate && (
        <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2 text-gray-600">
              <Calendar className="h-4 w-4" />
              <span>Rental Duration (Day-Only)</span>
            </div>
            <div className="flex items-center gap-4">
              <span className="text-blue-600 font-medium">
                {calculateDayOnlyDuration(pickupDate, dropoffDate)}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

function calculateDayOnlyDuration(pickupDate: string, dropoffDate: string): string {
  if (!pickupDate || !dropoffDate) return ""
  
  const rental = calculateDayOnlyRental(pickupDate, dropoffDate)
  
  if (!rental.isValid) {
    return rental.error || "Invalid dates"
  }
  
  return formatRentalDuration(rental.days)
}
