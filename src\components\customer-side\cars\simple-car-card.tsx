"use client"

import { type Car } from "@/lib/types"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle } from "lucide-react"
import Image from "next/image"
import * as React from "react"

interface SimpleCarCardProps {
  car: Car
  isSelected: boolean
  onSelect: (car: Car) => void
}

export function SimpleCarCard({ car, isSelected, onSelect }: SimpleCarCardProps) {
  const isAvailable = car.status === "Available"
  
  if (!isAvailable) return null
  
  return (
    <Card 
      className={`simple-car-card overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer ${
        isSelected ? "ring-2 ring-blue-500 bg-blue-50" : "border border-gray-200 hover:border-blue-300"
      }`}
      onClick={() => onSelect(car)}
    >
      <CardContent className="p-4">
        {/* Header with selection indicator */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 text-sm leading-tight line-clamp-2">
              {car.model}
            </h3>
          </div>
          {isSelected && (
            <div className="bg-blue-600 text-white rounded-full p-1 ml-2">
              <CheckCircle className="h-3 w-3" />
            </div>
          )}
        </div>

        {/* Car Image */}
        <div className="relative w-full h-24 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg overflow-hidden mb-3">
          <Image
            src={car.image_url || "/placeholder.svg"}
            alt={car.model}
            fill
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
            style={{ objectFit: "contain" }}
            className="transition-transform duration-200"
          />
        </div>

        {/* Essential Info Only */}
        <div className="space-y-2">
          {/* Price */}
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">
              ₱{car.price_per_day.toFixed(0)}
            </div>
            <div className="text-xs text-gray-500">per day</div>
          </div>
          
          {/* Transmission */}
          <div className="text-center">
            <div className="text-xs text-gray-600 font-medium">
              {car.transmission}
            </div>
          </div>
        </div>

        {/* Select Button */}
        <Button 
          className={`w-full mt-3 text-sm py-2 ${
            isSelected 
              ? "bg-blue-600 text-white" 
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          }`}
          onClick={(e) => {
            e.stopPropagation()
            onSelect(car)
          }}
        >
          {isSelected ? "Selected" : "Select"}
        </Button>
      </CardContent>
    </Card>
  )
}
