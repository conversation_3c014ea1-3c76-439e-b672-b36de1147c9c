-- Migration script to add separate name fields to existing profiles table
-- This migration safely adds new fields while preserving all existing data

-- Step 1: Add new columns to existing table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS first_name text,
ADD COLUMN IF NOT EXISTS middle_initial text,
ADD COLUMN IF NOT EXISTS last_name text;

-- Step 2: Add constraint for middle_initial length
ALTER TABLE public.profiles 
ADD CONSTRAINT IF NOT EXISTS profiles_middle_initial_check 
CHECK (middle_initial is null or length(middle_initial) <= 1);

-- Step 3: Create indexes for new name fields
CREATE INDEX IF NOT EXISTS idx_profiles_first_name ON public.profiles USING btree (first_name);
CREATE INDEX IF NOT EXISTS idx_profiles_last_name ON public.profiles USING btree (last_name);

-- Step 4: Create helper functions for name parsing and syncing
CREATE OR REPLACE FUNCTION sync_full_name_from_parts()
RETURNS trigger AS $$
BEGIN
  -- Only update full_name if individual name fields are provided
  IF NEW.first_name IS NOT NULL OR NEW.middle_initial IS NOT NULL OR NEW.last_name IS NOT NULL THEN
    NEW.full_name := trim(
      concat_ws(' ', 
        nullif(trim(COALESCE(NEW.first_name, '')), ''),
        nullif(trim(COALESCE(NEW.middle_initial, '')), ''),
        nullif(trim(COALESCE(NEW.last_name, '')), '')
      )
    );
    -- Set to null if result is empty string
    IF NEW.full_name = '' THEN
      NEW.full_name := null;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION parse_full_name_to_parts()
RETURNS trigger AS $$
BEGIN
  -- Only parse if individual fields are null but full_name exists
  IF NEW.full_name IS NOT NULL AND 
     NEW.first_name IS NULL AND 
     NEW.middle_initial IS NULL AND 
     NEW.last_name IS NULL THEN
    
    DECLARE
      name_parts text[];
      part_count integer;
    BEGIN
      -- Split full name by spaces and filter empty parts
      name_parts := array_remove(string_to_array(trim(NEW.full_name), ' '), '');
      part_count := array_length(name_parts, 1);
      
      IF part_count = 1 THEN
        NEW.first_name := name_parts[1];
      ELSIF part_count = 2 THEN
        NEW.first_name := name_parts[1];
        NEW.last_name := name_parts[2];
      ELSIF part_count >= 3 THEN
        NEW.first_name := name_parts[1];
        -- Check if second part looks like middle initial (single char or ends with .)
        IF length(name_parts[2]) = 1 OR name_parts[2] LIKE '%.' THEN
          NEW.middle_initial := upper(replace(name_parts[2], '.', ''));
          NEW.last_name := array_to_string(name_parts[3:], ' ');
        ELSE
          -- Treat as compound last name
          NEW.last_name := array_to_string(name_parts[2:], ' ');
        END IF;
      END IF;
    END;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Create triggers (order matters - parse first, then sync)
DROP TRIGGER IF EXISTS parse_full_name_trigger ON public.profiles;
DROP TRIGGER IF EXISTS sync_full_name_trigger ON public.profiles;

CREATE TRIGGER parse_full_name_trigger
  BEFORE INSERT OR UPDATE ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION parse_full_name_to_parts();

CREATE TRIGGER sync_full_name_trigger
  BEFORE INSERT OR UPDATE ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION sync_full_name_from_parts();

-- Step 6: Migrate existing data by parsing full_name into separate fields
-- This will trigger the parse function for existing records
UPDATE public.profiles 
SET updated_at = updated_at 
WHERE full_name IS NOT NULL 
  AND (first_name IS NULL AND middle_initial IS NULL AND last_name IS NULL);

-- Step 7: Verify migration completed successfully
DO $$
DECLARE
  total_records integer;
  migrated_records integer;
BEGIN
  SELECT COUNT(*) INTO total_records FROM public.profiles WHERE full_name IS NOT NULL;
  SELECT COUNT(*) INTO migrated_records FROM public.profiles WHERE full_name IS NOT NULL AND first_name IS NOT NULL;
  
  RAISE NOTICE 'Migration Summary:';
  RAISE NOTICE 'Total records with full_name: %', total_records;
  RAISE NOTICE 'Records migrated to name fields: %', migrated_records;
  RAISE NOTICE 'Migration completed successfully!';
END $$;

-- Step 8: Comments for documentation
COMMENT ON COLUMN public.profiles.first_name IS 'First name extracted or entered separately';
COMMENT ON COLUMN public.profiles.middle_initial IS 'Middle initial (optional, max 1 character)';
COMMENT ON COLUMN public.profiles.last_name IS 'Last name extracted or entered separately';
COMMENT ON COLUMN public.profiles.full_name IS 'Full name (maintained for backward compatibility, auto-synced from name parts)';

-- Optional: Create view for easier querying with computed full name
CREATE OR REPLACE VIEW profiles_with_computed_name AS
SELECT 
  id,
  email,
  full_name,
  first_name,
  middle_initial,
  last_name,
  COALESCE(
    full_name,
    trim(concat_ws(' ', first_name, middle_initial, last_name))
  ) AS computed_full_name,
  phone,
  role,
  avatar_url,
  created_at,
  updated_at
FROM public.profiles;
