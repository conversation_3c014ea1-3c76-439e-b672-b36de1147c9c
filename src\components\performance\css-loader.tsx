"use client"

import { useEffect } from 'react'

export function LeafletCSSLoader() {
  useEffect(() => {
    // Create a non-blocking CSS loader for Leaflet
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'
    link.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY='
    link.crossOrigin = 'anonymous'
    link.media = 'print'
    
    link.onload = () => {
      link.media = 'all'
    }
    
    document.head.appendChild(link)
    
    return () => {
      // Cleanup on unmount
      if (document.head.contains(link)) {
        document.head.removeChild(link)
      }
    }
  }, [])

  return null
}
