-- Migration: Add Day/Night selection and delivery fee columns to bookings table
-- This adds support for storing pickup/return time periods and associated delivery fees

-- Add new columns to the bookings table
ALTER TABLE bookings 
ADD COLUMN pickup_time_period TEXT CHECK (pickup_time_period IN ('day', 'night')) DEFAULT 'day',
ADD COLUMN return_time_period TEXT CHECK (return_time_period IN ('day', 'night')) DEFAULT 'day',
ADD COLUMN delivery_fee DECIMAL(10, 2) DEFAULT 0.00,
ADD COLUMN return_fee DECIMAL(10, 2) DEFAULT 0.00,
ADD COLUMN total_delivery_fees DECIMAL(10, 2) DEFAULT 0.00;

-- Add comments for documentation
COMMENT ON COLUMN bookings.pickup_time_period IS 'Time period category for pickup: day (7:00 AM - 7:00 PM) or night (7:01 PM - 6:59 AM)';
COMMENT ON COLUMN bookings.return_time_period IS 'Time period category for return: day (7:00 AM - 7:00 PM) or night (7:01 PM - 6:59 AM)';
COMMENT ON COLUMN bookings.delivery_fee IS 'Fee charged for vehicle delivery to pickup location based on location and time period';
COMMENT ON COLUMN bookings.return_fee IS 'Fee charged for vehicle return from drop-off location based on location and time period';
COMMENT ON COLUMN bookings.total_delivery_fees IS 'Total of delivery_fee + return_fee for easy calculation and display';

-- Create index for efficient querying by time periods
CREATE INDEX idx_bookings_time_periods ON bookings (pickup_time_period, return_time_period);

-- Update existing bookings to have default day time period if null
UPDATE bookings 
SET pickup_time_period = 'day', 
    return_time_period = 'day',
    delivery_fee = 0.00,
    return_fee = 0.00,
    total_delivery_fees = 0.00
WHERE pickup_time_period IS NULL OR return_time_period IS NULL;

-- Sample data showing how the new fields would be populated
-- (Uncomment to insert sample data for testing)
/*
INSERT INTO bookings (
    customer_id,
    car_id,
    pickup_location,
    dropoff_location,
    pickup_datetime,
    dropoff_datetime,
    pickup_time_period,
    return_time_period,
    delivery_fee,
    return_fee,
    total_delivery_fees,
    total_amount,
    status
) VALUES (
    'sample-customer-id',
    'sample-car-id',
    'Laoag Centro',
    'SM / Robinsons',
    '2024-01-15 08:00:00',
    '2024-01-16 20:00:00',
    'day',
    'night',
    250.00,
    400.00,
    650.00,
    2150.00, -- Example: car rental + delivery fees
    'pending'
);
*/
