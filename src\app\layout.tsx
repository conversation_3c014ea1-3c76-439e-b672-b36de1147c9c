import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "geist/font/sans";
import { cn } from '@/lib/utils'
import { AdminAuthProvider } from '@/components/auth/admin-auth-context'
import { CustomerAuthProvider } from '@/components/auth/customer-auth-context'
import { LeafletCSSLoader } from '@/components/performance/css-loader';
import { GeistMono } from "geist/font/mono";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import "../styles/globals.css";

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export const metadata: Metadata = {
  title: {
    default: "Ollie's Rent A Car - Premium Car Rental in Ilocos | Pathlink",
    template: "%s | Ollie's Rent A Car",
  },
  description:
    "Premium car rental service in Ilocos Norte. Book modern, well-maintained vehicles from trusted brands. Contact us via Messenger to complete your reservation. Powered by Pathlink technology.",
  keywords: [
    "car rental",
    "Ilocos",
    "vehicle rental",
    "Toyota",
    "Mitsubishi",
    "rent a car",
    "Pathlink",
    "Ollie's",
  ],
  authors: [{ name: "Pathlink" }],
  creator: "Pathlink",
  publisher: "Ollie's Rent A Car",
  icons: {
    icon: [
      { url: "/ollie_logo.jpg", sizes: "16x16", type: "image/jpeg" },
      { url: "/ollie_logo.jpg", sizes: "32x32", type: "image/jpeg" },
      { url: "/ollie_logo.jpg", sizes: "48x48", type: "image/jpeg" },
    ],
    shortcut: "/ollie_logo.jpg",
    apple: [{ url: "/ollie_logo.jpg", sizes: "180x180", type: "image/jpeg" }],
  },
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://olliesrentacar.com",
    siteName: "Ollie's Rent A Car",
    title: "Ollie's Rent A Car - Premium Car Rental in Ilocos",
    description:
      "Premium car rental service in Ilocos Norte. Book modern vehicles and contact us via Messenger to complete your reservation.",
  },
  twitter: {
    card: "summary_large_image",
    title: "Ollie's Rent A Car - Premium Car Rental in Ilocos",
    description:
      "Premium car rental service in Ilocos Norte. Book modern vehicles and contact us via Messenger to complete your reservation.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link
          rel="preconnect"
          href="https://unpkg.com"
        />
        <link
          rel="stylesheet"
          href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossOrigin=""
        />
        <style>{`
html {
  font-family: ${GeistSans.style.fontFamily};
  --font-sans: ${GeistSans.variable};
  --font-mono: ${GeistMono.variable};
}
        `}</style>
      </head>
      <body
        className="min-h-screen bg-white transition-colors duration-300"
        suppressHydrationWarning
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange
        >
          <LeafletCSSLoader />
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
