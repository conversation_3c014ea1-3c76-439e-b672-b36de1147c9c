"use client";

/**
 * NewHomepage Component - Customer Homepage with Fully Responsive Quick Booking
 *
 * RECENT UPDATES - Enhanced Mobile Responsiveness & Messenger Section Removal:
 * - Fixed Quick Booking section responsiveness across all screen sizes:
 *   * xs (<375px): Single-column stacked layout, full-width inputs with proper overflow handling
 *   * sm (≥640px): 2-column grid for locations, enhanced spacing and touch targets
 *   * md (≥768px): Improved 2-column layout with better input containment
 *   * lg (≥1024px): 4-column grid for date/time fields, optimized desktop experience
 *   * xl (≥1280px): Constrained max-width with proper centering and spacing
 * - Enhanced location input responsiveness with proper overflow containment
 * - Improved date/time picker mobile experience with enhanced touch targets
 * - Added better button responsive design with proper text truncation
 * - Completely removed "Complete Your Booking via Messenger" section
 * - Enhanced accessibility: ≥44px touch targets, proper focus states, semantic markup
 * - No horizontal scroll on any breakpoint, proper text wrapping and overflow handling
 * - Improved mobile-first approach with progressive enhancement for larger screens
 */

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { DateTimePicker } from "@/components/ui/datetime-picker";
import { TimePicker } from "@/components/customer-side/time";
import { DatePicker } from "@/components/customer-side/date";
import {
  FixedPickupLocationField,
  DropOffLocationDropdown,
  FIXED_PICKUP_LOCATION,
  PickupLocationField,
  DropOffLocationField,
} from "@/components/ui/booking-location-components";
import {
  PickupGarageCheckbox,
  DropoffSameAsPickupCheckbox,
} from "@/components/ui/booking-location-checkboxes";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  Car as CarIcon,
  Calendar,
  MapPin,
  MessageCircle,
  Star,
  Users,
  Shield,
  Clock,
  Fuel,
  Cog,
  CheckCircle,
  Phone,
  Mail,
  ArrowRight,
  Search,
  Filter,
  ChevronDown,
  Zap,
  Award,
  Globe,
} from "lucide-react";
import { CarCard } from "@/components/customer-side/cars/car-card";
import { CategoryCard } from "@/components/customer-side/cars/category-card";
import { VanRentalCategoryCard } from "@/components/customer-side/cars/van-rental-category-card";
import type { Car } from "@/lib/types";
import Image from "next/image";
import { CustomerImagesCarousel } from "@/components/customer-side/home/<USER>";
import { createClient } from "@/lib/supabase/client";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";
import { buildBookingFlowUrl } from "@/lib/customer-paths";
import { QuickBookingVehicleSearch } from "@/components/customer-side/home/<USER>";
import { 
  calculateDayOnlyRental, 
  formatRentalDuration,
  validateBookingDateTime,
  getMinimumDate,
  getMinimumDropoffDate
} from "@/utils/booking-date-validation";

// Helper function for date formatting
function getTodayDate(): string {
  const now = new Date();
  return now.toISOString().split("T")[0]; // YYYY-MM-DD format
}

export function NewHomepage() {
  // Authentication state - safe usage that works inside CustomerAuthProvider
  const { user, profile } = (() => {
    try {
      return useCustomerAuth();
    } catch (error) {
      // If not within CustomerAuthProvider, return null user
      return { user: null, profile: null };
    }
  })();

  const router = useRouter();

  const [selectedPickupDate, setSelectedPickupDate] = useState("");
  const [selectedPickupTime, setSelectedPickupTime] = useState("08:00");
  const [selectedPickupLocation, setSelectedPickupLocation] = useState("");
  const [selectedDropoffDate, setSelectedDropoffDate] = useState("");
  const [selectedDropoffTime, setSelectedDropoffTime] = useState("19:00");
  const [selectedDropoffLocation, setSelectedDropoffLocation] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [showQuickResults, setShowQuickResults] = useState(false);
  const [pickupGarageChecked, setPickupGarageChecked] = useState(false);
  const [dropoffSameAsPickupChecked, setDropoffSameAsPickupChecked] = useState(false);

  // State for storing cars and categories from database
  const [cars, setCars] = useState<Car[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch cars and categories from Supabase
  useEffect(() => {
    async function fetchCarsAndCategories() {
      try {
        setLoading(true);
        const supabase = createClient();

        // Fetch categories with stats using RPC
        const { data: categoriesData, error: categoriesError } =
          await supabase.rpc("get_vehicle_categories_with_stats");

        if (categoriesError) throw categoriesError;

        // Fetch cars
        const { data: carsData, error: carsError } = await supabase
          .from("cars")
          .select("*")
          .eq("is_archived", false);

        if (carsError) throw carsError;

        setCategories(categoriesData || []);
        setCars(carsData || []);
      } catch (error) {
        console.error("Error fetching cars and categories:", error);
        setCategories([]);
        setCars([]);
      } finally {
        setLoading(false);
      }
    }

    fetchCarsAndCategories();
  }, []);

  // Get all available cars for category display
  const availableStaticCars = cars.filter(
    (car: Car) => car.status === "Available"
  );

  // Calculate rental duration helper function - DAY-ONLY rentals
  const calculateDuration = (
    pickupDate: string,
    pickupTime: string,
    dropoffDate: string,
    dropoffTime: string
  ): string => {
    if (!pickupDate || !dropoffDate) return "";

    // Use day-only calculation - time is for pickup/delivery scheduling only
    const rental = calculateDayOnlyRental(pickupDate, dropoffDate);
    
    if (!rental.isValid) {
      return rental.error || "Invalid dates";
    }

    return formatRentalDuration(rental.days);
  };

  // Group cars by category for homepage preview
  const carsByCategory = React.useMemo(() => {
    const grouped = availableStaticCars.reduce(
      (acc: Record<string, Car[]>, car: Car) => {
        // Exclude van vehicles from regular categories (they have their own special category)
        const isVan = car.model?.toLowerCase().includes("grandia") || 
                     car.plate_number?.includes("VAN-") ||
                     car.notes?.toLowerCase().includes("with driver");
        
        if (!isVan) {
          if (!acc[car.type]) {
            acc[car.type] = [];
          }
          acc[car.type].push(car);
        }
        return acc;
      },
      {} as Record<string, Car[]>
    );

    return grouped;
  }, [availableStaticCars]);

  // Trigger the vehicle search when Search button is clicked
  const handleSearchClick = () => {
    setShowQuickResults(!showQuickResults);
  };

  // Close vehicle search
  const handleCloseVehicleSearch = () => {
    setShowQuickResults(false);
  };

  // Handle vehicle selection from search results
  const handleVehicleSelect = (car: Car) => {
    // This will be handled by the QuickBookingVehicleSearch component
    // which will navigate directly to the booking flow
  };

  // Handle pickup garage checkbox change
  const handlePickupGarageChange = (checked: boolean) => {
    setPickupGarageChecked(checked);
    if (checked) {
      setSelectedPickupLocation("#9 Lubnac, Vintar, Ilocos Norte");
    } else {
      setSelectedPickupLocation("");
    }
  };

  // Handle drop-off same as pickup checkbox change
  const handleDropoffSameAsPickupChange = (checked: boolean) => {
    setDropoffSameAsPickupChecked(checked);
    if (checked && selectedPickupLocation) {
      setSelectedDropoffLocation(selectedPickupLocation);
    } else if (!checked) {
      setSelectedDropoffLocation("");
    }
  };

  // Update drop-off location when pickup location changes and "same as pickup" is checked
  React.useEffect(() => {
    if (dropoffSameAsPickupChecked && selectedPickupLocation) {
      setSelectedDropoffLocation(selectedPickupLocation);
    }
  }, [selectedPickupLocation, dropoffSameAsPickupChecked]);

  // Navigate to booking flow with selected parameters
  const navigateToBookingFlow = () => {
    // Validate all required fields
    const validation = validateBookingDateTime(
      selectedPickupDate,
      selectedPickupTime,
      selectedDropoffDate,
      selectedDropoffTime
    );

    if (!validation.isValid) {
      alert(validation.error);
      return;
    }

    if (
      selectedPickupLocation &&
      selectedPickupDate &&
      selectedPickupTime &&
      selectedDropoffDate &&
      selectedDropoffTime &&
      selectedDropoffLocation
    ) {
      const pickUpDateTime = `${selectedPickupDate}T${selectedPickupTime}`;
      const dropOffDateTime = `${selectedDropoffDate}T${selectedDropoffTime}`;

      const bookingUrl = buildBookingFlowUrl({
        pickUpLocation: selectedPickupLocation,
        dropOffLocation: selectedDropoffLocation,
        pickUpDateTime,
        dropOffDateTime,
        pickupGarageChecked,
        dropoffSameAsPickupChecked,
      });
      router.push(bookingUrl);
    }
  };

  return (
    <div className="space-y-0 bg-gray-50">
      {/* Hero Section - Full width background */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent"></div>
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
        </div>
        {/* Content Container - Constrained width */}
        <div className="xl:max-w-7xl xl:mx-auto">
          <div className="relative px-4 md:px-6 py-16 md:py-24 max-w-full">
            <div className="max-w-7xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div className="space-y-8 min-w-0">
                  <div className="space-y-4">
                    <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold leading-tight break-words">
                      {user ? (
                        <>
                          Welcome back,{" "}
                          <span className="text-yellow-400">
                            {profile?.full_name || user.email?.split("@")[0]}
                          </span>
                          !
                        </>
                      ) : (
                        <>
                          Your Journey Starts with{" "}
                          <span className="text-yellow-400">Ollie's</span>
                        </>
                      )}
                    </h1>
                    <p className="text-lg xs:text-xl sm:text-xl md:text-2xl text-blue-100 leading-relaxed break-words">
                      {user
                        ? "Ready for your next adventure? Browse our premium fleet and book your perfect ride."
                        : "Premium car rental experience in Ilocos. Browse our fleet, select your perfect vehicle, and complete your booking seamlessly."}
                    </p>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4 flex-wrap">
                    <Link
                      href="/customer/catalog"
                      className="min-w-0 hidden sm:block"
                    >
                      <Button
                        size="lg"
                        className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold px-8 py-4 text-lg w-full sm:w-auto min-h-[44px]"
                      >
                        <Calendar className="w-5 h-5 mr-2 flex-shrink-0" />
                        <span className="truncate">Book Now</span>
                      </Button>
                    </Link>
                    {user ? (
                      <Link href="/customer/dashboard" className="min-w-0">
                        <Button
                          size="lg"
                          variant="secondary-inverse"
                          className="px-8 py-4 text-lg w-full sm:w-auto min-h-[44px]"
                        >
                          <CarIcon className="w-5 h-5 mr-2 flex-shrink-0" />
                          <span className="truncate">My Dashboard</span>
                        </Button>
                      </Link>
                    ) : (
                      <Link href="/customer/contact" className="min-w-0">
                        <Button
                          size="lg"
                          variant="secondary-inverse"
                          className="px-8 py-4 text-lg w-full sm:w-auto min-h-[44px]"
                        >
                          <MessageCircle className="w-5 h-5 mr-2 flex-shrink-0" />
                          <span className="truncate">Chat with Us</span>
                        </Button>
                      </Link>
                    )}
                  </div>

                  <div className="grid grid-cols-3 gap-3 xs:gap-4 sm:gap-6 pt-6 sm:pt-8 border-t border-white/20">
                    <div className="text-center min-w-0">
                      <div className="text-xl xs:text-2xl sm:text-2xl md:text-3xl font-bold"></div>
                      <div className="text-xs xs:text-sm text-blue-200"></div>
                    </div>
                    <div className="text-center min-w-0">
                      <div className="text-xl xs:text-2xl sm:text-2xl md:text-3xl font-bold"></div>
                      <div className="text-xs xs:text-sm text-blue-200"></div>
                    </div>
                    <div className="text-center min-w-0">
                      <div className="text-xl xs:text-2xl sm:text-2xl md:text-3xl font-bold"></div>
                      <div className="text-xs xs:text-sm text-blue-200"></div>
                    </div>
                  </div>
                </div>

                <div className="relative min-w-0 overflow-hidden">
                  <CustomerImagesCarousel />
                </div>
              </div>
            </div>
          </div>
        </div>{" "}
        {/* Close hero content container */}
      </section>
      {/* Main Content Container - Desktop width constraint matching Quick Booking */}
      <div className="xl:max-w-7xl xl:mx-auto xl:bg-white xl:shadow-sm">
        {/* Quick Booking Section - Enhanced Mobile Responsiveness for All Breakpoints */}
        <section className="bg-white shadow-lg relative z-10 mx-1 xs:mx-2 sm:mx-4 md:mx-6 lg:mx-8 rounded-xl sm:rounded-2xl border overflow-hidden mt-4 xs:mt-6 sm:mt-8 md:mt-12">
          <div className="p-2 xs:p-3 sm:p-6 md:p-8 lg:p-10 max-w-full overflow-hidden">
            <div className="text-center mb-4 xs:mb-6 sm:mb-8">
              <h2 className="text-lg xs:text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-2 break-words">
                Quick Booking
              </h2>
              <p className="text-xs xs:text-sm sm:text-base text-gray-600 break-words max-w-2xl mx-auto px-2">
                Find and reserve your perfect vehicle in minutes
              </p>
            </div>

            {/* Booking Table Layout - Enhanced Responsive Container for All Breakpoints */}
            <div className="max-w-6xl mx-auto overflow-hidden">
              <div className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-xl sm:rounded-2xl p-2 xs:p-3 sm:p-6 md:p-8 border border-blue-100 shadow-sm max-w-full overflow-hidden">
                {/* Location Row - Enhanced Responsive Design for All Breakpoints */}
                <div className="mb-4 xs:mb-6 sm:mb-8">
                  <div className="text-center mb-3 xs:mb-4 sm:mb-6">
                    <div className="inline-flex items-center gap-2 sm:gap-3 bg-white px-3 xs:px-4 sm:px-6 py-2 sm:py-3 rounded-full shadow-sm border border-blue-100">
                      <MapPin className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 flex-shrink-0" />
                      <h3 className="text-sm xs:text-base sm:text-lg font-semibold text-gray-800">
                        Location Details
                      </h3>
                    </div>
                  </div>
                  <div className="space-y-3 xs:space-y-4 sm:space-y-6 md:grid md:grid-cols-2 md:gap-3 lg:gap-4 xl:gap-6 md:space-y-0 max-w-full">
                    <div className="bg-white rounded-lg sm:rounded-xl p-3 xs:p-4 sm:p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 hover:border-blue-200 min-w-0 overflow-hidden space-y-3">
                      <PickupLocationField
                        value={selectedPickupLocation}
                        onValueChange={setSelectedPickupLocation}
                        placeholder="Select pickup location"
                        showLabel={true}
                        className="w-full min-w-0"
                      />
                      <PickupGarageCheckbox
                        checked={pickupGarageChecked}
                        onCheckedChange={handlePickupGarageChange}
                        className="w-full"
                      />
                    </div>

                    <div className="bg-white rounded-lg sm:rounded-xl p-3 xs:p-4 sm:p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 hover:border-blue-200 min-w-0 overflow-hidden space-y-3">
                      <DropOffLocationField
                        value={selectedDropoffLocation}
                        onValueChange={setSelectedDropoffLocation}
                        placeholder="Select drop-off location"
                        showLabel={true}
                        className="w-full min-w-0"
                      />
                      <DropoffSameAsPickupCheckbox
                        checked={dropoffSameAsPickupChecked}
                        onCheckedChange={handleDropoffSameAsPickupChange}
                        pickupLocation={selectedPickupLocation}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>

                {/* Date and Time Row - Mobile-Responsive Rental Schedule */}
                <div className="mb-6 sm:mb-8">
                  <div className="text-center mb-4 sm:mb-6">
                    <div className="inline-flex items-center gap-2 sm:gap-3 bg-white px-4 sm:px-6 py-2 sm:py-3 rounded-full shadow-sm border border-blue-100">
                      <Calendar className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                      <h3 className="text-base sm:text-lg font-semibold text-gray-800">
                        Rental Schedule
                      </h3>
                    </div>
                  </div>

                  {/* Mobile-first responsive container with enhanced overflow handling */}
                  <div className="max-w-5xl mx-auto overflow-hidden">
                    <div className="bg-white rounded-xl sm:rounded-2xl shadow-md border border-gray-100 px-3 py-4 sm:px-6 sm:py-8 lg:px-8 lg:py-10 overflow-hidden">
                      {/* Enhanced responsive grid: 1 column on xs, 2 columns on sm, 4 columns on lg+ */}
                      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 lg:grid-cols-4 lg:gap-x-8 lg:gap-y-6 max-w-full">
                        {/* Pick-up Date - Enhanced mobile responsiveness */}
                        <div className="min-w-0 w-full">
                          <label className="block text-sm font-medium text-gray-700 mb-2 sm:mb-3 text-center">
                            Pick-up Date
                          </label>
                          <div className="min-h-[44px] w-full">
                            <DatePicker
                              value={selectedPickupDate}
                              onChange={setSelectedPickupDate}
                              placeholder="Select date"
                              minDate={getMinimumDate()}
                              showQuickActions={true}
                              aria-label="Pick-up date for day-based rental"
                              className="w-full h-12 min-w-0 overflow-hidden text-ellipsis text-sm sm:text-base"
                            />
                          </div>
                        </div>

                        {/* Pick-up Time - Enhanced mobile responsiveness */}
                        <div className="min-w-0 w-full">
                          <label className="block text-sm font-medium text-gray-700 mb-2 sm:mb-3 text-center">
                            Pick-up Time
                          </label>
                          <div className="min-h-[44px] w-full">
                            <TimePicker
                              value={selectedPickupTime}
                              onChange={setSelectedPickupTime}
                              placeholder="Select time"
                              minTime="06:00"
                              maxTime="22:00"
                              step={60}
                              showQuickActions={true}
                              showSecondaryFormat={true}
                              aria-label="Pick-up time for scheduling pickup service"
                              className="w-full h-12 min-w-0 overflow-hidden text-ellipsis text-sm sm:text-base"
                            />
                          </div>
                        </div>

                        {/* Drop-off Date - Enhanced mobile responsiveness */}
                        <div className="min-w-0 w-full">
                          <label className="block text-sm font-medium text-gray-700 mb-2 sm:mb-3 text-center">
                            Drop-off Date
                          </label>
                          <div className="min-h-[44px] w-full">
                            <DatePicker
                              value={selectedDropoffDate}
                              onChange={setSelectedDropoffDate}
                              placeholder="Select date"
                              minDate={getMinimumDropoffDate(selectedPickupDate)}
                              showQuickActions={true}
                              aria-label="Drop-off date for day-based rental"
                              className="w-full h-12 min-w-0 overflow-hidden text-ellipsis text-sm sm:text-base"
                            />
                          </div>
                        </div>

                        {/* Drop-off Time - Enhanced mobile responsiveness */}
                        <div className="min-w-0 w-full">
                          <label className="block text-sm font-medium text-gray-700 mb-2 sm:mb-3 text-center">
                            Drop-off Time
                          </label>
                          <div className="min-h-[44px] w-full">
                            <TimePicker
                              value={selectedDropoffTime}
                              onChange={setSelectedDropoffTime}
                              placeholder="Select time"
                              minTime="06:00"
                              maxTime="22:00"
                              step={60}
                              showQuickActions={true}
                              showSecondaryFormat={true}
                              aria-label="Drop-off time for scheduling return service"
                              className="w-full h-12 min-w-0 overflow-hidden text-ellipsis text-sm sm:text-base"
                            />
                          </div>
                        </div>

                        {/* Duration Display - Day-Only Rental Period */}
                        {selectedPickupDate && selectedDropoffDate && (
                            <div className="col-span-1 sm:col-span-2 lg:col-span-4 mt-3 sm:mt-4 lg:mt-6 text-center">
                              <div className="inline-flex items-center gap-2 bg-green-50 text-green-700 px-4 sm:px-6 py-2 sm:py-3 rounded-full border border-green-200 shadow-sm">
                                <Calendar className="h-4 w-4 flex-shrink-0" />
                                <span className="font-medium text-xs sm:text-sm lg:text-base">
                                  Rental Period:{" "}
                                  {calculateDuration(
                                    selectedPickupDate,
                                    selectedPickupTime,
                                    selectedDropoffDate,
                                    selectedDropoffTime
                                  )}
                                </span>
                              </div>
                              <p className="text-xs text-gray-500 mt-2">
                                Rentals are charged per day only. Times are for pickup/delivery scheduling.
                              </p>
                            </div>
                          )}

                        {/* Search Button - Enhanced mobile-responsive positioning */}
                        <div className="col-span-1 sm:col-span-2 lg:col-span-4 mt-4 sm:mt-6 lg:mt-8">
                          <div className="flex justify-center px-2 sm:px-0">
                            <Button
                              className="w-full max-w-sm sm:w-auto sm:min-w-[280px] lg:min-w-[400px] bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 sm:px-8 lg:px-12 py-3 sm:py-4 text-sm sm:text-base lg:text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 min-h-[44px]"
                              disabled={
                                !selectedPickupDate ||
                                !selectedDropoffDate ||
                                !selectedDropoffLocation
                              }
                              onClick={handleSearchClick}
                            >
                              <Search className="w-5 h-5 mr-3 flex-shrink-0" />
                              <span className="truncate">
                                {showQuickResults
                                  ? "Hide Available Vehicles"
                                  : "Search Available Vehicles"}
                              </span>
                            </Button>
                          </div>
                          {(!selectedPickupDate ||
                            !selectedDropoffDate ||
                            !selectedDropoffLocation) && (
                            <p className="text-xs sm:text-sm text-gray-500 mt-3 text-center px-4">
                              Please fill in all required fields to search
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Vehicle Search Results Section */}
                <QuickBookingVehicleSearch
                  searchQuery={searchQuery}
                  onSearchQueryChange={setSearchQuery}
                  pickupDate={selectedPickupDate}
                  dropoffDate={selectedDropoffDate}
                  pickupLocation={selectedPickupLocation}
                  dropoffLocation={selectedDropoffLocation}
                  pickupDateTime={
                    selectedPickupDate && selectedPickupTime
                      ? `${selectedPickupDate}T${selectedPickupTime}`
                      : ""
                  }
                  dropoffDateTime={
                    selectedDropoffDate && selectedDropoffTime
                      ? `${selectedDropoffDate}T${selectedDropoffTime}`
                      : ""
                  }
                  onVehicleSelect={handleVehicleSelect}
                  isVisible={showQuickResults}
                  onClose={handleCloseVehicleSearch}
                />
              </div>
            </div>
          </div>
        </section>

        {/* Features Section - Enhanced Mobile Responsiveness */}
        <section className="bg-gray-50 px-2 xs:px-4 md:px-6 py-12 xs:py-16">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-8 xs:mb-12">
              <h2 className="text-2xl xs:text-3xl md:text-4xl font-bold text-gray-900 mb-3 xs:mb-4">
                Why Choose Ollie's?
              </h2>
              <p className="text-lg xs:text-xl text-gray-600 max-w-3xl mx-auto px-2">
                Experience premium car rental service in Ilocos
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 xs:gap-6 md:gap-8 max-w-5xl mx-auto">
              <Card variant="elevated" className="text-center group">
                <CardContent className="pt-8 pb-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                    <Shield className="h-8 w-8 text-blue-600" />
                  </div>
                  <CardTitle className="mb-2">Safe & Secure</CardTitle>
                  <CardDescription className="leading-relaxed">
                    All vehicles undergo regular maintenance and safety
                    inspections.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card variant="elevated" className="text-center group">
                <CardContent className="pt-8 pb-6">
                  <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                    <Award className="h-8 w-8 text-yellow-600" />
                  </div>
                  <CardTitle className="mb-2">Premium Fleet</CardTitle>
                  <CardDescription className="leading-relaxed">
                    Well-maintained, modern vehicles from trusted brands.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card variant="elevated" className="text-center group">
                <CardContent className="pt-8 pb-6">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                    <Clock className="h-8 w-8 text-purple-600" />
                  </div>
                  <CardTitle className="mb-2">24/7 Support</CardTitle>
                  <CardDescription className="leading-relaxed">
                    Round-the-clock customer support via phone and email.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Browse by Category Section - Enhanced Mobile Responsiveness */}
        <section className="px-2 xs:px-4 md:px-6 py-12 xs:py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-8 xs:mb-12">
              <h2 className="text-2xl xs:text-3xl md:text-4xl font-bold text-gray-900 mb-3 xs:mb-4">
                Browse by Vehicle Type
              </h2>
              <p className="text-lg xs:text-xl text-gray-600 px-2">
                Find the perfect vehicle category for your needs
              </p>
            </div>

            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-10 xs:h-12 w-10 xs:w-12 border-b-2 border-blue-600 mx-auto mb-3 xs:mb-4"></div>
                <p className="text-sm xs:text-base text-gray-600">
                  Loading vehicle categories...
                </p>
              </div>
            ) : (
              <>
                {/* Van Rental with Driver - Special Category */}
                <div className="mb-8 xs:mb-12">
                  <div className="text-center mb-6 xs:mb-8">
                    <h3 className="text-xl xs:text-2xl font-bold text-gray-900 mb-2">
                      Special Service
                    </h3>
                    <p className="text-base xs:text-lg text-gray-600">
                      Professional driver included for your convenience
                    </p>
                  </div>
                  <div className="flex justify-center">
                    <div className="w-full max-w-sm">
                      <VanRentalCategoryCard />
                    </div>
                  </div>
                </div>

                {/* Regular Vehicle Categories */}
                <div className="mb-6 xs:mb-8">
                  <div className="text-center mb-6 xs:mb-8">
                    <h3 className="text-xl xs:text-2xl font-bold text-gray-900 mb-2">
                      Self-Drive Vehicles
                    </h3>
                    <p className="text-base xs:text-lg text-gray-600">
                      Choose from our fleet of well-maintained vehicles
                    </p>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 xs:gap-6">
                    {categories
                      .filter((category) => {
                        // Only show categories that have vehicles after filtering out vans
                        const categoryType = category.type || "";
                        const categoryVehicles = carsByCategory[categoryType] || [];
                        return categoryVehicles.length > 0;
                      })
                      .slice(0, 6)
                      .map((category) => (
                        <CategoryCard
                          key={category.type}
                          category={category}
                          cars={carsByCategory[category.type] || []}
                          onSelect={(categoryType) => {
                            // Navigate to catalog with selected category
                            window.location.href = `/customer/catalog?category=${categoryType}`;
                          }}
                        />
                      ))}
                  </div>
                </div>
              </>
            )}

            <div className="text-center px-2">
              <Link href="/customer/catalog">
                <Button
                  size="lg"
                  variant="secondary"
                  className="px-6 xs:px-8 min-h-[44px] w-full xs:w-auto"
                >
                  <span>View All Vehicles</span>
                  <ArrowRight className="w-4 h-4 ml-2 flex-shrink-0" />
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* FAQ Section - Enhanced Mobile Responsiveness */}
        <section className="bg-gray-50 px-2 xs:px-4 md:px-6 py-12 xs:py-16">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8 xs:mb-12">
              <h2 className="text-2xl xs:text-3xl md:text-4xl font-bold text-gray-900 mb-3 xs:mb-4">
                Quick Questions & Answers
              </h2>
              <p className="text-lg xs:text-xl text-gray-600 px-2">
                Common questions about our car rental service
              </p>
            </div>

            <Accordion
              type="single"
              collapsible
              className="space-y-3 xs:space-y-4"
            >
              <AccordionItem
                value="booking"
                className="bg-white rounded-lg border-none shadow-sm"
              >
                <AccordionTrigger className="px-3 xs:px-6 py-3 xs:py-4 hover:no-underline">
                  <span className="text-left font-medium text-sm xs:text-base">
                    How do I book a vehicle?
                  </span>
                </AccordionTrigger>
                <AccordionContent className="px-3 xs:px-6 pb-3 xs:pb-4 text-gray-600 text-sm xs:text-base">
                  Use our quick booking form above to search for available
                  vehicles, select your preferred car, and complete your
                  reservation through our streamlined booking process.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem
                value="payment"
                className="bg-white rounded-lg border-none shadow-sm"
              >
                <AccordionTrigger className="px-3 xs:px-6 py-3 xs:py-4 hover:no-underline">
                  <span className="text-left font-medium text-sm xs:text-base">
                    What payment methods do you accept?
                  </span>
                </AccordionTrigger>
                <AccordionContent className="px-3 xs:px-6 pb-3 xs:pb-4 text-gray-600 text-sm xs:text-base">
                  We accept cash, bank transfers, and digital payment methods.
                  Payment details will be provided during the booking process.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem
                value="requirements"
                className="bg-white rounded-lg border-none shadow-sm"
              >
                <AccordionTrigger className="px-3 xs:px-6 py-3 xs:py-4 hover:no-underline">
                  <span className="text-left font-medium text-sm xs:text-base">
                    What are the rental requirements?
                  </span>
                </AccordionTrigger>
                <AccordionContent className="px-3 xs:px-6 pb-3 xs:pb-4 text-gray-600 text-sm xs:text-base">
                  Valid driver's license, valid ID, and minimum age of 21 years.
                  Additional requirements may apply for certain vehicles.
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            <div className="text-center mt-6 xs:mt-8 px-2">
              <Link href="/customer/faq">
                <Button
                  variant="secondary"
                  className="min-h-[44px] w-full xs:w-auto"
                >
                  <span>View All FAQs</span>
                  <ArrowRight className="w-4 h-4 ml-2 flex-shrink-0" />
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </div>{" "}
      {/* Close main content container before Contact & CTA */}
      {/* Contact & CTA Section - Enhanced Mobile Responsiveness with Full Width Background */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white px-2 xs:px-4 md:px-6 py-12 xs:py-16">
        {/* Content Container - Constrained width */}
        <div className="xl:max-w-7xl xl:mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-2xl xs:text-3xl md:text-4xl font-bold mb-3 xs:mb-4">
              Ready to Book Your Ride?
            </h2>
            <p className="text-lg xs:text-xl text-blue-100 mb-6 xs:mb-8 max-w-2xl mx-auto px-2">
              Experience the best car rental service in Ilocos. Start your
              booking above or contact us directly.
            </p>

            <div className="grid grid-cols-1 xs:grid-cols-2 gap-6 xs:gap-6 md:gap-8 max-w-2xl mx-auto mb-6 xs:mb-8">
              <div className="flex flex-col xs:flex-row items-center justify-center gap-3">
                <Phone className="h-5 w-5 xs:h-6 xs:w-6 text-yellow-400 flex-shrink-0" />
                <div className="text-center xs:text-left">
                  <div className="font-semibold text-sm xs:text-base">
                    Phone
                  </div>
                  <div className="text-xs xs:text-sm text-blue-200">
                    +639998810866
                  </div>
                </div>
              </div>
              <div className="flex flex-col xs:flex-row items-center justify-center gap-3">
                <Mail className="h-5 w-5 xs:h-6 xs:w-6 text-yellow-400 flex-shrink-0" />
                <div className="text-center xs:text-left">
                  <div className="font-semibold text-sm xs:text-base">
                    Email
                  </div>
                  <div className="text-xs xs:text-sm text-blue-200">
                    <EMAIL>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col xs:flex-row gap-3 xs:gap-4 justify-center max-w-md mx-auto">
              <Link href="/customer/book" className="hidden sm:block">
                <Button className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold min-h-[44px] w-full xs:w-auto px-6 xs:px-8">
                  Book Now
                </Button>
              </Link>
              <Link href="/customer/contact">
                <Button
                  variant="secondary"
                  className="min-h-[44px] w-full xs:w-auto px-4 xs:px-6"
                >
                  Contact Us
                </Button>
              </Link>
            </div>
          </div>
        </div>{" "}
        {/* Close Contact & CTA content container */}
      </section>
      {/* Footer - Enhanced Mobile Responsiveness with Full Width Background */}
      <footer className="bg-gray-900 text-gray-300 px-2 xs:px-4 md:px-6 py-8 xs:py-12">
        {/* Content Container - Constrained width */}
        <div className="xl:max-w-7xl xl:mx-auto">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-4 gap-6 xs:gap-8 mb-6 xs:mb-8">
              <div className="space-y-3 xs:space-y-4 text-center xs:text-left">
                <div className="flex items-center justify-center xs:justify-start gap-2">
                  <CarIcon className="h-6 w-6 xs:h-8 xs:w-8 text-yellow-500 flex-shrink-0" />
                  <span className="text-lg xs:text-xl font-bold text-white">
                    Ollie's Rent A Car
                  </span>
                </div>
                <p className="text-gray-400 text-sm xs:text-base">
                  Premium car rental service in Ilocos, powered by Pathlink
                  technology for seamless booking experience.
                </p>
              </div>

              <div className="text-center xs:text-left">
                <h4 className="font-semibold text-white mb-3 xs:mb-4 text-sm xs:text-base">
                  Quick Links
                </h4>
                <div className="space-y-2 text-xs xs:text-sm">
                  <div>
                    <a
                      href="/customer/catalog"
                      className="hover:text-yellow-500 transition-colors"
                    >
                      Vehicle Catalog
                    </a>
                  </div>
                  <div>
                    <a
                      href="/customer/booking/flow"
                      className="hover:text-yellow-500 transition-colors"
                    >
                      Book Now
                    </a>
                  </div>
                  <div>
                    <a
                      href="/customer/faq"
                      className="hover:text-yellow-500 transition-colors"
                    >
                      FAQ
                    </a>
                  </div>
                  <div>
                    <a
                      href="/customer/contact"
                      className="hover:text-yellow-500 transition-colors"
                    >
                      Contact Us
                    </a>
                  </div>
                </div>
              </div>

              <div className="text-center xs:text-left">
                <h4 className="font-semibold text-white mb-3 xs:mb-4 text-sm xs:text-base">
                  Legal
                </h4>
                <div className="space-y-2 text-xs xs:text-sm">
                  <div>
                    <a
                      href="/customer/terms"
                      className="hover:text-yellow-500 transition-colors"
                    >
                      Terms & Conditions
                    </a>
                  </div>
                  {/* <div><a href="/privacy" className="hover:text-yellow-500 transition-colors">Privacy Policy</a></div> */}
                  {/* <div><a href="/cancellation" className="hover:text-yellow-500 transition-colors">Cancellation Policy</a></div> */}
                </div>
              </div>

              <div className="text-center xs:text-left">
                <h4 className="font-semibold text-white mb-3 xs:mb-4 text-sm xs:text-base">
                  Contact Info
                </h4>
                <div className="space-y-2 text-xs xs:text-sm text-gray-400">
                  <div className="flex items-center justify-center xs:justify-start gap-2">
                    <MapPin className="h-3 w-3 xs:h-4 xs:w-4 text-yellow-400 flex-shrink-0" />
                    <span>Ilocos Norte, Philippines</span>
                  </div>
                  <div className="flex items-center justify-center xs:justify-start gap-2">
                    <Phone className="h-3 w-3 xs:h-4 xs:w-4 text-yellow-400 flex-shrink-0" />
                    <span>+639998810866</span>
                  </div>
                  <div className="flex items-center justify-center xs:justify-start gap-2">
                    <Mail className="h-3 w-3 xs:h-4 xs:w-4 text-yellow-400 flex-shrink-0" />
                    <span className="break-all"><EMAIL></span>
                  </div>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-800 pt-6 xs:pt-8 flex flex-col xs:flex-row justify-between items-center gap-3 xs:gap-4">
              <div className="text-xs xs:text-sm text-gray-400 text-center xs:text-left">
                © 2025 Ollie's Rent A Car. All rights reserved.
              </div>
              <div className="flex items-center gap-2 text-xs xs:text-sm text-gray-400">
                <span>Powered by</span>
                <span className="font-semibold text-yellow-500">Pathlink</span>
              </div>
            </div>
          </div>
        </div>{" "}
        {/* Close footer content container */}
      </footer>
    </div>
  );
}
