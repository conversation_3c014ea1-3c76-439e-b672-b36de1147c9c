#!/usr/bin/env node
/**
 * Stop Phantom GPS Script
 * Identifies and stops sources of phantom GPS data when ESP32 is offline
 */
import mqtt from 'mqtt';

const BROKER_URL = 'mqtt://broker.emqx.io:1883';
const TARGET_TOPIC = 'pathlink/gps/lilygo-a7670e-01';

console.log('🛡️ Phantom GPS Hunter Started...');
console.log(`🔗 Broker: ${BROKER_URL}`);
console.log(`🎯 Target Topic: ${TARGET_TOPIC}`);
console.log('🚨 This script will identify phantom GPS sources and help stop them\n');

const client = mqtt.connect(BROKER_URL, {
  clientId: `phantom_hunter_${Math.random().toString(16).substr(2, 8)}`,
  clean: true,
  keepalive: 60,
  connectTimeout: 30000
});

let messageCount = 0;
let phantomSources = new Map();

client.on('connect', () => {
  console.log('✅ Connected to EMQ Public Broker');
  console.log('👂 Monitoring for phantom GPS data...\n');
  
  // Subscribe to the specific GPS topic
  client.subscribe(TARGET_TOPIC, { qos: 1 }, (error) => {
    if (error) {
      console.error(`❌ Failed to subscribe:`, error);
    } else {
      console.log(`✅ Subscribed to ${TARGET_TOPIC}`);
      console.log('🔍 Turn OFF your ESP32 device now and wait...');
      console.log('📊 Any messages received after ESP32 is off are PHANTOM sources!\n');
    }
  });
});

client.on('message', (topic, payload) => {
  messageCount++;
  const timestamp = new Date().toISOString();
  
  console.log(`\n🚨 PHANTOM GPS DETECTED! Message #${messageCount}`);
  console.log(`📡 Topic: ${topic}`);
  console.log(`🕐 Time: ${timestamp}`);
  
  try {
    const data = JSON.parse(payload.toString());
    console.log(`📦 Data:`, JSON.stringify(data, null, 2));
    
    // Analyze the phantom source
    const sourceFingerprint = {
      hasAccuracy: data.accuracy !== undefined,
      hasSatellites: data.satellites !== undefined,
      hasFixMode: data.fixMode !== undefined,
      deviceId: data.deviceId || 'UNKNOWN',
      timestampType: typeof data.timestamp,
      coordinates: `${data.lat},${data.lng}`
    };
    
    console.log(`🔍 Source Analysis:`, sourceFingerprint);
    
    // Track phantom sources
    const sourceKey = JSON.stringify(sourceFingerprint);
    const count = phantomSources.get(sourceKey) || 0;
    phantomSources.set(sourceKey, count + 1);
    
    // Identify the type of phantom source
    if (!data.accuracy && !data.satellites && !data.fixMode) {
      console.log(`💡 LIKELY SOURCE: Test script or template ESP32 firmware (missing GPS quality fields)`);
    } else {
      console.log(`💡 LIKELY SOURCE: Another real ESP32 device or retained message`);
    }
    
    // Provide specific fix instructions
    console.log(`\n🛠️  FIX INSTRUCTIONS:`);
    if (!data.accuracy && !data.satellites && !data.fixMode) {
      console.log(`   1. Check for running Node.js test scripts`);
      console.log(`   2. Look for ESP32 template firmware publishing to this topic`);
      console.log(`   3. Clear retained MQTT messages`);
    } else {
      console.log(`   1. Check if another ESP32 device is using the same device ID`);
      console.log(`   2. Verify only one ESP32 device should be publishing to this topic`);
      console.log(`   3. Check for duplicate firmware uploads`);
    }
    
  } catch (error) {
    console.log(`📦 Raw Data: ${payload.toString()}`);
    console.log(`❌ Parse Error: ${error.message}`);
  }
  
  console.log(`\n📊 PHANTOM SUMMARY SO FAR:`);
  console.log(`   Total phantom messages: ${messageCount}`);
  console.log(`   Unique phantom sources: ${phantomSources.size}`);
  phantomSources.forEach((count, source) => {
    console.log(`   - Source ${JSON.parse(source).deviceId}: ${count} messages`);
  });
  
  console.log(`================================`);
});

client.on('error', (error) => {
  console.error('❌ MQTT Connection Error:', error);
});

// Auto-stop after 2 minutes of monitoring
setTimeout(() => {
  console.log(`\n⏰ Monitoring complete after 2 minutes`);
  
  if (messageCount === 0) {
    console.log(`✅ SUCCESS! No phantom GPS data detected`);
    console.log(`   Your ESP32 is properly offline - no phantom sources found`);
  } else {
    console.log(`\n🚨 PHANTOM GPS SOURCES FOUND:`);
    console.log(`   Total messages: ${messageCount}`);
    console.log(`   Unique sources: ${phantomSources.size}`);
    console.log(`\n   These sources are causing GPS data when your ESP32 is OFF`);
    console.log(`   Review the fix instructions above for each source`);
  }
  
  client.end();
  process.exit(0);
}, 2 * 60 * 1000);

// Manual exit
process.on('SIGINT', () => {
  console.log(`\n🛑 Manual stop - Phantom hunting ended`);
  
  if (messageCount === 0) {
    console.log(`✅ No phantom GPS detected during monitoring period`);
  } else {
    console.log(`🚨 Found ${messageCount} phantom GPS messages from ${phantomSources.size} sources`);
  }
  
  client.end();
  process.exit(0);
});
