#!/usr/bin/env node
/**
 * Force ESP32 Disconnect <PERSON>ript
 * Sends a disconnect command to ESP32 device via MQTT
 */
import mqtt from 'mqtt';

const BROKER_URL = 'mqtt://broker.emqx.io:1883';
const DEVICE_ID = 'lilygo-a7670e-01';
const COMMAND_TOPIC = `pathlink/commands/${DEVICE_ID}`;

console.log('🛑 ESP32 Force Disconnect Script');
console.log(`🔗 Broker: ${BROKER_URL}`);
console.log(`🎯 Target Device: ${DEVICE_ID}`);
console.log('📤 Sending disconnect command...\n');

const client = mqtt.connect(BROKER_URL, {
  clientId: `force_disconnect_${Math.random().toString(16).substr(2, 8)}`,
  clean: true,
  keepalive: 60,
  connectTimeout: 30000
});

client.on('connect', () => {
  console.log('✅ Connected to MQTT broker');
  
  // Send multiple disconnect commands
  const commands = [
    { action: 'disconnect', reason: 'manual_shutdown' },
    { action: 'sleep', duration: 3600 }, // 1 hour sleep
    { action: 'reset', type: 'soft' },
    { action: 'power_off', source: 'remote' }
  ];
  
  let commandIndex = 0;
  const sendNextCommand = () => {
    if (commandIndex < commands.length) {
      const command = commands[commandIndex];
      const payload = JSON.stringify(command);
      
      console.log(`📤 Sending command ${commandIndex + 1}/${commands.length}:`, command);
      
      client.publish(COMMAND_TOPIC, payload, { qos: 1, retain: true }, (error) => {
        if (error) {
          console.error('❌ Failed to send command:', error);
        } else {
          console.log(`✅ Command sent successfully`);
        }
        
        commandIndex++;
        setTimeout(sendNextCommand, 1000); // Wait 1 second between commands
      });
    } else {
      console.log('\n🎯 All disconnect commands sent!');
      console.log('⏰ Wait 30 seconds to see if ESP32 stops sending data...');
      
      setTimeout(() => {
        client.end();
        console.log('\n✅ Force disconnect complete');
        console.log('🔍 Run the phantom hunter again to verify ESP32 is offline');
        process.exit(0);
      }, 30000);
    }
  };
  
  sendNextCommand();
});

client.on('error', (error) => {
  console.error('❌ MQTT Connection Error:', error);
  process.exit(1);
});

// Manual exit
process.on('SIGINT', () => {
  console.log('\n🛑 Force disconnect interrupted');
  client.end();
  process.exit(0);
});
