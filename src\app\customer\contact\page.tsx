"use client";

import { PublicAppShell } from "@/components/layout/public-app-shell";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { EnhancedInput } from "@/components/ui/enhanced-input";
import { EnhancedTextarea } from "@/components/ui/enhanced-textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useFormValidation } from "@/hooks/use-form-validation";
import {
  Phone,
  Mail,
  MessageCircle,
  MapPin,
  Clock,
  Heart,
  Send,
  Headphones,
  ShoppingCart,
  HelpCircle,
  Facebook,
  Smartphone,
  Globe,
  Zap,
  User,
  AlertCircle,
} from "lucide-react";
import { cn } from "@/lib/utils";
import * as React from "react";

export default function ContactPage() {
  const { values, errors, isValid, getFieldProps, validateAll, reset } =
    useFormValidation({
      initialValues: {
        name: "",
        email: "",
        phone: "",
        subject: "",
        category: "",
        message: "",
      },
      validationRules: {
        name: {
          required: "Please enter your full name",
          minLength: {
            value: 2,
            message: "Name must be at least 2 characters",
          },
        },
        email: {
          required: "Email address is required",
          email: "Please enter a valid email address",
        },
        phone: {
          phone: "Please enter a valid Philippine phone number (+63 format)",
        },
        subject: {
          required: "Please enter a subject",
          minLength: {
            value: 5,
            message: "Subject must be at least 5 characters",
          },
        },
        category: {
          required: "Please select a category",
        },
        message: {
          required: "Please enter your message",
          minLength: {
            value: 10,
            message: "Message must be at least 10 characters",
          },
          maxLength: {
            value: 1000,
            message: "Message cannot exceed 1000 characters",
          },
        },
      },
    });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateAll()) {
      // Handle form submission here
      console.log("Contact form submitted:", values);
      alert("Thanks for reaching out! We'll get back to you soon. 😊");
      reset();
    }
  };

  const handleCategoryChange = (value: string) => {
    getFieldProps("category").onChange({ target: { value } } as any);
  };

  return (
    <PublicAppShell>
      <div className="space-y-6 max-w-full overflow-x-hidden">
        {/* Hero Section with Personality */}
        <Card className="overflow-hidden">
          <CardHeader className="text-center pb-8">
            <div className="flex justify-center mb-4">
              <div className="bg-blue-100 p-4 rounded-full">
                <Heart className="h-8 w-8 text-blue-600" />
              </div>
            </div>
            <CardTitle className="text-3xl font-bold text-gray-900 mb-4">
              Let's Chat! We're Here to Help 👋
            </CardTitle>
            <div className="max-w-2xl mx-auto space-y-3 text-gray-600">
              <p className="text-lg">
                Got questions about renting a car? Need help with your booking?
                Or just want to say hi? We'd love to hear from you!
              </p>
              <p className="text-base">
                At Ollie's, we believe great customer service starts with great
                conversations. Whether you're planning your first rental or
                you're one of our regulars, we're always excited to help make
                your journey smooth and memorable.
              </p>
            </div>
          </CardHeader>
        </Card>

        {/* Contact Methods - Segmented by Purpose */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Quick Questions & Bookings */}
          <Card className="border-green-200 bg-green-50/30">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3 mb-2">
                <div className="bg-green-500 text-white p-2 rounded-full">
                  <ShoppingCart className="h-5 w-5" />
                </div>
                <CardTitle className="text-lg text-green-900">
                  Quick Bookings
                </CardTitle>
              </div>
              <p className="text-sm text-green-700">
                Ready to book? Have a quick question? These are your fastest
                options!
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Phone Numbers */}
              <div className="space-y-3">
                <h4 className="font-semibold text-green-900 text-sm">
                  📞 Call or Text Us
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2 p-2 bg-white rounded border border-green-200">
                    <Smartphone className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-800">Smart:</span>
                    <a
                      href="tel:+639998810866"
                      className="text-green-700 hover:text-green-900 font-medium"
                    >
                      +639998810866
                    </a>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-white rounded border border-green-200">
                    <Smartphone className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-800">Smart:</span>
                    <a
                      href="tel:+639288110856"
                      className="text-green-700 hover:text-green-900 font-medium"
                    >
                      +639288110856
                    </a>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-white rounded border border-green-200">
                    <Globe className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-800">Globe:</span>
                    <a
                      href="tel:+639457342963"
                      className="text-green-700 hover:text-green-900 font-medium"
                    >
                      +639457342963
                    </a>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-white rounded border border-green-200">
                    <Zap className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-800">Dito:</span>
                    <a
                      href="tel:+639936448376"
                      className="text-green-700 hover:text-green-900 font-medium"
                    >
                      +639936448376
                    </a>
                  </div>
                </div>
                <p className="text-xs text-green-600 bg-white p-2 rounded border border-green-200">
                  💡 <span className="font-medium">Pro tip:</span> We usually
                  pick up faster on Smart numbers!
                </p>
              </div>

              {/* Facebook Messenger */}
              <div className="space-y-2">
                <h4 className="font-semibold text-green-900 text-sm">
                  💬 Message Us
                </h4>
                <a
                  href="https://www.facebook.com/olliescars001/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 p-3 bg-white rounded border border-green-200 hover:bg-green-50 transition-colors"
                >
                  <Facebook className="h-5 w-5 text-blue-600" />
                  <div className="flex-1">
                    <p className="font-medium text-green-900 text-sm">
                      Facebook Messenger
                    </p>
                    <p className="text-xs text-green-700">
                      We reply super fast here! 🚀
                    </p>
                  </div>
                </a>
              </div>
            </CardContent>
          </Card>

          {/* Support & Help */}
          <Card className="border-blue-200 bg-blue-50/30">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3 mb-2">
                <div className="bg-blue-500 text-white p-2 rounded-full">
                  <Headphones className="h-5 w-5" />
                </div>
                <CardTitle className="text-lg text-blue-900">
                  Need Support?
                </CardTitle>
              </div>
              <p className="text-sm text-blue-700">
                Having issues with your rental? We've got your back!
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Email Support */}
              <div className="space-y-2">
                <h4 className="font-semibold text-blue-900 text-sm">
                  📧 Email Us
                </h4>
                <a
                  href="mailto:<EMAIL>"
                  className="flex items-center gap-2 p-3 bg-white rounded border border-blue-200 hover:bg-blue-50 transition-colors"
                >
                  <Mail className="h-5 w-5 text-blue-600" />
                  <div className="flex-1">
                    <p className="font-medium text-blue-900 text-sm">
                      <EMAIL>
                    </p>
                    <p className="text-xs text-blue-700">
                      Perfect for detailed questions
                    </p>
                  </div>
                </a>
              </div>

              {/* Phone Support */}
              <div className="space-y-2">
                <h4 className="font-semibold text-blue-900 text-sm">
                  📞 Call for Support
                </h4>
                <div className="bg-white p-3 rounded border border-blue-200">
                  <p className="text-sm text-blue-800 mb-2">
                    Use any of our phone numbers above - we handle both bookings
                    and support on the same lines!
                  </p>
                  <p className="text-xs text-blue-600">
                    ⏰ <span className="font-medium">Best times to call:</span>{" "}
                    8AM - 6PM for fastest response
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* General Inquiries */}
          <Card className="border-purple-200 bg-purple-50/30">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3 mb-2">
                <div className="bg-purple-500 text-white p-2 rounded-full">
                  <HelpCircle className="h-5 w-5" />
                </div>
                <CardTitle className="text-lg text-purple-900">
                  Just Curious?
                </CardTitle>
              </div>
              <p className="text-sm text-purple-700">
                Want to learn more about our services? Have a special request?
                Let's talk!
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Contact Form Teaser */}
              <div className="bg-white p-3 rounded border border-purple-200">
                <h4 className="font-semibold text-purple-900 text-sm mb-2">
                  📝 Send us a message
                </h4>
                <p className="text-sm text-purple-700 mb-3">
                  Use the form below to tell us what's on your mind. We love
                  hearing from our customers!
                </p>
                <Button
                  onClick={() =>
                    document
                      .getElementById("contact-form")
                      ?.scrollIntoView({ behavior: "smooth" })
                  }
                  variant="secondary"
                  size="sm"
                  className="w-full border-purple-300 text-purple-700 hover:bg-purple-50"
                >
                  Jump to Contact Form
                </Button>
              </div>

              {/* Visit Us */}
              <div className="space-y-2">
                <h4 className="font-semibold text-purple-900 text-sm">
                  🏢 Visit Our Offices
                </h4>
                <div className="space-y-2 text-xs">
                  <div className="bg-white p-2 rounded border border-purple-200">
                    <p className="font-medium text-purple-900">Main Office</p>
                    <p className="text-purple-700">
                      Brgy 9 Lubnac, Vintar, Ilocos Norte
                    </p>
                  </div>
                  <div className="bg-white p-2 rounded border border-purple-200">
                    <p className="font-medium text-purple-900">Laoag Garage</p>
                    <p className="text-purple-700">
                      Brgy 59A Dibua South, Laoag City
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contact Form Section - Centered Layout */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-xl mx-auto px-4">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Get in touch
              </h2>
              <p className="text-gray-600">
                We'd love to hear from you. Please fill out this form.
              </p>
            </div>

            <Card id="contact-form" className="border-0 shadow-lg">
              <CardContent className="p-8">
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Name Row */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">
                        First name <span className="text-red-500">*</span>
                      </Label>
                      <EnhancedInput
                        placeholder="First name"
                        size="md"
                        required
                        {...getFieldProps("name")}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">
                        Last name <span className="text-red-500">*</span>
                      </Label>
                      <EnhancedInput
                        placeholder="Last name"
                        size="md"
                        required
                      />
                    </div>
                  </div>

                  {/* Email */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">
                      Email <span className="text-red-500">*</span>
                    </Label>
                    <EnhancedInput
                      type="email"
                      placeholder="<EMAIL>"
                      size="md"
                      required
                      {...getFieldProps("email")}
                    />
                  </div>

                  {/* Phone and Subject Row */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">
                        Phone number
                      </Label>
                      <EnhancedInput
                        type="tel"
                        placeholder="+****************"
                        size="md"
                        {...getFieldProps("phone")}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">
                        Subject <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={values.category}
                        onValueChange={handleCategoryChange}
                      >
                        <SelectTrigger
                          className={cn(
                            "h-10 transition-colors",
                            errors.category &&
                              "border-destructive focus:border-destructive"
                          )}
                        >
                          <SelectValue placeholder="Please select" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="booking">New Booking</SelectItem>
                          <SelectItem value="existing">
                            Existing Booking
                          </SelectItem>
                          <SelectItem value="pricing">
                            Pricing & Rates
                          </SelectItem>
                          <SelectItem value="vehicles">
                            Vehicle Information
                          </SelectItem>
                          <SelectItem value="support">
                            Technical Support
                          </SelectItem>
                          <SelectItem value="feedback">Feedback</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.category && (
                        <p className="text-xs text-destructive flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors.category}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Message */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">
                      Message <span className="text-red-500">*</span>
                    </Label>
                    <EnhancedTextarea
                      placeholder="Leave us a message..."
                      required
                      showCharCount
                      maxLength={500}
                      autoResize
                      className="min-h-[120px]"
                      {...getFieldProps("message")}
                    />
                  </div>

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    disabled={!isValid}
                    className={cn(
                      "w-full h-12 text-sm font-medium transition-all rounded-md",
                      isValid
                        ? "bg-green-600 hover:bg-green-700 text-white"
                        : "bg-gray-300 text-gray-500 cursor-not-allowed"
                    )}
                  >
                    Send message
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Fun Footer */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="text-center py-8">
            <div className="max-w-2xl mx-auto">
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                Thanks for choosing Ollie's! 🎉
              </h3>
              <p className="text-gray-600 mb-4">
                We're not just a car rental company - we're your travel
                partners. Every question you ask helps us serve you better, so
                never hesitate to reach out!
              </p>
              <div className="flex justify-center items-center gap-2 text-sm text-gray-500">
                <Clock className="h-4 w-4" />
                <span>
                  We typically respond within 1-2 hours during business hours
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </PublicAppShell>
  );
}
