"use client";

import * as React from "react";
import { DesktopAuthPage } from "@/components/customer-side/auth/desktop-auth-page";
import { MobileAuthPage } from "@/components/customer-side/auth/mobile-auth-page";
import { useIsMobile } from "@/hooks/use-mobile";

export default function CustomerLoginPage() {
  const isMobile = useIsMobile();

  // Use mobile layout for mobile devices, desktop layout for others
  if (isMobile) {
    return <MobileAuthPage mode="login" />;
  }

  return <DesktopAuthPage mode="login" />;
}
