"use client";

import * as React from "react";
import {
  getRenterProfileWithIssues,
  getIssueCategories,
  setRenterStatus,
  addRenterCategoryTag,
  removeRenterCategoryTag,
  createRenterIssue,
  updateRenterIssue,
  deleteRenterIssue,
} from "@/lib/supabase/renter-issues";
import type {
  RenterStatus,
  IssueCategory,
  RenterIssue,
  RenterCategoryTag,
  RenterBehaviorSummary,
  IssueSeverity,
} from "@/lib/types";

interface RenterIssueState {
  status: RenterStatus | null;
  categoryTags: RenterCategoryTag[];
  issues: RenterIssue[];
  behaviorSummary: RenterBehaviorSummary | null;
  availableCategories: IssueCategory[];
  loading: boolean;
  error: string | null;
}

interface RenterIssueActions {
  refreshData: () => Promise<void>;
  updateStatus: (customerId: string, statusTagline: string) => Promise<void>;
  addCategoryTag: (customerId: string, categoryId: string) => Promise<void>;
  removeCategoryTag: (customerId: string, categoryId: string) => Promise<void>;
  addIssue: (issue: {
    customerId: string;
    categoryId: string;
    bookingId?: string;
    description: string;
    severity: IssueSeverity;
  }) => Promise<void>;
  updateIssue: (
    issueId: string,
    updates: Partial<RenterIssue>
  ) => Promise<void>;
  removeIssue: (issueId: string) => Promise<void>;
}

interface RenterIssueContextValue
  extends RenterIssueState,
    RenterIssueActions {}

const RenterIssueContext = React.createContext<RenterIssueContextValue | null>(
  null
);

interface RenterIssueProviderProps {
  children: React.ReactNode;
  customerId: string;
  currentAdminId: string;
}

export function RenterIssueProvider({
  children,
  customerId,
  currentAdminId,
}: RenterIssueProviderProps) {
  const [state, setState] = React.useState<RenterIssueState>({
    status: null,
    categoryTags: [],
    issues: [],
    behaviorSummary: null,
    availableCategories: [],
    loading: true,
    error: null,
  });

  const refreshData = React.useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      const [renterData, categories] = await Promise.all([
        getRenterProfileWithIssues(customerId),
        getIssueCategories(),
      ]);

      setState((prev) => ({
        ...prev,
        status: renterData.status,
        categoryTags: renterData.category_tags || [],
        issues: renterData.recent_issues || [],
        behaviorSummary: renterData.behavior_summary || null,
        availableCategories: categories,
        loading: false,
      }));
    } catch (error) {
      console.error("Error fetching renter data:", error);
      setState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to load data",
        loading: false,
      }));
    }
  }, [customerId]);

  const updateStatus = React.useCallback(
    async (customerId: string, statusTagline: string) => {
      try {
        const updatedStatus = await setRenterStatus(
          customerId,
          statusTagline,
          currentAdminId
        );
        setState((prev) => ({
          ...prev,
          status: updatedStatus,
        }));
      } catch (error) {
        console.error("Error updating status:", error);
        throw error;
      }
    },
    [currentAdminId]
  );

  const addCategoryTag = React.useCallback(
    async (customerId: string, categoryId: string) => {
      try {
        const newTag = await addRenterCategoryTag(
          customerId,
          categoryId,
          currentAdminId
        );
        setState((prev) => ({
          ...prev,
          categoryTags: [...prev.categoryTags, newTag],
        }));
        // Refresh behavior summary after adding tag
        await refreshData();
      } catch (error) {
        console.error("Error adding category tag:", error);
        throw error;
      }
    },
    [currentAdminId, refreshData]
  );

  const removeCategoryTag = React.useCallback(
    async (customerId: string, categoryId: string) => {
      try {
        await removeRenterCategoryTag(customerId, categoryId);
        setState((prev) => ({
          ...prev,
          categoryTags: prev.categoryTags.filter(
            (tag) => tag.category_id !== categoryId
          ),
        }));
        // Refresh behavior summary after removing tag
        await refreshData();
      } catch (error) {
        console.error("Error removing category tag:", error);
        throw error;
      }
    },
    [refreshData]
  );

  const addIssue = React.useCallback(
    async (issue: {
      customerId: string;
      categoryId: string;
      bookingId?: string;
      description: string;
      severity: IssueSeverity;
    }) => {
      try {
        const newIssue = await createRenterIssue({
          ...issue,
          adminId: currentAdminId,
        });
        setState((prev) => ({
          ...prev,
          issues: [newIssue, ...prev.issues],
        }));
        // Refresh behavior summary after adding issue
        await refreshData();
      } catch (error) {
        console.error("Error adding issue:", error);
        throw error;
      }
    },
    [currentAdminId, refreshData]
  );

  const updateIssue = React.useCallback(
    async (issueId: string, updates: Partial<RenterIssue>) => {
      try {
        const updatedIssue = await updateRenterIssue(issueId, updates);
        setState((prev) => ({
          ...prev,
          issues: prev.issues.map((issue) =>
            issue.id === issueId ? updatedIssue : issue
          ),
        }));
        // Refresh behavior summary after updating issue
        await refreshData();
      } catch (error) {
        console.error("Error updating issue:", error);
        throw error;
      }
    },
    [refreshData]
  );

  const removeIssue = React.useCallback(
    async (issueId: string) => {
      try {
        await deleteRenterIssue(issueId);
        setState((prev) => ({
          ...prev,
          issues: prev.issues.filter((issue) => issue.id !== issueId),
        }));
        // Refresh behavior summary after removing issue
        await refreshData();
      } catch (error) {
        console.error("Error removing issue:", error);
        throw error;
      }
    },
    [refreshData]
  );

  // Initial data load
  React.useEffect(() => {
    refreshData();
  }, [refreshData]);

  const contextValue: RenterIssueContextValue = {
    ...state,
    refreshData,
    updateStatus,
    addCategoryTag,
    removeCategoryTag,
    addIssue,
    updateIssue,
    removeIssue,
  };

  return (
    <RenterIssueContext.Provider value={contextValue}>
      {children}
    </RenterIssueContext.Provider>
  );
}

export function useRenterIssues() {
  const context = React.useContext(RenterIssueContext);
  if (!context) {
    throw new Error(
      "useRenterIssues must be used within a RenterIssueProvider"
    );
  }
  return context;
}

// Hook for use without provider (standalone usage)
export function useRenterIssueData(customerId: string, currentAdminId: string) {
  const [state, setState] = React.useState<RenterIssueState>({
    status: null,
    categoryTags: [],
    issues: [],
    behaviorSummary: null,
    availableCategories: [],
    loading: true,
    error: null,
  });

  const refreshData = React.useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      const [renterData, categories] = await Promise.all([
        getRenterProfileWithIssues(customerId),
        getIssueCategories(),
      ]);

      setState((prev) => ({
        ...prev,
        status: renterData.status,
        categoryTags: renterData.category_tags || [],
        issues: renterData.recent_issues || [],
        behaviorSummary: renterData.behavior_summary || null,
        availableCategories: categories,
        loading: false,
      }));
    } catch (error) {
      console.error("Error fetching renter data:", error);
      setState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to load data",
        loading: false,
      }));
    }
  }, [customerId]);

  const actions: RenterIssueActions = {
    refreshData,
    updateStatus: async (customerId: string, statusTagline: string) => {
      const updatedStatus = await setRenterStatus(
        customerId,
        statusTagline,
        currentAdminId
      );
      setState((prev) => ({ ...prev, status: updatedStatus }));
    },
    addCategoryTag: async (customerId: string, categoryId: string) => {
      const newTag = await addRenterCategoryTag(
        customerId,
        categoryId,
        currentAdminId
      );
      setState((prev) => ({
        ...prev,
        categoryTags: [...prev.categoryTags, newTag],
      }));
      await refreshData();
    },
    removeCategoryTag: async (customerId: string, categoryId: string) => {
      await removeRenterCategoryTag(customerId, categoryId);
      setState((prev) => ({
        ...prev,
        categoryTags: prev.categoryTags.filter(
          (tag) => tag.category_id !== categoryId
        ),
      }));
      await refreshData();
    },
    addIssue: async (issue) => {
      const newIssue = await createRenterIssue({
        ...issue,
        adminId: currentAdminId,
      });
      setState((prev) => ({ ...prev, issues: [newIssue, ...prev.issues] }));
      await refreshData();
    },
    updateIssue: async (issueId: string, updates: Partial<RenterIssue>) => {
      const updatedIssue = await updateRenterIssue(issueId, updates);
      setState((prev) => ({
        ...prev,
        issues: prev.issues.map((issue) =>
          issue.id === issueId ? updatedIssue : issue
        ),
      }));
      await refreshData();
    },
    removeIssue: async (issueId: string) => {
      await deleteRenterIssue(issueId);
      setState((prev) => ({
        ...prev,
        issues: prev.issues.filter((issue) => issue.id !== issueId),
      }));
      await refreshData();
    },
  };

  React.useEffect(() => {
    refreshData();
  }, [refreshData]);

  return { ...state, ...actions };
}
