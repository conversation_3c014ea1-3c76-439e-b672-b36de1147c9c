"use client"

import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { getBookingStatusDisplay, BOOKING_STATUS_STYLES } from "@/lib/utils/booking-status"
import type { BookingStatus } from "@/lib/types"

interface StatusBadgeProps {
  status: BookingStatus
  className?: string
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
  const statusStyle = BOOKING_STATUS_STYLES[status];
  const displayLabel = getBookingStatusDisplay(status);

  return (
    <Badge
      variant="outline"
      className={cn(
        "px-3 py-1.5 text-sm font-bold rounded-lg border transition-all duration-200 hover:scale-105 hover:shadow-lg",
        statusStyle.className,
        className
      )}
    >
      <span className="mr-1">{statusStyle.icon}</span>
      {displayLabel}
    </Badge>
  )
}

interface PaymentBadgeProps {
  status: "Paid" | "Unpaid" | "Partial" | "Refunded"
  className?: string
}

export function PaymentBadge({ status, className }: PaymentBadgeProps) {
  const variants = {
    Paid: "bg-gradient-to-r from-green-100 to-emerald-100 text-green-900 border-2 border-green-400 hover:from-green-200 hover:to-emerald-200 shadow-md",
    Unpaid: "bg-gradient-to-r from-gray-100 to-slate-100 text-gray-900 border-2 border-gray-400 hover:from-gray-200 hover:to-slate-200 shadow-md",
    Partial: "bg-gradient-to-r from-violet-100 to-purple-100 text-violet-900 border-2 border-violet-400 hover:from-violet-200 hover:to-purple-200 shadow-md",
    Refunded: "bg-gradient-to-r from-teal-100 to-cyan-100 text-teal-900 border-2 border-teal-400 hover:from-teal-200 hover:to-cyan-200 shadow-md",
  }

  const icons = {
    Paid: "💳",
    Unpaid: "❗",
    Partial: "⚡",
    Refunded: "💰",
  }

  return (
    <Badge
      variant="outline"
      className={cn(
        "px-3 py-1.5 text-sm font-bold rounded-lg border transition-all duration-200 hover:scale-105 hover:shadow-lg",
        variants[status],
        className
      )}
    >
      <span className="mr-1">{icons[status]}</span>
      {status}
    </Badge>
  )
}
