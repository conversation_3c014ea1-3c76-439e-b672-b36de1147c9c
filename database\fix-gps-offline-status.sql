-- ============================================
-- FIX GPS OFFLINE STATUS DETECTION
-- ============================================
-- This migration updates the get_latest_gps_per_car function to properly
-- handle offline status detection based on data age

-- Function to get latest GPS location per car with active device mappings
-- Now includes automatic offline status detection for stale data
CREATE OR REPLACE FUNCTION get_latest_gps_per_car()
RETURNS TABLE (
  id uuid,
  car_id uuid,
  latitude double precision,
  longitude double precision,
  speed double precision,
  heading double precision,
  status text,
  "timestamp" timestamptz,
  driver_id uuid,
  created_at timestamptz,
  car jsonb
) 
LANGUAGE sql
SECURITY DEFINER
AS $$
  WITH latest_per_car AS (
    SELECT DISTINCT ON (gl.car_id) 
      gl.id,
      gl.car_id,
      gl.latitude,
      gl.longitude,
      gl.speed,
      gl.heading,
      CASE 
        -- If GPS data is older than 2 minutes (120 seconds), mark as offline
        WHEN EXTRACT(EPOCH FROM (NOW() - gl."timestamp")) > 120 THEN 'offline'
        -- If explicitly set as offline in database, keep offline
        WHEN gl.status = 'offline' THEN 'offline'
        -- Otherwise use the original status
        ELSE gl.status
      END as status,
      gl."timestamp",
      gl.driver_id,
      gl.created_at,
      jsonb_build_object(
        'model', c.model,
        'plate_number', c.plate_number,
        'data_age_seconds', EXTRACT(EPOCH FROM (NOW() - gl."timestamp"))
      ) as car_info
    FROM gps_locations gl
    INNER JOIN cars c ON gl.car_id = c.id
    INNER JOIN gps_device_mapping gdm ON c.id = gdm.car_id
    WHERE gdm.is_active = true
    ORDER BY gl.car_id, gl."timestamp" DESC
  )
  SELECT 
    lpc.id,
    lpc.car_id,
    lpc.latitude,
    lpc.longitude,
    lpc.speed,
    lpc.heading,
    lpc.status,
    lpc."timestamp",
    lpc.driver_id,
    lpc.created_at,
    lpc.car_info as car
  FROM latest_per_car lpc;
$$;

-- Log the migration
SELECT 'GPS offline status detection migration completed' as status;
