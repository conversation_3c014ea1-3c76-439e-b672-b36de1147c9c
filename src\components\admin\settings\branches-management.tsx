import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/hooks/use-toast"
import { BranchModal } from "./branch-modal"
import {
  MapPin,
  Plus,
  Edit,
  Trash2,
  Search,
  Phone,
  Clock,
  Car,
  AlertCircle,
  CheckCircle,
  XCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import type { Branch, BusinessHours } from "@/lib/types"
import {
  createBranch,
  updateBranch,
  deleteBranch
} from "../../../app/admin/settings/actions/settings-actions"

interface BranchesManagementProps {
  branches: Branch[]
  onUpdate: (branches: Branch[]) => void
}

export function BranchesManagement({ branches, onUpdate }: BranchesManagementProps) {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = React.useState("")
  const [selectedBranch, setSelectedBranch] = React.useState<Branch | null>(null)
  const [isModalOpen, setIsModalOpen] = React.useState(false)
  const [saving, setSaving] = React.useState(false)
  const [deleting, setDeleting] = React.useState<{ [id: string]: boolean }>({})

  const filteredBranches = React.useMemo(() => {
    if (!searchTerm) return branches
    const search = searchTerm.toLowerCase()
    return branches.filter(branch =>
      branch.name.toLowerCase().includes(search) ||
      branch.city.toLowerCase().includes(search) ||
      branch.province.toLowerCase().includes(search) ||
      branch.address.toLowerCase().includes(search)
    )
  }, [branches, searchTerm])

  const handleCreateBranch = async (branchData: Omit<Branch, 'id' | 'created_at' | 'updated_at'>) => {
    setSaving(true)
    try {
      const result = await createBranch(branchData)
      if (result.success && result.data) {
        onUpdate([...branches, result.data])
        toast({
          title: "Branch created",
          description: `${branchData.name} has been created successfully.`
        })
      } else {
        throw new Error(result.error || "Failed to create branch")
      }
    } catch (error) {
      console.error("Failed to create branch:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: (error as Error).message || "Failed to create branch"
      })
    } finally {
      setSaving(false)
    }
  }

  const handleUpdateBranch = async (id: string, updates: Partial<Branch>) => {
    setSaving(true)
    try {
      const result = await updateBranch(id, updates)
      if (result.success) {
        const updatedBranches = branches.map(branch =>
          branch.id === id ? { ...branch, ...updates } : branch
        )
        onUpdate(updatedBranches)
        toast({
          title: "Branch updated",
          description: "Branch has been updated successfully."
        })
      } else {
        throw new Error(result.error || "Failed to update branch")
      }
    } catch (error) {
      console.error("Failed to update branch:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: (error as Error).message || "Failed to update branch"
      })
    } finally {
      setSaving(false)
    }
  }

  const handleDeleteBranch = async (branch: Branch) => {
    if (!confirm(`Are you sure you want to delete "${branch.name}"? This action cannot be undone.`)) {
      return
    }

    setDeleting(prev => ({ ...prev, [branch.id]: true }))
    try {
      const result = await deleteBranch(branch.id)
      if (result.success) {
        const updatedBranches = branches.filter(b => b.id !== branch.id)
        onUpdate(updatedBranches)
        toast({
          title: "Branch deleted",
          description: `${branch.name} has been deleted successfully.`
        })
      } else {
        throw new Error(result.error || "Failed to delete branch")
      }
    } catch (error) {
      console.error("Failed to delete branch:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: (error as Error).message || "Failed to delete branch"
      })
    } finally {
      setDeleting(prev => ({ ...prev, [branch.id]: false }))
    }
  }

  const openCreateModal = () => {
    setSelectedBranch(null)
    setIsModalOpen(true)
  }

  const openEditModal = (branch: Branch) => {
    setSelectedBranch(branch)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setSelectedBranch(null)
  }

  return (
    <Card data-testid="admin-settings-card-branches">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Branches & Service Areas
        </CardTitle>
        <p className="text-sm text-gray-600">
          Manage your business locations and service coverage areas
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <div className="flex-1 max-w-sm">
            <Label htmlFor="search" className="sr-only">Search branches</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="search"
                placeholder="Search branches..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Button
            onClick={openCreateModal}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Branch
          </Button>
        </div>

        {/* Branches List */}
        {filteredBranches.length === 0 ? (
          <div className="text-center py-12">
            {branches.length === 0 ? (
              <>
                <MapPin className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No branches yet</h3>
                <p className="text-gray-600 mb-6">
                  Create your first branch to start managing locations
                </p>
                <Button
                  onClick={openCreateModal}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Branch
                </Button>
              </>
            ) : (
              <>
                <Search className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-gray-600">No branches match your search criteria</p>
              </>
            )}
          </div>
        ) : (
          <div className="grid gap-4">
            {filteredBranches.map((branch) => (
              <BranchCard
                key={branch.id}
                branch={branch}
                onEdit={() => openEditModal(branch)}
                onDelete={() => handleDeleteBranch(branch)}
                deleting={deleting[branch.id]}
              />
            ))}
          </div>
        )}

        {/* Branch Modal */}
        <BranchModal
          isOpen={isModalOpen}
          onClose={closeModal}
          onSave={handleCreateBranch}
          onUpdate={handleUpdateBranch}
          branch={selectedBranch}
          saving={saving}
        />
      </CardContent>
    </Card>
  )
}

interface BranchCardProps {
  branch: Branch
  onEdit: () => void
  onDelete: () => void
  deleting?: boolean
}

function BranchCard({ branch, onEdit, onDelete, deleting }: BranchCardProps) {
  const formatBusinessHours = (businessHours: BusinessHours): string => {
    const openDays = Object.entries(businessHours)
      .filter(([_, hours]) => hours.is_open)
      .map(([day]) => day.charAt(0).toUpperCase() + day.slice(1, 3))
    
    if (openDays.length === 0) return "Closed all week"
    if (openDays.length === 7) return "Open daily"
    if (openDays.length >= 5) return `Open ${openDays.length} days/week`
    return `Open ${openDays.join(", ")}`
  }

  const getStatusColor = (branch: Branch) => {
    if (!branch.is_active) return "bg-gray-100 text-gray-600 border-gray-200"
    if (branch.pickup_available && branch.dropoff_available) return "bg-green-100 text-green-700 border-green-200"
    return "bg-yellow-100 text-yellow-700 border-yellow-200"
  }

  const getStatusText = (branch: Branch) => {
    if (!branch.is_active) return "Inactive"
    if (branch.pickup_available && branch.dropoff_available) return "Full Service"
    if (branch.pickup_available) return "Pickup Only"
    if (branch.dropoff_available) return "Drop-off Only"
    return "Limited Service"
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-3">
            {/* Header */}
            <div className="flex items-start justify-between">
              <div>
                <h3 className="font-semibold text-lg text-gray-900">{branch.name}</h3>
                <p className="text-sm text-gray-600">{branch.address}</p>
                <p className="text-sm text-gray-600">{branch.city}, {branch.province}</p>
              </div>
              <Badge className={cn("text-xs font-medium", getStatusColor(branch))}>
                {getStatusText(branch)}
              </Badge>
            </div>

            {/* Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-2">
              {/* Contact Info */}
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Phone className="h-4 w-4" />
                  <span className="font-medium">Contact</span>
                </div>
                <div className="ml-6 space-y-1">
                  {branch.contact_numbers.slice(0, 2).map((number, index) => (
                    <p key={index} className="text-sm text-gray-700">{number}</p>
                  ))}
                  {branch.contact_numbers.length > 2 && (
                    <p className="text-xs text-gray-500">
                      +{branch.contact_numbers.length - 2} more
                    </p>
                  )}
                </div>
              </div>

              {/* Business Hours */}
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="h-4 w-4" />
                  <span className="font-medium">Hours</span>
                </div>
                <div className="ml-6">
                  <p className="text-sm text-gray-700">{formatBusinessHours(branch.business_hours)}</p>
                </div>
              </div>

              {/* Services */}
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Car className="h-4 w-4" />
                  <span className="font-medium">Services</span>
                </div>
                <div className="ml-6 space-y-1">
                  <div className="flex items-center gap-2">
                    {branch.pickup_available ? (
                      <CheckCircle className="h-3 w-3 text-green-600" />
                    ) : (
                      <XCircle className="h-3 w-3 text-gray-400" />
                    )}
                    <span className="text-sm text-gray-700">Pickup</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {branch.dropoff_available ? (
                      <CheckCircle className="h-3 w-3 text-green-600" />
                    ) : (
                      <XCircle className="h-3 w-3 text-gray-400" />
                    )}
                    <span className="text-sm text-gray-700">Drop-off</span>
                  </div>
                </div>
              </div>
            </div>

            {/* GPS Coordinates */}
            {(branch.latitude && branch.longitude) && (
              <div className="pt-2 border-t border-gray-100">
                <p className="text-xs text-gray-500">
                  GPS: {branch.latitude.toFixed(6)}, {branch.longitude.toFixed(6)}
                </p>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2 ml-4">
            <Button
              variant="secondary"
              size="sm"
              onClick={onEdit}
            >
              <Edit className="h-4 w-4" />
              <span className="sr-only">Edit branch</span>
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={onDelete}
              disabled={deleting}
            >
              {deleting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
              ) : (
                <Trash2 className="h-4 w-4" />
              )}
              <span className="sr-only">Delete branch</span>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
