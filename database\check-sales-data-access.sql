-- Check sales tracking data access issues
-- This script helps diagnose why sales data fetching might fail

-- Check if required tables exist
SELECT 
    schemaname,
    tablename,
    hasindexes,
    hasrules,
    hastriggers
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('bookings', 'cars', 'profiles', 'payments')
ORDER BY tablename;

-- Check table relationships and foreign keys
SELECT 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
    LEFT JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
WHERE 
    tc.table_schema = 'public'
    AND tc.table_name IN ('bookings', 'cars', 'profiles', 'payments')
    AND tc.constraint_type IN ('FOREIGN KEY', 'PRIMARY KEY')
ORDER BY tc.table_name, tc.constraint_type;

-- Check RLS policies that might block access
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('bookings', 'cars', 'profiles', 'payments')
ORDER BY tablename, policyname;

-- Check if tables have data
SELECT 'bookings' as table_name, COUNT(*) as row_count FROM bookings
UNION ALL
SELECT 'cars' as table_name, COUNT(*) as row_count FROM cars
UNION ALL  
SELECT 'profiles' as table_name, COUNT(*) as row_count FROM profiles
UNION ALL
SELECT 'payments' as table_name, COUNT(*) as row_count FROM payments
ORDER BY table_name;

-- Test the exact query that sales-actions.ts uses
SELECT 
    b.*,
    c.id as car_id, c.model as car_model, c.plate_number,
    p.id as profile_id, p.email, p.full_name, p.first_name, p.middle_initial, p.last_name, p.phone, p.role
FROM bookings b
LEFT JOIN cars c ON b.car_id = c.id  
LEFT JOIN profiles p ON b.customer_id = p.id
ORDER BY b.created_at DESC
LIMIT 5;

-- Check payments table structure and sample data
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_schema = 'public' AND table_name = 'payments'
ORDER BY ordinal_position;

-- Sample payments data
SELECT id, booking_id, status, amount, method, created_at
FROM payments 
ORDER BY created_at DESC 
LIMIT 5;
