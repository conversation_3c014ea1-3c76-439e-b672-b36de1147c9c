"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Receipt,
  User,
  Car,
  Calendar,
  MapPin,
  CreditCard,
  Clock,
  FileText,
  Mail,
  Phone
} from "lucide-react";
import { SalesBookingItem } from "@/app/admin/sales-tracking/actions/sales-actions";
import { formatBookingIdForDisplay, formatPaymentIdForDisplay } from "@/lib/reference-ids";

interface BookingReceiptCardProps {
  booking: SalesBookingItem;
  onClose?: () => void;
}

export function BookingReceiptCard({ booking, onClose }: BookingReceiptCardProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-PH", {
      style: "currency",
      currency: "PHP",
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-PH", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  const formatDateOnly = (date: Date) => {
    return new Intl.DateTimeFormat("en-PH", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  return (
    <Card className="max-w-2xl mx-auto bg-white border border-slate-200 shadow-lg">
      <CardHeader className="bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-bold text-slate-900 flex items-center gap-2">
            <Receipt className="h-5 w-5 text-slate-600" />
            Booking Receipt
          </CardTitle>
          {onClose && (
            <Button variant="secondary" size="sm" onClick={onClose}>
              ×
            </Button>
          )}
        </div>
        <div className="flex items-center justify-between mt-2">
          <div>
            <p className="text-sm font-medium text-slate-700">
              Booking ID: {formatBookingIdForDisplay({ id: booking.bookingId, booking_ref: booking.bookingRef })}
            </p>
            <p className="text-xs text-slate-500">
              Created: {formatDate(booking.createdAt)}
            </p>
          </div>
          <Badge
            variant={booking.paymentStatus === 'Paid' ? 'default' : 
                    booking.paymentStatus === 'Pending' ? 'secondary' : 'destructive'}
            className={`${
              booking.paymentStatus === 'Paid' ? 'bg-green-100 text-green-800 border-green-200' :
              booking.paymentStatus === 'Pending' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
              'bg-red-100 text-red-800 border-red-200'
            }`}
          >
            {booking.paymentStatus}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="p-6 space-y-6">
        {/* Customer Information */}
        <div className="bg-slate-50 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-slate-900 flex items-center gap-2 mb-3">
            <User className="h-4 w-4 text-slate-600" />
            Customer Information
          </h3>
          <div className="space-y-4">
            {/* Customer Name Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                  Customer Name
                </p>
                <div className="mt-1 space-y-1">
                  {booking.customerFirstName || booking.customerLastName ? (
                    <div className="space-y-1">
                      {booking.customerFirstName && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-slate-500 w-16">First:</span>
                          <span className="text-sm font-medium text-slate-900">{booking.customerFirstName}</span>
                        </div>
                      )}
                      {booking.customerMiddleInitial && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-slate-500 w-16">Middle:</span>
                          <span className="text-sm font-medium text-slate-900">{booking.customerMiddleInitial}</span>
                        </div>
                      )}
                      {booking.customerLastName && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-slate-500 w-16">Last:</span>
                          <span className="text-sm font-medium text-slate-900">{booking.customerLastName}</span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm font-medium text-slate-900">
                      {booking.customerFullName || booking.customerName}
                    </p>
                  )}
                </div>
              </div>
              <div>
                <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                  Booking Channel
                </p>
                <p className="text-sm font-medium text-slate-900 mt-1">
                  {booking.channel}
                </p>
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-xs font-medium text-slate-500 uppercase tracking-wide flex items-center gap-1">
                  <Mail className="h-3 w-3" />
                  Email Address
                </p>
                <p className="text-sm font-medium text-slate-900 mt-1">
                  {booking.customerEmail || "N/A"}
                </p>
              </div>
              <div>
                <p className="text-xs font-medium text-slate-500 uppercase tracking-wide flex items-center gap-1">
                  <Phone className="h-3 w-3" />
                  Phone Number
                </p>
                <p className="text-sm font-medium text-slate-900 mt-1">
                  {booking.customerPhone || "N/A"}
                </p>
              </div>
            </div>

            {/* Payment Reference */}
            {booking.paymentId && (
              <div>
                <p className="text-xs font-medium text-slate-500 uppercase tracking-wide flex items-center gap-1">
                  <CreditCard className="h-3 w-3" />
                  Payment Reference
                </p>
                <p className="text-sm font-medium text-slate-900 mt-1">
                  {formatPaymentIdForDisplay({ id: booking.paymentId, payment_ref: booking.paymentRef })}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Vehicle Information */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-slate-900 flex items-center gap-2 mb-3">
            <Car className="h-4 w-4 text-blue-600" />
            Vehicle Details
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                Vehicle Model
              </p>
              <p className="text-sm font-medium text-slate-900 mt-1">
                {booking.model}
              </p>
            </div>
            <div>
              <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                Plate Number
              </p>
              <p className="text-sm font-medium text-slate-900 mt-1">
                {booking.plateNo}
              </p>
            </div>
          </div>
        </div>

        {/* Rental Information */}
        <div className="bg-green-50 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-slate-900 flex items-center gap-2 mb-3">
            <Calendar className="h-4 w-4 text-green-600" />
            Rental Period
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                Pickup Date
              </p>
              <p className="text-sm font-medium text-slate-900 mt-1">
                {formatDateOnly(booking.pickupDate)}
              </p>
            </div>
            <div>
              <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                Return Date
              </p>
              <p className="text-sm font-medium text-slate-900 mt-1">
                {formatDateOnly(booking.dropoffDate)}
              </p>
            </div>
            <div>
              <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                Duration
              </p>
              <p className="text-sm font-medium text-slate-900 mt-1">
                {booking.rentalDays} day{booking.rentalDays !== 1 ? 's' : ''}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <p className="text-xs font-medium text-slate-500 uppercase tracking-wide flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              Pickup Location
            </p>
            <p className="text-sm font-medium text-slate-900 mt-1">
              {booking.location}
            </p>
          </div>
        </div>

        {/* Payment Breakdown */}
        <div className="bg-amber-50 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-slate-900 flex items-center gap-2 mb-3">
            <CreditCard className="h-4 w-4 text-amber-600" />
            Payment Breakdown
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">Base Rate ({booking.rentalDays} days)</span>
              <span className="text-sm font-medium text-slate-900">
                {formatCurrency(booking.baseRate)}
              </span>
            </div>
            {booking.addonsTotal > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Add-ons</span>
                <span className="text-sm font-medium text-slate-900">
                  {formatCurrency(booking.addonsTotal)}
                </span>
              </div>
            )}
            {booking.discounts > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Discounts</span>
                <span className="text-sm font-medium text-green-600">
                  -{formatCurrency(booking.discounts)}
                </span>
              </div>
            )}
            {booking.taxes > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Taxes (VAT)</span>
                <span className="text-sm font-medium text-slate-900">
                  {formatCurrency(booking.taxes)}
                </span>
              </div>
            )}
            {booking.fees > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Additional Fees</span>
                <span className="text-sm font-medium text-slate-900">
                  {formatCurrency(booking.fees)}
                </span>
              </div>
            )}
            <div className="border-t border-slate-200 pt-3">
              <div className="flex justify-between items-center">
                <span className="text-base font-semibold text-slate-900">Total Amount</span>
                <span className="text-lg font-bold text-slate-900">
                  {formatCurrency(booking.totalAmount)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="bg-slate-50 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-slate-900 flex items-center gap-2 mb-3">
            <FileText className="h-4 w-4 text-slate-600" />
            Additional Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                Daily Rate
              </p>
              <p className="text-sm font-medium text-slate-900 mt-1">
                {formatCurrency(booking.baseRate / booking.rentalDays)}/day
              </p>
            </div>
            <div>
              <p className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                Vehicle ID
              </p>
              <p className="text-sm font-medium text-slate-900 mt-1">
                {booking.vehicleId}
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center pt-4 border-t border-slate-200">
          <p className="text-xs text-slate-500">
            This is a system-generated receipt for booking {formatBookingIdForDisplay({ id: booking.bookingId, booking_ref: booking.bookingRef })}
          </p>
          <p className="text-xs text-slate-500 mt-1">
            Generated on {formatDate(new Date())}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
