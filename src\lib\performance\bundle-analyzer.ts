// Bundle analysis utilities for monitoring performance
export interface BundleStats {
  totalSize: number;
  jsSize: number;
  cssSize: number;
  imageSize: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
}

// Performance monitoring for network conditions
export function detectNetworkSpeed(): 'slow-2g' | '2g' | '3g' | '4g' | 'fast' {
  if (typeof navigator !== 'undefined' && 'connection' in navigator) {
    const connection = (navigator as any).connection;
    const effectiveType = connection?.effectiveType;
    
    switch (effectiveType) {
      case 'slow-2g': return 'slow-2g';
      case '2g': return '2g';
      case '3g': return '3g';
      case '4g': return '4g';
      default: return 'fast';
    }
  }
  return 'fast';
}

// Adaptive loading based on network conditions
export function shouldLoadHighQuality(): boolean {
  const networkSpeed = detectNetworkSpeed();
  return ['4g', 'fast'].includes(networkSpeed);
}

// Get Supabase storage URL for car images
export function getSupabaseImageUrl(filename: string): string {
  const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
  return `${SUPABASE_URL}/storage/v1/object/public/car-images/cars/${filename}`;
}

// Preload critical resources based on route and actual car data
export function preloadCriticalResources(route: string, carImages?: string[]): void {
  if (typeof window === 'undefined') return;

  const staticResources: Record<string, string[]> = {
    '/': ['/ollie_logo.jpg'],
    '/admin': ['/ollie_logo.jpg'],
  };

  let resources = staticResources[route] || [];

  // Add dynamic car images for customer routes
  if (route.includes('/customer') || route === '/' && carImages?.length) {
    const priorityCarImages = carImages?.slice(0, 3) || []; // Preload first 3 car images
    const supabaseImageUrls = priorityCarImages
      .filter(img => img && !img.includes('/placeholder'))
      .map(getSupabaseImageUrl);
    resources = [...resources, ...supabaseImageUrls];
  }
  
  resources.forEach((resource) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = resource;
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  });
}

// Preload popular car images based on actual database data
export async function preloadPopularCarImages(carImageUrls: string[] = []): Promise<void> {
  if (typeof window === 'undefined') return;
  
  try {
    // Use first 3 most popular car images for preloading
    const popularCarImages: string[] = carImageUrls.slice(0, 3);

    popularCarImages
      .filter(url => url && !url.includes('/placeholder'))
      .forEach((imageUrl) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = imageUrl;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      });
  } catch (error) {
    console.warn('Failed to preload car images:', error);
  }
}

// Monitor Core Web Vitals
export function measureWebVitals(onPerfEntry?: (metric: any) => void) {
  if (onPerfEntry && typeof window !== 'undefined') {
    import('web-vitals').then(({ onCLS, onINP, onFCP, onLCP, onTTFB }) => {
      onCLS(onPerfEntry);
      onINP(onPerfEntry);
      onFCP(onPerfEntry);
      onLCP(onPerfEntry);
      onTTFB(onPerfEntry);
    });
  }
}
