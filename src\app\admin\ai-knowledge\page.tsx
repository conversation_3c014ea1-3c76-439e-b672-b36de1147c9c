'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { AlertCircle, Database, RefreshCw, Brain, Check, X, Lock } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAdminAuth } from '@/components/auth/admin-auth-context';

interface KnowledgeStats {
  knowledgeCount: number;
  categories: string[];
  sources: string[];
  lastUpdated: string;
}

interface PopulationConfig {
  includeCars: boolean;
  includeBookingInfo: boolean;
  includePolicies: boolean;
  includeAdminProcedures: boolean;
  includeCustomerInfo: boolean;
  clearExisting: boolean;
}

export default function AIKnowledgePage() {
  const { user, profile, isSuperAdmin, loading: authLoading } = useAdminAuth();
  const [stats, setStats] = useState<KnowledgeStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isPopulating, setIsPopulating] = useState(false);
  const [lastPopulationResult, setLastPopulationResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  
  const [config, setConfig] = useState<PopulationConfig>({
    includeCars: true,
    includeBookingInfo: true,
    includePolicies: true,
    includeAdminProcedures: true,
    includeCustomerInfo: true,
    clearExisting: false
  });

  const fetchKnowledgeStats = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/populate-knowledge');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error('Error fetching knowledge stats:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch knowledge statistics');
    } finally {
      setIsLoading(false);
    }
  };

  const populateKnowledge = async () => {
    setIsPopulating(true);
    setError(null);
    setLastPopulationResult(null);
    
    try {
      const response = await fetch('/api/admin/populate-knowledge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      setLastPopulationResult(result);
      
      // Refresh stats after population
      await fetchKnowledgeStats();
    } catch (err) {
      console.error('Error populating knowledge:', err);
      setError(err instanceof Error ? err.message : 'Failed to populate knowledge base');
    } finally {
      setIsPopulating(false);
    }
  };

  useEffect(() => {
    fetchKnowledgeStats();
  }, []);

  // Wait for auth loading to complete before checking permissions
  if (authLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">AI Knowledge Base</h1>
            <p className="text-muted-foreground">
              Manage the AI chatbot's knowledge base and website data integration
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3" />
            <span className="text-muted-foreground">Loading...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Super admin access control
  if (!isSuperAdmin) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <Lock className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl">Super Admin Access Required</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              This page is restricted to super administrators only. The AI Knowledge Base management 
              requires elevated privileges to ensure system security and data integrity.
            </p>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                If you need access to this feature, please contact the system administrator.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI Knowledge Base</h1>
          <p className="text-muted-foreground">
            Manage the AI chatbot's knowledge base and website data integration
          </p>
        </div>
        <Button
          onClick={fetchKnowledgeStats}
          disabled={isLoading}
          variant="secondary"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="populate">Populate Knowledge</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Knowledge Items
                </CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {isLoading ? '...' : stats?.knowledgeCount || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Indexed knowledge entries
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Categories
                </CardTitle>
                <Brain className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {isLoading ? '...' : stats?.categories?.length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Knowledge categories
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Data Sources
                </CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {isLoading ? '...' : stats?.sources?.length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Information sources
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Last Updated
                </CardTitle>
                <RefreshCw className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-sm font-bold">
                  {isLoading ? '...' : stats?.lastUpdated 
                    ? new Date(stats.lastUpdated).toLocaleDateString()
                    : 'Never'}
                </div>
                <p className="text-xs text-muted-foreground">
                  Knowledge base update
                </p>
              </CardContent>
            </Card>
          </div>

          {stats && (
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Knowledge Categories</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {stats.categories.map((category, index) => (
                      <Badge key={index} variant="secondary">
                        {category}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Data Sources</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {stats.sources.map((source, index) => (
                      <Badge key={index} variant="outline">
                        {source}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="populate" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Populate Knowledge Base
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Extract and index content from your website to enhance AI responses
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="cars"
                      checked={config.includeCars}
                      onCheckedChange={(checked) => 
                        setConfig(prev => ({ ...prev, includeCars: checked }))
                      }
                    />
                    <Label htmlFor="cars">Car Information & Fleet Data</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="booking"
                      checked={config.includeBookingInfo}
                      onCheckedChange={(checked) => 
                        setConfig(prev => ({ ...prev, includeBookingInfo: checked }))
                      }
                    />
                    <Label htmlFor="booking">Booking Processes & Policies</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="policies"
                      checked={config.includePolicies}
                      onCheckedChange={(checked) => 
                        setConfig(prev => ({ ...prev, includePolicies: checked }))
                      }
                    />
                    <Label htmlFor="policies">Terms & Conditions</Label>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="admin"
                      checked={config.includeAdminProcedures}
                      onCheckedChange={(checked) => 
                        setConfig(prev => ({ ...prev, includeAdminProcedures: checked }))
                      }
                    />
                    <Label htmlFor="admin">Admin Procedures</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="customer"
                      checked={config.includeCustomerInfo}
                      onCheckedChange={(checked) => 
                        setConfig(prev => ({ ...prev, includeCustomerInfo: checked }))
                      }
                    />
                    <Label htmlFor="customer">Customer Support & FAQ</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="clear"
                      checked={config.clearExisting}
                      onCheckedChange={(checked) => 
                        setConfig(prev => ({ ...prev, clearExisting: checked }))
                      }
                    />
                    <Label htmlFor="clear" className="text-destructive">
                      Clear Existing Knowledge
                    </Label>
                  </div>
                </div>
              </div>

              <Button
                onClick={populateKnowledge}
                disabled={isPopulating}
                className="w-full"
                size="lg"
              >
                {isPopulating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Populating Knowledge Base...
                  </>
                ) : (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    Populate Knowledge Base
                  </>
                )}
              </Button>

              {lastPopulationResult && (
                <Alert className="border-green-200 bg-green-50">
                  <Check className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    <div className="font-medium">Knowledge base updated successfully!</div>
                    <div className="mt-1 text-sm">
                      {lastPopulationResult.message} - {lastPopulationResult.knowledgeCount} total items
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
