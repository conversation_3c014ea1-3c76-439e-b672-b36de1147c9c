// @ts-nocheck
// Edge Function for sending emails via Resend API
// TypeScript errors are expected in Deno environment - they don't affect runtime

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req: Request) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { to, subject, html, text, type, extensionRequestId, notificationType } = await req.json();
    console.log('Email function invoked with:', { to, subject });
    
    // Create Supabase client using proper Edge Function pattern
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
    
    // Process recipient array
    const recipients = Array.isArray(to) ? to : [to];
    console.log('Recipients:', recipients);
    
    // Track results
    const results: Array<{
      recipient: string;
      success: boolean;
      error?: string;
    }> = [];
    
    // Send emails to each recipient
    for (const recipient of recipients) {
      try {
        console.log(`Sending email to: ${recipient}`);
        
        // Send email via Resend API
        const resendApiKey = Deno.env.get('RESEND_API_KEY');
        
        if (!resendApiKey) {
          console.error('RESEND_API_KEY environment variable is not set');
          results.push({
            recipient,
            success: false,
            error: 'RESEND_API_KEY environment variable is not set'
          });
          continue;
        }
        
        console.log('Using Resend API key:', resendApiKey ? 'Present' : 'Missing');

        const response = await fetch('https://api.resend.com/emails', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${resendApiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            from: 'PathLink <<EMAIL>>',
            to: recipient,
            subject: subject,
            html: html || `<p>${text || ''}</p>`,
          }),
        });

        const data = await response.json();
        const error = response.ok ? null : data;
        
        if (error) {
          console.error(`Failed to send email to ${recipient}:`, error);
          results.push({
            recipient,
            success: false,
            error: JSON.stringify(error)
          });
        } else {
          console.log(`Email sent successfully to ${recipient}`);
          results.push({
            recipient,
            success: true
          });
        }
      } catch (err: any) {
        console.error(`Error sending to ${recipient}:`, err);
        results.push({
          recipient,
          success: false,
          error: err.message
        });
      }
    }
    
    // Check if any emails failed
    const failures = results.filter(r => !r.success);
    
    if (failures.length === 0) {
      return new Response(
        JSON.stringify({ success: true, message: "All emails sent successfully", results }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    } else {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: 'Failed to send one or more emails', 
          results 
        }),
        { status: 207, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
  } catch (error) {
    console.error('Unhandled error in Edge Function:', error);
    return new Response(
      JSON.stringify({ success: false, message: 'Server error', error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
