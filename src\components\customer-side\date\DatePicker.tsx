"use client"

import * as React from "react"
import { Calendar as CalendarIcon, ChevronDown, ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { format, addDays, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isToday, isSameDay, parseISO, addMonths, subMonths, startOfWeek, endOfWeek } from "date-fns"

interface DatePickerProps {
  value?: string // YYYY-MM-DD format
  onChange?: (value: string) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  minDate?: string // YYYY-MM-DD format
  maxDate?: string // YYYY-MM-DD format
  showQuickActions?: boolean
  id?: string
  "aria-label"?: string
  "aria-describedby"?: string
}

function getTodayDate(): string {
  const now = new Date()
  return format(now, "yyyy-MM-dd")
}

function getTomorrowDate(): string {
  const tomorrow = addDays(new Date(), 1)
  return format(tomorrow, "yyyy-MM-dd")
}

function formatDisplayDate(dateString: string): string {
  if (!dateString) return ""
  try {
    const date = parseISO(dateString)
    return format(date, "MMM d, yyyy")
  } catch {
    return dateString
  }
}

export function DatePicker({
  value,
  onChange,
  placeholder = "Select date",
  disabled = false,
  className,
  minDate,
  maxDate,
  showQuickActions = true,
  id,
  "aria-label": ariaLabel,
  "aria-describedby": ariaDescribedBy,
}: DatePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [currentMonth, setCurrentMonth] = React.useState(() => {
    if (value) {
      try {
        return parseISO(value)
      } catch {
        return new Date()
      }
    }
    return new Date()
  })
  const [selectedDate, setSelectedDate] = React.useState<Date | null>(() => {
    if (value) {
      try {
        return parseISO(value)
      } catch {
        return null
      }
    }
    return null
  })

  const triggerRef = React.useRef<HTMLButtonElement>(null)
  const calendarRef = React.useRef<HTMLDivElement>(null)

  // Update selected date when value prop changes
  React.useEffect(() => {
    if (value) {
      try {
        const date = parseISO(value)
        setSelectedDate(date)
        setCurrentMonth(date)
      } catch {
        setSelectedDate(null)
      }
    } else {
      setSelectedDate(null)
    }
  }, [value])

  // Prevent body scroll when popover is open
  React.useEffect(() => {
    if (isOpen) {
      const originalOverflow = document.body.style.overflow
      document.body.style.overflow = "hidden"
      return () => {
        document.body.style.overflow = originalOverflow
      }
    }
  }, [isOpen])

  const handleDateSelect = (date: Date) => {
    const dateString = format(date, "yyyy-MM-dd")
    setSelectedDate(date)
    onChange?.(dateString)
    setIsOpen(false)
    triggerRef.current?.focus()
  }

  const handleQuickAction = (action: "today" | "tomorrow" | "clear") => {
    switch (action) {
      case "today":
        handleDateSelect(new Date())
        break
      case "tomorrow":
        handleDateSelect(addDays(new Date(), 1))
        break
      case "clear":
        setSelectedDate(null)
        onChange?.("")
        setIsOpen(false)
        break
    }
  }

  const isDateDisabled = (date: Date): boolean => {
    if (minDate) {
      try {
        const min = parseISO(minDate)
        if (date < min) return true
      } catch {}
    }
    if (maxDate) {
      try {
        const max = parseISO(maxDate)
        if (date > max) return true
      } catch {}
    }
    return false
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => 
      direction === 'prev' ? subMonths(prev, 1) : addMonths(prev, 1)
    )
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === "ArrowDown" || e.key === "Enter" || e.key === " ") {
        e.preventDefault()
        setIsOpen(true)
      }
      return
    }

    switch (e.key) {
      case "Escape":
        e.preventDefault()
        setIsOpen(false)
        triggerRef.current?.focus()
        break
    }
  }

  // Generate calendar days
  const monthStart = startOfMonth(currentMonth)
  const monthEnd = endOfMonth(currentMonth)
  const calendarStart = startOfWeek(monthStart, { weekStartsOn: 0 })
  const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 0 })
  const calendarDays = eachDayOfInterval({ start: calendarStart, end: calendarEnd })

  const weekDays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          ref={triggerRef}
          variant="secondary"
          role="combobox"
          aria-expanded={isOpen}
          aria-haspopup="dialog"
          aria-label={ariaLabel || "Select date"}
          aria-describedby={ariaDescribedBy}
          className={cn(
            "h-12 w-full justify-between border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100 transition-all duration-200",
            !value && "text-muted-foreground",
            disabled && "cursor-not-allowed opacity-50",
            className
          )}
          disabled={disabled}
          onKeyDown={handleKeyDown}
          id={id}
        >
          <div className="flex items-center gap-3">
            <CalendarIcon className="h-5 w-5 text-blue-600 flex-shrink-0" />
            <span className="text-sm">
              {value ? formatDisplayDate(value) : placeholder}
            </span>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>

      <PopoverContent 
        className="w-80 p-0 bg-white border border-gray-200 rounded-lg shadow-lg"
        align="start"
        sideOffset={4}
        onCloseAutoFocus={(e) => {
          e.preventDefault()
          triggerRef.current?.focus()
        }}
      >
        <div ref={calendarRef} className="relative">
          {/* Quick Actions */}
          {showQuickActions && (
            <div className="flex items-center gap-2 p-3 border-b border-gray-100">
              <Button
                variant="tertiary"
                size="sm"
                onClick={() => handleQuickAction("today")}
                className="h-8 px-3 text-xs font-medium text-blue-600 hover:bg-blue-50"
              >
                Today
              </Button>
              <Button
                variant="tertiary"
                size="sm"
                onClick={() => handleQuickAction("tomorrow")}
                className="h-8 px-3 text-xs font-medium text-blue-600 hover:bg-blue-50"
              >
                Tomorrow
              </Button>
              <Button
                variant="tertiary"
                size="sm"
                onClick={() => handleQuickAction("clear")}
                className="h-8 px-3 text-xs font-medium text-gray-500 hover:bg-gray-50 ml-auto"
              >
                Clear
              </Button>
            </div>
          )}

          {/* Calendar Header */}
          <div className="flex items-center justify-between p-3 border-b border-gray-100">
            <Button
              variant="tertiary"
              size="sm"
              onClick={() => navigateMonth('prev')}
              className="h-8 w-8 p-0 hover:bg-gray-100"
              aria-label="Previous month"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <div className="font-medium text-gray-900">
              {format(currentMonth, "MMMM yyyy")}
            </div>
            
            <Button
              variant="tertiary"
              size="sm"
              onClick={() => navigateMonth('next')}
              className="h-8 w-8 p-0 hover:bg-gray-100"
              aria-label="Next month"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Calendar Grid */}
          <div className="p-3">
            {/* Day Headers */}
            <div className="grid grid-cols-7 gap-1 mb-2">
              {weekDays.map((day) => (
                <div
                  key={day}
                  className="h-8 flex items-center justify-center text-xs font-medium text-gray-500"
                >
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Days */}
            <div className="grid grid-cols-7 gap-1">
              {calendarDays.map((date, index) => {
                const isCurrentMonth = isSameMonth(date, currentMonth)
                const isSelected = selectedDate && isSameDay(date, selectedDate)
                const isCurrentDay = isToday(date)
                const isDisabled = isDateDisabled(date)

                return (
                  <button
                    key={date.toISOString()}
                    onClick={() => !isDisabled && handleDateSelect(date)}
                    disabled={isDisabled}
                    className={cn(
                      "h-9 w-9 text-sm rounded-md transition-colors relative",
                      "hover:bg-blue-50 focus:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-200",
                      !isCurrentMonth && "text-gray-400",
                      isCurrentMonth && "text-gray-700",
                      isSelected && "bg-blue-600 text-white hover:bg-blue-700 focus:bg-blue-700",
                      isCurrentDay && !isSelected && "bg-blue-100 text-blue-700 font-medium",
                      isDisabled && "text-gray-300 cursor-not-allowed hover:bg-transparent"
                    )}
                    aria-label={format(date, "MMMM d, yyyy")}
                    tabIndex={isSelected ? 0 : -1}
                  >
                    {format(date, "d")}
                  </button>
                )
              })}
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Export for compatibility
export default DatePicker
