-- Fix payment table constraints to match application usage

-- Drop existing constraints
ALTER TABLE public.payments DROP CONSTRAINT IF EXISTS payments_status_check;
ALTER TABLE public.payments DROP CONSTRAINT IF EXISTS payments_method_check;

-- Add updated status constraint to include all statuses used in the application
ALTER TABLE public.payments ADD CONSTRAINT payments_status_check 
CHECK (status = ANY (ARRAY[
  'Pending'::text,
  'Pending Verification'::text,
  'Paid'::text,
  'Failed'::text,
  'Rejected'::text,
  'Refunded'::text
]));

-- Add updated method constraint to include only the three payment methods
ALTER TABLE public.payments ADD CONSTRAINT payments_method_check 
CHECK (method = ANY (ARRAY[
  'GCash'::text,
  'Bank Transfer'::text,
  'Remittance Center'::text
]));

-- Add missing columns that might be needed
ALTER TABLE public.payments ADD COLUMN IF NOT EXISTS proof_of_payment_url text;
ALTER TABLE public.payments ADD COLUMN IF NOT EXISTS verification_notes text;
ALTER TABLE public.payments ADD COLUMN IF NOT EXISTS verified_by uuid;
ALTER TABLE public.payments ADD COLUMN IF NOT EXISTS verified_at timestamp with time zone;

-- Add foreign key constraint for verified_by if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'payments_verified_by_fkey'
    ) THEN
        ALTER TABLE public.payments 
        ADD CONSTRAINT payments_verified_by_fkey 
        FOREIGN KEY (verified_by) REFERENCES profiles(id);
    END IF;
END $$;
