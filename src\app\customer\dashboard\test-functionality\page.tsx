"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Car,
  Clock,
  Eye,
  FileText
} from "lucide-react";
import { UnifiedDashboardTable } from "@/components/customer-side/dashboard/unified-dashboard-table";
import { useToast } from "@/hooks/use-toast";

// Mock data for testing
const mockBookings = [
  {
    id: "test-booking-1",
    type: "booking" as const,
    status: "Pending" as const,
    pickup_location: "Downtown Office",
    dropoff_location: "Airport Terminal",
    pickup_datetime: "2024-01-15T09:00:00Z",
    dropoff_datetime: "2024-01-18T18:00:00Z",
    total_amount: 15000,
    cars: {
      id: "car-1",
      model: "Toyota Vios",
      type: "<PERSON>dan",
      image_url: "/images/toyota-vios.jpg",
      price_per_day: 5000
    }
  },
  {
    id: "test-booking-2",
    type: "booking" as const,
    status: "Active" as const,
    pickup_location: "Hotel Resort",
    dropoff_location: "Shopping Mall",
    pickup_datetime: "2024-01-10T08:00:00Z",
    dropoff_datetime: "2024-01-12T20:00:00Z",
    total_amount: 12000,
    cars: {
      id: "car-2",
      model: "Honda City",
      type: "Sedan",
      image_url: "/images/honda-city.jpg",
      price_per_day: 4000
    }
  },
  {
    id: "test-booking-3",
    type: "booking" as const,
    status: "Completed" as const,
    pickup_location: "Home Address",
    dropoff_location: "Business District",
    pickup_datetime: "2024-01-05T07:30:00Z",
    dropoff_datetime: "2024-01-07T19:30:00Z",
    total_amount: 8000,
    cars: {
      id: "car-3",
      model: "Mitsubishi Mirage",
      type: "Hatchback",
      image_url: "/images/mitsubishi-mirage.jpg",
      price_per_day: 3500
    }
  }
];

const mockPayments = [
  {
    id: "test-payment-1",
    type: "payment" as const,
    payment_id: "pay-001",
    payment_method: "GCash",
    payment_status: "Paid" as const,
    payment_amount: 15000,
    payment_date: "2024-01-14T10:00:00Z",
    booking_reference: "test-booking-1",
    proof_of_payment_url: "https://example.com/receipt1.jpg"
  },
  {
    id: "test-payment-2",
    type: "payment" as const,
    payment_id: "pay-002",
    payment_method: "Bank Transfer",
    payment_status: "Pending Verification" as const,
    payment_amount: 12000,
    payment_date: "2024-01-09T14:30:00Z",
    booking_reference: "test-booking-2",
    proof_of_payment_url: "https://example.com/receipt2.jpg"
  }
];

export default function DashboardFunctionalityTest() {
  const { toast } = useToast();
  const [loading, setLoading] = React.useState(false);
  const [completingTrips, setCompletingTrips] = React.useState<Set<string>>(new Set());
  const [cancellingBookings, setCancellingBookings] = React.useState<Set<string>>(new Set());
  
  // Test checklist items
  const [testResults, setTestResults] = React.useState({
    loadingState: false,
    cancelPendingBooking: false,
    finishActiveTrip: false,
    viewBookingDetails: false,
    responsiveLayout: false,
    tableColumns: false
  });

  const updateTestResult = (test: keyof typeof testResults, passed: boolean) => {
    setTestResults(prev => ({ ...prev, [test]: passed }));
  };

  // Mock handlers for testing
  const handleFinishTrip = async (bookingId: string) => {
    setCompletingTrips(prev => new Set(prev).add(bookingId));
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    toast({
      title: "Trip Completed Successfully! (TEST)",
      description: `Booking ${bookingId} has been marked as completed.`,
      className: "bg-white border-green-200",
    });
    
    setCompletingTrips(prev => {
      const next = new Set(prev);
      next.delete(bookingId);
      return next;
    });
    
    updateTestResult('finishActiveTrip', true);
  };

  const handleCancelBooking = async (bookingId: string) => {
    setCancellingBookings(prev => new Set(prev).add(bookingId));
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    toast({
      title: "Booking Cancelled Successfully! (TEST)",
      description: `Booking ${bookingId} has been cancelled.`,
      className: "bg-white border-green-200",
    });
    
    setCancellingBookings(prev => {
      const next = new Set(prev);
      next.delete(bookingId);
      return next;
    });
    
    updateTestResult('cancelPendingBooking', true);
  };

  const handleViewBookingDetails = (bookingId: string) => {
    toast({
      title: "View Details (TEST)",
      description: `Opening details for booking ${bookingId}`,
    });
    updateTestResult('viewBookingDetails', true);
  };

  const handleViewReceipt = (paymentId: string) => {
    toast({
      title: "View Receipt (TEST)",
      description: `Opening receipt for payment ${paymentId}`,
    });
  };

  const handleReuploadPayment = (bookingId: string) => {
    toast({
      title: "Reupload Payment (TEST)",
      description: `Opening payment upload for booking ${bookingId}`,
    });
  };

  const testLoadingState = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      updateTestResult('loadingState', true);
      toast({
        title: "Loading State Test Complete",
        description: "Loading skeleton displayed correctly",
      });
    }, 3000);
  };

  const TestStatusIcon = ({ passed }: { passed: boolean }) => (
    passed ? <CheckCircle className="h-4 w-4 text-green-600" /> : <XCircle className="h-4 w-4 text-red-600" />
  );

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Customer Dashboard - Functionality Test Page
        </h1>
        <p className="text-gray-600">
          Test all Cancel Trip and Finish Trip functionality plus enhanced loading states.
        </p>
      </div>

      {/* Test Controls */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Car className="h-5 w-5" />
            Test Controls
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-6">
            <Button onClick={testLoadingState} variant="secondary">
              Test Loading State
            </Button>
            <Button onClick={() => updateTestResult('responsiveLayout', true)} variant="secondary">
              Mark Responsive Test Passed
            </Button>
            <Button onClick={() => updateTestResult('tableColumns', true)} variant="secondary">
              Mark Table Columns Test Passed
            </Button>
          </div>

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>How to Test:</strong>
              <br />
              1. Test the loading state using the button above
              <br />
              2. Try cancelling the "Pending" booking (test-booking-1)
              <br />
              3. Try finishing the "Active" trip (test-booking-2) 
              <br />
              4. Click "View Details" on any booking
              <br />
              5. Resize the window to test responsive behavior
              <br />
              6. Verify table columns are properly aligned without duplication
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Test Results */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Test Results Checklist</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <TestStatusIcon passed={testResults.loadingState} />
              <span className={testResults.loadingState ? "text-green-700" : "text-gray-700"}>
                Loading State Enhanced
              </span>
            </div>
            <div className="flex items-center gap-2">
              <TestStatusIcon passed={testResults.cancelPendingBooking} />
              <span className={testResults.cancelPendingBooking ? "text-green-700" : "text-gray-700"}>
                Cancel Pending Booking
              </span>
            </div>
            <div className="flex items-center gap-2">
              <TestStatusIcon passed={testResults.finishActiveTrip} />
              <span className={testResults.finishActiveTrip ? "text-green-700" : "text-gray-700"}>
                Finish Active Trip
              </span>
            </div>
            <div className="flex items-center gap-2">
              <TestStatusIcon passed={testResults.viewBookingDetails} />
              <span className={testResults.viewBookingDetails ? "text-green-700" : "text-gray-700"}>
                View Booking Details
              </span>
            </div>
            <div className="flex items-center gap-2">
              <TestStatusIcon passed={testResults.responsiveLayout} />
              <span className={testResults.responsiveLayout ? "text-green-700" : "text-gray-700"}>
                Responsive Layout
              </span>
            </div>
            <div className="flex items-center gap-2">
              <TestStatusIcon passed={testResults.tableColumns} />
              <span className={testResults.tableColumns ? "text-green-700" : "text-gray-700"}>
                Table Columns Fixed
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dashboard Table */}
      <UnifiedDashboardTable
        bookings={mockBookings}
        payments={mockPayments}
        loading={loading}
        onFinishTrip={handleFinishTrip}
        onViewReceipt={handleViewReceipt}
        onReuploadPayment={handleReuploadPayment}
        onViewBookingDetails={handleViewBookingDetails}
        onCancelBooking={handleCancelBooking}
        completingTrips={completingTrips}
        cancellingBookings={cancellingBookings}
      />

      {/* Implementation Status */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Implementation Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-green-700 flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Issues Fixed
              </h4>
              <ul className="mt-2 text-sm text-gray-600 space-y-1">
                <li>• Fixed duplicate table columns causing broken layout</li>
                <li>• Replaced non-existent API route with proper server action for Cancel Trip</li>
                <li>• Enhanced loading state with proper skeletons for mobile and desktop</li>
                <li>• Improved responsive design and layout consistency</li>
                <li>• Added proper error handling and user feedback</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-blue-700 flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Features Working
              </h4>
              <ul className="mt-2 text-sm text-gray-600 space-y-1">
                <li>• Cancel Trip button (for Pending bookings only)</li>
                <li>• Finish Trip button (for Active bookings only)</li>
                <li>• View booking details functionality</li>
                <li>• Enhanced loading states with animations</li>
                <li>• Responsive mobile and desktop layouts</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
