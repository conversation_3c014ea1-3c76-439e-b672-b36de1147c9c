"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, X, FileText, CheckCircle, AlertCircle } from "lucide-react";
import { DocumentUpload, DocumentFile } from "@/components/ui/document-upload";

interface PaymentReuploadModalProps {
  bookingId: string;
  paymentId: string;
  onSuccess?: () => void;
}

export function PaymentReuploadModal({
  bookingId,
  paymentId,
  onSuccess,
}: PaymentReuploadModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [files, setFiles] = useState<DocumentFile[]>([]);

  const handleSubmitReupload = async () => {
    const uploadedFile = files.find((f) => f.status === "completed");
    if (!uploadedFile?.url) return;

    setIsUploading(true);

    try {
      // Update the payment record with new proof of payment
      const response = await fetch("/api/payments/reupload", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          paymentId,
          proofOfPaymentUrl: uploadedFile.url,
        }),
      });

      if (response.ok) {
        setIsOpen(false);
        setFiles([]);
        onSuccess?.();
      } else {
        console.error("Failed to reupload payment proof");
      }
    } catch (error) {
      console.error("Error reupload payment proof:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const uploadedFile = files.find((f) => f.status === "completed");

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="secondary" size="sm">
          <Upload className="h-4 w-4 mr-1" />
          Re-upload
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle>Re-upload Payment Proof</DialogTitle>
          <Button 
            variant="secondary" 
            size="icon" 
            className="flex items-center justify-center h-8 w-8 rounded-full hover:bg-gray-100" 
            onClick={() => setIsOpen(false)}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-yellow-800 mb-1">
                  Payment Rejected
                </h4>
                <p className="text-sm text-yellow-700">
                  Your previous payment proof was rejected. Please upload a new,
                  clear image of your payment receipt.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <DocumentUpload
              label="Upload New Payment Proof"
              description="Upload a clear image or PDF of your payment receipt"
              files={files}
              onChange={setFiles}
              accept="image/jpeg,image/jpg,image/png,application/pdf"
              maxFiles={1}
              maxSize={5 * 1024 * 1024} // 5MB
              required
            />
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              variant="secondary"
              onClick={() => setIsOpen(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitReupload}
              disabled={!uploadedFile || isUploading}
              className="flex-1"
            >
              {isUploading ? "Uploading..." : "Submit"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
