"use client";

import * as React from "react";
import { Upload, FileText, X, AlertCircle, Plus, RotateCcw, Trash2, Maximize2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { uploadDocument, uploadPaymentProof } from "@/lib/file-upload";
import { cn } from "@/lib/utils";

export interface DocumentFile {
  id: string;
  file: File;
  url?: string;
  status: "uploading" | "completed" | "error";
  error?: string;
  progress?: number;
  // Metadata captured at upload time so we can persist to storage/DB without needing the File object
  fileName?: string;
  fileSize?: number;
  fileType?: string;
}

interface DocumentUploadProps {
  label: string;
  description?: string;
  files: DocumentFile[];
  onChange: (files: DocumentFile[]) => void;
  accept?: string;
  maxFiles?: number;
  maxSize?: number;
  required?: boolean;
  className?: string;
  allowReplace?: boolean;
  // Optional folder within the bucket to upload into (defaults to 'requirements')
  uploadFolder?: string;
  // Optional user ID and booking ID for payment uploads
  userId?: string;
  bookingId?: string;
}

export function DocumentUpload({
  label,
  description,
  files,
  onChange,
  accept = "image/jpeg,image/jpg,image/png,application/pdf",
  maxFiles = 1,
  maxSize = 5 * 1024 * 1024, // 5MB
  required = false,
  className,
  allowReplace = true,
  uploadFolder = "requirements",
  userId,
  bookingId,
}: DocumentUploadProps) {
  const [dragActive, setDragActive] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const acceptedTypes = React.useMemo(() => {
    return accept.split(",").map((type) => type.trim());
  }, [accept]);

  const getFileTypeLabel = (fileOrType: File | string | undefined) => {
    if (!fileOrType) return "Document";
    const type = typeof fileOrType === "string" ? fileOrType : fileOrType.type;
    if (type.startsWith("image/")) return "Image";
    if (type === "application/pdf") return "PDF";
    return "Document";
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxSize) {
      return `File size must be less than ${formatFileSize(maxSize)}`;
    }

    // Check file type
    if (!acceptedTypes.includes(file.type)) {
      return `File type not supported. Please upload: ${acceptedTypes
        .map((type) => {
          if (type.startsWith("image/")) return "Images";
          if (type === "application/pdf") return "PDF";
          return "Documents";
        })
        .join(", ")}`;
    }

    return null;
  };

  const handleFileSelect = async (newFiles: FileList) => {
    setError(null);

    // For single file mode (maxFiles = 1), replace existing file
    if (maxFiles === 1 && files.length > 0) {
      // Clear existing files first
      onChange([]);
    } else {
      // Check max files limit for multi-file mode
      if (files.length + newFiles.length > maxFiles) {
        setError(`Maximum ${maxFiles} file(s) allowed`);
        return;
      }
    }

    const validFiles: DocumentFile[] = [];

    for (let i = 0; i < newFiles.length; i++) {
      const file = newFiles[i];
      const validationError = validateFile(file);

      if (validationError) {
        setError(validationError);
        continue;
      }

      const documentFile: DocumentFile = {
        id: `${Date.now()}-${Math.random().toString(36).substring(2)}`,
        file,
        status: "uploading",
        progress: 0,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
      };

      validFiles.push(documentFile);
    }

    if (validFiles.length > 0) {
      // For single file mode, replace completely; for multi-file, add to existing
      const updatedFiles = maxFiles === 1 ? validFiles : [...files, ...validFiles];
      onChange(updatedFiles);

      // Simulate upload progress (replace with actual upload logic)
      validFiles.forEach(async (docFile) => {
        try {
          // Update file to show uploading state
          const fileIndex = updatedFiles.findIndex((f) => f.id === docFile.id);
          if (fileIndex !== -1) {
            const newFiles = [...updatedFiles];
            newFiles[fileIndex] = { ...newFiles[fileIndex], progress: 10 };
            onChange(newFiles);
          }

          // Upload the actual file using appropriate method
          const uploadResult = uploadFolder === "payments" && userId
            ? await uploadPaymentProof(docFile.file, userId, bookingId)
            : await uploadDocument(docFile.file, uploadFolder);

          if (uploadResult.error) {
            throw new Error(uploadResult.error);
          }

          if (!uploadResult.url) {
            throw new Error("File upload succeeded but no URL was returned");
          }

          // Mark as completed with the uploaded URL
          const completedFileIndex = updatedFiles.findIndex(
            (f) => f.id === docFile.id
          );
          if (completedFileIndex !== -1) {
            const newFiles = [...updatedFiles];
            newFiles[completedFileIndex] = {
              ...newFiles[completedFileIndex],
              status: "completed",
              progress: 100,
              url: uploadResult.url || undefined,
            };
            onChange(newFiles);
          }
        } catch (err) {
          console.error("Upload error:", err);
          const errorIndex = updatedFiles.findIndex((f) => f.id === docFile.id);
          if (errorIndex !== -1) {
            const newFiles = [...updatedFiles];
            const errorMessage = err instanceof Error 
              ? err.message 
              : "Upload failed. Please try again.";
            
            newFiles[errorIndex] = {
              ...newFiles[errorIndex],
              status: "error",
              error: errorMessage,
              progress: 0,
            };
            onChange(newFiles);

            // Show toast notification for upload errors
            if (typeof window !== "undefined") {
              // Only show toast if we have a toast function available
              try {
                const { toast } = require("@/hooks/use-toast");
                toast({
                  title: "Upload Failed",
                  description: errorMessage,
                  variant: "destructive",
                });
              } catch {
                // Fallback if toast is not available
                console.warn("Toast notification not available");
              }
            }
          }
        }
      });
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFiles = e.target.files;
    if (newFiles && newFiles.length > 0) {
      handleFileSelect(newFiles);
    }
    // Reset input value to allow re-uploading the same file
    e.target.value = "";
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);

    const newFiles = e.dataTransfer.files;
    if (newFiles && newFiles.length > 0) {
      handleFileSelect(newFiles);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleRemoveFile = (fileId: string) => {
    const updatedFiles = files.filter((f) => f.id !== fileId);
    onChange(updatedFiles);
  };

  const handleReplaceFile = (fileId: string) => {
    // Remove the existing file and open file dialog
    handleRemoveFile(fileId);
    openFileDialog();
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const canAddMore = files.length < maxFiles;
  const hasFiles = files.length > 0;
  const firstFile = hasFiles ? files[0] : null;
  const isImage = firstFile && (
    (firstFile.file && firstFile.file.type?.startsWith('image/')) || 
    firstFile.fileType?.startsWith('image/')
  );

  // Create preview URL for images
  const [previewUrl, setPreviewUrl] = React.useState<string | null>(null);
  const [showFullScreen, setShowFullScreen] = React.useState(false);
  
  React.useEffect(() => {
    if (firstFile && isImage) {
      // If we already have a URL from existing document, use it
      if (firstFile.url) {
        setPreviewUrl(firstFile.url);
      } else if (firstFile.file && firstFile.file.size > 0) {
        // Only create object URL for real files with content
        const url = URL.createObjectURL(firstFile.file);
        setPreviewUrl(url);
        return () => URL.revokeObjectURL(url);
      }
    } else {
      setPreviewUrl(null);
    }
  }, [firstFile, isImage]);

  // Handle keyboard navigation for full-screen viewer
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && showFullScreen) {
        setShowFullScreen(false);
      }
    };
    
    if (showFullScreen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [showFullScreen]);

  return (
    <div className={cn("space-y-4", className)}>
      <div className="space-y-2">
        <label
          style={{
            fontSize: "14px",
            fontWeight: 500,
            color: "#212121",
            fontFamily: "'Inter','Helvetica Neue',Arial,sans-serif",
          }}
        >
          {label}
          {required ? " *" : ""}
        </label>
        {description && (
          <p
            style={{
              fontSize: "14px",
              color: "#616161",
              lineHeight: "20px",
            }}
          >
            {description}
          </p>
        )}
      </div>

      {/* Image Preview or Upload Area */}
      {hasFiles && isImage && previewUrl ? (
        <div 
          className="relative rounded-lg overflow-hidden group"
          style={{
            borderRadius: "12px",
            height: "200px",
            backgroundColor: "#F5F5F5"
          }}
        >
          <img 
            src={previewUrl}
            alt="Preview"
            className="w-full h-full object-cover cursor-pointer"
            onClick={() => setShowFullScreen(true)}
          />
          
          {/* Overlay Buttons */}
          <div className="absolute top-3 right-3 flex gap-2">
            {/* Full Screen Button */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowFullScreen(true);
              }}
              className="p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors"
              title="View full screen"
            >
              <Maximize2 className="w-4 h-4" style={{ color: '#616161' }} />
            </button>
            
            {/* Change Image Button */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                openFileDialog();
              }}
              className="flex items-center gap-2 px-3 py-2 rounded-lg text-white font-medium transition-colors"
              style={{
                backgroundColor: '#7B2CBF',
                fontSize: '14px'
              }}
            >
              <RotateCcw className="w-4 h-4" />
              Change Image
            </button>
          </div>
          
          {/* Delete Button */}
          <div className="absolute bottom-3 right-3">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveFile(firstFile.id);
              }}
              className="p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors"
              disabled={firstFile.status === "uploading"}
            >
              <Trash2 className="w-5 h-5" style={{ color: '#F44336' }} />
            </button>
          </div>
        </div>
      ) : hasFiles && !isImage ? (
        // Non-image file display
        <div className="flex flex-wrap gap-3 mb-4">
          {files.map((docFile) => (
            <div
              key={docFile.id}
              className="flex items-center gap-2 px-3 py-2 bg-gray-100 rounded-lg"
              style={{
                backgroundColor: "#F5F5F5",
                borderRadius: "8px",
                fontSize: "14px",
                color: "#212121",
              }}
            >
              <FileText className="w-4 h-4" style={{ color: "#616161" }} />
              <span className="text-sm">{docFile.fileName ?? docFile.file.name}</span>
              <button
                onClick={() => handleRemoveFile(docFile.id)}
                className="ml-1 p-1 hover:bg-gray-200 rounded-full transition-colors"
                disabled={docFile.status === "uploading"}
              >
                <X className="w-3 h-3" style={{ color: "#616161" }} />
              </button>
            </div>
          ))}
        </div>
      ) : (
        // Upload Area (when no files)
        <div
          className={cn(
            "border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
            dragActive
              ? "border-blue-400 bg-blue-50"
              : "border-gray-300 hover:border-gray-400"
          )}
          style={{
            borderColor: dragActive ? "#2196F3" : "#E0E0E0",
            backgroundColor: dragActive ? "#F8F9FA" : "#FAFAFA",
            borderRadius: "12px",
            minHeight: "120px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={openFileDialog}
        >
          <input
            type="file"
            accept={accept}
            multiple={maxFiles > 1}
            onChange={handleFileInputChange}
            className="hidden"
          />
          <div className="space-y-3">
            <div
              className="mx-auto flex items-center justify-center"
              style={{
                width: "48px",
                height: "48px",
                borderRadius: "12px",
                backgroundColor: "#E3F2FD",
              }}
            >
              <Upload
                className="w-6 h-6"
                style={{ color: "#2196F3" }}
              />
            </div>
            <div>
              <p
                className="font-medium"
                style={{
                  fontSize: "16px",
                  fontWeight: 500,
                  color: "#212121",
                  marginBottom: "8px",
                }}
              >
                {dragActive
                  ? "Drop files here"
                  : "Drag & drop files here, or click to browse"}
              </p>
              <p
                style={{
                  fontSize: "14px",
                  color: "#616161",
                }}
              >
                Supports:{" "}
                {accept
                  ?.split(",")
                  .map((type) => type.split("/")[1].toUpperCase())
                  .join(", ")}{" "}
                files up to {Math.round(maxSize / 1024 / 1024)}MB
              </p>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div 
          className="p-3 rounded-lg"
          style={{
            fontSize: '14px',
            color: '#E53935',
            backgroundColor: '#FFEBEE',
            border: '1px solid #FFCDD2',
            borderRadius: '8px'
          }}
        >
          {error}
        </div>
      )}

      {/* Full-Screen Image Viewer */}
      {showFullScreen && previewUrl && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"
          onClick={() => setShowFullScreen(false)}
        >
          <div className="relative max-w-[95vw] max-h-[95vh] flex items-center justify-center">
            {/* Close Button */}
            <button
              onClick={() => setShowFullScreen(false)}
              className="absolute top-4 right-4 p-3 bg-black bg-opacity-60 hover:bg-opacity-80 rounded-full transition-colors z-10 border-2 border-white border-opacity-30"
              style={{ backdropFilter: 'blur(10px)' }}
            >
              <X className="w-6 h-6 text-white" />
            </button>
            
            {/* Full-Screen Image */}
            <img 
              src={previewUrl}
              alt="Full screen preview"
              className="max-w-full max-h-full object-contain"
              onClick={(e) => e.stopPropagation()}
            />
            
            {/* Image Info */}
            <div className="absolute bottom-4 left-4 px-4 py-3 bg-black bg-opacity-70 rounded-lg border border-white border-opacity-20" style={{ backdropFilter: 'blur(10px)' }}>
              <p className="text-white text-sm font-medium">
                {firstFile?.fileName ?? firstFile?.file.name}
              </p>
              <p className="text-white text-xs opacity-90">
                {firstFile && formatFileSize(firstFile.file.size)}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={maxFiles > 1}
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  );
}
