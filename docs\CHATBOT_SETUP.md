# PathLink Chatbot Setup Guide

## Environment Variables Required

Add these variables to your `.env.local` file:

```bash
# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here

# OpenAI API for Embeddings (Required for RAG)
OPENAI_API_KEY=your_openai_api_key_here

# Existing Supabase Configuration (should already be set)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## API Keys Setup

### 1. OpenRouter API Key
1. Visit https://openrouter.ai/
2. Sign up for a free account
3. Navigate to Keys section
4. Create a new API key
5. Copy the key to `OPENROUTER_API_KEY`

### 2. OpenAI API Key (for embeddings)
1. Visit https://platform.openai.com/api-keys
2. Create a new API key
3. Copy the key to `OPENAI_API_KEY`

## Database Setup

Run the chatbot schema migration:

```bash
# Apply the chatbot database schema
psql -h your_supabase_host -p 5432 -d postgres -U postgres -f database/chatbot-knowledge-schema.sql
```

## Model Configuration

The chatbot uses OpenRouter's free `openai/gpt-oss-20b:free` model:
- **Rate Limits**: 10 requests/minute, 5000 tokens/minute
- **Temperature**: 0.7 for balanced creativity
- **Max Tokens**: 500 per response
- **Context Window**: Supports conversation history + RAG context

## Features

### Customer Chatbot
- Floating chat widget on customer pages
- RAG-enhanced responses using customer FAQ knowledge
- Session management with conversation history
- Real-time streaming responses

### Admin Chatbot  
- Admin-specific chat interface with security badges
- RAG-enhanced responses using admin documentation
- Role-based access control (admin/super_admin only)
- Administrative context and procedures

### RAG System
- Vector embeddings using OpenAI's text-embedding-ada-002
- Semantic search with cosine similarity
- Knowledge categorization (booking, payment, support, etc.)
- Source filtering (customer_faq, admin_docs, etc.)

## Usage

### Customer Side
The chatbot automatically appears on customer pages. Users can:
- Ask about booking procedures
- Get help with payments
- Learn about car rental policies
- Get general support

### Admin Side
Accessible only to admin/super_admin users. Admins can:
- Get help with booking management
- Ask about system procedures
- Get technical assistance
- Access operational guidance

## Knowledge Management

Add knowledge to the system using the `addKnowledge` function:

```typescript
import { addKnowledge, KNOWLEDGE_CATEGORIES, KNOWLEDGE_SOURCES } from '@/lib/rag';

await addKnowledge(
  "How to process refunds",
  "Refund processing involves...",
  KNOWLEDGE_SOURCES.ADMIN_DOCS,
  KNOWLEDGE_CATEGORIES.PAYMENT
);
```
