/**
 * Custom storage adapter for Supabase to ensure proper storage key isolation
 * This fixes the cookie inconsistency issues between admin and customer contexts
 */

import { logWithContext } from '../utils/logger';

export class IsolatedStorageAdapter {
  private storageKey: string

  constructor(storageKey: string) {
    this.storageKey = storageKey
    
    // Debug logging
    if (process.env.NODE_ENV === 'development') {
      logWithContext('StorageAdapter', `🔐 Creating isolated storage adapter with key: ${storageKey}`)
    }
  }

  async getItem(key: string): Promise<string | null> {
    if (typeof window === 'undefined') return null

    const fullKey = `${this.storageKey}.${key}`
    const value = window.localStorage.getItem(fullKey)

    // Enhanced logging for debugging session storage issue
    if (process.env.NODE_ENV === 'development') {
      logWithContext('StorageAdapter', `📖 Storage GET [${this.storageKey}]: ${key} = ${value ? 'found' : 'null'}`)
    }

    return value
  }

  async setItem(key: string, value: string): Promise<void> {
    if (typeof window === 'undefined') return

    const fullKey = `${this.storageKey}.${key}`

    if (process.env.NODE_ENV === 'development') {
      logWithContext('StorageAdapter', `💾 Storage SET [${this.storageKey}]: ${key} = ${value ? `${value.substring(0, 50)}...` : 'null'}`)
      logWithContext('StorageAdapter', `💾 Full key: ${fullKey}`)
    }

    window.localStorage.setItem(fullKey, value)

    // Verify the item was actually stored
    if (process.env.NODE_ENV === 'development') {
      const stored = window.localStorage.getItem(fullKey)
      logWithContext('StorageAdapter', `✅ Storage verification: ${stored ? 'SUCCESS' : 'FAILED'}`)
    }
  }

  async removeItem(key: string): Promise<void> {
    if (typeof window === 'undefined') return
    
    const fullKey = `${this.storageKey}.${key}`
    window.localStorage.removeItem(fullKey)
    
    // Enhanced logging for debugging session storage issue
    if (process.env.NODE_ENV === 'development') {
      logWithContext('StorageAdapter', `🗑️ Storage REMOVE [${this.storageKey}]: ${key}`)
      logWithContext('StorageAdapter', `🗑️ Full key: ${fullKey}`)
    }
  }
}

/**
 * Legacy storage adapter for backward compatibility with existing tokens
 */
export class LegacyStorageAdapter {
  private storageKey: string

  constructor(storageKey: string) {
    this.storageKey = storageKey
  }

  async getItem(key: string): Promise<string | null> {
    if (typeof window === 'undefined') return null
    return window.localStorage.getItem(key)
  }

  async setItem(key: string, value: string): Promise<void> {
    if (typeof window === 'undefined') return
    window.localStorage.setItem(key, value)
  }

  async removeItem(key: string): Promise<void> {
    if (typeof window === 'undefined') return
    window.localStorage.removeItem(key)
  }
}
