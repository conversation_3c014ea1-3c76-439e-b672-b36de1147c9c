# MQTT Migration Guide: ESP32 GPS Tracking

## Overview
This guide will help you migrate from HTTP-based GPS tracking to MQTT-based real-time tracking using HiveMQ Cloud while maintaining database persistence for historical data and device management.

## Prerequisites

### 1. HiveMQ Cloud Setup
1. Create a [HiveMQ Cloud](https://www.hivemq.com/mqtt-cloud-broker/) account
2. Create a new cluster 
3. Note down:
   - **Cluster URL**: `your-cluster-url.hivemq.cloud`
   - **Username**: Your created username
   - **Password**: Your created password

### 2. Environment Configuration
Add to your `.env.local`:

```env
# MQTT Configuration
HIVEMQ_BROKER_URL=wss://your-cluster-url.hivemq.cloud:8884/mqtt
HIVEMQ_USERNAME=your_hivemq_username
HIVEMQ_PASSWORD=your_hivemq_password
START_MQTT_CONSUMER=true

# GPS Configuration
NEXT_PUBLIC_GPS_STALE_SECONDS=60
INBOUND_DEVICE_TOKEN=pathlink-gps-device-2024
```

## Migration Steps

### Phase 1: Backend Setup (No Breaking Changes)

#### Step 1: Install Dependencies
```bash
pnpm install concurrently tsx
```

#### Step 2: Start MQTT Consumer
```bash
# Development (alongside Next.js)
pnpm run dev:with-mqtt

# Or separately
pnpm run mqtt:consumer
```

#### Step 3: Test MQTT Connection
Check console logs for:
```
✅ MQTT Consumer connected to HiveMQ Cloud
📡 Subscribed to pathlink/gps/+ topics
```

### Phase 2: ESP32 Firmware Update

#### Step 1: Update Firmware
1. Copy `iot/GPS/src/mqtt-main.cpp` to `iot/GPS/src/main.cpp`
2. Update credentials in firmware:
   ```cpp
   #define MQTT_BROKER   "your-cluster-url.hivemq.cloud"
   #define MQTT_USERNAME "your_hivemq_username"
   #define MQTT_PASSWORD "your_hivemq_password"
   ```

#### Step 2: Flash Updated Firmware
```bash
cd iot/GPS
platformio run --target upload
```

#### Step 3: Monitor MQTT Messages
- Check HiveMQ Cloud dashboard for incoming messages
- Topic format: `pathlink/gps/lilygo-esp32-01`
- Payload example:
  ```json
  {
    "deviceId": "lilygo-esp32-01",
    "lat": 14.691624,
    "lng": 121.042839,
    "speed": 0,
    "heading": 0,
    "timestamp": 1234567890
  }
  ```

### Phase 3: API Consolidation (Optional)

#### Step 1: Update ESP32 HTTP Fallback
Your ESP32 can now send to the enhanced `/api/gps/ingest` endpoint which handles both encrypted tokens and regular JSON.

#### Step 2: Remove Redundant APIs
Once MQTT is fully working, you can safely remove:
- `/api/gps/tracker/route.ts` (functionality moved to ingest)
- `/api/gps/latest/route.ts` (redundant with current)

## Architecture Comparison

### Before (HTTP Only)
```
ESP32 → HTTP POST → /api/gps/ingest → Database → Frontend Polling (5s)
```
**Latency**: 5-30 seconds

### After (MQTT + HTTP Hybrid)
```
ESP32 → MQTT → Consumer → Database → WebSocket → Frontend
      ↓
  HTTP Fallback → /api/gps/ingest → Database → Frontend Polling
```
**Latency**: <1 second (MQTT) + 5-30s fallback

## Benefits of New Architecture

### ✅ **Why Keep Database**
1. **Historical Analytics**: Route analysis, driver behavior, fleet metrics
2. **Device Management**: ESP32 ↔ Car mapping, authentication, status tracking
3. **Offline Resilience**: System recovery when MQTT is unavailable
4. **Admin Features**: Device monitoring, configuration management

### ✅ **MQTT Advantages**
1. **Real-Time Updates**: Sub-second latency for live tracking
2. **Bi-Directional**: Can send commands back to ESP32
3. **Efficient**: Lower bandwidth than HTTP polling
4. **Scalable**: Handles multiple devices simultaneously

### ✅ **Hybrid Approach**
- **Primary**: MQTT for real-time updates
- **Fallback**: HTTP API for reliability
- **Storage**: Database for persistence and analytics
- **Frontend**: Dual data sources (WebSocket + API polling)

## Monitoring & Troubleshooting

### Health Checks
1. **MQTT Consumer Status**: Check console logs
2. **Database Inserts**: Monitor `gps_locations` table
3. **Device Status**: Check `gps_device_mapping.connection_status`
4. **Frontend Updates**: Admin tracker map should update in real-time

### Common Issues
1. **MQTT Connection Failed**: Verify HiveMQ credentials
2. **ESP32 Not Publishing**: Check cellular connection and MQTT credentials
3. **Database Inserts Failing**: Verify device mapping exists
4. **Frontend Not Updating**: Ensure WebSocket/SSE implementation is active

## Rollback Plan
If issues occur, you can easily revert:
1. Flash original HTTP firmware to ESP32
2. Stop MQTT consumer service
3. Continue using existing HTTP APIs
4. Database and device mappings remain unchanged

## Performance Expectations

### Target Metrics
- **MQTT Message Delivery**: <1 second P95
- **Database Insert Latency**: <2 seconds P95  
- **Frontend Update Latency**: <3 seconds P95
- **Device Online Rate**: >98% uptime

### Cost Implications
- **HiveMQ Cloud**: ~$49/month for moderate usage
- **Reduced API Calls**: Lower server costs from reduced HTTP polling
- **Database**: Same storage, potentially lower query costs

## Next Steps
1. Set up HiveMQ Cloud cluster
2. Configure environment variables
3. Start MQTT consumer in development
4. Test with current HTTP firmware first
5. Gradually migrate ESP32 devices to MQTT
6. Monitor performance and reliability
7. Implement WebSocket/SSE for real-time frontend updates

## Support
- Check console logs for detailed error messages
- Monitor HiveMQ Cloud dashboard for connection status
- Use admin GPS devices page to monitor device health
- Database functions remain unchanged for compatibility
