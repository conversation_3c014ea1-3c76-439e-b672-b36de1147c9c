"use client";

import React from "react";
import Image from "next/image";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";
import { 
  Calendar, 
  Clock, 
  Car, 
  MapPin, 
  User, 
  Phone, 
  CheckCircle2, 
  AlertCircle, 
  ArrowRight,
  X,
  Sparkles,
  Timer,
  Calculator,
  Info,
  CheckCircle,
  CreditCard,
  AlertTriangle,
  Users
} from "lucide-react";
import { format, addHours, differenceInHours } from "date-fns";
import { cn } from "@/lib/utils";
import { createClient } from "@/lib/supabase/client";
import { 
  checkExtensionAvailability, 
  createExtensionRequest,
  type ExtensionAvailabilityCheck,
  type AlternativeCar 
} from "@/lib/services/booking-extension-service";
import { DatePicker } from "@/components/customer-side/date/DatePicker";
import { TimePicker } from "@/components/customer-side/time/TimePicker";

interface ExtensionRequestModalProps {
  booking: {
    id: string;
    status: string;
    pickup_location: string;
    dropoff_location: string;
    pickup_datetime: string;
    dropoff_datetime: string;
    total_amount: number;
    cars?: {
      id: string;
      model: string;
      type: string;
      image_url: string;
      price_per_day: number;
      plate_number?: string;
    } | null;
    customers?: {
      full_name: string;
      email?: string;
    } | null;
  } | null;
  isOpen: boolean;
  onClose: () => void;
  onExtensionCreated?: () => void;
}

type ExtensionStep = "request" | "availability" | "alternatives" | "confirmation";

export function ExtensionRequestModal({ 
  booking, 
  isOpen, 
  onClose, 
  onExtensionCreated 
}: ExtensionRequestModalProps) {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  
  const [currentStep, setCurrentStep] = React.useState<ExtensionStep>("request");
  const [loading, setLoading] = React.useState(false);
  const [submitting, setSubmitting] = React.useState(false);
  
  // Form state
  const [extensionDate, setExtensionDate] = React.useState("");
  const [extensionTime, setExtensionTime] = React.useState("");
  const [reason, setReason] = React.useState("");
  const [notes, setNotes] = React.useState("");
  const [selectedAlternativeCar, setSelectedAlternativeCar] = React.useState<string | null>(null);
  
  // Availability check result
  const [availabilityResult, setAvailabilityResult] = React.useState<ExtensionAvailabilityCheck | null>(null);

  // Reset form when modal opens
  React.useEffect(() => {
    if (isOpen && booking) {
      setCurrentStep("request");
      setExtensionDate("");
      setExtensionTime("");
      setReason("");
      setNotes("");
      setSelectedAlternativeCar(null);
      setAvailabilityResult(null);
    }
  }, [isOpen, booking]);

  if (!booking || booking.status !== "Active") {
    return null;
  }

  const originalDropoff = new Date(booking.dropoff_datetime);
  const minExtensionDate = format(addHours(originalDropoff, 1), "yyyy-MM-dd");
  const minExtensionTime = format(addHours(originalDropoff, 1), "HH:mm");

  const handleCheckAvailability = async () => {
    if (!extensionDate || !extensionTime) {
      toast({
        variant: "destructive",
        title: "Invalid Input",
        description: "Please select both date and time for the extension.",
      });
      return;
    }

    const requestedDateTime = new Date(`${extensionDate}T${extensionTime}`);
    if (requestedDateTime <= originalDropoff) {
      toast({
        variant: "destructive",
        title: "Invalid Extension Time",
        description: "Extension time must be after the original return time.",
      });
      return;
    }

    setLoading(true);
    try {
      const { data: availability, error } = await checkExtensionAvailability(
        booking.id,
        requestedDateTime.toISOString()
      );

      if (error) {
        throw new Error(error.message || "Failed to check availability");
      }

      if (!availability) {
        throw new Error("Unable to check availability");
      }

      setAvailabilityResult(availability);
      setCurrentStep("availability");
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Availability Check Failed",
        description: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleShowAlternatives = () => {
    setCurrentStep("alternatives");
  };

  const handleSubmitRequest = async () => {
    if (!availabilityResult) return;

    setSubmitting(true);
    try {
      // Get current authenticated user
      const supabase = createClient();
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        throw new Error("You must be logged in to submit an extension request");
      }

      const requestedDateTime = new Date(`${extensionDate}T${extensionTime}`);
      
      const { data: extensionRequest, error } = await createExtensionRequest({
        bookingId: booking.id,
        customerId: user.id, // Use the authenticated user's ID
        originalDropoffDatetime: booking.dropoff_datetime,
        requestedDropoffDatetime: requestedDateTime.toISOString(),
        requestReason: reason,
        requestNotes: notes,
      });

      if (error) {
        throw new Error(error.message || "Failed to create extension request");
      }

      setCurrentStep("confirmation");
      toast({
        title: "Extension Request Submitted",
        description: "Your booking extension request has been sent for admin approval.",
      });

      if (onExtensionCreated) {
        onExtensionCreated();
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Request Failed",
        description: error.message,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-PH", {
      style: "currency",
      currency: "PHP",
    }).format(amount);
  };

  const formatDateTime = (dateString: string) => {
    return format(new Date(dateString), "MMM dd, yyyy 'at' h:mm a");
  };

  const calculateExtensionHours = () => {
    if (!extensionDate || !extensionTime) return 0;
    const requestedDateTime = new Date(`${extensionDate}T${extensionTime}`);
    return differenceInHours(requestedDateTime, originalDropoff);
  };

  const estimateAdditionalCost = () => {
    const hours = calculateExtensionHours();
    if (hours <= 0 || !booking.cars?.price_per_day) return 0;
    return Math.ceil(hours / 24) * booking.cars.price_per_day;
  };

  const renderRequestStep = () => (
    <div className="space-y-8">
      {/* Modern Header with Gradient */}
      <div className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 rounded-2xl p-6 text-white overflow-hidden">
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
        <div className="relative z-10">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
              <Sparkles className="h-6 w-6" />
            </div>
            <div>
              <h3 className="text-xl font-bold">Extend Your Journey</h3>
              <p className="text-blue-100 text-sm">Add more time to your rental with ease</p>
            </div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <div className="flex items-center gap-2 mb-2">
              <Timer className="h-4 w-4 text-blue-200" />
              <span className="text-sm font-medium text-blue-100">Current Return Time</span>
            </div>
            <p className="text-white font-semibold">{formatDateTime(booking.dropoff_datetime)}</p>
          </div>
        </div>
      </div>

      {/* Modern Date/Time Selection */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-gray-50 to-white">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-gray-900">
            <Calendar className="h-5 w-5 text-blue-600" />
            Select New Return Time
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="extension-date" className="text-sm font-medium text-gray-700">
                New Return Date
              </Label>
              <DatePicker
                value={extensionDate}
                onChange={setExtensionDate}
                placeholder="Select new return date"
                minDate={minExtensionDate}
                aria-label="New return date"
                className="border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="extension-time" className="text-sm font-medium text-gray-700">
                New Return Time
              </Label>
              <TimePicker
                value={extensionTime}
                onChange={setExtensionTime}
                placeholder="Select new return time"
                minTime="06:00"
                maxTime="22:00"
                step={60}
                showQuickActions={true}
                showSecondaryFormat={false}
                aria-label="New return time"
                className="border-gray-300 bg-white hover:border-blue-400 focus:border-blue-600 focus:ring-2 focus:ring-blue-100"
              />
            </div>
          </div>

          {extensionDate && extensionTime && (
            <div className="relative bg-gradient-to-br from-emerald-50 via-emerald-100 to-teal-100 border-0 rounded-xl p-6 overflow-hidden">
              <div className="absolute top-0 right-0 w-20 h-20 bg-emerald-200/30 rounded-full -translate-y-10 translate-x-10"></div>
              <div className="relative z-10">
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-2 bg-emerald-600/10 rounded-lg">
                    <Calculator className="h-5 w-5 text-emerald-700" />
                  </div>
                  <span className="font-semibold text-emerald-900">Extension Summary</span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4">
                    <p className="text-sm text-emerald-700 mb-1">Extension Duration</p>
                    <p className="text-xl font-bold text-emerald-900">{calculateExtensionHours()} hours</p>
                  </div>
                  <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4">
                    <p className="text-sm text-emerald-700 mb-1">Estimated Cost</p>
                    <p className="text-xl font-bold text-emerald-900">{formatCurrency(estimateAdditionalCost())}</p>
                  </div>
                </div>
                <p className="text-xs text-emerald-600 mt-3 flex items-center gap-1">
                  <Info className="h-3 w-3" />
                  Final cost will be confirmed after availability check
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modern Reason/Notes Section */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-gray-50 to-white">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-gray-900">
            <Info className="h-5 w-5 text-blue-600" />
            Additional Information
          </CardTitle>
          <p className="text-sm text-gray-600 mt-1">Help us understand your extension needs</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="reason" className="text-sm font-medium text-gray-700">
              Reason for Extension (Optional)
            </Label>
            <div className="relative">
              <div className="flex items-center gap-3 border border-gray-300 rounded-lg px-4 py-3 bg-white focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all">
                <AlertCircle className="h-5 w-5 text-blue-600 flex-shrink-0" />
                <Input
                  id="reason"
                  placeholder="e.g., Flight delay, extended business meeting..."
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  className="border-0 p-0 focus:outline-none focus:ring-0 w-full"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes" className="text-sm font-medium text-gray-700">
              Additional Notes (Optional)
            </Label>
            <div className="relative">
              <div className="border border-gray-300 rounded-lg p-4 bg-white focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all">
                <Textarea
                  id="notes"
                  placeholder="Any additional information that might help us process your request..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  className="border-0 p-0 focus:outline-none focus:ring-0 w-full resize-none"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderAvailabilityStep = () => {
    if (!availabilityResult) return null;

    return (
      <div className="space-y-8">
        {/* Modern Status Header */}
        <div className="relative">
          {availabilityResult.isAvailable ? (
            <div className="relative bg-gradient-to-br from-emerald-600 via-emerald-700 to-green-700 rounded-2xl p-6 text-white overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
              <div className="relative z-10 text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-4 bg-white/20 rounded-full backdrop-blur-sm">
                    <CheckCircle className="h-8 w-8" />
                  </div>
                </div>
                <h3 className="text-2xl font-bold mb-2">Extension Available!</h3>
                <p className="text-emerald-100">
                  Great news! Your vehicle is available for the requested extension period.
                </p>
              </div>
            </div>
          ) : (
            <div className="relative bg-gradient-to-br from-amber-600 via-amber-700 to-orange-700 rounded-2xl p-6 text-white overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
              <div className="relative z-10 text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-4 bg-white/20 rounded-full backdrop-blur-sm">
                    <AlertCircle className="h-8 w-8" />
                  </div>
                </div>
                <h3 className="text-2xl font-bold mb-2">Scheduling Conflict</h3>
                <p className="text-amber-100">
                  Your current vehicle has conflicts, but we found alternatives for you.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Modern Extension Details Card */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-gray-50 to-white">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-gray-900">
              <Calculator className="h-5 w-5 text-blue-600" />
              Extension Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 overflow-hidden">
                <div className="absolute top-0 right-0 w-16 h-16 bg-blue-200/30 rounded-full -translate-y-8 translate-x-8"></div>
                <div className="relative z-10">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-5 w-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Duration</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-900">{availabilityResult.extensionHours} hours</p>
                </div>
              </div>
              <div className="relative bg-gradient-to-br from-emerald-50 to-green-100 rounded-xl p-6 overflow-hidden">
                <div className="absolute top-0 right-0 w-16 h-16 bg-emerald-200/30 rounded-full -translate-y-8 translate-x-8"></div>
                <div className="relative z-10">
                  <div className="flex items-center gap-2 mb-2">
                    <CreditCard className="h-5 w-5 text-emerald-600" />
                    <span className="text-sm font-medium text-emerald-900">Additional Cost</span>
                  </div>
                  <p className="text-2xl font-bold text-emerald-900">
                    {formatCurrency(availabilityResult.additionalCost)}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Modern Conflicts Section */}
        {availabilityResult.hasConflicts && (
          <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-orange-50">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-amber-900">
                <AlertTriangle className="h-5 w-5 text-amber-600" />
                Scheduling Conflicts
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                {availabilityResult.conflicts.map((conflict, index) => (
                  <div key={index} className="bg-white rounded-lg p-4 border border-amber-200 shadow-sm">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-amber-100 rounded-lg">
                        <Users className="h-4 w-4 text-amber-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-semibold text-amber-900">{conflict.customerName}</p>
                        <p className="text-sm text-amber-700">
                          {formatDateTime(conflict.conflictPeriod.start)} - {formatDateTime(conflict.conflictPeriod.end)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {availabilityResult.alternativeCars.length > 0 && (
                <div className="pt-4">
                  <Button
                    variant="secondary"
                    onClick={handleShowAlternatives}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 h-12"
                  >
                    <Car className="h-5 w-5 mr-2" />
                    View Alternative Vehicles ({availabilityResult.alternativeCars.length})
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  const renderAlternativesStep = () => {
    if (!availabilityResult?.alternativeCars.length) return null;

    return (
      <div className="space-y-8">
        {/* Modern Header */}
        <div className="relative bg-gradient-to-br from-indigo-600 via-purple-700 to-pink-700 rounded-2xl p-6 text-white overflow-hidden">
          <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
          <div className="relative z-10 text-center">
            <div className="flex justify-center mb-4">
              <div className="p-4 bg-white/20 rounded-full backdrop-blur-sm">
                <Car className="h-8 w-8" />
              </div>
            </div>
            <h3 className="text-2xl font-bold mb-2">Alternative Vehicles</h3>
            <p className="text-indigo-100">
              These premium vehicles are available for your extension period
            </p>
          </div>
        </div>

        {/* Modern Vehicle Selection */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-gray-50 to-white">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-gray-900">
              <Car className="h-5 w-5 text-blue-600" />
              Choose Your Vehicle
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">Select an alternative vehicle that suits your needs</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {availabilityResult.alternativeCars.map((car) => (
                <div
                  key={car.id}
                  className={cn(
                    "relative bg-white rounded-xl border-2 p-6 cursor-pointer transition-all duration-200 hover:shadow-lg",
                    selectedAlternativeCar === car.id 
                      ? "border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 ring-2 ring-blue-500/20" 
                      : "border-gray-200 hover:border-gray-300"
                  )}
                  onClick={() => setSelectedAlternativeCar(car.id)}
                >
                  <div className="flex items-center gap-6">
                    {car.imageUrl && (
                      <div className="relative h-20 w-28 rounded-xl overflow-hidden bg-gray-100 shadow-sm">
                        <Image
                          src={car.imageUrl}
                          alt={car.model}
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="text-lg font-bold text-gray-900">{car.model}</h4>
                        <Badge 
                          variant="secondary" 
                          className="bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border-0"
                        >
                          {car.type}
                        </Badge>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <div className="p-1 bg-gray-100 rounded">
                            <Car className="h-3 w-3" />
                          </div>
                          <span>Plate: {car.plateNumber}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="p-1 bg-emerald-100 rounded">
                            <CreditCard className="h-3 w-3 text-emerald-600" />
                          </div>
                          <span className="text-lg font-bold text-emerald-600">
                            {formatCurrency(car.pricePerDay)}/day
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      {selectedAlternativeCar === car.id ? (
                        <div className="p-2 bg-blue-600 rounded-full">
                          <CheckCircle className="h-6 w-6 text-white" />
                        </div>
                      ) : (
                        <div className="p-2 border-2 border-gray-300 rounded-full">
                          <div className="h-6 w-6"></div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {selectedAlternativeCar === car.id && (
                    <div className="absolute top-4 right-4">
                      <div className="bg-blue-600 text-white text-xs font-medium px-2 py-1 rounded-full">
                        Selected
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Modern Info Section */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-indigo-50">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <div className="p-3 bg-blue-600/10 rounded-xl">
                <Info className="h-6 w-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-blue-900 mb-2">Important Information</h4>
                <div className="space-y-2 text-sm text-blue-800">
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span>Selecting an alternative vehicle requires admin approval</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span>Vehicle handover arrangements will be coordinated by our team</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span>Additional costs may apply based on the new vehicle's rate</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderConfirmationStep = () => (
    <div className="space-y-8">
      {/* Modern Success Header */}
      <div className="relative bg-gradient-to-br from-emerald-600 via-green-700 to-teal-700 rounded-2xl p-8 text-white overflow-hidden">
        <div className="absolute top-0 right-0 w-40 h-40 bg-white/10 rounded-full -translate-y-20 translate-x-20"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/5 rounded-full translate-y-16 -translate-x-16"></div>
        <div className="relative z-10 text-center">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-white/20 rounded-full backdrop-blur-sm">
              <CheckCircle className="h-12 w-12" />
            </div>
          </div>
          <h3 className="text-3xl font-bold mb-3">Request Submitted Successfully!</h3>
          <p className="text-emerald-100 text-lg">
            Your booking extension request has been sent to our admin team for review.
          </p>
        </div>
      </div>

      {/* Modern Next Steps Card */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-gray-50 to-white">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-gray-900">
            <ArrowRight className="h-5 w-5 text-blue-600" />
            What Happens Next?
          </CardTitle>
          <p className="text-sm text-gray-600 mt-1">Here's what you can expect from our team</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl">
              <div className="p-2 bg-blue-600/10 rounded-lg flex-shrink-0">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h4 className="font-semibold text-blue-900 mb-1">Admin Review</h4>
                <p className="text-sm text-blue-700">Our admin team will carefully review your extension request</p>
              </div>
            </div>
            
            <div className="flex items-start gap-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
              <div className="p-2 bg-purple-600/10 rounded-lg flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <h4 className="font-semibold text-purple-900 mb-1">Email Notification</h4>
                <p className="text-sm text-purple-700">You'll receive an email with our decision and next steps</p>
              </div>
            </div>
            
            <div className="flex items-start gap-4 p-4 bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl">
              <div className="p-2 bg-emerald-600/10 rounded-lg flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <h4 className="font-semibold text-emerald-900 mb-1">Automatic Update</h4>
                <p className="text-sm text-emerald-700">If approved, your booking will be automatically updated</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modern Request Details Card */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-indigo-50 to-purple-50">
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full">
              <Calendar className="h-4 w-4 text-indigo-600" />
              <span className="text-sm font-medium text-indigo-900">Request Details</span>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-center gap-2 text-indigo-800">
                <span className="text-sm">Request ID:</span>
                <code className="bg-white/60 px-2 py-1 rounded text-sm font-mono">
                  #{booking.id.slice(-8)}-EXT
                </code>
              </div>
              <div className="flex items-center justify-center gap-2 text-indigo-700">
                <Clock className="h-4 w-4" />
                <span className="text-sm">
                  Submitted: {format(new Date(), "MMM dd, yyyy 'at' h:mm a")}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const getStepButtons = () => {
    switch (currentStep) {
      case "request":
        return (
          <>
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleCheckAvailability}
              disabled={!extensionDate || !extensionTime || loading}
            >
              {loading ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Checking...
                </>
              ) : (
                "Request Extension"
              )}
            </Button>
          </>
        );
      
      case "availability":
        return (
          <>
            <Button variant="secondary" onClick={() => setCurrentStep("request")}>
              Back
            </Button>
            <Button 
              onClick={handleSubmitRequest}
              disabled={submitting}
              className="bg-green-600 hover:bg-green-700"
            >
              {submitting ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                "Submit Request"
              )}
            </Button>
          </>
        );
      
      case "alternatives":
        return (
          <>
            <Button variant="secondary" onClick={() => setCurrentStep("availability")}>
              Back
            </Button>
            <Button 
              onClick={handleSubmitRequest}
              disabled={submitting || !selectedAlternativeCar}
              className="bg-green-600 hover:bg-green-700"
            >
              {submitting ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                "Submit with Alternative"
              )}
            </Button>
          </>
        );
      
      case "confirmation":
        return (
          <Button onClick={onClose} className="w-full">
            Done
          </Button>
        );
      
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn(
        "max-w-2xl w-[90vw]",
        isMobile ? "h-[90vh] max-h-[90vh]" : "max-h-[80vh]"
      )}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Extend Booking
          </DialogTitle>
        </DialogHeader>

        <div className={cn(
          "flex-1",
          isMobile ? "overflow-y-auto" : ""
        )}>
          {currentStep === "request" && renderRequestStep()}
          {currentStep === "availability" && renderAvailabilityStep()}
          {currentStep === "alternatives" && renderAlternativesStep()}
          {currentStep === "confirmation" && renderConfirmationStep()}
        </div>

        <DialogFooter className="flex gap-2">
          {getStepButtons()}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
