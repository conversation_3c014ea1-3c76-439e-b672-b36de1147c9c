"use client";

import * as React from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DocumentUpload,
  type DocumentFile,
} from "@/components/ui/document-upload";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  CreditCard,
  Smartphone,
  Banknote,
  Upload,
  CheckCircle,
  AlertCircle,
  InfoIcon,
  Shield,
  Copy,
  Check,
  Building,
  MapPin,
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import type { BookingData } from "./booking-flow";
import { cn } from "@/lib/utils";
import { formatCurrency } from "@/lib/utils/format-currency";
import { calculateDeli<PERSON><PERSON><PERSON><PERSON>, calculateReturn<PERSON>ee, isNightTime } from "@/lib/delivery-fees";
import { useCustomerAuth } from "@/components/auth/customer-auth-context";

interface PaymentProofStepProps {
  bookingData: BookingData;
  onUpdate: (updates: Partial<BookingData>) => void;
}

type PaymentMethod = "GCash" | "Bank Transfer" | "Remittance Center";

interface PaymentMethodData {
  id: PaymentMethod;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  accountName: string;
  accountNumber: string;
  instructions: string[];
}

const PAYMENT_METHODS: PaymentMethodData[] = [
  {
    id: "GCash",
    name: "GCash",
    icon: Smartphone,
    description: "Mobile wallet payment via GCash app",
    accountName: "Joel Jr Maltezo Riosa",
    accountNumber: "***********",
    instructions: [
      "Open your GCash app and select 'Send Money'",
      "Enter the GCash number provided above",
      "Input the exact downpayment amount (50% of total)",
      "Complete the transaction",
      "Take a screenshot of the successful transaction",
      "Upload the screenshot as proof of payment",
    ],
  },
  {
    id: "Bank Transfer",
    name: "Bank Transfer",
    icon: Building,
    description: "Bank-to-bank transfer or over-the-counter deposit",
    accountName: "Joel Jr Maltezo Riosa",
    accountNumber: "****************", // Replace with actual bank account
    instructions: [
      "Visit your bank or use online banking",
      "Transfer to the bank account provided above",
      "Input the exact downpayment amount (50% of total)",
      "Complete the transaction",
      "Take a photo of the deposit slip or transfer receipt",
      "Upload the receipt as proof of payment",
    ],
  },
  {
    id: "Remittance Center",
    name: "Remittance Center",
    icon: MapPin,
    description: "Send money via remittance centers (Palawan, Cebuana, etc.)",
    accountName: "Joel Jr Maltezo Riosa",
    accountNumber: "Reference: OLLIE2024", // Replace with actual reference
    instructions: [
      "Visit any remittance center (Palawan Express, Cebuana Lhuillier, etc.)",
      "Fill out the send money form with receiver details above",
      "Send the exact downpayment amount (50% of total)",
      "Complete the transaction and get your receipt",
      "Take a photo of the remittance receipt",
      "Upload the receipt as proof of payment",
    ],
  },
];

export function PaymentProofStep({
  bookingData,
  onUpdate,
}: PaymentProofStepProps) {
  const { user } = useCustomerAuth();
  const [selectedMethod, setSelectedMethod] =
    React.useState<PaymentMethod | null>(
      (bookingData.paymentMethod as PaymentMethod) || null
    );
  const [copiedStates, setCopiedStates] = React.useState<
    Record<PaymentMethod, boolean>
  >({
    "GCash": false,
    "Bank Transfer": false,
    "Remittance Center": false,
  });
  const [gcashReferenceError, setGcashReferenceError] = React.useState<string>("");

  const selectedMethodData = PAYMENT_METHODS.find(
    (m) => m.id === selectedMethod
  );
  const isDigitalPayment = selectedMethod !== null;

  const handleMethodSelect = (method: PaymentMethod) => {
    setSelectedMethod(method);
    onUpdate({
      paymentMethod: method,
      // Keep existing proof of payment files when switching methods
      proofOfPayment: bookingData.proofOfPayment,
      // Clear GCash reference number if switching away from GCash
      gcashReferenceNumber: method === "GCash" ? bookingData.gcashReferenceNumber : "",
    });
    // Clear validation error when switching methods
    setGcashReferenceError("");
  };

  const handleFileChange = (files: DocumentFile[]) => {
    onUpdate({ proofOfPayment: files });
  };

  const validateGCashReference = (value: string): string => {
    if (!value || value.trim().length === 0) {
      return "GCash reference number is required";
    }
    
    // Remove spaces and special characters for validation
    const cleanValue = value.replace(/[^0-9]/g, "");
    
    // GCash reference numbers are typically 13 digits
    if (cleanValue.length < 10) {
      return "Reference number is too short (minimum 10 digits)";
    }
    
    if (cleanValue.length > 20) {
      return "Reference number is too long (maximum 20 digits)";
    }
    
    return "";
  };

  const handleGCashReferenceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    onUpdate({ gcashReferenceNumber: value });
    
    // Validate in real-time
    const error = validateGCashReference(value);
    setGcashReferenceError(error);
  };

  const copyToClipboard = async (text: string, method: PaymentMethod) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStates((prev) => ({ ...prev, [method]: true }));
      toast({
        title: "Copied!",
        description: `${method} number copied to clipboard`,
      });
      setTimeout(() => {
        setCopiedStates((prev) => ({ ...prev, [method]: false }));
      }, 2000);
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Please copy the number manually",
        variant: "destructive",
      });
    }
  };

  const calculateTotalAmount = (bookingData: BookingData) => {
    if (!bookingData.selectedCar || !bookingData.pickUpDate || !bookingData.dropOffDate) {
      return 0;
    }

    const pickupDate = new Date(bookingData.pickUpDate);
    const dropoffDate = new Date(bookingData.dropOffDate);
    const diffTime = Math.abs(dropoffDate.getTime() - pickupDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Calculate base rental price
    const basePrice = diffDays * bookingData.selectedCar.price_per_day;

    // Calculate delivery fee based on pickup location and time
    const deliveryFee = calculateDeliveryFee(
      bookingData.pickUpLocation || "",
      bookingData.pickUpTime || ""
    );

    // Calculate return fee based on dropoff location and time
    const returnFee = calculateReturnFee(
      bookingData.dropOffLocation || "",
      bookingData.dropOffTime || ""
    );

    // Return total amount
    return basePrice + deliveryFee + returnFee;
  };

  // Calculate individual fee components for display
  const calculateFeeComponents = (bookingData: BookingData) => {
    if (!bookingData.selectedCar || !bookingData.pickUpDate || !bookingData.dropOffDate) {
      return { basePrice: 0, deliveryFee: 0, returnFee: 0, total: 0 };
    }

    const pickupDate = new Date(bookingData.pickUpDate);
    const dropoffDate = new Date(bookingData.dropOffDate);
    const diffTime = Math.abs(dropoffDate.getTime() - pickupDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Calculate base rental price
    const basePrice = diffDays * bookingData.selectedCar.price_per_day;

    // Calculate delivery fee based on pickup location and time
    const deliveryFee = calculateDeliveryFee(
      bookingData.pickUpLocation || "",
      bookingData.pickUpTime || ""
    );

    // Calculate return fee based on dropoff location and time
    const returnFee = calculateReturnFee(
      bookingData.dropOffLocation || "",
      bookingData.dropOffTime || ""
    );

    // Return fee components
    return {
      basePrice,
      deliveryFee,
      returnFee,
      total: basePrice + deliveryFee + returnFee,
    };
  };

  // Check if any selected time is during night hours
  const hasNightTimeSelection = (bookingData: BookingData) => {
    const pickupIsNight = isNightTime(bookingData.pickUpTime || "");
    const dropoffIsNight = isNightTime(bookingData.dropOffTime || "");

    return pickupIsNight || dropoffIsNight;
  };

  const feeComponents = calculateFeeComponents(bookingData);
  const totalAmount = feeComponents.total;
  const downpaymentAmount = totalAmount * 0.5; // 50% downpayment
  const remainingBalance = totalAmount - downpaymentAmount;
  const showNightTimeWarning = hasNightTimeSelection(bookingData);

  const getCompletionStatus = () => {
    if (!selectedMethod)
      return { isComplete: false, message: "Please select a payment method" };

    // Check GCash reference number if GCash is selected
    if (selectedMethod === "GCash") {
      const referenceError = validateGCashReference(bookingData.gcashReferenceNumber || "");
      if (referenceError) {
        return { isComplete: false, message: "Please enter a valid GCash reference number" };
      }
    }

    if (
      !bookingData.proofOfPayment ||
      bookingData.proofOfPayment.length === 0
    ) {
      return { isComplete: false, message: "Please upload proof of payment" };
    }

    const hasCompletedFiles = bookingData.proofOfPayment.every(
      (f) => f.status === "completed"
    );
    if (!hasCompletedFiles) {
      return {
        isComplete: false,
        message: "Please wait for file upload to complete",
      };
    }

    return { isComplete: true, message: "Payment proof uploaded successfully" };
  };

  const completionStatus = getCompletionStatus();

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Payment Requirements Notice */}
      <Card className="border-amber-200 bg-amber-50 overflow-hidden">
        <CardHeader className="p-3 sm:p-4 lg:p-6">
          <CardTitle className="text-base sm:text-lg flex items-center gap-2 text-amber-900 break-words">
            <CreditCard className="h-4 w-4 sm:h-5 sm:w-5" />
            Payment Requirements
          </CardTitle>
        </CardHeader>
        <CardContent className="p-3 sm:p-4 lg:p-6 pt-0">
          <div className="text-amber-800 text-sm sm:text-base break-words">
            We require a 50% Downpayment/Deposit to reserve/book. Payment can be
            made through Bank, GCash, or Remittance. The remaining balance must be paid upon release of the unit.
          </div>

          <div className="mt-3 p-2.5 sm:p-3 bg-white rounded-lg border border-amber-200">
            <h4 className="font-medium text-amber-900 mb-1.5 flex items-center gap-2 text-sm">
              <AlertCircle className="h-4 w-4" />
              Important Notes:
            </h4>
            <ul className="text-sm text-amber-800 space-y-0.5">
              <li>• Downpayments/Deposits are <strong>NON-REFUNDABLE</strong></li>
              <li>• <strong>First come, first serve</strong> basis</li>
              <li>• <strong>No downpayment, no reservation</strong></li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Payment Amount Summary */}
      <Card className="border-green-200 bg-green-50 overflow-hidden">
        <CardHeader className="p-4 sm:p-6 pb-3">
          <CardTitle className="text-base sm:text-lg flex items-center gap-2 text-green-900 break-words">
            <CreditCard className="h-5 w-5" />
            Payment Summary
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4 sm:p-6 pt-0">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-green-700">Vehicle:</span>
              <span className="font-medium text-green-900 text-sm sm:text-base break-words">
                {bookingData.selectedCar?.model || "N/A"}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-green-700">Rate per day:</span>
              <span className="font-medium text-green-900 text-sm sm:text-base">
                ₱
                {bookingData.selectedCar?.price_per_day?.toLocaleString() || "0"}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-green-700">Duration:</span>
              <span className="font-medium text-green-900 text-sm sm:text-base">
                {Math.max(
                  1,
                  Math.ceil(
                    (new Date(bookingData.dropOffDate).getTime() -
                      new Date(bookingData.pickUpDate).getTime()) /
                      (1000 * 60 * 60 * 24)
                  )
                )}{" "}
                day(s)
              </span>
            </div>
            <hr className="border-green-300" />
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Payment Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {/* Fee Breakdown */}
                <div className="space-y-1 text-sm bg-gray-50 p-3 rounded-md">
                  <h4 className="font-medium text-gray-700 mb-1">Fee Breakdown:</h4>
                  <div className="flex justify-between">
                    <p>Base Rental ({Math.max(
                  1,
                  Math.ceil(
                    (new Date(bookingData.dropOffDate).getTime() -
                      new Date(bookingData.pickUpDate).getTime()) /
                      (1000 * 60 * 60 * 24)
                  )
                )} days)</p>
                    <p>{formatCurrency(feeComponents.basePrice)}</p>
                  </div>
                  {feeComponents.deliveryFee > 0 && (
                    <div className="flex justify-between">
                      <p>Delivery Fee {isNightTime(bookingData.pickUpTime || "") ? "(Night)" : "(Day)"}</p>
                      <p>{formatCurrency(feeComponents.deliveryFee)}</p>
                    </div>
                  )}
                  {feeComponents.returnFee > 0 && (
                    <div className="flex justify-between">
                      <p>Return Fee {isNightTime(bookingData.dropOffTime || "") ? "(Night)" : "(Day)"}</p>
                      <p>{formatCurrency(feeComponents.returnFee)}</p>
                    </div>
                  )}
                </div>
                
                <Separator className="my-2" />
                
                {/* Payment Totals */}
                <div className="font-medium flex justify-between">
                  <p>Total Amount</p>
                  <p>{formatCurrency(totalAmount)}</p>
                </div>
                <div className="flex justify-between">
                  <p>Downpayment (50%)</p>
                  <p className="font-medium text-primary">{formatCurrency(downpaymentAmount)}</p>
                </div>
                <div className="flex justify-between">
                  <p>Remaining Balance</p>
                  <p>{formatCurrency(remainingBalance)}</p>
                </div>
              </div>
            </CardContent>
          </div>
        </CardContent>
      </Card>

      {/* Night Time Warning */}
      {showNightTimeWarning && (
        <Alert variant="destructive" className="bg-amber-50 border-amber-300 text-amber-900">
          <AlertCircle className="h-4 w-4 text-amber-600" />
          <AlertDescription className="break-words">
            <strong>Night Time Service Notice:</strong> You have selected a pickup or return time during night hours (18:00-06:59). 
            Additional fees apply for night time service as shown in the payment summary above.
          </AlertDescription>
        </Alert>
      )}

      {/* Payment Method Selection */}
      <Card
        className="overflow-hidden"
        style={{
          backgroundColor: "#FFFFFF",
          borderRadius: "12px",
          boxShadow: "0 2px 10px rgba(0,0,0,0.08)",
          border: "1px solid #E0E0E0",
        }}
      >
        <CardHeader style={{ padding: "24px" }}>
          <CardTitle
            className="break-words"
            style={{
              fontSize: "20px",
              fontWeight: 600,
              lineHeight: "28px",
              color: "#212121",
              fontFamily: "'Inter','Helvetica Neue',Arial,sans-serif",
            }}
          >
            Select Payment Method
          </CardTitle>
          <p
            className="break-words"
            style={{
              fontSize: "14px",
              color: "#616161",
              lineHeight: "20px",
              marginTop: "8px",
            }}
          >
            Choose your preferred payment method for the downpayment
          </p>
        </CardHeader>
        <CardContent style={{ padding: "24px", paddingTop: "0" }}>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {PAYMENT_METHODS.map((method) => {
              const Icon = method.icon;
              const isSelected = selectedMethod === method.id;

              return (
                <button
                  key={method.id}
                  onClick={() => handleMethodSelect(method.id)}
                  className="text-left transition-all min-w-0"
                  style={{
                    padding: "16px",
                    borderRadius: "8px",
                    border: isSelected
                      ? "2px solid #2196F3"
                      : "2px solid #E0E0E0",
                    backgroundColor: isSelected ? "#E3F2FD" : "#FFFFFF",
                    boxShadow: isSelected
                      ? "0 0 0 2px rgba(33, 150, 243, 0.2)"
                      : "none",
                  }}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <Icon
                      className={cn(
                        "w-6 h-6 flex-shrink-0",
                        isSelected ? "text-[#2196F3]" : "text-[#9E9E9E]"
                      )}
                    />
                    <span
                      className="font-medium break-words min-w-0"
                      style={{
                        fontSize: "14px",
                        fontWeight: 600,
                        color: isSelected ? "#1565C0" : "#212121",
                        lineHeight: "18px",
                      }}
                    >
                      {method.name}
                    </span>
                    {isSelected && (
                      <CheckCircle
                        className="ml-auto flex-shrink-0"
                        style={{
                          width: "20px",
                          height: "20px",
                          color: "#2196F3",
                        }}
                      />
                    )}
                  </div>
                  <p
                    className="break-words"
                    style={{
                      fontSize: "14px",
                      color: "#616161",
                      lineHeight: "20px",
                    }}
                  >
                    {method.description}
                  </p>
                </button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Payment Details */}
      {selectedMethodData && (
        <Card
          className="overflow-hidden"
          style={{
            backgroundColor: '#FFFFFF',
            borderRadius: '12px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
            border: '1px solid #E0E0E0'
          }}
        >
          <CardHeader style={{ padding: '24px' }}>
            <CardTitle 
              className="flex items-center gap-2 break-words"
              style={{
                fontSize: '20px',
                fontWeight: 600,
                lineHeight: '28px',
                color: '#212121',
                fontFamily: "'Inter','Helvetica Neue',Arial,sans-serif"
              }}
            >
              <selectedMethodData.icon 
                className="w-5 h-5 text-[#2196F3]"
              />
              {selectedMethodData.name} Payment Details
            </CardTitle>
          </CardHeader>
          <CardContent style={{ padding: '24px', paddingTop: '0' }}>
            <div className="space-y-4">
              {/* Account Details */}
              <div 
                className="rounded-lg p-4 space-y-4"
                style={{
                  backgroundColor: '#FAFAFA',
                  border: '1px solid #E0E0E0',
                  borderRadius: '8px'
                }}
              >
                <div>
                  <label 
                    style={{
                      fontSize: '14px',
                      fontWeight: 500,
                      color: '#616161'
                    }}
                  >
                    Account Name:
                  </label>
                  <div 
                    className="break-words"
                    style={{
                      fontSize: '16px',
                      fontWeight: 600,
                      color: '#212121',
                      marginTop: '4px'
                    }}
                  >
                    {selectedMethodData.accountName}
                  </div>
                </div>
                <div>
                  <label 
                    style={{
                      fontSize: '14px',
                      fontWeight: 500,
                      color: '#616161'
                    }}
                  >
                    {selectedMethodData.id === "GCash"
                      ? "GCash Number:"
                      : selectedMethodData.id === "Bank Transfer"
                      ? "Account Number:"
                      : "Reference Number:"}
                  </label>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-2">
                    <div 
                      className="font-mono font-bold bg-white px-3 py-2 rounded border flex-1 break-all"
                      style={{
                        fontSize: '16px',
                        color: '#212121',
                        backgroundColor: '#FFFFFF',
                        border: '1px solid #E0E0E0',
                        borderRadius: '8px'
                      }}
                    >
                      {selectedMethodData.accountNumber}
                    </div>
                    <button
                      onClick={() =>
                        copyToClipboard(
                          selectedMethodData.accountNumber,
                          selectedMethod!
                        )
                      }
                      className="flex items-center gap-2 w-full sm:w-auto flex-shrink-0 transition-colors"
                      style={{
                        padding: '12px 20px',
                        borderRadius: '8px',
                        backgroundColor: '#FFFFFF',
                        border: '1px solid #E0E0E0',
                        fontSize: '14px',
                        fontWeight: 600,
                        color: '#212121'
                      }}
                    >
                      {copiedStates[selectedMethod!] ? (
                        <Check style={{ width: '16px', height: '16px' }} />
                      ) : (
                        <Copy style={{ width: '16px', height: '16px' }} />
                      )}
                      {copiedStates[selectedMethod!] ? "Copied!" : "Copy"}
                    </button>
                  </div>
                </div>
              </div>

              {/* Instructions */}
              <div>
                <h4 
                  className="font-medium mb-3"
                  style={{
                    fontSize: '14px',
                    fontWeight: 600,
                    color: '#212121'
                  }}
                >
                  How to pay:
                </h4>
                <ol className="space-y-2">
                  {selectedMethodData.instructions.map((instruction, index) => (
                    <li 
                      key={index} 
                      className="flex items-start gap-3 break-words"
                      style={{
                        fontSize: '14px',
                        color: '#616161',
                        lineHeight: '20px'
                      }}
                    >
                      <span 
                        className="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-white font-medium"
                        style={{
                          backgroundColor: '#2196F3',
                          fontSize: '12px',
                          minWidth: '24px',
                          minHeight: '24px'
                        }}
                      >
                        {index + 1}
                      </span>
                      <span>{instruction}</span>
                    </li>
                  ))}
                </ol>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* GCash Reference Number Input */}
      {selectedMethod === "GCash" && (
        <Card className="overflow-hidden border-cyan-300 bg-cyan-50 shadow-md">
          <CardHeader className="p-4 sm:p-6">
            <CardTitle className="text-base sm:text-lg flex items-center gap-2 text-cyan-900 break-words">
              <Smartphone className="h-4 w-4 sm:h-5 sm:w-5 text-cyan-700" />
              GCash Reference Number
            </CardTitle>
            <CardDescription className="text-cyan-800">
              Enter the reference number from your GCash transaction
            </CardDescription>
          </CardHeader>
          <CardContent className="p-4 sm:p-6 pt-0">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label 
                  htmlFor="gcash-reference" 
                  className="text-sm font-medium text-cyan-900"
                >
                  Reference Number <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="gcash-reference"
                  type="text"
                  placeholder="Enter GCash reference number (e.g., 1234567890123)"
                  value={bookingData.gcashReferenceNumber || ""}
                  onChange={handleGCashReferenceChange}
                  className={cn(
                    "font-mono border-2",
                    gcashReferenceError 
                      ? "border-red-500 focus:border-red-500 focus:ring-red-500" 
                      : "border-cyan-400 focus:border-cyan-600 focus:ring-cyan-500 bg-cyan-50"
                  )}
                  required
                />
                {gcashReferenceError && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {gcashReferenceError}
                  </p>
                )}
              </div>
              
              <Alert className="border-cyan-300 bg-cyan-100">
                <InfoIcon className="h-4 w-4 text-cyan-700" />
                <AlertDescription className="text-cyan-900">
                  <strong>Where to find your reference number:</strong>
                  <ul className="mt-2 space-y-1 text-sm">
                    <li>• Check your GCash transaction history in the app</li>
                    <li>• Look for "Reference No." or "Transaction ID" in your receipt</li>
                    <li>• It's usually a 10-20 digit number</li>
                    <li>• Take a screenshot of the transaction details for your records</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
        </Card>
      )}

      {/* File Upload for Payment Proof */}
      {selectedMethod && (
        <Card 
          className="overflow-hidden"
          style={{
            backgroundColor: '#FFFFFF',
            borderRadius: '12px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
            border: '1px solid #E0E0E0'
          }}
        >
          <CardHeader style={{ padding: '24px' }}>
            <CardTitle 
              className="flex items-center gap-2 break-words"
              style={{
                fontSize: '20px',
                fontWeight: 600,
                lineHeight: '28px',
                color: '#212121',
                fontFamily: "'Inter','Helvetica Neue',Arial,sans-serif"
              }}
            >
              <Upload 
                style={{
                  width: '20px',
                  height: '20px',
                  color: '#2196F3'
                }} 
              />
              Upload Proof of Payment
            </CardTitle>
            <p 
              className="break-words"
              style={{
                fontSize: '14px',
                color: '#616161',
                lineHeight: '20px',
                marginTop: '8px'
              }}
            >
              Upload clear photos or screenshots of your payment receipt for the
              downpayment amount of ₱{downpaymentAmount.toLocaleString()}
            </p>
          </CardHeader>
          <CardContent style={{ padding: '24px', paddingTop: '0' }}>
            <DocumentUpload
              label="Upload Payment Proof"
              description={`Upload receipts or screenshots from your ${selectedMethod} payment`}
              files={bookingData.proofOfPayment || []}
              onChange={handleFileChange}
              accept="image/jpeg,image/jpg,image/png,application/pdf"
              maxFiles={1}
              maxSize={5 * 1024 * 1024} // 5MB
              required
              className="w-full"
              uploadFolder="payments"
              userId={user?.id}
            />

            <div 
              className="mt-4 p-4 rounded-lg border flex gap-3"
              style={{
                backgroundColor: '#E3F2FD',
                borderColor: '#2196F3',
                borderRadius: '8px'
              }}
            >
              <InfoIcon 
                style={{
                  width: '16px',
                  height: '16px',
                  color: '#2196F3',
                  flexShrink: 0,
                  marginTop: '2px'
                }} 
              />
              <div>
                <p 
                  style={{
                    fontSize: '14px',
                    fontWeight: 600,
                    color: '#1565C0',
                    marginBottom: '8px'
                  }}
                >
                  Important:
                </p>
                <p 
                  style={{
                    fontSize: '14px',
                    color: '#1976D2',
                    lineHeight: '20px',
                    marginBottom: '8px'
                  }}
                >
                  Ensure your proof of payment clearly shows:
                </p>
                <ul 
                  className="space-y-1"
                  style={{
                    fontSize: '14px',
                    color: '#1976D2',
                    lineHeight: '20px'
                  }}
                >
                  <li className="flex items-start gap-2">
                    <span className="text-[#2196F3] mt-1 flex-shrink-0">•</span>
                    <span>Transaction amount (₱{downpaymentAmount.toLocaleString()})</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-[#2196F3] mt-1 flex-shrink-0">•</span>
                    <span>Transaction date and time</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-[#2196F3] mt-1 flex-shrink-0">•</span>
                    <span>Reference/Transaction number</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-[#2196F3] mt-1 flex-shrink-0">•</span>
                    <span>Recipient details matching the account above</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Progress Summary */}
      <Card
        className={cn(
          "border-2 overflow-hidden",
          completionStatus.isComplete
            ? "border-green-200 bg-green-50"
            : "border-amber-200 bg-amber-50"
        )}
      >
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            {completionStatus.isComplete ? (
              <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
            ) : (
              <AlertCircle className="h-5 w-5 text-amber-600 flex-shrink-0" />
            )}
            <div className="flex-1 min-w-0">
              <p
                className={cn(
                  "font-medium text-sm sm:text-base",
                  completionStatus.isComplete
                    ? "text-green-900"
                    : "text-amber-900"
                )}
              >
                Payment Method Status
              </p>
              <p
                className={cn(
                  "text-sm break-words",
                  completionStatus.isComplete
                    ? "text-green-700"
                    : "text-amber-700"
                )}
              >
                {completionStatus.message}
              </p>
            </div>
            {completionStatus.isComplete && (
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-800 flex-shrink-0"
              >
                Ready
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Security Notice */}
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription className="break-words">
          <strong>Security & Privacy:</strong> Your payment information is
          secure and will only be used for verification purposes. Uploaded
          documents are encrypted and stored securely. Our team will verify your
          payment within 24 hours.
        </AlertDescription>
      </Alert>
    </div>
  );
}
