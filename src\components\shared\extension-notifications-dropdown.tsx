"use client";

import * as React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Bell,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Car,
  Eye,
  Trash2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import {
  ExtensionNotification,
  extensionNotificationService,
  formatNotificationTime,
  getNotificationColor,
} from "@/lib/services/extension-notification-service";

interface ExtensionNotificationsDropdownProps {
  userId: string;
  userType: "customer" | "admin";
  onNotificationClick?: (notification: ExtensionNotification) => void;
}

export function ExtensionNotificationsDropdown({ 
  userId, 
  userType, 
  onNotificationClick 
}: ExtensionNotificationsDropdownProps) {
  const { toast } = useToast();
  
  const [notifications, setNotifications] = React.useState<ExtensionNotification[]>([]);
  const [unreadCount, setUnreadCount] = React.useState(0);
  const [loading, setLoading] = React.useState(true);
  const [isOpen, setIsOpen] = React.useState(false);

  // Fetch notifications
  const fetchNotifications = React.useCallback(async () => {
    try {
      const { data, error } = await extensionNotificationService.getNotifications(userId, userType);
      
      if (error) {
        throw new Error(error.message || "Failed to fetch notifications");
      }

      setNotifications(data || []);
      
      // Update unread count
      const { count } = await extensionNotificationService.getUnreadCount(userId, userType);
      setUnreadCount(count);
    } catch (error: any) {
      console.error("Error fetching notifications:", error);
      toast({
        variant: "destructive",
        title: "Failed to load notifications",
        description: error.message,
      });
    } finally {
      setLoading(false);
    }
  }, [userId, userType, toast]);

  // Initial load
  React.useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Real-time subscription
  React.useEffect(() => {
    const unsubscribe = extensionNotificationService.subscribeToNotifications(
      userId,
      userType,
      (newNotification) => {
        setNotifications(prev => [newNotification, ...prev]);
        setUnreadCount(prev => prev + 1);
        
        // Show toast for new notification
        toast({
          title: newNotification.title,
          description: newNotification.message,
          duration: 5000,
        });
      },
      (error) => {
        console.error("Notification subscription error:", error);
      }
    );

    return unsubscribe;
  }, [userId, userType, toast]);

  const handleMarkAsRead = async (notificationId: string, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    try {
      const { success, error } = await extensionNotificationService.markAsRead(notificationId);
      
      if (!success || error) {
        throw new Error(error?.message || "Failed to mark as read");
      }

      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, is_read: true } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Failed to mark as read",
        description: error.message,
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      const { success, error } = await extensionNotificationService.markAllAsRead(userId, userType);
      
      if (!success || error) {
        throw new Error(error?.message || "Failed to mark all as read");
      }

      setNotifications(prev => prev.map(n => ({ ...n, is_read: true })));
      setUnreadCount(0);
      
      toast({
        title: "All notifications marked as read",
        duration: 3000,
      });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Failed to mark all as read",
        description: error.message,
      });
    }
  };

  const handleDeleteNotification = async (notificationId: string, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    try {
      const { success, error } = await extensionNotificationService.deleteNotification(notificationId);
      
      if (!success || error) {
        throw new Error(error?.message || "Failed to delete notification");
      }

      const deletedNotification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      
      if (deletedNotification && !deletedNotification.is_read) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Failed to delete notification",
        description: error.message,
      });
    }
  };

  const handleNotificationClick = (notification: ExtensionNotification) => {
    // Mark as read if unread
    if (!notification.is_read) {
      handleMarkAsRead(notification.id, { preventDefault: () => {}, stopPropagation: () => {} } as React.MouseEvent);
    }

    // Close dropdown
    setIsOpen(false);

    // Call parent handler
    if (onNotificationClick) {
      onNotificationClick(notification);
    }
  };

  const getNotificationIcon = (notificationType: string) => {
    switch (notificationType) {
      case "extension_requested":
        return <Clock className="h-4 w-4" />;
      case "extension_approved":
        return <CheckCircle className="h-4 w-4" />;
      case "extension_rejected":
        return <XCircle className="h-4 w-4" />;
      case "extension_expired":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="secondary" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center bg-red-500 text-white text-xs"
            >
              {unreadCount > 99 ? "99+" : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-80 max-h-96">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Extension Notifications</span>
          {unreadCount > 0 && (
            <Button
              variant="secondary"
              size="sm"
              onClick={handleMarkAllAsRead}
              className="h-6 px-2 text-xs"
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Mark all read
            </Button>
          )}
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <ScrollArea className="max-h-64">
          {loading ? (
            <div className="p-4 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
              <p className="text-sm text-gray-600 mt-2">Loading notifications...</p>
            </div>
          ) : notifications.length === 0 ? (
            <div className="p-4 text-center">
              <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600">No notifications yet</p>
              <p className="text-xs text-gray-500">Extension notifications will appear here</p>
            </div>
          ) : (
            <div className="space-y-1">
              {notifications.map((notification) => (
                <DropdownMenuItem
                  key={notification.id}
                  className={cn(
                    "flex items-start gap-3 p-3 cursor-pointer",
                    !notification.is_read && "bg-blue-50"
                  )}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex-shrink-0 mt-0.5">
                    <div className={cn(
                      "p-1.5 rounded-full",
                      getNotificationColor(notification.notification_type)
                    )}>
                      {getNotificationIcon(notification.notification_type)}
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                      <h4 className={cn(
                        "text-sm leading-tight",
                        !notification.is_read ? "font-semibold" : "font-medium"
                      )}>
                        {notification.title}
                      </h4>
                      <div className="flex items-center gap-1 flex-shrink-0">
                        {!notification.is_read && (
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={(e) => handleMarkAsRead(notification.id, e)}
                            className="h-6 w-6 p-0"
                            title="Mark as read"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={(e) => handleDeleteNotification(notification.id, e)}
                          className="h-6 w-6 p-0"
                          title="Delete notification"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    
                    <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                      {notification.message}
                    </p>
                    
                    {notification.extension_requests && (
                      <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                        <Car className="h-3 w-3" />
                        <span>
                          {notification.extension_requests.bookings.cars.model} • 
                          {notification.extension_requests.bookings.booking_ref}
                        </span>
                      </div>
                    )}
                    
                    <p className="text-xs text-gray-500 mt-1">
                      {formatNotificationTime(notification.created_at)}
                    </p>
                  </div>
                </DropdownMenuItem>
              ))}
            </div>
          )}
        </ScrollArea>
        
        {notifications.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="justify-center text-sm text-gray-600 cursor-pointer">
              View all notifications
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
